apollo:
  meta: http://service-apollo-config-server-test-beta.sre.svc.cluster.local:8080
  bootstrap:
    enabled: true
    namespaces: application.yml,data.sync.mq.yml,wechat.config.properties,merchant.authority.yml,redis.config.yml,sentinel-app-rules.properties,sentinel-dashboard-middle.properties,netty-redis.properties,dynamictp.yml,dynamictp-common.properties,kf-websocket.properties,third-party-info.properties,oss-public.properties,payment-refactor.properties,middle-stage.properties
  eagerLoad:
    enabled: true

app:
  id: merchant-business

env: UAT

logging:
  config: classpath:logback-uat.xml