<?xml version="1.0" encoding="UTF-8" ?>
<configuration>
    <logger name="cn.lyy" level="INFO"/>
    <logger name="org.springframework" level="WARN"/>
    <logger name="com.netflix" level="WARN"/>
    <logger name="com.ctrip.framework.apollo" level="ERROR"/>
    <logger name="com.aliyun.odps.jdbc" level="ERROR"/>

    <property name="logBasePath" value="/data/logs/application_service"/>
    <property name="maxFileSize" value="50MB"/>
    <property name="maxHistory" value="30"/>
    <property name="commonPattern" value="%d{yyyy-MM-dd HH:mm:ss.SSS}-${PID}-[%X{X-B3-TraceId},%X{X-B3-SpanId},%X{X-B3-ParentSpanId}]-[%thread] %-5level %logger{30} [%file:%line] - %msg%n%rEx{full,
     java.lang.reflect.Method,
     javax.servlet.http,
     java.util.concurrent,
     java.net,
     sun.reflect,
     sun.net,
     org.apache.catalina,
     org.springframework.aop,
     org.springframework.security,
     org.springframework.transaction,
     org.springframework.web,
     org.springframework.beans,
     org.springframework.cglib,
     org.springframework.cloud.netflix,
     org.springframework.retry.support,
     rx,
     feign,
     net.sf.cglib,
     org.apache.tomcat.util,
     org.apache.coyote,
     com.netflix.hystrix,
     com.netflix.loadbalancer.reactive,
     ByCGLIB,
     BySpringCGLIB$
}"/>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern>${commonPattern}</pattern>
        </layout>
    </appender>
    <appender name="fileInfoLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${logBasePath}/info/info.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${logBasePath}/info/info.log.%d{yyyy-MM-dd}-%i.zip</fileNamePattern>
            <maxHistory>${maxHistory}</maxHistory>
            <timeBasedFileNamingAndTriggeringPolicy
                    class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>${maxFileSize}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder>
            <pattern>${commonPattern}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <appender name="fileWarnLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${logBasePath}/warn/warn.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${logBasePath}/warn/warn.log.%d{yyyy-MM-dd}-%i.zip</fileNamePattern>
            <maxHistory>${maxHistory}</maxHistory>
            <timeBasedFileNamingAndTriggeringPolicy
                    class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>${maxFileSize}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder>
            <pattern>${commonPattern}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>WARN</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <appender name="fileErrorLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${logBasePath}/error/error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${logBasePath}/error/error.log.%d{yyyy-MM-dd}-%i.zip</fileNamePattern>
            <maxHistory>${maxHistory}</maxHistory>
            <timeBasedFileNamingAndTriggeringPolicy
                    class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>${maxFileSize}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder>
            <pattern>${commonPattern}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <appender name="asyncInfoLog" class="ch.qos.logback.classic.AsyncAppender">
        <includeCallerData>true</includeCallerData>
        <discardingThreshold>0</discardingThreshold>
        <queueSize>512</queueSize>
        <appender-ref ref="fileInfoLog"/>
    </appender>

    <root level="info">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="asyncInfoLog"/>
        <appender-ref ref="fileWarnLog"/>
        <appender-ref ref="fileErrorLog"/>
    </root>

</configuration>