<?xml version="1.0" encoding="UTF-8" ?>
<configuration>
    <logger name="cn.lyy" level="DEBUG"/>
    <logger name="org.springframework" level="WARN"/>
    <logger name="org.apache.http" level="WARN" />
    <logger name="com.netflix" level="WARN"/>
    <logger name="com.ctrip.framework.apollo" level="ERROR"/>

    <property name="logBasePath" value="./data/logs/application_service"/>
    <property name="maxFileSize" value="50MB"/>
    <property name="maxHistory" value="30"/>
    <property name="commonPattern"
              value="%d{yyyy-MM-dd HH:mm:ss.SSS}-${PID}-[%X{X-B3-TraceId},%X{X-B3-SpanId},%X{X-B3-ParentSpanId}]-[%thread] %-5level %logger{30} [%file:%line] - %msg%n%rEx{full,
     java.lang.reflect.Method,
     javax.servlet.http,
     java.util.concurrent,
     java.net,
     sun.reflect,
     sun.net,
     org.apache.catalina,
     org.springframework.aop,
     org.springframework.data,
     org.springframework.jdbc,
     org.springframework.security,
     org.springframework.transaction,
     org.springframework.web,
     org.springframework.beans,
     org.springframework.cglib,
     org.springframework.cloud.netflix,
     org.springframework.retry.support,
     rx,
     feign,
     net.sf.cglib,
     redis.clients,
     org.apache.tomcat.util,
     org.apache.coyote,
     com.netflix.hystrix,
     com.netflix.loadbalancer.reactive,
     com.alibaba.druid.sql,
     com.alibaba.druid.filter,
     ByCGLIB,
     BySpringCGLIB$
}"/>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern>${commonPattern}</pattern>
        </layout>
    </appender>
    <root level="debug">
        <appender-ref ref="STDOUT"/>
    </root>

</configuration>