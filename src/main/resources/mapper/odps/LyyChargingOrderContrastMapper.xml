<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.lyy.merchant.dao.odps.LyyChargingOrderContrastMapper">

    <select id="getPage" resultType="cn.lyy.merchant.dto.ordercount.LyyChargingOrderContrast">
        select x.lyy_distributor_id as merchantId , x.lyy_equipment_group_id as lyyEquipmentGroupId ,sum(x.oneOrderCount) as oneOrderCount , sum(x.threeOrderCount) as threeOrderCount
        from (
        SELECT lyy_distributor_id , lyy_equipment_group_id , count(id) as oneOrderCount , 0 as threeOrderCount from dwd_lyy_charging_order
        where ds &gt;= #{startDs} and ds &lt;= #{endDs} and lyy_distributor_id in (
            <foreach collection="merchantIds" separator="," item="item">
                #{item}
            </foreach>
            ) group by lyy_distributor_id , lyy_equipment_group_id
        UNION
        SELECT lyy_distributor_id , lyy_group_id as lyy_equipment_group_id , 0 as oneOrderCount , count(equipment_startup_flow_id) as threeOrderCount from dwd_saas_equipment_startup_flow_new
        where ds &gt;= #{startDs} and ds &lt;= #{endDs} and lyy_distributor_id in (
            <foreach collection="merchantIds" separator="," item="item">
                #{item}
            </foreach>
        ) group by lyy_distributor_id , lyy_group_id
        UNION
        select ad_org_id as lyy_distributor_id,lyy_equipment_group_id,0 as oneOrderCount, 0 as threeOrderCount from ods_lyy_equipment_group where ad_org_id in (
        <foreach collection="merchantIds" separator="," item="item">
            #{item}
        </foreach>
        ) and isactive = 'Y') x

        group by x.lyy_distributor_id , x.lyy_equipment_group_id order by x.lyy_equipment_group_id
        limit #{limit} offset #{offset}
    </select>
</mapper>