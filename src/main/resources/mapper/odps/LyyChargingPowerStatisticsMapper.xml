<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.lyy.merchant.dao.odps.LyyChargingPowerStatisticsMapper">

    <select id="getLyyChargingPowerStatisticsList" resultType="cn.lyy.life.api.charging.dto.LyyChargingPowerStatisticsDTO">
        select
        lco.lyy_distributor_id as merchantId,
        lco.id as lyyChargingOrderId,
        lco.lyy_equipment_id as lyyEquipmentId,
        lco.lyy_equipment_group_id as lyyEquipmentGroupId,
        lco.value as equipmentValue,
        lco.money as payMoney,
        lco.lyy_user_id as lyyUserId,
        case when lco.out_trade_no = '3' then 3
        else 4 end as startType,
        case when cp.maxPower is null then 0 else cp.maxPower end as maxPower,
        case when ed.equipment_electric is null then 0 else ed.equipment_electric end as equipmentElectric ,
        substr(lco.ds,1,6) as monthDate,
        case when cast(cast(cp.maxPower / 100 as int) * 100 + 100 as STRING ) is null
        then 0 else cast(cast(cp.maxPower / 100 as int) * 100 + 100 as STRING ) end as powerRange
        from dwd_lyy_charging_order lco
        left join
        (select charging_order_id , max(charging_power) as maxPower
        from ods_lyy_charging_order_power where ds &gt;= regexp_replace(#{fromDate},'-','')
        <if test="null != toDate and '' != toDate">
            and ds &lt; regexp_replace(#{toDate},'-','')
        </if>
        group by charging_order_id
        ) cp on cp.charging_order_id = lco.id
        left join ods_mchair_lyy_charging_electric_di ed on ed.lyy_charging_order_id = lco.id and ed.day &gt;= #{fromDate}
        and ed.lyy_distributor_id in (${merchantIds})
        <if test="null != toDate and '' != toDate">
            and ed.day &lt; #{toDate}
        </if>
        where lco.ds &gt;= regexp_replace(#{fromDate},'-','') and lco.lyy_distributor_id in (${merchantIds}) and lco.order_status = -1
        <if test="null != toDate and '' != toDate">
            and lco.ds &lt; regexp_replace(#{toDate},'-','')
        </if>
        order by lco.id
        <if test="limit != null and offset != null">
            limit #{limit} offset #{offset}
        </if>
    </select>

    <select id="getLyyChargingPowerStatisticsCount" resultType="int">
        select
        count(1)
        from dwd_lyy_charging_order lco
        left join
        (select charging_order_id , max(charging_power) as maxPower
        from ods_lyy_charging_order_power where ds &gt;= regexp_replace(#{fromDate},'-','')
        <if test="null != toDate and '' != toDate">
            and ds &lt; regexp_replace(#{toDate},'-','')
        </if>
        group by charging_order_id
        ) cp on cp.charging_order_id = lco.id
        left join ods_mchair_lyy_charging_electric_di ed on ed.lyy_charging_order_id = lco.id and ed.day &gt;= #{fromDate}
        and ed.lyy_distributor_id in (${merchantIds})
        <if test="null != toDate and '' != toDate">
            and ed.day &lt; #{toDate}
        </if>
        where lco.ds &gt;= regexp_replace(#{fromDate},'-','') and lco.lyy_distributor_id in (${merchantIds}) and lco.order_status = -1
        <if test="null != toDate and '' != toDate">
            and lco.ds &lt; regexp_replace(#{toDate},'-','')
        </if>
    </select>
</mapper>