<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.lyy.merchant.dao.odps.MerchantOrderMapper">

    <select id="queryOrder" resultType="cn.lyy.merchant.dto.OdpsOrderVO">
SELECT id, out_trade_no as outTradeNo from dwd_order_info  where ds &gt; 20200501 and ds &lt; 20211119 order by id desc limit 1

    </select>

    <select id="queryOrder2" resultType="cn.lyy.merchant.dto.OdpsOrderVO">
SELECT * from ods_lyy_equipment_type ORDER BY lyy_equipment_type_id desc limit 1
    </select>


    <select id="getExportOrderList" resultType="cn.lyy.merchant.dto.MerchantPayOrderStatisticsDTO">
        select
        oi.out_trade_no as outTradeNo,
        oi.business_type as businessType,
        pr.channel_order_no as channelOrderNo,
        oi.actual_amount as actuaAmount,
        case when oi.status=2 and roi.status is null then 0
        else roi.refund_amount
        end as refundAmount,
        roi.refund_detail as refundDetail,
        case
        when roi.refund_amount = 0 then '余额'
        when roi.refund_amount > 0 then '原路'
        else '' end as refundPath,
        case oi.status
        when 1 then '待支付'
        when 2 then '已支付'
        when 3 then '退款中'
        when 4 then '已退款'
        when 5 then '已取消'
        when 6 then '未支付'
        when 7 then '已关闭'
        else ''
        end as status,
        case oi.client_type
        when 2 then '微信支付'
        when 3 then '支付宝支付'
        else ''
        end as clientType,
        le.value as equipmentValue,
        et.name as equipmentClass,
        eg.name as groupName,
        to_char(oi.create_time, 'yyyy-MM-dd hh:mi') as createTime,
        oi.distributor_id as distributorId
        from dwd_order_info oi
        left join dwd_refund_order_info roi on roi.out_trade_no = oi.out_trade_no
        left join dwd_payment_record pr on pr.out_trade_no = oi.out_trade_no
        left join dwd_lyy_equipment_group eg on eg.lyy_equipment_group_id = oi.store_id
        left join ods_lyy_equipment_type et on et.lyy_equipment_type_id =
        cast(get_json_object(oi.equipment_detail,'$.typeId')  as bigint)
        left join ods_lyy_equipment le on le.lyy_equipment_id =
        cast(get_json_object(oi.equipment_detail,'$.equipmentId') as bigint)
        where
        oi.distributor_id in
        <foreach collection="ids" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and oi.ds &gt;= #{firstDay}
        and oi.ds &lt;= #{lastDay}
        and roi.ds &gt;= #{firstDay}
        and roi.ds &lt;= #{lastDay}
        and pr.ds &gt;= #{firstDay}
        and pr.ds &lt;= #{lastDay}
        and oi.status in (2,3,4)
        and oi.actual_amount > 0
        order by oi.create_time asc LIMIT 200000
    </select>

</mapper>