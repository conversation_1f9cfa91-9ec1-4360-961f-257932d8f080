spring:
  main:
    allow-bean-definition-overriding: true
apollo:
  meta: http://10.99.13.125:8080
  bootstrap:
    enabled: true
    namespaces: application.yml,data.sync.mq.yml,wechat.config.properties,merchant.authority.yml,redis.config.yml,sentinel-app-rules.properties,sentinel-dashboard-middle.properties,netty-redis.properties,dynamictp.yml,dynamictp-common.properties,kf-websocket.properties,third-party-info.properties,payment-refactor.properties,oss-public.properties,middle-stage.properties
  eagerLoad:
    enabled: true

app:
  id: merchant-business

env: DEV

logging:
  config: classpath:logback-dev.xml

#hystrix:
#  threadpool:
#    IEquipmentParamService#getParamConfigListFromProduct(uniqueCode):
#      maximumSize: 10
#      coreSize: 10
#    IEquipmentParamService#paramQuery(String,Long,Integer):
#      maximumSize: 10
#      coreSize: 10
#    IEquipmentParamService#paramSetting(String,EquipmentExtendParamSettingDTO):
#      maximumSize: 10
#      coreSize: 10
#  command:
#    IEquipmentParamService#getParamConfigListFromProduct(uniqueCode):
#      execution:
#        isolation:
#          thread:
#            timeoutInMilliseconds: 5000
#      fallback:
#        isolation:
#          semaphore:
#            maxConcurrentRequests: 10
#    IEquipmentParamService#paramQuery(String,Long,Integer):
#      execution:
#        isolation:
#          thread:
#            timeoutInMilliseconds: 5000
#      fallback:
#        isolation:
#          semaphore:
#            maxConcurrentRequests: 10
#    IEquipmentParamService#paramSetting(String,EquipmentExtendParamSettingDTO):
#      execution:
#        isolation:
#          thread:
#            timeoutInMilliseconds: 5000
#      fallback:
#        isolation:
#          semaphore:
#            maxConcurrentRequests: 10
