--!柏来设备出货数明细
select e.value 设备号,to_char(e.created,'yyyy-MM-dd hh:mm:ss') 出货时间
from ods_lyy_equipment e
join ods_lyy_equipment_attach using (lyy_equipment_id)
where factory_brand in('BLAl','BLGW') and lyy_equipment_type_id=1000078
union all
select et.value 设备号,to_char(et.created,'yyyy-MM-dd hh:mm:ss')  出货时间
from ods_lyy_equipment et
where et.lyy_equipment_type_id=1001253
and et .lyy_equipment_id not in(
select e.lyy_equipment_id
from ods_lyy_equipment e
join ods_lyy_equipment_attach attach on attach.lyy_equipment_id=e.lyy_equipment_id
where  e.lyy_equipment_type_id=1001253
and attach.factory_brand in ('FY_GW','CM_GW','XK_GW','DC_GW','TC_GW','BULL_AL')
) order by 设备号

--!柏来设备在线数明细
with gateway as (
    select e.lyy_equipment_id,value,count(1) online_count
    from ods_lyy_equipment_status_day d
    join ods_lyy_equipment e on d.lyy_equipment_id=e.lyy_equipment_id
    where
    e.lyy_equipment_type_id = 1001253
    and is_online = 1 and  ds >20200101
   and e.lyy_equipment_id not in(
   select e.lyy_equipment_id
   from ods_lyy_equipment e
   join ods_lyy_equipment_attach attach on attach.lyy_equipment_id=e.lyy_equipment_id
   where  e.lyy_equipment_type_id=1001253
   and e.value  like '837%'
   or attach.factory_brand in ('FY_GW','CM_GW','XK_GW','DC_GW','TC_GW','BULL_AL'))
   group by e.lyy_equipment_id,value
)
SELECT EquipmentNumber 设备号, SUM(onlineDays) AS "在线天数+交易天数" FROM (
	select e.value EquipmentNumber , online_count onlineDays
	from ods_lyy_equipment_association
	join ods_lyy_equipment e on association_id = e.lyy_equipment_id
	join gateway g on equipment_id = g.lyy_equipment_id
	union all
	select value EquipmentNumber, count(1) onlineDays
	from ods_lyy_equipment_status_day d
	join ods_lyy_equipment e using (lyy_equipment_id)
	join ods_lyy_equipment_attach using (lyy_equipment_id)
	where factory_brand='BLAl'
	  and is_online = 1  and  ds >20200101
	group by value
	union all
	select value EquipmentNumber, online_count onlineDays
	from gateway
	union all
	select value EquipmentNumber, count(d) onlineDays from (
	select distinct lco.value , to_char(lco.create_time,'yyyyMMdd') as d from dwd_lyy_charging_order as  lco
	join ods_lyy_equipment_attach lea using (lyy_equipment_id)
	where  (lco.value like '839%' and length(lco.value) = 8   )
	and lea.factory_brand='BLGW'  and  ds >20200101
	)a  group by value
) b
GROUP BY 设备号 order by 设备号

--!柏来设备出货数明细_count
select count(1) from (select e.value EquipmentNumber,to_char(e.created,'yyyy-MM-dd hh:mm:ss')  shipmentsTime
from ods_lyy_equipment e
join ods_lyy_equipment_attach using (lyy_equipment_id)
where factory_brand in('BLAl','BLGW') and lyy_equipment_type_id=1000078
union all
select et.value EquipmentNumber,to_char(et.created,'yyyy-MM-dd hh:mm:ss')  shipmentsTime
from ods_lyy_equipment et
where et.lyy_equipment_type_id=1001253
and et .lyy_equipment_id not in(
select e.lyy_equipment_id
from ods_lyy_equipment e
join ods_lyy_equipment_attach attach on attach.lyy_equipment_id=e.lyy_equipment_id
where  e.lyy_equipment_type_id=1001253
and attach.factory_brand in ('FY_GW','CM_GW','XK_GW','DC_GW','TC_GW','BULL_AL')
)) tt

--!柏来设备在线数明细_count
with gateway as (
    select e.lyy_equipment_id,value,count(1) online_count
    from ods_lyy_equipment_status_day d
    join ods_lyy_equipment e on d.lyy_equipment_id=e.lyy_equipment_id
    where
    e.lyy_equipment_type_id = 1001253
    and is_online = 1 and  ds >20200101
   and e.lyy_equipment_id not in(
   select e.lyy_equipment_id
   from ods_lyy_equipment e
   join ods_lyy_equipment_attach attach on attach.lyy_equipment_id=e.lyy_equipment_id
   where  e.lyy_equipment_type_id=1001253
   and e.value  like '837%'
   or attach.factory_brand ='FY_GW')
   group by e.lyy_equipment_id,value
)
select count(1) num from (
SELECT EquipmentNumber FROM (
	select e.value EquipmentNumber , online_count onlineDays
	from ods_lyy_equipment_association
	join ods_lyy_equipment e on association_id = e.lyy_equipment_id
	join gateway g on equipment_id = g.lyy_equipment_id
	union all
	select value EquipmentNumber, count(1) onlineDays
	from ods_lyy_equipment_status_day d
	join ods_lyy_equipment e using (lyy_equipment_id)
	join ods_lyy_equipment_attach using (lyy_equipment_id)
	where factory_brand='BLAl'
	  and is_online = 1  and  ds >20200101
	group by value
	union all
	select value EquipmentNumber, online_count onlineDays
	from gateway
	union all
	select value EquipmentNumber, count(d) onlineDays from (
	select distinct lco.value , to_char(lco.create_time,'yyyyMMdd') as d from dwd_lyy_charging_order as  lco
	join ods_lyy_equipment_attach lea using (lyy_equipment_id)
	where  (lco.value like '839%' and length(lco.value) = 8   )
	and lea.factory_brand='BLGW'  and  ds >20200101
	)a  group by value
) b
GROUP BY EquipmentNumber ) n

--!飞宇设备出货数明细
select e.value 设备号,to_char(e.created,'yyyy-MM-dd hh24:mm:ss')  出货时间
from ods_lyy_equipment e
join ods_lyy_equipment_attach using (lyy_equipment_id)
where factory_brand IN('FY_GW','FYBOX') order by 设备号

--!飞宇设备出货数明细_count
select count(1) from (
select e.value 设备号,to_char(e.created,'yyyy-MM-dd hh24:mm:ss')  出货时间
from ods_lyy_equipment e
join ods_lyy_equipment_attach using (lyy_equipment_id)
where factory_brand IN('FY_GW','FYBOX')
) tt

--!飞宇设备在线数明细
with gateway as (
    select e.lyy_equipment_id,value,count(1) online_count
    from ods_lyy_equipment_status_day d
    join ods_lyy_equipment e on d.lyy_equipment_id=e.lyy_equipment_id
    join ods_lyy_equipment_attach attach on attach.lyy_equipment_id=e.lyy_equipment_id
    where
    e.lyy_equipment_type_id = 1001253
    and is_online = 1 and  ds >20200101 AND attach.factory_brand ='FY_GW'
  group by e.lyy_equipment_id,value
)
SELECT 设备号 , SUM(在线天数) AS "在线天数+交易天数" FROM (
	select e.value 设备号 , online_count 在线天数
	from ods_lyy_equipment_association
	join ods_lyy_equipment e on association_id = e.lyy_equipment_id
	join gateway g on equipment_id = g.lyy_equipment_id
	union all
	select value 设备号, count(1) 在线天数
	from ods_lyy_equipment_status_day d
	join ods_lyy_equipment e using (lyy_equipment_id)
	join ods_lyy_equipment_attach using (lyy_equipment_id)
	where factory_brand='FYBOX'
	 and is_online = 1  and  ds >20200101
	group by value
	union all
	select value 设备号, online_count 在线天数
	from gateway
	union all
	select value 设备号, count(d) 在线天数 from (
	select distinct lco.value , to_char(lco.create_time,'yyyyMMdd') as d from dwd_lyy_charging_order as  lco
	join ods_lyy_equipment_attach lea using (lyy_equipment_id)
	where   length(lco.value) = 8
	and lea.factory_brand='FY_GW'  and  ds >20200101
	)a  group by value
) b
GROUP BY 设备号 order by 设备号

--!飞宇设备在线数明细_count
with gateway as (
    select e.lyy_equipment_id,value,count(1) online_count
    from ods_lyy_equipment_status_day d
    join ods_lyy_equipment e on d.lyy_equipment_id=e.lyy_equipment_id
    join ods_lyy_equipment_attach attach on attach.lyy_equipment_id=e.lyy_equipment_id
    where
    e.lyy_equipment_type_id = 1001253
    and is_online = 1 and  ds >20200101 AND attach.factory_brand ='FY_GW'
  group by e.lyy_equipment_id,value
)
select count(1) from (
SELECT 设备号 FROM (
	select e.value 设备号 , online_count 在线天数
	from ods_lyy_equipment_association
	join ods_lyy_equipment e on association_id = e.lyy_equipment_id
	join gateway g on equipment_id = g.lyy_equipment_id
	union all
	select value 设备号, count(1) 在线天数
	from ods_lyy_equipment_status_day d
	join ods_lyy_equipment e using (lyy_equipment_id)
	join ods_lyy_equipment_attach using (lyy_equipment_id)
	where factory_brand='FYBOX'
	 and is_online = 1  and  ds >20200101
	group by value
	union all
	select value 设备号, online_count 在线天数
	from gateway
	union all
	select value 设备号, count(d) 在线天数 from (
	select distinct lco.value , to_char(lco.create_time,'yyyyMMdd') as d from dwd_lyy_charging_order as  lco
	join ods_lyy_equipment_attach lea using (lyy_equipment_id)
	where   length(lco.value) = 8
	and lea.factory_brand='FY_GW'  and  ds >20200101
	)a  group by value
) b
GROUP BY 设备号) tt

--!电川设备出货数明细
select e.value 设备号,to_char(e.created,'yyyy-MM-dd hh24:mm:ss')  出货时间
from ods_lyy_equipment e
join ods_lyy_equipment_attach using (lyy_equipment_id)
where factory_brand ='DC_GW' and lyy_equipment_type_id=1000078
union all
select e.value 设备号,to_char(e.created,'yyyy-MM-dd hh24:mm:ss')  出货时间
from ods_lyy_equipment e
left join ods_lyy_equipment_attach using (lyy_equipment_id)
where lyy_equipment_type_id=1001253 and factory_brand ='DC_GW'
order by 设备号

--!电川设备出货数明细_count
select count(1) from (
select e.value 设备号,to_char(e.created,'yyyy-MM-dd hh24:mm:ss')  出货时间
from ods_lyy_equipment e
join ods_lyy_equipment_attach using (lyy_equipment_id)
where factory_brand ='DC_GW' and lyy_equipment_type_id=1000078
union all
select e.value 设备号,to_char(e.created,'yyyy-MM-dd hh24:mm:ss')  出货时间
from ods_lyy_equipment e
left join ods_lyy_equipment_attach using (lyy_equipment_id)
where lyy_equipment_type_id=1001253 and factory_brand ='DC_GW') tt

--!电川设备在线数明细
with gateway as (
    select lyy_equipment_id,value,count(1) online_count
    from ods_lyy_equipment_status_day d
    join ods_lyy_equipment e using (lyy_equipment_id)
    left join ods_lyy_equipment_attach using (lyy_equipment_id)
    where
    e.lyy_equipment_type_id = 1001253
    and is_online = 1 and  ds >20200101
    and factory_brand='DC_GW'
    group by lyy_equipment_id,value
)
SELECT 设备号 , SUM(在线天数) AS "在线天数+交易天数" FROM (
	select e.value 设备号 , online_count 在线天数
	from ods_lyy_equipment_association
	join ods_lyy_equipment e on association_id = e.lyy_equipment_id
	join gateway g on equipment_id = g.lyy_equipment_id
	union all
	select value 设备号, count(1) 在线天数
	from ods_lyy_equipment_status_day d
	join ods_lyy_equipment e using (lyy_equipment_id)
	join ods_lyy_equipment_attach using (lyy_equipment_id)
	where factory_brand='DC_GW'
	  and is_online = 1  and  ds >20200101
	group by value
	union all
	select value 设备号, online_count 在线天数
	from gateway
	union all
	select value 设备号, count(d) 在线天数 from (
	select distinct lco.value , to_char(lco.create_time,'yyyyMMdd') as d from dwd_lyy_charging_order as  lco
	join ods_lyy_equipment_attach lea using (lyy_equipment_id)
	where  (lco.value like '1312%' and length(lco.value) = 14   )
	and lea.factory_brand='DC_GW'  and  ds >20200101
	)a  group by value
) b
GROUP BY 设备号 order by 设备号

--!电川设备在线数明细_count
with gateway as (
    select lyy_equipment_id,value,count(1) online_count
    from ods_lyy_equipment_status_day d
    join ods_lyy_equipment e using (lyy_equipment_id)
    left join ods_lyy_equipment_attach using (lyy_equipment_id)
    where
    e.lyy_equipment_type_id = 1001253
    and is_online = 1 and  ds >20200101
    and factory_brand='DC_GW'
    group by lyy_equipment_id,value
)
select count(1) from (
SELECT 设备号 , SUM(在线天数) AS "在线天数+交易天数" FROM (
	select e.value 设备号 , online_count 在线天数
	from ods_lyy_equipment_association
	join ods_lyy_equipment e on association_id = e.lyy_equipment_id
	join gateway g on equipment_id = g.lyy_equipment_id
	union all
	select value 设备号, count(1) 在线天数
	from ods_lyy_equipment_status_day d
	join ods_lyy_equipment e using (lyy_equipment_id)
	join ods_lyy_equipment_attach using (lyy_equipment_id)
	where factory_brand='DC_GW'
	  and is_online = 1  and  ds >20200101
	group by value
	union all
	select value 设备号, online_count 在线天数
	from gateway
	union all
	select value 设备号, count(d) 在线天数 from (
	select distinct lco.value , to_char(lco.create_time,'yyyyMMdd') as d from dwd_lyy_charging_order as  lco
	join ods_lyy_equipment_attach lea using (lyy_equipment_id)
	where  (lco.value like '1312%' and length(lco.value) = 14   )
	and lea.factory_brand='DC_GW'  and  ds >20200101
	)a  group by value
) b
GROUP BY 设备号 ) tt

--!诚马设备出货数明细_count
select count(1) from (
select e.value 设备号,to_char(e.created,'yyyy-MM-dd hh24:mm:ss')  出货时间
from ods_lyy_equipment e
         join ods_lyy_equipment_attach using (lyy_equipment_id)
where factory_brand in ('CM_GW','CM_DL') and lyy_equipment_type_id=1000078
union all
select e.value 设备号,to_char(e.created,'yyyy-MM-dd hh24:mm:ss')  出货时间
from ods_lyy_equipment e
         left join ods_lyy_equipment_attach using (lyy_equipment_id)
where lyy_equipment_type_id=1001253 and factory_brand in ('CM_GW','CM_DL') and e.value not like '1312719%') tt

--!诚马设备在线数明细
with gateway as (
select lyy_equipment_id,value,count(1) online_count
from ods_lyy_equipment_status_day d
join ods_lyy_equipment e using (lyy_equipment_id)
left join ods_lyy_equipment_attach using (lyy_equipment_id)
where
e.lyy_equipment_type_id = 1001253
and is_online = 1 and  ds >20200101
and factory_brand in ('CM_GW','CM_DL') and e.value not like '1312719%'
group by lyy_equipment_id,value
)
SELECT 设备号 , SUM(在线天数) AS "在线天数+交易天数" FROM (
select e.value 设备号 , online_count 在线天数
from ods_lyy_equipment_association
         join ods_lyy_equipment e on association_id = e.lyy_equipment_id
         join gateway g on equipment_id = g.lyy_equipment_id
union all
select value 设备号, count(1) 在线天数
from ods_lyy_equipment_status_day d
         join ods_lyy_equipment e using (lyy_equipment_id)
         join ods_lyy_equipment_attach using (lyy_equipment_id)
where factory_brand in ('CM_GW','CM_DL') and e.value not like '1312719%'
  and is_online = 1  and  ds >20220101
group by value
union all
select value 设备号, online_count 在线天数
from gateway
union all
select value 设备号, count(d) 在线天数 from (
select distinct lco.value , to_char(lco.create_time,'yyyyMMdd') as d from dwd_lyy_charging_order as  lco
join ods_lyy_equipment_attach lea using (lyy_equipment_id)
where  (lco.value like '1312%' and length(lco.value) = 14   )
  and lea.factory_brand in ('CM_GW','CM_DL')  and  ds >20220101
 )a  group by value
 ) b
GROUP BY 设备号 order by 设备号

--!诚马设备在线数明细_count
with gateway as (
select lyy_equipment_id,value,count(1) online_count
from ods_lyy_equipment_status_day d
join ods_lyy_equipment e using (lyy_equipment_id)
left join ods_lyy_equipment_attach using (lyy_equipment_id)
where
e.lyy_equipment_type_id = 1001253
and is_online = 1 and  ds >20220101
and factory_brand in ('CM_GW','CM_DL') and e.value not like '1312719%'
group by lyy_equipment_id,value
)
select count(1) from (
SELECT 设备号 , SUM(在线天数) AS "在线天数+交易天数" FROM (
select e.value 设备号 , online_count 在线天数
from ods_lyy_equipment_association
         join ods_lyy_equipment e on association_id = e.lyy_equipment_id
         join gateway g on equipment_id = g.lyy_equipment_id
union all
select value 设备号, count(1) 在线天数
from ods_lyy_equipment_status_day d
         join ods_lyy_equipment e using (lyy_equipment_id)
         join ods_lyy_equipment_attach using (lyy_equipment_id)
where factory_brand in ('CM_GW','CM_DL') and e.value not like '1312719%'
  and is_online = 1  and  ds >20220101
group by value
union all
select value 设备号, online_count 在线天数
from gateway
union all
select value 设备号, count(d) 在线天数 from (
select distinct lco.value , to_char(lco.create_time,'yyyyMMdd') as d from dwd_lyy_charging_order as  lco
join ods_lyy_equipment_attach lea using (lyy_equipment_id)
where  (lco.value like '1312%' and length(lco.value) = 14   )
  and lea.factory_brand in ('CM_GW','CM_DL')  and  ds >20220101
)a  group by value
) b
GROUP BY 设备号 ) tt

--!诚马设备出货数明细
select e.value 设备号,to_char(e.created,'yyyy-MM-dd hh24:mm:ss')  出货时间
from ods_lyy_equipment e
         join ods_lyy_equipment_attach using (lyy_equipment_id)
where factory_brand in ('CM_GW','CM_DL') and lyy_equipment_type_id=1000078
union all
select e.value 设备号,to_char(e.created,'yyyy-MM-dd hh24:mm:ss')  出货时间
from ods_lyy_equipment e
         left join ods_lyy_equipment_attach using (lyy_equipment_id)
where lyy_equipment_type_id=1001253 and factory_brand in ('CM_GW','CM_DL') and e.value not like '1312719%'
order by 设备号

--!公牛设备出货数明细
select e.value 设备号,to_char(e.created,'yyyy-MM-dd hh:mm:ss') 出货时间
from ods_lyy_equipment e
         join ods_lyy_equipment_attach using (lyy_equipment_id)
where factory_brand in('BULL_AL') and lyy_equipment_type_id=1000078
order by 设备号

--!公牛设备在线数明细
select e.value 设备号,count(1) 在线天数
from ods_lyy_equipment_status_day d
         join ods_lyy_equipment e on d.lyy_equipment_id=e.lyy_equipment_id
         join ods_lyy_equipment_attach ea on ea.lyy_equipment_id = e.lyy_equipment_id
where
        e.lyy_equipment_type_id = 1000078
  and is_online = 1 and  ds >20220101
  and ea.factory_brand='BULL_AL'
group by e.lyy_equipment_id,value order by value

--!公牛设备出货数明细_count
select count(1) from (select e.value 设备号,to_char(e.created,'yyyy-MM-dd hh:mm:ss') 出货时间
                      from ods_lyy_equipment e
                               join ods_lyy_equipment_attach using (lyy_equipment_id)
                      where factory_brand in('BULL_AL') and lyy_equipment_type_id=1000078) tt

--!公牛设备在线数明细_count
select count(1) num from (
                             select e.value 设备号,count(1) 在线天数
                             from ods_lyy_equipment_status_day d
                                      join ods_lyy_equipment e on d.lyy_equipment_id=e.lyy_equipment_id
                                      join ods_lyy_equipment_attach ea on ea.lyy_equipment_id = e.lyy_equipment_id
                             where
                                     e.lyy_equipment_type_id = 1000078
                               and is_online = 1 and  ds >20220101
                               and ea.factory_brand='BULL_AL'
                             group by e.lyy_equipment_id,value ) n

--!烁飞新单机设备出货数明细
select e.value 设备号,to_char(e.created,'yyyy-MM-dd hh:mm:ss') 出货时间
from ods_lyy_equipment e
         join ods_lyy_equipment_attach using (lyy_equipment_id)
where factory_brand in('SFBOX') and lyy_equipment_type_id=1000078
order by 设备号

--!烁飞新单机设备在线数明细
select e.value 设备号,count(1) 在线天数
from ods_lyy_equipment_status_day d
         join ods_lyy_equipment e on d.lyy_equipment_id=e.lyy_equipment_id
         join ods_lyy_equipment_attach ea on ea.lyy_equipment_id = e.lyy_equipment_id
where
        e.lyy_equipment_type_id = 1000078
  and is_online = 1 and  ds >20230101
  and ea.factory_brand='SFBOX'
group by e.lyy_equipment_id,value order by value

--!烁飞新单机设备出货数明细_count
select count(1) from (select e.value 设备号,to_char(e.created,'yyyy-MM-dd hh:mm:ss') 出货时间
                      from ods_lyy_equipment e
                               join ods_lyy_equipment_attach using (lyy_equipment_id)
                      where factory_brand in('SFBOX') and lyy_equipment_type_id=1000078) tt

--!烁飞新单机设备在线数明细_count
select count(1) num from (
                             select e.value 设备号,count(1) 在线天数
                             from ods_lyy_equipment_status_day d
                                      join ods_lyy_equipment e on d.lyy_equipment_id=e.lyy_equipment_id
                                      join ods_lyy_equipment_attach ea on ea.lyy_equipment_id = e.lyy_equipment_id
                             where
                                     e.lyy_equipment_type_id = 1000078
                               and is_online = 1 and  ds >20230101
                               and ea.factory_brand='SFBOX'
                             group by e.lyy_equipment_id,value ) n