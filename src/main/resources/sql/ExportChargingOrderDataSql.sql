--!3.0充电记录导出
select
	co.business_order_no 业务订单号,
	case
	    when up.channel_merchant_trade_no is null then '0'
	    else up.channel_merchant_trade_no
	end as 支付订单号,
	co.startup_time 消费时间,
	co.startup_time 开始充电时间,
	case
	    when co.actual_end_time is null then '0'
	    else co.actual_end_time
	end as 结束充电时间,
	co.ad_user_id 用户id,
	case
	    when lu.telephone is null then '0'
	    else lu.telephone
	end as 用户手机,
	round(cast(get_json_object(co.startup_param,'$.group_service_price') as DECIMAL) / 100,2) 套餐金额,
	round(cast((get_json_object(co.startup_param,'$.group_service_price') - co.amount) as DECIMAL) / 100,2) 优惠金额,
	round(cast(co.amount as DECIMAL) / 100,2) 实付金额,
	case
	    when co.refund_amount is null then 0
	    else round(cast(co.refund_amount as DECIMAL ) / 100,2)
	end as 退款金额,
	case
	    when co.refund_amount is not null then round(cast((co.amount - co.refund_amount) as DECIMAL) / 100,2)
	    else round(cast(co.amount as DECIMAL) / 100)
	end as 消费金额,
	case
		when co.payment_type = 'IC' then '刷卡'
		when co.payment_type = 'BL' then '余额'
		else '扫码'
	end as 启动方式,
	case
		when co.payment_type = 'BL' then '余额支付'
		when co.payment_channel = 'wx' or co.payment_channel = 'Wechat' then '微信'
		when co.payment_channel = 'alipay' then '支付宝'
		when co.payment_type = 'IC' then '刷卡'
		else '其他'
	end as 支付方式,
	eg.name 场地名称
from
	dwd_saas_equipment_startup_flow_new co
left join dwd_lyy_equipment_group eg on
	co.lyy_group_id = eg.lyy_equipment_group_id
left join dwd_lyy_user_payment up on
	up.out_trade_no = co.business_order_no
	and up.ds >= :ds
	and up.ds < :endds
left join dwd_lyy_user lu on
    lu.lyy_user_id = co.ad_user_id	and lu.ds > 20200101
where
	co.status in (1,2,3)
	and co.lyy_distributor_id = :distributor_id
	and co.ds >= :ds
	and co.ds < :endds
order by
	co.startup_time desc



--!3.0充电记录导出_count
select
	count(1)
from
	dwd_saas_equipment_startup_flow_new co
left join dwd_lyy_equipment_group eg on
	co.lyy_group_id = eg.lyy_equipment_group_id
left join dwd_lyy_user_payment up on
	up.out_trade_no = co.business_order_no
	and up.ds >= :ds
	and up.ds < :endds
left join dwd_lyy_user lu on
    lu.lyy_user_id = co.ad_user_id	and lu.ds > 20200101
where
	co.status in (1,2,3)
	and co.lyy_distributor_id = :distributor_id
	and co.ds >= :ds
	and co.ds < :endds