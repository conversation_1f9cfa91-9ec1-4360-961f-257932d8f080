package cn.lyy.merchant.config;

import java.util.Map;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023/10/18 - 16:10
 */
@Data
@Component
@ConfigurationProperties(prefix = "payment-refactor")
public class PaymentRefactorProperties {

    private Map<String, Integer> grayMap;

}
