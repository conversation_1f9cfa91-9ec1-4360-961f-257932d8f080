package cn.lyy.merchant.config;

import cn.lyy.merchant.constants.SystemConstants;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <p>Title:fork</p>
 * <p>Desc: 系统配置</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020/11/19
 */
@Component
public class SystemConfig {

    /**
     * 1.0和2.0默认系统权限配置
     * @param roleId
     */
    @Value("${merchant.role.version1.id}")
    public void setMerchantRoleVersion1Id(Long roleId) {
        SystemConstants.MERCHANT_BACKEND_ROLE_ID_VERSION_1 = roleId;
    }
    @Value("${merchant.role.version2.id}")
    public void setMerchantRoleId(Long roleId) {
        SystemConstants.MERCHANT_BACKEND_ROLE_ID = roleId;
    }
}
