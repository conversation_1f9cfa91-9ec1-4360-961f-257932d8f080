package cn.lyy.merchant.config;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.module.SimpleModule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.jackson.JsonComponent;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * json序列化
 * <AUTHOR>
 * @className: MyJsonComponentConfig
 * @date 2021/5/28
 */
//@JsonComponent
@Slf4j
public class MyJsonComponentConfig {
    private static final String LONG_NEW_STRING_SUFFIX = "-str";

    /**
     * 处理long类型时，自动把增加一个对字符串数值类型的描述，格式为 key+ ”-str“
     * 如原来的{"t4":99999999999} 会生成 {"t4":99999999999,"t4-str":"99999999999"}
     */
    public static class AddLongStrJsonSerializer extends JsonSerializer<Long>{
        @Override
        public void serialize(Long aLong, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
            if (aLong != null){
                jsonGenerator.writeNumber(aLong);
//                if( aLong < Integer.MIN_VALUE || aLong > Integer.MAX_VALUE){
                    //超出int范围可能引起前端显示数据异常
                    if(jsonGenerator.getOutputContext().hasCurrentName()){
                        String name = jsonGenerator.getOutputContext().getCurrentName();
                        String newName = name + LONG_NEW_STRING_SUFFIX;
                        try {
                            jsonGenerator.writeFieldName(newName);
                            jsonGenerator.writeString(aLong.toString());
                            log.debug("{} Long数据需要增加新属性 {},用于前端显示",name,aLong,newName);
                        }catch (Exception e){
                            log.debug("{} Long数据需要增加新属性 {} 失败",name,aLong,newName);
                        }
                    }
//                }
            }

        }
    }

    public static void main(String[] args) throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();
        SimpleModule simpleModule = new SimpleModule();
        simpleModule.addSerializer(Long.class, new AddLongStrJsonSerializer());
        objectMapper.registerModule(simpleModule);

        Map<String,Object> map = new HashMap<>();
        map.put("t1",10);
        map.put("t2",10000000000L);
        map.put("t2-str","789");
        map.put("t3","10000000000");
        map.put("t4",99999999999L);
        map.put("t5",99L);
        String str = objectMapper.writeValueAsString(map);
        System.out.println(str);
    }
}
