package cn.lyy.merchant.config;

import java.util.List;
import javax.annotation.PostConstruct;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2024/11/21
 */
@Slf4j
@Data
@Configuration
public class UmsGrayConfig {

    @Value("${position.ums.fullGray:false}")
    private Boolean umsFullGray;

    @Value("${position.ums.merchantIds:}")
    private List<Long> merchantIds;

    public boolean isUmsGray(Long merchantId) {
        if (umsFullGray) {
            return true;
        }
        if (merchantIds != null && merchantIds.contains(merchantId)) {
            log.info("站内信灰度消息中心，商家:{}", merchantId);
            return true;
        }
        return false;
    }

    @PostConstruct
    public void init() {
        log.info("站内信灰度配置:{}", this);
    }
}
