package cn.lyy.merchant.config;

import cn.lyy.merchant.filter.CommonParamsContext;
import com.alibaba.fastjson.JSON;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

/**
 * fegin请求头
 *
 * <AUTHOR>
 * @date 2022/1/27
 **/
@Configuration
@Slf4j
public class FeignRequestConfig implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate template) {
        template.header("storeId", Optional.ofNullable(CommonParamsContext.getStoreId()).map(String::valueOf).orElse(null));
        template.header("merchantId", Optional.ofNullable(CommonParamsContext.getBelongTo()).map(String::valueOf).orElse(null));
        template.header("orgnizationId", Optional.ofNullable(CommonParamsContext.getOrgnizationId()).map(String::valueOf).orElse(null));
        template.header("adUserId", Optional.ofNullable(CommonParamsContext.getAdUserId()).map(String::valueOf).orElse(null));
        if (log.isDebugEnabled()) {
            log.debug("commonParams:{}", JSON.toJSONString(CommonParamsContext.getCommonParams()));
        }
    }

}
