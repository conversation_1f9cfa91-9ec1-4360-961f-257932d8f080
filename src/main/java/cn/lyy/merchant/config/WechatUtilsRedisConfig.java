package cn.lyy.merchant.config;

import cn.lyy.base.utils.WechatUtilsRedisClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * @ClassName: WechatUtilsRedisConfig
 * @description: WechatUtilsRedisConfig初始化
 * @author: pengkun
 * @create: 2020-11-24 19:49
 * @Version 1.0
 **/
@Component
public class WechatUtilsRedisConfig {

    @Autowired
    private StringRedisTemplate template;

    @Bean
    @PostConstruct
    public void initRedisClient() {
        WechatUtilsRedisClient.setRedisTemplate(template);
    }
}
