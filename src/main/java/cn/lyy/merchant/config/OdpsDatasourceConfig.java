package cn.lyy.merchant.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.zaxxer.hikari.HikariDataSource;
import java.sql.SQLException;
import javax.sql.DataSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

/**
 * 阿里云数仓配置
 *
 */
@Configuration
@MapperScan(basePackages = "cn.lyy.merchant.dao",sqlSessionTemplateRef ="odpsSessionTemplate")
@Slf4j
public class OdpsDatasourceConfig {

    /**本数据源扫描的mapper路径*/
    static final String MAPPER_LOCATION = "classpath*:mapper/odps/*.xml";

    /**
     * odpsDataSource
     * @return 数据源
     */
    @ConfigurationProperties(prefix = "odps.datasource")
    @Bean(name = "odpsDataSource")
    public DataSource odpsDataSource() {
        //指定type即可，会自动匹配
        DataSource dataSource = DataSourceBuilder.create().type(DruidDataSource.class).build();
        return dataSource;
    }

    /**
     * session factory
     * @param dataSource 数据源
     * @return session factory
     * @throws Exception 异常
     */
    @Bean(name = "odpsSessionFactory")
    public SqlSessionFactory odpsSessionFactory(@Qualifier("odpsDataSource") DataSource dataSource) throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        log.debug("====创建数仓datasource===={}", dataSource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources(MAPPER_LOCATION));
        bean.getObject().getConfiguration().setMapUnderscoreToCamelCase(true);
        //DruidPooledPreparedStatement t = new DruidPooledPreparedStatement();
        return bean.getObject();
    }

    @Bean
    public DataSourceTransactionManager masterTransactionManager(@Qualifier("odpsDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean("odpsSessionTemplate")
    public SqlSessionTemplate odpsSessionTemplate(
        @Qualifier("odpsSessionFactory") SqlSessionFactory odpsSessionFactory) throws SQLException {
        SqlSessionTemplate template = new SqlSessionTemplate(odpsSessionFactory);
        return template;
    }

}
