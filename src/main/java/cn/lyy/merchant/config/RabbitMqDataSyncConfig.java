package cn.lyy.merchant.config;

import cn.lyy.datasync.dto.DataSyncDTO;
import cn.lyy.datasync.dto.EquipmentGroupSyncDTO;
import cn.lyy.datasync.dto.EquipmentSyncDTO;
import cn.lyy.datasync.dto.EquipmentTypeSyncDTO;
import com.google.gson.Gson;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.AmqpException;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.UUID;

/**
 * <p>Title:saas2</p>
 * <p>Desc: 2.0（设备、场地）数据同步到1.0 mq配置</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/10/8
 */
@Slf4j
@Data
@Configuration
@ConfigurationProperties(prefix = "data-sync.rabbitmq")
public class RabbitMqDataSyncConfig {

    private String host;
    private int port;
    private String username;
    private String password;

    public RabbitTemplate rabbitTemplate;

    @Value("${message.rabbit.data-sync-exchange:cn.lyy.data-sync.exchange}")
    private String dataSyncExchange;
    @Value("${message.rabbit.data-sync.equipment-queue:cn.lyy.data-sync.equipment-queue}")
    private String dataSyncEquipmentQueue;
    @Value("${message.rabbit.data-sync.equipment-type-queue:cn.lyy.data-sync.equipment-type-queue}")
    private String dataSyncEquipmentTypeQueue;
    @Value("${message.rabbit.data-sync.equipment-group-queue:cn.lyy.data-sync.equipment-group-queue}")
    private String dataSyncEquipmentGroupQueue;

    @Bean(name = "dataSyncConnectionFactory")
    public ConnectionFactory dataSyncConnectionFactory() {
        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
        connectionFactory.setHost(host);
        connectionFactory.setPort(port);
        connectionFactory.setUsername(username);
        connectionFactory.setPassword(password);
        return connectionFactory;
    }

    @Bean(name = "dataSyncRabbitTemplate")
    public RabbitTemplate dataSyncRabbitTemplate(@Qualifier("dataSyncConnectionFactory") ConnectionFactory connectionFactory) {
        rabbitTemplate = new RabbitTemplate(connectionFactory);
        rabbitTemplate.setMessageConverter(new Jackson2JsonMessageConverter());
        return rabbitTemplate;
    }




    public void sendEquipmentToDataSync(DataSyncDTO<EquipmentSyncDTO> content) {
        try {
            this.sendMsg(this.dataSyncEquipmentQueue, content);
        } catch (AmqpException var3) {
            log.error("同步设备信息失败:{}, message:{}", ((EquipmentSyncDTO)content.getData()).getUniqueCode(), var3.getMessage());
        }

    }

    public void sendEquipmentTypeToDataSync(DataSyncDTO<EquipmentTypeSyncDTO> content) {
    }

    public void sendEquipmentGroupToDataSync(DataSyncDTO<EquipmentGroupSyncDTO> content) {
        try {
            this.sendMsg(this.dataSyncEquipmentGroupQueue, content);
        } catch (Exception var3) {
            log.error("同步设备场地信息失败:{}, message:{}", ((EquipmentGroupSyncDTO)content.getData()).getLyyDistributorId(), var3.getMessage());
        }

    }

    private void sendMsg(String queue, Object content) {
        rabbitTemplate.convertAndSend(this.dataSyncExchange, queue, (new Gson()).toJson(content), new CorrelationData(UUID.randomUUID().toString()));
    }

    public void confirm(CorrelationData correlationData, boolean ack, String cause) {
        log.info(" 回调id:" + correlationData);
        if (ack) {
            log.info("消息成功消费");
        } else {
            log.info("消息消费失败:" + cause);
        }

    }
}
