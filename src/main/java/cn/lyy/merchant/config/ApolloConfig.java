package cn.lyy.merchant.config;

import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.cloud.context.environment.EnvironmentChangeEvent;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> {<EMAIL>}
 * @date 2020/7/22 14:50
 *
 * Apollo 配置
 **/
@Slf4j
@Component("appApolloConfig")
@EnableApolloConfig
public class ApolloConfig implements ApplicationContextAware {

    private ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @ApolloConfigChangeListener(value = {"application.yml","merchant.authority.properties","payment-refactor.properties","middle-stage.properties"})
    private void handlerListener(ConfigChangeEvent event) {
        // 遍历输出配置更新的属性值的key
        event.changedKeys().forEach(key -> log.info("##### Change Keys: {}", event.getChange(key)));

        // 重新绑定屬性值
        applicationContext.publishEvent(new EnvironmentChangeEvent(event.changedKeys()));
    }
}
