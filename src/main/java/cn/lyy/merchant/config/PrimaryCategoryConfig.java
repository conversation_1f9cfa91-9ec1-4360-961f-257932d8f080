package cn.lyy.merchant.config;

import com.google.common.collect.Lists;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2025/6/10 - 16:56
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "merchant")
public class PrimaryCategoryConfig {

    private Map<String, PrimaryCategory> primaryCategories = new HashMap<>();

    @Data
    public static class PrimaryCategory {

        private List<String> equipmentType = Lists.newArrayList();

        /**
         * 防止修改
         */
        public List<String> getEquipmentType() {
            return Collections.unmodifiableList(equipmentType);
        }
    }

}
