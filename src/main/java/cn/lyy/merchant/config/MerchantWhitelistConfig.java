package cn.lyy.merchant.config;

import java.util.List;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 可以操作的白名单判断
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "merchant.whitelist")
public class MerchantWhitelistConfig {

    private List<Integer> enabledControlTypeList;

    public Boolean include(Integer type) {
        if (CollectionUtils.isEmpty(enabledControlTypeList)) {
            return false;
        }
        if (enabledControlTypeList.contains(type)) {
            return true;
        }
        return false;
    }
}
