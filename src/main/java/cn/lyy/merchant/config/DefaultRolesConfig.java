package cn.lyy.merchant.config;

import lombok.Data;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * <p>Title:saas2</p>
 * <p>Desc: 默认角色与权限配置</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020/11/14
 */
@Data
@ToString
@Configuration
@ConfigurationProperties(prefix = "authority")
@EnableConfigurationProperties(DefaultRolesConfig.class)
public class DefaultRolesConfig {

    private List<Role> roles;
    private List<Menu> menus;
    private List<MenuType> menuTypes;
    

    @Data
    public static class Role {
        /**
         * 用于排序
         */
        private Integer id;
        private String name;
        private String authorities;
    }

    @Data
    public static class MenuType{
        private Integer id;
        //类目编码
        private String code;
        //类目名称
        private String name;
    }

    @Data
    public static class Menu {
        /**
         * 用于排序
         */
        private Integer id;
        //类目编码
        private String typeCode;

        //菜单名称(权限名称)
        private String menuName;
        //菜单编码(权限编码)
        private String menuCode;
        //能否显示，true：showIndexPage有效
        private boolean showEnabled;
        //首页显示设置
        private ShowIndexPage showIndexPage;
    }

    @Data
    public static class ShowIndexPage{
        //能否修改显不显示
        private boolean canChange;
        //默认显示值
        private boolean defaultShow;
    }
}
