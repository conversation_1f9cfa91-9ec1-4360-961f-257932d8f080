package cn.lyy.merchant.config;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class MemberQueryConfig {

    /**
     * 会员selectdb查询全局开关
     */
    @Value("${member.selectdb.query.full.gray:false}")
    private Boolean memberSelectdbQueryFullGray;


    /**
     *会员selectdb查询商户白名单
     */
    @Value("${member.selectdb.query.distributor.list:0}")
    private List<Long> memberSelectdbQueryDistributorList;


    public Boolean getMemberSelectdbQuery(Long distributorId) {
        return memberSelectdbQueryFullGray ||
                (distributorId != null && CollectionUtils.isNotEmpty(memberSelectdbQueryDistributorList) && memberSelectdbQueryDistributorList.contains(distributorId));

    }


}
