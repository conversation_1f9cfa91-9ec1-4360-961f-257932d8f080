package cn.lyy.merchant.config;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 线程池定义和注入
 * <AUTHOR>
 * @since 2023-04-13
 */

@Configuration
public class ThreadPoolConfig {


    /**
     * 用来作为设备列表统计过程中并行计算的线程池
     * @return 线程池对象
     */
    @Bean("equipmentListPool")
    public ExecutorService equipmentListPool() {
        return new ThreadPoolExecutor(
                Runtime.getRuntime().availableProcessors(),
                Runtime.getRuntime().availableProcessors() * 2, 30, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(500),
                r -> {
                    Thread thread = new Thread(r);
                    thread.setName("equipment-list-pool");
                    return thread;
                }, new ThreadPoolExecutor.CallerRunsPolicy());
    }

}
