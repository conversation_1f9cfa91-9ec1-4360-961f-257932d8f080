package cn.lyy.merchant.constants;

/**
 * <p>Title:saas2</p>
 * <p>Desc: 协议功能参数枚举，对应配置在 equpment_type_function 表</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020/4/7
 */
public enum ProtocolCodeEnum {

    FEE_MODE("TIME", "按时长计费"),
    PORT_USED("OPEN", "端口禁用"),
    EXTRA_TIME("OPEN", "续充加时"),
    START_UP_MODE("MODE", "启动套餐"),
    START_ADD_LIQUID("OPEN", "支持加液"),
    SUP_CHECK_LOADED("OPEN", "支持负载检查"),
    SUP_CLEAR_FAULT("OPEN", "支持清除故障"),
    NOT_SUP_DECIMAL("OPEN", "不支持小数金额"),
    SUP_STOP_DEVICE("OPEN", "支持设备停止"),
    SUP_LOCK_PORT("OPEN", "支持锁定启动端口"),
    DELIVER_WAY_BOTH("OPEN", "支持多个计费方式下发"),
    NOT_SUP_CONTROL_PORT("OPEN", "不支持端口控制"),
    SUP_REMOTE_STOP("OPEN", "支持远程停止")
    ;

    String defaultName;
    String desc;

    ProtocolCodeEnum(String defaultName, String desc) {
        this.defaultName = defaultName;
        this.desc = desc;
    }
}
