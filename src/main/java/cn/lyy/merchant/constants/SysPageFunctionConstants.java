package cn.lyy.merchant.constants;

public interface SysPageFunctionConstants {

    /**
     * 乐摇摇SAAS B端系统ID
     */
    String AUTH_SYSTEM_ID = "1197142797590298624";

    /**
     * B端新首页页面ID
     */
    String MERCHANT_HOME_PAGE_ID = "1199725073357242368";


    // ==========以下是规则配置中用到的参数==========
    /**
     * 是否主账号，取值：Y/N
     */
    String APPROVER = "approver";

    /**
     * 黑白名单，取值：名单编号,多个用逗号分隔
     */
    String BW_LIST = "bwList";

    /**
     * 设备类型，取值：设备类型编码,多个用逗号分隔
     */
    String EQUIPMENT_TYPE_LIST = "equipmentTypeList";

    /**
     * 所拥有设备类型的余额余币，取值：余额余币类型值,多个用逗号分隔
     */
    String EQUIPMENT_TYPE_BALANCE_LIST = "equipmentTypeBalanceList";

    /**
     * 商户是否存在未实名的资料，取值：Y/N
     */
    String MERCHANT_UN_REAL_NAME = "merchantUnRealName";

    /**
     * 商户是否在体验期中，取值：Y/N
     */
    String MERCHANT_IN_EXPERIENCE = "merchantInExperience";

    /**
     * 商户是否体验期过期告警，取值：Y/N
     */
    String MERCHANT_EXPERIENCE_NOTICE = "merchantExperienceNotice";

    /**
     * 商户是否未绑卡，取值：Y/N
     */
    String MERCHANT_UN_BIND_CARD = "merchantUnBindCard";

    /**
     * 商户是否入网资料错误，取值：Y/N
     */
    String MERCHANT_ACCESS_INFO_ERROR = "merchantAccessInfoError";

    /**
     * 商户是否开通分账，取值：Y/N
     */
    String MERCHANT_OPEN_DIVIDE = "merchantOpenDivide";

    /**
     * 商户是否有复购易弹窗，取值：Y/N
     */
    String MERCHANT_FGY_POPUP = "merchantFgyPopup";

    /**
     * 商户是否有配置复购易活动，取值：Y/N
     */
    String MERCHANT_FGY_CONFIG = "merchantFgyConfig";

    /**
     * 识别费(余额不足)通知弹窗，取值：Y/N
     */
    String IDENTIFY_FEE_BNE_POPUP = "identifyFeeBnePopup";

    /**
     * 识别费(余额小于设备数*10)通知弹窗，取值：Y/N
     */
    String IDENTIFY_FEE_BLT_POPUP = "identifyFeeBltPopup";

    /**
     * 待缴服务费类型
     */
    String FEE_TO_PAY_TYPE = "feeToPayType";

    /**
     * 待缴服务费内容
     */
    String FEE_TO_PAY_CONTENT = "feeToPayContent";

    /**
     * 业务服务费弹窗，取值：Y/N
     */
    String BIZ_FEE_POPUP = "bizFeePopup";

    /**
     * 经营快报弹窗，取值：Y/N
     */
    String BUSINESS_EXPRESS_POPUP = "businessExpressPopup";

    /**
     * 商户拥有AI兑币机设备的数量
     */
    String AI_DBJ_COUNT = "aiDbjCount";

    /**
     * AI兑币机过期告警数量
     */
    String AI_DBJ_EXPIRE_SOON_COUNT = "aiDbjExpireSoonCount";

    /**
     * AI兑币机数据报告弹窗，取值：Y/N
     */
    String AI_DBJ_DATA_REPORT_POPUP = "aiDbjDataReportPopup";

    /**
     * 智能导购提升率，取值：0-100
     */
    String SHOPPING_GUIDE_PROPORTION = "shoppingGuideProportion";

    /**
     * 智能导购试用报名条件，取值：Y/N
     */
    String SHOPPING_GUIDE_TRIAL_REG = "shoppingGuideTrialReg";

    /**
     * 洗水预充弹窗，取值：Y/N
     */
    String WASH_RECHARGE_POPUP = "washRechargePopup";
    /**
     * 洗水校园卡弹窗，取值：Y/N
     */
    String WASH_CAMPUS_CARD_POPUP = "washCampusCardPopup";

    /**
     * 按摩椅次卡弹窗，取值：Y/N
     */
    String MASSAGE_TIME_POPUP = "massageTimePopup";

    /**
     * 充电桩退款工单数量
     */
    String CHARGE_WORK_ORDER_NUM = "chargeWorkOrderNum";

    /**
     * 洗衣机退款工单数量
     */
    String WASH_WORK_ORDER_NUM = "washWorkOrderNum";

    /**
     * 退款工单总数量
     */
    String TOTAL_WORK_ORDER_NUM = "totalWorkOrderNum";

    /**
     * 流量卡续费弹窗，取值：Y/N
     */
    String CHARGE_FLOWCARDRENEW_POPUP = "chargeFlowCardRenewPopup";

    /**
     * 升购储值弹窗，取值：Y/N
     */
    String CHARGE_UPWARDPURCHASE_POPUP = "chargeUpwardPurchasePopup";

    /**
     * 充电桩保险saas2.5场地ID，取值：NULL或者场地ID值
     */
    String CHARGE_INSURANCE_GROUP_25 = "chargeInsuranceGroup25";

    /**
     * 充电桩保险saas2.0场地ID，取值：NULL或者场地ID值
     */
    String CHARGE_INSURANCE_GROUP_20 = "chargeInsuranceGroup20";

    /**
     * 新手任务-是否完成支付任务，取值：Y/N
     */
    String NEWBIE_TASK_PAYMENT = "newbieTaskPayment";

    /**
     * 新手任务-是否完成设备任务，取值：Y/N
     */
    String NEWBIE_TASK_EQUIPMENT = "newbieTaskEquipment";

    /**
     * 新手任务-全部完成，取值：Y/N
     */
    String NEWBIE_TASK_ALL = "newbieTaskAll";

    /**
     * 弱密码弹框
     */
    String WEAK_PASSWORD_POPUP = "weakPasswordPopup";

    /**
     * SAAS企业版免费体验弹窗
     */
    String SAAS_EXPERIENCE_POPUP = "saasExperiencePopup";

    /**
     * 取消订阅SAAS企业版免费体验弹窗
     */
    String CANCELED_SAAS_EXPERIENCE_POPUP = "canceledSaasExperiencePopup";

    /**
     * 是否钱包结算
     */
    String WALLET_SETTLEMENT = "walletSettlement";

    /**
     * 场地管理
     */
    String STATION_MENU = "address";
    String TEMPLATE_MENU = "templateManage";
    String CHARGING_ORDER_MENU = "car_charging_records";
    String PORT_MANAGEMENT_MENU = "port_management";

    /**
     * 会员管理
     */
    String MEMBER_MENU = "memberManager";
    /**
     * 申诉反馈菜单
     */
    String APPEAL_FEEDBACK_MENU = "complaint_feedback";

    /**
     * 积分兑换
     */
    String REDEEM_MENU = "integral_scan";

    /**
     * 标签管理
     */
    String LABEL_MANAGEMENT_MENU = "labelManager";

    /**
     * 升购储值
     */
    String MARKET_PRE_RECHARGE = "upgrade";

    /**
     * IC卡管理
     */
    String IC_CARD_MANAGE = "ic_card_manage";

    /**
     * 用电量统计 elec_count
     */
    String ELEC_COUNT  = "elec_count";

    /**
     * 手续费补贴
     */
    String COMMISSION_INDEX  = "commission_index";

    /**
     * 计费模式
     */
    String PRICE_MODEL  = "priceModel";

    /**
     * 运营数据
     */
    String OPERATIONAL_DATA  = "operational_data";

}
