package cn.lyy.merchant.constants;

public enum WechatIndustryCodeEnum {

    CODE1("IT科技", "互联网|电子商务", "1"),
    CODE2("IT科技", "IT软件与服务", "2"),
    CODE3("IT科技", "IT硬件与设备", "3"),
    CODE4("IT科技", "电子技术", "4"),
    CODE5("IT科技", "通信与运营商", "5"),
    CODE6("IT科技", "网络游戏", "6"),
    CODE7("IT科技", "电子技术", "7"),
    CODE8("金融业", "银行", "8"),
    CODE9("金融业", "基金理财信托", "9"),
    CODE10("金融业", "保险", "10"),
    CODE11("餐饮", "餐饮", "11"),
    CODE12("酒店旅游", "酒店", "12"),
    CODE13("酒店旅游", "旅游", "13"),
    CODE14("运输与仓储", "快递", "14"),
    CODE15("运输与仓储", "物流", "15"),
    CODE16("运输与仓储", "仓储", "16"),
    CODE17("教育", "培训", "17"),
    CODE18("教育", "院校", "18"),
    CODE19("政府与公共事业", "学术科研", "19"),
    CODE20("政府与公共事业", "交警", "20"),
    CODE21("政府与公共事业", "博物馆", "21"),
    CODE22("医药护理", "医药医疗", "22"),
    CODE23("医药护理", "护理美容", "23"),
    CODE24("医药护理", "保健与卫生", "24"),
    CODE25("交通工具", "汽车相关", "25"),
    CODE26("交通工具", "摩托车相关", "25"),
    CODE27("交通工具", "飞机相关", "27"),
    CODE28("交通工具", "汽车相关", "28"),
    CODE29("房地产", "建筑", "29"),
    CODE30("房地产", "物业", "30"),
    CODE31("消费品", "消费品", "31"),
    CODE32("商业服务", "法律", "32"),
    CODE33("商业服务", "会展", "33"),
    CODE34("商业服务", "中介服务", "34"),
    CODE35("商业服务", "认证", "35"),
    CODE36("商业服务", "审计", "36"),
    CODE37("文体娱乐", "传媒", "37"),
    CODE38("文体娱乐", "体育", "38"),
    CODE39("文体娱乐", "娱乐休闲", "39"),
    CODE40("印刷", "印刷", "40"),
    CODE41("其它", "其它", "41");

    private String first;
    private String second;
    private String code;

    WechatIndustryCodeEnum(String first, String second, String code) {
        this.first = first;
        this.second = second;
        this.code = code;

    }

    public String getFirst() {
        return first;
    }

    public String getSecond() {
        return second;
    }

    public String getCode() {
        return code;
    }

    public static WechatIndustryCodeEnum getEnumBySecondClass(String name) {
        for (WechatIndustryCodeEnum tempNum : WechatIndustryCodeEnum.values()) {
            if (tempNum.getSecond().equals(name)) {
                return tempNum;
            }
        }
        return null;
    }

    public static WechatIndustryCodeEnum getEnumByCode(String code) {
        for (WechatIndustryCodeEnum tempNum : WechatIndustryCodeEnum.values()) {
            if (tempNum.getCode().equals(code)) {
                return tempNum;
            }
        }
        return null;
    }
}