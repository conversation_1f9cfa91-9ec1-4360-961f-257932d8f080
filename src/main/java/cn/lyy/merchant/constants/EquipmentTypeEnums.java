package cn.lyy.merchant.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 类描述：
 * <p>
 *
 * <AUTHOR>
 * @since 2020/11/5 16:58
 */
@AllArgsConstructor
@Getter
public enum EquipmentTypeEnums {

    /**
     *
     */
    CHARGING_PILE("CDZ", "充电桩"),
    MASSAGE_CHAIR("AMY", "按摩椅"),
    MASSAGE_MAT("AMD", "按摩垫"),
    GASHAPON("NDJ", "扭蛋机"),
    FOOT_MASSAGE_MACHINE("ZLJ", "足疗机"),
    CHARGING_PILE_FOR_CAR("QCCDZ", "汽车充电桩"),
    AMMETER("DB", "电表"),
    WASHER("XYJ", "洗衣机"),
    MCCDZ("MCCDZ","脉冲充电桩"),
    CHILDREN_CLASS("ETL", "儿童类"),
    BOXING_MACHINE("QJJ", "拳击机"),
    SELL_WATER_MACHINE("SSJ", "洗衣机"),
    GAME_MACHINE("YXJ", "游戏机"),

    ;
    /**
     * 编码
     */
    private String value;

    /**
     * 设备类型名称
     */
    private String name;

    /**
     * 是否充电桩
     * @param equipmentTypeValue
     * @return
     */
    public static boolean isCDZLogic(String equipmentTypeValue) {
        return Arrays.asList(CHARGING_PILE.getValue(), CHARGING_PILE_FOR_CAR.getValue()).contains(equipmentTypeValue);
    }

    public static EquipmentTypeEnums of(String equipmentType) {
        for (EquipmentTypeEnums typeEnum : EquipmentTypeEnums.values()) {
            if (typeEnum.getValue().equals(equipmentType)) {
                return typeEnum;
            }
        }
        return null;
    }
}
