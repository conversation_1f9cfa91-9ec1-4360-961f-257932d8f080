package cn.lyy.merchant.constants;

import java.util.Arrays;

public enum EquipmentEnum {
    YES("Y" , "YES"),
    NO("N" , "NO"),
    ERROR("E" , "UNKNOWN"),
    FEE_RULE_TYPE_EQUIPMENT("equipment" , "设备"),
    FEE_RULE_TYPE_GROUP("group" , "场地"),

    OPERATION_TYPE_TRANSFER("Transfer", "转移"),
    OPERATION_TYPE_REGISTER("Register", "注册"),
    OPERATION_TYPE_UNBIND("Unbind", "解绑"),
    OPERATION_TYPE_SETPRICE("SetPrice", "定价"),
    OPERATION_TYPE_QUICKSELECT("QuickSelect", "快速选择"),
    OPERATION_TYPE_OTHER("Other", "其他"),
    ;
    String value;
    String name;

    public String getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    EquipmentEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static EquipmentEnum findByValue(String value){
        return Arrays.asList(EquipmentEnum.values()).stream().filter(item -> item.value.equals(value)).findFirst().orElse(null);
    }
}
