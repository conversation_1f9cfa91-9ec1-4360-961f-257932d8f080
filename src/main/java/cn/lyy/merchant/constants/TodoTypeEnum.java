package cn.lyy.merchant.constants;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Getter
public enum TodoTypeEnum {
    CHANNEL_RISK(1 , "渠道风控"),
    OPEN_COLLECTION(2, "新手任务开通收款"),
    CHARGING_REMIND(3 , "待缴服务费提醒"),
    BIND_EQUIPMENT(4,"未绑定设备"),
    WRONG_COLLECTION_INFO(5,"收款资料有误"),
    REAL_NAME(6,"去实名"),
    EXPERIENCE_EXPIRE_NOTICE(7,"体验期过期预警"),
    SAAS_PRODUCT_CHARGING_NOTIFY(8,"SAAS续订提醒"),
    ;

    final int code;
    final String msg;
}
