package cn.lyy.merchant.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 类描述：
 * <p>
 *
 * <AUTHOR>
 * @since 2020/11/5 11:36
 */
@AllArgsConstructor
@Getter
public enum BusinessExceptionEnums {

    /**
     * 异常枚举
     */
    PARAM_ERROR("参数异常", "E10000"),
    DEVICE_NOT_EXISTS("设备不存在", "E10001"),
    CHANNEL_ARGS_MISSING("通道参数缺失", "E10002"),
    MAIN_BOARD_ARGS_MISSING("主板参数缺失", "E10003"),
    EQUIPMENT_OFFLINE("设备离线了", "E10004"),
    GROUP_NOT_ALLOW_DELETED("已绑定设备的场地不能删除", "E10005"),
    NOT_SUPPORT_BATCH_REGISTER("不支持批量注册！", "E10006"),
    REGISTER_TEMPLATE_MISSING("找不到该设备类型的注册模板", "E10007"),
    NOT_SUPPORT_EQUIPMENT_TYPE("不支持此设备类型的注册", "E10008"),
    EQUIPMENT_TYPE_MISSING("设备类型不存在", "E10009"),
    GROUP_NOT_EXIST("该投放地址不存在", "E10010"),
    REGISTER_FAIL("注册设备%s失败", "E10011"),
    ALREADY_REGISTERED("设备%s已被注册", "E10012"),
    ONLY_BATCH_REGISTER_ONE_TYPE("不可批量注册不同类型设备", "E10013"),
    ONLY_BATCH_REGISTER_SAME_FACTORY("只能批量添加同一工厂生产的设备", "E10014"),
    PROTOCOL_NOT_FOUND("未匹配到相应协议", "E10015"),
    EQUIPMENT_HAS_NOT_BEEN_REGISTERED("设备未注册", "E10016"),
    CAN_NOT_TRANSFER_TO_CURRENT_GROUP("设备不能转移到相同场地", "E10017"),
    IS_NOT_YOUR_EQUIPMENT("不是您的设备，不可进行转移", "E10018"),
    FEE_MODE_NOT_SUPPORT("不能识别的计费标准:%s", "E10019"),
    FEE_MODE_MISSING("计费规则不能为空", "E10020"),
    FEE_RULE_SAVE_ERROR("保存计费规则异常", "E10021"),
    CREATE_ORDER_ERROR("下单失败，请稍后重试", "E10022"),
    PAY_INVOKE_ERROR("支付失败，请稍后重试", "E10023"),
    MEMBER_NOT_EXIST("会员信息不存在", "ERROR_24"),
    OPERATE_ERROR("操作失败，请稍后重试", "E10025"),
    GROUP_NUM_ERROR("机台号已被占用，请重新输入", "E10026"),
    FEE_RULE_DEL_ERROR("计费规则删除异常", "E10027"),
    MERCHANT_ALREADY_REGISTER_EQUIPMENT("请输入当前商家绑定的设备号", "E10028"),
    YOU_HAS_NO_AUTHORIZE("无权限操作", "E10029"),
    OSS_UPLOAD_ERROR("OSS上传文件失败", "E10030"),
    AT_LEAST_ONE_GROUP("必须选择至少一个场地", "E10031"),
    POST_CAN_NOT_DELETE("该岗位不能删除", "E10032"),
    //强制提示
    SUB_ACCOUNT_NOT_PERMIT("子账户无权限操作！", "E10033"),
    GET_PARAM_FAIL("获取参数失败，系统正在异步重试", "E10034"),
    TAG_BUSINESS_TYPE_ERROR("标签业务类型错误", "E10035"),
    FOUND_USER_INFO_ERROR("商户用户信息异常", "E10036"),
    TAG_NAME_CAN_NOT_NULL("标签名不能为空", "E10037"),
    TAG_NAME_OVER_LENGTH("标签名超出长度", "E10038"),
    TAG_NAME_HAS_ILLEGAL_CHAR("标签名有非法字符", "E10039"),
    BATCH_REGISTER_NUM("批量注册设备过多，请控制在200台以下", "E10040"),
    BATCH_OPERATE_NUM("批量设置设备过多，请控制在%s台以下", "E10041"),
    SET_EQUIPMENT_ERROR("设备设备信息失败", "E10042"),

    SAVE_SKU_ERROR("新增商品失败", "E10043"),
    MERCHANT_ID_IS_NUMM_ERROR("商户ID为空", "E10044"),

    SAVE_CONSUMER_SOLUTION_ERROR("新增消费方案失败", "E10045"),
    EDIT_CONSUMER_SOLUTION_ERROR("修改消费方案失败", "E10046"),
    EQUIPMENT_ASSOCIATION_CONSUMER_SOLUTION_ERROR("设备关联消费方案失败", "E10047"),
    DELETE_CONSUMER_SOLUTION_ERROR("删除消费方案失败", "E10048"),

    MERCHANT_ADJUST_ERROR("商家调整用户权益失败", "E10050"),

    MERCHANT_ANNUAL_REPORT_SUB_ACCOUNT_NOT_PERMIT("因年度报告内容涉及个人及经营等相关信息，仅支持主账号登录获取，您的账号暂无权限。", "E10051"),
    MERCHANT_ANNUAL_REPORT_NOT_EXIST("本年度报告仅针对娱乐类设备商家，如您无经营该品类，暂无法查看本报告。", "E10052"),
    MERCHANT_ANNUAL_REPORT_NULL("系统未能获取您的经营信息，暂无法查看年度报告。", "E10053"),

    NOT_SUPPORT_GROUP_SERVICE("此系统不支持操作此设备套餐", "E10054"),

    NOT_SUPPORT_BATCH_SERVICE("场地有1.0设备，不允许批量操作套餐", "E10055"),

    PACKAGE_LACK_IMITATE_COINS("当前消费方案中的套餐缺少模拟投币数,设备将无法启动,请尽快完善!", "E10056"),

    PACKAGE_LACK_IMITATE_START_NUM("当前消费方案中的套餐缺少启动数量,设备将无法启动,请尽快完善!", "E10057"),

    PACKAGE_LACK_IMITATE_ALL_COINS("当前消费方案中的套餐设置了模拟投币数，其余套餐也需设置,请尽快完善!", "E10057"),

    PACKAGE_LACK_IMITATE_ALL_START_NUM("当前消费方案中的套餐设置了启动数量，其余套餐也需设置,请尽快完善!", "E10058"),

    SERVICE_ERROR("调用失败", "E10059"),

    ;

    /**
     * 异常信息
     */
    private String msg;
    /**
     * 异常码
     */
    private String exCode;
}
