package cn.lyy.merchant.constants;

import lombok.Getter;

/**
 * <p>Title:saas2</p>
 * <p>Desc: 业务事件</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020/4/9
 */
@Getter
public enum BusinessTypeEnums {

    COMFIRM_PAY("_ConfirmPay", "C端支付确认"),
    FEERULE_FILTER("_FeeRuleFilter", "C端计费规则过滤"),
    FEERULE("_FeeRule", "B端计费设置"),
    EQ_START("_EqStart", "B/C端设备启动");

    String event;
    String desc;

    BusinessTypeEnums(String event, String desc) {
        this.event = event;
        this.desc = desc;
    }

}
