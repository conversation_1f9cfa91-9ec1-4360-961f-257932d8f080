package cn.lyy.merchant.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 订单异常枚举类
 */
@AllArgsConstructor
@Getter
public enum OrderExceptionEnums {

    /**
     * 异常枚举
     */
    QUERY_PARAM_ERROR("订单查询参数异常", "ORDER10000"),
    ORDER_NOT_EXIST_ERROR("订单不存在", "ORDER10001"),
    REFUND_AMOUNT_ILLEAGL_ERROR("退款金额参数不合法", "ORDER10002"),
    REFUND_NOT_EXIST_ERROR("退款单不存在", "ORDER10003"),


    ;

    /**
     * 异常信息
     */
    private String msg;
    /**
     * 异常码
     */
    private String exCode;
}
