package cn.lyy.merchant.constants;

import lombok.Getter;

/**
 * <p>Title:saas2</p>
 * <p>Desc: 设备注册规则</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020/4/7
 */
@Getter
public enum EquipmentRegisterRuleEnum {

    TYPE("type", "与品类关联"),
    PROTOCOL("protocol", "与协议关联");

    String type;
    String desc;

    EquipmentRegisterRuleEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
