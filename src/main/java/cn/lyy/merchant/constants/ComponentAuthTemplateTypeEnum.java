package cn.lyy.merchant.constants;

import java.util.Arrays;

public enum ComponentAuthTemplateTypeEnum {
    XYJ_KSXY("ksxy", "开始洗衣"),
    XYJ_JSXY("jsxy", "洗衣完成"),
    AMY_KSAM("ksam", "开始按摩"),
    AMY_JSAM("jsam", "按摩完成"),
    CDZ_CDTX("cdtx", "充电提醒"),
    CDZ_JSCD("jscd", "结束充电"),
    CDZ_FWZTTX("fwzttx", "服务状态提醒"),
    WWJ_ZXQD("zxqd", "在线启动"),
    WWJ_QDSB("qdsb", "启动失败"),
    WWJ_CLCG("clcg", "出礼成功"),
    DBJ_CBCG("cbcg", "出币成功"),
    DBJ_CBSB("cbsb", "出币失败"),
    SHJ_START("start_success", "启动成功"),
    SSJ_MESSAGE("start_message", "售水机启动成功"),
    XCJ_MESSAGE("start_message", "洗车机启动通知"),
    XCJ_FREE("end_free", "洗车机自由洗结束"),
    XCJ_DURING("end_during", "洗车机时长洗结束");

    String value;
    String name;

    private ComponentAuthTemplateTypeEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public String getValue() {
        return this.value;
    }

    public String getName() {
        return this.name;
    }

}
