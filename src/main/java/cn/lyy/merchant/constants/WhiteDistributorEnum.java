package cn.lyy.merchant.constants;

import lombok.Getter;

@Getter
public enum WhiteDistributorEnum {

    /**
     * 3.0灰度商户白名单编号
     */
    GRAY_MERCHANT("3.0灰度商户白名单", 341),

    /**
     * 2.0商户白名单编号
     */
    SECOND_MERCHANT("2.0商户白名单", 356),

    CDZ_JUMP_ONE_ACCOUNT("充电桩-2.0/3.0b端跳转1.0(白)",2007),
    
    SINGLE_MERCHANT_MULTI_ACCOUNT("单账号多主体", 220),
    ;

    /**
     * 白名单名称
     */
    String name;
    /**
     * 白名单编号
     */
    Integer type;

    WhiteDistributorEnum(String name, Integer type) {
        this.name = name;
        this.type = type;
    }
}
