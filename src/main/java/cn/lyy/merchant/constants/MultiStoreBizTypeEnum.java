package cn.lyy.merchant.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 功能码
 *
 * <AUTHOR>
 * */
@Getter
@AllArgsConstructor
@ToString
public enum MultiStoreBizTypeEnum {



    NORMAL_QUERY("normal", "普通查询",""),
    DIVIDE_STORE("divideRuleStore","分成规则场地列表","(merchant-business)cn.lyy.merchant.controller.merchant.divide.DivideRuleGroupEquipmentController.listEquipmentDivideInfoPage"),
    ;




    private String code;

    private String description;

    /**
     * 备注 旧逻辑原接口或关键方法
     */
    private String note;

}
