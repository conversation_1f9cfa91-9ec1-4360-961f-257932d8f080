package cn.lyy.merchant.constants;

import java.util.Arrays;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum OrderBusinessType {

    /**
     * 枚举
     */
    COIN_EXCHANGE(1, "兑币机充值"),
    DOLL_EXCHANGE(2, "娃娃机充值"),
    CHILDREN_EXCHANGE(3, "儿童类充值"),
    CHILDREN_START(4, "儿童类启动"),
    BUGGY_EXCHANGE(5, "儿童车启动"),
    GRID_VENDING_START(6, "聚合品类售货机启动"),
    GRID_CHARGING_CABLE_START(7, "聚合品类手机充电线启动"),
    CONSTELLATION_EXCHANGE(8, "星座机充值"),
    CONSTELLATION_START(9, "星座机启动"),
    MINIMUM_PAY(10, "托底服务购买"),
    VENDING_START(12, "售货机启动"),
    GASHAPON_START(13, "扭蛋机启动"),
    MASSAGE_EXCHANGE(14, "按摩类充值"),
    MASSAGE_START(15, "按摩类启动"),
    SAAS2_PAY(16, "SAAS2.0支付充值"),
    SAAS2_START(17, "SAAS2.0支付启动"),
    MERIT_BOX_EXCHANGE(18, "功德箱捐赠"),
    GASHAPON_EXCHANGE(20, "扭蛋机充值"),
    ELECTRONIC_CIGARETTE_START(21, "电子烟售货机启动"),
    BARRAGE_EXCHANGE(22, "弹幕充值"),
    POWER_CLAW_EXCHANGE(23, "强爪充值"),
    LUCKY_COINS_PAY(24, "幸运购币支付"),
    CURRENT_LIFE_EXCHANGE(25, "通用生活类充值"),
    CURRENT_LIFE_START(26, "通用生活类启动"),
    WASHING_EXCHANGE(27, "洗衣机充值"),
    WASHING_START(28, "洗衣机启动"),
    CHARGING_EXCHANGE(29, "充电桩充值"),
    CHARGING_START(30, "充电桩启动"),
    VIP_FANS(31, "VIP加粉"),
    EASY_CATCH_PAY(32, "最容易抓支付"),
    AD_RAFFLE(33, "兑多多抽奖"),
    COINS_GAMBLE_PAY(34, "游戏出币支付"),
    BUGGY_LEASEARK_START(35, "童车租赁柜启动"),
    PASSPORT_PAY(36, "通行证购买支付"),
    CWCARD_PAY(37, "畅玩卡购买支付"),
    DOLL_START(38, "娃娃机充值"),
    IC_CARD(39, "IC卡充值"),
    GOLDEN_PACKAGE(40, "金主套餐"),
    TRAFFIC_CARD(41, "流量卡"),
    INTEGRAL_VENDING_START(42, "售货机积分启动"),
    IDLE_COINS_EXCHANGE(43, "闲时币充值"),
    GROUP_BUYING(44, "拼团购币"),
    VALUE_CARD_PAY(45, "超值卡支付"),
    MEMBER_CARD(46, "会员卡支付"),
    TRAFFIC_MODE_PAY(47, "贩卖模式"),
    VENDING_CHARGING_START(48, "售货机蓝牙充电"),
    VENDING_BLUETOOTH_START(49, "售货机蓝牙启动"),
    KNIGHT_CARD_PAY(50, "骑士卡购买支付"),
    CDKEY_PAY(51, "卡密购买支付"),
    FREE_CARD_PAY(52, "逍遥卡购买支付"),
    FREE_CARD_COMMODITY_PAY(53, "逍遥卡商品购买支付"),
    JDBT_ONEPOINTPURCHASE_PAY(54, "京东白条一分购支付"),
    VIDEO_PLAYBACK_PAY(55, "视频回放购买支付"),
    COINS_START(56, "兑币机启动"),
    LIQUID_START(57, "加液机启动"),
    FACTORY_TEST_PAY(58, "工厂测试支付"),
    MEMBERSHIP_CARD_PAY(59, "商家会员卡购买支付"),
    VENDING_RECHARGE(60, "售货机余额充值"),
    VENDING_EXCHANGE(61, "售货机充值"),
    GRAVITY_CABINET_START(62, "重力柜启动"),
    FREE_CARD_INTEGRAL_PAY(63, "逍遥卡积分商品购买支付"),
    ONE_CENT_ACTIVITY_PAY(64, "一分购活动支付"),
    FREE_CARD_COUPON_PAY(65, "逍遥金兑换券支付"),
    LIQUID_EXCHANGE(66, "加液机充值"),
    SYSTEM_CHARGE(67, "系统收费"),
    MQJ_EXCHANGE(68, "麻雀机充值"),
    MQJ_START(69, "麻雀机启动"),
    GDJ_EXCHANGE(70, "格斗机充值"),
    GDJ_START(71, "格斗机启动"),
    COLLABORATOR_RECHARGE_PAY(72, "协作者充值支付"),
    MEITUAN_COUPON_PAY(73, "美团合作活动支付"),
    CHARGING_PRE_RECHARGE(74, "充电桩预充值"),
    CAR_BALANCE_START(75, "汽车充电桩余额启动"),
    CAR_IC_START(76, "汽车充电桩IC启动"),
    CAR_EXCHANGE(77, "汽车充电桩充值"),
    GENERAL_PULSE_EXCHANGE(78, "通用脉冲类充值"),
    GENERAL_PULSE_START(79, "通用脉冲类启动"),
    RECEIVE_CODE_PAY(80, "收款码支付"),
    RECEIVE_CODE_RECHARGE(81, "收款码充值"),
    DYNAMIC_ARK_PAY(82, "动态柜支付"),
    NKJ_PAY(83, "扭卡机支付"),
    NKJ_RECHARGE(84, "扭卡机充值"),
    NEW_GUEST_CHARGE(85, "新客宝推荐充值"),
    DYNAMIC_GRAVITY_PAY(86, "智能重力柜支付"),
    INSURANCE_MONTH_CARD(87, "充电桩保险月卡"),
    DRAINAGE_RECHARGE(88, "智慧营销大额充值券"),
    IC_CARD_FACE_EXCHANGE(89, "IC卡刷脸充值"),
    ATMOSPHERE_CHARGE(90, "氛围机充值"),
    CAR_CHARGING(91, "汽车桩储值支付"),
    DRAINAGE_MINI_APP_PAY(92, "智慧营销小程序支付"),
    CAR_CHARGING_START(93, "汽车桩储值启动"),
    ;

    private Integer type;
    private String description;

    public static OrderBusinessType findDescription(Integer type) {
        return Arrays.stream(OrderBusinessType.values())
            .filter(businessTypeEnum -> businessTypeEnum.getType().equals(type))
            .findFirst().orElse(null);
    }
}
