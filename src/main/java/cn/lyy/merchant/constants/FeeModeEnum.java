package cn.lyy.merchant.constants;

import lombok.Getter;

import java.util.Arrays;

/**
 * 计费标准，用于B端设置计费规则
 * 相关表 equpment_type_function
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/11/20
 */
@Getter
public enum FeeModeEnum {

    //默认
    DEFAULT_TIME("TIME", "按时间计费", FeeModeEnum.UNIT_TIME, "如1元240分钟",FeeModeEnum.EQUIPMENT_TYPE_PY_DEFAULT),

    //充电桩
    CDZ_TIME("TIME", "按时间计费", FeeModeEnum.UNIT_TIME, "如1元240分钟", FeeModeEnum.EQUIPMENT_TYPE_PY_CDZ),
    //充电桩、电表
    CDZ_ELEC("ELEC", "按电量计费", FeeModeEnum.UNIT_ELEC, "如1元1度", FeeModeEnum.EQUIPMENT_TYPE_PY_CDZ),

    // 按模式计费
    MODE("MODE", "按模式计费", FeeModeEnum.UNIT_TIME, "", ""),
    // 按时长计费
    TIME("TIME", "按时间计费", FeeModeEnum.UNIT_TIME, "如1元240分钟", ""),
    // 按电量计费
    ELEC("ELEC", "按电量计费", FeeModeEnum.UNIT_ELEC, "如1元1度", ""),
    // 按次数计费
    NUMBER("NUM", "按次数计费", FeeModeEnum.UNIT_NUM, "如1元1次", ""),
    // 按次数计费
    MULTI("MULTI", "按组合计费", "", "", ""),
    // 按功率计费
    DYNAMIC("DYNAMIC", "按初始功率计费", FeeModeEnum.UNIT_TIME, "", ""),
    // 按次数计费
    GRADE_TIME("GRADE_TIME", "按电量计费", FeeModeEnum.UNIT_ELEC, "如1元1度", FeeModeEnum.EQUIPMENT_TYPE_PY_QCCDZ),
    // 汽车充电桩按组合套餐
    PERIOD("PERIOD","按电量计费", FeeModeEnum.UNIT_ELEC, "如1元1度", FeeModeEnum.EQUIPMENT_TYPE_PY_QCCDZ);
    ;

    // 计费标准 数据库关键词
    @Deprecated
    /**
     * @see ProtocolCodeEnum
     */
    public static final String DB_CODE_FEE_MODE = "FEE_MODE";

    // 设备设置模式-IOT, 计费模式，0：按时间计费，1：按电量计费
    public static final int IOT_EQUIPMENT_FEE_MODE_TIME = 0;
    public static final int IOT_EQUIPMENT_FEE_MODE_ELEC = 1;


    // 设备类型常量
    public static final String EQUIPMENT_TYPE_PY_DEFAULT = "DEFAULT"; // 默认
    public static final String EQUIPMENT_TYPE_PY_CDZ = "CDZ"; // 充电桩
    public static final String EQUIPMENT_TYPE_PY_QCCDZ = "QCCDZ"; // 汽车充电桩


    // 单位常量
    public static final String UNIT_ELEC = "度";
    public static final String UNIT_POWER = "瓦";
    public static final String UNIT_TIME = "分钟";
    public static final String UNIT_NUM = "次";


    private String code;
    private String name;
    private String unit;
    private String unitDesc;
    //设备类型
    private String type;


    FeeModeEnum(String code, String name, String unit, String unitDesc, String type) {
        this.code = code;
        this.name = name;
        this.unit = unit;
        this.unitDesc = unitDesc;
        this.type = type;
    }


    public static String checkFeeModeCode(String feeModeCode) {
        for (FeeModeEnum feeMode : FeeModeEnum.values()) {
            if (feeMode.getCode().equalsIgnoreCase(feeModeCode)) {
                return feeModeCode;
            }
        }
        return null;
    }

    public static String getUnit(String feeModeCode) {
        return Arrays.asList(FeeModeEnum.values()).stream().filter(fee -> fee.getCode().equalsIgnoreCase(feeModeCode)).map(fee -> fee.getUnit()).findFirst().orElse(FeeModeEnum.DEFAULT_TIME.getUnit());
    }

    public static String getUnitDesc(String feeModeCode) {
        return Arrays.asList(FeeModeEnum.values()).stream().filter(fee -> fee.getCode().equalsIgnoreCase(feeModeCode)).map(fee -> fee.getUnitDesc()).findFirst().orElse(FeeModeEnum.DEFAULT_TIME.getUnitDesc());
    }

    public static FeeModeEnum getFeeModeByType(String equipmentType) {
        for (FeeModeEnum feeMode : FeeModeEnum.values()) {
            if (feeMode.getType().equals(equipmentType)) {
                return feeMode;
            }
        }
        return FeeModeEnum.DEFAULT_TIME;
    }

    public static int getCDZIotFeeModeByCode(String code) {
        if (FeeModeEnum.CDZ_TIME.getCode().equalsIgnoreCase(code)) {
             return FeeModeEnum.IOT_EQUIPMENT_FEE_MODE_TIME;
        } else if (FeeModeEnum.CDZ_TIME.getCode().equalsIgnoreCase(code)) {
            return FeeModeEnum.IOT_EQUIPMENT_FEE_MODE_ELEC;
        }
        return 0;
    }

    public static int getCDZIotFeeModeByCode(Integer code) {
        if (1 == code) {
            return FeeModeEnum.IOT_EQUIPMENT_FEE_MODE_TIME;
        } else if (2 == code) {
            return FeeModeEnum.IOT_EQUIPMENT_FEE_MODE_ELEC;
        }
        return FeeModeEnum.IOT_EQUIPMENT_FEE_MODE_TIME;
    }


    public static FeeModeEnum getFeeModeByCode(String feeModeCode) {
        for (FeeModeEnum feeMode : FeeModeEnum.values()) {
            if (feeMode.getCode().equalsIgnoreCase(feeModeCode)) {
                return feeMode;
            }
        }
        return FeeModeEnum.DEFAULT_TIME;
    }

    public static String geeFeeModeDesc(String feeMode) {
        if(FeeModeEnum.CDZ_TIME.getCode().equalsIgnoreCase(feeMode)) {
            return "时间";
        }
        if(FeeModeEnum.CDZ_ELEC.getCode().equalsIgnoreCase(feeMode)) {
            return "电量";
        }
        if(FeeModeEnum.DYNAMIC.getCode().equalsIgnoreCase(feeMode)) {
            return "时间";
        }
        return "";
    }

    /**
     * 是否按时长的模式
     * @param feeMode
     * @return
     */
    public static boolean ifTimeMode(String feeMode) {
        if(FeeModeEnum.CDZ_TIME.getCode().equalsIgnoreCase(feeMode)) {
            return true;
        }
        if(FeeModeEnum.DYNAMIC.getCode().equalsIgnoreCase(feeMode)) {
            return true;
        }
        if(FeeModeEnum.CDZ_ELEC.getCode().equalsIgnoreCase(feeMode)) {
            return false;
        }
        return true;
    }
}
