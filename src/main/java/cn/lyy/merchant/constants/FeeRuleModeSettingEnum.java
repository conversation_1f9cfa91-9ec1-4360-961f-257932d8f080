package cn.lyy.merchant.constants;

import lombok.Getter;

import java.util.Arrays;

/**
 * 计费规则模式，与PC端的注册模板配置一致
 * <AUTHOR>
 * @version 1.0
 * @since 2019/11/28
 */
@Getter
public enum FeeRuleModeSettingEnum {
    //计费规则模式：时长(time)、时长加电量(time-elec)、次数(num)

    TIME("time", "时长"),
    TIME_ELEC("time-elec", "时长或电量"),
//    POWER("power", "功率"),
    MODE("mode", "模式"),
    NUMBER("num", "次数"),
    MULTI("multi", "组合"),
    ;

    String key;
    String name;

    FeeRuleModeSettingEnum(String key, String name) {
        this.key = key;
        this.name = name;
    }

    public static FeeRuleModeSettingEnum getByName(String name) {
        return Arrays.stream(FeeRuleModeSettingEnum.values()).filter(prop -> prop.name().equals(name)).findFirst().orElse(null);
    }

    public static FeeRuleModeSettingEnum getByKey(String key) {
        return Arrays.stream(FeeRuleModeSettingEnum.values()).filter(prop -> prop.getKey().equals(key)).findFirst().orElse(null);
    }
}
