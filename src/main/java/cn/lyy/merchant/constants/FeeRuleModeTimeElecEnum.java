package cn.lyy.merchant.constants;

import lombok.Getter;

import java.util.Arrays;

/**
 * 充电桩计费标准，用于B端设置计费规则
 * 相关表 equpment_type_function
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/11/20
 */
@Getter
public enum FeeRuleModeTimeElecEnum {


    //充电桩
    TIME("TIME", "按时间计费", "如1元240分钟"),
    ELEC("ELEC", "按电量计费", "如1元1度");


    private String code;
    private String name;
    private String unitDesc;
    //设备类型


    FeeRuleModeTimeElecEnum(String code, String name, String unitDesc) {
        this.code = code;
        this.name = name;
        this.unitDesc = unitDesc;
    }

    public static String findByCode(String code) {
        return Arrays.asList(values()).stream().filter(fee -> fee.getCode().equalsIgnoreCase(code)).map(fee -> fee.getCode()).findFirst().orElse(TIME.getCode());
    }

}
