package cn.lyy.merchant.constants;

/**
 * <p>Title:saas2</p>
 * <p>Desc: 系统角色枚举</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/9/18
 */
public enum SystemRoleEnum {

    ADMIN("ADMIN", "乐摇摇"),
    FACTORY("FACTORY", "工厂"),
    MERCHANT("MERCHANT", "商户"),

    BUSINESS("B", "B端"),
    CLIENT("C", "C端");

    private String value;

    private String name;

    SystemRoleEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}
