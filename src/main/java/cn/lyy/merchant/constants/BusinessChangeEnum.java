package cn.lyy.merchant.constants;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2020/7/17
 */
public enum BusinessChangeEnum {
    TYPE_ONLINE_IC_AMOUNT("在线IC卡余额变更" , 50),
    TYPE_ONLINE_IC_REFUND("在线IC卡订单退款" , 51),
    TYPE_C_DEOCCUPY("C端客户解除设备占用" , 52)
    ;


    public final static int USER_TYPE_SYSTEM = 0; //系统
    public final static int USER_TYPE_PARTNER = 1; //工厂/商家/代理
    public final static int USER_TYPE_CUSTOMER = 2; //顾客

    String name;
    int code;
    BusinessChangeEnum(String name , int code) {
        this.name = name;
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public int getCode() {
        return code;
    }
}
