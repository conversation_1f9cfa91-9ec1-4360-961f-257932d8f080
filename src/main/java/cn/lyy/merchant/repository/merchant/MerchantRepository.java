package cn.lyy.merchant.repository.merchant;

import cn.lyy.merchant.api.service.AdOrgAttachClient;
import cn.lyy.merchant.api.service.merchant.MerchantFeignClientBean;
import cn.lyy.merchant.dto.merchant.AdOrgAttachDTO;
import cn.lyy.merchant.dto.merchant.attach.dto.MerchantAttachUpdateDTO;
import cn.lyy.merchant.utils.ResponseCheckUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @since 2025/6/11 - 10:16
 */
@Repository
@RequiredArgsConstructor
public class MerchantRepository {

    private final MerchantFeignClientBean merchantClient;
    private final AdOrgAttachClient adOrgAttachClient;

    public Boolean updateMerchantAttach(Long merchantId,String primaryCategory) {
        MerchantAttachUpdateDTO updateDTO = new MerchantAttachUpdateDTO();
        updateDTO.setMerchantId(merchantId);
        updateDTO.setPrimaryCategory(primaryCategory);
        return updateMerchantAttach(updateDTO);
    }

    private Boolean updateMerchantAttach(MerchantAttachUpdateDTO updateDTO) {
        return ResponseCheckUtil.getData(merchantClient.updateMerchantAttach(updateDTO));
    }

    public AdOrgAttachDTO getMerchantAttach(Long merchantId) {
        return ResponseCheckUtil.getData(adOrgAttachClient.getByMerchantId(merchantId));
    }
}
