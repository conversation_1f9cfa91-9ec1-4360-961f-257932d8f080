package cn.lyy.merchant.repository.equipment;

import static java.util.Optional.ofNullable;

import cn.lyy.merchant.api.service.MerchantGroupService;
import cn.lyy.merchant.dto.merchant.response.MerchantGroupDTO;
import cn.lyy.merchant.utils.ResponseCheckUtil;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @since 2024/10/12 - 09:53
 */
@Repository
@RequiredArgsConstructor
public class StoreRepository {

    private final MerchantGroupService merchantGroupService;

    public Optional<MerchantGroupDTO> getStore(Long groupId) {
        return ofNullable(groupId)
                .map(merchantGroupService::getGroup)
                .map(ResponseCheckUtil::getData);
    }
}
