package cn.lyy.merchant.repository.member;

import cn.lyy.lyy_consumption_api.customer.LyyGrantCoinsDTO;
import cn.lyy.merchant.service.remote.ConsumptionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @since 2024/10/17 - 15:28
 */
@Repository
public class ConsumptionRepository {

    @Autowired
    private ConsumptionService consumptionService;

    public void saveGrantCoins(LyyGrantCoinsDTO param) {
        consumptionService.saveGrantCoins(param);
    }

}
