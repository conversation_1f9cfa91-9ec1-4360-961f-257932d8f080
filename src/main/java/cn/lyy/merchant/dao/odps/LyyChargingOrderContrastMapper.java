package cn.lyy.merchant.dao.odps;

import cn.lyy.merchant.dto.ordercount.LyyChargingOrderContrast;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface LyyChargingOrderContrastMapper {

    List<LyyChargingOrderContrast> getPage (@Param("limit") Integer limit ,
                                            @Param("offset") Integer offset ,
                                            @Param("merchantIds") List<Long> merchantIds,
                                            @Param("startDs") String startDs,
                                            @Param("endDs") String endDs);

}
