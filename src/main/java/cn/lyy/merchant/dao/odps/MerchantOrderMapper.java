package cn.lyy.merchant.dao.odps;

import cn.lyy.merchant.dto.MerchantPayOrderStatisticsDTO;
import cn.lyy.merchant.dto.OdpsOrderVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

@Mapper
@Repository
public interface MerchantOrderMapper {
    //queryOrder  MerchantPayOrderStatisticsDTO
    OdpsOrderVO queryOrder();


    OdpsOrderVO queryOrder2();

    /**
     *
     * @param merchantIdsInt
     * @param firstDay
     * @param lastDay
     * @return
     */
    List<MerchantPayOrderStatisticsDTO> getExportOrderList(@Param("ids") List<Integer> merchantIdsInt, @Param("firstDay") String firstDay,
        @Param("lastDay") String lastDay);

}
