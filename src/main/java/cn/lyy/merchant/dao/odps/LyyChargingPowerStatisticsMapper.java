package cn.lyy.merchant.dao.odps;

import cn.lyy.life.api.charging.dto.LyyChargingPowerStatisticsDTO;
import cn.lyy.merchant.dto.LyyChargingPowerStatisticsRequestDTO;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface LyyChargingPowerStatisticsMapper {

    List<LyyChargingPowerStatisticsDTO> getLyyChargingPowerStatisticsList(LyyChargingPowerStatisticsRequestDTO powerStatisticsRequestDTO);

    Integer getLyyChargingPowerStatisticsCount(LyyChargingPowerStatisticsRequestDTO powerStatisticsRequestDTO);
}
