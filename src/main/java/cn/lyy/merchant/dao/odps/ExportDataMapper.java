package cn.lyy.merchant.dao.odps;


import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.LinkedHashMap;
import java.util.List;

@Mapper
@Repository
public interface ExportDataMapper {

    @Select({"${sql}"})
    List<LinkedHashMap<String,Object>> execute_sql(@Param("sql") String sql);

    @Select({"${sql}"})
    Integer data_count_execute(@Param("sql") String sql);

}
