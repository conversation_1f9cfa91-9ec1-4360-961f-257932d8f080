package cn.lyy.merchant.assembler.consumer;

import static java.util.Optional.ofNullable;

import cn.lyy.merchant.dto.request.baichuan.BaichuanConsumerSolutionUpsertDTO;
import cn.lyy.merchant.dto.request.baichuan.ConsumerSolutionDetailVO;
import cn.lyy.merchant.dto.request.baichuan.ConsumerSolutionGroupVO;
import cn.lyy.merchant.dto.request.baichuan.ConsumerSolutionOverviewVO;
import cn.lyy.merchant.dto.request.baichuan.GroupEquipmentVO;
import cn.lyy.merchant.dto.request.baichuan.MeteringCommodity;
import cn.lyy.merchant.dto.request.baichuan.RechargeCommodity;
import cn.lyy.merchant.dto.request.baichuan.RefundSetting;
import cn.lyy.merchant.dto.request.baichuan.StartCommodity;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.gson.Gson;
import com.lyy.billing.interfaces.consumer.dto.EquipmentAssociatedGroupRespDTO;
import com.lyy.billing.interfaces.consumer.dto.EquipmentSimpleDTO;
import com.lyy.billing.interfaces.consumer.dto.baichuan.BaichuanConsumerSolutionOverviewVO;
import com.lyy.billing.interfaces.consumer.dto.baichuan.ConsumerSolutionCommodityFieldDTO;
import com.lyy.billing.interfaces.consumer.dto.baichuan.ConsumerSolutionCommodityFieldDTO.StageRule;
import com.lyy.billing.interfaces.consumer.dto.baichuan.ConsumerSolutionCommodityVO;
import com.lyy.billing.interfaces.consumer.dto.baichuan.ConsumerSolutionFieldDTO;
import com.lyy.billing.interfaces.consumer.dto.baichuan.ConsumerSolutionVO.OtherSetting;
import org.apache.commons.lang.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface ConsumerSolutionAssembler {

    ConsumerSolutionAssembler INSTANCE = Mappers.getMapper(ConsumerSolutionAssembler.class);

    @Mappings({
            @Mapping(target = "otherSetting", source = "otherSetting", qualifiedByName = "otherSettingToString"),
            @Mapping(target = "refundSetting", source = "refundSetting", qualifiedByName = "refundSettingToString"),
            @Mapping(target = "equipmentType", source = "equipmentTypeId")
    })
    ConsumerSolutionFieldDTO toConsumerSolutionFieldDTO(BaichuanConsumerSolutionUpsertDTO dto);

    @Named("otherSettingToString")
    default String otherSettingToString(OtherSetting otherSetting) {
        return ofNullable(otherSetting).map(JSON::toJSONString).orElse(null);
    }

    @Named("refundSettingToString")
    default String refundSettingToString(RefundSetting refundSetting) {
        return ofNullable(refundSetting).map(JSON::toJSONString).orElse(null);
    }

    @Mappings({
            @Mapping(target = "name", source = "commodityName")
    })
    ConsumerSolutionCommodityFieldDTO toConsumerSolutionCommodityFieldDTO(RechargeCommodity rechargeCommodity);

    @Mappings({
            @Mapping(target = "name", source = "commodityName")
    })
    ConsumerSolutionCommodityFieldDTO toConsumerSolutionCommodityFieldDTO(StartCommodity startCommodity);

    @Mappings({
            @Mapping(target = "stageRuleList", source = "stageRuleList"),
            @Mapping(target = "name", source = "commodityName")
    })
    ConsumerSolutionCommodityFieldDTO toConsumerSolutionCommodityFieldDTO(MeteringCommodity meteringCommodity);

    StageRule toStageRule(cn.lyy.merchant.dto.request.baichuan.StageRule stageRule);

    ConsumerSolutionOverviewVO toConsumerSolutionOverviewVO(BaichuanConsumerSolutionOverviewVO vo);

    Page<ConsumerSolutionOverviewVO> toPageConsumerSolutionOverviewVO(Page<BaichuanConsumerSolutionOverviewVO> page);

    @Mappings({
            @Mapping(target = "otherSetting", source = "otherSetting", qualifiedByName = "StringToOtherSetting"),
            @Mapping(target = "refundSetting", source = "refundSetting"),
            @Mapping(target = "startWay", source = "payWay")
    })
    ConsumerSolutionDetailVO toConsumerSolutionDetailVO(com.lyy.billing.interfaces.consumer.dto.baichuan.ConsumerSolutionDetailVO detail);

    RefundSetting toRefundSetting(com.lyy.billing.interfaces.consumer.dto.baichuan.ConsumerSolutionDetailVO.RefundSetting refundSetting);

    @Named("StringToOtherSetting")
    default OtherSetting stringToOtherSetting(String otherSetting) {
        return ofNullable(otherSetting).filter(StringUtils::isNotBlank).map(s -> new Gson().fromJson(s, OtherSetting.class)).orElse(null);
    }

    @Mappings({
            @Mapping(target = "commodityName", source = "name")
    })
    RechargeCommodity toRechargeCommodity(ConsumerSolutionCommodityVO commodity);

    @Mappings({
            @Mapping(target = "commodityName", source = "name")
    })
    StartCommodity toStartCommodity(ConsumerSolutionCommodityVO commodity);

    @Mappings({
            @Mapping(target = "stageRuleList", source = "stageRuleList"),
            @Mapping(target = "commodityName", source = "name")
    })
    MeteringCommodity toMeteringCommodity(ConsumerSolutionCommodityVO commodity);

    cn.lyy.merchant.dto.request.baichuan.StageRule toStageRule(StageRule stageRule);

    @Mappings({
            @Mapping(target = "storeId", source = "groupId"),
            @Mapping(target = "name", source = "groupName")
    })
    ConsumerSolutionGroupVO toConsumerSolutionGroupVO(EquipmentAssociatedGroupRespDTO dto);

    @Mappings({
            @Mapping(target = "value", source = "equipmentValue"),
            @Mapping(target = "isAssociation", source = "association")
    })
    GroupEquipmentVO toGroupEquipmentVO(EquipmentSimpleDTO dto);
}
