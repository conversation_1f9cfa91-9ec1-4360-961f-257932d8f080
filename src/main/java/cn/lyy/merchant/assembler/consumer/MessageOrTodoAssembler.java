package cn.lyy.merchant.assembler.consumer;

import cn.lyy.merchant.dto.positionmessage.MessageOrTodoDTO;
import cn.lyy.merchant.dto.positionmessage.PositionMessageDTO;
import cn.lyy.merchant.dto.positionmessage.TodoDTO;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @description mapstruct 实体映射
 * @create 2025/7/21 17:14
 */
@Mapper(componentModel = "spring")
public interface MessageOrTodoAssembler {

    // 转换 TodoDTO
    MessageOrTodoDTO convertTodoDTO(TodoDTO todoDTO);

    // 转换 PositionMessageDTO
    MessageOrTodoDTO convertPositionMessageDTO(PositionMessageDTO dto);

}