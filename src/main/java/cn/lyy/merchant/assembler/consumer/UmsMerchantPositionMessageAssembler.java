package cn.lyy.merchant.assembler.consumer;

/**
 * <AUTHOR>
 * @date 2024/11/21
 */

import cn.lyy.merchant.dto.positionmessage.PositionMessageDTO;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface UmsMerchantPositionMessageAssembler {

    UmsMerchantPositionMessageAssembler INSTANCE = Mappers.getMapper(UmsMerchantPositionMessageAssembler.class);

    List<PositionMessageDTO> toPositionMessageDTOList(
            List<cn.lyy.message.dto.positionmessage.PositionMessageDTO> umsMerchantPositionMessages);
}
