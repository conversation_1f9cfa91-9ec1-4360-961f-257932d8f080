package cn.lyy.merchant.service.switchcache;

import cn.lyy.lyy_switch_api.cache.SwitchCache;
import cn.lyy.lyy_switch_api.dto.SwitchMemoryDTO;
import cn.lyy.lyy_switch_api.enums.ResponseEnum;
import cn.lyy.lyy_switch_api.response.BaseResponse;
import cn.lyy.tools.util.GsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
public class SwitchMemoryService {

    @Autowired
    private SwitchService switchService;

    public boolean reload() {
        BaseResponse<ConcurrentHashMap<String, SwitchMemoryDTO>> data = switchService.loadCache();
        if (ResponseEnum.SUCCESS.getCode().equals(data.getCode())
            && data.getData() != null) {
            SwitchCache.reload(data.getData());
        } else {
            log.error("读取开关缓存异常", new Exception(String.format("读取开关缓存异常 => %s", GsonUtils.toJson(data))));
            return false;
        }
        return true;
    }
}
