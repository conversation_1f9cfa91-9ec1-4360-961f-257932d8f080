package cn.lyy.merchant.service.switchcache;

import cn.lyy.base.dto.Pagination;
import cn.lyy.lyy_switch_api.dto.*;
import cn.lyy.lyy_switch_api.response.BaseResponse;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

@Component
@Slf4j
public class SwitchFallbackFactory implements FallbackFactory<SwitchService> {


    @Override
    public SwitchService create(Throwable cause) {
        if(cause != null && cause.getMessage() != null
            && !"".equals(cause.getMessage())) {
            log.error(cause.getLocalizedMessage(), cause);
        }
        return new SwitchService() {
            @Override
            public BaseResponse<Pagination<SwitchDTO>> listSwitch(QueryParam param) {
                return BaseResponse.systemError();
            }

            @Override
            public BaseResponse saveSwitch(SaveParam param) {
                return BaseResponse.systemError();
            }

            @Override
            public BaseResponse updateSwitch(SaveParam param) {
                return BaseResponse.systemError();
            }

            /**
             * 根据key更新开关状态
             * @param param
             * @return
             */
            @Override
            public BaseResponse updateSwitchByKey(SaveParam param) {
                return BaseResponse.systemError();
            }

            @Override
            public BaseResponse listByGroup(Integer groupId) {
                return BaseResponse.systemError();
            }

            @Override
            public BaseResponse batchOperate(BatchOperateParam param) {
                return BaseResponse.systemError();
            }

            @Override
            public BaseResponse<ConcurrentHashMap<String, SwitchMemoryDTO>> loadCache() {
                return BaseResponse.systemError();
            }

            @Override
            public BaseResponse<List<GroupDTO>> listGroup(QueryParam param) {
                return BaseResponse.systemError();
            }

            @Override
            public BaseResponse saveGroup(SaveParam param) {
                return BaseResponse.systemError();
            }

            @Override
            public BaseResponse updateGroup(SaveParam param) {
                return BaseResponse.systemError();
            }

            @Override
            public BaseResponse deleteGroup(Integer groupId) {
                return BaseResponse.systemError();
            }
        };
    }
}

