package cn.lyy.merchant.service.switchcache;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SwitchCacheTask {
    @Autowired
    private SwitchMemoryService switchMemoryService;

    @Scheduled(cron = "${switch.cache.refresh.cron:0 */1 * * * ?}")
    public void refresh() {
        long start = System.currentTimeMillis();
        switchMemoryService.reload();
        log.info("[switch] 定时刷新开关缓存 => 耗时{}s", (System.currentTimeMillis() - start) / 1000);
    }

}
