package cn.lyy.merchant.service.productmatrix.impl;

import cn.lyy.base.dto.Pagination;
import cn.lyy.base.util.DateUtil;
import cn.lyy.base.util.StringUtil;
import cn.lyy.lyy_cmember_service_api.IProductMatrixService;
import cn.lyy.lyy_cmember_service_api.dto.productmatrix.ProductMatrixReportConfigDTO;
import cn.lyy.lyy_cmember_service_api.dto.productmatrix.ProductMatrixReportDTO;
import cn.lyy.merchant.api.service.MerchantGroupService;
import cn.lyy.merchant.dto.merchant.response.MerchantGroupDTO;
import cn.lyy.merchant.dto.productmatrix.*;
import cn.lyy.merchant.service.productmatrix.ProductMatrixService;
import com.alibaba.fastjson.JSONObject;
import com.lyy.user.infrastructure.dto.activityeffect.productmatrix.DaProductMatrixExamineDiDTO;
import com.lyy.user.infrastructure.dto.activityeffect.productmatrix.DaProductMatrixPackageDistributionDiDTO;
import com.lyy.user.infrastructure.req.ProductMatrixQueryRequest;
import com.lyy.user.interfaces.openfeign.ProductMatrixClientOpenFeign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static java.util.Optional.ofNullable;

/**
 * @description
 * <AUTHOR>
 * @date 2023/4/16 17:24
*/
@Service
@Slf4j
public class ProductMatrixServiceImpl implements ProductMatrixService {

    @Autowired
    private IProductMatrixService productMatrixService;

    @Autowired
    private MerchantGroupService merchantGroupService;

    @Autowired
    private ProductMatrixClientOpenFeign productMatrixClientOpenFeign;

    @Override
    public ReportConfigResult getReportConfig(Long adOrgId, Integer probationId) {
        // 获取报告
        ProductMatrixReportDTO dto = productMatrixService.getReportByProbationId(adOrgId.intValue(), probationId).getData();
//        ProductMatrixReportDTO dto = productMatrixService.getReportConfig(adOrgId.intValue()).getData();
        if (dto == null) {
            return null;
        }
        ReportConfigResult result = new ReportConfigResult();
        if (StringUtil.isNotEmpty(dto.getConfigDetail())) {
            result = JSONObject.parseObject(dto.getConfigDetail(), ReportConfigResult.class);
        }
        result.setStartTime(dto.getStartTime());
        result.setEndTime(dto.getEndTime());
        return result;
    }

    @Override
    public ExamineGroupResult getExamineGroup(Long adOrgId, Integer probationId) {
        // 获取报告
        ProductMatrixReportDTO dto = productMatrixService.getReportByProbationId(adOrgId.intValue(), probationId).getData();
        if (dto == null || StringUtil.isEmpty(dto.getConfigDetail())) {
            log.info("getExamineGroup，报告配置或配置内容为空，adOrgId：[{}]", adOrgId);
            return null;
        }
        if (CollectionUtils.isEmpty(dto.getGroupIds())) {
            log.info("getExamineGroup，测试场地为空，adOrgId：[{}]", adOrgId);
            return null;
        }
        ExamineGroupResult result = new ExamineGroupResult();
        result.setCount(dto.getGroupIds().size());
        List<Long> lyyEquipmentGroupIdList = dto.getGroupIds().stream().map(Long::valueOf).limit(3).collect(Collectors.toList());
        List<MerchantGroupDTO> groupDTOList = merchantGroupService.listGroupByIds(lyyEquipmentGroupIdList).getData();
        List<String> group = new ArrayList<>();
        ofNullable(groupDTOList).orElse(new ArrayList<>()).forEach(i -> {
            group.add(i.getName());
        });
        result.setGroup(group);
        return result;
    }

    @Override
    public ReportInfoResult getReportInfo(Long adOrgId, Integer probationId) {
        // 获取报告
        ProductMatrixReportDTO dto = productMatrixService.getReportByProbationId(adOrgId.intValue(), probationId).getData();
        if (dto == null || StringUtil.isEmpty(dto.getConfigDetail())) {
            log.info("getReportInfo，报告配置或配置内容为空，adOrgId：[{}]", adOrgId);
            return null;
        }
        if (CollectionUtils.isEmpty(dto.getGroupIds())) {
            log.info("getExamineGroup，测试场地为空，adOrgId：[{}]", adOrgId);
            return null;
        }
        // 获取数据
        ProductMatrixQueryRequest request = new ProductMatrixQueryRequest();
        request.setAdOrgId(adOrgId);
        request.setStartDate(DateUtil.format(dto.getStartTime(), "yyyy-MM-dd"));
        request.setEndDate(DateUtil.format(cn.lyy.base.util.DateUtil.addDateByDays(dto.getEndTime(), 1), "yyyy-MM-dd"));
        request.setLyyEquipmentGroupIdList(dto.getGroupIds());
        List<DaProductMatrixExamineDiDTO> list = productMatrixClientOpenFeign.getExamineDataAll(request).getBody();
        log.info("getExamineDataAll查询参数：[{}]，查询结果result：[{}]", request, list);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        ReportInfoResult result = new ReportInfoResult();
        list.forEach(i -> {
            if (i.getExamineType() != null) {
                // 有导购
                if (i.getExamineType() == 1) {
                    result.setExamineUserNum(i.getUserNum());
                    result.setExamineRevenue(i.getRevenue());
                    result.setExaminePayment(i.getPayment());
                    if (i.getRevenue() != null && i.getUserNum() != null && i.getUserNum() != 0) {
                        result.setPerPayUser(i.getRevenue().divide(BigDecimal.valueOf(i.getUserNum()), 1, BigDecimal.ROUND_HALF_UP));
                    }
                    result.setScanUser(i.getScanUser());
                    result.setOver10Amount(i.getOver10Amount());
                    result.setOver20Amount(i.getOver20Amount());
                    result.setOver50Amount(i.getOver50Amount());
                    result.setOver100Amount(i.getOver100Amount());
                    result.setRepurchaseUser(i.getRepurchaseUser());
                }
                // 无导购
                if (i.getExamineType() == 0) {
                    result.setNoExamineUserNum(i.getUserNum());
                    result.setNoExamineRevenue(i.getRevenue());
                    result.setNoExaminePayment(i.getPayment());
                    if (i.getRevenue() != null && i.getUserNum() != null && i.getUserNum() != 0) {
                        result.setNoPerPayUser(i.getRevenue().divide(BigDecimal.valueOf(i.getUserNum()), 1, BigDecimal.ROUND_HALF_UP));
                    }
                    result.setNoScanUser(i.getScanUser());
                    result.setNoOver10Amount(i.getOver10Amount());
                    result.setNoOver20Amount(i.getOver20Amount());
                    result.setNoOver50Amount(i.getOver50Amount());
                    result.setNoOver100Amount(i.getOver100Amount());
                    result.setNoRepurchaseUser(i.getRepurchaseUser());
                }
            }
        });
        // 配置
        ProductMatrixReportConfigDTO config = JSONObject.parseObject(dto.getConfigDetail(), ProductMatrixReportConfigDTO.class);
        log.info("getReportInfo，config：[{}]", config);
        ofNullable(config).map(ProductMatrixReportConfigDTO::getScanUser).filter(i -> i != null && !i).ifPresent(o -> {
            result.setScanUser(null);
            result.setNoScanUser(null);
        });
        /*ofNullable(config).map(ProductMatrixReportConfigDTO::getExaminePayment).filter(i -> i != null && !i).ifPresent(o -> {
            result.setExaminePayment(null);
            result.setNoExaminePayment(null);
        });*/
        ofNullable(config).map(ProductMatrixReportConfigDTO::getPerPayUser).filter(i -> i != null && !i).ifPresent(o -> {
            result.setPerPayUser(null);
            result.setNoPerPayUser(null);
        });
        ofNullable(config).map(ProductMatrixReportConfigDTO::getRepurchaseUser).filter(i -> i != null && !i).ifPresent(o -> {
            result.setRepurchaseUser(null);
            result.setNoRepurchaseUser(null);
        });
        ofNullable(config).map(ProductMatrixReportConfigDTO::getOver10Amount).filter(i -> i != null && !i).ifPresent(o -> {
            result.setOver10Amount(null);
            result.setNoOver10Amount(null);
        });
        ofNullable(config).map(ProductMatrixReportConfigDTO::getOver20Amount).filter(i -> i != null && !i).ifPresent(o -> {
            result.setOver20Amount(null);
            result.setNoOver20Amount(null);
        });
        ofNullable(config).map(ProductMatrixReportConfigDTO::getOver50Amount).filter(i -> i != null && !i).ifPresent(o -> {
            result.setOver50Amount(null);
            result.setNoOver50Amount(null);
        });
        ofNullable(config).map(ProductMatrixReportConfigDTO::getOver100Amount).filter(i -> i != null && !i).ifPresent(o -> {
            result.setOver100Amount(null);
            result.setNoOver100Amount(null);
        });
        return result;
    }

    @Override
    public PackageDistributionResult PackageDistributionResult(Long adOrgId, Integer probationId) {
        // 获取报告
        ProductMatrixReportDTO dto = productMatrixService.getReportByProbationId(adOrgId.intValue(), probationId).getData();
        if (dto == null || StringUtil.isEmpty(dto.getConfigDetail())) {
            log.info("getReportInfo，报告配置或配置内容为空，adOrgId：[{}]，dto：[{}]", adOrgId, dto);
            return null;
        }
        if (CollectionUtils.isEmpty(dto.getGroupIds())) {
            log.info("getExamineGroup，测试场地为空，adOrgId：[{}]", adOrgId);
            return null;
        }
        // 配置
        ProductMatrixReportConfigDTO config = JSONObject.parseObject(dto.getConfigDetail(), ProductMatrixReportConfigDTO.class);
        if (config != null && config.getPackageDistribution() != null && !config.getPackageDistribution()) {
            log.info("getReportInfo，导购类型订单统计不可见，adOrgId：[{}]，config：[{}]", adOrgId, config);
            return null;
        }
        // 获取数据
        ProductMatrixQueryRequest request = new ProductMatrixQueryRequest();
        request.setAdOrgId(adOrgId);
        request.setStartDate(DateUtil.format(dto.getStartTime(), "yyyy-MM-dd"));
        request.setEndDate(DateUtil.format(cn.lyy.base.util.DateUtil.addDateByDays(dto.getEndTime(), 1), "yyyy-MM-dd"));
        request.setPageIndex(1);
        request.setPageSize(3);
        request.setLyyEquipmentGroupIdList(dto.getGroupIds());
        Pagination<DaProductMatrixPackageDistributionDiDTO> pagination = productMatrixClientOpenFeign.getPackageDistribution(request).getBody();
        log.info("getPackageDistribution查询参数：[{}]，查询结果pagination：[{}]", request, pagination);
        if (pagination == null || CollectionUtils.isEmpty(pagination.getItems())) {
            return null;
        }
        PackageDistributionResult result = new PackageDistributionResult();
        List<PackageDistributionDTO> packageDistribution = new ArrayList<>();
        pagination.getItems().forEach(i ->{
            PackageDistributionDTO packageDistributionDTO = new PackageDistributionDTO();
            packageDistributionDTO.setFromAmount(i.getFromAmount());
            packageDistributionDTO.setToAmount(i.getToAmount());
            packageDistributionDTO.setTimes(i.getTimes());
            packageDistribution.add(packageDistributionDTO);
        });
        result.setPackageDistribution(packageDistribution);
        result.setCount(pagination.getItems().get(0).getTimesTotal());
        return result;
    }
}
