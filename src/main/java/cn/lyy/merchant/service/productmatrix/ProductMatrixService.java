package cn.lyy.merchant.service.productmatrix;

import cn.lyy.merchant.dto.productmatrix.ExamineGroupResult;
import cn.lyy.merchant.dto.productmatrix.PackageDistributionResult;
import cn.lyy.merchant.dto.productmatrix.ReportConfigResult;
import cn.lyy.merchant.dto.productmatrix.ReportInfoResult;

/**
 * @description 
 * <AUTHOR>
 * @date 2023/4/16 17:23
*/
public interface ProductMatrixService {

    ReportConfigResult getReportConfig(Long adOrgId, Integer probationId);

    ExamineGroupResult getExamineGroup(Long adOrgId, Integer probationId);

    ReportInfoResult getReportInfo(Long adOrgId, Integer probationId);

    PackageDistributionResult PackageDistributionResult(Long adOrgId, Integer probationId);
}
