package cn.lyy.merchant.service.legal;

import cn.lyy.base.dto.Pagination;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.merchant.dto.legal.MerchantAgreementQueryDTO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.legal.interfaces.agreement.dto.request.AgreementBatchSignReqDTO;
import com.lyy.legal.interfaces.agreement.dto.request.AgreementQueryReqDTO;
import com.lyy.legal.interfaces.agreement.dto.request.UserAgreementQueryReqDTO;
import com.lyy.legal.interfaces.agreement.dto.response.LegalAgreementRespDTO;
import com.lyy.legal.interfaces.agreement.dto.response.LegalUserAgreementRespDTO;
import com.lyy.legal.interfaces.agreement.feign.AgreementSignInfoClient;
import com.lyy.legal.interfaces.agreement.feign.LegalAgreementClient;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2021-08-24
 * @description
 */

@Slf4j
@Service
public class MerchantAgreementServiceImpl implements MerchantAgreementSignService {

    @Autowired
    private LegalAgreementClient legalAgreementClient;

    @Autowired
    private AgreementSignInfoClient agreementSignInfoClient;


    @Override
    public List<LegalUserAgreementRespDTO> getAgreementInfo(MerchantAgreementQueryDTO agreementQueryDTO) {
        AgreementQueryReqDTO dto = new AgreementQueryReqDTO();
        BeanUtils.copyProperties(agreementQueryDTO, dto);

        dto.setUserId(String.valueOf(agreementQueryDTO.getAdOrgId()));
        return ResponseUtils.checkResponse(legalAgreementClient.getUserAgreementInfo(dto));
    }


    @Override
    public Pagination<LegalUserAgreementRespDTO> getUserSignList(MerchantAgreementQueryDTO agreementQueryDTO) {
        UserAgreementQueryReqDTO dto = new UserAgreementQueryReqDTO();
        BeanUtils.copyProperties(agreementQueryDTO, dto);

        dto.setUserId(String.valueOf(agreementQueryDTO.getAdOrgId()));
        Page<LegalUserAgreementRespDTO> data = ResponseUtils.checkResponse(legalAgreementClient.getUserSignList(dto));

        Pagination pagination = new Pagination();
        pagination.setItems(data.getRecords());
        pagination.setTotal((int) data.getTotal());
        pagination.setPage((int) data.getCurrent());
        pagination.setPageSize((int) data.getSize());
        pagination.setMaxPage((int) data.getPages());
        return pagination;
    }

    @Override
    public LegalAgreementRespDTO agreementDetail(Long legalAgreementId, Long legalAgreementSignId, String userId) {
        return ResponseUtils.checkResponse(legalAgreementClient.agreementContent(legalAgreementId, userId, legalAgreementSignId));
    }


    @Override
    public Void batchSignAgreement(AgreementBatchSignReqDTO reqDTO) {
        return ResponseUtils.checkResponse(agreementSignInfoClient.batchSignAgreement(reqDTO));
    }

    /**
     * 批量获取协议详情
     */
    @Override
    public List<LegalAgreementRespDTO> getAgreementInfoList(List<Long> agreementIds) {
        return ResponseUtils.checkResponse(legalAgreementClient.getAgreementInfoList(agreementIds));
    }
}
