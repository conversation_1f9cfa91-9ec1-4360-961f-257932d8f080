package cn.lyy.merchant.service.legal;

import cn.lyy.base.dto.Pagination;
import cn.lyy.merchant.dto.legal.MerchantAgreementQueryDTO;
import com.lyy.legal.interfaces.agreement.dto.request.AgreementBatchSignReqDTO;
import com.lyy.legal.interfaces.agreement.dto.response.LegalAgreementRespDTO;
import com.lyy.legal.interfaces.agreement.dto.response.LegalUserAgreementRespDTO;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-08-24
 * @description
 */

public interface MerchantAgreementSignService {

    /**
     * 查询用户需要签署的协议
     * @param agreementQueryDTO 查询参数
     * @return
     */
    List<LegalUserAgreementRespDTO> getAgreementInfo(MerchantAgreementQueryDTO agreementQueryDTO);


    /**
     * 获取用户签署协议
     *
     * @param agreementQueryDTO
     * @return
     */
    Pagination<LegalUserAgreementRespDTO> getUserSignList(MerchantAgreementQueryDTO agreementQueryDTO);

    /**
     * 查看协议内容
     * @param legalAgreementId
     * @param legalAgreementSignId
     * @param userId
     */
    LegalAgreementRespDTO agreementDetail(Long legalAgreementId, Long legalAgreementSignId, String userId);

    /**
     * 签署协议
     * @param reqDTO
     */
    Void batchSignAgreement(AgreementBatchSignReqDTO reqDTO);

    /**
     * 批量获取协议详情
     * @param agreementIds
     * @return
     */
    List<LegalAgreementRespDTO> getAgreementInfoList(List<Long> agreementIds);
}
