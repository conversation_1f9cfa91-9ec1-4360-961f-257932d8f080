package cn.lyy.merchant.service.permission.impl;

import static java.util.Optional.ofNullable;

import cn.lyy.authority_service_api.AdResourcesDTO;
import cn.lyy.authority_service_api.miscroservice.AuthorityService;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.equipment.service.TrafficCardService;
import cn.lyy.merchant.dto.permission.AdPermissionDTO;
import cn.lyy.merchant.dto.permission.AdPermissionResultDTO;
import cn.lyy.merchant.service.permission.IPermissionService;
import cn.lyy.merchant.util.SAASPermissionUtil;
import cn.lyy.tools.constants.SystemConstants;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/08/28
 */
@Slf4j
@Service
public class PermissionServiceImpl implements IPermissionService {

    @Autowired
    private AuthorityService authorityService;

    @Autowired
    private TrafficCardService trafficCardService;

    @Value("cdz.wallet.menu.value:m_cdz_paramSetting")
    private String menuValue;

    @Override
    public AdPermissionResultDTO authorityResources(Long authorityUserId, Long adOrgId, Boolean isWalletSettlement) {
        AdPermissionResultDTO adPermissionResultDTO = new AdPermissionResultDTO();
        List<AdResourcesDTO> resources = authorityService.getMenusAndButtonByUserId(authorityUserId);
        if (CollectionUtils.isEmpty(resources)) {
            return adPermissionResultDTO;
        }
        // 获取SAAS标准版权限
        List<String> saasStandardResources = new ArrayList<>();
        if (SAASPermissionUtil.enabled()) {
            saasStandardResources.addAll(ofNullable(
                    authorityService.getResourceByRoleValueAndUserId(SystemConstants.SYSTEM_MERCHANT_ID,
                            SystemConstants.MERCHANT_SAAS_STANDARD, authorityUserId)).orElse(new ArrayList<>())
                    .parallelStream().map(AdResourcesDTO::getValue).collect(Collectors.toList()));
        }
        // 商家是否有流量卡收费
        BaseResponse<Boolean> response = trafficCardService.checkExistTrafficCard(adOrgId.intValue());
        Boolean checkExistTrafficCard = ofNullable(response).filter(r -> r.getCode() == ResponseCodeEnum.SUCCESS.getCode()).map(
                BaseResponse::getData).orElse(false);
        log.debug("流量卡checkExistTrafficCard:{}，adOrgId:{}", checkExistTrafficCard, adOrgId);
        // 转换权限对象
        List<AdPermissionDTO> adPermissionDTOList = resources.parallelStream().map(adResourcesDTO -> {
            if (BooleanUtils.isNotTrue(checkExistTrafficCard)
                    && "m_traffic_card_manage".equals(adResourcesDTO.getValue())) {
                // 剔除流量卡管理权限
                return null;
            }
            if ("m_equipment_fault_install_group".equals(adResourcesDTO.getValue())) {
                // 单独设置投放场地
                adPermissionResultDTO.setExistEquipmentFaultInstallGroup(true);
            }
            return getAdPermissionDTO(adResourcesDTO, saasStandardResources);
        }).filter(Objects::nonNull).collect(Collectors.toList());

        if(isWalletSettlement){
            List<String> permissioList = new ArrayList<>();
            permissioList.add("m_equipment_fault_remarks");
            permissioList.add("m_cdz_groupService");
            permissioList.add("m_cdz_paramSetting");
            permissioList.add("m_equipment_fault_disable");
            permissioList.addAll(Arrays.asList(menuValue.split(",")));
            adPermissionDTOList = adPermissionDTOList.stream().filter(value-> (StringUtils.isNotBlank(value.getValue())&&  value.getValue().contains("charging-merchant") )|| permissioList.contains(value.getValue())).collect(
                    Collectors.toList());
        }

        adPermissionResultDTO.setAccess(adPermissionDTOList);

        return adPermissionResultDTO;
    }

    private AdPermissionDTO getAdPermissionDTO(AdResourcesDTO adResourcesDTO, List<String> saasStandardResources) {
        AdPermissionDTO adPermissionDTO = new AdPermissionDTO();
        adPermissionDTO.setValue(adResourcesDTO.getValue());
        adPermissionDTO.setLock(false);
        if (SAASPermissionUtil.enabled() && CollectionUtils.isNotEmpty(saasStandardResources)) {
            adPermissionDTO.setLock(true);
            // 包含在SAAS标准版
            if (saasStandardResources.contains(adResourcesDTO.getValue())) {
                adPermissionDTO.setLock(false);
            }
            // 只锁入口权限
            if (SAASPermissionUtil.lockEntry() && "BUTTON".equals(adResourcesDTO.getType())) {
                adPermissionDTO.setLock(false);
            }
        }
        return adPermissionDTO;
    }
}
