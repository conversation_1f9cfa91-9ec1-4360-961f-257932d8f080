package cn.lyy.merchant.service.statistics;

import cn.lyy.merchant.dto.PageDTO;
import cn.lyy.merchant.dto.ProfitDTO;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;

import java.util.Map;

/**
 * 类描述：
 * <p>
 *
 * <AUTHOR>
 * @since 2020/12/8 11:41
 */
public interface ProfitService {

    /**
     * 获取汇总收益
     */
    ProfitDTO getCollectProfit(ProfitDTO.OffsetRequest request);

    /**
     * 获取指定type 的收益统计
     * @param request
     * @param pageDTO
     * @return
     */
    Object getProfit(ProfitDTO.OffsetRequest request, PageDTO pageDTO);

    /**
     * 首页经营统计数据获取
     * @param adUserInfoDTO
     * @return
     */
    Map<String, Object> getProfitOrderDetail(AdUserInfoDTO adUserInfoDTO);
}
