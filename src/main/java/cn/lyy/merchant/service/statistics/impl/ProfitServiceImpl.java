package cn.lyy.merchant.service.statistics.impl;

import cn.lyy.equipment.service.EquipmentService;
import cn.lyy.income.dto.bean.BaseResponse;
import cn.lyy.income.dto.income.*;
import cn.lyy.income.dto.param.MerchantIncomeRequest;
import cn.lyy.merchant.api.service.MerchantGroupService;
import cn.lyy.merchant.api.service.MerchantMemberAppealService;
import cn.lyy.merchant.api.service.MerchantWhiteClient;
import cn.lyy.merchant.api.service.RegisterTemplateClient;
import cn.lyy.merchant.constants.StatisticsDimensionConstants;
import cn.lyy.merchant.dto.*;
import cn.lyy.merchant.dto.common.UserInfoDTO;
import cn.lyy.merchant.dto.merchant.request.MerchantGroupRequest;
import cn.lyy.merchant.dto.merchant.response.MerchantGroupDTO;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.service.statistics.MerchantIncomeRemoteService;
import cn.lyy.merchant.service.statistics.ProfitService;
import cn.lyy.merchant.util.CommonUtil;
import cn.lyy.merchant.util.DateUtils;
import cn.lyy.tools.constants.LyyDistributorConstants;
import cn.lyy.tools.constants.LyyDistributorConstants.WhiteListType;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.Optional.ofNullable;

/**
 * 类描述：
 * <p>
 *
 * <AUTHOR>
 * @since 2020/12/8 11:41
 */
@Slf4j
@Service
public class ProfitServiceImpl implements ProfitService {

    private static final Integer MAX_PAGE_SIZE = 100;

    @Autowired
    private MerchantIncomeRemoteService merchantIncomeRemoteService;

    @Autowired
    private MerchantGroupService merchantGroupService;

    @Autowired
    private RegisterTemplateClient registerTemplateClient;

    @Autowired
    private EquipmentService equipmentService;

    @Autowired
    private MerchantMemberAppealService merchantMemberAppealService;

    @Autowired
    private MerchantWhiteClient merchantWhiteClient;

    @Override
    public ProfitDTO getCollectProfit(ProfitDTO.OffsetRequest request) {
        Long userOrgId = request.getUserOrgId();

        Map<String, LocalDate> dateMap = filterParam(request);
        LocalDate startDateStr = dateMap.get("start");
        LocalDate endDateStr = dateMap.get("end");

        DateQueryResult dateQueryResult = null;
        //如果offsetType不为空串时，就认为不是自定义
        if(StringUtils.isNotBlank(request.getOffsetType())) {
            dateQueryResult = queryCondition(request.getOffsetType(), request.getOffset());
            startDateStr = dateQueryResult.getBegDate();
            endDateStr = dateQueryResult.getEndDate();
        }


        BaseResponse<IncomeResponseDTO> incomeResponseDtoBaseResponse = requestCollectProfit(startDateStr, endDateStr,
                request.getUserId(), userOrgId,request.approver(), request.getGroupIds());

        ProfitDTO data;
        if (incomeResponseDtoBaseResponse != null && incomeResponseDtoBaseResponse.getData() != null) {
            data = genProfit(incomeResponseDtoBaseResponse.getData());

            if (Objects.isNull(data.getOfflineAmount())) {
                data.setOfflineAmount(BigDecimal.ZERO);
            }
            if (Objects.isNull(data.getOfflineCoins())) {
                data.setOfflineCoins(0);
            }
            if (Objects.isNull(data.getAmount())) {
                data.setAmount(BigDecimal.ZERO);
            }

            //2019-09-29 不算入线下币数
            data.setTotalIncome(data.getOfflineAmount().add(data.getAmount()).add(BigDecimal.valueOf(data.getOfflineCoins())));

            List<NameValueVO> amounts = new ArrayList<>();
            // 线上收益
            amounts.add(genAmountNameValueVO(StatisticsDimensionConstants.INCOME_ONLINE, data.getAmount().add(data.getAdAmount()),null));
            // 在线支付
            amounts.add(genAmountNameValueVO(StatisticsDimensionConstants.PAY_ONLINE, data.getAmount(),null));
            // 平台补贴
            amounts.add(genAmountNameValueVO(StatisticsDimensionConstants.PLATFORM_SUBSIDY, data.getAdAmount(),null));
            // 线下投币
            amounts.add(genAmountNameValueVO(StatisticsDimensionConstants.OFFLINE_COINS, data.getOfflineAmount().add(BigDecimal.valueOf(data.getOfflineCoins())),userOrgId));

            data.setAmounts(amounts);

        } else {
            data = defaultProfit();
        }
        data.setQueryOffset(dateQueryResult);
        return data;
    }

    private BaseResponse<IncomeResponseDTO> requestCollectProfit(LocalDate startDateStr, LocalDate endDateStr,
                                                                 Long userId, Long userOrgId, Boolean primaryAccount,
                                                                 List<Long> groupIds) {
        return merchantIncomeRemoteService.general(requestProfitParam(startDateStr, endDateStr, userId, userOrgId,
                primaryAccount, groupIds, null, 1, 100));
    }

    private MerchantIncomeRequest requestProfitParam(LocalDate startDateStr, LocalDate endDateStr,
                                                     Long userId, Long userOrgId, Boolean primaryAccount,
                                                     List<Long> groupIds, Long equipmentTypeId, Integer pageIndex, Integer pageSize) {
        MerchantIncomeRequest incomeRequest = new MerchantIncomeRequest();
        incomeRequest.setStart(startDateStr);
        incomeRequest.setEnd(endDateStr);
        incomeRequest.setDistributor(userOrgId.intValue());
        incomeRequest.setPageIndex(pageIndex);
        incomeRequest.setPageSize(pageSize);
        incomeRequest.setEquipmentTypes(equipmentTypeId == null ? null : Arrays.asList(equipmentTypeId.intValue()));
        incomeRequest.setGroups(CollectionUtils.isEmpty(groupIds) ? null : groupIds.stream().map(m -> m.intValue()).collect(Collectors.toList()));

        if(CollectionUtils.isEmpty(groupIds)) {

            //判断用户是否子账号，是就只传有权限的场地
            if (!primaryAccount) {
                MerchantGroupRequest request = MerchantGroupRequest.builder()
                        .adUser(userId)
                        .distributor(userOrgId)
                        .showGroupLabel(false)
//                        .isActive(1)
                        .build();
                List<MerchantGroupDTO> groups = ofNullable(merchantGroupService.selectGroup(request).getData()).orElse(null);
                List<Integer> groupIdInts = groups != null ? groups.stream()
                        .map(item -> item.getEquipmentGroupId().intValue())
                        .collect(Collectors.toList()) : null;

                if (groupIdInts != null) {
                    incomeRequest.setGroups(groupIdInts);
                }
                log.debug("userId[{}] , userOrgId[{}] , 只能查询场地范围: {}", userId, userOrgId, incomeRequest.getGroups());
            }
        }
        return incomeRequest;
    }

    /**
     * 默认收益数据，用于当收益数据为null时填充给前端
     * @return
     */
    private ProfitDTO defaultProfit() {
        BigDecimal defaultAmount = BigDecimal.ZERO;
        ProfitDTO profitDTO = new ProfitDTO();
        profitDTO.setAmount(defaultAmount);
        profitDTO.setTotalIncome(defaultAmount);
        profitDTO.setTotalIncome(defaultAmount);
        profitDTO.setOfflineAmount(defaultAmount);
        //支付笔数
        profitDTO.setOnlineCount(0);
        List<NameValueVO> amounts = new ArrayList<>();
        // 线上收益
        amounts.add(genAmountNameValueVO(StatisticsDimensionConstants.INCOME_ONLINE, defaultAmount,null));
        // 平台补贴
        amounts.add(genAmountNameValueVO(StatisticsDimensionConstants.PLATFORM_SUBSIDY, defaultAmount,null));
        // 线下投币
        amounts.add(genAmountNameValueVO(StatisticsDimensionConstants.OFFLINE_COINS, defaultAmount,null));

        profitDTO.setAmounts(amounts);
        return profitDTO;
    }

    private NameValueVO genAmountNameValueVO(int statisticsValue, BigDecimal amount, Long distributorId) {
        return (new NameValueVO(StatisticsDimensionConstants.DIMENSION_TEXT[statisticsValue], Objects.isNull(amount) ? "0" : profitAmountFilter(distributorId,amount)));
    }


    private ProfitDTO genProfit(IncomeResponseDTO param) {
        if (param == null) {
            return null;
        }
        ProfitDTO profitDTO = new ProfitDTO();
        BeanUtils.copyProperties(param, profitDTO);
        return profitDTO;
    }

    private Map<String, LocalDate> filterParam(ProfitDTO.OffsetRequest request) {
        String dateRange = request.getDateRange();
        LocalDate startDate = request.getStartDate();
        LocalDate endDate = request.getEndDate();
        if(StringUtils.isNotBlank(dateRange)) {
            LocalDate nowDate = DateUtils.nowDate();
            switch (dateRange) {
                case "TODAY":
                    startDate = nowDate;
                    endDate = DateUtils.addDay(nowDate, 1);
                    break;
                case "YESTARDAY":
                    startDate = DateUtils.addDay(nowDate, -1);
                    endDate = nowDate;
                    break;
                case "SEVENDAY":
                    startDate = DateUtils.addDay(nowDate, -7);
                    endDate = nowDate;
                    break;
                default:
            }
        }
        Map<String, LocalDate> dateStr = Maps.newHashMap();
        dateStr.put("start", startDate);
        dateStr.put("end", endDate);
        return dateStr;
    }

    /**
     * 请求场地设备经营数据统计
     */
    private BaseResponse<PageInfo<IncomeGroupResponseDTO>> requestGroupProfit(LocalDate startDateStr, LocalDate endDateStr, Long userId, Long userOrgId,Boolean primaryAccount, Integer pageIndex, Integer pageSize, Long groupId) {
        return merchantIncomeRemoteService.generalGroup(requestProfitParam(startDateStr, endDateStr, userId, userOrgId,primaryAccount, Objects.isNull(groupId) ? null : Lists.newArrayList(groupId), null, pageIndex, pageSize));
    }

    @Override
    public Object getProfit(ProfitDTO.OffsetRequest request, PageDTO pageDTO) {
        String type = request.getType();
        if (Strings.isNullOrEmpty(type) && request.getQueryType().equalsIgnoreCase("group")) {
            throw new BusinessException("统计类型不能为空！");
        }
        Long userOrgId = request.getUserOrgId();
        Long userId = request.getUserId();
        Long groupId = request.getGroupId();

        Map<String, LocalDate> dateMap = filterParam(request);
        LocalDate startDateStr = dateMap.get("start");
        LocalDate endDateStr = dateMap.get("end");

        //如果offsetType不为空串时，就认为不是自定义
        if(StringUtils.isNotBlank(request.getOffsetType())) {
            DateUtils.DateQueryResult dateQueryResult = DateUtils.queryCondition(request.getOffsetType(), request.getOffset());
            startDateStr = dateQueryResult.getBegDate();
            endDateStr = dateQueryResult.getEndDate();
        }

        if (request.getQueryType() == null) {
            request.setQueryType("group");
        }

        Object obj = null;
        switch (request.getQueryType()) {
            case "group":
                switch (type) {
                    // 获取场地的收益统计
                    case "GROUP":
                        obj = getGroupProfit(userOrgId, userId, request.approver(), startDateStr, endDateStr, request.getGroupIds());
                        break;
                    // 获取场地下不同设备类型收益统计
                    case "EQUIPMENT_TYPE":
                        obj = getEquipmentTypeProfit(userId, userOrgId, request.approver(), groupId, startDateStr, endDateStr);
                        break;
                    // 获取场地下某个设备类型下的设备收益统计
                    case "EQUIPMENT":
                        obj = getEquipmentProfit(userId, userOrgId, request.approver(), groupId, request.getEquipmentTypeId(), request.getEquipmentIds(), startDateStr, endDateStr, pageDTO);
                        break;
                    default:
                        obj = null;
                }
                break;
            case "time":
                obj = getProfitByDate(userId, userOrgId, request.approver(), startDateStr, endDateStr);
                break;
            case "equipment":
                obj = getProfitByEquipment(userId, userOrgId, request.approver(), startDateStr, endDateStr);
                break;
            default:
        }

        return obj;
    }

    /**
     * 首页经营统计数据获取
     * @param adUserInfoDTO
     * @return
     */
    @Override
    public Map<String, Object> getProfitOrderDetail(AdUserInfoDTO adUserInfoDTO) {
        Map<String, Object> result = Maps.newHashMap();
        // 获取今日收益数据
        LocalDate nowDate = DateUtils.nowDate();
        //获取今日收益
        MerchantIncomeRequest incomeRequest = requestProfitParam(nowDate, nowDate, adUserInfoDTO.getAdUserId(), adUserInfoDTO.getAdOrgId(),
                adUserInfoDTO.getIsApprover(), null, null, 1, 5);
        IncomeToDayResponseDTO incomeToDayResponseDTO = merchantIncomeRemoteService.generalToDay(incomeRequest).getData();
        //数据转换
        ProfitDTO todayProfit = getDayProfit(incomeToDayResponseDTO);
        result.put("todayIncome", todayProfit);
        //获取昨日收益
        LocalDate yesterdayDate = DateUtils.addDay(nowDate, -1);
        incomeRequest.setStart(yesterdayDate);
        incomeRequest.setEnd(yesterdayDate);
        IncomeToDayResponseDTO incomeYesterdayResponseDTO = merchantIncomeRemoteService.generalToDay(incomeRequest).getData();
        ProfitDTO yesterdayProfit = getDayProfit(incomeYesterdayResponseDTO);
        result.put("yesterdayIncome", yesterdayProfit);
        //获取客诉信息
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setIsApprover(adUserInfoDTO.getIsApprover());
        userInfoDTO.setUserOrgId(adUserInfoDTO.getAdOrgId());
        userInfoDTO.setUserId(adUserInfoDTO.getAdUserId());
        Integer appealCount = null;
        try {
            appealCount = merchantMemberAppealService.appealCount(userInfoDTO).getData();
        } catch (Exception e) {
            log.warn("获取未处理申诉记录失败");
            appealCount = 0;
        }
        result.put("appealCount", appealCount == null ? 0 : appealCount);
        return result;
    }

    private List<JSONObject> getProfitByEquipment(Long userId, Long userOrgId, Boolean primaryAccount, LocalDate startDateStr, LocalDate endDateStr) {
        Map<Integer, JSONObject> groupData = new HashMap<>();
        boolean isIgnoreCoinDistributorId = checkIgnoreDistributorId(userOrgId);

        int pageCount = 1;  //默认查第一页
        for (int i = 1; i <= pageCount; i++) {
            BaseResponse<PageInfo<IncomeEquipmentResponseDTO>> incomeResponse = requestEquipmentProfit(startDateStr, endDateStr, userId, userOrgId, primaryAccount, null , null ,i, MAX_PAGE_SIZE);
            if (incomeResponse.getData() != null && incomeResponse.getData().getPages() > 0) {
                pageCount = incomeResponse.getData().getPages();
            }

            if (Objects.nonNull(incomeResponse) && incomeResponse.getCode() == 0 && Objects.nonNull(incomeResponse.getData())
                    && CollectionUtils.isNotEmpty(incomeResponse.getData().getList()) && incomeResponse.getData().getList().size() > 0) {

                incomeResponse.getData().getList().forEach(item -> {
                    JSONArray equipmentJary = null;
                    JSONObject jobj = null;
                    if (groupData.containsKey(item.getGroupId())){
                        jobj = groupData.get(item.getGroupId());
                    }
                    else{
                        jobj = new JSONObject();
                        groupData.put(item.getGroupId() , jobj);
                    }

                    jobj.put("groupId", item.getGroupId());
                    jobj.put("groupName",
                            ofNullable(item.getGroupName()).orElseGet(() -> {
                                StringBuilder sb = new StringBuilder();
                                //返回场地信息
                                ofNullable(merchantGroupService.getGroup(item.getGroupId()))
                                        .map(cn.lyy.base.communal.bean.BaseResponse::getData)
                                        .ifPresent(group -> {
                                            sb.append(group.getName());
                                        });
                                return sb.toString();
                            })
                    );


                    jobj.put("equipmentCount", jobj.containsKey("equipmentCount") ? jobj.getIntValue("equipmentCount") + 1 : 1);

                    if (jobj.containsKey("equipments")) {
                        equipmentJary = jobj.getJSONArray("equipments");
                    } else {
                        equipmentJary = new JSONArray();
                        jobj.put("equipments" , equipmentJary);
                    }

                    BigDecimal offlineTotal = getOfflineTotal(isIgnoreCoinDistributorId,item);

                    JSONObject equipmentItem = new JSONObject();
                    equipmentItem.put("name", item.getEquipmentType()+"_"+item.getEquipmentValue());
                    equipmentItem.put("totalAmount", item.getAmount().add(offlineTotal).floatValue());
                    equipmentItem.put("onlineAmount", CommonUtil.formatCurrency(item.getAmount().floatValue(), CommonUtil.CURRENCY_FORMAT));
                    equipmentItem.put("offlineAmount", CommonUtil.formatCurrency(offlineTotal.floatValue(), CommonUtil.CURRENCY_FORMAT));
                    equipmentItem.put("unbind", item.getUnbind());
                    equipmentItem.put("transfer", item.getTransfer());
                    equipmentItem.put("equipmentId" ,item.getEquipmentId());


                    //返回设备备注
                    ofNullable(equipmentService.getEquipmentInfoById(item.getEquipmentId().longValue()))
                            .map(cn.lyy.base.communal.bean.BaseResponse::getData)
                            .ifPresent(equipment -> {
                                equipmentItem.put("remark", equipment.getRemarks() == null ? StringUtils.EMPTY : equipment.getRemarks());
                            });
                    equipmentJary.add(equipmentItem);
                    jobj.put("equipments", equipmentJary);

                });
            }
        }

        //======= 以设备数量多的优先
        List<JSONObject> groupList = new ArrayList<>();
        groupList.addAll(groupData.values());
        //排序
        Collections.sort(groupList,
                (o1 ,o2) ->
                        o2.getInteger("equipmentCount").compareTo(o1.getInteger("equipmentCount"))
        );

        //====== 以设备金额大的排序 ========//
        groupList.forEach(group -> {
            JSONArray equipments = group.getJSONArray("equipments");
            if(equipments.size() > 1){
                equipments.sort((o1, o2) -> {
                    JSONObject c1 = (JSONObject)o1;
                    JSONObject c2 = (JSONObject)o2;
                    return c2.getFloat("totalAmount").compareTo(c1.getFloat("totalAmount"));
                });
            }
        });


        return groupList;
    }

    private JSONArray getProfitByDate(Long userId, Long userOrgId, Boolean primaryAccount, LocalDate startDateStr, LocalDate endDateStr) {
        JSONArray resultJson = new JSONArray();
        LocalDate compareDate = startDateStr.plusDays(0);

        List<Integer> topListIndex = new ArrayList<>();
        int jsonIndex = 0;
        BigDecimal topAmount = BigDecimal.ZERO;

        boolean isIgnoreCoinDistributorId = checkIgnoreDistributorId(userOrgId);//是否忽略投币收益的客户
        while (compareDate.compareTo(endDateStr) <= 0) {
            BaseResponse<IncomeResponseDTO> incomeResponseDTOBaseResponse = requestCollectProfit(compareDate, compareDate, userId, userOrgId, primaryAccount,null);
            String dateStr = DateUtils.dateFormat(compareDate , DateUtils.DatePattern.yyyy_MM_dd);
            JSONObject jobj = new JSONObject();
            jobj.put("date", dateStr);
            if (Objects.nonNull(incomeResponseDTOBaseResponse) && incomeResponseDTOBaseResponse.getCode() == 0 && Objects.nonNull(incomeResponseDTOBaseResponse.getData())) {
                IncomeResponseDTO income = incomeResponseDTOBaseResponse.getData();

                BigDecimal offineTotal = getOfflineTotal(isIgnoreCoinDistributorId,income);

                jobj.put("onlineAmount", CommonUtil.formatCurrency(income.getAmount().floatValue(), CommonUtil.CURRENCY_FORMAT));
                jobj.put("offlineAmount", CommonUtil.formatCurrency(offineTotal.floatValue(), CommonUtil.CURRENCY_FORMAT));
                BigDecimal totalAmount = income.getAmount().add(offineTotal);
                if (totalAmount.compareTo(topAmount) > 0) {
                    topListIndex.clear();
                    topListIndex.add(jsonIndex);
                    topAmount = totalAmount;
                } else if (totalAmount.compareTo(topAmount) == 0 && topAmount.compareTo(BigDecimal.ZERO) > 0) {
                    topListIndex.add(jsonIndex);
                    topAmount = totalAmount;
                }


            }
            else{
                jobj.put("onlineAmount", "0");
                jobj.put("offlineAmount", "0");
            }
            jobj.put("top" ,false);



            resultJson.add(jobj);
            jsonIndex++;

            compareDate = compareDate.plusDays(1);
        }

        if (topListIndex.size() > 0) {
            topListIndex.forEach(index -> {
                resultJson.getJSONObject(index).put("top", true);
            });
        }

        Collections.reverse(resultJson);
        return resultJson;
    }

    private JSONObject getEquipmentProfit(Long userId, Long userOrgId, Boolean primaryAccount, Long groupId, Long equipmentTypeId, List<Integer> equipmentIds, LocalDate startDateStr, LocalDate endDateStr, PageDTO pageDTO) {
        List<ProfitEquipmentDTO> profitEquipments = Lists.newArrayList();
        Integer pageIndex = pageDTO.getPageIndex();
        Integer pageSize = pageDTO.getPageSize();


        BaseResponse<PageInfo<IncomeEquipmentResponseDTO>> pageInfoBaseResponse;
        PageInfo pageInfo = null;
        try {
            pageInfoBaseResponse = requestEquipmentProfit(startDateStr, endDateStr, userId, userOrgId,primaryAccount, groupId, equipmentTypeId, pageIndex, pageSize);
            List<IncomeEquipmentResponseDTO> list;
            if (Objects.nonNull(pageInfoBaseResponse) && pageInfoBaseResponse.getCode() == 0 && Objects.nonNull(pageInfoBaseResponse.getData())
                    && CollectionUtils.isNotEmpty(list = pageInfoBaseResponse.getData().getList())) {
                list.stream().forEach(l -> {
                    ProfitEquipmentDTO profitEquipmentDTO = new ProfitEquipmentDTO();
                    BeanUtils.copyProperties(l, profitEquipmentDTO);

                    BigDecimal offineTotal = offlineTotal(profitEquipmentDTO.getOfflineAmount(), new BigDecimal(profitEquipmentDTO.getOfflineCoins()));

                    List<NameValueVO> amounts = new ArrayList<>();

                    amounts.add(genAmountNameValueVO(StatisticsDimensionConstants.INCOME_ONLINE, profitEquipmentDTO.getAmount(),null));
                    amounts.add(genAmountNameValueVO(StatisticsDimensionConstants.OFFLINE_COINS, offineTotal,userOrgId));
                    amounts = amounts.stream().filter(item -> {
                        return item != null;
                    }).collect(Collectors.toList());
                    profitEquipmentDTO.setAmounts(amounts);

                    //返回设备备注
                    ofNullable(equipmentService.getEquipmentInfoById(profitEquipmentDTO.getEquipmentId().longValue()))
                            .map(cn.lyy.base.communal.bean.BaseResponse::getData)
                            .ifPresent(item -> {
                                profitEquipmentDTO.setEquipmentRemark(item.getRemarks());
                    });
                    profitEquipments.add(profitEquipmentDTO);
                });
                pageInfo = pageInfoBaseResponse.getData();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        List<ProfitEquipmentDTO> newprofitEquipments = profitEquipments;
        //线上收益倒序
        if (CollectionUtils.isNotEmpty(newprofitEquipments)) {
            newprofitEquipments = newprofitEquipments.stream().sorted(Comparator.comparing(ProfitEquipmentDTO::getAmount).reversed()).collect(Collectors.toList());
        }

        JSONObject listJsonObj;
        if (Objects.nonNull(pageInfo)) {
            ListVO listVO = new ListVO(Long.valueOf(pageInfo.getPageNum()), Long.valueOf(pageInfo.getSize()), Long.valueOf(pageInfo.getTotal()), null);
            listJsonObj = (JSONObject) JSONObject.toJSON(listVO);
        } else {
            listJsonObj = new JSONObject();
        }
        listJsonObj.put("data", newprofitEquipments);
        //返回场地信息
        ofNullable(merchantGroupService.getGroup(groupId))
                .map(cn.lyy.base.communal.bean.BaseResponse::getData)
                .ifPresent(item -> {
                    listJsonObj.put("groupName", item.getName());
                    listJsonObj.put("groupAdress", item.getAddress());
        });

        return listJsonObj;
    }

    private BaseResponse<PageInfo<IncomeEquipmentResponseDTO>> requestEquipmentProfit(LocalDate startDateStr, LocalDate endDateStr, Long userId, Long userOrgId, Boolean primaryAccount, Long groupId, Long equipmentTypeId, Integer pageIndex, Integer pageSize) {
        return merchantIncomeRemoteService.generalEquipment(requestProfitParam(startDateStr, endDateStr, userId, userOrgId, primaryAccount, Objects.isNull(groupId) ? null : Lists.newArrayList(groupId), equipmentTypeId, pageIndex, pageSize));
    }

    private List<ProfitEquipmentTypeDTO> getEquipmentTypeProfit(Long userId, Long userOrgId, Boolean primaryAccount, Long groupId, LocalDate startDateStr, LocalDate endDateStr) {
        List<ProfitEquipmentTypeDTO> profitEquipmentTypes = Lists.newArrayList();

        /**
         * 这里需要改成即使解绑了也要看到设备类型、设备的收益记录
         * 先通过场地查出所有设备的记录，然后查出对应的设备类型进行分组
         */
        int pageCount = 1;  //默认查第一页
        for (int i = 1; i <= pageCount; i++) {
            BaseResponse<PageInfo<IncomeEquipmentTypeResponseDTO>> incomeResponse = requestEquipmentTypeProfit(startDateStr, endDateStr, userId, userOrgId, primaryAccount, groupId, null, i, MAX_PAGE_SIZE);
            if (incomeResponse.getData() != null && incomeResponse.getData().getPages() > 0) {
                pageCount = incomeResponse.getData().getPages();
            }

            if (Objects.nonNull(incomeResponse) && incomeResponse.getCode() == 0 && Objects.nonNull(incomeResponse.getData())
                    && CollectionUtils.isNotEmpty(incomeResponse.getData().getList()) && incomeResponse.getData().getList().size() > 0) {

                incomeResponse.getData().getList().forEach(income -> {
                    Long typeId = Long.valueOf(income.getEquipmentTypeId());
                    ProfitEquipmentTypeDTO profitEquipmentTypeDTO = new ProfitEquipmentTypeDTO();
                    //废除，设置空列表，防止前端报错
                    profitEquipmentTypeDTO.setEquipmentIds(Collections.EMPTY_LIST);
                    profitEquipmentTypeDTO.setEquipmentTypeId(typeId);
                    profitEquipmentTypeDTO.setEquipmentType(income.getEquipmentType());

                    //设置设备图标
                    ofNullable(registerTemplateClient.queryByEquipmentTypeId(income.getEquipmentTypeId().longValue()))
                            .map(cn.lyy.base.communal.bean.BaseResponse::getData)
                            .ifPresent(item -> {
                                profitEquipmentTypeDTO.setEquipmentTypeIcon(item.getIconSmallUrl());
                            });
                    BigDecimal amount = BigDecimal.ZERO, offlineTotal = BigDecimal.ZERO;

                    if (income.getEquipmentCount() > 0) {
                        amount = checkAmount(amount, income.getAmount());
                        offlineTotal = offlineTotal(income.getOfflineAmount(), BigDecimal.valueOf(income.getOfflineCoins()));
                    }

                    if (offlineTotal.compareTo(BigDecimal.ZERO) > 0 || amount.compareTo(BigDecimal.ZERO) > 0) {

                        List<NameValueVO> amounts = new ArrayList<>();
                        amounts.add(genAmountNameValueVO(StatisticsDimensionConstants.INCOME_ONLINE, amount, null));
                        amounts.add(genAmountNameValueVO(StatisticsDimensionConstants.OFFLINE_COINS, offlineTotal,userOrgId));
                        amounts = amounts.stream().filter(item -> item != null).collect(Collectors.toList());
                        profitEquipmentTypeDTO.setAmounts(amounts);

                        profitEquipmentTypes.add(profitEquipmentTypeDTO);
                    }
                });

            }
        }
        return profitEquipmentTypes;
    }

    private BaseResponse<PageInfo<IncomeEquipmentTypeResponseDTO>> requestEquipmentTypeProfit(LocalDate startDateStr, LocalDate endDateStr, Long userId, Long userOrgId, Boolean primaryAccount, Long groupId, Long equipmentTypeId, int pageIndex, Integer pageSize) {
        return merchantIncomeRemoteService.generalEquipmentType(requestProfitParam(startDateStr, endDateStr, userId, userOrgId, primaryAccount, Objects.isNull(groupId) ? null : Lists.newArrayList(groupId), equipmentTypeId, pageIndex, pageSize));
    }

    public List<ProfitGroupDTO> getGroupProfit(Long userOrgId, Long userId, Boolean primaryAccount, LocalDate startDateStr, LocalDate endDateStr, List<Long> groupIds) {
        List<ProfitGroupDTO> profitGroups = Lists.newArrayList();

        int pageCount = 1;  //默认查第一页
        for (int i = 1; i <= pageCount; i++) {
            List<BaseResponse<PageInfo<IncomeGroupResponseDTO>>> respList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(groupIds)) {
                for (Long groupId : groupIds) {
                    BaseResponse<PageInfo<IncomeGroupResponseDTO>> groupResponse = requestGroupProfit(startDateStr, endDateStr, userId, userOrgId, primaryAccount, i, MAX_PAGE_SIZE, groupId);
                    respList.add(groupResponse);
                }
            } else {
                BaseResponse<PageInfo<IncomeGroupResponseDTO>> groupResponse = requestGroupProfit(startDateStr, endDateStr, userId, userOrgId, primaryAccount, i, MAX_PAGE_SIZE, null);
                respList.add(groupResponse);
            }
            for (BaseResponse<PageInfo<IncomeGroupResponseDTO>> groupResponse : respList) {
                if (groupResponse.getData() != null && groupResponse.getData().getPages() > 0) {
                    pageCount = groupResponse.getData().getPages();
                }
                if (groupResponse.getCode() == 0 && Objects.nonNull(groupResponse.getData())
                        && CollectionUtils.isNotEmpty(groupResponse.getData().getList()) && groupResponse.getData().getList().size() > 0) {
                    groupResponse.getData().getList().forEach(g -> {
                        try {
                            if (Objects.nonNull(g)) {
                                // 场地名称
                                String name = g.getGroupName();

                                // 场地收益统计
                                ProfitGroupDTO profitGroupDTO = new ProfitGroupDTO();
                                profitGroups.add(profitGroupDTO);
                                profitGroupDTO.setGroupName(name);
                                profitGroupDTO.setGroupId(Long.valueOf(g.getGroupId()));
                                profitGroupDTO.setAmount(BigDecimal.ZERO);

                                if (Objects.isNull(g.getOfflineAmount())) {
                                    g.setOfflineAmount(BigDecimal.ZERO);
                                }
                                if (Objects.isNull(g.getOfflineCoins())) {
                                    g.setOfflineCoins(0);
                                }
                                if (Objects.isNull(g.getAmount())) {
                                    g.setAmount(BigDecimal.ZERO);
                                }
                                profitGroupDTO.setOnlineAmount(g.getAmount());

                                BigDecimal amount = g.getOfflineAmount().add(g.getAmount()).add(BigDecimal.valueOf(g.getOfflineCoins()));
                                // 场地收益统计
                                profitGroupDTO.setAmount(amount);

                                List<NameValueVO> amounts = new ArrayList<>();
                                //维度线下投币与现金收益是一样的，都是线下收益，是同一个值，所以合并为offlineTotal
                                amounts.add(new NameValueVO(StatisticsDimensionConstants.DIMENSION_TEXT[StatisticsDimensionConstants.INCOME_ONLINE], profitAmountFilter(null,g.getAmount())));
                                amounts.add(new NameValueVO(StatisticsDimensionConstants.DIMENSION_TEXT[StatisticsDimensionConstants.OFFLINE_COINS], profitAmountFilter(userOrgId, g.getOfflineAmount().add(BigDecimal.valueOf(g.getOfflineCoins())))));
                                profitGroupDTO.setAmounts(amounts);
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    });
                }
            }
        }
        fillGroup(profitGroups, groupIds, userOrgId, userId);
        //线上收益倒序
        List<ProfitGroupDTO> newprofitGroups = profitGroups;
        if (CollectionUtils.isNotEmpty(newprofitGroups)) {
            newprofitGroups = newprofitGroups.stream().sorted(Comparator.comparing(ProfitGroupDTO::getOnlineAmount).reversed()).collect(Collectors.toList());
        }
        return newprofitGroups;
    }

    /**
     * 获取所有场地
     * @param groupIds
     * @param distributorId
     * @param adUserId
     */
    private List<ProfitGroupDTO> fillGroup(List<ProfitGroupDTO> originGroup, List<Long> groupIds, Long distributorId, Long adUserId) {

        MerchantGroupRequest.MerchantGroupRequestBuilder request = MerchantGroupRequest.builder()
                .distributor(distributorId)
                .isActive(1)
                .adUser(adUserId)
                .showGroupLabel(false);
        if (CollectionUtils.isNotEmpty(groupIds)) {
            request.groups(groupIds);
        }
        List<MerchantGroupDTO> allGroups = merchantGroupService.selectGroup(request.build()).getData();
        if (CollectionUtils.isEmpty(allGroups)) {
            return originGroup;
        }
        originGroup = ofNullable(originGroup).orElse(new ArrayList<>());
        Map<Long, ProfitGroupDTO> groupMap = originGroup.stream()
                .collect(Collectors.toMap(ProfitGroupDTO::getGroupId, Function.identity()));
        List<ProfitGroupDTO> finalOriginGroup = originGroup;
        allGroups.forEach(group -> {
            if (groupMap.containsKey(group.getEquipmentGroupId())) {
                return;
            }
            ProfitGroupDTO profitGroupDTO = new ProfitGroupDTO();
            profitGroupDTO.setGroupName(group.getName());
            profitGroupDTO.setGroupId(group.getEquipmentGroupId());
            profitGroupDTO.setAmount(BigDecimal.ZERO);
            profitGroupDTO.setOnlineAmount(profitGroupDTO.getAmount());
            List<NameValueVO> amounts = new ArrayList<>();
            //维度线下投币与现金收益是一样的，都是线下收益，是同一个值，所以合并为offlineTotal
            amounts.add(new NameValueVO(StatisticsDimensionConstants.DIMENSION_TEXT[StatisticsDimensionConstants.INCOME_ONLINE], CommonUtil.formatCurrency(0)));
            amounts.add(new NameValueVO(StatisticsDimensionConstants.DIMENSION_TEXT[StatisticsDimensionConstants.OFFLINE_COINS], CommonUtil.formatCurrency(0)));
            profitGroupDTO.setAmounts(amounts);
            finalOriginGroup.add(profitGroupDTO);

        });
        return finalOriginGroup;
    }

    /**
     * 计算总的线下收益（包括线下消费和线下投币）
     * @param offlineAmount
     * @param offlineCoins
     * @return
     */
    private static BigDecimal offlineTotal(BigDecimal offlineAmount, BigDecimal offlineCoins) {
        BigDecimal offlineTotal = checkAmount(null, offlineAmount);
        offlineTotal = checkAmount(offlineTotal, offlineCoins);
        return offlineTotal;
    }

    /**
     * 检测金额是否存在
     * @param originalAmount
     * @param addAmount
     * @return
     */
    private static BigDecimal checkAmount(BigDecimal originalAmount, BigDecimal addAmount) {
        if(Objects.isNull(originalAmount)) {
            originalAmount = BigDecimal.ZERO;
        }
        if (Objects.nonNull(addAmount)) {
            originalAmount = originalAmount.add(addAmount);
        }
        return originalAmount;
    }

    /**
     * 日期查询条件，返回开始和结束时间，
     * @param type  天、周、月 分类
     * @param offset  0表示当天、周、月，向前查是负数，没有正数
     * by mjl
     */
    public static DateQueryResult queryCondition(String type , int offset){
        LocalDate today = LocalDate.now();
        LocalDate beg = today;
        LocalDate end = today;
        switch (type){
            case "day":
                beg = today.plusDays(offset);
                end = beg;
                break;
            case "week":
                beg = today.plusWeeks(offset);
                beg = beg.minusDays(beg.getDayOfWeek().getValue()-1);
                if(offset == 0) {
                    end = today;
                } else {
                    end = beg.plusDays(6);
                }
                break;
            case "month":
                beg = today.plusMonths(offset);
                beg = beg.minusDays(beg.getDayOfMonth()-1);
                if(offset == 0) {
                    end = today;
                }
                else {
                    end = beg.plusMonths(1).plusDays(-1);
                }
                break;
            default:
        }

        DateQueryResult result = new DateQueryResult();
        result.setHasNextOffset(offset < 0);
        result.setHasPrevOffset(true);
        result.setCurOffset(offset);
        result.setNextOffset(offset >= 0 ? 0 : offset + 1);
        result.setPrevOffset(offset - 1);
        result.setType(type);
        result.setBegDate(beg);
        result.setEndDate(end);
        return result;

    }

    private ProfitDTO getDayProfit(IncomeToDayResponseDTO responseDTO){
        ProfitDTO profitDTO = new ProfitDTO();
        if(responseDTO != null){
            profitDTO.setOnlineCount(responseDTO.getOnlineCount());
            profitDTO.setAmount(responseDTO.getOnlineIncome());
        }else {
            profitDTO.setOnlineCount(0);
            profitDTO.setAmount(BigDecimal.ZERO);
        }
        return profitDTO;
    }

    /**
     * 金额屏蔽过滤
     * @param distributorId
     * @param amount
     * @return
     */
    private String profitAmountFilter(Long distributorId, BigDecimal amount) {
        if (checkIgnoreDistributorId(distributorId)) {
            return "0";
        } else {
            return Objects.isNull(amount) ? "0" : CommonUtil.formatCurrency(amount.doubleValue());
        }
    }

    /**
     * 判断商户是否屏蔽投币收益的
     * @param distributorId
     * @return
     */
    private boolean checkIgnoreDistributorId(Long distributorId) {
        if (distributorId == null) {
            return false;
        }
        cn.lyy.base.communal.bean.BaseResponse<Boolean> response = merchantWhiteClient.isWhiteDistributor(distributorId, WhiteListType.CDZ_CANCEL_COIN_REPORTING.getValue());
        if (response == null || response.getData()==null) {
            return false;
        }
        log.debug("商户[{}]是否屏蔽线下收益: {}", distributorId, response.getData());
        return response.getData();
    }

    /**
     * 获取线下总收益
     * @param isIgnoreCoinDistributorId
     * @param income
     * @return
     */
    private BigDecimal getOfflineTotal(boolean isIgnoreCoinDistributorId, IncomeResponseDTO income) {
        return isIgnoreCoinDistributorId ? BigDecimal.ZERO
            : offlineTotal(income.getOfflineAmount(), new BigDecimal(income.getOfflineCoins()));
    }
}
