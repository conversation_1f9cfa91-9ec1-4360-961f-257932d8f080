package cn.lyy.merchant.service.statistics;

import cn.lyy.income.api.fallback.MerchantIncomeFallbackFactory;
import cn.lyy.income.dto.bean.BaseResponse;
import cn.lyy.income.dto.income.*;
import cn.lyy.income.dto.param.MerchantIncomeRequest;
import com.github.pagehelper.PageInfo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(
        name = "income-statistics-query",
        fallbackFactory = MerchantIncomeFallbackFactory.class
)
public interface MerchantIncomeRemoteService {
    @PostMapping(
            value = {"/rest/income/merchant/general"},
            consumes = {"application/json"}
    )
    BaseResponse<IncomeResponseDTO> general(@RequestBody MerchantIncomeRequest var1);

    @PostMapping(
            value = {"/rest/income/merchant/general/today"},
            consumes = {"application/json"}
    )
    BaseResponse<IncomeToDayResponseDTO> generalToDay(@RequestBody MerchantIncomeRequest var1);

    @PostMapping(
            value = {"/rest/income/merchant/general/equipment"},
            consumes = {"application/json"}
    )
    BaseResponse<PageInfo<IncomeEquipmentResponseDTO>> generalEquipment(@RequestBody MerchantIncomeRequest var1);

    @PostMapping(
            value = {"/rest/income/merchant/general/group"},
            consumes = {"application/json"}
    )
    BaseResponse<PageInfo<IncomeGroupResponseDTO>> generalGroup(@RequestBody MerchantIncomeRequest var1);

    @PostMapping(
            value = {"/rest/income/merchant/general/equipment/type"},
            consumes = {"application/json"}
    )
    BaseResponse<PageInfo<IncomeEquipmentTypeResponseDTO>> generalEquipmentType(@RequestBody MerchantIncomeRequest var1);

    @PostMapping(
            value = {"/rest/income/merchant/general/equipment/label"},
            consumes = {"application/json"}
    )
    BaseResponse<PageInfo<IncomeEquipmentLabelDTO>> generalIncomeByLabel(@RequestBody MerchantIncomeRequest var1);
}
