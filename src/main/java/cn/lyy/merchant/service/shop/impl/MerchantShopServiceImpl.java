package cn.lyy.merchant.service.shop.impl;

import cn.lyy.merchant.api.service.MerchantGroupService;
import cn.lyy.merchant.api.service.MerchantShopClient;
import cn.lyy.merchant.api.service.MerchantTemplateService;
import cn.lyy.merchant.constant.BalanceSharedGroupType;
import cn.lyy.merchant.dto.account.SubAccountUserInfoDTO;
import cn.lyy.merchant.dto.common.IdDTO;
import cn.lyy.merchant.dto.group.BalanceSharedGroupDTO;
import cn.lyy.merchant.dto.group.MerchantEquipmentTypeConfigDTO;
import cn.lyy.merchant.dto.group.SummationOfEquipmentTypeDTO;
import cn.lyy.merchant.dto.merchant.request.MerchantGroupRequest;
import cn.lyy.merchant.dto.merchant.request.MerchantShopNoticeRequest;
import cn.lyy.merchant.dto.merchant.request.ShopInfoSetDTO;
import cn.lyy.merchant.dto.merchant.response.MerchantGroupDTO;
import cn.lyy.merchant.dto.merchant.response.MerchantShopNoticeDTO;
import cn.lyy.merchant.dto.merchant.response.ShopTitleRespDTO;
import cn.lyy.merchant.dto.request.BalanceSettingReqDTO;
import cn.lyy.merchant.dto.response.MerchantShopNoticeResponseDTO;
import cn.lyy.merchant.dto.response.ShopProfileDTO;
import cn.lyy.merchant.service.UserService;
import cn.lyy.merchant.service.shop.MerchantShopService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static java.util.Optional.ofNullable;

/**
 * @ClassName: MerchantShopServiceImpl
 * @description: 商家店铺
 * @author: pengkun
 * @create: 2020-11-16 16:54
 * @Version 1.0
 **/
@Service
@Slf4j
public class MerchantShopServiceImpl implements MerchantShopService {

    @Autowired
    private MerchantShopClient merchantShopClient;

    @Autowired
    private MerchantGroupService merchantGroupService;

    @Autowired
    private MerchantTemplateService merchantTemplateService;

    @Autowired
    private UserService userService;


    /**
     * 保存或更新公告
     *
     * @param request
     * @return
     */
    @Override
    public Long saveOrUpdateMerchantShopNotice(MerchantShopNoticeRequest request) {
        return ofNullable(merchantShopClient.saveOrUpdateMerchantShopNotice(request).getData()).orElse(0L);
    }

    /**
     * 删除公告
     *
     * @param request
     * @return
     */
    @Override
    public boolean delMerchantShopNotice(MerchantShopNoticeRequest request) {
        return ofNullable(merchantShopClient.delMerchantShopNotice(request).getData()).orElse(false);
    }

    /**
     * 获取商家店铺公告列表
     *
     * @param request
     * @return
     */
    @Override
    public List<MerchantShopNoticeResponseDTO> getNoticeList(MerchantShopNoticeRequest request) {
        List<MerchantShopNoticeDTO> noticeDTOList = merchantShopClient.getNoticeList(request).getData();
        if(noticeDTOList == null || noticeDTOList.size() == 0){
            return  new ArrayList<>();
        }
        Map<Long, List<MerchantShopNoticeDTO>> groupNoticeMap = noticeDTOList.stream().collect(Collectors.toMap(MerchantShopNoticeDTO::getNoticeId,
                v -> Lists.newArrayList(v), (v1, v2) -> {
                    v1.addAll(v2);
                    return v1;
                }));
        List<MerchantShopNoticeResponseDTO> list = Lists.newArrayListWithCapacity(groupNoticeMap.size());
        groupNoticeMap.forEach((k, v)->{
            MerchantShopNoticeResponseDTO dto = new MerchantShopNoticeResponseDTO();
            MerchantShopNoticeDTO merchantShopNoticeDO = v.get(0);
            dto.setNoticeId(merchantShopNoticeDO.getNoticeId());
            dto.setContent(merchantShopNoticeDO.getContent());
            dto.setCreate(merchantShopNoticeDO.getCreate());
            dto.setTitle(merchantShopNoticeDO.getTitle());
            dto.setGroups(v.stream().collect(Collectors.toMap(MerchantShopNoticeDTO::getGroupId, MerchantShopNoticeDTO::getGroupName)));
            list.add(dto);
        });
        list.sort(Comparator.comparing(MerchantShopNoticeResponseDTO::getNoticeId).reversed());
        return list;
    }

    @Override
    public ShopTitleRespDTO pageTitleList(Long distributorId, Long userId) {
        return merchantShopClient.shopInfoList(distributorId, userId,1).getData();
    }

    @Override
    public void pageTitleSet(Long distributorId, Long userId, ShopInfoSetDTO param) {
        merchantShopClient.shopInfoSet(param, distributorId, userId);
    }

    @Override
    public ShopTitleRespDTO servicePhoneList(Long distributorId, Long userId) {
        return merchantShopClient.shopInfoList(distributorId, userId,2).getData();
    }

    @Override
    public List<SummationOfEquipmentTypeDTO> enforceRechargeList(Long distributorId, Long userId) {
        List<SummationOfEquipmentTypeDTO> resultList = merchantGroupService.withEquipmentTypeSummation(distributorId, userId).getData();
        if (!CollectionUtils.isEmpty(resultList)) {
            // 前端展示数据比较不寻常，所以需要遍历拼接
            // 以前的表设计开关值是 1=关闭，0=开启，修改会影响现有系统的使用，所以沿用
            List<MerchantEquipmentTypeConfigDTO> configs = merchantGroupService.settingsWithGroupAndType(distributorId).getData();
            if (!CollectionUtils.isEmpty(configs)) {
                resultList.forEach(result -> {
                    result.getGroups().forEach(group -> {
                        group.setEnforceRecharge(1);
                        configs.forEach(config -> {
                            if (Objects.equals(group.getEquipmentTypeId(), config.getEquipmentTypeId())
                                    && Objects.equals(group.getGroupId(), config.getGroupId())) {
                                group.setEnforceRecharge(config.getEnforceRechargeToStart());
                            }
                        });
                    });

                });
            }
        } else {
            resultList = new ArrayList<>();
        }
        return resultList;
    }

    @Override
    public void enforceRechargeSet(Long distributorId, Long userId, List<MerchantEquipmentTypeConfigDTO> param) {
        merchantGroupService.setWithGroupAndType(param, distributorId, userId);
    }

    @Override
    public List<BalanceSharedGroupDTO> balanceSharedGroupList(Long distributorId, Long userId) {
        return merchantShopClient.sharedGroupList(distributorId, userId,
                BalanceSharedGroupType.MONEY.getType()).getData();
    }

    @Override
    public BalanceSharedGroupDTO balanceSharedGroupInfo(Long distributorId, Long balanceSharedGroupId, Long userId) {
        return merchantShopClient.sharedGroupInfo(distributorId, balanceSharedGroupId, userId, BalanceSharedGroupType.MONEY.getType()).getData();
    }

    @Override
    public void balanceSharedGroupSet(Long distributorId, Long userId, BalanceSettingReqDTO param) {
        merchantShopClient.sharedGroupSave(param.getBalanceSharedGroupId(), param.getName(),
                BalanceSharedGroupType.MONEY.getType(), distributorId, userId, param.getGroups());
    }

    @Override
    public ShopProfileDTO shopProfile(IdDTO idDTO) {
        Long distributorId = idDTO.getUserOrgId();
        ShopProfileDTO profile = new ShopProfileDTO();
        MerchantGroupRequest request = MerchantGroupRequest.builder()
                .isActive(1)
                .distributor(distributorId)
                .adUser(idDTO.getUserId())
                .build();
        List<MerchantGroupDTO> groupList = merchantGroupService.selectGroup(request).getData();
        profile.setServicePhone(false);
        if (groupList != null) {
            profile.setNumberOfGroup(groupList.size());
            ShopTitleRespDTO resp = merchantShopClient.shopInfoList(distributorId, idDTO.getUserId(), 2).getData();
            long sizeOfSet = resp.getShopInfos().stream().filter(shop -> shop.getServicePhone() != null).count();
            if (sizeOfSet == groupList.size()) {
                profile.setServicePhone(true);
            }
        }
        Integer numberOfStaff  = 0;
        if (idDTO.getIsApprover()) {
            List<SubAccountUserInfoDTO> subAccountInfos = userService.getSubAccountInfos(idDTO);
            numberOfStaff = Optional.ofNullable(subAccountInfos).map(List::size).orElse(0);
        }
        profile.setNumberOfStaff(numberOfStaff);

        return profile;
    }

}
