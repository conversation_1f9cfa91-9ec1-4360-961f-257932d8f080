package cn.lyy.merchant.service.shop;


import cn.lyy.merchant.dto.common.IdDTO;
import cn.lyy.merchant.dto.group.BalanceSharedGroupDTO;
import cn.lyy.merchant.dto.group.MerchantEquipmentTypeConfigDTO;
import cn.lyy.merchant.dto.group.SummationOfEquipmentTypeDTO;
import cn.lyy.merchant.dto.merchant.request.MerchantShopNoticeRequest;
import cn.lyy.merchant.dto.merchant.request.ShopInfoSetDTO;
import cn.lyy.merchant.dto.merchant.response.ShopTitleRespDTO;
import cn.lyy.merchant.dto.request.BalanceSettingReqDTO;
import cn.lyy.merchant.dto.response.MerchantShopNoticeResponseDTO;
import cn.lyy.merchant.dto.response.ShopProfileDTO;

import java.util.List;

public interface MerchantShopService {
    /**
     * 保存或更新公告
     * @param request
     * @return
     */
    Long saveOrUpdateMerchantShopNotice(MerchantShopNoticeRequest request);

    /**
     * 删除公告
     * @param request
     * @return
     */
    boolean delMerchantShopNotice(MerchantShopNoticeRequest request);

    /**
     * 获取商家店铺公告列表
     * @param request
     * @return
     */
    List<MerchantShopNoticeResponseDTO> getNoticeList(MerchantShopNoticeRequest request);

    ShopTitleRespDTO pageTitleList(Long distributorId, Long userId);

    void pageTitleSet(Long distributorId, Long userId, ShopInfoSetDTO param);

    ShopTitleRespDTO servicePhoneList(Long distributorId, Long userId);

    List<SummationOfEquipmentTypeDTO> enforceRechargeList(Long distributorId, Long userId);

    void enforceRechargeSet(Long distributorId, Long userId, List<MerchantEquipmentTypeConfigDTO> param);

    List<BalanceSharedGroupDTO> balanceSharedGroupList(Long distributorId, Long userId);

    BalanceSharedGroupDTO balanceSharedGroupInfo(Long distributorId, Long balanceSharedGroupId, Long userId);

    void balanceSharedGroupSet(Long adOrgIdNotNull, Long adUserIdNotNull, BalanceSettingReqDTO param);

    ShopProfileDTO shopProfile(IdDTO idDTO);
}
