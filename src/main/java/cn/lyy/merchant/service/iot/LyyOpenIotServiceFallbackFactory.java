package cn.lyy.merchant.service.iot;

import feign.hystrix.FallbackFactory;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2019/9/9
 * @Version 1.0
 **/
@Component
public class LyyOpenIotServiceFallbackFactory implements FallbackFactory<LyyOpenIotService> {
    private static final Logger log = LoggerFactory.getLogger(LyyOpenIotServiceFallbackFactory.class);

    public LyyOpenIotServiceFallbackFactory() {
    }

    @Override
    public LyyOpenIotService create(Throwable cause) {
        if (cause != null && StringUtils.isNotEmpty(cause.getMessage())) {
            log.error("fallback reason was:" + cause.getMessage(), cause);
        }

        return (uniqueCode) -> {
            log.error(String.format("调用queryDeviceType微服务接口异常,uniqueCode:%s", uniqueCode));
            return null;
        };
    }
}