package cn.lyy.merchant.service.iot;

import cn.lyy.merchant.dto.response.RetResultVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(
        name = "lyyopen-iot",
        fallbackFactory = LyyOpenIotServiceFallbackFactory.class
)
public interface LyyOpenIotService {
    @RequestMapping(
            value = {"/rest/device/deviceType"},
            method = {RequestMethod.POST}
    )
    RetResultVO queryDeviceType(@RequestParam("uniqueCode") String var1);
}
