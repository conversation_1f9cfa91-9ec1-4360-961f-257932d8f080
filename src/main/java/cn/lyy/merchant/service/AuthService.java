package cn.lyy.merchant.service;

import cn.lyy.auth.request.AuthTokenRequest;
import cn.lyy.authority_service_api.merchant.EditableResourcesDTO;
import cn.lyy.base.dto.JsonObject;
import cn.lyy.merchant.dto.auth.AuthUserDTO;
import cn.lyy.merchant.dto.auth.RoleDTO;

import java.util.List;
import java.util.Map;

/**
 * <p>Title:saas2</p>
 * <p>Desc: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020/11/16
 */
public interface AuthService {

    /**
     * 创建默认角色
     * @param authorityUserId
     * @param userId
     * @param userOrgId
     * @return
     */
    int createDefaultRole(Long authorityUserId , Long userId , Long userOrgId);

    /**
     * 添加或更新角色的
     * @param roleDTO
     * @return
     */
    JsonObject saveOrUpdateRole(RoleDTO roleDTO);

    /**
     * 获取需要自动鉴权通过的资源
     * @return
     */
    List<EditableResourcesDTO> getAutoPassAuth();

    /**
     * 获取默认的角色配置
     * @return
     */
    Map<String, List<String>> getDefaultRoles();

    /**
     * 根据手机号加载权限用户信息
     * @param phone
     * @param systemId
     * @return
     */
    AuthUserDTO loadAuthorityUser(String phone, long systemId);

    /**
     * 权限库添加用户记录和用户角色记录
     * @param adUserDTO
     * @param systemVersion1
     * @param systemVersion2
     * @return
     */
    JsonObject addAuthorityUser(cn.lyy.merchant.dto.user.AdUserDTO adUserDTO, long systemVersion1, long systemVersion2);

    /**
     * 生成token
     * @param authTokenRequest
     * @return
     */
    String generateToken(AuthTokenRequest authTokenRequest);
}
