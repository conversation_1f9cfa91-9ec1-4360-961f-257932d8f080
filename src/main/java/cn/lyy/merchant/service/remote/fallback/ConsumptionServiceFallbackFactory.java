package cn.lyy.merchant.service.remote.fallback;

import cn.lyy.merchant.service.remote.ConsumptionService;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <p>Title:fork</p>
 * <p>Desc: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020/11/21
 */
@Component
@Slf4j
public class ConsumptionServiceFallbackFactory implements FallbackFactory<ConsumptionService> {
    @Override
    public ConsumptionService create(Throwable throwable) {
        if (throwable != null && StringUtils.isNotEmpty(throwable.getMessage())) {
            log.error(throwable.getMessage(), throwable);
        }
        return grantCoinsDTO -> {
            log.error("保存派币记录熔断[saveGrantCoins],{}", grantCoinsDTO);
            return null;
        };
    }
}
