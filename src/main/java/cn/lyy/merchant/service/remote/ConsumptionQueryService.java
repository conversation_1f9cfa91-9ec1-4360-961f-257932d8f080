package cn.lyy.merchant.service.remote;

import cn.lyy.base.dto.Pagination;
import cn.lyy.lyy_consumption_api.merchant.GrantCoinsDTO;
import cn.lyy.lyy_consumption_api.merchant.LyyGrantCoinsPara;
import cn.lyy.merchant.service.remote.fallback.ConsumptionQueryServiceFallbackFactory;
import cn.lyy.user_statistics_api.response.BaseResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <p>Title:fork</p>
 * <p>Desc: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020/11/21
 */
@FeignClient(
        name = "lyy-consumption-query-service",
            fallbackFactory = ConsumptionQueryServiceFallbackFactory.class
)
public interface ConsumptionQueryService {

    /**
     * 获取汇总的派币信息
     */
    @PostMapping({"/lyyGrantCoins/getAllGrantCoins"})
    GrantCoinsDTO getAllGrantCoins(LyyGrantCoinsPara param);

    @PostMapping({"/lyyGrantCoins/getByLyyUserIdAndDistributorId"})
    Pagination<GrantCoinsDTO> getByLyyUserIdAndDistributorId(LyyGrantCoinsPara param);
    /**
     * 查询红包历史派送总额
     * @return
     */
    @GetMapping({"/lyyGrantCoins/getTotalReceive"})
    BaseResponse getRedAmountTotalHistory(@RequestParam("distributorId") Long distributorId , @RequestParam("userId") Long userId);
}
