package cn.lyy.merchant.service.remote;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.merchant.dto.AdvertOperationDTO;
import cn.lyy.merchant.dto.OfficialAccountDTO;
import cn.lyy.merchant.dto.response.RetResultVO;
import com.lyy.advert.api.internal.dto.cdz.LyyInsuranceOrderDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient("lyy-advert-server")
public interface AdvertService {

    /**
     * 查询公众号授权信息列表
     * @param queryDTO
     * @return
     */
    @PostMapping(value = "/task/manage/account/query")
    BaseResponse<List<OfficialAccountDTO>> query(@RequestBody AdvertOperationDTO queryDTO);

    /**
     * 指定appid获取公众号授权信息
     * @param appId
     * @return
     */
    @GetMapping({"/official-account/query/by/appid"})
    BaseResponse<OfficialAccountDTO> queryByAppId(@RequestParam("appId") String appId);

    /**
     * 更新公众号授权信息
     * @param queryDTO
     * @return
     */
    @PostMapping(value = "/official-account/operate/update")
    RetResultVO update(@RequestBody AdvertOperationDTO queryDTO);


    /**
     * 根据订单号查询投保信息
     * @param outTradeNo
     * @return
     */
    @PostMapping("/cdz/lyyDistributor/findInsuranceOrderInfo")
    BaseResponse<LyyInsuranceOrderDTO> findInsuranceOrderInfo(@RequestParam("outTradeNo") String outTradeNo);

}