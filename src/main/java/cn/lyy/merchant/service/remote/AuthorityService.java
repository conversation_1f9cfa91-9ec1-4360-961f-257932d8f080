package cn.lyy.merchant.service.remote;

import cn.lyy.authority_service_api.miscroservice.AuthorityServiceFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;
import java.util.Map;

/**
 * <p>Title:saas2</p>
 * <p>Desc: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020/11/16
 */
@FeignClient(
        name = "lyy-authority-service",
        fallbackFactory = AuthorityServiceFallbackFactory.class,
        primary = false
)
public interface AuthorityService extends cn.lyy.authority_service_api.miscroservice.AuthorityService {
    @RequestMapping(
            value = {"/authority/buttonParentMapRolesBySystem/{systemId}"},
            method = {RequestMethod.GET}
    )
    Map<String, List<String>> buttonParentMapRolesBySystem(@PathVariable("systemId") long systemId);
}
