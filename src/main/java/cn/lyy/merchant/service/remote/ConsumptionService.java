package cn.lyy.merchant.service.remote;

import cn.lyy.lyy_consumption_api.customer.LyyGrantCoinsDTO;
import cn.lyy.merchant.service.remote.fallback.ConsumptionServiceFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <p>Title:fork</p>
 * <p>Desc: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020/11/21
 */
@FeignClient(
        name = "lyy-consumption-service",
        fallbackFactory = ConsumptionServiceFallbackFactory.class
)
public interface ConsumptionService {

    /**
     * 保存派币记录
     */
    @PostMapping({"/lyyGrantCoins/save"})
    Integer saveGrantCoins(LyyGrantCoinsDTO grantCoinsDTO);
}
