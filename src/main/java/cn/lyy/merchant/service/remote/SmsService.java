package cn.lyy.merchant.service.remote;

import cn.lyy.merchant.dto.common.SMSSendResult;
import cn.lyy.merchant.service.remote.fallback.SmsServiceFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <p>Title:saas2</p>
 * <p>Desc: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020/11/14
 */
@FeignClient(name = "sms-service", fallbackFactory = SmsServiceFallbackFactory.class)
public interface SmsService {

    @GetMapping("/sms/sendCode")
    SMSSendResult sendCode(@RequestParam("phoneNumber") String phoneNumber, @RequestParam("userId") Long userId , @RequestParam(value = "tag" , required = false) String tag);

    @GetMapping("/sms/hasSendInSeconds")
    boolean hasSendInSeconds(@RequestParam("phoneNumber") String phoneNumber, @RequestParam("seconds") int seconds);

    @GetMapping("/sms/validateCode")
    boolean validateCode(@RequestParam("phoneNumber") String phoneNumber, @RequestParam("code") String code);

    @GetMapping("/sms/sendContent")
    SMSSendResult sendContent(@RequestParam("phoneNumber") String phoneNumber, @RequestParam("content") String content, @RequestParam("smsType") String smsType);
}
