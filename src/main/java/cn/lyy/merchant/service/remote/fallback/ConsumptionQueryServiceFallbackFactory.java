package cn.lyy.merchant.service.remote.fallback;

import cn.lyy.base.dto.Pagination;
import cn.lyy.lyy_consumption_api.merchant.GrantCoinsDTO;
import cn.lyy.lyy_consumption_api.merchant.LyyGrantCoinsPara;
import cn.lyy.merchant.service.remote.ConsumptionQueryService;
import cn.lyy.user_statistics_api.response.BaseResponse;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <p>Title:fork</p>
 * <p>Desc: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020/11/21
 */
@Component
@Slf4j
public class ConsumptionQueryServiceFallbackFactory implements FallbackFactory<ConsumptionQueryService> {
    @Override
    public ConsumptionQueryService create(Throwable throwable) {
        if (throwable != null && StringUtils.isNotEmpty(throwable.getMessage())) {
            log.error(throwable.getMessage(), throwable);
        }
        return new ConsumptionQueryService() {
            @Override
            public GrantCoinsDTO getAllGrantCoins(LyyGrantCoinsPara param) {
                log.error("获取汇总的派币信息熔断[getAllGrantCoins],{}", param);
                return null;
            }

            @Override
            public Pagination<GrantCoinsDTO> getByLyyUserIdAndDistributorId(LyyGrantCoinsPara param) {
                log.error("获取派币信息熔断[getByLyyUserIdAndDistributorId],{}", param);
                return null;
            }

            @Override
            public BaseResponse getRedAmountTotalHistory(Long distributorId, Long userId) {
                log.error("查询红包历史派送总额[getRedAmountTotalHistory],{},{}", distributorId, userId);
                return null;
            }
        };
    }
}
