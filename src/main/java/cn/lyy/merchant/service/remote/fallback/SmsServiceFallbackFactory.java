package cn.lyy.merchant.service.remote.fallback;

import cn.lyy.merchant.dto.common.SMSSendResult;
import cn.lyy.merchant.service.remote.SmsService;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <p>Title:saas2</p>
 * <p>Desc: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020/11/14
 */
@Slf4j
@Component
public class SmsServiceFallbackFactory implements FallbackFactory<SmsService> {

    @Override
    public SmsService create(Throwable throwable) {
        if (throwable != null && StringUtils.isNotBlank(throwable.getMessage())) {
            log.error("fallback reason was:" + throwable.getMessage(), throwable);
        }
        return new SmsService() {

            @Override
            public SMSSendResult sendCode(String phoneNumber, Long userId , String tag) {
                log.error(String.format("调用sendCode微服务接口异常,phoneNumber:%s", phoneNumber));
                return null;
            }

            @Override
            public boolean hasSendInSeconds(String phoneNumber, int seconds) {
                log.error(String.format("调用phoneNumber微服务接口异常,phoneNumber:%s", phoneNumber));
                return false;
            }

            @Override
            public boolean validateCode(String phoneNumber, String code) {
                log.error(String.format("调用hasSendInSeconds微服务接口异常,phoneNumber:%s", phoneNumber));
                return false;
            }

            @Override
            public SMSSendResult sendContent(String phoneNumber, String content, String smsType) {
                log.error(String.format("调用sendContent微服务接口异常,phoneNumber:%s", phoneNumber));
                return null;
            }
        };
    }
}
