package cn.lyy.merchant.service;

import cn.lyy.merchant.dto.account.DivideUserDTO;
import cn.lyy.merchant.dto.common.DivideDTO;

import java.util.List;


/**
 * @description:
 * 分账 服务类</p>
 * @author: qgw
 * @date on 2021/3/5.
 * @Version: 1.0
 */
public interface DivideService {

    /**
     * 根据商户获取分成人员信息
     * @param divideUserDTO
     * @return
     */
    List<DivideUserDTO> getDivideUsers(DivideDTO divideUserDTO);

}
