package cn.lyy.merchant.service.officialaccount.impl;

import cn.lyy.merchant.api.service.TemplateMessageConfigClient;
import cn.lyy.merchant.dto.AdvertOperationDTO;
import cn.lyy.merchant.dto.OfficialAccountDTO;
import cn.lyy.merchant.dto.template.SaasTemplateMessageConfigDTO;
import cn.lyy.merchant.service.officialaccount.OfficialAccountsService;
import cn.lyy.merchant.service.remote.AdvertService;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * 类描述：公众号配置
 * <p>
 *
 * <AUTHOR>
 * @since 2021/3/3 15:34
 */
@Service
public class OfficialAccountsServiceImpl implements OfficialAccountsService {

    @Autowired
    private AdvertService advertService;

    @Autowired
    private TemplateMessageConfigClient templateMessageConfigClient;


    @Override
    public SaasTemplateMessageConfigDTO getTemMesConfig(Long distributorId) {
        SaasTemplateMessageConfigDTO param = new SaasTemplateMessageConfigDTO();
        param.setLyyDistributorId(distributorId);
        List<SaasTemplateMessageConfigDTO> list = templateMessageConfigClient.queryByCondition(param).getData();
        if (CollectionUtils.isEmpty(list)) {
            return null;
        } else {
            return list.get(0);
        }
    }

    @Override
    public OfficialAccountDTO getOfficialAccoutByAppId(String authorizerAppid) {
        return advertService.queryByAppId(authorizerAppid).getData();
    }

    @Override
    public SaasTemplateMessageConfigDTO getTemMesConfig(String authorizerAppid, Long distributorId) {
        SaasTemplateMessageConfigDTO param = new SaasTemplateMessageConfigDTO();
        param.setLyyDistributorId(distributorId);
        param.setAppId(authorizerAppid);
        List<SaasTemplateMessageConfigDTO> list = templateMessageConfigClient.queryByCondition(param).getData();
        if (CollectionUtils.isEmpty(list)) {
            return null;
        } else {
            return list.get(0);
        }
    }

    @Override
    public Long saveTemMesConfig(String authorizerAppid, Long distributorId, String officialAccout, String eType) {
        SaasTemplateMessageConfigDTO param = new SaasTemplateMessageConfigDTO();
        param.setAppId(authorizerAppid);
        param.setLyyDistributorId(distributorId);
        param.setOfficialAccount(officialAccout);
        param.setEquipmentType(eType);
        return templateMessageConfigClient.saveConfig(param).getData();
    }

    @Override
    public void updatePlatformType(Long lyyOfficialAccountId, Integer platformType) {
        AdvertOperationDTO param = new AdvertOperationDTO();
        param.setPlatformType(platformType);
        param.setLyyOfficialAccoutId(lyyOfficialAccountId);
        advertService.update(param);
    }

    @Override
    public List<SaasTemplateMessageConfigDTO> getAllTemMesConfig(Long distributorId) {
        SaasTemplateMessageConfigDTO param = new SaasTemplateMessageConfigDTO();
        param.setLyyDistributorId(distributorId);
        List<SaasTemplateMessageConfigDTO> list = templateMessageConfigClient.queryByCondition(param).getData();
        return list;
    }

    @Override
    public void updateSaasTemMesConfig(SaasTemplateMessageConfigDTO cau) {
        templateMessageConfigClient.updateConfig(cau);
    }

    @Override
    public List<SaasTemplateMessageConfigDTO> findByPagination(int offset, int size) {
        PageInfo<SaasTemplateMessageConfigDTO> page = templateMessageConfigClient.listByEquipmentType(offset, size).getData();
        if (page == null) {
            return Collections.EMPTY_LIST;
        }
        return page.getList();
    }

    @Override
    public void updateMenuConfig(Integer status, String oldMenuConfig, String appId) {
        SaasTemplateMessageConfigDTO updateParam = new SaasTemplateMessageConfigDTO();
        updateParam.setAppId(appId);
        updateParam.setMenuConfig(oldMenuConfig);
        updateParam.setMenuStatus(status);
        templateMessageConfigClient.updateConfig(updateParam);
    }

    @Override
    public void deleteSaasTemMesCon(String appId) {
        templateMessageConfigClient.deleteConfig(appId);
    }
}
