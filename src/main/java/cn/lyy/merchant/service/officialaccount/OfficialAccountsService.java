package cn.lyy.merchant.service.officialaccount;

import cn.lyy.merchant.dto.OfficialAccountDTO;
import cn.lyy.merchant.dto.template.SaasTemplateMessageConfigDTO;

import java.util.List;

/**
 * 类描述：
 * <p>
 *
 * <AUTHOR>
 * @since 2021/3/3 15:33
 */
public interface OfficialAccountsService {
    /**
     * 获取商户模板消息配置
     * @param adOrgIdNotNull
     * @return
     */
    SaasTemplateMessageConfigDTO getTemMesConfig(Long adOrgIdNotNull);

    OfficialAccountDTO getOfficialAccoutByAppId(String authorizerAppid);

    SaasTemplateMessageConfigDTO getTemMesConfig(String authorizerAppid, Long distributorId);

    Long saveTemMesConfig(String authorizerAppid, Long distributorId, String officialAccout, String eType );

    void updatePlatformType(Long lyyOfficialAccoutId, Integer platformType);

    List<SaasTemplateMessageConfigDTO> getAllTemMesConfig(Long adOrgIdNotNull);

    void updateSaasTemMesConfig(SaasTemplateMessageConfigDTO cau);

    List<SaasTemplateMessageConfigDTO> findByPagination(int offset, int size);

    void updateMenuConfig(Integer status, String oldMenuConfig, String appId);

    void deleteSaasTemMesCon(String appId);
}
