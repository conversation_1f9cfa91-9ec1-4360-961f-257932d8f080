package cn.lyy.merchant.service;

import cn.lyy.merchant.dto.DaMerchantAnnualReportYiVO;

import java.util.List;

/**
 * <AUTHOR> =￣ω￣=
 * @date 2022/12/2
 */
public interface MerchantAnnualReportService {

    DaMerchantAnnualReportYiVO getByMerchantId(Long merchantId);

    void cacheReportByMerchantIds(List<String> merchantIds);

    void deleteCacheReportByMerchantIds(List<String> merchantIds);

    Boolean isPopUps(String merchantId);

    void deletePopUpsCache(String merchantId, String today);
}
