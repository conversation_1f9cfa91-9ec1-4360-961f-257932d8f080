package cn.lyy.merchant.service.refund;

/**
 * 类描述：启动退费处理
 * <p>
 *
 * <AUTHOR>
 * @since 2020/11/13 15:14
 */
public interface StartRefundService {

    /**
     * 同步工厂配置，商家注册设备时初始化同步，如启动与退费开关
     * @param distributorId
     * @param userId
     * @param factoryId
     * @param groupId
     * @param equipmentTypeValue
     * @return
     */
    int syncFactorySetting(Long distributorId,Long userId, Long factoryId, Long groupId, String equipmentTypeValue);

}
