package cn.lyy.merchant.service.refund.impl;

import cn.lyy.equipment.dto.equipment.EquipmentTypeDTO;
import cn.lyy.equipment.service.IEquipmentTypeService;
import cn.lyy.merchant.api.service.AgentClient;
import cn.lyy.merchant.api.service.MerchantEquipmentService;
import cn.lyy.merchant.constants.BooleanEnum;
import cn.lyy.merchant.dto.equipment.SaasMerchantEquipmentConfigDTO;
import cn.lyy.merchant.dto.merchant.AgentPluginMerchantMappingDTO;
import cn.lyy.merchant.service.refund.StartRefundService;
import cn.lyy.merchant.util.JsonUtils;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableSet;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 类描述：启动退费处理
 * <p>
 *
 * <AUTHOR>
 * @since 2020/11/13 15:14
 */
@Slf4j
@Service
public class StartRefundServiceImpl implements StartRefundService {

    private static ImmutableSet<String> supportType = ImmutableSet.of("QCCDZ","CDZ", "XYJ");

    @Autowired
    private IEquipmentTypeService equipmentTypeService;

    @Autowired
    private MerchantEquipmentService merchantEquipmentService;

    @Autowired
    private AgentClient agentClient;

    @Override
    public int syncFactorySetting(Long distributorId, Long userId, Long factoryId, Long groupId, String equipmentTypeValue) {
        if(Objects.isNull(factoryId)) {
            return 1;
        }
        if(!supportType.contains(equipmentTypeValue)) {
            log.debug("不支持的品类，不用开启自动退费功能！");
            return 1;
        }
        EquipmentTypeDTO equipmentTypeDTO = equipmentTypeService.getByValue(equipmentTypeValue).getData();
        SaasMerchantEquipmentConfigDTO config = merchantEquipmentService.getSaasConfig(1, distributorId, groupId, equipmentTypeDTO.getEquipmentTypeId(), null).getData();
        if(Objects.isNull(config)) {
            AgentPluginMerchantMappingDTO param = new AgentPluginMerchantMappingDTO();
            param.setFactoryOrgId(factoryId);
            param.setFunctionId("Plugin-StartRefun");
            AgentPluginMerchantMappingDTO agentPluginMerchantMapping = agentClient.queryByParam(param).getData();
            if(Objects.isNull(agentPluginMerchantMapping)) {
                return 1;
            }
            JSONObject ruleJsonObj;
            try {
                ruleJsonObj = JsonUtils.parseObject(agentPluginMerchantMapping.getRuleJson());
            } catch (Exception e) {
                return -1;
            }
            if(BooleanEnum.YES.getValue().equals(ruleJsonObj.getString("isOpen"))) {
                log.debug("商家：{}未设置过该场地：{}、该品类：{}的自动退费功能，工厂配置开启！", distributorId, groupId, equipmentTypeValue);
                merchantEquipmentService.setAutoRefundStatus(userId, distributorId, equipmentTypeDTO.getEquipmentTypeId(), groupId, 0);
            }
        } else {
            log.debug("商家：{}已设置过该场地：{}、该品类：{}的自动退费功能！", distributorId, groupId, equipmentTypeValue);
        }
        return 0;
    }


}
