package cn.lyy.merchant.service.order.impl;

import static java.util.Optional.ofNullable;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.dto.Pagination;
import cn.lyy.base.utils.converter.CommonConverterTools;
import cn.lyy.equipment.dto.equipment.EquipmentTypeDTO;
import cn.lyy.equipment.service.IEquipmentTypeService;
import cn.lyy.merchant.api.service.MerchantGroupService;
import cn.lyy.merchant.constants.OrderExceptionEnums;
import cn.lyy.merchant.dto.merchant.response.MerchantGroupDTO;
import cn.lyy.merchant.dto.request.OrderQueryRequest;
import cn.lyy.merchant.dto.response.OrderInfoDTO;
import cn.lyy.merchant.dto.response.OrderInfoDetailDTO;
import cn.lyy.merchant.dto.response.OrderInfoDetailDTO.OrderGoodsDetail;
import cn.lyy.merchant.exception.OrderException;
import cn.lyy.merchant.microservice.CommoditySkuService;
import cn.lyy.merchant.service.order.OrderService;
import cn.lyy.merchant.service.order.RefundService;
import cn.lyy.merchant.service.remote.AdvertService;
import cn.lyy.merchant.utils.RemoteResponseUtils;
import cn.lyy.open.order.api.OrderServiceInterface;
import cn.lyy.open.order.api.v2.OrderClient;
import cn.lyy.open.order.api.v2.RefundOrderClient;
import cn.lyy.open.order.constans.ClientTypeEnum;
import cn.lyy.open.order.constans.OrderEnum;
import cn.lyy.open.order.constans.OrderEnum.OrderStatesEnum;
import cn.lyy.open.order.constans.OrderRefundMarkEnum;
import cn.lyy.open.order.dto.OrderAggregationDTO;
import cn.lyy.open.order.dto.OrderItemAggregationDTO;
import cn.lyy.open.order.dto.Page;
import cn.lyy.open.order.dto.request.OrderDTO;
import cn.lyy.open.order.dto.request.OrderDTO.EquipmentDetail;
import cn.lyy.open.order.dto.request.OrderDetailQueryDTO;
import cn.lyy.open.order.dto.request.v2.query.OrderDetailQueryRequest;
import cn.lyy.open.order.dto.response.RefundQueryInfoDTO;
import cn.lyy.open.order.dto.response.v2.OrderQueryResponse;
import cn.lyy.open.payment.api.PaymentServiceInterface;
import cn.lyy.open.payment.constant.PaymentModeEnum;
import cn.lyy.open.payment.dto.request.PaymentRecordDTO;
import cn.lyy.user.api.service.IUserService;
import com.google.common.collect.Sets;
import com.lyy.advert.api.internal.dto.cdz.LyyInsuranceOrderDTO;
import com.lyy.commodity.rpc.dto.response.CommodityDetailResDTO;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @createTime 2020-12-02
 * @auther peterguo
 * @Description
 */
@Service
@Slf4j
public class OrderServiceImpl implements OrderService {

    @Autowired
    private OrderServiceInterface orderService;

    @Autowired
    private IUserService userService;

    @Autowired
    private RefundService refundService;

    @Autowired
    private CommoditySkuService commoditySkuService;

    @Autowired
    private MerchantGroupService merchantGroupService;

    @Autowired
    private IEquipmentTypeService equipmentTypeService;

    @Autowired
    private AdvertService advertService;

    @Autowired
    private OrderClient orderClient;

    @Autowired
    private RefundOrderClient refundOrderClient;

    @Autowired
    private PaymentServiceInterface paymentServiceInterface;

    @Override
    public Pagination<OrderInfoDTO> getOrderList(OrderQueryRequest orderQueryRequest) {

        OrderDetailQueryDTO orderDetailQueryDTO = CommonConverterTools.convert(OrderDetailQueryDTO.class, orderQueryRequest);

        Page<OrderAggregationDTO> page = ofNullable(orderService.search(orderDetailQueryDTO)) // 查询订单相关信息
                .map(BaseResponse::getData).orElseGet(Page::new);

        List<OrderAggregationDTO> orderAggregationList = page.getItems();

        return ofNullable(orderAggregationList).map(aggres -> {

            List<OrderInfoDTO> orderInfoList = orderAggregationList.parallelStream().map(orderAggre -> {
                OrderInfoDTO orderInfoDTO = CommonConverterTools.convert(OrderInfoDTO.class, orderAggre);

                OrderInfoDTO.MemberDetail memberDetail = new OrderInfoDTO.MemberDetail();
                ofNullable(orderAggre.getUser())
                        .map(OrderDTO.UserInfo::getUserId)
                        .ifPresent(memberDetail::setUserId);
                orderInfoDTO.setMemberDetail(memberDetail);

                OrderInfoDTO.OrderDetail orderDetail = new OrderInfoDTO.OrderDetail();
                ofNullable(orderAggre.getOutTradeNo()).ifPresent(orderDetail::setOutTradeNo);
                ofNullable(orderAggre.getActualAmount()).ifPresent(orderDetail::setActualAmount);
                ofNullable(orderAggre.getCreateTime()).ifPresent(orderDetail::setCreateTime);

                if (CollectionUtils.isEmpty(orderAggre.getPayModeList())) {
                    ofNullable(orderAggre.getPayMode()).map(mode -> {
                        //真正支付的的才去判断是否是刷脸
                        orderDetail.setPayMode(orderAggre.getPayMode());
                        orderDetail.setPayModeList(Sets.newHashSet(orderAggre.getPayMode()));
                        Optional.of(mode).map(String::valueOf)
                                .filter(ability -> ability.length() >= 3)
                                .map(ability -> {
                                    if (Integer.parseInt(ability.substring(1, 3)) == PaymentModeEnum.BasicPayAbility.FACE.getCode()) {
                                        orderDetail.setFacePay(true);
                                    }
                                    ofNullable(ability.substring(0, 1)).map(Integer::parseInt).ifPresent(orderDetail::setPayClient);
                                    return ability;
                                });
                        return mode;
                    });
                } else {
                    //保存多个paymode,暂时不处理刷脸判断
                    orderDetail.setPayModeList(orderAggre.getPayModeList());
                }

                ofNullable(orderAggre.getStoreId()).map(storeId -> {
                    MerchantGroupDTO merchantGroup = ofNullable(merchantGroupService.getGroup(storeId))
                            .map(BaseResponse::getData).orElse(null);
                    String storeName = ofNullable(merchantGroup).map(
                            MerchantGroupDTO::getName).orElseGet(String::new);
                    orderDetail.setStoreName(storeName);
                    return storeId;
                });

                ofNullable(orderAggre.getOrderAmount()).ifPresent(orderDetail::setTotalAmount);
                ofNullable(orderAggre.getActualAmount()).ifPresent(orderDetail::setActualAmount);
                orderInfoDTO.setOrderDetail(orderDetail);

                ofNullable(orderAggre.getOrders()).ifPresent(orderInfoDTO::setSubOrders);
                ofNullable(orderAggre.getStatus()).ifPresent(orderDetail::setOrderStatus);

                Long userId = ofNullable(orderInfoDTO.getMemberDetail()).map(OrderInfoDTO.MemberDetail::getUserId).orElse(null); // 拼接用户信息

                ofNullable(userService.get(userId).getData())
                        .map(user -> {
                            ofNullable(user.getTelephone()).ifPresent(orderInfoDTO.getMemberDetail()::setPhoneNo);
                            ofNullable(user.getHeadImg()).ifPresent(orderInfoDTO.getMemberDetail()::setHeadImg);
                            ofNullable(user.getName()).ifPresent(orderInfoDTO.getMemberDetail()::setNickName);
                            return user;
                        });

                //处理子单
                if (orderInfoDTO.getSubOrders() == null) {
                    OrderItemAggregationDTO orderItemAggregationDTO = CommonConverterTools
                            .convert(OrderItemAggregationDTO.class, orderAggre);
                    ofNullable(orderAggre.getGoodsDetailVO())
                            .map(goodsDetailVO -> CommonConverterTools.convert(OrderDTO.GoodsDetail.class, goodsDetailVO))
                            .ifPresent(orderItemAggregationDTO::setGoods);
                    EquipmentDetail equipmentDetailInfo = ofNullable(orderAggre.getEquipment())
                            .map(equipmentDetail -> CommonConverterTools.convert(EquipmentDetail.class, equipmentDetail))
                            .orElse(orderAggre.getEquipment());
                    orderItemAggregationDTO.setEquipment(equipmentDetailInfo);
                    orderInfoDTO.setSubOrders(Lists.newArrayList(orderItemAggregationDTO));

                }

                ofNullable(orderInfoDTO.getSubOrders()).orElse(Lists.newArrayList()).parallelStream()
                        .forEach(subOrder -> {
                            if (subOrder.getGoods() != null) {
                                CommodityDetailResDTO commodityDetail = ofNullable(commoditySkuService.get(subOrder.getGoods().getGoodsId()))
                                        .map(BaseResponse::getData).orElse(null); //获取商品信息
                                ofNullable(commodityDetail).map(CommodityDetailResDTO::getIcon).ifPresent(subOrder.getGoods()::setGoodsIcon);
                            }

                            ofNullable(subOrder.getEquipment())
                                    .ifPresent(equipmentDetail -> {
                                        EquipmentTypeDTO equipmentType = ofNullable(equipmentTypeService.getByKey(equipmentDetail.getTypeId()))
                                                .map(BaseResponse::getData).orElse(new EquipmentTypeDTO());
                                        equipmentDetail.setEquipmentTypeValue(equipmentType.getValue());//获取设备类型value
                                        subOrder.setEquipment(equipmentDetail);
                                    });
                        });
                //充电桩保险加购订单查询
                if (orderInfoDTO.getSubOrders() != null && orderInfoDTO.getSubOrders().size() >0
                        && orderInfoDTO.getSubOrders().get(0).getEquipment() != null
                        && "CDZ".equals(orderInfoDTO.getSubOrders().get(0).getEquipment().getEquipmentTypeValue())){
                    LyyInsuranceOrderDTO insuranceOrderDTO = advertService.findInsuranceOrderInfo(orderAggre.getOutTradeNo()).getData();
                    orderInfoDTO.setLyyInsuranceOrderDTO(insuranceOrderDTO);
                }
                return orderInfoDTO;
            }).collect(Collectors.toList());
            Pagination<OrderInfoDTO> pagination = new Pagination<>();
            pagination.setItems(orderInfoList);
            pagination.setPage(orderQueryRequest.getPageIndex());
            pagination.setPageSize(orderQueryRequest.getPageSize());
            pagination.setTotal(page.getTotalSize());
            pagination.setMaxPage(page.getTotalPage());

            return pagination;
        }).orElseGet(Pagination::new);

    }
    @Override
    public OrderInfoDetailDTO getOrderDetail(String outTradeNo) {
            return getOrderDetailV2(outTradeNo);
    }


    private OrderInfoDetailDTO getOrderDetailV2(String outTradeNo) {
        if (log.isDebugEnabled()) {
            log.debug("获取订单信息: {}", outTradeNo);
        }
        OrderDetailQueryRequest query = new OrderDetailQueryRequest();
        query.setOutTradeNo(outTradeNo);
        query.setQuerySubOrder(true);
        query.setQueryGoods(true);

        OrderQueryResponse queryInfoDTO = ofNullable(orderClient.detail(query)).map(RemoteResponseUtils::getData).orElseThrow(
                () -> new OrderException(OrderExceptionEnums.ORDER_NOT_EXIST_ERROR.getExCode(),
                        OrderExceptionEnums.ORDER_NOT_EXIST_ERROR.getMsg()));
        Integer payMode = ofNullable(paymentServiceInterface.getPaymentRecord(outTradeNo)).map(RemoteResponseUtils::getData)
                .map(PaymentRecordDTO::getPayMode)
                .orElseGet(() -> payModeSup(queryInfoDTO));

        if (Objects.nonNull(queryInfoDTO.getRefundMark())) {
            if (OrderRefundMarkEnum.FULL_REFUND == OrderRefundMarkEnum.findByStatus(queryInfoDTO.getRefundMark())) {
                queryInfoDTO.setStatus(OrderStatesEnum.REFUNDED.getStatus());
            } else if (OrderRefundMarkEnum.PART_REFUND == OrderRefundMarkEnum.findByStatus(queryInfoDTO.getRefundMark())) {
                queryInfoDTO.setStatus(OrderStatesEnum.PART_REFUND.getStatus());
            }
        }

        OrderInfoDetailDTO orderInfoDetailDTO = CommonConverterTools.convert(OrderInfoDetailDTO.class, queryInfoDTO);
        OrderInfoDTO.OrderDetail orderDetail = new OrderInfoDTO.OrderDetail();

        ofNullable(queryInfoDTO.getSubOrderList()).map(orders -> {

            //设备信息
            orders.stream().findFirst().map(OrderQueryResponse::getEquipmentDetail)
                    .ifPresent(equipmentDetail -> {
                        EquipmentTypeDTO equipmentType = ofNullable(equipmentTypeService.getByKey(equipmentDetail.getTypeId()))
                                .map(BaseResponse::getData).orElse(null);
                        equipmentDetail.setEquipmentTypeValue(equipmentType.getValue());//获取设备类型value
                        orderInfoDetailDTO.setEquipmentDetail(equipmentDetail);

                    });
            orders.stream().findFirst().map(OrderQueryResponse::getTransType).ifPresent(orderInfoDetailDTO::setTransType);

            orders.stream().findFirst().map(OrderQueryResponse::getStoreId).ifPresent(storeId -> {
                MerchantGroupDTO merchantGroup = ofNullable(merchantGroupService.getGroup(storeId))
                        .map(BaseResponse::getData).orElse(null);
                String storeName = ofNullable(merchantGroup).map(
                        MerchantGroupDTO::getName).orElseGet(String::new);
                orderDetail.setStoreName(storeName);
            });

            orders.stream().findFirst().map(OrderQueryResponse::getBusinessType).ifPresent(orderInfoDetailDTO::setBusinessType);
            return orders;
        });

        // 原来外层位置放回一个storeName给前端可以显示
        ofNullable(queryInfoDTO.getStoreId()).ifPresent(storeId -> {
            MerchantGroupDTO merchantGroup = ofNullable(merchantGroupService.getGroup(storeId))
                    .map(BaseResponse::getData).orElse(null);
            String storeName = ofNullable(merchantGroup).map(
                    MerchantGroupDTO::getName).orElseGet(String::new);
            orderDetail.setStoreName(storeName);
        });

        List<OrderGoodsDetail> goodDetails = queryInfoDTO.getOrderGoodsList().stream()
                .map(order -> {
                    OrderGoodsDetail goodsDetail = new OrderGoodsDetail();
                    ofNullable(order).ifPresent(
                            goods -> {

                                ofNullable(goods.getGoodsId()).ifPresent(goodsDetail::setGoodsId);
                                ofNullable(goods.getQuantity()).map(BigDecimal::new).ifPresent(goodsDetail::setBuyQuantity);
                                goodsDetail.setPayAmount(order.getTotalAmount());
                                goodsDetail.setActualPayAmount(order.getActualAmount());
                                ofNullable(goods.getGoodsName()).ifPresent(goodsDetail::setGoodsName);
                                ofNullable(goods.getGoodsShortName()).ifPresent(goodsDetail::setGoodsShortName);

                                ofNullable(goods.getGoodsId()).ifPresent(goodsId -> {//获取商品信息
                                    try {
                                        CommodityDetailResDTO commodityDetail = ofNullable(commoditySkuService.get(goodsId))
                                                .map(BaseResponse::getData).orElse(null);
                                        ofNullable(commodityDetail).map(CommodityDetailResDTO::getIcon)
                                                .ifPresent(goodsDetail::setGoodIcon);
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                        log.warn("商品icon获取异常,e={}", e);
                                    }
                                });
                            }
                    );

                    return goodsDetail;
                })
                .collect(Collectors.toList());
        orderInfoDetailDTO.setGoodsDetails(goodDetails);
        ofNullable(queryInfoDTO.getUserInfo())
                .map(OrderDTO.UserInfo::getUserId)
                .map(userId -> {

                    OrderInfoDTO.MemberDetail memberDetail = new OrderInfoDTO.MemberDetail();
                    memberDetail.setUserId(userId);
                    orderInfoDetailDTO.setMemberDetail(memberDetail);

                    //获取会员信息
                    ofNullable(userService.get(userId).getData())
                            .map(user -> {
                                ofNullable(user.getTelephone()).ifPresent(orderInfoDetailDTO.getMemberDetail()::setPhoneNo);
                                ofNullable(user.getHeadImg()).ifPresent(orderInfoDetailDTO.getMemberDetail()::setHeadImg);
                                ofNullable(user.getName()).ifPresent(orderInfoDetailDTO.getMemberDetail()::setNickName);
                                return user;
                            });

                    return userId;
                });

        //订单详情
        ofNullable(queryInfoDTO.getOutTradeNo()).ifPresent(orderDetail::setOutTradeNo);
        ofNullable(queryInfoDTO.getActualAmount()).ifPresent(orderDetail::setActualAmount);
        ofNullable(queryInfoDTO.getCreateTime()).ifPresent(orderDetail::setCreateTime);
        ofNullable(payMode).ifPresent(orderDetail::setPayMode);
        ofNullable(payMode).ifPresent(orderInfoDetailDTO::setPayMode);   // 原来外层位置放回一个payMode给前端可以显示
        ofNullable(queryInfoDTO.getOrderAmount()).ifPresent(orderDetail::setTotalAmount);
        ofNullable(queryInfoDTO.getPayOrderNo()).ifPresent(orderDetail::setOrderNo);

        orderDetail.setOrderStatus(queryInfoDTO.getStatus());

        orderInfoDetailDTO.setOrderDetail(orderDetail);

        //退款的话需要获取退款的相关信息
        if (orderInfoDetailDTO.getOrderDetail().getOrderStatus() == OrderEnum.OrderStatesEnum.REFUNDED.getStatus()
                || orderInfoDetailDTO.getOrderDetail().getOrderStatus() == OrderEnum.OrderStatesEnum.REFUNDING.getStatus()) {

            ofNullable(orderService.refundQueryByOrderNo(outTradeNo))
                    .map(BaseResponse::getData)
                    .map(RefundQueryInfoDTO::getRefundOrders)
                    .map(refundOrders -> {
                        refundOrders.forEach(refund -> {
                            orderInfoDetailDTO.getGoodsDetails().forEach(goods -> {

                                if (goods.getGoodsId().equals(refund.getGoods().getGoodsId())) {
                                    goods.setRefundQuantity(refund.getGoods().getQuantity());
                                }
                            });
                        });
                        return refundOrders;
                    });
        }
        //充电桩保险加购订单查询
        if (orderInfoDetailDTO.getEquipmentDetail() != null && "CDZ".equals(
                orderInfoDetailDTO.getEquipmentDetail().getEquipmentTypeValue())) {
            log.debug("进来了明细来了。。。。, outTradeNo:{}", outTradeNo);
            LyyInsuranceOrderDTO insuranceOrderDTO = advertService.findInsuranceOrderInfo(outTradeNo).getData();
            orderInfoDetailDTO.setLyyInsuranceOrderDTO(insuranceOrderDTO);
            log.debug("insuranceOrderDTO:{}", insuranceOrderDTO == null ? "insuranceOrderDTO空的" : "insuranceOrderDTO非空");
        }
        return orderInfoDetailDTO;
    }

    private Integer payModeSup(OrderQueryResponse queryResponse) {
        if (queryResponse.getStatus()== OrderEnum.OrderStatesEnum.REFUNDING.getStatus() && StringUtils.isEmpty(queryResponse.getPayOrderNo())) {
            return ClientTypeEnum.UNKNOW.getNumber();
        }
        return ClientTypeEnum.BALANCED.getNumber();
    }

}
