package cn.lyy.merchant.service.order;

import cn.lyy.base.dto.Pagination;
import cn.lyy.merchant.dto.request.OrderQueryRequest;
import cn.lyy.merchant.dto.response.OrderInfoDTO;
import cn.lyy.merchant.dto.response.OrderInfoDetailDTO;

/**
 * @createTime 2020-12-02
 * @auther peterguo
 * @Description
 */
public interface OrderService {


    /**
     * 获取订单列表
     * @param orderQueryRequest
     * @return
     */
    Pagination<OrderInfoDTO> getOrderList(OrderQueryRequest orderQueryRequest);

    /**
     * 获取订单详情
     * @param outTradeNo
     * @return
     */
    OrderInfoDetailDTO getOrderDetail(String outTradeNo);
}
