package cn.lyy.merchant.service.order;

import cn.lyy.merchant.dto.request.OrderRefundRequest;
import cn.lyy.merchant.dto.response.RefundInfoDetailDTO;

/**
 * @createTime 2020-12-02
 * @auther peterguo
 * @Description
 */
public interface RefundService {


    /**
     * 获取退款详情
     * @param refundNo
     * @return
     */
     RefundInfoDetailDTO getRefundDetail(String refundNo);

    /**
     * 退款接口
     * @param request
     */
    void refund(OrderRefundRequest request);
}
