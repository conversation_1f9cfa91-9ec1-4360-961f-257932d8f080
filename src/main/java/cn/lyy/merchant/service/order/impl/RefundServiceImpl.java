package cn.lyy.merchant.service.order.impl;

import static java.util.Optional.ofNullable;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.utils.converter.CommonConverterTools;
import cn.lyy.merchant.constants.OrderExceptionEnums;
import cn.lyy.merchant.dto.request.OrderRefundRequest;
import cn.lyy.merchant.dto.response.RefundInfoDetailDTO;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.exception.OrderException;
import cn.lyy.merchant.service.order.RefundService;
import cn.lyy.merchant.utils.PaymentRefactorUtils;
import cn.lyy.merchant.utils.ResponseCheckUtil;
import cn.lyy.open.order.api.OrderServiceInterface;
import cn.lyy.open.order.api.v2.OrderClient;
import cn.lyy.open.order.api.v2.RefundOrderClient;
import cn.lyy.open.order.constans.OrderEnum;
import cn.lyy.open.order.constans.ServiceSourceEnum;
import cn.lyy.open.order.dto.request.OrderDTO;
import cn.lyy.open.order.dto.request.OrderDTO.CommonExtend;
import cn.lyy.open.order.dto.request.OrderRefundDTO.RefundDetail;
import cn.lyy.open.order.dto.request.v2.OrderRefundRequest.RefundOrderGoodsRequest;
import cn.lyy.open.order.dto.request.v2.query.OrderDetailQueryRequest;
import cn.lyy.open.order.dto.request.v2.query.RefundOrderQueryRequest;
import cn.lyy.open.order.dto.response.v2.ChannelRefundDetail;
import cn.lyy.open.order.dto.response.v2.OrderQueryResponse;
import cn.lyy.open.order.dto.response.v2.OrderRefundResponse;
import cn.lyy.open.order.dto.response.v2.RefundOrderQueryResponse;
import cn.lyy.open.order.dto.response.v2.StoredRefundDetail;
import cn.lyy.open.payment.constant.PaymentEnum;
import com.google.common.collect.Lists;
import com.lyy.lock.LockInfo;
import com.lyy.lock.redis.RedisLock;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

/**
 * @createTime 2020-12-02
 * @auther peterguo
 * @Description
 */
@Service
@Slf4j
public class RefundServiceImpl implements RefundService {

    /**
     * 退款锁
     */
    private static final String REFUND_LOCK_KEY = "washer:refund:lock:";

    @Autowired
    private OrderServiceInterface orderService;

    @Autowired
    private RefundOrderClient refundOrderClient;

    @Autowired
    private OrderClient orderClient;

    @Autowired
    @Lazy
    private RedisLock redisLock;

    @Override
    public RefundInfoDetailDTO getRefundDetail(String refundNo) {
        RefundOrderQueryResponse refundOrderQueryResponse = ofNullable(refundNo)
                .map(found -> {
                    RefundOrderQueryRequest refundOrderQueryRequest = new RefundOrderQueryRequest();
                    refundOrderQueryRequest.setRefundNo(found);
                    refundOrderQueryRequest.setQueryGoods(true);
                    return refundOrderClient.refundQuery(refundOrderQueryRequest);
                })
                .map(ResponseCheckUtil::getData)
                .orElseThrow(() -> new OrderException(OrderExceptionEnums.REFUND_NOT_EXIST_ERROR.getExCode(),
                        OrderExceptionEnums.REFUND_AMOUNT_ILLEAGL_ERROR.getMsg()))
                .stream().findFirst().get();
        RefundInfoDetailDTO refundInfoDetail = CommonConverterTools.convert(RefundInfoDetailDTO.class, refundOrderQueryResponse);
        List<RefundDetail> refundDetails = refundOrderQueryResponse.getStoredRefundDetailList().stream().map(info -> {
            // convert detail
            return CommonConverterTools.convert(RefundDetail.class, info);
        }).collect(Collectors.toList());
        refundInfoDetail.setRefundDetail(refundDetails);

        return refundInfoDetail;

    }

    @Override
    public void refund(OrderRefundRequest request) {
        String outTradeNo = request.getOutTradeNo();
        // 获取锁
        final LockInfo lockInfo = redisLock.lock(REFUND_LOCK_KEY.concat(outTradeNo), "Y", 10, 0);
        if (lockInfo == null) {
            throw new BusinessException(outTradeNo + "此订单正在退款中...");
        }

        OrderQueryResponse orderQueryResponse = Optional.of(outTradeNo)
                .map(param -> {
                    OrderDetailQueryRequest orderDetailQueryRequest = new OrderDetailQueryRequest();
                    orderDetailQueryRequest.setOutTradeNo(param);
                    orderDetailQueryRequest.setQueryGoods(true);
                    orderDetailQueryRequest.setQuerySubOrder(false);
                    return orderClient.detail(orderDetailQueryRequest);
                })
                .map(ResponseCheckUtil::getData)
                .orElseThrow(() -> new BusinessException(outTradeNo + "订单不存在"));
        Integer status = orderQueryResponse.getStatus();

        if (!Objects.equals(OrderEnum.OrderStatesEnum.PAID.getStatus(), status)) {
            throw new BusinessException(outTradeNo + "此订单不可退款");
        }
        cn.lyy.open.order.dto.request.v2.OrderRefundRequest refundRequest = assembleRefundOrder(orderQueryResponse);
        log.info("V2退款, 请求参数:{}", refundRequest);
        BaseResponse<OrderRefundResponse> refund = refundOrderClient.refund(refundRequest);
        ResponseCheckUtil.getData(refund);
        log.info("V2退款完成, 响应参数:{}", refund);

    }

    private cn.lyy.open.order.dto.request.v2.OrderRefundRequest assembleRefundOrder(OrderQueryResponse order) {
        cn.lyy.open.order.dto.request.v2.OrderRefundRequest orderRefundRequest = new cn.lyy.open.order.dto.request.v2.OrderRefundRequest();
        orderRefundRequest.setDistributorId(order.getDistributorId());
        orderRefundRequest.setOutTradeNo(order.getOutTradeNo());
        orderRefundRequest.setRemark(PaymentEnum.RefundTypeEnum.REFUND_TYPE_ORDER.getDesc());
        orderRefundRequest.setRefundType(PaymentEnum.RefundTypeEnum.REFUND_TYPE_ORDER.getCode());
        orderRefundRequest.setSource(ServiceSourceEnum.MERCHANT.getCode());

        orderRefundRequest.setStoredRefundDetailList(generateStoredRefund(order.getPayDetailList()));

        List<ChannelRefundDetail> details = Lists.newArrayList();
        ChannelRefundDetail crd = new ChannelRefundDetail();
        crd.setPayAmount(order.getActualAmount().setScale(2, RoundingMode.HALF_UP));
        details.add(crd);
        orderRefundRequest.setChannelRefundDetailList(details);

        List<RefundOrderGoodsRequest> refundGoodsList = ofNullable(order.getOrderGoodsList())
                .orElse(Lists.newArrayList())
                .stream()
                .map(v -> {
                    RefundOrderGoodsRequest refundGoods = new RefundOrderGoodsRequest();
                    refundGoods.setOrderGoodsId(v.getId());
                    refundGoods.setRefundQuantity(v.getRefundQuantity());
                    refundGoods.setRefundAmount(v.getActualAmount());
                    return refundGoods;
                })
                .collect(Collectors.toList());
        orderRefundRequest.setRefundOrderGoodsList(refundGoodsList);
        return orderRefundRequest;
    }

    public static List<StoredRefundDetail> generateStoredRefund(List<OrderDTO.PayDetail> details) {
        return ofNullable(details)
                .orElse(Lists.newArrayList())
                .stream()
                .map(rd -> {
                    StoredRefundDetail storedRefundDetail = new StoredRefundDetail();
                    storedRefundDetail.setPayAmount(rd.getPayAmount());
                    storedRefundDetail.setPayType(rd.getPayType());
                    storedRefundDetail.setTypeName(rd.getTypeName());
                    storedRefundDetail.setStoreValueId(rd.getStoreValueId());
                    storedRefundDetail.setUseNum(rd.getUseNum());
                    storedRefundDetail.setSource(rd.getSource());
                    storedRefundDetail.setSourceName(rd.getSourceName());
                    storedRefundDetail.setChannelRefundStore(false);
                    return storedRefundDetail;
                })
                .collect(Collectors.toList());
    }
}
