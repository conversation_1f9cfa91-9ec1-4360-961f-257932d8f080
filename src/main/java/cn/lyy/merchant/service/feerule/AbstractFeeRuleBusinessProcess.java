package cn.lyy.merchant.service.feerule;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.equipment.dto.equipment.EquipmentDTO;
import cn.lyy.merchant.constants.BusinessTypeEnums;
import cn.lyy.merchant.dto.request.EquipmentRegisterDTO;
import cn.lyy.merchant.service.business.AbstractBusinessProcess;
import com.lyy.commodity.rpc.constants.CategoryEnum;
import com.lyy.commodity.rpc.constants.ClassifyCodeEnum;
import com.lyy.commodity.rpc.dto.request.BatchBindEquipmentDTO;
import com.lyy.commodity.rpc.dto.request.SimpleEquipment;
import com.lyy.commodity.rpc.feign.ICommodityRelatedService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>Title:saas2</p>
 * <p>Desc: 计费规则抽象类</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020/4/9
 */
@Slf4j
public abstract class AbstractFeeRuleBusinessProcess extends AbstractBusinessProcess {

    @Autowired
    private ICommodityRelatedService commodityRelatedService;

    @Override
    public String getBusinessType() {
        return BusinessTypeEnums.FEERULE.getEvent();
    }

    @Override
    public AbstractFeeRuleBusinessProcess getCommon() {
        return (AbstractFeeRuleBusinessProcess)super.getCommon();
    }


    /**
     * 保存或修改计费规则
     * @param equipments
     * @param param
     * @return
     */
    public abstract void saveOrUpdateFeeRule(List<EquipmentDTO> equipments, EquipmentRegisterDTO param);

    /**
     * 保存或修改充值优惠套餐
     * @param equipments
     * @param param
     */
    public void saveOrUpdateRecharge(List<EquipmentDTO> equipments, EquipmentRegisterDTO param){

        BatchBindEquipmentDTO batchBindEquipmentDTO = new BatchBindEquipmentDTO();
        batchBindEquipmentDTO.setDistributorId(param.getDistributorId());
        batchBindEquipmentDTO.setAdUserId(param.getUserId());
        batchBindEquipmentDTO.setCategoryCode(CategoryEnum.SET_MEAL.getCode());
        batchBindEquipmentDTO.setClassifyCode(ClassifyCodeEnum.RECHARGE.getCode());
        batchBindEquipmentDTO.setStoreId(param.getGroupId());
        List<SimpleEquipment> equipmentList = equipments.stream().map(equipmentDTO -> {
            SimpleEquipment simpleEquipment = new SimpleEquipment();
            simpleEquipment.setEquipmentId(equipmentDTO.getEquipmentId());
            simpleEquipment.setStoreId(param.getGroupId());
            simpleEquipment.setEquipmentTypeId(equipmentDTO.getEquipmentTypeId());
            batchBindEquipmentDTO.setEquipmentTypeId(equipmentDTO.getEquipmentTypeId());
            return simpleEquipment;
        }).collect(Collectors.toList());
        batchBindEquipmentDTO.setEquipmentList(equipmentList);
        BaseResponse bindResponse = commodityRelatedService.batchBindEquipment(batchBindEquipmentDTO);
        if (bindResponse.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            log.error("绑定充值套餐结果 -> {}", bindResponse);
        }
    };

    /**
     * 保存或修改计费规则之后执行的逻辑
     * @param equipments
     * @param param
     * @return
     */
    public abstract void afterSave(List<EquipmentDTO> equipments, EquipmentRegisterDTO param);

}
