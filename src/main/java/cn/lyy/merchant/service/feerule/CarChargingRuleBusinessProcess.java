package cn.lyy.merchant.service.feerule;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.base.utils.GsonUtils;
import cn.lyy.equipment.dto.equipment.EquipmentDTO;
import cn.lyy.equipment.dto.operation.OperationParam;
import cn.lyy.equipment.service.EquipmentService;
import cn.lyy.merchant.constants.BusinessExceptionEnums;
import cn.lyy.merchant.dto.request.EquipmentRegisterDTO;
import cn.lyy.merchant.dto.request.FeeCommodityDTO;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.microservice.CommoditySkuService;
import com.alibaba.fastjson.JSON;
import com.lyy.commodity.rpc.constants.ChannelEnum;
import com.lyy.commodity.rpc.constants.ClassifyCodeEnum;
import com.lyy.commodity.rpc.constants.CategoryEnum;
import com.lyy.commodity.rpc.dto.request.*;
import com.lyy.commodity.rpc.feign.ICommodityRelatedService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.Optional.ofNullable;

/**
 * 类描述：
 * <p>
 *
 * <AUTHOR>
 * @since 2021/2/22 15:20
 */
@Slf4j
@Component("QCCDZ_FeeRule")
public class CarChargingRuleBusinessProcess extends AbstractFeeRuleBusinessProcess{

    @Autowired
    private ICommodityRelatedService commodityRelatedService;

    @Resource
    private CommoditySkuService commoditySkuService;

    @Resource
    private EquipmentService equipmentService;

    @Override
    public void saveOrUpdateFeeRule(List<EquipmentDTO> equipments, EquipmentRegisterDTO param) {
        RelateCommodityDTO relateCommodity = new RelateCommodityDTO();
        relateCommodity.setDistributor(param.getDistributorId());
        relateCommodity.setAdUser(param.getUserId());
        relateCommodity.setEquipments(new ArrayList<>(equipments.size()));
        // 设置关联设备
        equipments.forEach(equipment -> {
            RelateEquipmentDTO relateEquipment = new RelateEquipmentDTO();
            relateEquipment.setEquipment(equipment.getEquipmentId());
            relateEquipment.setEquipmentType(equipment.getEquipmentTypeId());
            relateEquipment.setStore(equipment.getEquipmentGroupId());
            relateCommodity.getEquipments().add(relateEquipment);
        });
        // 设置商品
        relateCommodity.setCommodities(new ArrayList<>(param.getFeeRule().size()));

        param.getFeeRule().forEach(rule -> {
            List<Long> composeIdList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(rule.getComposes())) {
                composeIdList = saveCompose(rule, param.getDistributorId(), param.getUserId());
            }
            // 定价
            CommodityFixPriceDTO fixPrice = new CommodityFixPriceDTO();
            fixPrice.setPrice(rule.getPrice());
            List<CommodityFixPriceValueDTO> valueList = new ArrayList<>();
            rule.getPriceValueList().forEach(fixValue -> {
                CommodityFixPriceValueDTO item = new CommodityFixPriceValueDTO();
                item.setUnit(fixValue.getUnit());
                item.setValue(fixValue.getValue());
                valueList.add(item);
            });
            fixPrice.setFixPriceValueList(valueList);
            fixPrice.setRangeCategory(rule.getRangeCategory());
            fixPrice.setEnd(rule.getRangeEnd());
            fixPrice.setStart(rule.getRangeStart());
            // 商品
            CommodityDTO commodity = new CommodityDTO();
            commodity.setCategoryCode(ofNullable(rule.getCategoryCode()).orElse(CategoryEnum.DEVICE_SERVICE.getCode()));
            commodity.setChannel(ChannelEnum.H5.getId());
            commodity.setName(ofNullable(rule.getName()).orElse("商品"));
            commodity.setClassifyCode(rule.getClassifyCode());
            commodity.setFixPrice(fixPrice);
            commodity.setComposes(composeIdList);
            relateCommodity.getCommodities().add(commodity);
        });
        log.debug("[注册保存计费规则] 参数: -> {} ", relateCommodity);
        BaseResponse<Boolean> resp = commodityRelatedService.registerInit(relateCommodity);
        if (resp.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            log.error("[注册保存计费规则] 调用商品中心保存商品结果 -> {}", resp);
            throw new BusinessException(BusinessExceptionEnums.FEE_RULE_SAVE_ERROR);
        }
    }

    @Override
    public void saveOrUpdateRecharge(List<EquipmentDTO> equipments, EquipmentRegisterDTO param) {
        super.saveOrUpdateRecharge(equipments,param);
    }

    @Override
    public void afterSave(List<EquipmentDTO> equipments, EquipmentRegisterDTO param) {
        Map<List<String>, BigDecimal> timeMap = new HashMap<>();
        List<BigDecimal> levelList = param.getFeeRule().stream().map(FeeCommodityDTO::getPrice)
                .sorted(Comparator.reverseOrder()).collect(Collectors.toList());
        if (levelList.size() > 4 || levelList.size() == 0) {
            throw new BusinessException("汽车充电桩计费规则必须为1~4个");
        }
        if(levelList.size() != 4) {
            int size = levelList.size();
            for (int time = 0; time < 4 - size; time ++) {
                levelList.add(levelList.get(0));
            }
            Collections.sort(levelList);
            Collections.reverse(levelList);
        }
        log.debug("尖峰平谷->{}", levelList);
        param.getFeeRule().forEach(rule -> {
            timeMap.put(Arrays.asList(rule.getRangeStart(), rule.getRangeEnd()), rule.getPrice());
        });
        int rangeNumber = 30;
        Map<String, Object> result = new HashMap<>();
        timeMap.forEach((k, v) -> {
            List<Integer> startList = Arrays.stream(k.get(0).split(":"))
                    .map(Integer::new).collect(Collectors.toList());
            int start = (startList.get(0) * 60 / rangeNumber + (startList.get(1) / rangeNumber)) + 1;
            List<Integer> endList = Arrays.stream(k.get(1).split(":"))
                    .map(Integer::new).collect(Collectors.toList());
            int end = (endList.get(0) * 60 / rangeNumber) + (endList.get(1) / rangeNumber);

            for (int index = start; index <= end; index ++) {
                result.put("timeFee_30_" + index, levelList.indexOf(v));
            }
        });
        for (int x = 1; x <= (24 * 60 / rangeNumber); x ++) {
            result.putIfAbsent("timeFee_30_" + x, 0);
        }
        result.put("tipFee", levelList.get(0).setScale(2, RoundingMode.HALF_UP).floatValue());
        result.put("peakFee", levelList.get(1).setScale(2, RoundingMode.HALF_UP).floatValue());
        result.put("flatFee", levelList.get(2).setScale(2, RoundingMode.HALF_UP).floatValue());
        result.put("valleyFee", levelList.get(3).setScale(2, RoundingMode.HALF_UP).floatValue());

        equipments.parallelStream().forEach(eqmt -> {
            OperationParam operationParam = new OperationParam();
            operationParam.setUniqueCode(eqmt.getUniqueCode());
            operationParam.setSync(false);
            operationParam.setIdentity("CSYS_SAAS_CONTROL_SETTING_FEE_MODEL");
            operationParam.setParam(GsonUtils.serialize(result));
            equipmentService.operation(operationParam);
        });

    }

    private List<Long> saveCompose(FeeCommodityDTO rule, Long distributorId, Long adUserId) {
        List<Long> composeIdList = new ArrayList<>();
        rule.getComposes().forEach(compose -> {
            // 新增计费规则
            SkuCommodityDTO skuCommodityDTO = new SkuCommodityDTO();
            skuCommodityDTO.setDistributor(distributorId);
            skuCommodityDTO.setAdUser(adUserId);

            // 定价
            CommodityFixPriceDTO fixPrice = new CommodityFixPriceDTO();
            fixPrice.setPrice(compose.getPrice());

            List<CommodityFixPriceValueDTO> fixPriceValueDTOList = new ArrayList<>();
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(compose.getPriceValueList())) {
                compose.getPriceValueList().forEach(fixPriceValueDTO -> {
                    CommodityFixPriceValueDTO commodityFixPriceValueDTO = new CommodityFixPriceValueDTO();
                    commodityFixPriceValueDTO.setValue(fixPriceValueDTO.getValue());
                    commodityFixPriceValueDTO.setUnit(fixPriceValueDTO.getUnit());
                    fixPriceValueDTOList.add(commodityFixPriceValueDTO);
                });
            }
            fixPrice.setFixPriceValueList(fixPriceValueDTOList);

            // 商品
            CommodityDTO commodity = new CommodityDTO();
            commodity.setCategoryCode(compose.getCategoryCode());
            commodity.setChannel(ChannelEnum.H5.getId());

            commodity.setName(ofNullable(compose.getName()).orElse("商品"));
            commodity.setClassifyCode(compose.getClassifyCode());
            commodity.setFixPrice(fixPrice);
            skuCommodityDTO.setCommodities(commodity);

            log.debug("[新增计费规则] 参数: -> {} ", JSON.toJSONString(skuCommodityDTO));
            BaseResponse<Long> resp = commoditySkuService.addCommodity(skuCommodityDTO);
            composeIdList.add(resp.getData());
        });
        return composeIdList;
    }
}
