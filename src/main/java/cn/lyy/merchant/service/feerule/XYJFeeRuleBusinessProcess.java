package cn.lyy.merchant.service.feerule;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.equipment.dto.equipment.EquipmentDTO;
import cn.lyy.merchant.constants.BusinessExceptionEnums;
import cn.lyy.merchant.dto.request.EquipmentRegisterDTO;
import cn.lyy.merchant.exception.BusinessException;
import com.lyy.commodity.rpc.constants.CategoryEnum;
import com.lyy.commodity.rpc.constants.ChannelEnum;
import com.lyy.commodity.rpc.constants.ClassifyCodeEnum;
import com.lyy.commodity.rpc.dto.request.*;
import com.lyy.commodity.rpc.feign.ICommodityRelatedService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component("XYJ_FeeRule")
public class XYJFeeRuleBusinessProcess extends  AbstractFeeRuleBusinessProcess {

    @Autowired
    private ICommodityRelatedService commodityRelatedService;

    @Override
    public void saveOrUpdateFeeRule(List<EquipmentDTO> equipments, EquipmentRegisterDTO param) {
        RelateCommodityDTO relateCommodity = new RelateCommodityDTO();
        relateCommodity.setDistributor(param.getDistributorId());
        relateCommodity.setAdUser(param.getUserId());
        relateCommodity.setEquipments(new ArrayList<>(equipments.size()));
        // 设置关联设备
        equipments.forEach(equipment -> {
            RelateEquipmentDTO relateEquipment = new RelateEquipmentDTO();
            relateEquipment.setEquipment(equipment.getEquipmentId());
            relateEquipment.setEquipmentType(equipment.getEquipmentTypeId());
            relateEquipment.setStore(equipment.getEquipmentGroupId());
            //relateEquipment.setIsUse(equipment.getIs);


            relateCommodity.getEquipments().add(relateEquipment);
        });
        // 设置商品
        relateCommodity.setCommodities(new ArrayList<>(param.getFeeRule().size()));

        param.getFeeRule().forEach(rule -> {
            // 定价
            CommodityFixPriceDTO fixPrice = new CommodityFixPriceDTO();
            fixPrice.setPrice(rule.getPrice());
            List<CommodityFixPriceValueDTO> valueList = new ArrayList<>();
            rule.getPriceValueList().forEach(fixValue -> {
                CommodityFixPriceValueDTO item = new CommodityFixPriceValueDTO();
                item.setUnit(fixValue.getUnit());
                item.setValue(fixValue.getValue());
                valueList.add(item);
            });
            fixPrice.setFixPriceValueList(valueList);
            // 商品
            CommodityDTO commodity = new CommodityDTO();
            commodity.setCategoryCode(CategoryEnum.DEVICE_SERVICE.getCode());
            commodity.setChannel(ChannelEnum.H5.getId());
            commodity.setName(rule.getName());
            commodity.setClassifyCode(rule.getClassifyCode());
            commodity.setFixPrice(fixPrice);
            // 批量支持是否启用参数
            commodity.setIsUse(rule.getIsUse());

            relateCommodity.getCommodities().add(commodity);
        });
        log.debug("[注册保存计费规则] 参数: -> {} ", relateCommodity);
        BaseResponse<Boolean> resp = commodityRelatedService.registerInit(relateCommodity);
        if (resp.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            log.error("[注册保存计费规则] 调用商品中心保存商品结果 -> {}", resp);
            throw new BusinessException(BusinessExceptionEnums.FEE_RULE_SAVE_ERROR);
        }
    }

    @Override
    public void saveOrUpdateRecharge(List<EquipmentDTO> equipments, EquipmentRegisterDTO param) {
        super.saveOrUpdateRecharge(equipments,param);
    }

    @Override
    public void afterSave(List<EquipmentDTO> equipments, EquipmentRegisterDTO param) {

    }
}
