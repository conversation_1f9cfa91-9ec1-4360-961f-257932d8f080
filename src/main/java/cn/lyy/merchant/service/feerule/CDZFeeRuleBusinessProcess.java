package cn.lyy.merchant.service.feerule;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.equipment.dto.equipment.BatchUpdateGroupServiceCostWayDTO;
import cn.lyy.equipment.dto.equipment.EquipmentDTO;
import cn.lyy.equipment.service.IEquipmentAttachService;
import cn.lyy.merchant.constants.BusinessExceptionEnums;
import cn.lyy.merchant.dto.request.EquipmentRegisterDTO;
import cn.lyy.merchant.exception.BusinessException;
import com.lyy.commodity.rpc.constants.CategoryEnum;
import com.lyy.commodity.rpc.constants.ChannelEnum;
import com.lyy.commodity.rpc.constants.ClassifyCodeEnum;
import com.lyy.commodity.rpc.dto.request.*;
import com.lyy.commodity.rpc.feign.ICommodityRelatedService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>Title:saas2</p>
 * <p>Desc: 充电桩计费处理</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020/4/9
 */
@Slf4j
@Component("CDZ_FeeRule")
public class CDZFeeRuleBusinessProcess extends AbstractFeeRuleBusinessProcess {

    @Resource
    private ICommodityRelatedService commodityRelatedService;

    @Resource
    private IEquipmentAttachService equipmentAttachService;

    @Override
    public void saveOrUpdateFeeRule(List<EquipmentDTO> equipments, EquipmentRegisterDTO param){
        RelateCommodityDTO relateCommodity = new RelateCommodityDTO();
        relateCommodity.setDistributor(param.getDistributorId());
        relateCommodity.setAdUser(param.getUserId());
        relateCommodity.setEquipments(new ArrayList<>(equipments.size()));
        // 设置关联设备
        equipments.forEach(equipment -> {
            RelateEquipmentDTO relateEquipment = new RelateEquipmentDTO();
            relateEquipment.setEquipment(equipment.getEquipmentId());
            relateEquipment.setEquipmentType(equipment.getEquipmentTypeId());
            relateEquipment.setStore(equipment.getEquipmentGroupId());
            relateCommodity.getEquipments().add(relateEquipment);
        });
        // 设置商品
        relateCommodity.setCommodities(new ArrayList<>(param.getFeeRule().size()));

        param.getFeeRule().forEach(rule -> {
            // 定价
            CommodityFixPriceDTO fixPrice = new CommodityFixPriceDTO();
            fixPrice.setPrice(rule.getPrice());
            List<CommodityFixPriceValueDTO> valueList = new ArrayList<>();
            rule.getPriceValueList().forEach(fixValue -> {
                CommodityFixPriceValueDTO item = new CommodityFixPriceValueDTO();
                item.setUnit(fixValue.getUnit());
                item.setValue(fixValue.getValue());
                valueList.add(item);
            });
            fixPrice.setFixPriceValueList(valueList);
            // 商品
            CommodityDTO commodity = new CommodityDTO();
            commodity.setCategoryCode(CategoryEnum.DEVICE_SERVICE.getCode());
            commodity.setChannel(ChannelEnum.H5.getId());
            commodity.setName(rule.getName());
            commodity.setClassifyCode(rule.getClassifyCode());
            commodity.setFixPrice(fixPrice);

            relateCommodity.getCommodities().add(commodity);
        });
        log.debug("[注册保存计费规则] 参数: -> {} ", relateCommodity);
        BaseResponse<Boolean> resp = commodityRelatedService.registerInit(relateCommodity);
        if (resp.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            log.error("[注册保存计费规则] 调用商品中心保存商品结果 -> {}", resp);
            throw new BusinessException(BusinessExceptionEnums.FEE_RULE_SAVE_ERROR);
        }
    }

    @Override
    public void saveOrUpdateRecharge(List<EquipmentDTO> equipments, EquipmentRegisterDTO param) {
        super.saveOrUpdateRecharge(equipments,param);
    }


    @Override
    public void afterSave(List<EquipmentDTO> equipments, EquipmentRegisterDTO equipmentRegisterDTO) {
        log.debug("修改attach表计费模式");
        //  充电桩业务在修改计费模式之后 需同步附加信息表计费模式
        List<Integer> equipmentIds =  equipments.stream().map(r -> r.getEquipmentId().intValue()).collect(Collectors.toList());
        BatchUpdateGroupServiceCostWayDTO batchUpdateGroupServiceCostWayDTO = new BatchUpdateGroupServiceCostWayDTO();
        if(equipmentRegisterDTO.getFeeRule() != null && equipmentRegisterDTO.getFeeRule().size()>0){
            // 取商品设置的计费模式
            batchUpdateGroupServiceCostWayDTO.setGroupServiceCostWay(equipmentRegisterDTO.getFeeRule().get(0).getClassifyCode());
        }else{
            // 设置默认值
            batchUpdateGroupServiceCostWayDTO.setGroupServiceCostWay("TIME");
        }

        batchUpdateGroupServiceCostWayDTO.setEquipmentIds(equipmentIds);
        BaseResponse<Boolean> baseReponse = equipmentAttachService.batchUpdateGroupServiceCostWay(batchUpdateGroupServiceCostWayDTO);
        Boolean updateResult =  Optional.ofNullable(baseReponse).filter(r ->ResponseCodeEnum.SUCCESS.getCode() == r.getCode()).map(BaseResponse::getData).orElseThrow(()-> new BusinessException(BusinessExceptionEnums.SET_EQUIPMENT_ERROR));
        if(!Boolean.TRUE.equals(updateResult)){
            log.error("修改attach表计费模式 失败");
        }
    }

}
