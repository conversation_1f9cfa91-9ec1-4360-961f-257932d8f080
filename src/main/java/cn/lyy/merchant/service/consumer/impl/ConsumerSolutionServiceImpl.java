package cn.lyy.merchant.service.consumer.impl;

import static java.util.Optional.ofNullable;

import cn.hutool.core.collection.CollUtil;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.merchant.api.service.MerchantEquipmentService;
import cn.lyy.merchant.api.service.PayscoreClient;
import cn.lyy.merchant.billing.ConsumerSolutionClient;
import cn.lyy.merchant.constants.BusinessExceptionEnums;
import cn.lyy.merchant.dto.equipment.EquipmentTypeDTO;
import cn.lyy.merchant.dto.merchant.request.MerchantEquipmentConditionDTO;
import cn.lyy.merchant.dto.merchant.response.MerchantEquipmentInfoDTO;
import cn.lyy.merchant.dto.payscore.PayscoreDTO;
import cn.lyy.merchant.dto.payscore.request.PayscoreConfigQueryDTO;
import cn.lyy.merchant.dto.request.ConsumerSolutionListReqDTO;
import cn.lyy.merchant.dto.request.ConsumerSolutionSaveDTO;
import cn.lyy.merchant.dto.request.ConsumerSolutionUpdateDTO;
import cn.lyy.merchant.dto.request.EquipmentConsumerSolutionReqDTO;
import cn.lyy.merchant.dto.request.GroupConsumerSolutionReqDTO;
import cn.lyy.merchant.dto.response.ConsumerSolutionDetailDTO;
import cn.lyy.merchant.dto.response.ConsumerSolutionDetailDTO.StageTimeInfo;
import cn.lyy.merchant.dto.response.ConsumerSolutionListDTO;
import cn.lyy.merchant.dto.response.GroupConsumerSolutionDTO;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.filter.CommonParamsContext;
import cn.lyy.merchant.microservice.CommoditySkuService;
import cn.lyy.merchant.service.consumer.ConsumerSolutionService;
import com.lyy.billing.infrastructure.constants.ConsumerModelEnum;
import com.lyy.billing.infrastructure.constants.ConsumerSolutionCategoryEnum;
import com.lyy.billing.infrastructure.constants.ConsumerSolutionCommodityCategoryEnum;
import com.lyy.billing.infrastructure.constants.EquipmentTypeEnum;
import com.lyy.billing.infrastructure.constants.StartWayEnum;
import com.lyy.billing.interfaces.consumer.dto.ConsumerSolutionCommodityDTO;
import com.lyy.billing.interfaces.consumer.dto.ConsumerSolutionDTO;
import com.lyy.billing.interfaces.consumer.dto.EquipmentConsumerSolutionDTO;
import com.lyy.billing.interfaces.consumer.dto.TimingComboDTO;
import com.lyy.billing.interfaces.consumer.dto.request.AssociationEquipmentReqDTO;
import com.lyy.billing.interfaces.consumer.dto.request.ConsumerSolutionInfoSaveDTO;
import com.lyy.billing.interfaces.consumer.dto.request.ConsumerSolutionInfoUpdateDTO;
import com.lyy.commodity.rpc.dto.request.CommodityDetailsReq;
import com.lyy.commodity.rpc.dto.response.SkuCommodityDetailsResDTO;
import com.lyy.equipment.interfaces.feign.equipment.IotEquipmentLocationServiceFeignClient;
import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.starter.common.resp.RespBody;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2022/3/7
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ConsumerSolutionServiceImpl implements ConsumerSolutionService {

    private final ConsumerSolutionClient consumerSolutionClient;
    private final MerchantEquipmentService merchantEquipmentService;
    private final CommoditySkuService commoditySkuService;
    private final IotEquipmentLocationServiceFeignClient iotEquipmentLocationService;
    private final PayscoreClient payscoreClient;

    private final String defaultChannel = "wechat";

    @Override
    public Long save(ConsumerSolutionSaveDTO consumerSolutionSaveDTO, Long merchantId, Long adUserId) {
        CommonParamsContext.setBelongTo(merchantId);
        //有设置押金，查询支付分风险金，押金不得大于风险金
        BigDecimal price = consumerSolutionSaveDTO.getPrice();
        String appid = consumerSolutionSaveDTO.getAppid();
        String channel = consumerSolutionSaveDTO.getChannel();
        Long equipmentTypeId = consumerSolutionSaveDTO.getEquipmentType();
        if (Objects.nonNull(price) && StringUtils.isNotEmpty(appid)) {
            validateDeposit(merchantId, price, appid, channel, equipmentTypeId);
        }
        ConsumerSolutionInfoSaveDTO saveDTO = new ConsumerSolutionInfoSaveDTO();
        BeanUtils.copyProperties(consumerSolutionSaveDTO, saveDTO);
        saveDTO.setModel(Optional.ofNullable(consumerSolutionSaveDTO.getModel()).orElse(ConsumerModelEnum.TIME.getCategory()));
        saveDTO.setStartWay(Optional.ofNullable(consumerSolutionSaveDTO.getStartWay()).filter(CollUtil::isNotEmpty)
            .orElse(Collections.singletonList(StartWayEnum.PAY.getCategory())));
        saveDTO.setCreateBy(adUserId);
        saveDTO.setMerchantId(merchantId);
        ConsumerSolutionCommodityDTO consumerSolutionCommodityDTO = new ConsumerSolutionCommodityDTO();
        consumerSolutionCommodityDTO.setCategory(ConsumerSolutionCommodityCategoryEnum.PRE_PAY_TIME.getCategory());
        TimingComboDTO timingCombo = new TimingComboDTO();
        timingCombo.setFreeTime(consumerSolutionSaveDTO.getFreeTime());
        timingCombo.setPricingType(consumerSolutionSaveDTO.getPricingType());
        timingCombo.setPrice(consumerSolutionSaveDTO.getPrice());
        timingCombo.setDayMaxAmount(consumerSolutionSaveDTO.getDayMaxAmount());
        // 商品名 品类+租借+消费时间  沙滩车租借100分钟
        timingCombo.setName(consumerSolutionSaveDTO.getEquipmentTypeName());
        List<com.lyy.billing.interfaces.consumer.dto.StageTimeInfo> stageTimeInfoList = consumerSolutionSaveDTO.getStageTimeList().stream()
            .map(stageTimeInfo -> {
                com.lyy.billing.interfaces.consumer.dto.StageTimeInfo billingStageTime = new com.lyy.billing.interfaces.consumer.dto.StageTimeInfo();
                billingStageTime.setAmount(stageTimeInfo.getAmount());
                billingStageTime.setStageTime(stageTimeInfo.getStageTime());
                billingStageTime.setStageTimeType(stageTimeInfo.getStageTimeType());
                billingStageTime.setSort(stageTimeInfo.getSort());
                return billingStageTime;
            }).collect(Collectors.toList());
        timingCombo.setStageTimeInfoList(stageTimeInfoList);
        consumerSolutionCommodityDTO.setTimingCombo(timingCombo);
        saveDTO.setConsumerSolutionCommodity(consumerSolutionCommodityDTO);
        RespBody<Long> respBody = consumerSolutionClient.save(saveDTO, merchantId);
        boolean result = GlobalErrorCode.OK.getCode().equals(respBody.getCode());
        if (!result) {
            log.debug("保存消费方案,{}", respBody);
            throw new BusinessException(BusinessExceptionEnums.SAVE_CONSUMER_SOLUTION_ERROR.getExCode(), respBody.getMessage());
        }
        return respBody.getBody();
    }

    @Override
    public Boolean edit(ConsumerSolutionUpdateDTO consumerSolutionUpdateDTO, Long merchantId, Long adUserId) {
        CommonParamsContext.setBelongTo(merchantId);
        //有设置押金，查询支付分风险金，押金不得大于风险金
        BigDecimal price = consumerSolutionUpdateDTO.getPrice();
        String appid = consumerSolutionUpdateDTO.getAppid();
        String channel = consumerSolutionUpdateDTO.getChannel();
        Long equipmentTypeId = consumerSolutionUpdateDTO.getEquipmentType();
        if (Objects.nonNull(price) && StringUtils.isNotEmpty(appid)) {
            validateDeposit(merchantId, price, appid, channel, equipmentTypeId);
        }
        ConsumerSolutionInfoUpdateDTO updateDTO = new ConsumerSolutionInfoUpdateDTO();
        if (consumerSolutionUpdateDTO.getCommodityId() != null) {
            ConsumerSolutionCommodityDTO consumerSolutionCommodityDTO = new ConsumerSolutionCommodityDTO();
            consumerSolutionCommodityDTO.setCategory(ConsumerSolutionCommodityCategoryEnum.PRE_PAY_TIME.getCategory());
            TimingComboDTO timingCombo = new TimingComboDTO();
            timingCombo.setCommodityId(consumerSolutionUpdateDTO.getCommodityId());
            timingCombo.setPricingType(consumerSolutionUpdateDTO.getPricingType());
            timingCombo.setFreeTime(consumerSolutionUpdateDTO.getFreeTime());
            timingCombo.setPrice(consumerSolutionUpdateDTO.getPrice());
            timingCombo.setDayMaxAmount(consumerSolutionUpdateDTO.getDayMaxAmount());
            // 商品名 品类+租借+消费时间  沙滩车租借100分钟
            timingCombo.setName(consumerSolutionUpdateDTO.getEquipmentTypeName());
            List<com.lyy.billing.interfaces.consumer.dto.StageTimeInfo> stageTimeInfoList = consumerSolutionUpdateDTO.getStageTimeList().stream()
                .map(stageTimeInfo -> {
                    com.lyy.billing.interfaces.consumer.dto.StageTimeInfo billingStageTime = new com.lyy.billing.interfaces.consumer.dto.StageTimeInfo();
                    billingStageTime.setAmount(stageTimeInfo.getAmount());
                    billingStageTime.setStageTime(stageTimeInfo.getStageTime());
                    billingStageTime.setStageTimeType(stageTimeInfo.getStageTimeType());
                    billingStageTime.setSort(stageTimeInfo.getSort());
                    return billingStageTime;
                }).collect(Collectors.toList());
            timingCombo.setStageTimeInfoList(stageTimeInfoList);
            consumerSolutionCommodityDTO.setTimingCombo(timingCombo);
            updateDTO.setConsumerSolutionCommodity(consumerSolutionCommodityDTO);
        }
        BeanUtils.copyProperties(consumerSolutionUpdateDTO, updateDTO);
        updateDTO.setUpdateBy(adUserId);
        RespBody<Boolean> respBody = consumerSolutionClient.edit(updateDTO, merchantId);
        boolean result = GlobalErrorCode.OK.getCode().equals(respBody.getCode());
        if (!result) {
            log.debug("编辑消费方案,{}", respBody);
            throw new BusinessException(BusinessExceptionEnums.EDIT_CONSUMER_SOLUTION_ERROR.getExCode(), respBody.getMessage());
        }
        return respBody.getBody();
    }

    private void validateDeposit(Long merchantId, BigDecimal price, String appid, String channel, Long equipmentTypeId) {
        PayscoreConfigQueryDTO request = new PayscoreConfigQueryDTO();
        request.setChannel(ofNullable(channel).orElse(defaultChannel));
        request.setAdOrgId(ofNullable(merchantId).map(Long::intValue).orElse(null));
        request.setEquipmentTypeId(ofNullable(equipmentTypeId).map(Long::intValue).orElse(null));
        request.setAppid(appid);
        PayscoreDTO payscore = ofNullable(payscoreClient.getPayscoreConfigInfo(request))
            .filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode()).map(BaseResponse::getData).orElse(null);
        ofNullable(payscore).map(PayscoreDTO::getDepositAmt).map(deposit -> deposit / 100).ifPresent(deposit -> {
            if (price.compareTo(new BigDecimal(deposit)) > 0) {
                String message = String.format("风险金为%s,押金不得大于风险金", deposit);
                throw new BusinessException(BusinessExceptionEnums.PARAM_ERROR.getExCode(), message);
            }
        });
    }

    @Override
    public ConsumerSolutionDetailDTO detail(Long id, Integer category, Integer type, Long merchantId) {
        CommonParamsContext.setBelongTo(merchantId);
        com.lyy.billing.interfaces.consumer.dto.ConsumerSolutionDetailDTO responseDetail = ofNullable(
            consumerSolutionClient.detail(id, category, type, merchantId)).filter(b -> GlobalErrorCode.OK.getCode().equals(b.getCode()))
            .map(RespBody::getBody).orElse(null);
        ConsumerSolutionDetailDTO detail = null;
        if (responseDetail != null) {
            detail = new ConsumerSolutionDetailDTO();
            BeanUtils.copyProperties(responseDetail, detail);
            detail.setConsumerSolutionId(responseDetail.getId());
            ConsumerSolutionCommodityDTO consumerSolutionCommodity = ofNullable(responseDetail.getConsumerSolutionCommodity())
                .filter(commodity -> commodity.getCategory().equals(ConsumerSolutionCommodityCategoryEnum.PRE_PAY_TIME.getCategory()))
                .orElse(null);
            if (consumerSolutionCommodity != null && consumerSolutionCommodity.getTimingCombo() != null) {
                TimingComboDTO timingCombo = consumerSolutionCommodity.getTimingCombo();
                detail.setFreeTime(timingCombo.getFreeTime());
                detail.setDayMaxAmount(timingCombo.getDayMaxAmount());
                detail.setPricingType(timingCombo.getPricingType());
                Long commodityId = timingCombo.getCommodityId();
                detail.setCommodityId(commodityId);
                ConsumerSolutionDetailDTO finalDetail = detail;
                Optional.ofNullable(commoditySkuService.getSkuDetail(commodityId))
                    .filter(b -> {
                        log.debug("获取商品详情,{}", b);
                        return ResponseCodeEnum.SUCCESS.getCode() == b.getCode();
                    })
                    .map(BaseResponse::getData).ifPresent(sku -> {
                    finalDetail.setPrice(sku.getShelfInfoReqList().get(0).getPrice());
                    List<StageTimeInfo> stageTimeInfoList = sku.getFixPriceValueList().stream().map(fixPriceValueDTO -> {
                        StageTimeInfo stageTimeInfo = new StageTimeInfo();
                        stageTimeInfo.setAmount(fixPriceValueDTO.getValue());
                        stageTimeInfo.setStageTime(fixPriceValueDTO.getStageTime());
                        stageTimeInfo.setStageTimeType(fixPriceValueDTO.getStageTimeType());
                        stageTimeInfo.setSort(fixPriceValueDTO.getSort());
                        return stageTimeInfo;
                    }).collect(Collectors.toList());
                    finalDetail.setStageTimeList(stageTimeInfoList);
                });
            }
        }
        return detail;
    }

    @Override
    public Boolean associationEquipments(AssociationEquipmentReqDTO associationEquipmentReqDTO, Long merchantId) {
        CommonParamsContext.setBelongTo(merchantId);
        return ofNullable(consumerSolutionClient.associationEquipments(associationEquipmentReqDTO, merchantId))
            .filter(b -> {
                log.debug("消费方案关联设备,{}", b);
                return GlobalErrorCode.OK.getCode().equals(b.getCode());
            })
            .map(RespBody::getBody)
            .orElseThrow(() -> new BusinessException(BusinessExceptionEnums.EQUIPMENT_ASSOCIATION_CONSUMER_SOLUTION_ERROR));
    }

    @Override
    public Boolean delete(Long id, Long merchantId) {
        CommonParamsContext.setBelongTo(merchantId);
        return ofNullable(consumerSolutionClient.deleteConsumerSolution(id, merchantId))
            .filter(b -> {
                log.debug("删除消费方案,{}", b);
                return GlobalErrorCode.OK.getCode().equals(b.getCode());
            })
            .map(RespBody::getBody).orElseThrow(() -> new BusinessException(BusinessExceptionEnums.DELETE_CONSUMER_SOLUTION_ERROR));
    }

    @Override
    public List<GroupConsumerSolutionDTO> findGroupConsumerSolutions(GroupConsumerSolutionReqDTO groupConsumerSolutionReqDTO,
        Long merchantId, Long adUserId) {
        CommonParamsContext.setBelongTo(merchantId);
        List<Long> groupIds = groupConsumerSolutionReqDTO.getGroupIds();
        List<Long> equipmentTypeList = groupConsumerSolutionReqDTO.getEquipmentTypeList();
        MerchantEquipmentConditionDTO condition = MerchantEquipmentConditionDTO.builder().build();
        condition.setAdUserId(Optional.of(groupConsumerSolutionReqDTO).map(GroupConsumerSolutionReqDTO::getAdUserId).orElse(adUserId));
        condition.setMerchantId(merchantId);
        condition.setGroupList(groupIds);
        condition.setEquipmentTypeList(equipmentTypeList);
        condition.setLabels(groupConsumerSolutionReqDTO.getLabels());
        condition.setQueryStr(groupConsumerSolutionReqDTO.getQueryStr());
        condition.setDistrictIds(groupConsumerSolutionReqDTO.getDistrictIds());
        //获取用户场地下设备列表
        List<MerchantEquipmentInfoDTO> merchantEquipments = ofNullable(merchantEquipmentService.findEquipmentByAdUser(condition))
            .filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode())
            .map(BaseResponse::getData).orElse(new ArrayList<>());
        List<GroupConsumerSolutionDTO> list = new ArrayList<>();
        if (CollUtil.isNotEmpty(merchantEquipments)) {
            //根据场地id进行分组
            Map<Long, List<MerchantEquipmentInfoDTO>> groupMap = merchantEquipments.stream()
                .filter(me -> me.getStoreId() != null)
                .collect(Collectors.groupingBy(MerchantEquipmentInfoDTO::getStoreId));
            List<Long> equipmentIds = new ArrayList<>();
            groupMap.forEach(
                (k, v) -> equipmentIds.addAll(v.stream().map(MerchantEquipmentInfoDTO::getEquipmentId).collect(Collectors.toList())));
            Integer type = ofNullable(groupConsumerSolutionReqDTO.getAssociationType()).orElse(EquipmentTypeEnum.TERMINAL.getType());
            Integer category = ofNullable(groupConsumerSolutionReqDTO.getCategory())
                .orElse(ConsumerSolutionCategoryEnum.SCAN_CODE.getCategory());
            if (CollUtil.isNotEmpty(equipmentIds)) {
                com.lyy.billing.interfaces.consumer.dto.request.EquipmentConsumerSolutionReqDTO request = new com.lyy.billing.interfaces.consumer.dto.request.EquipmentConsumerSolutionReqDTO();
                request.setType(type);
                request.setCategory(category);
                request.setEquipmentIds(equipmentIds);
                Map<Long, cn.lyy.merchant.dto.response.EquipmentConsumerSolutionDTO> map = ofNullable(
                    consumerSolutionClient.findConsumerSolutionEquipmentList(request, merchantId))
                    .filter(b -> {
                        log.debug("获取设备关联消费方案,{}", b);
                        return GlobalErrorCode.OK.getCode().equals(b.getCode());
                    })
                    .map(RespBody::getBody).orElse(new ArrayList<>()).stream().map(obj -> {
                        cn.lyy.merchant.dto.response.EquipmentConsumerSolutionDTO equipmentConsumerSolution = new cn.lyy.merchant.dto.response.EquipmentConsumerSolutionDTO();
                        equipmentConsumerSolution
                            .setEquipmentId(type.equals(EquipmentTypeEnum.TERMINAL.getType()) ? obj.getTerminalId() : obj.getDeviceId());
                        equipmentConsumerSolution.setConsumerSolutionId(obj.getConsumerSolutionId());
                        equipmentConsumerSolution.setCategory(obj.getCategory());
                        equipmentConsumerSolution.setName(obj.getName());
                        return equipmentConsumerSolution;
                    }).collect(
                        Collectors.toMap(cn.lyy.merchant.dto.response.EquipmentConsumerSolutionDTO::getEquipmentId, Function.identity()));
                groupMap.forEach((storeId, groupEquipmentList) -> {
                    GroupConsumerSolutionDTO groupConsumerSolution = new GroupConsumerSolutionDTO();
                    if (CollUtil.isNotEmpty(groupEquipmentList)) {
                        groupConsumerSolution.setName(groupEquipmentList.get(0).getGroupName());
                        groupConsumerSolution.setStoreId(storeId);
                        groupConsumerSolution.setEquipmentConsumerSolutionList(groupEquipmentList.stream()
                            .map(equipment -> {
                                cn.lyy.merchant.dto.response.EquipmentConsumerSolutionDTO dto = map
                                    .getOrDefault(equipment.getEquipmentId(),
                                        new cn.lyy.merchant.dto.response.EquipmentConsumerSolutionDTO());
                                dto.setEquipmentId(equipment.getEquipmentId());
                                dto.setValue(equipment.getEquipmentValue());
                                dto.setEquipmentTypeId(equipment.getEquipmentTypeId());
                                return dto;
                            }).collect(Collectors.toList()));
                        list.add(groupConsumerSolution);
                    }
                });
            }
        }
        return list;
    }

    @Override
    public List<cn.lyy.merchant.dto.response.EquipmentConsumerSolutionDTO> findEquipmentConsumerSolutions(
        EquipmentConsumerSolutionReqDTO equipmentConsumerSolutionReqDTO, Long merchantId) {
        CommonParamsContext.setBelongTo(merchantId);
        Integer type = ofNullable(equipmentConsumerSolutionReqDTO.getAssociationType()).orElse(EquipmentTypeEnum.TERMINAL.getType());
        Integer category = ofNullable(equipmentConsumerSolutionReqDTO.getCategory())
            .orElse(ConsumerSolutionCategoryEnum.SCAN_CODE.getCategory());
        List<Long> equipmentIds = equipmentConsumerSolutionReqDTO.getEquipmentIds();
        if (CollUtil.isNotEmpty(equipmentIds)) {
            return ofNullable(consumerSolutionClient.findEquipmentConsumerSolutionList(type, equipmentIds, category, merchantId))
                .filter(b -> {
                    log.debug("获取设备关联消费方案列表,{}", b);
                    return GlobalErrorCode.OK.getCode().equals(b.getCode());
                })
                .map(RespBody::getBody).orElse(new ArrayList<>()).stream().map(obj -> {
                    cn.lyy.merchant.dto.response.EquipmentConsumerSolutionDTO equipmentConsumerSolution = new cn.lyy.merchant.dto.response.EquipmentConsumerSolutionDTO();
                    equipmentConsumerSolution
                        .setEquipmentId(type.equals(EquipmentTypeEnum.TERMINAL.getType()) ? obj.getTerminalId() : obj.getDeviceId());
                    equipmentConsumerSolution.setConsumerSolutionId(obj.getConsumerSolutionId());
                    equipmentConsumerSolution.setCategory(obj.getCategory());
                    equipmentConsumerSolution.setName(obj.getName());
                    return equipmentConsumerSolution;
                }).collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public List<ConsumerSolutionListDTO> list(ConsumerSolutionListReqDTO request, Long merchantId) {
        CommonParamsContext.setBelongTo(merchantId);
        List<Long> consumerSolutionIds = null;
        if (StringUtils.isNotEmpty(request.getKeyword())) {
            List<Long> equipmentIds = ofNullable(
                merchantEquipmentService.findMerchantEquipmentIdsByKeyword(request.getMerchantId(), request.getKeyword()))
                .filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode())
                .map(BaseResponse::getData).orElse(null);
            if (CollUtil.isNotEmpty(equipmentIds)) {
                List<EquipmentConsumerSolutionDTO> equipmentConsumerSolutionList = ofNullable(
                    consumerSolutionClient.findEquipmentConsumerSolutionList(EquipmentTypeEnum.TERMINAL.getType(), equipmentIds, null, merchantId))
                    .filter(b -> {
                        log.debug("获取设备关联消费方案列表,{}", b);
                        return GlobalErrorCode.OK.getCode().equals(b.getCode());
                    })
                    .map(RespBody::getBody).orElse(new ArrayList<>());
                consumerSolutionIds = equipmentConsumerSolutionList.stream().map(EquipmentConsumerSolutionDTO::getConsumerSolutionId)
                    .collect(Collectors.toList());
            }
        }
        List<ConsumerSolutionListDTO> list = new ArrayList<>();
        List<ConsumerSolutionDTO> consumerSolutionList = ofNullable(
            consumerSolutionClient.findConsumerSolutionList(request.getMerchantId(), request.getScope(),
                null, null, request.getKeyword(), request.getEquipmentTypeId(), consumerSolutionIds,
                EquipmentTypeEnum.TERMINAL.getType(), request.getIsCount(), true, request.getStore(), merchantId))
            .filter(b -> {
                log.debug("获取消费方案列表,{}", b);
                return GlobalErrorCode.OK.getCode().equals(b.getCode());
            })
            .map(RespBody::getBody).orElse(null);
        if (CollUtil.isNotEmpty(consumerSolutionList)) {
            Map<Long, EquipmentTypeDTO> typeMap = ofNullable(merchantEquipmentService.listEquipmentType(request.getMerchantId()))
                .filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode())
                .map(BaseResponse::getData).orElse(new ArrayList<>())
                .stream().collect(Collectors.toMap(EquipmentTypeDTO::getId, Function.identity()));
            Map<Long, ConsumerSolutionCommodityDTO> map = consumerSolutionList.stream()
                .filter(consumerSolution -> CollUtil.isNotEmpty(consumerSolution.getConsumerSolutionCommodityList()))
                .map(consumerSolution -> consumerSolution.getConsumerSolutionCommodityList().stream().filter(commodity ->
                    commodity.getCategory().equals(ConsumerSolutionCommodityCategoryEnum.PRE_PAY_TIME.getCategory())).findFirst().orElse(null))
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(ConsumerSolutionCommodityDTO::getConsumerSolutionId, Function.identity()));
            List<Long> commodityIds = map.values().stream().map(ConsumerSolutionCommodityDTO::getTimingCombo)
                .map(TimingComboDTO::getCommodityId).collect(Collectors.toList());
            CommodityDetailsReq commodityDetailsReq = new CommodityDetailsReq();
            commodityDetailsReq.setIdList(commodityIds);
            List<SkuCommodityDetailsResDTO> skuCommodityDetails = ofNullable(
                commoditySkuService.querySkuBathDetail(commodityDetailsReq))
                .filter(b -> {
                    log.debug("批量查询商品详情,{}", b);
                    return ResponseCodeEnum.SUCCESS.getCode() == b.getCode();
                })
                .map(BaseResponse::getData).orElse(new ArrayList<>());
            Map<Long, SkuCommodityDetailsResDTO> commodityMap = skuCommodityDetails.stream()
                .collect(Collectors.toMap(SkuCommodityDetailsResDTO::getId, Function.identity()));
            consumerSolutionList.forEach(consumerSolution -> {
                ConsumerSolutionListDTO dto = buildConsumerSolutionListDTO(map, commodityMap, typeMap, consumerSolution);
                list.add(dto);
            });

        }
        return list;
    }

    private ConsumerSolutionListDTO buildConsumerSolutionListDTO(Map<Long, ConsumerSolutionCommodityDTO> map,
        Map<Long, SkuCommodityDetailsResDTO> commodityMap, Map<Long, EquipmentTypeDTO> typeMap,
        ConsumerSolutionDTO consumerSolution) {
        ConsumerSolutionListDTO dto = new ConsumerSolutionListDTO();
        dto.setDescription(consumerSolution.getDescription());
        dto.setEquipmentNum(consumerSolution.getEquipmentNum());
        dto.setEquipmentType(consumerSolution.getEquipmentType());
        dto.setCategory(consumerSolution.getCategory());
        dto.setId(consumerSolution.getId());
        dto.setUpdated(consumerSolution.getUpdated());
        dto.setName(consumerSolution.getName());
        dto.setStoreNum(consumerSolution.getStoreNum());
        dto.setMerchantId(consumerSolution.getMerchantId());
        ofNullable(consumerSolution.getEquipmentType()).ifPresent(equipmentTypeId -> {
            EquipmentTypeDTO equipmentTypeDTO = typeMap.get(equipmentTypeId);
            dto.setEquipmentTypeName(ofNullable(equipmentTypeDTO).map(EquipmentTypeDTO::getName).orElse(null));
        });
        if (map.get(consumerSolution.getId()) != null) {
            ConsumerSolutionCommodityDTO commodity = map.get(consumerSolution.getId());
            TimingComboDTO timingCombo = commodity.getTimingCombo();
            Long commodityId = ofNullable(timingCombo).map(TimingComboDTO::getCommodityId).orElse(null);
            dto.setFreeTime(ofNullable(timingCombo).map(TimingComboDTO::getFreeTime).orElse(null));
            dto.setDayMaxAmount(ofNullable(timingCombo).map(TimingComboDTO::getDayMaxAmount).orElse(null));
            dto.setPricingType(ofNullable(timingCombo).map(TimingComboDTO::getPricingType).orElse(null));
            ofNullable(commodityMap.get(commodityId)).ifPresent(sku -> {
                dto.setPrice(sku.getShelfInfoReqList().get(0).getPrice());
                List<ConsumerSolutionListDTO.StageTimeInfo> stageTimeInfoList = sku.getFixPriceValueList().stream()
                    .map(fixPriceValueDTO -> {
                        ConsumerSolutionListDTO.StageTimeInfo stageTimeInfo = new ConsumerSolutionListDTO.StageTimeInfo();
                        stageTimeInfo.setAmount(fixPriceValueDTO.getValue());
                        stageTimeInfo.setStageTime(fixPriceValueDTO.getStageTime());
                        stageTimeInfo.setStageTimeType(fixPriceValueDTO.getStageTimeType());
                        stageTimeInfo.setSort(fixPriceValueDTO.getSort());
                        return stageTimeInfo;
                    }).collect(Collectors.toList());
                dto.setStageTimeList(stageTimeInfoList);
            });
        }
        return dto;
    }
}
