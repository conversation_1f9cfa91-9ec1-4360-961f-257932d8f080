package cn.lyy.merchant.service.consumer;

import cn.lyy.merchant.dto.request.ConsumerSolutionListReqDTO;
import cn.lyy.merchant.dto.request.ConsumerSolutionSaveDTO;
import cn.lyy.merchant.dto.request.ConsumerSolutionUpdateDTO;
import cn.lyy.merchant.dto.request.EquipmentConsumerSolutionReqDTO;
import cn.lyy.merchant.dto.request.GroupConsumerSolutionReqDTO;
import cn.lyy.merchant.dto.response.ConsumerSolutionDetailDTO;
import cn.lyy.merchant.dto.response.ConsumerSolutionListDTO;
import cn.lyy.merchant.dto.response.EquipmentConsumerSolutionDTO;
import cn.lyy.merchant.dto.response.GroupConsumerSolutionDTO;
import com.lyy.billing.interfaces.consumer.dto.ConsumerSolutionDTO;
import com.lyy.billing.interfaces.consumer.dto.request.AssociationEquipmentReqDTO;
import com.lyy.billing.interfaces.consumer.dto.request.ConsumerSolutionBillingCalculateDTO;
import com.lyy.billing.interfaces.consumer.dto.request.ConsumerSolutionBillingPayDTO;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/7
 */
public interface ConsumerSolutionService {

    Long save(ConsumerSolutionSaveDTO consumerSolutionSaveDTO, Long merchantId, Long adUserId);

    Boolean edit(ConsumerSolutionUpdateDTO consumerSolutionUpdateDTO, Long merchantId, Long adUserId);

    ConsumerSolutionDetailDTO detail(Long id, Integer category, Integer type, Long merchantId);

    List<ConsumerSolutionListDTO> list(ConsumerSolutionListReqDTO consumerSolutionListReqDTO, Long merchantId);

    Boolean associationEquipments(AssociationEquipmentReqDTO associationEquipmentReqDTO, Long merchantId);

    Boolean delete(Long id, Long merchantId);

    List<GroupConsumerSolutionDTO> findGroupConsumerSolutions(GroupConsumerSolutionReqDTO groupConsumerSolutionReqDTO, Long merchantId, Long adUserId);

    List<EquipmentConsumerSolutionDTO> findEquipmentConsumerSolutions(EquipmentConsumerSolutionReqDTO equipmentConsumerSolutionReqDTO, Long merchantId);
}
