package cn.lyy.merchant.service.consumer;

import cn.lyy.merchant.dto.request.baichuan.BaichuanConsumerSolutionPageReqDTO;
import cn.lyy.merchant.dto.request.baichuan.BaichuanConsumerSolutionReqDTO;
import cn.lyy.merchant.dto.request.baichuan.BaichuanConsumerSolutionUpsertDTO;
import cn.lyy.merchant.dto.request.baichuan.ConsumerSolutionDetailVO;
import cn.lyy.merchant.dto.request.baichuan.ConsumerSolutionGroupReqDTO;
import cn.lyy.merchant.dto.request.baichuan.ConsumerSolutionGroupVO;
import cn.lyy.merchant.dto.request.baichuan.ConsumerSolutionOverviewVO;
import cn.lyy.merchant.dto.request.baichuan.EquipmentAssociationReqDTO;
import cn.lyy.merchant.dto.request.baichuan.EquipmentConfigurationListVO;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.billing.interfaces.consumer.dto.request.EquipmentConfigurationExistRatioReqDTO;

import java.util.List;

public interface BaichuanConsumerSolutionService {

    Long upsertConsumerSolution(BaichuanConsumerSolutionUpsertDTO upsertDTO, Long merchantId, Long adUserId);

    List<ConsumerSolutionOverviewVO> listOverviews(BaichuanConsumerSolutionReqDTO request, Long merchantId);

    ConsumerSolutionDetailVO detail(Long id);

    Boolean equipmentAssociate(EquipmentAssociationReqDTO request, Long merchantId, Long adUserId);

    /**
     * 百川消费方案获取设备配置列表
     *
     * @param equipmentType
     * @param equipmentValue
     * @return
     */
    List<EquipmentConfigurationListVO> equipmentConfigurationList(String equipmentType, String equipmentValue);

    Page<ConsumerSolutionOverviewVO> pageOverviews(BaichuanConsumerSolutionPageReqDTO request, Long merchantId);

    List<ConsumerSolutionGroupVO> findSolutionGroupsEquipment(ConsumerSolutionGroupReqDTO request);

    Boolean deleteConsumerSolution(Long id, Long merchantId, Long adUserId);

    Boolean existConfigurationRatio(EquipmentConfigurationExistRatioReqDTO reqDTO);
}
