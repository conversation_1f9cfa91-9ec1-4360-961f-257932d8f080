package cn.lyy.merchant.service.consumer.impl;

import cn.hutool.core.collection.CollUtil;
import cn.lyy.base.utils.converter.CommonConverterTools;
import cn.lyy.base.utils.text.JSONObjectUtils;
import cn.lyy.merchant.api.service.EquipmentTypeClient;
import cn.lyy.merchant.assembler.consumer.ConsumerSolutionAssembler;
import cn.lyy.merchant.dto.equipment.EquipmentTypeDTO;
import cn.lyy.merchant.dto.request.baichuan.BaichuanConsumerSolutionPageReqDTO;
import cn.lyy.merchant.dto.request.baichuan.BaichuanConsumerSolutionReqDTO;
import cn.lyy.merchant.dto.request.baichuan.BaichuanConsumerSolutionUpsertDTO;
import cn.lyy.merchant.dto.request.baichuan.ConsumerSolutionDetailVO;
import cn.lyy.merchant.dto.request.baichuan.ConsumerSolutionGroupReqDTO;
import cn.lyy.merchant.dto.request.baichuan.ConsumerSolutionGroupVO;
import cn.lyy.merchant.dto.request.baichuan.ConsumerSolutionOverviewVO;
import cn.lyy.merchant.dto.request.baichuan.EquipmentAssociationReqDTO;
import cn.lyy.merchant.dto.request.baichuan.EquipmentConfigurationListVO;
import cn.lyy.merchant.dto.request.baichuan.GroupEquipmentVO;
import cn.lyy.merchant.dto.request.baichuan.MeteringCommodity;
import cn.lyy.merchant.dto.request.baichuan.RechargeCommodity;
import cn.lyy.merchant.dto.request.baichuan.StageRule;
import cn.lyy.merchant.dto.request.baichuan.StartCommodity;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.service.consumer.BaichuanConsumerSolutionService;
import cn.lyy.merchant.utils.RemoteResponseUtils;
import com.alibaba.csp.sentinel.util.AssertUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.lyy.billing.infrastructure.constants.BillingErrorCode;
import com.lyy.billing.infrastructure.constants.ConsumerModelEnum;
import com.lyy.billing.interfaces.consumer.dto.EquipmentAssociatedGroupRespDTO;
import com.lyy.billing.interfaces.consumer.dto.EquipmentConfigurationDTO;
import com.lyy.billing.interfaces.consumer.dto.EquipmentConfigurationListRespDTO;
import com.lyy.billing.interfaces.consumer.dto.baichuan.BaichuanConsumerSolutionOverviewVO;
import com.lyy.billing.interfaces.consumer.dto.baichuan.ConsumerSolutionCommodityFieldDTO;
import com.lyy.billing.interfaces.consumer.dto.baichuan.ConsumerSolutionCommodityVO;
import com.lyy.billing.interfaces.consumer.dto.baichuan.ConsumerSolutionDeleteDTO;
import com.lyy.billing.interfaces.consumer.dto.baichuan.ConsumerSolutionFieldDTO;
import com.lyy.billing.interfaces.consumer.dto.baichuan.ConsumerSolutionPageQueryDTO;
import com.lyy.billing.interfaces.consumer.dto.baichuan.ConsumerSolutionQueryDTO;
import com.lyy.billing.interfaces.consumer.dto.baichuan.EquipmentAssociationDTO;
import com.lyy.billing.interfaces.consumer.dto.request.EquipmentConfigurationExistRatioReqDTO;
import com.lyy.billing.interfaces.consumer.dto.request.EquipmentConfigurationQueryReqDTO;
import com.lyy.billing.interfaces.consumer.feign.BaichuanConsumerSolutionClient;
import com.lyy.billing.interfaces.consumer.feign.ConsumerSolutionFeignClient;
import com.lyy.billing.interfaces.consumer.feign.EquipmentConfigurationFeignClient;
import com.lyy.commodity.rpc.constants.CategoryTwoEnum;
import com.lyy.commodity.rpc.constants.ValueEum;
import com.lyy.commodity.rpc.constants.ValueUnitEnum;
import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.starter.common.resp.RespBody;
import com.lyy.starter.common.resp.RespBodyUtil;
import java.math.RoundingMode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import cn.lyy.merchant.constants.BusinessExceptionEnums;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import org.springframework.util.StringUtils;

import static java.util.Optional.ofNullable;

@Service
@Slf4j
public class BaichuanConsumerSolutionServiceImpl implements BaichuanConsumerSolutionService {

    @Resource
    private BaichuanConsumerSolutionClient baichuanConsumerSolutionClient;

    @Autowired
    private EquipmentConfigurationFeignClient equipmentConfigurationFeignClient;
    @Resource
    private EquipmentTypeClient equipmentTypeClient;
    @Resource
    private ConsumerSolutionFeignClient consumerSolutionFeignClient;


    @Override
    public Long upsertConsumerSolution(BaichuanConsumerSolutionUpsertDTO upsertDTO, Long merchantId, Long adUserId) {
        ConsumerSolutionFieldDTO consumerSolutionField = ConsumerSolutionAssembler.INSTANCE.toConsumerSolutionFieldDTO(upsertDTO);
        consumerSolutionField.setMerchantId(merchantId);
        com.lyy.billing.interfaces.consumer.dto.baichuan.BaichuanConsumerSolutionUpsertDTO consumerSolutionUpsertDTO =
                new com.lyy.billing.interfaces.consumer.dto.baichuan.BaichuanConsumerSolutionUpsertDTO();
        consumerSolutionUpsertDTO.setConsumerSolution(consumerSolutionField);
        consumerSolutionUpsertDTO.setOperator(adUserId);
        List<ConsumerSolutionCommodityFieldDTO> commodityList = new ArrayList<>();
        //充值套餐
        List<RechargeCommodity> rechargeCommodityList = upsertDTO.getRechargeCommodityList();
        if (!CollectionUtils.isEmpty(rechargeCommodityList)) {
            for (RechargeCommodity rechargeCommodity : rechargeCommodityList) {
                ConsumerSolutionCommodityFieldDTO commodity =
                        ConsumerSolutionAssembler.INSTANCE.toConsumerSolutionCommodityFieldDTO(rechargeCommodity);
                commodity.setCategoryCode(CategoryTwoEnum.SET_MEAL.getCode());
                commodity.setConsumerSolutionId(consumerSolutionField.getId());
                commodityList.add(commodity);
            }
        }
        if (!Objects.equals(consumerSolutionField.getModel(), ConsumerModelEnum.TIME.getCategory())) {
            //启动套餐
            List<StartCommodity> startCommodityList = upsertDTO.getStartCommodityList();
            if (!CollectionUtils.isEmpty(startCommodityList)) {
                startCommodityList.forEach(startCommodity -> {
                    if (startCommodity.getFixPriceValue() == null
                            && (startCommodity.getCoins() == null || BigDecimal.ZERO.compareTo(startCommodity.getCoins()) >= 0)) {
                        throw new BusinessException(BusinessExceptionEnums.PARAM_ERROR.getExCode(), "消费方案的套餐启动数量和模拟投币数至少有一个");
                    }else if (startCommodity.getFixPriceValue() != null && startCommodity.getUnit() == null) {
                        throw new BusinessException(BusinessExceptionEnums.PARAM_ERROR.getExCode(), "启动数量单位为空");
                    }
                });
                //投币数校验
                List<StartCommodity> hasCoinsList = startCommodityList.stream().filter(startCommodity -> startCommodity.getCoins() != null && BigDecimal.ZERO.compareTo(startCommodity.getCoins()) < 0).collect(Collectors.toList());
                //没有全部套餐设置模拟投币数
                if (CollUtil.isNotEmpty(hasCoinsList) && hasCoinsList.size() < startCommodityList.size()) {
                    log.info("没有全部套餐设置模拟投币数,hasCoinsList:{},startCommodityList:{}", hasCoinsList, startCommodityList);
                    throw new BusinessException(BusinessExceptionEnums.PACKAGE_LACK_IMITATE_ALL_COINS);
                }
                //启动数量校验
                List<StartCommodity> hasFixPriceValueList = startCommodityList.stream().filter(startCommodity -> startCommodity.getFixPriceValue() != null).collect(Collectors.toList());
                //没有全部套餐设置启动数量
                if (CollUtil.isNotEmpty(hasFixPriceValueList) && hasFixPriceValueList.size() < startCommodityList.size()) {
                    log.info("没有全部套餐设置启动数量,hasFixPriceValueList:{},startCommodityList:{}", hasFixPriceValueList, startCommodityList);
                    throw new BusinessException(BusinessExceptionEnums.PACKAGE_LACK_IMITATE_ALL_START_NUM);
                }
                //没有全部设置且有关联设备
                List<Long> equipmentIdList = ofNullable(upsertDTO.getId()).map(id -> RemoteResponseUtils.getData(consumerSolutionFeignClient.findDeviceIdsBySolution(2, Lists.newArrayList(id)), Lists.newArrayList())).orElse(Lists.newArrayList());
                if ((CollUtil.isEmpty(hasCoinsList) || CollUtil.isEmpty(hasFixPriceValueList)) && CollUtil.isNotEmpty(equipmentIdList)) {
                    EquipmentConfigurationExistRatioReqDTO reqDTO = new EquipmentConfigurationExistRatioReqDTO();
                    reqDTO.setConsumerSolutionId(upsertDTO.getId());
                    reqDTO.setEquipmentIdList(equipmentIdList);
                    RespBody<Boolean> respBody = equipmentConfigurationFeignClient.existConfigurationRatio(reqDTO);
                    if (!GlobalErrorCode.OK.getCode().equals(respBody.getCode())) {
                        if (BillingErrorCode.PACKAGE_LACK_IMITATE_COINS.getCode().equals(respBody.getCode())) {
                            log.debug("不存在配置比例,ConsumerSolutionId:{},equipmentIdList:{}", upsertDTO.getId(), equipmentIdList);
                            throw new BusinessException(BusinessExceptionEnums.PACKAGE_LACK_IMITATE_COINS);
                        }
                        if (BillingErrorCode.PACKAGE_LACK_IMITATE_START_NUM.getCode().equals(respBody.getCode())) {
                            log.debug("不存在配置比例,ConsumerSolutionId:{},equipmentIdList:{}", upsertDTO.getId(), equipmentIdList);
                            throw new BusinessException(BusinessExceptionEnums.PACKAGE_LACK_IMITATE_START_NUM);
                        }
                        throw new BusinessException(BusinessExceptionEnums.PARAM_ERROR.getExCode(), respBody.getMessage());
                    }
                }

                for (StartCommodity startCommodity : startCommodityList) {
                    ConsumerSolutionCommodityFieldDTO commodity =
                            ConsumerSolutionAssembler.INSTANCE.toConsumerSolutionCommodityFieldDTO(startCommodity);
                    commodity.setCategoryCode(CategoryTwoEnum.DEVICE_SERVICE.getCode());
                    commodity.setConsumerSolutionId(consumerSolutionField.getId());
                    commodityList.add(commodity);
                }
            }
        } else {
            MeteringCommodity meteringCommodity = upsertDTO.getMeteringCommodity();
            if (Objects.nonNull(meteringCommodity)) {
                AssertUtil.isTrue(Objects.isNull(meteringCommodity.getMaxAmount()) ||
                                meteringCommodity.getMaxAmount().compareTo(BigDecimal.ZERO) > 0, "封顶金额需大于0");
                AssertUtil.isTrue(Objects.isNull(meteringCommodity.getDayMaxAmount()) ||
                                meteringCommodity.getDayMaxAmount().compareTo(BigDecimal.ZERO) > 0, "每日封顶金额需大于0");
                if (Objects.nonNull(meteringCommodity.getDayMaxAmount()) && Objects.nonNull(meteringCommodity.getMaxAmount())) {
                    AssertUtil.isTrue(meteringCommodity.getDayMaxAmount().compareTo(meteringCommodity.getMaxAmount()) <= 0,
                            "每日封顶金额大于总封顶金额");
                }
                ConsumerSolutionCommodityFieldDTO commodity =
                        ConsumerSolutionAssembler.INSTANCE.toConsumerSolutionCommodityFieldDTO(meteringCommodity);
                List<StageRule> stageRuleList = meteringCommodity.getStageRuleList().stream()
                        .sorted(Comparator.comparing(StageRule::getSort)).collect(Collectors.toList());
                validStageRule(stageRuleList);
                commodity.setStageRuleList(stageRuleList.stream().map(ConsumerSolutionAssembler.INSTANCE::toStageRule).collect(Collectors.toList()));
                commodity.setCategoryCode(CategoryTwoEnum.DEVICE_SERVICE.getCode());
                commodity.setConsumerSolutionId(consumerSolutionField.getId());
                if (StringUtils.isEmpty(commodity.getName())) {
                    commodity.setName("计费规则");
                }
                commodity.setSort(0);
                commodityList.add(commodity);
            }
        }
        consumerSolutionUpsertDTO.setCommodityList(commodityList);
        return RemoteResponseUtils.getDataThrowException(baichuanConsumerSolutionClient.upsertBaichuanConsumerSolution(consumerSolutionUpsertDTO));
    }

    private void validStageRule(List<StageRule> stageRuleList) {
        if (stageRuleList.size() == 1) {
            StageRule stageRule = stageRuleList.get(0);
            AssertUtil.isTrue(stageRule.getAmount().compareTo(BigDecimal.ZERO) > 0, "计费单价需大于0");
            AssertUtil.isTrue(stageRule.getStageCalculateRange().compareTo(BigDecimal.ZERO) > 0, "计算单位范围需大于0");
            return;
        }
        BigDecimal lastEnd = BigDecimal.ZERO;
        for (StageRule stageRule : stageRuleList) {
            AssertUtil.isTrue(stageRule.getAmount().compareTo(BigDecimal.ZERO) > 0, "计费单价需大于0");
            AssertUtil.isTrue(stageRule.getStageCalculateRange().compareTo(BigDecimal.ZERO) > 0, "计算单位范围需大于0");
            BigDecimal rangeStart = stageRule.getRangeStart();
            BigDecimal rangeEnd = stageRule.getRangeEnd();
            AssertUtil.isTrue(Objects.nonNull(rangeEnd) || Objects.nonNull(rangeStart), "阶梯范围起始值、结束值都为空");
            AssertUtil.isTrue(Objects.isNull(rangeStart) || rangeStart.compareTo(BigDecimal.ZERO) >= 0, "阶梯范围起始值不能小于0");
            AssertUtil.isTrue(Objects.isNull(rangeEnd) || rangeEnd.compareTo(BigDecimal.ZERO) > 0, "阶梯范围结束值需大于0");
            boolean rangeValueValid = Objects.isNull(rangeStart) ||
                    (rangeStart.compareTo(lastEnd) == 0 && (Objects.isNull(rangeEnd) || rangeEnd.compareTo(lastEnd) > 0));
            AssertUtil.isTrue(rangeValueValid, "阶梯范围值异常");
            lastEnd = rangeEnd;
            //设置阶梯范围值
            BigDecimal stageRange = Objects.nonNull(rangeStart) && Objects.nonNull(rangeEnd) ? rangeEnd.subtract(rangeStart) : null;
            stageRange = Objects.isNull(stageRange) && Objects.nonNull(rangeEnd) ? rangeEnd : stageRange;
            if (Objects.nonNull(stageRange)) {
                BigDecimal multiple = stageRange.divide(stageRule.getStageCalculateRange(), 2, RoundingMode.HALF_UP);
                AssertUtil.isTrue(multiple.compareTo(multiple.setScale(0, RoundingMode.DOWN)) == 0, "阶梯范围需为计量范围倍数");
            }
            stageRule.setStageRange(stageRange);
        }
    }

    @Override
    public List<ConsumerSolutionOverviewVO> listOverviews(BaichuanConsumerSolutionReqDTO request, Long merchantId) {
        ConsumerSolutionQueryDTO query = new ConsumerSolutionQueryDTO();
        query.setMerchantId(merchantId);
        query.setName(request.getKeyword());
        ofNullable(request.getSettingId()).map(Lists::newArrayList).ifPresent(query::setSettingIds);
        ofNullable(request.getEquipmentTypeId()).map(Lists::newArrayList).ifPresent(query::setEquipmentTypeIds);
        if (!StringUtils.isEmpty(request.getEquipmentValue())) {
            List<Long> settingIds = RemoteResponseUtils.getData(
                    equipmentConfigurationFeignClient.listByEquipmentValueAndProtocol(request.getEquipmentValue()), Lists.newArrayList())
                    .stream().map(EquipmentConfigurationDTO::getId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(settingIds)) {
                return Lists.newArrayList();
            }
            if (Objects.nonNull(request.getSettingId())) {
                if (!settingIds.contains(request.getSettingId())) {
                    return Lists.newArrayList();
                }
            } else {
                query.setSettingIds(settingIds);
            }
        }
        List<BaichuanConsumerSolutionOverviewVO> list =
                RemoteResponseUtils.getData(baichuanConsumerSolutionClient.listConsumerSolution(query), Lists.newArrayList());
        List<Long> equipmentTypeIds = list.stream().map(BaichuanConsumerSolutionOverviewVO::getEquipmentTypeId)
                .filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<EquipmentTypeDTO> equipmentTypes = RemoteResponseUtils.getData(equipmentTypeClient.findByTypeIds(equipmentTypeIds),
                Lists.newArrayList());
        Map<Long, String> equipmentTypeMap = equipmentTypes.stream().collect(Collectors.toMap(EquipmentTypeDTO::getId, EquipmentTypeDTO::getName));
        return list.stream().map(item -> {
            ConsumerSolutionOverviewVO overview = ConsumerSolutionAssembler.INSTANCE.toConsumerSolutionOverviewVO(item);
            String equipmentTypeName = ofNullable(overview.getEquipmentTypeId()).map(equipmentTypeMap::get).orElse(null);
            overview.setEquipmentTypeName(equipmentTypeName);
            return overview;
        }).collect(Collectors.toList());
    }

    @Override
    public ConsumerSolutionDetailVO detail(Long id) {
        com.lyy.billing.interfaces.consumer.dto.baichuan.ConsumerSolutionDetailVO consumerSolution =
                RemoteResponseUtils.getData(baichuanConsumerSolutionClient.getConsumerSolutionDetail(id));
        if (Objects.isNull(consumerSolution)) {
            return null;
        }
        ConsumerSolutionDetailVO detail = ConsumerSolutionAssembler.INSTANCE.toConsumerSolutionDetailVO(consumerSolution);
        if (Objects.nonNull(detail.getEquipmentTypeId())) {
            RemoteResponseUtils.getData(equipmentTypeClient.findByTypeIds(Lists.newArrayList(detail.getEquipmentTypeId())),
                    Lists.newArrayList()).stream().findFirst().map(EquipmentTypeDTO::getName).ifPresent(detail::setEquipmentTypeName);
        }
        List<ConsumerSolutionCommodityVO> commodityList = consumerSolution.getCommodityList();
        if (CollectionUtils.isEmpty(commodityList)) {
            return detail;
        }
        List<ConsumerSolutionCommodityVO> rechargeCommodityList = commodityList.stream()
                .filter(commodity -> Objects.equals(commodity.getCategoryCode(), CategoryTwoEnum.SET_MEAL.getCode()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(rechargeCommodityList)) {
            List<RechargeCommodity> rechargeCommodities = rechargeCommodityList.stream().map(ConsumerSolutionAssembler.INSTANCE::toRechargeCommodity)
                    .collect(Collectors.toList());
            detail.setRechargeCommodityList(rechargeCommodities);
        }
        List<ConsumerSolutionCommodityVO> startCommodityList = commodityList.stream()
                .filter(commodity -> Objects.equals(commodity.getCategoryCode(), CategoryTwoEnum.DEVICE_SERVICE.getCode()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(startCommodityList)) {
            if (Objects.equals(detail.getModel(), ConsumerModelEnum.TIME.getCategory())) {
                MeteringCommodity meteringCommodity = startCommodityList.stream().findFirst()
                        .map(ConsumerSolutionAssembler.INSTANCE::toMeteringCommodity).orElse(null);
                if (Objects.nonNull(meteringCommodity)) {
                    List<StageRule> stageRuleList = ofNullable(meteringCommodity.getStageRuleList()).orElse(new ArrayList<>()).stream()
                            .sorted(Comparator.comparing(StageRule::getSort)).collect(Collectors.toList());
                    getStageRuleRangeValue(stageRuleList);
                    meteringCommodity.setStageRuleList(stageRuleList);
                    detail.setMeteringCommodity(meteringCommodity);
                }
            } else {
                List<StartCommodity> startCommodities = startCommodityList.stream().map(ConsumerSolutionAssembler.INSTANCE::toStartCommodity)
                        .collect(Collectors.toList());
                detail.setStartCommodityList(startCommodities);
            }
        }
        return detail;
    }

    private void getStageRuleRangeValue(List<StageRule> stageRuleList) {
        if (stageRuleList.size() > 1) {
            BigDecimal lastEnd = BigDecimal.ZERO;
            for (int i = 0; i < stageRuleList.size(); i++) {
                StageRule stageRule = stageRuleList.get(i);
                stageRule.setRangeStart(lastEnd);
                BigDecimal rangeEnd = Objects.nonNull(stageRule.getStageRange()) ? lastEnd.add(stageRule.getStageRange()) : null;
                stageRule.setRangeEnd(rangeEnd);
                lastEnd = rangeEnd;
            }
        }
    }

    @Override
    public Boolean equipmentAssociate(EquipmentAssociationReqDTO request, Long merchantId, Long adUserId) {
        EquipmentAssociationDTO equipmentAssociation = new EquipmentAssociationDTO();
        equipmentAssociation.setMerchantId(merchantId);
        equipmentAssociation.setOperator(adUserId);
        equipmentAssociation.setAddEquipmentIds(request.getAddEquipmentIds());
        equipmentAssociation.setRemoveEquipmentIds(request.getRemoveEquipmentIds());
        equipmentAssociation.setForceAssociation(request.getForceUpdate());
        equipmentAssociation.setConsumerSolutionId(request.getConsumerSolutionId());
        return RemoteResponseUtils.getDataThrowException(baichuanConsumerSolutionClient.equipmentAssociate(equipmentAssociation));
    }

    @Override
    public List<EquipmentConfigurationListVO> equipmentConfigurationList(String equipmentType, String equipmentValue) {
        List<EquipmentTypeDTO> equipmentTypeList = RespBodyUtil.checkResp(equipmentTypeClient.findByValues(Lists.newArrayList(equipmentType)));
        AssertUtil.assertNotEmpty(equipmentTypeList,"当前设备类型不存在");
        EquipmentConfigurationQueryReqDTO queryReqDTO = new EquipmentConfigurationQueryReqDTO();
        queryReqDTO.setEquipmentTypeId(equipmentTypeList.get(0).getId());
        queryReqDTO.setEquipmentValue(equipmentValue);
        List<EquipmentConfigurationListRespDTO> equipmentConfigurationList = RespBodyUtil.checkResp(equipmentConfigurationFeignClient.list(queryReqDTO));
        if (CollectionUtils.isEmpty(equipmentConfigurationList)){
            return null;
        }
        List<EquipmentConfigurationListVO> list =
                CommonConverterTools.convert(EquipmentConfigurationListVO.class, equipmentConfigurationList);
        for (EquipmentConfigurationListVO equipmentConfiguration : list) {
            ofNullable(equipmentConfiguration.getUnit()).map(ValueEum::getValueUnitByValue).map(ValueEum::getMessage)
                    .ifPresent(equipmentConfiguration::setUnitTypeName);
        }
        return list;
    }

    @Override
    public Page<ConsumerSolutionOverviewVO> pageOverviews(BaichuanConsumerSolutionPageReqDTO request, Long merchantId) {
        ConsumerSolutionPageQueryDTO query = new ConsumerSolutionPageQueryDTO();
        query.setMerchantId(merchantId);
        query.setName(request.getKeyword());
        ofNullable(request.getSettingId()).map(Lists::newArrayList).ifPresent(query::setSettingIds);
        ofNullable(request.getEquipmentTypeId()).map(Lists::newArrayList).ifPresent(query::setEquipmentTypeIds);
        query.setCurrent(request.getCurrent());
        query.setSize(request.getSize());
        if (!StringUtils.isEmpty(request.getEquipmentValue())) {
            List<Long> settingIds = RemoteResponseUtils.getData(
                            equipmentConfigurationFeignClient.listByEquipmentValueAndProtocol(request.getEquipmentValue()), Lists.newArrayList())
                    .stream().map(EquipmentConfigurationDTO::getId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(settingIds)) {
                return new Page<>();
            }
            if (Objects.nonNull(request.getSettingId())) {
                if (!settingIds.contains(request.getSettingId())) {
                    return new Page<>();
                }
            } else {
                query.setSettingIds(settingIds);
            }
        }
        Page<BaichuanConsumerSolutionOverviewVO> page =
                RemoteResponseUtils.getData(baichuanConsumerSolutionClient.pageConsumerSolution(query), new Page<>());
        Page<ConsumerSolutionOverviewVO> overviewPage = ConsumerSolutionAssembler.INSTANCE.toPageConsumerSolutionOverviewVO(page);
        List<ConsumerSolutionOverviewVO> list = overviewPage.getRecords();
        if (CollectionUtils.isEmpty(list)) {
            return new Page<>();
        }
        List<Long> equipmentTypeIds = list.stream().map(ConsumerSolutionOverviewVO::getEquipmentTypeId)
                .filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(equipmentTypeIds)) {
            return overviewPage;
        }
        List<EquipmentTypeDTO> equipmentTypes = RemoteResponseUtils.getData(equipmentTypeClient.findByTypeIds(equipmentTypeIds),
                Lists.newArrayList());
        Map<Long, String> equipmentTypeMap = equipmentTypes.stream().collect(Collectors.toMap(EquipmentTypeDTO::getId, EquipmentTypeDTO::getName));
        for (ConsumerSolutionOverviewVO overview : list) {
            String equipmentTypeName = ofNullable(overview.getEquipmentTypeId()).map(equipmentTypeMap::get).orElse(null);
            overview.setEquipmentTypeName(equipmentTypeName);
        }
        return overviewPage;
    }

    @Override
    public List<ConsumerSolutionGroupVO> findSolutionGroupsEquipment(ConsumerSolutionGroupReqDTO request) {
        Long consumerSolutionId = request.getConsumerSolutionId();
        List<EquipmentAssociatedGroupRespDTO> list = RemoteResponseUtils.getData(
                equipmentConfigurationFeignClient.associatedEquipmentList(consumerSolutionId));
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        List<ConsumerSolutionGroupVO> groups = new ArrayList<>();
        String queryStr = request.getQueryStr();
        List<Long> filterGroupIds = request.getGroupIds();
        for (EquipmentAssociatedGroupRespDTO dto : list) {
            ConsumerSolutionGroupVO group = ConsumerSolutionAssembler.INSTANCE.toConsumerSolutionGroupVO(dto);
            boolean groupMatch = CollectionUtils.isEmpty(filterGroupIds) || filterGroupIds.contains(group.getStoreId());
            if (groupMatch) {
                List<GroupEquipmentVO> equipmentList = group.getEquipmentList();
                if (!StringUtils.isEmpty(queryStr) && !CollectionUtils.isEmpty(equipmentList)) {
                    List<GroupEquipmentVO> filterEquipments = equipmentList.stream().filter(equipment -> {
                        String equipmentIdStr = String.valueOf(equipment.getEquipmentId());
                        String value = equipment.getValue();
                        return equipmentIdStr.contains(queryStr) || value.contains(queryStr);
                    }).collect(Collectors.toList());
                    group.setEquipmentList(filterEquipments);
                }
                groups.add(group);
            }
        }
        return groups.stream().filter(group -> !CollectionUtils.isEmpty(group.getEquipmentList())).collect(Collectors.toList());
    }

    @Override
    public Boolean deleteConsumerSolution(Long id, Long merchantId, Long adUserId) {
        ConsumerSolutionDeleteDTO param = new ConsumerSolutionDeleteDTO();
        param.setId(id);
        param.setMerchantId(merchantId);
        param.setOperator(adUserId);
        return RemoteResponseUtils.getDataThrowException(baichuanConsumerSolutionClient.delete(param));
    }

    @Override
    public Boolean existConfigurationRatio(EquipmentConfigurationExistRatioReqDTO reqDTO) {
        RespBody<Boolean> respBody = equipmentConfigurationFeignClient.existConfigurationRatio(reqDTO);
        if (!GlobalErrorCode.OK.getCode().equals(respBody.getCode())) {
            if (BillingErrorCode.PACKAGE_LACK_IMITATE_COINS.getCode().equals(respBody.getCode())) {
                log.debug("不存在配置比例,reqDTO:{}", reqDTO);
                throw new BusinessException(BusinessExceptionEnums.PACKAGE_LACK_IMITATE_COINS);
            }
            if (BillingErrorCode.PACKAGE_LACK_IMITATE_START_NUM.getCode().equals(respBody.getCode())) {
                log.debug("不存在配置比例,reqDTO:{}", reqDTO);
                throw new BusinessException(BusinessExceptionEnums.PACKAGE_LACK_IMITATE_START_NUM);
            }
            throw new BusinessException(BusinessExceptionEnums.PARAM_ERROR.getExCode(), respBody.getMessage());
        }
        return respBody.getBody();
    }

}
