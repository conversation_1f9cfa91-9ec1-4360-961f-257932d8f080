package cn.lyy.merchant.service.datasync;


import cn.lyy.equipment.dto.equipment.EquipmentDTO;

/**
 * <p>Title:saas2</p>
 * <p>Desc: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/10/8
 */
public interface DataSyncService {

    enum DataSyncTypeEnum {
        EQUIPMENT_REGISTER("register_equipment", "设备注册/转移"),
        EQUIPMENT_UNBIND("unbind_equipment", "设备解绑"),
        GROUP_SAVE("save_group", "场地新增"),
        GROUP_UPDATE("update_group", "场地更新/删除")
        ;

        String type;
        String desc;
        DataSyncTypeEnum(String type, String desc) {
            this.type = type;
            this.desc = desc;
        }

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 发送消息到mq
     * @param type
     * @param obj
     */
    void sendToDataSync(DataSyncTypeEnum type, String obj);

    /**
     * 发送设备相关数据到mq
     * @param type
     * @param equipment
     */
    void sendEquipmentToDataSync(DataSyncTypeEnum type, EquipmentDTO equipment);
}
