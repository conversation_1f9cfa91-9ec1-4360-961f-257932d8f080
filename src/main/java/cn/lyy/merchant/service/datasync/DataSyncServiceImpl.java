package cn.lyy.merchant.service.datasync;

import cn.lyy.base.converter.CommonConverterTools;
import cn.lyy.datasync.constants.DataSyncConstants;
import cn.lyy.datasync.dto.DataSyncDTO;
import cn.lyy.datasync.dto.EquipmentGroupSyncDTO;
import cn.lyy.datasync.dto.EquipmentSyncDTO;
import cn.lyy.equipment.dto.equipment.EquipmentDTO;
import cn.lyy.merchant.config.RabbitMqDataSyncConfig;
import cn.lyy.merchant.util.JsonUtils;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

import static java.util.Optional.ofNullable;

/**
 * <p>Title:saas2</p>
 * <p>Desc: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/10/8
 */
@Slf4j
@Service
public class DataSyncServiceImpl implements DataSyncService {

    @Autowired
    RabbitMqDataSyncConfig rabbitMqDataSyncConfig;

    @Override
    public void sendToDataSync(DataSyncTypeEnum type, String obj) {
        log.debug("================ {}，开始同步数据 {} 到1.0 =================", type.getDesc(), obj);
        switch (type) {
            case EQUIPMENT_REGISTER:
            case EQUIPMENT_UNBIND:
                EquipmentSyncDTO equipmentSyncDTO = JsonUtils.parse(obj, EquipmentSyncDTO.class);
                rabbitMqDataSyncConfig.sendEquipmentToDataSync(new DataSyncDTO<>(DataSyncConstants.METHOD_UPDATE_REGISTERORUNBIND, equipmentSyncDTO));
                break;
            case GROUP_SAVE:
                EquipmentGroupSyncDTO groupSyncDTO = JsonUtils.parse(obj, EquipmentGroupSyncDTO.class);
                rabbitMqDataSyncConfig.sendEquipmentGroupToDataSync(new DataSyncDTO<>(DataSyncConstants.METHOD_SAVE_EQUIPMENT_GROUP, groupSyncDTO));
                break;
            case GROUP_UPDATE:
                groupSyncDTO = JsonUtils.parse(obj, EquipmentGroupSyncDTO.class);
                rabbitMqDataSyncConfig.sendEquipmentGroupToDataSync(new DataSyncDTO<>(DataSyncConstants.METHOD_UPDATE_EQUIPMENT_GROUP, groupSyncDTO));
                break;
        }
    }

    @Override
    public void sendEquipmentToDataSync(DataSyncTypeEnum type, EquipmentDTO equipment) {
        // 注册、转移设备时同步数据到1.0
        EquipmentSyncDTO convert = CommonConverterTools.convert(EquipmentSyncDTO.class, equipment);
        convert.setLyyEquipmentId(ofNullable(equipment.getEquipmentId()).map(m -> m.intValue()).orElse(null));
        convert.setLyyEquipmentGroupId(ofNullable(equipment.getEquipmentGroupId()).map(m -> m.intValue()).orElse(null));
        convert.setLyyCityId(ofNullable(equipment.getLyyCityId()).map(m -> m.intValue()).orElse(null));
        convert.setLyyDistributorId(ofNullable(equipment.getDistributorId()).map(m -> m.intValue()).orElse(null));
        convert.setLyyDistrictId(ofNullable(equipment.getDistrictId()).map(m -> m.intValue()).orElse(null));
        convert.setLyyEquipmentTypeId(ofNullable(equipment.getEquipmentTypeId()).map(m -> m.intValue()).orElse(null));
        convert.setLyyFactoryId(ofNullable(equipment.getFactoryId()).map(m -> m.intValue()).orElse(null));
        convert.setAmount(ofNullable(equipment.getAmount()).orElse(BigDecimal.ZERO));
        sendToDataSync(type, JSON.toJSONString(convert));
    }
}
