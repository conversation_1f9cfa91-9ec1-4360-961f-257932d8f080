package cn.lyy.merchant.service.business;

import cn.lyy.merchant.util.SpringContextUtil;
import org.apache.commons.lang.StringUtils;

/**
 * @Description 工厂起始类
 * <AUTHOR>
 * @Date 2020/4/9
 * @Version 1.0
 **/
public class SaasBusinessFactory {
    /**
     * 获得对应设备类型工厂
     * @param equipmentTypeValue 设备类型
     * @return
     */
    public static IBusinessFactory create(String equipmentTypeValue){
        String factoryType = "_BusinessFactory";
        String common = "Common"+factoryType;
        String beanName = StringUtils.isNotBlank(equipmentTypeValue) ? equipmentTypeValue + factoryType : common;
        try{
            return SpringContextUtil.getBean(beanName);
        }catch(Exception e){
            return SpringContextUtil.getBean(common);
        }
    }
}
