package cn.lyy.merchant.service.business.wash;

import static java.util.Optional.ofNullable;

import cn.lyy.lyy_cmember_service_api.dto.wash.common.WashActivityMerchantSystemConfigQueryDTO;
import cn.lyy.lyy_cmember_service_api.dto.wash.common.response.WashActivityMerchantSystemConfigDTO;
import cn.lyy.lyy_cmember_service_api.dto.wash.common.response.WashActivityMerchantSystemConfigItemDTO;
import cn.lyy.lyy_cmember_service_api.enums.wash.WashActivityTypeEnum;
import cn.lyy.lyy_cmember_service_api.wash.WashActivityCommonClient;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 页面功能规则配置 洗水校园卡弹窗处理
 */
@Component
@Slf4j
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class WashActivityJudgeService {

    private final WashActivityCommonClient washActivityCommonClient;


    public Boolean campusCardMarketingShow(AdUserInfoDTO currentUser) {
        return campusCardStatus(currentUser, Boolean.FALSE);
    }

    public Boolean campusCardPopUp(AdUserInfoDTO currentUser) {
        return campusCardStatus(currentUser, Boolean.TRUE);
    }

    private Boolean campusCardStatus(AdUserInfoDTO currentUser, Boolean popUp) {
        // 输入参数校验
        if (currentUser == null || currentUser.getAdOrgId() == null) {
            log.warn("当前用户信息为空或商家ID为空");
            return Boolean.FALSE;
        }

        // 构造请求参数
        WashActivityMerchantSystemConfigQueryDTO requestDTO = new WashActivityMerchantSystemConfigQueryDTO();
        requestDTO.setMerchantId(currentUser.getAdOrgId());
        requestDTO.setProductTypeList(Collections.singletonList(WashActivityTypeEnum.CAMPUS_CARD.getCode()));

        // 查询商家配置
        WashActivityMerchantSystemConfigDTO activityMerchantDTO = ofNullable(
                washActivityCommonClient.queryMerchantSystemConfigList(requestDTO))
                .map(cn.lyy.base.communal.bean.BaseResponse::getData).orElse(null);

        log.info("校园卡查询商家配置:{}", activityMerchantDTO);
        if (activityMerchantDTO == null) {
            return Boolean.FALSE;
        }

        // 查找校园卡配置项
        WashActivityMerchantSystemConfigItemDTO campusCardConfig = findCampusCardConfig(activityMerchantDTO,
                WashActivityTypeEnum.CAMPUS_CARD.getCode());
        if (campusCardConfig == null) {
            log.info("未找到校园卡配置项");
            return Boolean.FALSE;
        }

        // 校验商家活动状态
        if (!isMerchantOpen(campusCardConfig)) {
            log.info("校园卡-商家活动状态已关闭");
            return Boolean.FALSE;
        }

        // 校验活动产品状态
        if (!isActivitySaleOpen(campusCardConfig)) {
            log.info("校园卡-活动产品状态为下架");
            return Boolean.FALSE;
        }
        if (Objects.equals(Boolean.TRUE, popUp)) {
            // 校验弹窗显示次数
            Integer popUpShowCount = Optional.ofNullable(campusCardConfig.getPopUpShowCount()).orElse(0);
            log.debug("校园卡-弹窗次数为{}",popUpShowCount);
            if (popUpShowCount == 0) {
                return Boolean.FALSE;
            }
        }
        return Boolean.TRUE;
    }

    // 辅助方法：查找校园卡配置项
    private WashActivityMerchantSystemConfigItemDTO findCampusCardConfig(WashActivityMerchantSystemConfigDTO activityMerchantDTO,
            Integer productType) {
        List<WashActivityMerchantSystemConfigItemDTO> items = ofNullable(activityMerchantDTO.getItems()).orElse(Collections.emptyList());
        return items.stream()
                .filter(item -> Objects.equals(item.getProductType(), productType))
                .findFirst()
                .orElse(null);
    }

    // 辅助方法：校验商家活动状态
    private boolean isMerchantOpen(WashActivityMerchantSystemConfigItemDTO config) {
        return Objects.equals(Boolean.TRUE, config.getMerchantOpenStatus());
    }

    // 辅助方法：校验活动产品状态
    private boolean isActivitySaleOpen(WashActivityMerchantSystemConfigItemDTO config) {
        return Objects.equals(Boolean.TRUE, config.getActivitySaleStatus());
    }


}
