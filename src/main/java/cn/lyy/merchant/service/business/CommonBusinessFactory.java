package cn.lyy.merchant.service.business;

import cn.lyy.merchant.constants.BusinessTypeEnums;
import cn.lyy.merchant.util.SpringContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Description 通用业务工厂
 * <AUTHOR>
 * @Date 2020/4/8
 * @Version 1.0
 **/
@Slf4j
@Component("Common_BusinessFactory")
public class CommonBusinessFactory implements IBusinessFactory {

    @Override
    public String getType() {
        return "Common";
    }

    @Override
    public <T extends AbstractBusinessProcess> T createConfirmPay(boolean defaultReturnCommon) {
        String beanName = getType() + BusinessTypeEnums.COMFIRM_PAY.getEvent();
        try{
            return SpringContextUtil.getBean(beanName);
        }catch (Exception e){
            log.error("通用处理类"+beanName+"不存在");
            return null;
        }
    }
}
