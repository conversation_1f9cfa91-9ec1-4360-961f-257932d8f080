package cn.lyy.merchant.service.business;

import cn.lyy.merchant.util.SpringContextUtil;
import lombok.Data;

import java.util.Optional;

/**
 * @Description 业务处理抽象类
 * <AUTHOR>
 * @Date 2020/4/8
 * @Version 1.0
 **/
@Data
public abstract class AbstractBusinessProcess {
    public abstract String getBusinessType();
    public <T extends AbstractBusinessProcess> T getCommon(){
        return SpringContextUtil.getBean("Common"+getBusinessType());
    }
    protected ThreadLocal<BusinessContext> businessContext = new ThreadLocal<>();

    public BusinessContext initBusinessContext(boolean clearContext){
        if(clearContext){
            clearBusinessContext();
            return initBusinessContext(new BusinessContext());
        }
        return initBusinessContext(Optional.ofNullable(this.businessContext.get()).orElse(new BusinessContext()));
    }

    public BusinessContext initBusinessContext(BusinessContext businessContext){
        this.businessContext.set(Optional.ofNullable(businessContext).orElse(new BusinessContext()));
        return this.businessContext.get();
    }

    //最后结束一定要清除
    protected void clearBusinessContext(){
        businessContext.remove();
    }
}
