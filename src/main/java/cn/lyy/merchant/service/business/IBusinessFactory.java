package cn.lyy.merchant.service.business;

import cn.lyy.merchant.constants.BusinessTypeEnums;
import cn.lyy.merchant.constants.SystemRoleEnum;
import cn.lyy.merchant.util.SpringContextUtil;
import org.apache.commons.lang.StringUtils;

/**
 * @Description 业务工厂接口
 * <AUTHOR>
 * @Date 2020/4/8
 * @Version 1.0
 **/
public interface IBusinessFactory {

    /**
     * 获取设备类型
     * @return
     */
    String getType();



    /**
     * 确认信息
     * @return
     */
    default <T extends AbstractBusinessProcess> T createConfirmPay(boolean defaultReturnCommon){
        return getProcessByBusinessType(BusinessTypeEnums.COMFIRM_PAY, defaultReturnCommon);
    }

    /**
     * 计费规则
     * @param systemRoleEnum    系统，B/C端
     * @param defaultReturnCommon
     * @return
     */
    default <T extends AbstractBusinessProcess> T feeRule(SystemRoleEnum systemRoleEnum, boolean defaultReturnCommon) {
        switch (systemRoleEnum) {
            case BUSINESS:
                return getProcessByBusinessType(BusinessTypeEnums.FEERULE, defaultReturnCommon);
            case CLIENT:
                return getProcessByBusinessType(BusinessTypeEnums.FEERULE_FILTER, defaultReturnCommon);
        }
        return getProcessByBusinessType(BusinessTypeEnums.FEERULE_FILTER, defaultReturnCommon);
    }

    /**
     * 设备启动
     * @param defaultReturnCommon
     * @param <T>
     * @return
     */
    default <T extends AbstractBusinessProcess> T eqStart(boolean defaultReturnCommon) {
        return getProcessByBusinessType(BusinessTypeEnums.EQ_START, defaultReturnCommon);
    }

    /**
     * 获取业务处理类
     * @param businessType 业务类型 ， 以下划线开头
//     * @param equipmentTypeValue 设备类型，例如 CDZ
     * @param defaultReturnCommon 是否返回通用处理类，如果找不到指定设备类型时可返回Common的，否则null
     * @param <T>
     * @return
     */
    default <T extends AbstractBusinessProcess> T getProcessByBusinessType(BusinessTypeEnums businessType, boolean defaultReturnCommon){
        String common = "Common"+businessType.getEvent();
        String equipmentTypeValue = getType();
        String beanName = StringUtils.isNotBlank(equipmentTypeValue) ? equipmentTypeValue + businessType.getEvent() : common;
        AbstractBusinessProcess process = null;
        try{
            process = SpringContextUtil.getBean(beanName);
        }catch (Exception e){
            process = defaultReturnCommon ? SpringContextUtil.getBean(common) : null;
        }
        process.initBusinessContext(true);
        return (T) process;
    }

}