package cn.lyy.merchant.service.equipment.impl;

import cn.lyy.equipment.dto.equipment.EquipmentDTO;
import cn.lyy.equipment.dto.protocol.ProtocolMainDTO;
import cn.lyy.equipment.service.ProtocolMicroService;
import cn.lyy.merchant.constants.BooleanEnum;
import cn.lyy.merchant.constants.BusinessExceptionEnums;
import cn.lyy.merchant.constants.EquipmentEventType;
import cn.lyy.merchant.constants.EquipmentRegisterRuleEnum;
import cn.lyy.merchant.dto.merchant.response.MerchantGroupDTO;
import cn.lyy.merchant.dto.request.EquipmentRegisterDTO;
import cn.lyy.merchant.dto.template.EquipmentRegisterTmplDTO;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.service.equipment.AbstractEquipmentRegisterTransfer;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2019/8/28
 * @Version 1.0
 **/
@Slf4j
@Service
public class EquipmentRegisterService extends AbstractEquipmentRegisterTransfer {

    private static final String STATUS_NORMAL = "1Normal";

    @Autowired
    private ProtocolMicroService protocolMicroService;

    public EquipmentRegisterService() {
        setEventType(EquipmentEventType.REGISTER);
    }

    @Override
    protected void checkEquipment(EquipmentRegisterDTO param, List<EquipmentDTO> equipments, EquipmentRegisterTmplDTO registerTmpl) {
        String batchRegister = registerTmpl.getBatchRegister();
        String batchRegisterRule = registerTmpl.getBatchRegisterRule();

        String equipmentType = registerTmpl.getEquipmentType();
        Long protocolId = 0L;
//        Long productId = 0L;
        for(EquipmentDTO equipment : equipments) {
//            if (isRegister(equipment)) {
//                throw new BusinessException(BusinessExceptionEnums.ALREADY_REGISTERED, equipment.getValue());
//            }
            if(BooleanEnum.YES.getValue().equals(batchRegister)) {
                // 默认需要同一品类
                if(StringUtils.isNotBlank(equipmentType) && ! registerTmpl.getEquipmentType().equals(equipmentType)) {
                    throw new BusinessException(BusinessExceptionEnums.ONLY_BATCH_REGISTER_ONE_TYPE);
                }
                // 如果是按协议的规则
                else if(EquipmentRegisterRuleEnum.PROTOCOL.getType().equals(batchRegisterRule) &&
                        Objects.nonNull(protocolId)) {
                    // 查询协议信息
                    List<ProtocolMainDTO> protocolMains = protocolMicroService.getProtocolMain(Lists.newArrayList(equipment.getUniqueCode())).getData();
                    if (protocolMains == null) {
                        throw new BusinessException(BusinessExceptionEnums.PROTOCOL_NOT_FOUND);
                    }
                    ProtocolMainDTO protocolMain = protocolMains.get(0);
                    if(protocolId > 0 && ! protocolId.equals(protocolMain.getProtocolId())) {
                        throw new BusinessException(BusinessExceptionEnums.ONLY_BATCH_REGISTER_SAME_FACTORY);
                    }
                    protocolId = protocolMain.getProtocolId();
                }
            }
        }
    }

    @Override
    protected void beforeUpdateEquipment(EquipmentRegisterDTO param , EquipmentDTO equipment , MerchantGroupDTO group) {
        equipment.setEquipmentGroupId(group.getEquipmentGroupId());
        equipment.setRegisterDate(LocalDateTime.now());
        equipment.setDistributorId(param.getDistributorId());
        equipment.setAmount(null);
        equipment.setDistrictId(group.getDistrictId());
        equipment.setName(group.getName());
        equipment.setAddress(group.getAddress());
        equipment.setStatus(STATUS_NORMAL);
        equipment.setInstallDate(equipment.getRegisterDate());
        equipment.setUpdated(LocalDateTime.now());
        equipment.setUpdatedby(param.getUserId());

        //by mjl 200512 判断设备版本是否2.0，如果不是就设置为2.0的，无论是1.0的设备号一样处理
        if(equipment.getDeviceType().equalsIgnoreCase("LYY")){
            equipment.setDeviceType("LYY2");
            log.debug("设置设备[{}]版本LYY2" , equipment.getUniqueCode());
        }
    }

}
