package cn.lyy.merchant.service.equipment.impl;

import cn.hutool.core.collection.CollUtil;
import cn.lyy.equipment.dto.equipment.RemoteCoinsDailyDTO;
import cn.lyy.equipment.dto.equipment.RemoteCoinsQueryDTO;
import cn.lyy.equipment.dto.equipment.RemoteCoinsRecordDTO;
import cn.lyy.equipment.service.RemoteCoinsMicroService;
import cn.lyy.merchant.api.service.MerchantGroupService;
import cn.lyy.merchant.dto.merchant.request.MerchantGroupRequest;
import cn.lyy.merchant.dto.merchant.response.MerchantGroupDTO;
import cn.lyy.merchant.service.equipment.RemoteStartService;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static java.util.Optional.ofNullable;

/**
 * 类描述：
 * <p>
 *
 * <AUTHOR>
 * @since 2020/11/10 11:30
 */
@Service
public class RemoteStartServiceImpl implements RemoteStartService {

    @Autowired
    private RemoteCoinsMicroService remoteCoinsMicroService;

    @Autowired
    private MerchantGroupService merchantGroupService;

    @Override
    public PageInfo<RemoteCoinsRecordDTO> recordPage(Integer pageIndex, Integer pageSize, String month, Long distributorId, Long adUserId) {
        // 获取有权限的场地 merchantGroupService.
        List<Long> groupIds = queryGroupByUser(distributorId, adUserId);
        RemoteCoinsQueryDTO query = new RemoteCoinsQueryDTO();
        query.setPageIndex(pageIndex);
        query.setPageSize(pageSize);
        query.setGroupIds(groupIds);
        query.setStartDate(LocalDate.parse(month.concat("-01"), DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        query.setEndDate(query.getStartDate().plusMonths(1L));
        return remoteCoinsMicroService.remoteCoinsRecord(query).getData();
    }

    @Override
    public List<RemoteCoinsDailyDTO> statisticsDaily(String month, Long distributorId, Long adUserId) {
        // 获取有权限的场地 merchantGroupService.
        List<Long> groupIds = queryGroupByUser(distributorId, adUserId);
        RemoteCoinsQueryDTO query = new RemoteCoinsQueryDTO();
        query.setGroupIds(groupIds);
        query.setStartDate(LocalDate.parse(month.concat("-01"), DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        query.setEndDate(query.getStartDate().plusMonths(1L));
        List<RemoteCoinsDailyDTO> list = remoteCoinsMicroService.statisticsDaily(query).getData();
        List<RemoteCoinsDailyDTO> result = new ArrayList<>();
        if (CollUtil.isEmpty(list)) {
            return result;
        }
        Map<String, RemoteCoinsDailyDTO> remoteCoinMap = list.stream()
                .collect(Collectors.toMap(k -> k.getDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")), v -> v));
        LocalDate current  = query.getStartDate();
        LocalDate lastDate = query.getEndDate();
        fillData(remoteCoinMap, result, current, lastDate);
        return result;
    }

    /**
     * 填充空数据
     * @param remoteCoinMap
     * @param result
     * @param current
     * @param lastDate
     */
    private void fillData(Map<String, RemoteCoinsDailyDTO> remoteCoinMap, List<RemoteCoinsDailyDTO> result, LocalDate current, LocalDate lastDate) {
        if (current.isEqual(lastDate)) {
            return;
        }
        String dateStr = current.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        RemoteCoinsDailyDTO daily = remoteCoinMap.get(dateStr);

        if (daily != null) {
            result.add(daily);
        } else {
            LocalDate date = LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            RemoteCoinsDailyDTO empty = new RemoteCoinsDailyDTO();
            empty.setDate(date);
            empty.setTimes(0);
            empty.setTotal(0);
            result.add(empty);
        }
        fillData(remoteCoinMap, result, current.plusDays(1L), lastDate);
    }
    /**
     * 查询用户有权限的场地id
     * @param userId
     * @param distributorId
     * @return
     */
    private List<Long> queryGroupByUser(Long distributorId, Long userId) {
        MerchantGroupRequest request = MerchantGroupRequest.builder()
                .adUser(userId)
                .distributor(distributorId)
                .isActive(1)
                .showGroupLabel(false)
                .build();
        List<MerchantGroupDTO> list = ofNullable(merchantGroupService.selectGroup(request).getData()).orElse(null);
        if (list != null) {
            return list.stream().map(MerchantGroupDTO::getEquipmentGroupId).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }
}
