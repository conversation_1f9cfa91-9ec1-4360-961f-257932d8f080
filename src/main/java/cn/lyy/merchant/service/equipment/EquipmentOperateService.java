package cn.lyy.merchant.service.equipment;

import cn.lyy.merchant.dto.request.EquipmentGetByCodeDTO;
import cn.lyy.merchant.dto.request.EquipmentRegisterDTO;
import cn.lyy.merchant.dto.response.BindCheckDTO;

/**
 * 类描述：
 * <p>
 *
 * <AUTHOR>
 * @since 2020/11/12 14:10
 */
public interface EquipmentOperateService {

    /**
     * 绑定设备确认
     * @return
     */
    BindCheckDTO bindCheck(EquipmentGetByCodeDTO param);

    /**
     * 注册设备
     * @param param
     */
    void bind(EquipmentRegisterDTO param);

    /**
     * 设置设备的计费模式
     * @param equipmentCode
     * @param feeMode
     */
    void settingEquipmentFeeMode(String equipmentCode,String feeMode);
}
