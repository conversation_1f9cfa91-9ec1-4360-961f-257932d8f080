package cn.lyy.merchant.service.equipment.impl;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.equipment.dto.equipment.EquipmentListResponseDTO;
import cn.lyy.equipment.service.IEquipmentService;
import cn.lyy.merchant.api.service.MerchantGroupService;
import cn.lyy.merchant.constants.BusinessExceptionEnums;
import cn.lyy.merchant.dto.merchant.request.MerchantGroupRequest;
import cn.lyy.merchant.dto.merchant.response.MerchantGroupDTO;
import cn.lyy.merchant.dto.request.QrCodeDownloadDTO;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.service.equipment.QrCodeService;
import cn.lyy.merchant.util.DateUtils;
import cn.lyy.merchant.utils.QrcodeUtil;
import cn.lyy.merchant.utils.ZipUtil;
import com.lyy.oss.service.impl.AliyunOss;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static java.util.Optional.ofNullable;

/**
 * <AUTHOR>
 * @date 2020/12/26 09:05
 * </p>
 */
@Service
@Slf4j
public class QrCodeServiceImpl implements QrCodeService {

    @Value("${project.path.tmp:./data/}")
    String tempDir;

    @Value("${project.qrcode.c:}")
    String qrcodeTempl;

    @Autowired
    private IEquipmentService equipmentService;
    @Autowired
    private MerchantGroupService groupService;


    private static final String ROOTPATH =
            new StringBuilder().append("%s").append("batch_qrcode").append(File.separator).append("%s").append(File.separator).toString();

    @Override
    public String batchCreate(QrCodeDownloadDTO dto, Long user) {
        String folderRootPath = String.format(ROOTPATH, tempDir, user);
        String curTimeStr = DateUtils.dateFormat(new Date(), DateUtils.DatePattern.yyyyMMddHHmmssSSS);
        String curTimePath = folderRootPath + curTimeStr + File.separator;

        List<Long> ids = dto.getDetails().stream().map(QrCodeDownloadDTO.Details::getEquipment).collect(Collectors.toList());
        List<EquipmentListResponseDTO> equipments =
                ofNullable(equipmentService.selectInfo(ids)).filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode())
                        .map(BaseResponse::getData).orElse(new ArrayList<>());
        if (!CollectionUtils.isEmpty(equipments)) {
            // 店铺
            List<Long> groups = equipments.stream().map(EquipmentListResponseDTO::getGroupId).collect(Collectors.toList());
            MerchantGroupRequest request = MerchantGroupRequest.builder().groups(groups).build();
            Map<Long, String> gMap =
                    ofNullable(groupService.selectGroup(request)).filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode())
                            .map(BaseResponse::getData).orElse(new ArrayList<>())
                            .stream().collect(Collectors.toMap(MerchantGroupDTO::getEquipmentGroupId, MerchantGroupDTO::getName,
                            (k1, k2) -> k1));
            // 设备
            Map<Long, EquipmentListResponseDTO> map =
                    equipments
                            .stream().collect(Collectors.toMap(EquipmentListResponseDTO::getEquipmentId, v -> v, (k1, k2) -> k1));

            dto.getDetails().forEach(item -> {
                EquipmentListResponseDTO e = map.get(item.getEquipment());
                String equipmentQRCode = qrcodeTempl.replace("{code}", e.getValue());
                String equipmentName = e.getTypeName() + "_" + e.getValue();
                String qrCodeTitle = e.getValue();
                String groupName = gMap.getOrDefault(e.getGroupId(), "_");
                createQRCode(curTimePath, equipmentQRCode, equipmentName, groupName, qrCodeTitle);
                if (!CollectionUtils.isEmpty(item.getWay())) {
                    item.getWay().forEach(w -> {
                        String qrCodeTitlePort = qrCodeTitle + " - " + w ;
                        createQRCode(curTimePath, equipmentQRCode + "/" + w, equipmentName + "-" + w, groupName, qrCodeTitlePort);
                    });
                }
            });

            log.debug("打包文件{}", curTimePath);
            File zipfile = ZipUtil.compress(curTimePath, null, null);
            log.debug("zip file: {} , size: {}", zipfile.getAbsolutePath(), zipfile.length());

            try {
                String url = AliyunOss.upload(AliyunOss.buildFileName("saas/3c/qrcode/", ".".concat("zip")), new FileInputStream(zipfile));
                return url;
            } catch (Exception e) {
                throw new BusinessException(BusinessExceptionEnums.OSS_UPLOAD_ERROR);
            }
        }
        throw new BusinessException(BusinessExceptionEnums.PARAM_ERROR);
    }

    private void createQRCode(String folderRoot, String content, String equipmentName, String groupName, String qrCodeTitle) {
        log.debug("createQRCode : root[{}]  , content[{}]  , name[{}] , group[{}]", folderRoot, content, equipmentName, groupName);
        //创建文件夹
        String folderPath = folderRoot + groupName;
        File rootFolder = new File(folderPath);
        rootFolder.mkdirs();

        File deviceQrcode = new File(folderPath + File.separator + equipmentName + ".png");
        boolean success = QrcodeUtil.write(content, qrCodeTitle, deviceQrcode);
        log.debug("create qrcode {},{}", deviceQrcode.getAbsolutePath(), success);
    }
}
