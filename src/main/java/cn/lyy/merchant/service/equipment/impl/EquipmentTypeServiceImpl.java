package cn.lyy.merchant.service.equipment.impl;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.equipment.dto.equipment.EquipmentTypeDTO;
import cn.lyy.equipment.service.IEquipmentTypeService;
import cn.lyy.merchant.api.service.AdOrgClient;
import cn.lyy.merchant.api.service.MerchantEquipmentService;
import cn.lyy.merchant.dto.equipment.EquipmentDistributorDTO;
import cn.lyy.merchant.exception.CustomAssert;
import cn.lyy.merchant.service.equipment.EquipmentTypeService;
import cn.lyy.merchant.utils.ResponseCheckUtil;
import com.lyy.equipment.interfaces.dto.equipment.resp.EquipmentDeviceTypeDTO;
import com.lyy.equipment.interfaces.feign.equipment.IotEquipmentServiceFeignClient;
import java.util.ArrayList;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import org.springframework.util.CollectionUtils;

import static java.util.Optional.ofNullable;

/**
 * @ClassName: EquipmentTypeServiceImpl
 * @description: TODO
 * @author: pengkun
 * @create: 2020-11-11 09:50
 * @Version 1.0
 **/
@Service
@Slf4j
public class EquipmentTypeServiceImpl implements EquipmentTypeService {

    @Autowired
    private MerchantEquipmentService merchantEquipmentService;

    @Autowired
    private IEquipmentTypeService equipmentTypeService;

    @Autowired
    private AdOrgClient adOrgClient;

    @Autowired
    private IotEquipmentServiceFeignClient iotEquipmentServiceFeignClient;

    /**
     * 获取用户所操作设备的所有设备类型
     * @param adUserId
     * @param orgId
     * @return
     */
    @Override
    public List<EquipmentTypeDTO> selectAllEquipmentType(Long adUserId,Long orgId) {
        boolean isPrimary = ofNullable(adOrgClient.checkPrimary(adUserId)).map(BaseResponse::getData).orElse(false);
        List<EquipmentTypeDTO> list = null;
        List<Long> typeIds = merchantEquipmentService.selectEquipmentType(orgId,isPrimary,adUserId,null).getData();
        if(typeIds != null && typeIds.size() > 0){
            list = equipmentTypeService.selectByIds(typeIds).getData();
        }
        return list;
    }

    @Override
    public List<cn.lyy.merchant.dto.equipment.EquipmentTypeDTO> getSupportBatchRegisterEquipmentType(Long orgId) {
        List<Long> equipmentIdList = ResponseCheckUtil.getData(merchantEquipmentService.findEquipmentIdsByMerchantId(orgId));
        if(CollectionUtils.isEmpty(equipmentIdList)){
            return new ArrayList<>();
        }
        List<EquipmentDeviceTypeDTO> deviceTypeList = ResponseCheckUtil
            .getData(iotEquipmentServiceFeignClient.getEquipmentIdListByDeviceTypeSelect(equipmentIdList));
        CustomAssert.assertNotNull(deviceTypeList,"查询设备类型失败");

        equipmentIdList= deviceTypeList.stream().filter(i -> "LYY2".equals(i.getDeviceType())).map(EquipmentDeviceTypeDTO::getId)
            .collect(Collectors.toList());

        if(CollectionUtils.isEmpty(equipmentIdList)){
            log.warn("未找到商家LYY2:{} 的设备信息",equipmentIdList);
            return new ArrayList<>();
        }
        EquipmentDistributorDTO dto = new EquipmentDistributorDTO();
        dto.setDistributorId(orgId);
        dto.setEquipmentIdList(equipmentIdList);
        List<cn.lyy.merchant.dto.equipment.EquipmentTypeDTO> data=
            ResponseCheckUtil.getData(merchantEquipmentService.getSupportBatchRegisterEquipmentTypeNew(dto));
        if(CollectionUtils.isEmpty(data)){
            log.warn("未找到商家:{} 的设备信息",orgId);
            return new ArrayList<>();
        }
        return data;
    }
}
