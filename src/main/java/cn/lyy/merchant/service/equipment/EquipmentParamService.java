package cn.lyy.merchant.service.equipment;

import cn.lyy.tools.equipment.OpenSettingInfo;

import java.util.Collection;
import java.util.Map;

/**
 * 设备的主板参数设置
 * <AUTHOR>
 * @className: EquipmentParamService
 * @date 2020/12/17
 */
public interface EquipmentParamService {


    /**
     * 从1.0的saas中查询自定义参数信息
     *     查不到情况，采用异步方式
     * @param uniqueCode
     * @param functionCode
     * @param data
     * @param adUserId
     * @return
     */
    String queryParamInfoFromS1ByAsync(String uniqueCode, String functionCode, String data,Long adUserId,String toUniqueId);

    /**
     * 从1.0的saas中查询自定义参数信息
     * @param uniqueCode
     * @param functionCode
     * @param data
     * @return
     */
    String queryParamInfoFromS1(String uniqueCode, String functionCode, String data);

    /**
     * 用1.0的方式批量设置参数
     * @param infoList
     * @return
     */
    Map<String, Boolean> batchSettingParamInfoFromS1(Collection<OpenSettingInfo> infoList);

    Object s2ParamConfigList(String uniqueCode);
}
