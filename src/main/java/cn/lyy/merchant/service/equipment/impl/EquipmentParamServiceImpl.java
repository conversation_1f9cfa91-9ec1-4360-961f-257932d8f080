package cn.lyy.merchant.service.equipment.impl;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.equipment.constant.ClientTypeEnum;
import cn.lyy.equipment.dto.machine.ComponentValueRange;
import cn.lyy.equipment.dto.operation.OperationParam;
import cn.lyy.equipment.service.EquipmentRedisService;
import cn.lyy.equipment.service.EquipmentService;
import cn.lyy.merchant.async.EquipmentParamAsync;
import cn.lyy.merchant.constants.BusinessExceptionEnums;
import cn.lyy.merchant.dto.equipment.EquipmentExtendParamJsonDTO;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.service.equipment.EquipmentParamService;
import cn.lyy.tools.constants.open.FunctionConstants;
import cn.lyy.tools.equipment.OpenSettingInfo;
import cn.lyy.websocket.constant.MessageTypeEnum;
import cn.lyy.websocket.mq.WebSocketMessageProducer;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.Optional.ofNullable;

/**
 *
 * <AUTHOR>
 * @className: EquipmentParamServiceImpl
 * @date 2020/12/17
 */
@Service
@Slf4j
public class EquipmentParamServiceImpl implements EquipmentParamService {

    @Resource
    private EquipmentService equipmentService;

    @Resource
    private EquipmentRedisService equipmentRedisService;



    @Resource
    private EquipmentParamAsync equipmentParamAsync;
    /**
     * 从1.0的saas中查询自定义参数信息
     *
     * @param uniqueCode
     * @param functionCode
     * @param data
     * @return
     */
    @Override
    public String queryParamInfoFromS1ByAsync(String uniqueCode, String functionCode, String data,Long adUserId,String toUniqueId) {

        //判读设备是否在线
        Map<String, Boolean>  onLineMap = getEquipmentOnLinkStatus(Arrays.asList(uniqueCode));
        boolean online = onLineMap.get(uniqueCode);
        if(!online) {
            throw new BusinessException(BusinessExceptionEnums.EQUIPMENT_OFFLINE);
        }
        String key = getEquipmentRedisKey(uniqueCode, functionCode, data).toString();
        equipmentRedisService.del(key);

        OperationParam param = new OperationParam();
        param.setUniqueCode(uniqueCode);
        param.setIdentity(functionCode);
        param.setParam(ofNullable(data).orElse(""));
        BaseResponse baseResponse = equipmentService.operation(param);
        boolean success = ofNullable(baseResponse).filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode()).isPresent();
        if(!success){
            throw new BusinessException(BusinessExceptionEnums.OPERATE_ERROR);
        }
        //查功能的最多3秒,因为不需要查主板
        int checkTime = FunctionConstants.BSYS_SAAS_QUERY_FUNCTION.equals(functionCode)?3:10;
        String settingInfo2 = null;

        // 待功能上线，此处代码再删除  2021年3月12日09:11:57
//        for (int i = 0; i < checkTime*2; i++) {
//            log.info("第{}次查询",i);
//            BaseResponse<String> response = equipmentRedisService.getString(key.toString());
//            if(ResponseCodeEnum.SUCCESS.getCode() == response.getCode() && org.apache.commons.lang3.StringUtils.isNoneBlank(response.getData())){
//                settingInfo2 = response.getData();
//                break;
//            }
//            //每隔500毫秒查1次
//            try {
//                Thread.sleep(500);
//            } catch (InterruptedException e) {
//                e.printStackTrace();
//                break;
//            }
//        }

        BaseResponse<String> response = equipmentRedisService.getString(key.toString());
        if(ResponseCodeEnum.SUCCESS.getCode() == response.getCode() && org.apache.commons.lang3.StringUtils.isNoneBlank(response.getData())){
                settingInfo2 = response.getData();
        }else {
            // 如查询不到，此任务采用异步查询，由websocket通知
            if(adUserId != null) {
                equipmentParamAsync.getEquipmentParam(toUniqueId, functionCode,data, key);
            }
        }
//        log.info("设备自定义参数再次查,KEY:{},settingInfo: {}", key, settingInfo2);
//        if(org.springframework.util.StringUtils.isEmpty(settingInfo2)){
//            throw new BusinessException(BusinessExceptionEnums.GET_PARAM_FAIL);
//        }
        return settingInfo2;
    }


    @Override
    public String queryParamInfoFromS1(String uniqueCode, String functionCode, String data){
        //判读设备是否在线
        Map<String, Boolean>  onLineMap = getEquipmentOnLinkStatus(Arrays.asList(uniqueCode));
        boolean online = onLineMap.get(uniqueCode);
        if(!online) {
            throw new BusinessException(BusinessExceptionEnums.EQUIPMENT_OFFLINE);
        }
        String key = getEquipmentRedisKey(uniqueCode, functionCode, data).toString();
        equipmentRedisService.del(key);

        OperationParam param = new OperationParam();
        param.setUniqueCode(uniqueCode);
        param.setIdentity(functionCode);
        param.setParam(ofNullable(data).orElse(""));
        BaseResponse baseResponse = equipmentService.operation(param);
        boolean success = ofNullable(baseResponse).filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode()).isPresent();
        if(!success){
            throw new BusinessException(BusinessExceptionEnums.OPERATE_ERROR);
        }
        //查功能的最多3秒,因为不需要查主板
        int checkTime = FunctionConstants.BSYS_SAAS_QUERY_FUNCTION.equals(functionCode)?3:10;
        String settingInfo2 = null;

        for (int i = 0; i < checkTime*2; i++) {
            log.info("第{}次查询",i);
            BaseResponse<String> response = equipmentRedisService.getString(key.toString());
            if(ResponseCodeEnum.SUCCESS.getCode() == response.getCode() && org.apache.commons.lang3.StringUtils.isNoneBlank(response.getData())){
                settingInfo2 = response.getData();
                break;
            }
            //每隔500毫秒查1次
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                e.printStackTrace();
                break;
            }
        }

        log.info("设备自定义参数再次查,KEY:{},settingInfo: {}", key, settingInfo2);
        if(org.springframework.util.StringUtils.isEmpty(settingInfo2)){
            throw new BusinessException(BusinessExceptionEnums.GET_PARAM_FAIL);
        }
        return settingInfo2;
    }

    /**
     * 用1.0的方式批量设置参数
     *
     * @param infoList
     * @return
     */
    @Override
    public Map<String, Boolean> batchSettingParamInfoFromS1(Collection<OpenSettingInfo> infoList) {
        if(infoList == null || infoList.isEmpty()){
            return new HashMap<>(0);
        }
        List<String> uniqueCodeList = infoList.stream().map(OpenSettingInfo::getUniqueCode).collect(Collectors.toList());

        Map<String, Boolean> onlineMap = getEquipmentOnLinkStatus(uniqueCodeList);
        //parallelStream 进行异步处理
        Map<String, Boolean> resultMap = infoList.parallelStream()
                .collect(Collectors.toMap(OpenSettingInfo::getUniqueCode,
                        //map的value值，为是否设置成功
                        info-> {
                            Boolean online = onlineMap.get(info.getUniqueCode());
                            if (online == null || !online) {
                                return false;
                            }
                            // 删除参数缓存
                            String key = getEquipmentRedisKey(info.getUniqueCode(),info.getFunctionCode(),info.getData()).toString();
                            equipmentRedisService.del(key);

                            OperationParam param = new OperationParam();
                            param.setUniqueCode(info.getUniqueCode());
                            param.setIdentity(info.getFunctionCode());
                            param.setParam(info.getData());
                            boolean success = ofNullable(equipmentService.operation(param)).filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode()).isPresent();
                            return success;
                        }));

        return resultMap;
    }

    @Override
    public Object s2ParamConfigList(String uniqueCode) {
//        if(equipmentIds.size() > 1) {
//            List<EquipmentManageDO> equipments = baseMapper.selectByBatchIds(equipmentIds);
//            boolean same = checkSameProduct(equipments.stream().map(m -> m.getUniqueCode()).collect(Collectors.toList()));
//            if(! same) {
//                throw new SaasException(BusinessCodeEnums.DIFFERENT_PROTOCOL);
//            }
//        }
//        Long equipmentId = equipmentIds.get(0);
//        EquipmentManageDO equipmentProduct = ofNullable(baseMapper.getEquipmentProduct(equipmentId)).orElseThrow(() -> new SaasException("设备不存在！"));
//        String uniqueCode = ofNullable(equipmentProduct.getUniqueCode()).orElseThrow(() -> new SaasException("设备不存在！"));

//        //判读设备是否在线
//        boolean online = getEquipmentOnLinkStatus(Arrays.asList(uniqueCode)).get(uniqueCode);
//        if(!online) {
//            throw new BusinessException(BusinessExceptionEnums.EQUIPMENT_OFFLINE);
//        }
//
//        // TODO 后续需让门户操作将设备参数数据存入缓存！
//        Long productId = null;
//        EquipmentExtendParamJsonDTO paramJsonDTO = new EquipmentExtendParamJsonDTO();
//        paramJsonDTO.setBoardId(productId);
//        // 获取按钮
//        List<EquipmentExtendParamDO> boardButtons = factoryMotherboardMapper.getBoardButton(productId);
//        if(CollectionUtils.isEmpty(boardButtons)) {
//            return Lists.newArrayList();
//        }
//        List<ButtonsBean> buttons = Lists.newArrayList();
//        paramJsonDTO.setButtons(buttons);
//        Map<Long, ButtonsBean> buttonIdBeanMap = boardButtons.stream().collect(Collectors.toMap(k -> k.getButtonId(), v -> {
//            ButtonsBean b = paramConverter.to(v);
//            List<ParamsBean> paramsBeans = Lists.newLinkedList();
//            b.setParams(paramsBeans);
//            buttons.add(b);
//            return b;
//        }));
//        List<Long> buttonIds = boardButtons.stream().map(m -> m.getButtonId()).collect(Collectors.toList());
//        // 获取参数
//        List<EquipmentExtendParamDO> boardButtonParams = factoryMotherboardMapper.getBoardButtonParam(buttonIds);
//        if(CollectionUtils.isEmpty(boardButtonParams)) {
//            return paramJsonDTO.getButtons();
//        }
//        Map<Long, EquipmentExtendParamDO> paramIdBeanMap = boardButtonParams.stream().collect(Collectors.toMap(k -> k.getParamId(), v -> v));
//        List<Long> buttonParamIds = paramIdBeanMap.entrySet().stream().map(m -> m.getKey()).collect(Collectors.toList());
//        // 获取选择列表
//        List<EquipmentExtendParamDO> boardButtonSelects = factoryMotherboardMapper.getBoardButtonSelect(buttonParamIds);
//        // 排序
//        paramIdBeanMap.entrySet().stream().sorted(Comparator.comparing(v -> v.getValue().getCreated())).forEach(entry -> {
//            Long paramId = entry.getKey();
//            EquipmentExtendParamDO v = entry.getValue();
//            ParamsBean bean = paramConverter.toParam(v);
//            if("select".equals(bean.getComponentType())) {
//                List<ComponentValueArray> collect = boardButtonSelects.stream().filter(f -> paramId.equals(f.getParamId())).map(m -> new ComponentValueArray(m.getValue(), m.getText())).collect(Collectors.toList());
//                bean.setComponentJson(JSON.toJSONString(collect));
//            } else if("switch".equals(bean.getComponentType())) {
//                ComponentValueSwitch sw = new ComponentValueSwitch();
//                sw.setOpen(v.getComponentValueSwitchOpen());
//                sw.setClose(v.getComponentValueSwitchClose());
//                bean.setComponentJson(JSON.toJSONString(sw));
//            } else {
//                ComponentValueRange range= new ComponentValueRange();
//                range.setMin(v.getComponentValueMin());
//                range.setMax(v.getComponentValueMax());
//                bean.setComponentJson(JSON.toJSONString(range));
//            }
//            ButtonsBean buttonsBean = buttonIdBeanMap.get(v.getButtonId());
//            buttonsBean.getParams().add(bean);
//        });
//        RedisClient.set(RedisKeys.PORTAL_PRODUCT_FUNCTION_PUBLISH + productId, JSON.toJSONString(paramJsonDTO));
//
//        List<ButtonsBean> expands;
//        if(Objects.isNull(paramJsonDTO) || CollectionUtils.isEmpty(expands = paramJsonDTO.getButtons())) {
//            throw new SaasException("未匹配到参数设置记录！");
//        }
//
//        return expands;


        return null;
    }

    /**
     * 获取设备的在线状态
     * @param uniqueCodeList
     * @return
     */
    private Map<String, Boolean> getEquipmentOnLinkStatus(List<String> uniqueCodeList){
        BaseResponse<Map<String, Boolean>> response = equipmentService.equipmentStatus(uniqueCodeList);
        if (ResponseCodeEnum.SUCCESS.getCode() != response.getCode() || response.getData() == null) {
            throw new BusinessException(BusinessExceptionEnums.OPERATE_ERROR);
        }
        return response.getData();
    }


    /**
     * 获取redis的key
     * @param uniqueCode
     * @param functionCode
     * @param data
     * @return
     */
    private StringBuilder getEquipmentRedisKey(String uniqueCode, String functionCode, String data) {
        StringBuilder key = new StringBuilder("Esetting_open_").append(functionCode).append("_").append(uniqueCode);
        if (StringUtils.isNotBlank(data)) {
            HashMap<String, String> dataMap = new Gson().fromJson(data, new TypeToken<HashMap<String, String>>() {
            }.getType());
            String cmd = dataMap.get("cmd");
            if (StringUtils.isNotBlank(cmd)) {
                key.append("_").append(cmd);
            }
        }
        return key;
    }

}
