package cn.lyy.merchant.service.equipment;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.equipment.dto.EquipmentCDZAttachDTO;
import cn.lyy.equipment.dto.equipment.EquipmentDTO;
import cn.lyy.equipment.dto.equipment.EquipmentFuncQueueDTO;
import cn.lyy.equipment.dto.equipment.RegisterItemDTO;
import cn.lyy.equipment.service.EquipmentRedisService;
import cn.lyy.equipment.service.EquipmentService;
import cn.lyy.equipment.service.IEquipmentAttachService;
import cn.lyy.equipment.service.IEquipmentFuncQueueService;
import cn.lyy.merchant.api.service.*;
import cn.lyy.merchant.constants.*;
import cn.lyy.merchant.dto.equipment.CommoditySettingsSaveDTO;
import cn.lyy.merchant.dto.equipment.EquipmentFactoryIdDTO;
import cn.lyy.merchant.dto.merchant.AgentUserDTO;
import cn.lyy.merchant.dto.merchant.response.MerchantGroupDTO;
import cn.lyy.merchant.dto.request.EquipmentOperateSyncDTO;
import cn.lyy.merchant.dto.request.EquipmentRegisterDTO;
import cn.lyy.merchant.dto.request.FeeCommodityDTO;
import cn.lyy.merchant.dto.template.EquipmentRegisterTmplDTO;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.microservice.SystemChargeEquipmentApi;
import cn.lyy.merchant.service.agent.LyyAgentService;
import cn.lyy.merchant.service.business.SaasBusinessFactory;
import cn.lyy.merchant.service.datasync.DataSyncService;
import cn.lyy.merchant.service.feerule.AbstractFeeRuleBusinessProcess;
import cn.lyy.merchant.service.refund.StartRefundService;
import cn.lyy.merchant.util.CommonUtil;
import cn.lyy.merchant.util.EquipmentUtils;
import cn.lyy.tools.constants.merchant.EquipmentTypeConstant;
import com.lyy.charge.dto.equipment.EquipmentStatusChangeDTO;
import com.lyy.charge.enums.equipment.EquipmentStatusMsgTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import static cn.lyy.merchant.redis.MerchantRedisKeyEnum.EQUIPMENT_INTER_WORKING;
import static java.util.Optional.ofNullable;

@Slf4j
public abstract class AbstractEquipmentRegisterTransfer {

    @Autowired
    private EquipmentService equipmentService;

    @Autowired
    private RegisterTemplateClient registerTemplateClient;

    @Autowired
    private MerchantEquipmentService merchantEquipmentService;

    @Autowired
    private MerchantGroupService merchantGroupService;

    @Autowired
    private StartRefundService startRefundService;

    @Autowired
    private AdOrgClient adOrgClient;

    @Autowired
    private DataSyncService dataSyncService;

    @Autowired
    private LyyAgentService lyyAgentService;

    @Autowired
    private IEquipmentFuncQueueService equipmentFuncQueueService;

    @Autowired
    private IEquipmentAttachService equipmentAttachService;

    @Resource
    private EquipmentRedisService equipmentRedisService;

    private EquipmentEventType eventType;

    @Value("${charging-equipment-start-report-queue:cn.lyy.charging.equipment.start.queue}")
    private String chargingEquipmentQueue;

    @Value("${charging-equipment-start-report-exchange:cn.lyy.equipment-start-report-exchange}")
    private String chargingEquipmentExchange;

    @Value("${car-charging-equipment-start-report-queue:cn.lyy.car.charging.equipment.start.queue}")
    private String carChargingEquipmentQueue;

    @Value("${washer-equipment-start-report-queue:cn.lyy.washer.equipment.start.queue}")
    private String washerEquipmentQueue;

    @Value("${general-pulse-equipment-start-report-queue:cn.lyy.general.pulse.equipment.start.queue}")
    private String generalPulseEquipmentQueue;

    @Autowired
    private SystemChargeEquipmentApi systemChargeApi;

    @Autowired
    private AgentClient agentClient;


    private final int maxRegisterNum = 200;

    protected void setEventType(EquipmentEventType eventType){
        this.eventType = eventType;
    }

    protected EquipmentEventType getEventType(){
        return this.eventType;
    }

    /**
     * 检查设备信息
     * @param param
     * @param equipments
     */
    protected abstract void checkEquipment(EquipmentRegisterDTO param, List<EquipmentDTO> equipments, EquipmentRegisterTmplDTO registerTmpl);

    /**
     * 更新设备信息前处理
     * @param param
     * @param equipment
     * @param newGroup
     */
    protected abstract void beforeUpdateEquipment(EquipmentRegisterDTO param , EquipmentDTO equipment , MerchantGroupDTO newGroup);

    /**
     * 获取设备信息
     * @param values
     * @return
     */
    protected List<EquipmentDTO> getEquipments(List<String> values) {

        return equipmentService.getEquipmentByValueList(values).getData();
    }


    public void process(EquipmentRegisterDTO param) {
        Map<String, RegisterItemDTO> updateMap = new HashMap<>(param.getEquipments().size());
        param.getEquipments().forEach(e -> {
            e.setGroupId(param.getGroupId());
            updateMap.put(e.getCode(), e);
        });
        // 检查是否占用了机台号
        Boolean groupNumberInUsed = equipmentService.doesGroupNumberInUsed(param.getEquipments()).getData();
        if (groupNumberInUsed) {
            throw new BusinessException(BusinessExceptionEnums.GROUP_NUM_ERROR);
        }
        List<String> codes = param.getEquipments().stream().map(RegisterItemDTO::getCode).collect(Collectors.toList());
        codes = codes.stream().filter(CommonUtil.distinctByKey(String::toString)).collect(Collectors.toList());
        if(codes.size() > maxRegisterNum){
            throw new BusinessException(BusinessExceptionEnums.BATCH_REGISTER_NUM);
        }
        String equipmentType = param.getEquipmentType();
        Long groupId = param.getGroupId();

        // 获取注册模板
        EquipmentRegisterTmplDTO registerTmpl = ofNullable(registerTemplateClient.queryByEquipmentType(equipmentType))
                .map(BaseResponse::getData)
                .orElseThrow(() -> new BusinessException(BusinessExceptionEnums.REGISTER_TEMPLATE_MISSING));
        if(BooleanEnum.NO.getValue().equals(registerTmpl.getBatchRegister()) && codes.size() > 1) {
            throw new BusinessException(BusinessExceptionEnums.NOT_SUPPORT_BATCH_REGISTER);
        }

        log.debug("计费设置方法：{}，实际统一为按设备", registerTmpl.getFeeRuleType());

        // 获取并校验设备信息
        List<EquipmentDTO> equipments = ofNullable(equipmentService.getEquipmentByValueList(codes))
                .map(BaseResponse::getData)
                .orElseThrow(() -> new BusinessException(BusinessExceptionEnums.DEVICE_NOT_EXISTS));
        checkEquipment(param , equipments, registerTmpl);

        AbstractFeeRuleBusinessProcess feeRuleBusinessProcess = SaasBusinessFactory.create(equipmentType).feeRule(SystemRoleEnum.BUSINESS, true);

        Long factoryId = null;
        for(EquipmentDTO equipment: equipments) {
            Long equipmentId = equipment.getEquipmentId();
            try {
                if(Objects.isNull(factoryId)) {
                    EquipmentFactoryIdDTO equipmentFactoryId = merchantEquipmentService.getEquipmentFactoryId(equipmentId).getData();
                    factoryId = Objects.nonNull(equipmentFactoryId.getFirstFactoryId()) ? equipmentFactoryId.getFirstFactoryId() : equipmentFactoryId.getDefaultFactoryId();
                }
            } catch (Exception e) {
            }


            MerchantGroupDTO newGroup = ofNullable(merchantGroupService.getGroup(groupId))
                    .map(BaseResponse::getData)
                    .orElseThrow(() -> new BusinessException(BusinessExceptionEnums.GROUP_NOT_EXIST));

            beforeUpdateEquipment(param , equipment , newGroup);

            equipment.setGroupNumber(ofNullable(updateMap.get(equipment.getValue()))
                    .map(RegisterItemDTO::getGroupNumber).map(Long::new).orElse(null));
            equipment.setRemarks(ofNullable(updateMap.get(equipment.getValue()))
                    .map(RegisterItemDTO::getRemarks).orElse(null));

            boolean success = registerEquipment(equipment);
            if (!success) {
                throw new BusinessException(BusinessExceptionEnums.REGISTER_FAIL, equipment.getValue());
            }

            //将扩展信息保存到设备扩展表中
            if(EquipmentTypeEnums.CHARGING_PILE_FOR_CAR.getValue().equalsIgnoreCase(equipmentType)){
                saveCarChargingEquipmentAttachInfo(equipment,ofNullable(updateMap.get(equipment.getValue()))
                        .map(RegisterItemDTO::getExtendParam).orElse(""),param.getUserId());
            }else if(EquipmentTypeEnums.CHARGING_PILE.getValue().equalsIgnoreCase(equipmentType)){
                // 充电桩处理扩展信息
                String groupServiceCostWay = null;
                for(FeeCommodityDTO feeCommodityDTO:param.getFeeRule()){
                    groupServiceCostWay = feeCommodityDTO.getClassifyCode();
                    break;
                }
                saveChargingEquipmentAttachInfo(equipment,groupServiceCostWay,param.getUserId(),param.getIsTransfer());
                //更新缓存
                if (!param.getIsTransfer()) {
                    equipmentRedisService.setEx(EQUIPMENT_INTER_WORKING.getKey() + equipment.getUniqueCode(),"3.0", EQUIPMENT_INTER_WORKING.getTimeout());
                }
            }

            //保存设备队列信息
            saveEquipmentFuncQueue(equipment,equipmentType);

            // 注册、转移设备时同步数据到1.0
            dataSyncService.sendEquipmentToDataSync(DataSyncService.DataSyncTypeEnum.EQUIPMENT_REGISTER, equipment);

            //如果是注册就同步商家id到工厂agent表
            if(this.eventType.equals(EquipmentEventType.REGISTER)){
                log.debug("syncToFactoryAgent : {}" , equipment.getValue());
                merchantEquipmentService.syncToAgent(equipment.getValue());
            }

            //设备注册 推送到 商业化平台
            sendEquipmentRegisterMsg(equipment);

        }
        // 保存或者更新计费规则
        long stime = System.currentTimeMillis();
        feeRuleBusinessProcess.saveOrUpdateFeeRule(equipments, param);
        feeRuleBusinessProcess.saveOrUpdateRecharge(equipments,param);
        long etime = System.currentTimeMillis();
        log.debug("保存商品套餐，耗时 {}",(etime - stime));
        feeRuleBusinessProcess.afterSave(equipments, param);
        // 保存计费规则配置：展不展示标题、单位
        CommoditySettingsSaveDTO saveParam = new CommoditySettingsSaveDTO();
        saveParam.setEquipmentId(equipments.stream().map(EquipmentDTO::getEquipmentId).collect(Collectors.toList()));
        saveParam.setDistributorId(param.getDistributorId());
        saveParam.setShowTitle(param.getShowTitle() ? BooleanNumEnum.TRUE.getValue():BooleanNumEnum.FLASE.getValue());
        saveParam.setShowUnit(param.getShowUnit() ? BooleanNumEnum.TRUE.getValue():BooleanNumEnum.FLASE.getValue());
        log.debug("保存计费规则设置");
        merchantEquipmentService.saveCommoditySetting(saveParam);

        // 同步工厂配置，检测是否开启自动退费，默认开启
        startRefundService.syncFactorySetting(param.getDistributorId(), param.getUserId(), factoryId, param.getGroupId(), param.getEquipmentType());
        //处理1.0商户注册2.0设备时，商户渠道关系处理(异步)

        adOrgClient.channelLogSave(param.getDistributorId());

        // 注册 转移 同步状态给工厂端，用于流量卡
//        try {
//            lyyAgentService.equipmentOperate(EquipmentOperateSyncDTO.create(param.getDistributorId(), codes, EquipmentEventType.REGISTER.equals(eventType) ? 1 : 3));
//        } catch (Exception e) {
//            log.error("流量卡，设备[{}]操作同步到工厂失败: {}" , codes , e.getMessage());
//        }
    }


    //设备注册 推送到 商业化平台
    private void sendEquipmentRegisterMsg(EquipmentDTO equipment){
        //推送注册消息到收费服务
        try {
            EquipmentStatusChangeDTO equipmentStatusChangeDTO = new EquipmentStatusChangeDTO();
            equipmentStatusChangeDTO.setMsgType(EquipmentStatusMsgTypeEnum.EQUIPMENT_REGISTER.getMsgType());

            //设置代理商
            AgentUserDTO agentInfo = ofNullable(agentClient.getAgentInfoByEquipmentValue(equipment.getValue()))
                    .map(BaseResponse::getData)
                    .orElse(null);
            equipmentStatusChangeDTO.setAgentUserId(agentInfo == null ? 0: agentInfo.getAgentUserId().longValue());
            //设备注册时间
            equipmentStatusChangeDTO.setRegistrationDate(Date.from( equipment.getRegisterDate().atZone( ZoneId.systemDefault()).toInstant()));
            equipmentStatusChangeDTO.setEquipmentValue(equipment.getValue());
            if (equipment.getEquipmentTypeId() != null) {
                equipmentStatusChangeDTO.setEquipmentTypeId(equipment.getEquipmentTypeId().longValue());
            }
            if (Objects.nonNull(equipment.getDistributorId())) {
                equipmentStatusChangeDTO.setDistributorId(equipment.getDistributorId());
            }
            if (equipment.getEquipmentGroupId() != null) {
                equipmentStatusChangeDTO.setGroupId(equipment.getEquipmentGroupId().longValue());
            }
            if (equipment.getEquipmentId() != null) {
                equipmentStatusChangeDTO.setEquipmentId(equipment.getEquipmentId().longValue());
            }
            log.info("equipmentStatusChangeDTO is {}",equipmentStatusChangeDTO);
            systemChargeApi.sendEquipmentChangeMsg(equipmentStatusChangeDTO);
        }catch (Exception e){
            log.error("设备id:{} 推送注册消息到收费服务失败: {}",equipment.getEquipmentId() ,e.getMessage());
        }
    }

    private void saveChargingEquipmentAttachInfo(EquipmentDTO equipment, String groupServiceCostWay, Long userId, Boolean isTransfer){
        EquipmentCDZAttachDTO equipmentCDZAttachDTO = new EquipmentCDZAttachDTO();
        equipmentCDZAttachDTO.setAdUserId(userId);
        equipmentCDZAttachDTO.setGroupServiceCostWay(groupServiceCostWay);
        equipmentCDZAttachDTO.setLyyEquipmentId(equipment.getEquipmentId());
        if (!isTransfer) {
            equipmentCDZAttachDTO.setRegisterSystem("3.0");
        }
        BaseResponse<Boolean> baseResponse = equipmentAttachService.saveOrUpdateCDZAttachInfo(equipmentCDZAttachDTO);
        log.debug("保存充电桩拓展信息，结果：{}",baseResponse);
    }

    /**
     * 调用设备服务注册设备
     * @param equipment
     * @return
     */
    protected boolean registerEquipment(EquipmentDTO equipment) {
        cn.lyy.equipment.dto.equipment.EquipmentRegisterDTO equipmentRegisterDTO = new cn.lyy.equipment.dto.equipment.EquipmentRegisterDTO();
        equipmentRegisterDTO.setEquipmentGroupId(equipment.getEquipmentGroupId());
        equipmentRegisterDTO.setDistributorId(equipment.getDistributorId());
        equipmentRegisterDTO.setEquipmentTypeId(equipment.getEquipmentTypeId());
        equipmentRegisterDTO.setEquipmentValue(equipment.getValue());
        equipmentRegisterDTO.setGroupNumber(equipment.getGroupNumber());
        equipmentRegisterDTO.setRemarks(equipment.getRemarks());

        equipmentRegisterDTO.setAdUser(equipment.getUpdatedby());
        equipmentRegisterDTO.setCityId(equipment.getLyyCityId());
        equipmentRegisterDTO.setDistrictId(equipment.getDistrictId());
        equipmentRegisterDTO.setName(equipment.getName());
        equipmentRegisterDTO.setStatus(equipment.getStatus());
        equipmentRegisterDTO.setDeviceType(equipment.getDeviceType());

        BaseResponse resp = equipmentService.register(equipmentRegisterDTO);
        return resp.getCode() == ResponseCodeEnum.SUCCESS.getCode();
    }

    /**
     * 保存设备队列
     * @param equipment
     * @param equipmentTypeValue
     */
    private void saveEquipmentFuncQueue(EquipmentDTO equipment,String equipmentTypeValue){
        EquipmentFuncQueueDTO queueDTO = new EquipmentFuncQueueDTO();
        queueDTO.setEquipment(equipment.getEquipmentId());
        queueDTO.setEquipmentValue(equipment.getValue());
        queueDTO.setUniqueCode(equipment.getUniqueCode());
        // 暂时只灰度充电桩品类的设备，后续灰度其他品类设备需要进行处理
        if(EquipmentTypeConstant.CDZ.getCode().equals(equipmentTypeValue) || EquipmentTypeEnums.MCCDZ.getValue().equals(equipmentTypeValue)){
            queueDTO.setQueue(chargingEquipmentQueue);
        }else if(EquipmentTypeConstant.XYJ.getCode().equals(equipmentTypeValue)){
            queueDTO.setQueue(washerEquipmentQueue);
        }else if(EquipmentTypeEnums.CHARGING_PILE_FOR_CAR.getValue().equalsIgnoreCase(equipmentTypeValue)){
            //汽车充电桩
            queueDTO.setQueue(carChargingEquipmentQueue);
//        }else if(EquipmentTypeConstant.ETL.getCode().equals(equipmentTypeValue)){
        }else if(EquipmentUtils.checkPulse(equipmentTypeValue)){
            //脉冲设备
            queueDTO.setQueue(generalPulseEquipmentQueue);
        }else {
            //非充电桩暂时不处理
            return;
        }
        queueDTO.setExchange(chargingEquipmentExchange);
        equipmentFuncQueueService.cleanQueueCacheByUniqueCode(equipment.getUniqueCode());

        queueDTO.setExchange(chargingEquipmentExchange);
        BaseResponse<Boolean> response = equipmentFuncQueueService.save(queueDTO);
        log.info("保存设备队列,queueDTO：{},结果：{}",queueDTO,response);
    }

    /**
     * 保存汽车充电桩拓展信息
     * @param equipment 设备信息
     * @param extendParam   汽车充电桩拓展信息
     * @param userId   当前操作用户
     */
    private void saveCarChargingEquipmentAttachInfo(EquipmentDTO equipment,String extendParam,Long userId){
        if(StringUtils.isNotBlank(extendParam)){
            BaseResponse<Boolean> baseResponse = equipmentAttachService.saveOrUpdateCarChargingAttachInfo(equipment.getEquipmentId(),extendParam,userId);
            log.debug("保存汽车充电桩拓展信息，结果：{}",baseResponse);
        }
    }

    /**
     * 判断设备是否已经注册
     * @param equipment
     * @return
     */
    protected boolean isRegister(EquipmentDTO equipment) {
        return Objects.nonNull(equipment.getRegisterDate()) && Objects.nonNull(equipment.getDistributorId());
    }
}
