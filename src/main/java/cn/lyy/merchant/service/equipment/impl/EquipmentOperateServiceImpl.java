package cn.lyy.merchant.service.equipment.impl;

import cn.hutool.core.lang.Assert;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.base.util.StringUtil;
import cn.lyy.equipment.dto.EquipmentInfoDTO;
import cn.lyy.equipment.dto.equipment.EquipmentTypeDTO;
import cn.lyy.equipment.dto.equipment.EquipmentTypeFunctionDTO;
import cn.lyy.equipment.dto.operation.OperationParam;
import cn.lyy.equipment.dto.protocol.ProtocolMainDTO;
import cn.lyy.equipment.service.EquipmentService;
import cn.lyy.equipment.service.IEquipmentTypeService;
import cn.lyy.equipment.service.ProtocolMicroService;
import cn.lyy.merchant.api.service.MerchantEquipmentService;
import cn.lyy.merchant.api.service.MerchantGroupService;
import cn.lyy.merchant.api.service.RegisterTemplateClient;
import cn.lyy.merchant.constants.*;
import cn.lyy.merchant.dto.equipment.CommoditySettingsDTO;
import cn.lyy.merchant.dto.merchant.response.MerchantGroupDTO;
import cn.lyy.merchant.dto.request.EquipmentGetByCodeDTO;
import cn.lyy.merchant.dto.request.EquipmentRegisterDTO;
import cn.lyy.merchant.dto.request.FeeCommodityDTO;
import cn.lyy.merchant.dto.response.BindCheckDTO;
import cn.lyy.merchant.dto.response.FeeModeVO;
import cn.lyy.merchant.dto.response.FeeRuleUnitDTO;
import cn.lyy.merchant.dto.response.RetResultVO;
import cn.lyy.merchant.dto.template.EquipmentRegisterTmplDTO;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.service.commodity.FeeRuleService;
import cn.lyy.merchant.service.equipment.EquipmentOperateService;
import cn.lyy.merchant.service.iot.LyyOpenIotService;
import cn.lyy.merchant.util.FeeRuleUnitGenerater;
import com.google.common.collect.Lists;
import com.lyy.commodity.rpc.constants.CategoryEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static java.util.Optional.ofNullable;

/**
 * 类描述：设备相关操作处理
 * <p>
 *
 * <AUTHOR>
 * @since 2020/11/12 14:11
 */
@Slf4j
@Service
public class EquipmentOperateServiceImpl implements EquipmentOperateService {

    @Autowired
    private EquipmentService equipmentService;

    @Autowired
    private ProtocolMicroService protocolMicroService;

    @Autowired
    private IEquipmentTypeService equipmentTypeService;

    @Autowired
    private MerchantGroupService merchantGroupService;

    @Autowired
    private LyyOpenIotService lyyOpenIotService;

    @Autowired
    private RegisterTemplateClient registerTemplateClient;

    @Autowired
    private EquipmentRegisterService equipmentRegisterService;

    @Autowired
    private FeeRuleService feeRuleService;

    @Autowired
    private MerchantEquipmentService merchantEquipmentService;

    /**
     * 请求设备服务超时时间，单位：毫秒
     */
    private static int REQUEST_DEVICE_SERVICE_TIMEOUT = 12000;

    @Override
    public BindCheckDTO bindCheck(EquipmentGetByCodeDTO param) {

        String attachEquipmentType = null;
        String equipmentValue = param.getCode();
        Long protocolId = param.getProtocolId();

        EquipmentInfoDTO equipmentInfo = ofNullable(equipmentService.getEquipmentInfoByValue(equipmentValue))
                .map(BaseResponse::getData).orElse(null);
        if (equipmentInfo == null) {
            throw new BusinessException(BusinessExceptionEnums.DEVICE_NOT_EXISTS);
        }

        String equipmentTypeStr = param.getEquipmentType();
        if(equipmentTypeStr == null){
            equipmentTypeStr = equipmentInfo.getEquipmentTypeValue();
            Assert.notNull(equipmentTypeStr,"设备类型不能为空");
        }
        if(equipmentTypeStr.equals("XYJ")){
            // 洗衣机类型，默认加载 加液机套餐
            attachEquipmentType = "JYJ";
        }

        boolean registered = isRegister(equipmentInfo);

        checkAndUpdateEquipmentTypeAndFactoryId(equipmentInfo);

        BindCheckDTO registerVO = new BindCheckDTO();
        registerVO.setCode(equipmentValue);
        registerVO.setRemarks(equipmentInfo.getRemarks());
        registerVO.setEquipmentId(equipmentInfo.getEquipmentId().longValue());
        registerVO.setGroupNumber(equipmentInfo.getGroupNumber());
        //拓展信息
        registerVO.setExtendParam(equipmentInfo.getExtendParam());
        //查询设备类型
        EquipmentTypeDTO equipmentType = new EquipmentTypeDTO();
        equipmentType.setEquipmentTypeId(equipmentInfo.getEquipmentTypeId().longValue());
        equipmentType.setValue(equipmentInfo.getEquipmentTypeValue());
        equipmentType.setName(equipmentInfo.getEquipmentTypeName());
        equipmentType.setType(equipmentInfo.getType());

        registerVO.setEquipmentType(equipmentType);
        // 获取注册模板
        EquipmentRegisterTmplDTO registerTmpl = ofNullable(registerTemplateClient.queryByEquipmentType(equipmentType.getValue())).map(BaseResponse::getData).orElse(null);
        if (registerTmpl == null) {
            throw new BusinessException(BusinessExceptionEnums.REGISTER_TEMPLATE_MISSING);
        }
        registerVO.setRegisterTmpl(registerTmpl);
        registerVO.setIfBatchRegister("Y".equals(registerTmpl.getBatchRegister()));

        if(registerVO.getIfBatchRegister()) {
            // 如果是按协议的规则
            if("protocol".equals(registerTmpl.getBatchRegisterRule())) {
                // 查询协议信息
                List<ProtocolMainDTO> protocolMains = ofNullable(protocolMicroService.getProtocolMain(Lists.newArrayList(equipmentInfo.getUniqueCode())))
                        .map(BaseResponse::getData)
                        .orElse(new ArrayList<>());
                if (!protocolMains.isEmpty()) {
                    ProtocolMainDTO protocolMain = protocolMains.get(0);
                    registerVO.setProductId(protocolMain.getProductId());
                    registerVO.setProtocolId(protocolMain.getProtocolId());
                }
            }
        } else {
            if(StringUtils.isNotBlank(equipmentTypeStr) || Objects.nonNull(protocolId)) {
                throw new BusinessException(BusinessExceptionEnums.NOT_SUPPORT_BATCH_REGISTER);
            }
        }
        List<FeeRuleUnitDTO> unitList = querySupportUnit(equipmentInfo, 1);
        if (registered) {

            String categoryCode = getCategory(equipmentType.getValue());
            // 如果是已注册的，返回设备当前的计费规则
            registerVO.setFeeRuleList(feeRuleService.listByCondition(equipmentInfo.getDistributorId().longValue(),
                    equipmentInfo.getEquipmentGroupId().longValue(), equipmentInfo.getEquipmentTypeId().longValue(),
                    equipmentInfo.getEquipmentId().longValue(), categoryCode, null));

            if(StringUtil.isNotEmpty(attachEquipmentType)){
                BaseResponse<EquipmentTypeDTO> etypeResponse =  equipmentTypeService.getByValue(attachEquipmentType);
                Long attachEquipmentTypeId =  Optional.ofNullable(etypeResponse).filter(r->r.getCode()== ResponseCodeEnum.SUCCESS.getCode()).map(r->r.getData().getEquipmentTypeId()).orElseThrow(()->new BusinessException(etypeResponse.getMessage()));
                registerVO.setAttachFeeRuleList(feeRuleService.listByCondition(equipmentInfo.getDistributorId().longValue(),
                        equipmentInfo.getEquipmentGroupId().longValue(), attachEquipmentTypeId,
                        equipmentInfo.getEquipmentId().longValue(), categoryCode, null));
            }

        }
        // 获取当前的计费模式、
        String feeMode = FeeModeEnum.getFeeModeByCode(
                getFeeRuleMode(equipmentInfo, registerVO.getFeeRuleList())
        ).getCode();
        // 查询支持的计费模式
        List<FeeModeVO> feeModeList = listFeeMode(registerTmpl.getFeeRuleMode(), equipmentInfo.getValue(), feeMode);
        registerVO.setFeeModeList(feeModeList);
        registerVO.setFeeUnitList(unitList);

        // 查询商户的配置
        CommoditySettingsDTO commoditySettings = null;
        if (registered) {
            commoditySettings = merchantEquipmentService.queryCommoditySettings(equipmentInfo.getDistributorId().longValue(), equipmentInfo.getEquipmentId().longValue()).getData();
        }
        if (commoditySettings == null) {
            registerVO.setShowTitle(true);
            registerVO.setShowUnit(true);
        } else {
            registerVO.setShowTitle(Objects.equals(commoditySettings.getShowTitle(), BooleanNumEnum.TRUE.getValue()));
            registerVO.setShowUnit(Objects.equals(commoditySettings.getShowUnit(), BooleanNumEnum.TRUE.getValue()));
        }
        if (equipmentInfo.getEquipmentGroupId() != null) {
            MerchantGroupDTO lyyEquipmentGroup = ofNullable(merchantGroupService.getGroup(equipmentInfo.getEquipmentGroupId())).map(BaseResponse::getData).orElse(new MerchantGroupDTO());
            registerVO.setEquipmentGroup(lyyEquipmentGroup);
        }
        registerVO.setRegisterStatus(registered ? BindCheckDTO.RegisterStatus.REGISTERED.getStatus() : BindCheckDTO.RegisterStatus.AVAILABLE.getStatus());
        return registerVO;
    }

    /**
     * 获取每个品类对应计费规则的商品分类
     * @param value
     * @return
     */
    private String getCategory(String value) {

        switch (value) {
            case "QCCDZ":
                return CategoryEnum.VIRTUAL_COMBINE.getCode();
            default:
                return CategoryEnum.DEVICE_SERVICE.getCode();
        }
    }

    private List<FeeModeVO> listFeeMode(String registerTmplFeeRuleMode, String equipmentCode, String currentFeeMode) {
        List<FeeModeVO> feeModeVOList = new ArrayList<>();

        // 按时间或电量（功率）计费，目前是充电桩
        if (FeeRuleModeSettingEnum.TIME_ELEC.getKey().equals(registerTmplFeeRuleMode)
                && null != equipmentCode) {
            List<EquipmentTypeFunctionDTO> feeModeList = protocolMicroService.typeFunctionByEquipment(equipmentCode, ProtocolCodeEnum.FEE_MODE.name()).getData();

            if (CollectionUtils.isNotEmpty(feeModeList)) {
                for (EquipmentTypeFunctionDTO typeFunc : feeModeList) {
                    String typeFuncName = typeFunc.getName();

                    // 当前设备的计费标准放在首位
                    if (StringUtil.isNotEmpty(currentFeeMode)
                            && currentFeeMode.equalsIgnoreCase(typeFuncName)) {
                        feeModeVOList.add(0, settingFeeMode(typeFuncName));
                        continue;
                    }
                    feeModeVOList.add(settingFeeMode(typeFuncName));
                }
            }
        }
        if(FeeRuleModeSettingEnum.MULTI.getKey().equals(registerTmplFeeRuleMode)){
            //组合模式的需要改为支持哪几种组合的数据
            List<EquipmentTypeFunctionDTO> feeModeList = protocolMicroService.typeFunctionByEquipment(equipmentCode, ProtocolCodeEnum.FEE_MODE.name()).getData();
            ofNullable(feeModeList).ifPresent(list -> {
                 List<FeeModeVO> feeModeVOList1 = list.stream().map(typeFunc -> {
                    String feeRuleMode = typeFunc.getName();
                    return FeeModeVO.ofFeeModeEnum(feeRuleMode);
                }).collect(Collectors.toList());
                log.debug("{} 设备为组合模式,查询得到模式为 {}",equipmentCode,feeModeVOList1);
                feeModeVOList.addAll(feeModeVOList1);
            });

        }
        if (feeModeVOList.isEmpty() ) {
            feeModeVOList.add(FeeModeVO.ofFeeModeEnum(currentFeeMode));
        }

        return feeModeVOList;
    }

    private FeeModeVO settingFeeMode(String feeModeCode) {
        FeeModeEnum feeModeEnum = FeeModeEnum.getFeeModeByCode(feeModeCode);

        FeeModeVO feeModeVO = new FeeModeVO();
        feeModeVO.setName(feeModeEnum.getName());
        feeModeVO.setCode(feeModeEnum.getCode());
        feeModeVO.setUnitDesc(feeModeEnum.getUnitDesc());

        return feeModeVO;
    }

    private List<FeeRuleUnitDTO> querySupportUnit(EquipmentInfoDTO equipment, Integer level) {
        List<FeeRuleUnitDTO> feeRuleUnitList = new ArrayList<>();

        // 计费规则模式
        String settingFeeRuleModeStyle = this.getFeeRuleMode(equipment, null);

        // 计费模式是 组合模式，查找主板配置支持哪几种计费单位
        if (FeeRuleModeSettingEnum.MULTI.getKey().equalsIgnoreCase(settingFeeRuleModeStyle)
                && null != equipment.getEquipmentId()) {

            // 计费规则：按时长、按次数
            List<EquipmentTypeFunctionDTO> feeModeList = protocolMicroService.typeFunctionByEquipment(equipment.getValue(), ProtocolCodeEnum.FEE_MODE.name()).getData();
            ofNullable(feeModeList).ifPresent(list -> {
                list.stream().forEach(typeFunc -> {
                    String feeRuleMode = typeFunc.getName();
                    FeeRuleUnitDTO feeRuleUnit = FeeRuleUnitGenerater.generater(FeeRuleModeSettingEnum.getByKey(settingFeeRuleModeStyle), equipment.getEquipmentTypeValue(), feeRuleMode, level);
                    feeRuleUnitList.add(feeRuleUnit);
                });
            });
        }



        if (feeRuleUnitList.isEmpty()) {
            feeRuleUnitList.add(this.getFeeRuleUnit(equipment, level, null));
        }

        return feeRuleUnitList;
    }

    private FeeRuleUnitDTO getFeeRuleUnit(EquipmentInfoDTO equipment, Integer level, String currentFeeMode) {
        // 区分按场地、按设备
        EquipmentRegisterTmplDTO registerTmpl = ofNullable(registerTemplateClient.queryByEquipmentType(equipment.getEquipmentTypeValue()))
                .map(BaseResponse::getData)
                .orElseThrow(() -> new BusinessException(BusinessExceptionEnums.REGISTER_TEMPLATE_MISSING));

        // 计费规则模式
        String settingFeeRuleModeStyle = registerTmpl.getFeeRuleMode();

        // 获取单位
        String feeRuleMode = null;
        if(EquipmentTypeEnums.isCDZLogic(equipment.getEquipmentTypeValue())) {
            if(StringUtils.isNotEmpty(currentFeeMode)) {
                feeRuleMode = currentFeeMode;
            } else {
                // 计费规则：按时长、按电量
                feeRuleMode = getFeeRuleMode(equipment, null);
            }
        }

        if (FeeModeEnum.MULTI.getCode().equalsIgnoreCase(settingFeeRuleModeStyle)) {
            if (StringUtils.isNotEmpty(currentFeeMode)) {
                feeRuleMode = currentFeeMode;
            } else {
                // 组合模式，默认单位次数
                feeRuleMode = FeeRuleModeSettingEnum.NUMBER.getKey();
            }
        }
        if(EquipmentTypeEnums.AMMETER.getValue().equalsIgnoreCase(equipment.getEquipmentTypeValue())) {
            List<EquipmentTypeFunctionDTO> feeModeList = protocolMicroService.typeFunctionByEquipment(equipment.getValue(), ProtocolCodeEnum.FEE_MODE.name()).getData();
            if(CollectionUtils.isNotEmpty(feeModeList)) {
                feeRuleMode = feeModeList.get(0).getName();
            }

        }

        FeeRuleUnitDTO feeRuleUnit = FeeRuleUnitGenerater.generater(FeeRuleModeSettingEnum.getByKey(settingFeeRuleModeStyle), equipment.getEquipmentTypeValue(), feeRuleMode, level);
        return feeRuleUnit;
    }

    private String getFeeRuleMode(EquipmentInfoDTO equipmentInfo, List<FeeCommodityDTO> feeRuleList) {
        // 区分按场地、按设备
        String equipmentTypeValue = equipmentInfo.getEquipmentTypeValue();
        EquipmentRegisterTmplDTO registerTmpl = ofNullable(registerTemplateClient.queryByEquipmentType(equipmentTypeValue))
                .map(BaseResponse::getData)
                .orElseThrow(() -> new BusinessException(BusinessExceptionEnums.REGISTER_TEMPLATE_MISSING));

        // 计费规则模式
        String settingFeeRuleModeStyle = registerTmpl.getFeeRuleMode();

        // 获取单位
        String feeRuleMode = null;
        // 由于充电桩可以让商户手动配置计费标准，所以需要特殊处理，默认都是取注册规则的计费标准
        if(EquipmentTypeEnums.isCDZLogic(equipmentTypeValue)) {
            // 计费规则：按时长、按电量
            /* 改成商品中心后, 没办法查原来的套餐表
            SaasFeeRuleProp prop = feeRuleProp(UserInfoDTO.createOrg(distributorId), equipmentTypeId, equipmentId, FeeModePropEnum.RULE_TYPE.getPropkey());
            if(Objects.nonNull(prop)) {
                feeRuleMode = prop.getPropValue();
            }
             */
            if (CollectionUtils.isNotEmpty(feeRuleList)) {
                feeRuleMode = feeRuleList.get(0).getClassifyCode();
            }
            // 充电桩默认按时长
            if (StringUtil.isEmpty(feeRuleMode)) {
                //汽车桩默认电量计费
                if(EquipmentTypeEnums.CHARGING_PILE_FOR_CAR.getValue().equalsIgnoreCase(equipmentTypeValue)) {
                    feeRuleMode = FeeRuleModeTimeElecEnum.ELEC.getCode();
                } else {
                    feeRuleMode = FeeRuleModeSettingEnum.TIME.getKey();
                }
            }
        }
        if(EquipmentTypeEnums.AMMETER.getValue().equalsIgnoreCase(equipmentTypeValue)) {
            List<EquipmentTypeFunctionDTO> feeModeList = protocolMicroService.typeFunctionByEquipment(equipmentInfo.getValue(), ProtocolCodeEnum.FEE_MODE.name()).getData();
            if(CollectionUtils.isNotEmpty(feeModeList)) {
                feeRuleMode = feeModeList.get(0).getName();
            }
        }

        if (StringUtil.isEmpty(feeRuleMode)) {
            feeRuleMode = settingFeeRuleModeStyle;
        }

        return feeRuleMode;
    }

    @Override
    public void bind(EquipmentRegisterDTO param) {
        equipmentRegisterService.process(param);
    }

    /**
     * 发送指令作为异步操作
     * @param equipmentCode
     * @param feeMode
     */
    @Override
    public void settingEquipmentFeeMode(String  equipmentCode, String feeMode) {
        int iotFeeModeCode = FeeModeEnum.getCDZIotFeeModeByCode(Integer.parseInt(feeMode));
        log.info("settingEquipmentFeeMode {}",Thread.currentThread().getName());
//        IotEquipmentAttachParam operationParam = new IotEquipmentAttachParam();
//        operationParam.setFeeMode(iotFeeModeCode);
        JSONObject param = new JSONObject();
        param.put("feeMode",iotFeeModeCode);
        BaseResponse response = operation(equipmentCode, ProtocolFunctionConstants.CDZ_SET_MODE, false, param.toString());
         //如下发指令异常，支持报错返回
        Optional.ofNullable(response).filter(res -> res.getCode() == ResponseCodeEnum.SUCCESS.getCode()).orElseThrow(()-> new BusinessException(response.getMessage()));
    }


    /**
     *
     * @param deviceNo
     * @param fn
     * @param sync 同步或异步操作
     * @param jsonParamStr
     * @return
     */
    private BaseResponse operation(String deviceNo, String fn, boolean sync, String jsonParamStr) {
        OperationParam operationParam = new OperationParam();
        operationParam.setUniqueCode(deviceNo);
        operationParam.setSync(sync);
        operationParam.setIdentity(fn);
        operationParam.setTimeout(REQUEST_DEVICE_SERVICE_TIMEOUT);
        if(StringUtils.isNotBlank(jsonParamStr)) {
            operationParam.setParam(jsonParamStr);
        }else {
            operationParam.setParam("{}");
        }
        // 暂时用不到，先屏蔽
//        if(fn.toUpperCase().contains("STARTUP")) {
//            // 设备启动异步，需要传递mq配置
//            operationParam.setCallbackMqConfig(MqSourceConstants.BUSINESS);
//            operationParam.setCallbackExchange(exchange);
//            operationParam.setCallbackRouteKey(routeKey);
//        }

        String batchId = new StringBuilder().append(System.currentTimeMillis()).append(new Random().nextInt(999)).toString();
        log.info("[{}][IOT-请求参数] - {}", batchId, operationParam.toString());
        BaseResponse response = equipmentService.operation(operationParam);
        log.info("[{}][IOT-返回参数] - {}", batchId, response.toString());
        return response;
    }

    private void checkAndUpdateEquipmentTypeAndFactoryId(EquipmentInfoDTO equipment) {

        if(equipment.getEquipmentTypeId() != null && equipment.getFactoryId() != null) {
            return;
        }

        RetResultVO result = lyyOpenIotService.queryDeviceType(equipment.getUniqueCode());
        if (result == null) {
            return;
        }
        log.info("lyyOpenIotService.queryDeviceType-result:{}", result);
        if (result.getData() == null) {
            return;
        }
        HashMap<String, Object> resultData = (HashMap<String, Object>) result.getData();
        Object deviceType = resultData.get("deviceType");
        if (deviceType == null) {
            return;
        }
        EquipmentTypeDTO equipmentTypeDTO = Optional.ofNullable(equipmentTypeService.getByValue(deviceType.toString()))
                .map(BaseResponse::getData).orElseThrow(()->{
            return new BusinessException(BusinessExceptionEnums.NOT_SUPPORT_EQUIPMENT_TYPE);
        });

        if ((equipment.getFactoryId() == null || equipment.getFactoryId() == 0) && resultData.get("lyyFactoryMotherboardId") != null) {
            Long lyyFactoryMotherboardId = Long.parseLong(resultData.get("lyyFactoryMotherboardId").toString());

            equipmentService.updateFactoryByMainboard(lyyFactoryMotherboardId, equipment.getEquipmentId().longValue(), equipmentTypeDTO.getEquipmentTypeId());
        }
    }


    private boolean isRegister(EquipmentInfoDTO equipment) {
        // 兼容工厂只批给商家但是没设置场地的情况
        return Objects.nonNull(equipment.getRegisterDate()) && Objects.nonNull(equipment.getDistributorId()) && Objects.nonNull(equipment.getEquipmentGroupId());
    }
}
