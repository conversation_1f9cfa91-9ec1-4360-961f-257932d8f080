package cn.lyy.merchant.service.equipment.impl;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.merchant.api.service.MerchantEquipmentService;
import cn.lyy.merchant.dto.equipment.EquipmentLabelDTO;
import cn.lyy.merchant.service.equipment.LabelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import static java.util.Optional.ofNullable;

/**
 * 类描述：
 * <p>
 *
 * <AUTHOR>
 * @since 2020/11/5 14:40
 */
@Service
public class LabelServiceImpl implements LabelService {

    @Autowired
    private MerchantEquipmentService merchantEquipmentService;

    @Override
    public List<EquipmentLabelDTO> queryAll(Long distributorId,Integer labelType) {
        return ofNullable(merchantEquipmentService.queryAll(distributorId,labelType)).map(BaseResponse::getData).orElse(new ArrayList<>());
    }
}
