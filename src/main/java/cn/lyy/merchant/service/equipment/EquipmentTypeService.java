package cn.lyy.merchant.service.equipment;

import cn.lyy.equipment.dto.equipment.EquipmentTypeDTO;

import java.util.List;

public interface EquipmentTypeService {

    /**
     * 获取用户所操作设备的所有设备类型
     * @param adUserId
     * @param orgId
     * @return
     */
    List<EquipmentTypeDTO> selectAllEquipmentType(Long adUserId,Long orgId);

    /**
     * 获取指定商家已注册过的支持批量注册的设备类型
     * @param orgId
     */
    List<cn.lyy.merchant.dto.equipment.EquipmentTypeDTO> getSupportBatchRegisterEquipmentType(Long orgId);
}
