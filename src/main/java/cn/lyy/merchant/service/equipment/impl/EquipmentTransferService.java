package cn.lyy.merchant.service.equipment.impl;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.equipment.dto.equipment.EquipmentDTO;
import cn.lyy.merchant.api.service.MerchantEquipmentService;
import cn.lyy.merchant.api.service.MerchantGroupService;
import cn.lyy.merchant.constants.BusinessExceptionEnums;
import cn.lyy.merchant.constants.EquipmentEnum;
import cn.lyy.merchant.constants.EquipmentEventType;
import cn.lyy.merchant.dto.equipment.EquipmentTransferDTO;
import cn.lyy.merchant.dto.merchant.response.MerchantGroupDTO;
import cn.lyy.merchant.dto.request.EquipmentRegisterDTO;
import cn.lyy.merchant.dto.template.EquipmentRegisterTmplDTO;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.service.equipment.AbstractEquipmentRegisterTransfer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2019/8/28
 * @Version 1.0
 **/
@Slf4j
@Service
public class EquipmentTransferService extends AbstractEquipmentRegisterTransfer {

    @Autowired
    private MerchantGroupService merchantGroupService;

    @Autowired
    private MerchantEquipmentService merchantEquipmentService;

    public EquipmentTransferService() {
        setEventType(EquipmentEventType.TRANSFER);
    }

    @Override
    protected void checkEquipment(EquipmentRegisterDTO param, List<EquipmentDTO> equipments, EquipmentRegisterTmplDTO registerTmpl) {
        for(EquipmentDTO equipment : equipments) {
            if (!isRegister(equipment)) {
                throw new BusinessException(BusinessExceptionEnums.EQUIPMENT_HAS_NOT_BEEN_REGISTERED);
            }

            if(param.getGroupId().equals(equipment.getEquipmentGroupId())){
                throw new BusinessException(BusinessExceptionEnums.CAN_NOT_TRANSFER_TO_CURRENT_GROUP);
            }

            MerchantGroupDTO newGroup = Optional.ofNullable(merchantGroupService.getGroup(param.getGroupId()))
                    .map(BaseResponse::getData)
                    .orElseThrow(()-> new BusinessException(BusinessExceptionEnums.GROUP_NOT_EXIST));

            if(!newGroup.getDistributorId().equals(equipment.getDistributorId())){
                throw new BusinessException(BusinessExceptionEnums.IS_NOT_YOUR_EQUIPMENT);
            }
        }
    }

    @Override
    protected void beforeUpdateEquipment(EquipmentRegisterDTO param , EquipmentDTO equipment, MerchantGroupDTO newGroup) {

        // 6. 新增转移记录
        EquipmentTransferDTO transfer = new EquipmentTransferDTO();
        transfer.setLyyEquipmentId(equipment.getEquipmentId());
        transfer.setFromGroupId(equipment.getEquipmentGroupId());
        transfer.setToGroupId(newGroup.getEquipmentGroupId());
        transfer.setOperationType(EquipmentEnum.OPERATION_TYPE_TRANSFER.getValue());
        transfer.setAdOrgId(param.getDistributorId());
        transfer.setAdClientId(0L);
        transfer.setIsactive(EquipmentEnum.YES.getValue());
        transfer.setCreatedby(param.getUserId());
        merchantEquipmentService.transferSave(transfer);


        equipment.setEquipmentGroupId(newGroup.getEquipmentGroupId());
        equipment.setDistrictId(newGroup.getDistrictId());
        equipment.setName(newGroup.getName());
        equipment.setAddress(newGroup.getAddress());
        equipment.setUpdated(LocalDateTime.now());
        equipment.setUpdatedby(param.getUserId());
    }

}
