package cn.lyy.merchant.service.equipment;

import cn.lyy.equipment.dto.equipment.RemoteCoinsDailyDTO;
import cn.lyy.equipment.dto.equipment.RemoteCoinsRecordDTO;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 类描述：
 * <p>
 *
 * <AUTHOR>
 * @since 2020/11/10 11:30
 */
public interface RemoteStartService {

    /**
     * 分页获取启动记录
     * @param pageIndex
     * @param pageSize
     * @param adUserIdNotNull
     * @return
     */
    PageInfo<RemoteCoinsRecordDTO> recordPage(Integer pageIndex, Integer pageSize, String month, Long distributorId, Long adUserIdNotNull);

    List<RemoteCoinsDailyDTO> statisticsDaily(String month, Long distributorId, Long adUserIdNotNull);
}
