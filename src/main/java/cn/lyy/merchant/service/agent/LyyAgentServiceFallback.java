package cn.lyy.merchant.service.agent;

import cn.lyy.merchant.dto.request.EquipmentOperateSyncDTO;
import cn.lyy.merchant.dto.response.RetResultVO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <p>Title:saas2</p>
 * <p>Desc: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020/5/26
 */
@Slf4j
@Component
public class LyyAgentServiceFallback implements FallbackFactory<LyyAgentService> {
    @Override
    public LyyAgentService create(Throwable throwable) {
        if (throwable != null && StringUtils.isNotEmpty(throwable.getMessage())) {
            log.error("fallback reason was:" + throwable.getMessage(), throwable);
        }
        return new LyyAgentService() {
            @Override
            public Object equipmentOperate(EquipmentOperateSyncDTO equipmentOperateSyncDTO) {
                log.error("调用lyy-agent-server服务equipmentOperate超时");
                return RetResultVO.create("调用lyy-agent-server服务equipmentOperate超时");
            }
        };
    }
}
