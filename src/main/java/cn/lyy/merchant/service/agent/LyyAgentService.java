package cn.lyy.merchant.service.agent;

import cn.lyy.merchant.dto.request.EquipmentOperateSyncDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <p>Title:saas2</p>
 * <p>Desc: 代理端 服务接口</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020/5/26
 */
@FeignClient(name = "lyy-agent-server", path = "/agent", fallbackFactory = LyyAgentServiceFallback.class)
public interface LyyAgentService {

    /**
     * Saas 设备注册、解绑、转移时同步给工厂端，用于配置流量卡收费、免费
     * 当解绑时，将收费的设备改为免费
     * @param equipmentOperateSyncDTO
     * @return
     */
    @PostMapping("/rest/equipment/saas/operate")
    Object equipmentOperate(@RequestBody EquipmentOperateSyncDTO equipmentOperateSyncDTO);

}
