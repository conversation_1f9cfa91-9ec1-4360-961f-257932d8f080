package cn.lyy.merchant.service.superstaff.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.income.dto.utils.AddressTypeUtil;
import cn.lyy.lyy_api.advert.EquipmentMediaAdsClient;
import cn.lyy.lyy_cmember_service_api.IAiDbjService;
import cn.lyy.lyy_cmember_service_api.dto.aidbj.AiDbjRequestDTO;
import cn.lyy.lyy_cmember_service_api.dto.aidbj.AiDbjServiceDTO;
import cn.lyy.lyy_cmember_service_api.dto.aidbj.launch.AiDbjServiceAdRecommendResponseDTO;
import cn.lyy.lyy_cmember_service_api.dto.aidbj.launch.AiDbjServiceAdRecommendUseDTO;
import cn.lyy.marketing.api.service.superstaff.SuperStaffRuleClient;
import cn.lyy.marketing.api.service.superstaff.SuperStaffV2Client;
import cn.lyy.marketing.dto.constants.superstaff.SuperStaffItemItemTypeEnum;
import cn.lyy.marketing.dto.constants.superstaff.SuperStaffItemKeyEnum;
import cn.lyy.marketing.dto.constants.superstaff.SuperStaffRuleItemConstant;
import cn.lyy.marketing.dto.superstaff.v2.*;
import cn.lyy.merchant.api.service.MerchantEquipmentService;
import cn.lyy.merchant.api.service.MerchantGroupService;
import cn.lyy.merchant.constants.BusinessExceptionEnums;
import cn.lyy.merchant.dto.merchant.EquipmentBaseDTO;
import cn.lyy.merchant.dto.merchant.equpiment.request.MerchantEquipmentBaseInfoQueryRequest;
import cn.lyy.merchant.dto.merchant.request.MerchantGroupRequest;
import cn.lyy.merchant.dto.merchant.response.MerchantGroupDTO;
import cn.lyy.merchant.dto.superstaff.SuperStaffStoreQuery;
import cn.lyy.merchant.dto.superstaff.v2.*;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.service.superstaff.SuperStaffV2Service;
import cn.lyy.merchant.utils.RemoteResponseUtils;
import cn.lyy.tools.equipment.LyyConstant;
import cn.lyy_dto.advert.SpecialAdCreatedDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.lyy.charge.enums.response.ResponseEnum;
import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.starter.common.resp.RespBody;
import com.lyy.user.app.infrastructure.resp.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static java.util.Optional.ofNullable;

/**
 * @date: 2023-11-16
 * @author: YUNLONG
 */
@Slf4j
@Service
public class SuperStaffV2ServiceImpl implements SuperStaffV2Service {

    @Autowired
    private SuperStaffRuleClient superStaffRuleClient;

    @Autowired
    private SuperStaffV2Client superStaffV2Client;

    @Autowired
    private EquipmentMediaAdsClient equipmentMediaAdsClient;

    @Autowired
    private MerchantGroupService merchantGroupService;

    @Autowired
    private MerchantEquipmentService merchantEquipmentService;

    @Autowired
    private IAiDbjService iAiDbjService;

    @Value("${superStaff.store.equipmentBatchSize:500}")
    private Integer batchSize;

    private static final ThreadLocal<Long> AD_RECOMMEND_ACTIVITY_ID = new ThreadLocal<>();

    public int getBatchSize() {
        return ofNullable(batchSize).filter(o -> o > 0).orElse(500);
    }

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

    private static final LocalDateTime END_TIME = LocalDateTime.of(2099, 12, 31, 23, 59, 59);


    @Override
    public Long createRule(SuperStaffRuleCreatedV2Command command, Long merchantId, Long operatorId) {

        limitCheck(merchantId);

        SuperStaffRuleCreateV2DTO dto = new SuperStaffRuleCreateV2DTO();
        dto.setName(command.getName());
        dto.setMerchantId(merchantId);
        dto.setOperatorId(operatorId);
        dto.setItems(convertCreateItems(command.getItems(), merchantId, operatorId));

        fillMoreItems(dto);

        duplicateTypeCheck(dto.getItems().stream().map(SuperStaffRuleItemV2CreateDTO::getType).collect(Collectors.toList()));

        Long ruleId = RemoteResponseUtils.getData(superStaffV2Client.create(dto));
        if (Objects.isNull(ruleId)) {
            throw new BusinessException("设置新增失败");
        }
        ofNullable(AD_RECOMMEND_ACTIVITY_ID.get()).ifPresent(id -> {
            saveAdRecommendUse(id, ruleId,merchantId);
        });

        if (CharSequenceUtil.isBlank(command.getEquipmentValue())) {
            return ruleId;
        }

        SuperStaffEquipmentBindRuleDTO bindRuleDTO = new SuperStaffEquipmentBindRuleDTO();
        bindRuleDTO.setRuleId(ruleId);
        bindRuleDTO.setMerchantId(merchantId);
        bindRuleDTO.setOperatorId(operatorId);
        bindRuleDTO.setValue(command.getEquipmentValue());
        BaseResponse<Void> response = superStaffV2Client.equipmentBind(bindRuleDTO);
        if (!RemoteResponseUtils.checkResponse(response)) {
            throw new BusinessException("设备关联新设置失败，手动关联");
        }

        return ruleId;
    }

    private void limitCheck(Long merchantId) {
        BaseResponse<Boolean> checkResponse = superStaffRuleClient.checkEditLimit(merchantId);
        if (!RemoteResponseUtils.checkResponse(checkResponse)) {
            throw new BusinessException("编辑检查失败，稍后重试");
        }
        boolean editLimitCheck = ofNullable(RemoteResponseUtils.getData(checkResponse)).orElse(false);
        if (!editLimitCheck) {
            throw new BusinessException("今日编辑已达限次");
        }
    }

    private List<SuperStaffRuleItemV2CreateDTO> convertCreateItems(List<SuperStaffRuleV2CreateItem> items, Long merchantId,
            Long operatorId) {
        return items.stream()
                .map(o -> {
                    SuperStaffRuleItemV2CreateDTO item = new SuperStaffRuleItemV2CreateDTO();
                    item.setAutoGenerate(o.getAutoGenerate());
                    item.setContent(o.getContent());
                    item.setKey(o.getKey());
                    item.setType(o.getType());
                    item.setEnable(ofNullable(o.getEnable()).orElse(Boolean.TRUE));

                    fillAdsId(o, merchantId, operatorId);

                    item.setData(o.getData());

                    return item;
                })
                .collect(Collectors.toList());
    }

    private void fillAdsId(SuperStaffRuleV2CreateItem item, Long merchantId, Long operatorId) {
        if (!SuperStaffItemItemTypeEnum.VOICE_CUSTOM.getType().equals(item.getType())
                || !SuperStaffItemKeyEnum.AD_IDLE_FULL_SCREEN.getKey().equals(item.getKey())
                || Objects.isNull(item.getMaterialId())) {
            return;
        }

        JSONObject data = ofNullable(item.getData())
                .filter(CharSequenceUtil::isNotBlank)
                .map(JSON::parseObject)
                .orElse(new JSONObject());

        Long adsId = buildAndCreateAd(item, merchantId, operatorId);

        data.put(SuperStaffRuleItemConstant.ADS_ID, adsId);

        item.setData(data.toJSONString());
        log.debug("超级导购员｜创建特殊广告 item：{}", item);
        ofNullable(item.getActivityId()).ifPresent(id -> {
            AD_RECOMMEND_ACTIVITY_ID.set(id);
        });
    }

    private void fillMoreItems(SuperStaffRuleCreateV2DTO dto) {
        BaseResponse<SuperStaffRuleV2DTO> response = superStaffV2Client.findDefaultRule();
        if (!RemoteResponseUtils.checkResponse(response)) {
            throw new BusinessException("设置信息获取失败，重试");
        }

        SuperStaffRuleV2DTO defaultRule = RemoteResponseUtils.getData(response);
        if (Objects.isNull(defaultRule) || CollUtil.isEmpty(defaultRule.getItems())) {
            return;
        }

        Set<String> existTypes = dto.getItems().stream().map(SuperStaffRuleItemV2CreateDTO::getType).collect(Collectors.toSet());

        Stream<SuperStaffRuleItemV2CreateDTO> helperItems = defaultRule.getItems().stream()
                .filter(o -> !existTypes.contains(o.getType()))
                .map(o -> {
                    SuperStaffRuleItemV2CreateDTO item = new SuperStaffRuleItemV2CreateDTO();
                    item.setType(o.getType());
                    item.setData(o.getData());
                    item.setKey(o.getKey());
                    item.setContent(o.getContent());
                    item.setAutoGenerate(o.getAutoGenerate());
                    item.setEnable(o.getEnable());
                    return item;
                });

        dto.setItems(Stream.concat(dto.getItems().stream(), helperItems).collect(Collectors.toList()));
    }

    private void duplicateTypeCheck(List<String> types) {
        Set<String> exists = new HashSet<>();
        for (String type : types) {
            if (SuperStaffItemItemTypeEnum.VOICE_CUSTOM.getType().equals(type)) {
                continue;
            }

            if (!exists.add(type)) {
                throw new BusinessException("设置项目重复，数据异常");
            }
        }
    }

    private Long buildAndCreateAd(SuperStaffRuleV2CreateItem item, Long merchantId, Long operatorId) {

        SpecialAdCreatedDTO dto = new SpecialAdCreatedDTO();
        dto.setAdClientId(1000001L);
        dto.setMerchantId(merchantId);
        dto.setOperatorId(operatorId);
        dto.setAdsType(1);
        dto.setCommunicationType(1);
        dto.setPlayType(2);
        dto.setStartTime(LocalDate.now().atStartOfDay());
        dto.setEndTime(END_TIME);
        dto.setEquipmentTypeId((long) LyyConstant.DBJ_TYPE_ID);
        dto.setMaterialId(item.getMaterialId());

        String adsName = ofNullable(item.getAdsName()).filter(CharSequenceUtil::isNotBlank)
                .orElseGet(() -> "广告" + FORMATTER.format(Instant.now()));
        dto.setName(adsName);

        dto.setOwner(2);
        dto.setPrice(1L);
        dto.setStatus(4);

        return createAds(dto);
    }

    private Long createAds(SpecialAdCreatedDTO dto) {
        RespBody<Long> response = equipmentMediaAdsClient.saveSpecialMaterial(dto);
        Boolean result = ofNullable(response)
                .map(RespBody::getCode)
                .map(o -> GlobalErrorCode.OK.getCode().equals(o))
                .filter(o -> o)
                .orElseThrow(() -> new BusinessException(BusinessExceptionEnums.SERVICE_ERROR.getExCode(), "广告新增失败"));
        if (log.isDebugEnabled()) {
            log.debug("超级导购员｜创建特殊广告 结果：{}", result);
        }

        return RemoteResponseUtils.getData(response);
    }

    @Override
    public void editRule(SuperStaffRuleUpdateV2Command command, Long merchantId, Long operatorId) {

        limitCheck(merchantId);

        boolean isItemChange = CollUtil.isNotEmpty(command.getItems());
        List<String> ruleAllMatchEquipments = Collections.emptyList();
        if (isItemChange) {
            ruleAllMatchEquipments = findRuleAllMatchEquipment(command.getRuleId());
        }

        SuperStaffRuleUpdateV2DTO dto = new SuperStaffRuleUpdateV2DTO();
        dto.setName(command.getName());
        dto.setMerchantId(merchantId);
        dto.setOperatorId(operatorId);
        dto.setRuleId(command.getRuleId());

        dto.setItems(convertUpdateItems(command.getItems(), merchantId, operatorId));

        duplicateTypeCheck(dto.getItems().stream().map(SuperStaffRuleItemUpdateV2DTO::getType).collect(Collectors.toList()));

        BaseResponse<Void> response = superStaffV2Client.update(dto);
        if (!RemoteResponseUtils.checkResponse(response)) {
            log.info("超级导购员｜设置编辑 失败，{}", response.getMessage());
            throw new BusinessException(BusinessExceptionEnums.PARAM_ERROR.getExCode(), response.getMessage());
        }
        ofNullable(AD_RECOMMEND_ACTIVITY_ID.get()).ifPresent(id -> {
            saveAdRecommendUse(id, command.getRuleId(), merchantId);
        });

        if (isItemChange) {
            handleRuleChangeNotify(ruleAllMatchEquipments, merchantId, command.getRuleId());
        }

        if (CharSequenceUtil.isBlank(command.getEquipmentValue())
                || (CollUtil.isNotEmpty(ruleAllMatchEquipments) && ruleAllMatchEquipments.contains(command.getEquipmentValue()))) {
            return;
        }

        SuperStaffEquipmentBindRuleDTO bindRuleDTO = new SuperStaffEquipmentBindRuleDTO();
        bindRuleDTO.setRuleId(command.getRuleId());
        bindRuleDTO.setMerchantId(merchantId);
        bindRuleDTO.setOperatorId(operatorId);
        bindRuleDTO.setValue(command.getEquipmentValue());
        BaseResponse<Void> bindResponse = superStaffV2Client.equipmentBind(bindRuleDTO);
        if (!RemoteResponseUtils.checkResponse(bindResponse)) {
            throw new BusinessException("设备关联新设置失败，手动关联");
        }
    }

    private List<SuperStaffRuleItemUpdateV2DTO> convertUpdateItems(List<SuperStaffRuleUpdateV2Item> items, Long merchantId,
            Long operatorId) {
        return items.stream()
                .map(o -> {
                    SuperStaffRuleItemUpdateV2DTO item = new SuperStaffRuleItemUpdateV2DTO();
                    item.setItemId(o.getItemId());
                    item.setAutoGenerate(o.getAutoGenerate());
                    item.setContent(o.getContent());
                    item.setKey(o.getKey());
                    item.setEnable(ofNullable(o.getEnable()).orElse(Boolean.TRUE));
                    item.setType(o.getType());

                    fillAdsId(o, merchantId, operatorId);

                    item.setData(o.getData());

                    return item;
                })
                .collect(Collectors.toList());
    }

    private void saveAdRecommendUse(Long activityId, Long ruleId, Long merchantId) {
        log.info("超级导购员｜设置编辑 保存推荐海报应用，activityId：{}，ruleId：{}", activityId, ruleId);
        AiDbjServiceAdRecommendUseDTO param = new AiDbjServiceAdRecommendUseDTO();
        param.setAiDbjServiceAdRecommendId(activityId.intValue());
        param.setRuleId(ruleId);
        param.setCreatedby(merchantId.intValue());
        param.setUpdatedby(merchantId.intValue());
        iAiDbjService.saveAdRecommendUse(param);
    }

    private void fillAdsId(SuperStaffRuleUpdateV2Item item, Long merchantId, Long operatorId) {
        if (!SuperStaffItemItemTypeEnum.VOICE_CUSTOM.getType().equals(item.getType())
                || !SuperStaffItemKeyEnum.AD_IDLE_FULL_SCREEN.getKey().equals(item.getKey())
                || Objects.isNull(item.getMaterialId())) {
            return;
        }

        JSONObject data = ofNullable(item.getData())
                .filter(CharSequenceUtil::isNotBlank)
                .map(JSON::parseObject)
                .orElse(new JSONObject());

        Long adsId = buildAndCreateAd(item, merchantId, operatorId);

        data.put(SuperStaffRuleItemConstant.ADS_ID, adsId);

        item.setData(data.toJSONString());
        ofNullable(item.getActivityId()).ifPresent(id -> {
            AD_RECOMMEND_ACTIVITY_ID.set(id);
        });
    }

    private Long buildAndCreateAd(SuperStaffRuleUpdateV2Item item, Long merchantId, Long operatorId) {
        SpecialAdCreatedDTO dto = new SpecialAdCreatedDTO();
        dto.setAdClientId(1000001L);
        dto.setMerchantId(merchantId);
        dto.setOperatorId(operatorId);
        dto.setAdsType(1);
        dto.setCommunicationType(1);
        dto.setPlayType(2);
        dto.setStartTime(LocalDate.now().atStartOfDay());
        dto.setEndTime(END_TIME);
        dto.setEquipmentTypeId((long) LyyConstant.DBJ_TYPE_ID);
        dto.setMaterialId(item.getMaterialId());

        String adsName = ofNullable(item.getAdsName()).filter(CharSequenceUtil::isNotBlank)
                .orElseGet(() -> "广告" + FORMATTER.format(Instant.now()));

        dto.setName(adsName);
        dto.setOwner(2);
        dto.setPrice(1L);
        dto.setStatus(4);

        return createAds(dto);
    }

    private List<String> findRuleAllMatchEquipment(Long ruleId) {

        int size = getBatchSize();

        com.github.pagehelper.PageInfo<String> firstPage = RemoteResponseUtils
                .getData(superStaffV2Client.boundEquipments(ruleId, 1, size));
        if (Objects.isNull(firstPage)) {
            throw new BusinessException("设置相关信息获取失败，重试");
        }

        return fetchAllRuleEquipments(firstPage, size, ruleId);
    }

    private List<String> fetchAllRuleEquipments(com.github.pagehelper.PageInfo<String> firstPage, int size, Long ruleId) {
        long total = firstPage.getTotal();
        if (total <= size) {
            return ofNullable(firstPage.getList()).orElse(Collections.emptyList());
        }

        int pageNum = calculatePageNum(total, size);
        Stream<String> otherValues = IntStream.range(1, pageNum)
                .boxed()
                .flatMap(page -> {
                    com.github.pagehelper.PageInfo<String> pageData = RemoteResponseUtils
                            .getData(superStaffV2Client.boundEquipments(ruleId, ++page, size));
                    if (Objects.isNull(pageData)) {
                        throw new BusinessException("设置相关信息获取失败，重试");
                    }

                    return ofNullable(pageData.getList()).map(Collection::stream).orElse(Stream.empty());
                });

        return Stream.concat(otherValues, ofNullable(firstPage.getList()).map(Collection::stream).orElse(Stream.empty()))
                .collect(Collectors.toList());
    }

    private void handleRuleChangeNotify(List<String> equipmentValues, Long merchantId, Long ruleId) {
        if (CollUtil.isEmpty(equipmentValues)) {
            return;
        }

        int size = getBatchSize();
        Lists.partition(equipmentValues, size).forEach(values -> {
            SuperStaffRuleChangeV2Notify notify = new SuperStaffRuleChangeV2Notify();
            notify.setRuleId(ruleId);
            notify.setMerchantId(merchantId);
            notify.setValues(values);
            try {
                BaseResponse<Void> response = superStaffV2Client.notify(notify);
                if (!RemoteResponseUtils.checkResponse(response)) {
                    log.info("超级导购员｜设备通知失败 {}", response);
                }
            } catch (Exception e) {
                log.warn("超级导购员｜设备通知失败", e);
            }
        });

    }

    @Override
    public int totalUnboundCount(Long merchantId, Long adUserId) {
        int size = getBatchSize();

        try {
            MerchantEquipmentBaseInfoQueryRequest request = new MerchantEquipmentBaseInfoQueryRequest();
            request.setPageSize(size);
            request.setMerchantId(merchantId);
            request.setAdUserId(adUserId);
            request.setPageIndex(1);
            request.setEquipmentTypeId((long) LyyConstant.DBJ_TYPE_ID);
            com.github.pagehelper.PageInfo<EquipmentBaseDTO> firstPage = equipmentPage(request);

            return countUnbind(firstPage, size, request, merchantId);
        } catch (Exception e) {
            log.warn("超级导购员｜默认设置绑定设备数获取失败，略过", e);
            return 0;
        }
    }

    private int countUnbind(com.github.pagehelper.PageInfo<EquipmentBaseDTO> firstPage, long size,
            MerchantEquipmentBaseInfoQueryRequest request, Long merchantId) {
        if (firstPage.getTotal() <= size) {
            return unbindEquipmentCheckCount(firstPage.getList(), merchantId);
        }

        return IntStream.range(1, calculatePageNum(firstPage.getTotal(), size))
                .map(page -> {
                    request.setPageIndex(++page);
                    return unbindEquipmentCheckCount(equipmentPage(request).getList(), merchantId);
                })
                .sum() + unbindEquipmentCheckCount(firstPage.getList(), merchantId);
    }

    private int unbindEquipmentCheckCount(List<EquipmentBaseDTO> equipments, Long merchantId) {
        if (CollUtil.isEmpty(equipments)) {
            return 0;
        }

        List<String> values = equipments.stream().map(EquipmentBaseDTO::getEquipmentValue).collect(Collectors.toList());

        AiDbjRequestDTO versionQuery = new AiDbjRequestDTO();
        versionQuery.setEquipmentList(values);
        cn.lyy.lyy_cmember_service_api.response.BaseResponse<List<AiDbjServiceDTO>> versionResponse = iAiDbjService
                .getServiceEquipment(versionQuery);
        if (Objects.isNull(versionResponse) || !Objects.equals(ResponseEnum.SUCCESS.getCode(), versionResponse.getCode())) {
            throw new BusinessException("设备权限信息获取失败，重试");
        }
        List<String> validEquipments = ofNullable(versionResponse.getData())
                .filter(CollUtil::isNotEmpty)
                .map(o -> o.stream().map(AiDbjServiceDTO::getLyyEquipmentValue).collect(Collectors.toList()))
                .orElse(Collections.emptyList());
        if (CollUtil.isEmpty(validEquipments)) {
            return 0;
        }

        SuperStaffEquipmentBoundCheckCountQuery query = new SuperStaffEquipmentBoundCheckCountQuery();
        query.setMerchantId(merchantId);
        query.setValues(validEquipments);
        BaseResponse<Integer> response = superStaffV2Client.boundCheckCountByValues(query);
        if (!RemoteResponseUtils.checkResponse(response)) {
            throw new BusinessException("设备已绑定数获取失败");
        }

        int boundCount = ofNullable(response.getData()).filter(o -> o > 0).orElse(0);
        if (boundCount > validEquipments.size()) {
            return 0;
        }

        return validEquipments.size() - boundCount;
    }

    @Override
    public PageInfo<StoreWithEquipmentListDTO> equipmentStoreList(SuperStaffStoreQuery query, Long merchantId, Long adUserId) {
        MerchantGroupRequest request = new MerchantGroupRequest();
        request.setContext(query.getQuery());
        request.setPageIndex(query.getCurrent());
        request.setPageSize(query.getSize());
        request.setDistributor(merchantId);
        request.setAdUser(adUserId);
        request.setIsActive(1);
        request.setEquipmentTypeId((long) LyyConstant.DBJ_TYPE_ID);

        BaseResponse<com.github.pagehelper.PageInfo<MerchantGroupDTO>> response = merchantGroupService.pageGroupQueryWithEquipment(request);
        if (!RemoteResponseUtils.checkResponse(response)) {
            throw new BusinessException("场地数据获取失败，重试");
        }

        com.github.pagehelper.PageInfo<MerchantGroupDTO> pageInfo = response.getData();
        PageInfo<StoreWithEquipmentListDTO> page = PageInfo.<StoreWithEquipmentListDTO>builder()
                .total(pageInfo.getTotal())
                .current((long) pageInfo.getPageNum())
                .size((long) pageInfo.getPageSize())
                .records(transferStore2DTO(pageInfo.getList()))
                .build();

        fillStoreEquipments(page.getRecords(), merchantId);

        return page;
    }

    private List<StoreWithEquipmentListDTO> transferStore2DTO(List<MerchantGroupDTO> stores) {
        if (CollUtil.isEmpty(stores)) {
            return Collections.emptyList();
        }

        return stores.stream().map(o -> {
                    StoreWithEquipmentListDTO dto = new StoreWithEquipmentListDTO();
                    dto.setStoreId(o.getEquipmentGroupId());
                    dto.setName(o.getName());
                    dto.setAddress(o.getAddress());
                    dto.setProvinceName(o.getProvinceName());
                    dto.setCityName(o.getCityName());
                    dto.setDistrict(o.getDistrict());
                    dto.setAddressType(o.getAddressType());
                    dto.setAddressTypeName(ofNullable(o.getAddressType())
                            .filter(CharSequenceUtil::isNotBlank)
                            .map(AddressTypeUtil::getAddressTypeContainAlipay)
                            .orElse(""));

                    return dto;
                })
                .collect(Collectors.toList());
    }

    private void fillStoreEquipments(List<StoreWithEquipmentListDTO> stores, Long merchantId) {
        if (CollUtil.isEmpty(stores)) {
            return;
        }

        List<Long> storeIds = stores.stream().map(StoreWithEquipmentListDTO::getStoreId).collect(Collectors.toList());

        int size = getBatchSize();
        MerchantEquipmentBaseInfoQueryRequest request = new MerchantEquipmentBaseInfoQueryRequest();
        request.setStoreIds(storeIds);
        request.setEquipmentTypeId((long) LyyConstant.DBJ_TYPE_ID);
        request.setPageIndex(1);
        request.setPageSize(size);
        request.setMerchantId(merchantId);
        com.github.pagehelper.PageInfo<EquipmentBaseDTO> data = equipmentPage(request);
        Map<Long, List<EquipmentWithRuleInfoDTO>> equipmentMap = collectStoreAllEquipments(data, size, request, merchantId);

        SuperStaffRuleV2DTO defaultRule = defaultRule();

        for (StoreWithEquipmentListDTO store : stores) {
            List<EquipmentWithRuleInfoDTO> equipments = equipmentMap.getOrDefault(store.getStoreId(), Collections.emptyList());
            store.setEquipments(equipments);
            store.setEquipmentCount(equipments.size());
            for (EquipmentWithRuleInfoDTO equipment : equipments) {
                if (equipment.getVersion() < 1) {
                    equipment.setSuperStaffRuleId(ofNullable(defaultRule).map(SuperStaffRuleV2DTO::getRuleId).orElse(null));
                    equipment.setSuperStaffRuleName(ofNullable(defaultRule).map(SuperStaffRuleV2DTO::getName).orElse(null));
                }
            }
        }
    }

    private com.github.pagehelper.PageInfo<EquipmentBaseDTO> equipmentPage(MerchantEquipmentBaseInfoQueryRequest request) {
        BaseResponse<com.github.pagehelper.PageInfo<EquipmentBaseDTO>> response = merchantEquipmentService.pageStoreEquipmentBaseInfo(
                request);
        if (!RemoteResponseUtils.checkResponse(response) || Objects.isNull(response.getData())) {
            throw new BusinessException("设备信息获取失败，重试");
        }

        return response.getData();
    }

    private Map<Long, List<EquipmentWithRuleInfoDTO>> collectStoreAllEquipments(com.github.pagehelper.PageInfo<EquipmentBaseDTO> pageInfo,
            int size, MerchantEquipmentBaseInfoQueryRequest request, Long merchantId) {
        long total = pageInfo.getTotal();
        if (total <= size) {
            if (log.isDebugEnabled()) {
                log.debug("超级导购员｜聚合场地下设备，一页结束");
            }
            return filterEquipment(pageInfo.getList(), merchantId).stream()
                    .collect(Collectors.groupingBy(EquipmentWithRuleInfoDTO::getStoreId));
        }

        int pageNum = calculatePageNum(total, size);
        Stream<EquipmentWithRuleInfoDTO> streamData = IntStream.range(1, pageNum)
                .boxed()
                .flatMap(page -> {
                    request.setPageIndex(++page);
                    return filterEquipment(equipmentPage(request).getList(), merchantId).stream();
                });

        return Stream.concat(streamData, filterEquipment(pageInfo.getList(), merchantId).stream())
                .collect(Collectors.groupingBy(EquipmentWithRuleInfoDTO::getStoreId));

    }

    private int calculatePageNum(long total, long pageSize) {
        if (total <= pageSize) {
            return 1;
        }

        return new BigDecimal(total).divide(new BigDecimal(pageSize), 0, RoundingMode.UP).intValue();
    }

    private List<EquipmentWithRuleInfoDTO> filterEquipment(List<EquipmentBaseDTO> equipments, Long merchantId) {
        if (CollUtil.isEmpty(equipments)) {
            return Collections.emptyList();
        }

        Map<String, EquipmentBaseDTO> eMap = equipments.stream()
                .collect(Collectors.toMap(EquipmentBaseDTO::getEquipmentValue, Function.identity(), (v1, v2) -> v1));

        AiDbjRequestDTO versionQuery = new AiDbjRequestDTO();
        versionQuery.setEquipmentList(new ArrayList<>(eMap.keySet()));
        cn.lyy.lyy_cmember_service_api.response.BaseResponse<List<AiDbjServiceDTO>> versionResponse = iAiDbjService
                .getServiceEquipment(versionQuery);
        if (Objects.isNull(versionResponse) || !Objects.equals(ResponseEnum.SUCCESS.getCode(), versionResponse.getCode())) {
            throw new BusinessException("设备权限信息获取失败，重试");
        }

        List<EquipmentWithRuleInfoDTO> result = ofNullable(versionResponse.getData())
                .filter(CollUtil::isNotEmpty)
                .map(o -> o.stream()
                        .map(version -> {
                            EquipmentBaseDTO equipmentBaseDTO = eMap.get(version.getLyyEquipmentValue());
                            if (Objects.isNull(equipmentBaseDTO)) {
                                return null;
                            }

                            EquipmentWithRuleInfoDTO equipment = new EquipmentWithRuleInfoDTO();
                            equipment.setEquipmentId(equipmentBaseDTO.getEquipmentId());
                            equipment.setEquipmentTypeId(equipmentBaseDTO.getEquipmentTypeId());
                            equipment.setEquipmentTypeName(equipmentBaseDTO.getEquipmentTypeName());
                            equipment.setValue(equipmentBaseDTO.getEquipmentValue());
                            equipment.setUniqueCode(equipmentBaseDTO.getUniqueCode());
                            equipment.setVersion(ofNullable(version.getVersion()).orElse(0));
                            equipment.setEndDate(version.getEndDate());
                            equipment.setStoreId(equipmentBaseDTO.getStoreId());

                            return equipment;
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList())
                )
                .orElse(Collections.emptyList());

        fillEquipmentRuleInfo(result, merchantId);

        return result;
    }

    private void fillEquipmentRuleInfo(List<EquipmentWithRuleInfoDTO> equipments, Long merchantId) {
        if (CollUtil.isEmpty(equipments)) {
            return;
        }

        List<String> values = equipments.stream().map(EquipmentWithRuleInfoDTO::getValue).collect(Collectors.toList());
        SuperStaffEquipmentRuleQuery query = new SuperStaffEquipmentRuleQuery();
        query.setValues(values);
        query.setMerchantId(merchantId);
        BaseResponse<List<EquipmentSuperStaffRuleInfoDTO>> response = superStaffV2Client.equipmentRuleInfoList(query);
        if (!RemoteResponseUtils.checkResponse(response)) {
            throw new BusinessException("设备导购设置信息获取失败，重试");
        }

        Map<String, EquipmentSuperStaffRuleInfoDTO> ruleMap = ofNullable(response.getData())
                .filter(CollUtil::isNotEmpty)
                .map(o -> o.stream()
                        .collect(Collectors.toMap(EquipmentSuperStaffRuleInfoDTO::getValue, Function.identity(), (v1, v2) -> v1)))
                .orElse(Collections.emptyMap());

        equipments.forEach(o -> {
            EquipmentSuperStaffRuleInfoDTO ruleInfo = ruleMap.get(o.getValue());
            if (Objects.isNull(ruleInfo)) {
                return;
            }

            o.setSuperStaffRuleId(ruleInfo.getRuleId());
            o.setSuperStaffRuleName(ruleInfo.getRuleName());
        });
    }

    @Override
    public SuperStaffRuleV2DTO detail(Long ruleId, Long merchantId) {
        BaseResponse<Long> response = superStaffV2Client.defaultRuleId();
        if (!RemoteResponseUtils.checkResponse(response)) {
            throw new BusinessException("设置详情获取失败");
        }

        Long defaultRuleId = response.getData();
        if (Objects.equals(ruleId, defaultRuleId)) {
            return defaultRule();
        }

        BaseResponse<SuperStaffRuleV2DTO> detailResponse = superStaffV2Client.detail(merchantId, ruleId);
        if (!RemoteResponseUtils.checkResponse(detailResponse)) {
            throw new BusinessException("设置详情获取失败");
        }

        return detailResponse.getData();
    }

    @Override
    public AiDbjServiceAdRecommendResponseDTO getAdRecommendInfo(Long ruleId) {
        return iAiDbjService.getAdRecommendInfo(ruleId).getData();
    }

    private SuperStaffRuleV2DTO defaultRule() {
        SuperStaffRuleV2DTO defaultRule = RemoteResponseUtils.getData(superStaffV2Client.findDefaultRule());
        if (Objects.isNull(defaultRule)) {
            throw new BusinessException("设置详情获取失败");
        }

        return defaultRule;
    }

    @Override
    public PageInfo<StoreWithEquipmentListDTO> equipmentStoreListV2(SuperStaffStoreQuery query) {

        Stopwatch stopwatch = Stopwatch.createStarted();

        query.setLimitSize(getBatchSize());

        PageInfo<StoreWithEquipmentListDTO> result = findPageStores(query);

        fillStoreEquipmentRuleInfo(result.getRecords(), query);

        log.info("超级导购员｜获取ai兑币机列表，query:{}, 耗时:{}", query, stopwatch.stop());

        return result;
    }

    private void fillStoreEquipmentRuleInfo(List<StoreWithEquipmentListDTO> stores, SuperStaffStoreQuery query) {
        if (CollUtil.isEmpty(stores)) {
            return;
        }

        Map<String, EquipmentWithRuleInfoDTO> equipmentMap = stores.stream()
                .flatMap(o -> o.getEquipments().stream())
                .collect(Collectors.toMap(EquipmentWithRuleInfoDTO::getValue, Function.identity(), (v1, v2) -> v1));

        List<String> equipmentValues = equipmentMap.values().stream()
                .filter(e -> e.getVersion() > 0)
                .map(EquipmentWithRuleInfoDTO::getValue).collect(Collectors.toList());

        AtomicInteger boundFindCount = new AtomicInteger(0);

        SuperStaffEquipmentRuleQuery equipmentRuleQuery = new SuperStaffEquipmentRuleQuery();
        equipmentRuleQuery.setMerchantId(query.getMerchantId());
        Lists.partition(equipmentValues, query.getLimitSize())
                .forEach(values -> {
                    equipmentRuleQuery.setValues(values);
                    BaseResponse<List<EquipmentSuperStaffRuleInfoDTO>> response = superStaffV2Client
                            .equipmentRuleInfoList(equipmentRuleQuery);

                    if (!RemoteResponseUtils.checkResponse(response)) {
                        throw new BusinessException("设备导购设置信息获取失败，重试");
                    }

                    ofNullable(response.getData())
                            .filter(CollUtil::isNotEmpty)
                            .ifPresent(o -> o.forEach(er -> ofNullable(equipmentMap.get(er.getValue()))
                                    .ifPresent(e -> {
                                        e.setSuperStaffRuleId(er.getRuleId());
                                        e.setSuperStaffRuleName(er.getRuleName());
                                        boundFindCount.incrementAndGet();
                                    }))
                            );
                });
        if (boundFindCount.get() == equipmentMap.size()) {
            return;
        }

        SuperStaffRuleV2DTO defaultRule = defaultRule();
        equipmentMap.values().forEach(e -> {
            if (e.getVersion() <= 0 || Objects.isNull(e.getSuperStaffRuleId())) {
                e.setSuperStaffRuleId(defaultRule.getRuleId());
                e.setSuperStaffRuleName(defaultRule.getName());
            }
        });
    }

    private PageInfo<StoreWithEquipmentListDTO> findPageStores(SuperStaffStoreQuery query) {

        List<StoreWithEquipmentListDTO> stores = fetchAllGroups(query);

        fillStoreValidServiceEquipment(stores, query);

        long current = ofNullable(query.getCurrent()).filter(o -> o > 0).map(Integer::longValue).orElse(1L);
        long size = ofNullable(query.getSize()).filter(o -> o > 0).map(Integer::longValue).orElse(10L);
        long offset = (current - 1L) * size;

        long total = stores.stream()
                .filter(o -> CollUtil.isNotEmpty(o.getEquipments()))
                .count();
        PageInfo<StoreWithEquipmentListDTO> pageData = PageInfo.<StoreWithEquipmentListDTO>builder()
                .total(total)
                .current(current)
                .size(size)
                .records(Collections.emptyList())
                .build();
        if (total <= offset) {
            return pageData;
        }

        List<StoreWithEquipmentListDTO> records = stores.stream()
                .filter(o -> CollUtil.isNotEmpty(o.getEquipments()))
                .skip(offset)
                .limit(size)
                .collect(Collectors.toList());
        pageData.setRecords(records);

        return pageData;
    }

    private List<StoreWithEquipmentListDTO> fetchAllGroups(SuperStaffStoreQuery query) {
        MerchantGroupRequest request = new MerchantGroupRequest();
        request.setContext(query.getQuery());
        request.setDistributor(query.getMerchantId());
        request.setAdUser(query.getAdUserId());
        request.setIsActive(1);
        request.setEquipmentTypeId((long) LyyConstant.DBJ_TYPE_ID);
        request.setPageSize(query.getLimitSize());

        request.setPageIndex(1);
        com.github.pagehelper.PageInfo<MerchantGroupDTO> firstPage = storePage(request);
        if (CollUtil.isEmpty(firstPage.getList())) {
            return Collections.emptyList();
        }

        long total = firstPage.getTotal();
        if (total <= query.getLimitSize()) {
            return transferStore2DTO(firstPage.getList());
        }

        int pageNum = calculatePageNum(total, query.getLimitSize());
        Stream<StoreWithEquipmentListDTO> remainStoreStream = IntStream.range(1, pageNum)
                .boxed()
                .flatMap(page -> {
                    request.setPageIndex(++page);
                    return transferStore2DTO(storePage(request).getList()).stream();
                });

        return Stream.concat(transferStore2DTO(firstPage.getList()).stream(), remainStoreStream)
                .collect(Collectors.toList());
    }

    private void fillStoreValidServiceEquipment(List<StoreWithEquipmentListDTO> stores, SuperStaffStoreQuery query) {
        if (CollUtil.isEmpty(stores)) {
            return;
        }

        Stopwatch stopwatch = Stopwatch.createStarted();

        Map<Long, StoreWithEquipmentListDTO> storeMap = stores.stream()
                .collect(Collectors.toMap(StoreWithEquipmentListDTO::getStoreId, Function.identity(), (k1, k2) -> k1));

        if (CharSequenceUtil.isNotBlank(query.getQuery())) {
            Lists.partition(new ArrayList<>(storeMap.keySet()), query.getLimitSize())
                    .forEach(storeIds -> findAndFillStoreEquipment(storeMap, storeIds, query));
        } else {
            findAndFillStoreEquipment(storeMap, null, query);
        }

        log.info("超级导购员｜过滤ai兑币机， 场地数:{}, 耗时:{}", stores.size(), stopwatch.stop());
    }

    private void findAndFillStoreEquipment(Map<Long, StoreWithEquipmentListDTO> storeMap, List<Long> storeIds, SuperStaffStoreQuery query) {
        MerchantEquipmentBaseInfoQueryRequest request = new MerchantEquipmentBaseInfoQueryRequest();
        request.setPageSize(query.getLimitSize());
        request.setMerchantId(query.getMerchantId());
        request.setEquipmentTypeId((long) LyyConstant.DBJ_TYPE_ID);
        request.setStoreIds(storeIds);
        Map<Long, List<EquipmentWithRuleInfoDTO>> storeEquipments =
                collectStoreAllValidServiceEquipments(request, query, storeMap.keySet())
                        .filter(o -> Objects.nonNull(o.getStoreId()))
                        .collect(Collectors.groupingBy(EquipmentWithRuleInfoDTO::getStoreId));
        storeEquipments.forEach((key, value) -> ofNullable(storeMap.get(key))
                .ifPresent(store -> {
                    store.setEquipments(value);
                    store.setEquipmentCount(value.size());
                })
        );
    }

    private Stream<EquipmentWithRuleInfoDTO> collectStoreAllValidServiceEquipments(MerchantEquipmentBaseInfoQueryRequest request,
            SuperStaffStoreQuery query, Set<Long> allStoreIds) {
        request.setPageIndex(1);
        com.github.pagehelper.PageInfo<EquipmentBaseDTO> firstPage = equipmentPage(request);
        long total = firstPage.getTotal();
        if (total <= query.getLimitSize()) {
            return filterValidServiceEquipment(firstPage.getList(), query, allStoreIds);
        }

        int pageNum = calculatePageNum(total, query.getLimitSize());
        Stream<EquipmentWithRuleInfoDTO> remainEquipmentStream = IntStream.range(1, pageNum)
                .boxed()
                .flatMap(page -> {
                    request.setPageIndex(++page);
                    return filterValidServiceEquipment(equipmentPage(request).getList(), query, allStoreIds);
                });

        return Stream.concat(remainEquipmentStream, filterValidServiceEquipment(firstPage.getList(), query, allStoreIds));
    }

    private Stream<EquipmentWithRuleInfoDTO> filterValidServiceEquipment(List<EquipmentBaseDTO> equipments,SuperStaffStoreQuery query,
            Set<Long> allStoreIds) {

        if (CollUtil.isEmpty(equipments) || CollUtil.isEmpty(allStoreIds)) {
            return Stream.empty();
        }

        Map<String, EquipmentBaseDTO> eMap = equipments.stream()
                .filter(e -> !query.isNeedFilterStoreInternal() || allStoreIds.contains(e.getStoreId()))
                .collect(Collectors.toMap(EquipmentBaseDTO::getEquipmentValue, Function.identity(), (v1, v2) -> v1));

        AiDbjRequestDTO versionQuery = new AiDbjRequestDTO();
        versionQuery.setEquipmentList(new ArrayList<>(eMap.keySet()));
        cn.lyy.lyy_cmember_service_api.response.BaseResponse<List<AiDbjServiceDTO>> versionResponse = iAiDbjService
                .getServiceEquipment(versionQuery);
        if (Objects.isNull(versionResponse) || !Objects.equals(ResponseEnum.SUCCESS.getCode(), versionResponse.getCode())) {
            throw new BusinessException("设备权限信息获取失败，重试");
        }

        Set<String> distinctHelper = new HashSet<>(eMap.size());

        return ofNullable(versionResponse.getData())
                .filter(CollUtil::isNotEmpty)
                .map(o -> o.stream()
                        .map(version -> {
                            EquipmentBaseDTO equipmentBaseDTO = eMap.get(version.getLyyEquipmentValue());
                            if (Objects.isNull(equipmentBaseDTO) || !distinctHelper.add(version.getLyyEquipmentValue())) {
                                return null;
                            }

                            EquipmentWithRuleInfoDTO equipment = new EquipmentWithRuleInfoDTO();
                            equipment.setEquipmentId(equipmentBaseDTO.getEquipmentId());
                            equipment.setEquipmentTypeId(equipmentBaseDTO.getEquipmentTypeId());
                            equipment.setEquipmentTypeName(equipmentBaseDTO.getEquipmentTypeName());
                            equipment.setValue(equipmentBaseDTO.getEquipmentValue());
                            equipment.setUniqueCode(equipmentBaseDTO.getUniqueCode());
                            equipment.setVersion(ofNullable(version.getVersion()).orElse(0));
                            equipment.setEndDate(version.getEndDate());
                            equipment.setStoreId(equipmentBaseDTO.getStoreId());

                            return equipment;
                        })
                        .filter(Objects::nonNull)
                )
                .orElse(Stream.empty());
    }

    private com.github.pagehelper.PageInfo<MerchantGroupDTO> storePage(MerchantGroupRequest request) {
        BaseResponse<com.github.pagehelper.PageInfo<MerchantGroupDTO>> response = merchantGroupService.pageGroupQueryWithEquipment(request);
        if (!RemoteResponseUtils.checkResponse(response) || Objects.isNull(response.getData())) {
            throw new BusinessException("场地数据获取失败，重试");
        }

        return response.getData();
    }
}
