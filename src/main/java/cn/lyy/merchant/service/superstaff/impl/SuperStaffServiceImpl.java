package cn.lyy.merchant.service.superstaff.impl;

import static java.util.Optional.of;
import static java.util.Optional.ofNullable;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.lyy_api.advert.EquipmentMediaAdsClient;
import cn.lyy.marketing.api.service.superstaff.SuperStaffRuleClient;
import cn.lyy.marketing.dto.constants.superstaff.SuperStaffItemKeyEnum;
import cn.lyy.marketing.dto.constants.superstaff.SuperStaffRuleItemConstant;
import cn.lyy.marketing.dto.superstaff.SuperStaffBoundStoreQuery;
import cn.lyy.marketing.dto.superstaff.SuperStaffRuleChangeNotify;
import cn.lyy.marketing.dto.superstaff.SuperStaffRuleCreateDTO;
import cn.lyy.marketing.dto.superstaff.SuperStaffRuleDTO;
import cn.lyy.marketing.dto.superstaff.SuperStaffRuleDeleteDTO;
import cn.lyy.marketing.dto.superstaff.SuperStaffRuleItemCreateDTO;
import cn.lyy.marketing.dto.superstaff.SuperStaffRuleItemUpdateDTO;
import cn.lyy.marketing.dto.superstaff.SuperStaffRuleUpdateDTO;
import cn.lyy.marketing.dto.superstaff.SuperStaffStoreBatchBindRuleDTO;
import cn.lyy.merchant.api.service.MerchantGroupService;
import cn.lyy.merchant.constants.BusinessExceptionEnums;
import cn.lyy.merchant.dto.merchant.request.MerchantGroupRequest;
import cn.lyy.merchant.dto.superstaff.SuperStaffRuleBindStoreDTO;
import cn.lyy.merchant.dto.superstaff.SuperStaffRuleCreateItem;
import cn.lyy.merchant.dto.superstaff.SuperStaffRuleCreateRequest;
import cn.lyy.merchant.dto.superstaff.SuperStaffRuleUpdateItem;
import cn.lyy.merchant.dto.superstaff.SuperStaffRuleUpdateRequest;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.service.superstaff.SuperStaffService;
import cn.lyy.merchant.utils.RemoteResponseUtils;
import cn.lyy.tools.equipment.LyyConstant;
import cn.lyy_dto.advert.SpecialAdCreatedDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.starter.common.resp.RespBody;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * @date: 2023-10-24
 * @author: YUNLONG
 */
@Slf4j
@Service
public class SuperStaffServiceImpl implements SuperStaffService {

    @Autowired
    private SuperStaffRuleClient superStaffRuleClient;

    @Autowired
    private EquipmentMediaAdsClient equipmentMediaAdsClient;

    @Autowired
    private MerchantGroupService merchantGroupService;

    @Value("${superStaff.store.batchSize:500}")
    private Integer batchSize;

    public int getBatchSize() {
        return ofNullable(batchSize).filter(o -> o > 0).orElse(500);
    }

    private static final LocalDateTime END_TIME = LocalDateTime.of(2099, 12, 31, 23, 59, 59);

    @Override
    public Long createRule(SuperStaffRuleCreateRequest request, Long merchantId, Long operatorId) {

        limitCheck(merchantId);

        SuperStaffRuleCreateDTO dto = new SuperStaffRuleCreateDTO();
        dto.setName(request.getName());
        dto.setMerchantId(merchantId);
        dto.setOperatorId(operatorId);
        dto.setItems(convertCreateItems(request.getItems(), merchantId, operatorId));
        BaseResponse<Long> response = superStaffRuleClient.created(dto);
        if (!RemoteResponseUtils.checkResponse(response)) {
            throw new BusinessException("规则新增失败");
        }

        return RemoteResponseUtils.getData(response);
    }

    private void limitCheck(Long merchantId) {
        BaseResponse<Boolean> checkResponse = superStaffRuleClient.checkEditLimit(merchantId);
        if (!RemoteResponseUtils.checkResponse(checkResponse)) {
            throw new BusinessException("编辑检查失败，稍后重试");
        }
        boolean editLimitCheck = ofNullable(RemoteResponseUtils.getData(checkResponse)).orElse(false);
        if (!editLimitCheck) {
            throw new BusinessException("今日编辑已达限次");
        }
    }

    private List<SuperStaffRuleItemCreateDTO> convertCreateItems(List<SuperStaffRuleCreateItem> items, Long merchantId, Long operatorId) {
        return items.stream()
                .map(o -> {
                    SuperStaffRuleItemCreateDTO item = new SuperStaffRuleItemCreateDTO();
                    item.setAutoGenerate(o.getAutoGenerate());
                    item.setContent(o.getContent());
                    item.setKey(o.getKey());

                    JSONObject data = ofNullable(o.getData())
                            .filter(CharSequenceUtil::isNotBlank)
                            .map(JSON::parseObject)
                            .orElse(new JSONObject());

                    if (SuperStaffItemKeyEnum.AD_IDLE_FULL_SCREEN.getKey().equals(o.getKey())) {
                        Long adsId = createAdIfNecessary(o, merchantId, operatorId);
                        if (Objects.nonNull(adsId)) {
                            data.put(SuperStaffRuleItemConstant.ADS_ID, adsId);
                        }
                    }

                    if (CollUtil.isNotEmpty(data)) {
                        item.setData(data.toJSONString());
                    }

                    return item;
                })
                .collect(Collectors.toList());
    }

    private Long createAdIfNecessary(SuperStaffRuleCreateItem data, Long merchantId, Long operatorId) {
        if (Objects.isNull(data.getMaterialId())) {
            return null;
        }

        SpecialAdCreatedDTO dto = new SpecialAdCreatedDTO();
        dto.setAdClientId(1000001L);
        dto.setMerchantId(merchantId);
        dto.setOperatorId(operatorId);
        dto.setAdsType(1);
        dto.setCommunicationType(1);
        dto.setPlayType(2);
        dto.setStartTime(LocalDate.now().atStartOfDay());
        dto.setEndTime(END_TIME);
        dto.setEquipmentTypeId((long) LyyConstant.DBJ_TYPE_ID);
        dto.setMaterialId(data.getMaterialId());
        dto.setName(data.getAdsName());
        dto.setOwner(2);
        dto.setPrice(1L);
        dto.setStatus(4);

        return createAds(dto);
    }

    private Long createAds(SpecialAdCreatedDTO dto) {
        RespBody<Long> response = equipmentMediaAdsClient.saveSpecialMaterial(dto);
        Boolean result = ofNullable(response)
                .map(RespBody::getCode)
                .map(o -> GlobalErrorCode.OK.getCode().equals(o))
                .filter(o -> o)
                .orElseThrow(() -> new BusinessException(BusinessExceptionEnums.SERVICE_ERROR.getExCode(), "广告新增失败"));
        if (log.isDebugEnabled()) {
            log.debug("超级导购员｜创建特殊广告 结果：{}", result);
        }

        return RemoteResponseUtils.getData(response);
    }

    @Override
    public void editRule(SuperStaffRuleUpdateRequest request, Long merchantId, Long operatorId) {

        limitCheck(merchantId);

        boolean isItemChange = CollUtil.isNotEmpty(request.getItems());
        List<Long> ruleAllMatchStoreIds = null;
        if (isItemChange) {
            ruleAllMatchStoreIds = findRuleAllMatchStoreIds(request.getRuleId(), merchantId);
        }

        SuperStaffRuleUpdateDTO dto = new SuperStaffRuleUpdateDTO();
        dto.setName(request.getName());
        dto.setMerchantId(merchantId);
        dto.setOperatorId(operatorId);
        dto.setRuleId(request.getRuleId());

        dto.setItems(convertUpdateItems(request.getItems(), merchantId, operatorId));

        BaseResponse<Void> response = superStaffRuleClient.update(dto);
        if (!RemoteResponseUtils.checkResponse(response)) {
            log.info("超级导购员｜规则编辑 失败，{}", response.getMessage());
            throw new BusinessException(BusinessExceptionEnums.PARAM_ERROR.getExCode(), response.getMessage());
        }

        if (isItemChange) {
            handleRuleChangeNotify(ruleAllMatchStoreIds, merchantId, request.getRuleId());
        }
    }

    private void handleRuleChangeNotify(List<Long> storeIds, Long merchantId, Long ruleId) {
        if (CollUtil.isEmpty(storeIds)) {
            return;
        }

        int size = getBatchSize();

        Lists.partition(storeIds, size).forEach(o -> ruleChangeNotify(o, merchantId, ruleId));
    }

    private void ruleChangeNotify(List<Long> storeIds, Long merchantId, Long ruleId) {
        SuperStaffRuleChangeNotify notify = new SuperStaffRuleChangeNotify();
        notify.setRuleId(ruleId);
        notify.setMerchantId(merchantId);
        notify.setStoreIds(storeIds);

        BaseResponse<Void> response = superStaffRuleClient.sendNotifyMessage(notify);
        if (!RemoteResponseUtils.checkResponse(response)) {
            log.warn("超级导购员｜规则变更通知 下发失败:{}", response);
        }
    }

    private List<SuperStaffRuleItemUpdateDTO> convertUpdateItems(List<SuperStaffRuleUpdateItem> items, Long merchantId, Long operatorId) {
        return items.stream()
                .map(o -> {
                    SuperStaffRuleItemUpdateDTO item = new SuperStaffRuleItemUpdateDTO();
                    item.setItemId(o.getItemId());
                    item.setAutoGenerate(o.getAutoGenerate());
                    item.setContent(o.getContent());
                    item.setKey(o.getKey());

                    JSONObject data = ofNullable(o.getData())
                            .filter(CharSequenceUtil::isNotBlank)
                            .map(JSON::parseObject)
                            .orElse(new JSONObject());

                    if (SuperStaffItemKeyEnum.AD_IDLE_FULL_SCREEN.getKey().equals(o.getKey())) {
                        Long adsId = createAdIfNecessary(o, merchantId, operatorId);
                        if (Objects.nonNull(adsId)) {
                            data.put(SuperStaffRuleItemConstant.ADS_ID, adsId);
                        }
                    }

                    if (CollUtil.isNotEmpty(data)) {
                        item.setData(data.toJSONString());
                    }

                    return item;
                })
                .collect(Collectors.toList());
    }

    private Long createAdIfNecessary(SuperStaffRuleUpdateItem data, Long merchantId, Long operatorId) {
        if (Objects.isNull(data.getMaterialId())) {
            return null;
        }

        SpecialAdCreatedDTO dto = new SpecialAdCreatedDTO();
        dto.setAdClientId(1000001L);
        dto.setMerchantId(merchantId);
        dto.setOperatorId(operatorId);
        dto.setAdsType(1);
        dto.setCommunicationType(1);
        dto.setPlayType(2);
        dto.setStartTime(LocalDate.now().atStartOfDay());
        dto.setEndTime(END_TIME);
        dto.setEquipmentTypeId((long) LyyConstant.DBJ_TYPE_ID);
        dto.setMaterialId(data.getMaterialId());
        dto.setName(data.getAdsName());
        dto.setOwner(2);
        dto.setPrice(1L);
        dto.setStatus(4);

        return createAds(dto);
    }

    @Override
    public void batchStoreBound(SuperStaffRuleBindStoreDTO dto, Long merchantId, Long adUserId) {
        List<Long> storeIds = processStoreIds(dto, merchantId, adUserId);
        if (CollUtil.isEmpty(storeIds)) {
            throw new BusinessException("未选择有效场地");
        }

        log.info("超级导购员｜批量绑定场地 全选场地数:{}", storeIds.size());

        int size = getBatchSize();
        Lists.partition(storeIds, size).forEach(ids -> {
            SuperStaffStoreBatchBindRuleDTO bindRuleDTO = new SuperStaffStoreBatchBindRuleDTO();
            bindRuleDTO.setRuleId(dto.getRuleId());
            bindRuleDTO.setStoreIds(ids);
            bindRuleDTO.setMerchantId(merchantId);
            bindRuleDTO.setOperatorId(adUserId);
            BaseResponse<Void> response = superStaffRuleClient.batchBind(bindRuleDTO);
            if (!RemoteResponseUtils.checkResponse(response)) {
                throw new BusinessException("部分场地绑定场地失败，请稍后重试");
            }

            ruleChangeNotify(ids, merchantId, dto.getRuleId());
        });
    }

    private List<Long> processStoreIds(SuperStaffRuleBindStoreDTO dto, Long merchantId, Long adUserId) {
        if (Objects.equals(Boolean.FALSE, dto.getSelectAll())) {
            return dto.getGroupIds();
        }

        List<Long> storeIds = findAllStoreIds(merchantId, adUserId, dto.getQuery());
        if (CollUtil.isEmpty(storeIds)) {
            return storeIds;
        }

        if (CollUtil.isNotEmpty(dto.getExcludeGroupIds())) {
            storeIds.removeAll(dto.getExcludeGroupIds());
        }

        return storeIds;
    }

    @Override
    public int totalUnboundCount(Long merchantId, Long adUserId, String queryStr) {
        BaseResponse<Long> response = superStaffRuleClient.defaultRuleId(merchantId);
        if (!RemoteResponseUtils.checkResponse(response)) {
            throw new BusinessException("场地关联数据获取失败");
        }

        Long defaultRuleId = RemoteResponseUtils.getData(response);
        if (Objects.nonNull(defaultRuleId)) {
            return 0;
        }

        List<Long> merchantStoreIds = findAllStoreIds(merchantId, adUserId, queryStr);
        if (CollUtil.isEmpty(merchantStoreIds)) {
            return 0;
        }

        int totalStoreNum = merchantStoreIds.size();

        List<List<Long>> partition = Lists.partition(merchantStoreIds, getBatchSize());
        int boundCount = partition.stream()
                .filter(CollUtil::isNotEmpty)
                .map(ids -> {
                    SuperStaffBoundStoreQuery query = new SuperStaffBoundStoreQuery();
                    query.setMerchantId(merchantId);
                    query.setStoreIds(ids);
                    BaseResponse<Integer> response1 = superStaffRuleClient.boundCountByStoreIds(query);
                    if (!RemoteResponseUtils.checkResponse(response1)) {
                        throw new BusinessException("场地绑定信息获取失败，稍后重试");
                    }

                    return ofNullable(RemoteResponseUtils.getData(response1)).orElse(0);
                })
                .mapToInt(i -> i)
                .sum();

        if (boundCount >= totalStoreNum) {
            return 0;
        }

        return totalStoreNum - boundCount;
    }

    @Override
    public void delete(SuperStaffRuleDeleteDTO dto) {
        List<Long> ruleAllMatchStoreIds = findRuleAllMatchStoreIds(dto.getRuleId(), dto.getMerchantId());

        BaseResponse<Void> response = superStaffRuleClient.delete(dto);
        if (!RemoteResponseUtils.checkResponse(response)) {
            throw new BusinessException("规则移除失败，刷新重试");
        }

        handleRuleChangeNotify(ruleAllMatchStoreIds, dto.getMerchantId(), dto.getRuleId());
    }

    /**
     * 处理绑定选择时确认的场地id
     */
    private List<Long> findAllStoreIds(Long merchantId, Long adUserId, String storeQuery) {

        MerchantGroupRequest request = new MerchantGroupRequest();
        request.setIsActive(1);
        request.setDistributor(merchantId);
        request.setAdUser(adUserId);
        request.setContext(storeQuery);
        int groupTotal = ofNullable(merchantGroupService.groupCount(request))
                .filter(RemoteResponseUtils::checkResponse)
                .map(RemoteResponseUtils::getData)
                .orElseThrow(() -> new BusinessException("场地数据获取失败"));
        if (groupTotal <= 0) {
            return Collections.emptyList();
        }

        int batchSizeValue = getBatchSize();

        List<Long> storeIds = new ArrayList<>(groupTotal);
        int pageNums = calculatePageNum(groupTotal, batchSizeValue);

        request.setPageSize(batchSizeValue);
        for (int i = 1 ; i <= pageNums; i++) {
            request.setPageIndex(i);
            BaseResponse<List<Long>> response = merchantGroupService.groupIdPage(request);
            if (!RemoteResponseUtils.checkResponse(response)) {
                throw new BusinessException("场地数据获取失败，稍后重试");
            }

            List<Long> data = RemoteResponseUtils.getData(response);
            if (CollUtil.isEmpty(data)) {
                break;
            }

            storeIds.addAll(data);
        }

        return storeIds;
    }

    public List<Long> findRuleAllMatchStoreIds(Long ruleId, Long merchantId) {
        BaseResponse<SuperStaffRuleDTO> response = superStaffRuleClient.ruleDetail(ruleId, merchantId);
        if (!RemoteResponseUtils.checkResponse(response)) {
            throw new BusinessException("规则信息获取失败，稍后重试");
        }

        SuperStaffRuleDTO ruleDTO = RemoteResponseUtils.getData(response);
        if (Objects.isNull(ruleDTO)) {
            throw new BusinessException("规则已失效");
        }

        boolean defaultMatch = ofNullable(ruleDTO.getDefaultMatch()).orElse(false);
        List<Long> storeIds;
        if (defaultMatch) {
            storeIds = fetchAllDefaultMatchStoreIds(ruleDTO.getRuleId(), ruleDTO.getMerchantId());
        } else {
            storeIds = fetchAllRuleBoundStoreIds(ruleDTO.getRuleId());
        }

        if (CollUtil.isEmpty(storeIds)) {
            return Collections.emptyList();
        }

        return storeIds;
    }

    private List<Long> fetchAllDefaultMatchStoreIds(Long ruleId, Long merchantId) {
        List<Long> merchantStoreIds = findMerchantStoreIds(merchantId);
        if (CollUtil.isEmpty(merchantStoreIds)) {
            return Collections.emptyList();
        }

        List<Long> filterBoundStoreIds = merchantFilterRuleBoundStoreIds(merchantId, ruleId);
        if (CollUtil.isNotEmpty(filterBoundStoreIds)) {
            merchantStoreIds.removeAll(filterBoundStoreIds);
        }

        return merchantStoreIds;
    }

    private List<Long> fetchAllRuleBoundStoreIds(Long ruleId) {
        int count = ofNullable(superStaffRuleClient.countRuleBoundStores(ruleId))
                .filter(RemoteResponseUtils::checkResponse)
                .map(RemoteResponseUtils::getData)
                .orElseThrow(() -> new BusinessException("关联场地数据获取失败"));
        if (count < 1) {
            return Collections.emptyList();
        }

        List<Long> storeIds = new ArrayList<>(count);

        int size = getBatchSize();
        int batchNum = calculatePageNum(count, size);
        int offset = 0;
        for (int i = 1; i <= batchNum; i++) {
            List<Long> findIds = ofNullable(superStaffRuleClient.ruleBoundStorePage(ruleId, offset, size))
                    .filter(RemoteResponseUtils::checkResponse)
                    .map(RemoteResponseUtils::getData)
                    .orElseThrow(() -> new BusinessException("关联场地数据获取失败"));

            if (log.isDebugEnabled()) {
                log.debug("超级导购员｜获取非默认规则绑定场地 offset:{}, size:{}, findIds:{}", offset, size, findIds);
            }

            if (CollUtil.isEmpty(findIds)) {
                break;
            }

            storeIds.addAll(findIds);

            offset += size;
        }

        return storeIds;
    }

    /**
     * 获取商家所有场地ids
     */
    private List<Long> findMerchantStoreIds(Long merchantId) {
        MerchantGroupRequest request = new MerchantGroupRequest();
        request.setIsActive(1);
        request.setDistributor(merchantId);
        BaseResponse<Integer> countResponse = merchantGroupService.groupCount(request);
        if (Objects.equals(countResponse.getCode(), ResponseCodeEnum.FAIL.getCode())) {
            log.info("超级导购员｜获取商家所有场地id 获取场地总数失败:{}", countResponse);
            throw new BusinessException("商家场地数据获取失败，稍后重试");
        }

        int groupTotal = of(countResponse)
                .map(BaseResponse::getData)
                .orElse(0);
        if (groupTotal <= 0) {
            return Collections.emptyList();
        }

        int size = getBatchSize();

        List<Long> storeIds = new ArrayList<>(groupTotal);
        int pageNums = calculatePageNum(groupTotal, size);

        request.setPageSize(size);
        for (int i = 1; i <= pageNums; i++) {
            request.setPageIndex(i);
            BaseResponse<List<Long>> response = merchantGroupService.groupIdPage(request);
            if (Objects.equals(ResponseCodeEnum.FAIL.getCode(), response.getCode())) {
                log.info("超级导购员｜获取商家所有场地id 分页获取场地失败:{}", countResponse);
                throw new BusinessException("商家场地数据获取失败，稍后重试");
            }

            List<Long> data = response.getData();
            if (CollUtil.isEmpty(data)) {
                break;
            }

            storeIds.addAll(data);
        }

        return storeIds;
    }

    private List<Long> merchantFilterRuleBoundStoreIds(Long merchantId, Long filterRuleId) {
        int count = ofNullable(superStaffRuleClient.countMerchantFilterRuleBoundStores(merchantId, filterRuleId))
                .filter(RemoteResponseUtils::checkResponse)
                .map(RemoteResponseUtils::getData)
                .orElseThrow(() -> new BusinessException("关联场地数据获取失败"));
        if (count < 1) {
            log.info("超级导购员｜获取非指定规则其它绑定场地id 商家:{} 过滤规则:{} 后无已绑定的场地", merchantId, filterRuleId);
            return Collections.emptyList();
        }

        List<Long> merchantBoundStoreIds = new ArrayList<>(count);

        int size = getBatchSize();
        int batchNum = calculatePageNum(count, size);
        int offset = 0;
        for (int i = 1; i <= batchNum; i++) {
            List<Long> findIds = ofNullable(superStaffRuleClient.merchantFilterRuleBoundStorePage(merchantId, filterRuleId, offset, size))
                    .filter(RemoteResponseUtils::checkResponse)
                    .map(RemoteResponseUtils::getData)
                    .orElseThrow(() -> new BusinessException("关联场地数据获取失败"));
            if (CollUtil.isEmpty(findIds)) {
                break;
            }

            merchantBoundStoreIds.addAll(findIds);

            offset += size;
        }

        return merchantBoundStoreIds;
    }

    private int calculatePageNum(int total, int pageSize) {
        if (total <= pageSize) {
            return 1;
        }

        return new BigDecimal(total).divide(new BigDecimal(pageSize), 0, RoundingMode.UP).intValue();
    }
}
