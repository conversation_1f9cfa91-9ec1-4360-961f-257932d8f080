package cn.lyy.merchant.service.superstaff;

import cn.lyy.lyy_cmember_service_api.dto.aidbj.launch.AiDbjServiceAdRecommendResponseDTO;
import cn.lyy.marketing.dto.superstaff.v2.SuperStaffRuleV2DTO;
import cn.lyy.merchant.dto.superstaff.SuperStaffStoreQuery;
import cn.lyy.merchant.dto.superstaff.v2.StoreWithEquipmentListDTO;
import cn.lyy.merchant.dto.superstaff.v2.SuperStaffRuleCreatedV2Command;
import cn.lyy.merchant.dto.superstaff.v2.SuperStaffRuleUpdateV2Command;
import com.lyy.user.app.infrastructure.resp.PageInfo;

/**
 * @date: 2023-11-16
 * @author: YUNLONG
 */
public interface SuperStaffV2Service {

    Long createRule(SuperStaffRuleCreatedV2Command command, Long merchantId, Long operatorId);

    void editRule(SuperStaffRuleUpdateV2Command command, Long merchantId, Long operatorId);

    int totalUnboundCount(Long merchantId, Long adUserId);

    PageInfo<StoreWithEquipmentListDTO> equipmentStoreList(SuperStaffStoreQuery query, Long merchantId, Long adUserId);

    SuperStaffRuleV2DTO detail(Long ruleId, Long merchantId);

    AiDbjServiceAdRecommendResponseDTO getAdRecommendInfo(Long ruleId);

    PageInfo<StoreWithEquipmentListDTO> equipmentStoreListV2(SuperStaffStoreQuery query);
}
