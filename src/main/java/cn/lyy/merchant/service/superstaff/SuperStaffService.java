package cn.lyy.merchant.service.superstaff;

import cn.lyy.marketing.dto.superstaff.SuperStaffRuleDeleteDTO;
import cn.lyy.merchant.dto.superstaff.SuperStaffRuleBindStoreDTO;
import cn.lyy.merchant.dto.superstaff.SuperStaffRuleCreateRequest;
import cn.lyy.merchant.dto.superstaff.SuperStaffRuleUpdateRequest;

/**
 * @date: 2023-10-24
 * @author: YUNLONG
 */
public interface SuperStaffService {

    Long createRule(SuperStaffRuleCreateRequest request, Long merchantId, Long operatorId);

    void editRule(SuperStaffRuleUpdateRequest request, Long merchantId, Long operatorId);

    void batchStoreBound(SuperStaffRuleBindStoreDTO dto, Long merchantId, Long adUserId);

    int totalUnboundCount(Long merchantId, Long adUserId, String queryStr);

    void delete(SuperStaffRuleDeleteDTO dto);
}
