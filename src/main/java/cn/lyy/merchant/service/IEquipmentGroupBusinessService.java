package cn.lyy.merchant.service;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.equipment.dto.equipment.EquipmentManageDTO;
import cn.lyy.merchant.dto.group.GroupVO;
import cn.lyy.merchant.dto.group.SaveGroupDTO;
import cn.lyy.merchant.dto.merchant.response.MerchantGroupDTO;
import cn.lyy.merchant.dto.response.MerchantEquipmentDTO;
import cn.lyy.merchant.dto.response.MerchantGroupListDTO;

import java.util.List;
import java.util.Map;

/**
 * 设备场地信息
 */
public interface IEquipmentGroupBusinessService {

    List<MerchantGroupListDTO> getGroupInfoDistributorId(Long distributorId, Long userId,
                                                         boolean isShowCont,String context,
                                                         Long labelId);

    /**
     * 获取没有标签的场地列表
     * @param distributorId
     * @param userId
     * @param isShowCont
     * @param typeValue
     * @return
     */
    List<MerchantGroupDTO> getGroupNameAndAddressNoLabel(Long distributorId, Long userId,boolean isShowCont,String typeValue);

    Long saveGroup(SaveGroupDTO param);

    /**
     * 获取省市区级联信息
     * @return
     */
    List<Map<String, Object>> getAllDistrict();

    void deleteGroup(Long groupId, Long adOrgIdNotNull, Long adUserIdNotNull);

    /**
     * 根据场地id获取场地信息
     * @param groupId
     * @return
     */
    MerchantGroupDTO getGroupInfoById(Long groupId);

    List<Integer> queryGroupNumber(Long groupId, Long distributorId);

    List<GroupVO> getGroupByEtype(Long equipmentTypeId, Long distributorId);

    MerchantEquipmentDTO getGroupEquipmentByTypeProduct(String equipmentType, Long adUserId, Boolean isApprover, Long adOrgId, Long productId);
}
