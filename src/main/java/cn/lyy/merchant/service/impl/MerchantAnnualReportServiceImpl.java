package cn.lyy.merchant.service.impl;

import static java.util.Optional.ofNullable;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.utils.GsonUtils;
import cn.lyy.lyy_data_service_api.DaMerchantAnnualReportYiDTO;
import cn.lyy.merchant.api.service.MerchantEquipmentService;
import cn.lyy.merchant.constants.BusinessExceptionEnums;
import cn.lyy.merchant.dto.DaMerchantAnnualReportYiVO;
import cn.lyy.merchant.dto.merchant.MerchantEquipmentDTO;
import cn.lyy.merchant.dto.merchant.equpiment.request.MerchantEquipmentBaseInfoQueryRequest;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.microservice.LyyDataService;
import cn.lyy.merchant.redis.MerchantRedisClient;
import cn.lyy.merchant.redis.MerchantRedisKeyEnum;
import cn.lyy.merchant.service.MerchantAnnualReportService;
import cn.lyy.merchant.utils.RemoteResponseUtils;
import cn.lyy.tools.equipment.LyyConstant;
import com.github.pagehelper.PageInfo;
import com.google.gson.Gson;
import com.lyy.charge.dto.response.Pagination;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> =￣ω￣=
 * @date 2022/12/2
 */
@Slf4j
@Service
public class MerchantAnnualReportServiceImpl implements MerchantAnnualReportService {

    @Value("${merchant.annual.report.year}")
    private String year;

    @Value("${merchant.annual.report.popups.date}")
    private String popUpsDate;

//    @Value("${merchant.annual.report.popups.interval}")
//    private Long popUpsInterval;

    @Value("${merchant.annual.report.popups.count}")
    private Integer popUpscount;

    @Value("${merchant.annual.report.desc.gmv1wDesc:再加把劲，明年一定会更好!}")
    private String gmv1wDesc;
    @Value("${merchant.annual.report.desc.gmv1W_10WDesc:是您，撑起了行业的半边天!}")
    private String gmv1W_10WDesc;
    @Value("${merchant.annual.report.desc.gmv10W_50WDesc:成为了超越同行的行业中流砥柱!}")
    private String gmv10W_50WDesc;
    @Value("${merchant.annual.report.desc.gmv50W_100WDesc:成为了超越同行的行业领头羊!}")
    private String gmv50W_100WDesc;
    @Value("${merchant.annual.report.desc.gmv100WDesc:成为了超越同行的行业天花板!}")
    private String gmv100WDesc;

    private static final String ALL_MERCHANT = "all";

    private static final Integer DEFAULT_PAGESIZE = 100;

    /**
     * 1万
     */
    private final BigDecimal TEN_THOUSAND = new BigDecimal(10000);

    /**
     * 10万
     */
    private final BigDecimal ONE_HUNDRED_THOUSAND = new BigDecimal(100000);

    /**
     * 50万
     */
    private final BigDecimal FIVE_HUNDRED_THOUSAND = new BigDecimal(500000);

    /**
     * 100万
     */
    private final BigDecimal ONE_MILLION = new BigDecimal(1000000);


    @Autowired
    private LyyDataService lyyDataService;

    @Autowired
    private MerchantEquipmentService merchantEquipmentService;

    @Override
    public DaMerchantAnnualReportYiVO getByMerchantId(Long merchantId) {
        String reportJson = MerchantRedisClient.get(MerchantRedisKeyEnum.MERCHANT_ANNUAL_REPORT, merchantId.toString());
        String allReportJson = MerchantRedisClient.get(MerchantRedisKeyEnum.MERCHANT_ANNUAL_REPORT, ALL_MERCHANT);
        DaMerchantAnnualReportYiVO daMerchantAnnualReportYiVo = new DaMerchantAnnualReportYiVO();
        DaMerchantAnnualReportYiVO allMerchantAnnualReportVo = new DaMerchantAnnualReportYiVO();

        if (StrUtil.isBlank(reportJson)) {
            DaMerchantAnnualReportYiDTO merchantAnnualReport = lyyDataService.getByMerchantIdAndYear(merchantId.toString(), year);
            log.info("查询 merchantId: {}, DaMerchantAnnualReportYiDTO: {}", merchantId, GsonUtils.toJson(merchantAnnualReport));
            if (merchantAnnualReport != null) {
                BeanUtil.copyProperties(merchantAnnualReport, daMerchantAnnualReportYiVo);
                if (BigDecimal.ONE.compareTo(daMerchantAnnualReportYiVo.getGmvAmount()) >= 0 ||
                        BigDecimal.ZERO.compareTo(daMerchantAnnualReportYiVo.getMostSetAmount()) >= 0) {
                    throw new BusinessException(BusinessExceptionEnums.MERCHANT_ANNUAL_REPORT_NULL);
                }
            } else {
                throw new BusinessException(BusinessExceptionEnums.MERCHANT_ANNUAL_REPORT_NOT_EXIST);
            }
            if (StrUtil.isBlank(allReportJson)) {
                DaMerchantAnnualReportYiDTO allMerchantAnnualReport = lyyDataService.getByMerchantIdAndYear(ALL_MERCHANT, year);
                log.info("merchantId: ALL, DaMerchantAnnualReportYiDTO: {}", GsonUtils.toJson(allMerchantAnnualReport));
                if (allMerchantAnnualReport != null) {
                    BeanUtil.copyProperties(allMerchantAnnualReport, allMerchantAnnualReportVo);
                }
                MerchantRedisClient.setex(MerchantRedisKeyEnum.MERCHANT_ANNUAL_REPORT, ALL_MERCHANT, GsonUtils.toJson(allMerchantAnnualReportVo));
            } else {
                allMerchantAnnualReportVo = new Gson().fromJson(allReportJson, DaMerchantAnnualReportYiVO.class);
            }
            handlerData(daMerchantAnnualReportYiVo, allMerchantAnnualReportVo);
            checkHasDbj(merchantId, daMerchantAnnualReportYiVo);
            MerchantRedisClient.setex(MerchantRedisKeyEnum.MERCHANT_ANNUAL_REPORT, merchantId.toString(), GsonUtils.toJson(daMerchantAnnualReportYiVo));
        } else {
            if (log.isDebugEnabled()) {
                log.debug("从缓存中读取: merchantId = {}", merchantId);
            }

            daMerchantAnnualReportYiVo = new Gson().fromJson(reportJson, DaMerchantAnnualReportYiVO.class);
            if (StrUtil.isBlank(daMerchantAnnualReportYiVo.getMerchantId())) {
                throw new BusinessException(BusinessExceptionEnums.MERCHANT_ANNUAL_REPORT_NOT_EXIST);
            }
            if (BigDecimal.ZERO.compareTo(daMerchantAnnualReportYiVo.getGmvAmount()) >= 0 ||
                    BigDecimal.ZERO.equals(daMerchantAnnualReportYiVo.getMostSetAmount())) {
                throw new BusinessException(BusinessExceptionEnums.MERCHANT_ANNUAL_REPORT_NULL);
            }
            checkHasDbj(merchantId, daMerchantAnnualReportYiVo);
        }
        return daMerchantAnnualReportYiVo;
    }

    private void checkHasDbj(Long merchantId, DaMerchantAnnualReportYiVO daMerchantAnnualReportYiVo) {
        if (Objects.nonNull(daMerchantAnnualReportYiVo.getHasDbj())) {
            return;
        }

        MerchantEquipmentBaseInfoQueryRequest request = new MerchantEquipmentBaseInfoQueryRequest();
        request.setMerchantId(merchantId);
        request.setEquipmentTypeId((long) LyyConstant.DBJ_TYPE_ID);
        request.setPageIndex(1);
        request.setPageSize(1);
        BaseResponse<PageInfo<MerchantEquipmentDTO>> response = merchantEquipmentService.pageEquipment(request);
        if (!RemoteResponseUtils.checkResponse(response)) {
            log.info("年度报告｜获取商家兑币机数据 商家:{} 获取失败:{}", merchantId, response);
            throw new BusinessException("数据获取失败，重试");
        }

        ofNullable(RemoteResponseUtils.getData(response))
                .map(PageInfo::getTotal)
                .map(o -> o > 0L)
                .ifPresent(daMerchantAnnualReportYiVo::setHasDbj);
    }

    @Override
    public void cacheReportByMerchantIds(List<String> merchantIds) {
        log.info("手动缓存 merchantIds: {}", GsonUtils.toJson(merchantIds));
        if (CollUtil.isNotEmpty(merchantIds)) {
            DaMerchantAnnualReportYiVO allMerchantAnnualReportVo = new DaMerchantAnnualReportYiVO();
            DaMerchantAnnualReportYiDTO allMerchantAnnualReport = lyyDataService.getByMerchantIdAndYear(ALL_MERCHANT, year);
            if (allMerchantAnnualReport != null) {
                BeanUtil.copyProperties(allMerchantAnnualReport, allMerchantAnnualReportVo);
            }
            MerchantRedisClient.setex(MerchantRedisKeyEnum.MERCHANT_ANNUAL_REPORT, ALL_MERCHANT, GsonUtils.toJson(allMerchantAnnualReportVo));

            for (String merchantId : merchantIds) {
                DaMerchantAnnualReportYiVO daMerchantAnnualReportYiVo = new DaMerchantAnnualReportYiVO();
                DaMerchantAnnualReportYiDTO merchantAnnualReport = lyyDataService.getByMerchantIdAndYear(merchantId, year);
                if (merchantAnnualReport != null) {
                    BeanUtil.copyProperties(merchantAnnualReport, daMerchantAnnualReportYiVo);
                    handlerData(daMerchantAnnualReportYiVo, allMerchantAnnualReportVo);
                    MerchantRedisClient.setex(MerchantRedisKeyEnum.MERCHANT_ANNUAL_REPORT, merchantId, GsonUtils.toJson(daMerchantAnnualReportYiVo));
                }
            }
        } else {
            cacheAllReport();
        }
    }

    private void cacheAllReport() {
        log.info("缓存所有商家年度报告数据");
        Integer count = lyyDataService.count(year, BigDecimal.valueOf(0));
        log.info("一共有{}条商家年度报告数据", count);
        int pageIndex = 0;
        while (count != null && count > 0) {
            pageIndex = pageIndex + 1;
            count = count - DEFAULT_PAGESIZE;
            Pagination<DaMerchantAnnualReportYiDTO> pagination = lyyDataService.listPageByYear(year, BigDecimal.valueOf(0), pageIndex, DEFAULT_PAGESIZE);
            if (pagination != null && CollUtil.isNotEmpty(pagination.getItems())) {
                List<String> merchantIds = pagination.getItems().stream().map(DaMerchantAnnualReportYiDTO::getMerchantId).collect(Collectors.toList());
                cacheReportByMerchantIds(merchantIds);
            }
        }
        log.info("缓存所有商家年度报告数据完毕");
    }

    @Override
    public void deleteCacheReportByMerchantIds(List<String> merchantIds) {
        log.info("手动删除缓存 merchantIds: {}", GsonUtils.toJson(merchantIds));
        if (CollUtil.isNotEmpty(merchantIds)) {
            for (String merchantId : merchantIds) {
                MerchantRedisClient.del(MerchantRedisKeyEnum.MERCHANT_ANNUAL_REPORT, merchantId);
            }
        } else {
            deleteCacheAllReport();
        }
    }

    @Override
    public Boolean isPopUps(String merchantId) {
        if (popUpscount == null || popUpscount <= 0 || StrUtil.isBlank(popUpsDate)) {
            return false;
        }
        DateTime time = DateUtil.parse(popUpsDate, "yyyyMMdd");
        DateTime today = DateUtil.parse(DateUtil.format(new Date(), "yyyyMMdd"), "yyyyMMdd");
        log.debug("time: {}, today: {}", time, today);
        if (time.after(today)) {
            log.debug("弹窗时间还未开始");
            return false;
        }
        String count = MerchantRedisClient.get(MerchantRedisKeyEnum.MERCHANT_ANNUAL_REPORT_POP_UPS_MERCHANT_ID, merchantId);
        log.info("count: {}", count);
        // 商户弹窗次数已达到
        if (StrUtil.isNotBlank(count) && Integer.parseInt(count) >= popUpscount) {
            return false;
        }
        // 商户今天是否已弹窗
        String key = merchantId + ":" + today.toString("yyyyMMdd");
        String exist = MerchantRedisClient.get(MerchantRedisKeyEnum.MERCHANT_ANNUAL_REPORT_POP_UPS_DATE_MERCHANT_ID, key);
        if (StrUtil.isNotBlank(exist)) {
            return false;
        }
        // 记录缓存
        int times = 0;
        if (StrUtil.isBlank(count)) {
            times = 0;
        } else {
            times += 1;
        }
        MerchantRedisClient.setex(MerchantRedisKeyEnum.MERCHANT_ANNUAL_REPORT_POP_UPS_MERCHANT_ID, merchantId, String.valueOf(times));
        MerchantRedisClient.setex(MerchantRedisKeyEnum.MERCHANT_ANNUAL_REPORT_POP_UPS_DATE_MERCHANT_ID, key, "1");
        return true;
    }

    @Override
    public void deletePopUpsCache(String merchantId, String today) {
        MerchantRedisClient.del(MerchantRedisKeyEnum.MERCHANT_ANNUAL_REPORT_POP_UPS_MERCHANT_ID, merchantId);
        String key = merchantId + ":" + today;
        MerchantRedisClient.del(MerchantRedisKeyEnum.MERCHANT_ANNUAL_REPORT_POP_UPS_DATE_MERCHANT_ID, key);
    }

    private void deleteCacheAllReport() {
        log.info("手动删除所有商家年度报告缓存");
        Integer count = lyyDataService.count(year, BigDecimal.valueOf(0));
        log.info("一共有{}条商家年度报告数据", count);
        int pageIndex = 0;
        while (count != null && count > 0) {
            pageIndex = pageIndex + 1;
            count = count - DEFAULT_PAGESIZE;
            Pagination<DaMerchantAnnualReportYiDTO> pagination = lyyDataService.listPageByYear(year, BigDecimal.valueOf(0), pageIndex, DEFAULT_PAGESIZE);
            if (pagination != null && CollUtil.isNotEmpty(pagination.getItems())) {
                List<String> merchantIds = pagination.getItems().stream().map(DaMerchantAnnualReportYiDTO::getMerchantId).collect(Collectors.toList());
                deleteCacheReportByMerchantIds(merchantIds);
            }
        }
        log.info("手动删除所有商家年度报告缓存完毕");
    }

    private void handlerData(DaMerchantAnnualReportYiVO daMerchantAnnualReportYiVo, DaMerchantAnnualReportYiVO allMerchantAnnualReportVo) {

        daMerchantAnnualReportYiVo.setGmvAmountDesc(getGmvDesc(daMerchantAnnualReportYiVo.getGmvAmount()));

        String merchantRegisterTm = daMerchantAnnualReportYiVo.getMerchantRegisterTm();
        daMerchantAnnualReportYiVo.setMerchantRegisterTm(ofNullable(merchantRegisterTm).map(m -> {
            return DateUtil.format(DateUtil.parse(merchantRegisterTm, "yyyy-MM-dd"), "yyyy年MM月dd日");
        }).orElse(merchantRegisterTm));

        String timeEndLatest = daMerchantAnnualReportYiVo.getTimeEndLatest();
        daMerchantAnnualReportYiVo.setTimeEndLatest(ofNullable(timeEndLatest).map(m -> {
            return DateUtil.format(DateUtil.parse(timeEndLatest, "yyyy-MM-dd HH:mm:ss"), "MM月dd日 HH:mm");
        }).orElse(timeEndLatest));

        String maxGmvDate = daMerchantAnnualReportYiVo.getMaxGmvDate();
        daMerchantAnnualReportYiVo.setMaxGmvDate(ofNullable(maxGmvDate).map(m -> {
            return DateUtil.format(DateUtil.parse(maxGmvDate, "yyyy-MM-dd"), "MM月dd日");
        }).orElse(maxGmvDate));

        if (allMerchantAnnualReportVo != null) {
            daMerchantAnnualReportYiVo.setTotalGroupNum(allMerchantAnnualReportVo.getTotalGroupNum());
            daMerchantAnnualReportYiVo.setNewRegisterGroupNum(allMerchantAnnualReportVo.getNewRegisterGroupNum());
        }
    }

    private String getGmvDesc(BigDecimal gmvAmount) {
        if (gmvAmount != null) {
            if (TEN_THOUSAND.compareTo(gmvAmount) > 0) {  // gmv < 1万
                return gmv1wDesc;
            } else if (TEN_THOUSAND.compareTo(gmvAmount) <= 0
                    && ONE_HUNDRED_THOUSAND.compareTo(gmvAmount) > 0) {  // 1万 <= gmv < 10万
                return gmv1W_10WDesc;
            } else if (ONE_HUNDRED_THOUSAND.compareTo(gmvAmount) <= 0
                    && FIVE_HUNDRED_THOUSAND.compareTo(gmvAmount) > 0) {  // 10万 <= gmv < 50万
                return gmv10W_50WDesc;
            } else if (FIVE_HUNDRED_THOUSAND.compareTo(gmvAmount) <= 0  // 50万 <= gmv < 100万
                    && ONE_MILLION.compareTo(gmvAmount) > 0) {
                return gmv50W_100WDesc;
            } else if (ONE_MILLION.compareTo(gmvAmount) >= 0) {  // 100万 <= gmv
                return gmv100WDesc;
            }
        }
        return "";
    }
}
