package cn.lyy.merchant.service.impl;

import static java.util.Optional.ofNullable;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.base.dto.Pagination;
import cn.lyy.equipment.dto.equipment.EquipmentListRequest;
import cn.lyy.equipment.dto.equipment.EquipmentListResponseDTO;
import cn.lyy.equipment.service.IEquipmentService;
import cn.lyy.lyy_ic_service_api.LyyIcService;
import cn.lyy.lyy_ic_service_api.dto.IcCardFlowQueryRequestDTO;
import cn.lyy.lyy_ic_service_api.dto.IcCardTotalCountDTO;
import cn.lyy.lyy_ic_service_api.dto.LyyIcCardDTO;
import cn.lyy.lyy_ic_service_api.dto.LyyIcCardDetailDTO;
import cn.lyy.lyy_ic_service_api.dto.LyyIcCardFlowDTO;
import cn.lyy.lyy_ic_service_api.dto.LyyIcCardFlowListDTO;
import cn.lyy.lyy_ic_service_api.dto.LyyIcCardTotalEarningDTO;
import cn.lyy.lyy_ic_service_api.dto.LyyIcConfigDTO;
import cn.lyy.lyy_ic_service_api.dto.LyyIcFillingPreferentialGroupDto;
import cn.lyy.lyy_ic_service_api.dto.LyyIcListParamDTO;
import cn.lyy.lyy_ic_service_api.dto.LyyIcRecordsFlowParamDTO;
import cn.lyy.lyy_ic_service_api.enums.ICCardFlowTypes;
import cn.lyy.merchant.constants.BusinessChangeEnum;
import cn.lyy.merchant.dto.ChangeIcCardBalanceDTO;
import cn.lyy.merchant.dto.StatisticsItemDTO;
import cn.lyy.merchant.dto.response.IcCardConsumeDTO;
import cn.lyy.merchant.dto.response.PreferentialListDTO;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.service.IcBusinessService;
import cn.lyy.merchant.util.DateUtils;
import com.alibaba.fastjson.JSON;
import com.lyy.commodity.rpc.constants.CategoryEnum;
import com.lyy.commodity.rpc.constants.ChannelEnum;
import com.lyy.commodity.rpc.constants.ClassifyCodeEnum;
import com.lyy.commodity.rpc.constants.ValueUnitEnum;
import com.lyy.commodity.rpc.dto.EquipmentDisplayDTO;
import com.lyy.commodity.rpc.dto.FixPriceValueDTO;
import com.lyy.commodity.rpc.dto.request.CommodityDTO;
import com.lyy.commodity.rpc.dto.request.CommodityFixPriceDTO;
import com.lyy.commodity.rpc.dto.request.CommodityFixPriceValueDTO;
import com.lyy.commodity.rpc.dto.request.DisplayReqDTO;
import com.lyy.commodity.rpc.dto.request.RelateEquipmentDTO;
import com.lyy.commodity.rpc.dto.request.SkuCommodityDTO;
import com.lyy.commodity.rpc.feign.ICommodityRelatedService;
import com.lyy.commodity.rpc.feign.ISkuService;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @ClassName: IcBusinessServiceImpl
 * @description: IC卡业务
 * @author: pengkun
 * @create: 2020-11-03 13:46
 * @Version 1.0
 **/
@Service
@Slf4j
public class IcBusinessServiceImpl implements IcBusinessService {

    @Autowired
    private LyyIcService icService;

    @Autowired
    private IEquipmentService equipmentService;

    @Resource
    private ISkuService iSkuService;

    @Resource
    private ICommodityRelatedService commodityRelatedService;

    /**
     * 统计卡信息
     * @param adOrgId
     * @param queryStr
     * @param type
     * @param status
     * @return
     */
    @Override
    public IcCardTotalCountDTO findTotalCount(Long adOrgId, String queryStr, Integer type, Integer status) {
        return icService.findTotalCount(adOrgId.intValue(),queryStr,type,status);
    }

    /**
     * 获取Ic卡列表
     *
     * @param lyyIcListParamDTO
     * @return
     */
    @Override
    public Pagination<LyyIcCardDTO> queryListForMerchant(LyyIcListParamDTO lyyIcListParamDTO) {
        return icService.queryListForMerchant(lyyIcListParamDTO);
    }

    /**
     * 根据卡号获取卡信息
     *
     * @param adOrgId
     * @param cardNo
     * @return
     */
    @Override
    public LyyIcCardDetailDTO findByCardNo(Long adOrgId, String cardNo) {
        LyyIcCardDetailDTO detailDTO =icService.findByCardNo(adOrgId, cardNo);
        if (detailDTO != null) {
            detailDTO.setTotalConsumption(handleMoney(detailDTO.getTotalConsumption()));
            detailDTO.setTotalCharge(handleMoney(detailDTO.getTotalCharge()));
        }
        return detailDTO;
    }

    /**
     * 获取IC卡banner图
     *
     * @param adOrgId
     * @return
     */
    @Override
    public LyyIcConfigDTO icBanner(Long adOrgId) {
        return icService.icBanner(adOrgId.intValue());
    }

    /**
     * 更新IC卡banner图
     *
     * @param lyyIcConfigDTO
     * @param adOrgId
     * @return
     */
    @Override
    public Integer setBanner(LyyIcConfigDTO lyyIcConfigDTO, Long adOrgId) {
        if (lyyIcConfigDTO.getLyyIcConfigId() == null) {
            lyyIcConfigDTO.setLyyDistributorId(adOrgId.intValue());
        }
        return icService.setBanner(lyyIcConfigDTO);
    }

    /**
     * IC卡消费详情
     * @param requestDTO
     * @return
     */
    @Override
    public Pagination<IcCardConsumeDTO> listConsume(IcCardFlowQueryRequestDTO requestDTO) {
        //B端消费记录查询
        List<Integer> queryTypes = Arrays.asList(ICCardFlowTypes.D_CARD_FOR_CHARGING.getVal(),
                ICCardFlowTypes.D_CARD_FOR_WASHING.getVal(),ICCardFlowTypes.D_CARD_FOR_GET_WATER.getVal(),
                ICCardFlowTypes.CARD_REFUND_MONEY.getVal(),ICCardFlowTypes.PRESENT_FROM_MERCHANTS.getVal(),
                ICCardFlowTypes.MERCHANT_ADD_BALANCE.getVal(),ICCardFlowTypes.MERCHANT_SUB_BALANCE.getVal(),
                ICCardFlowTypes.D_CARD_STOP_CHARGING.getVal());
        requestDTO.setFlowTypes(queryTypes);
        Pagination<LyyIcCardFlowListDTO> result = icService.getIcCardFlowList(requestDTO);
        if(result == null){
            return new Pagination<>();
        }
        Pagination<IcCardConsumeDTO> page = new Pagination<>();
        BeanUtils.copyProperties(result,page);
        List<LyyIcCardFlowListDTO> items = result.getItems();
        List<IcCardConsumeDTO> list = null;
        if(items != null && items.size() > 0){
            List<Long> equipmentIds = new ArrayList<>();
            list = items.stream().map(dto ->{
                IcCardConsumeDTO icCardConsumeDTO = new IcCardConsumeDTO();
                //卡号
                icCardConsumeDTO.setCardNo(dto.getCardNo());
                //场地名称
                icCardConsumeDTO.setGroupName(dto.getLyyEquipmentGroup());
                if(dto.getLyyEquipmentId() != null){
                    equipmentIds.add(dto.getLyyEquipmentId());
                    icCardConsumeDTO.setEquipmentId(dto.getLyyEquipmentId());
                }
                icCardConsumeDTO.setCreateTime(dto.getCreateTime());
                icCardConsumeDTO.setUniqueCode(dto.getEquipmentNo());
                icCardConsumeDTO.setFlowAmount(dto.getFlowAmount());
                icCardConsumeDTO.setFlowPresent(dto.getFlowPresent());
                //消费类型
                if(ICCardFlowTypes.D_CARD_FOR_CHARGING.getVal() == dto.getFlowType().intValue() ||
                        ICCardFlowTypes.D_CARD_FOR_WASHING.getVal() == dto.getFlowType().intValue() ||
                        ICCardFlowTypes.D_CARD_FOR_GET_WATER.getVal() == dto.getFlowType().intValue()){
                    //刷卡消费
                    icCardConsumeDTO.setType(1);
                }else if(dto.getFlowType().intValue() == ICCardFlowTypes.CARD_REFUND_MONEY.getVal()
                        ||dto.getFlowType().intValue() == ICCardFlowTypes.D_CARD_STOP_CHARGING.getVal()){
                    //费用退款
                    icCardConsumeDTO.setType(2);
                }else if(dto.getFlowType().intValue() == ICCardFlowTypes.PRESENT_FROM_MERCHANTS.getVal()
                        || dto.getFlowType().intValue() == ICCardFlowTypes.MERCHANT_ADD_BALANCE.getVal()){
                    //余额增加
                    icCardConsumeDTO.setType(3);
                }else if(dto.getFlowType().intValue() == ICCardFlowTypes.MERCHANT_SUB_BALANCE.getVal()){
                    //余额扣减
                    icCardConsumeDTO.setType(4);
                }
                //状态 0初始状态 1操作成功 2失败 3超时 4无效 -1已退款
                icCardConsumeDTO.setStatus(dto.getStatus());
                return icCardConsumeDTO;
            }).collect(Collectors.toList());

            if(equipmentIds.size() > 0){
                EquipmentListRequest request = new EquipmentListRequest();
                request.setEquipments(equipmentIds);
                List<EquipmentListResponseDTO> equipmentList = ofNullable(equipmentService.selectEquipment(request))
                        .filter(r -> r.getCode() == ResponseCodeEnum.SUCCESS.getCode()).map(BaseResponse::getData).orElse(new ArrayList<>());
                Map<Long,EquipmentListResponseDTO> map = equipmentList.stream().collect(Collectors.toMap(EquipmentListResponseDTO::getEquipmentId,
                        Function.identity()));
                list.forEach(dto ->{
                    if(dto.getEquipmentId() != null){
                        if(map.containsKey(dto.getEquipmentId())){
                            EquipmentListResponseDTO equipmentListResponseDTO = map.get(dto.getEquipmentId());
                            dto.setEquipmentValue(equipmentListResponseDTO.getValue());
                            dto.setEquipmentTypeName(equipmentListResponseDTO.getTypeName());
                        }
                    }
                });
            }
        }
        page.setItems(list);
        return page;
    }

    /**
     * 保存Ic卡信息
     * @param lyyIcCardDTO
     * @return
     */
    @Override
    public Long saveCard(LyyIcCardDTO lyyIcCardDTO) {
        return icService.saveCard(lyyIcCardDTO);
    }

    @Override
    public List<LyyIcFillingPreferentialGroupDto> getFillingPreferentialGroups(Long merchantId) {
        return icService.getFillingPreferentialGroupIdList(merchantId.intValue());
    }

    /**
     * IC卡每日分析
     *
     * @param date
     * @param beforeNum
     * @param merchantId
     * @return
     */
    @Override
    public List<StatisticsItemDTO> statisticsIcCardEveryDay(String date, Integer beforeNum, Long merchantId) {
        LocalDate endDate = DateUtils.localDateParse(date, DateUtils.DatePattern.yyyy_MM_dd);
        List<StatisticsItemDTO> resultList = new ArrayList<>();
        for (int i = 0; i <= beforeNum; i++) {
            String queryDate = DateUtils.dateFormat(endDate.minusDays(i) , DateUtils.DatePattern.yyyy_MM_dd);
            resultList.add(genStatisticsItemDTO(queryDate , queryDate , queryDate , merchantId));
        }
        return resultList;
    }

    /**
     * IC卡每月分析
     * @param date
     * @param beforeNum
     * @param merchantId
     * @return
     */
    @Override
    public List<StatisticsItemDTO> statisticsIcCardEveryMonth(String date, Integer beforeNum, Long merchantId) {
        LocalDate endDate = DateUtils.localDateParse(date + "-01", DateUtils.DatePattern.yyyy_MM_dd);
        List<StatisticsItemDTO> resultList = new ArrayList<>();
        for (int i = 0; i <= beforeNum; i++) {
            String queryMonth = DateUtils.dateFormat(endDate.minusMonths(i), DateUtils.DatePattern.yyyy_MM);
            resultList.add(genStatisticsItemDTO(queryMonth, queryMonth + "-01", queryMonth + "-31", merchantId));
        }
        return resultList;
    }

    /**
     * 增减ic卡余额，只支持在线卡
     *
     * @param changeIcCardBalanceDTO
     * @param businessChangeEnum
     * @param userType
     * @return
     */
    @Override
    public int incrOnlineCardBalance(ChangeIcCardBalanceDTO changeIcCardBalanceDTO, BusinessChangeEnum businessChangeEnum, Integer userType) {
        AtomicInteger succNum = new AtomicInteger(0);
        BigDecimal incrAmount = changeIcCardBalanceDTO.getChangeAmount().setScale(2, RoundingMode.HALF_UP);
        changeIcCardBalanceDTO.getCards().stream().distinct().forEach(cardNO -> {
            LyyIcCardDTO card = icService.getByCardNo(changeIcCardBalanceDTO.getAdOrgId(), cardNO);
            BigDecimal beforeAmount = card.getAmount(); //元

            //卡类型：0-离线卡，1-在线卡
            //卡状态 在线卡：0-禁用，1-正常，2-挂失，3-未绑定
            //if ((card.getStatus() == 1 || card.getStatus() == 3) && card.getType() == 1) {
            if (card.getType() == 1) {
                BigDecimal changeAmount = incrAmount;
                //如果现时金额不足扣除，变动金额=现时金额扣除得0
                if (incrAmount.compareTo(BigDecimal.ZERO) < 0) {
                    if (beforeAmount.compareTo(BigDecimal.ZERO) < 0 ||
                            //剩余金额+剩余赠送金额 不够扣除时全部扣除
                            beforeAmount.add(incrAmount).compareTo(BigDecimal.ZERO) < 0) {
                        changeAmount = beforeAmount.negate();
                        log.debug("卡[{}]修改金额[{}]，卡余额：[{}]，将会变为0", cardNO, incrAmount, beforeAmount);
                    }
                }

                BigDecimal finalChangeAmount = changeAmount;
                 // 保存记录的同时会修改金额
                Long flowId = saveChangeRecord(card, finalChangeAmount);
                if(flowId != null){
                    //成功自增1
                    succNum.addAndGet(BigDecimal.ONE.intValue());
                }else{
                    log.warn("卡[{}]修改金额[{}]失败", cardNO, finalChangeAmount);
                }
            } else {
                log.warn("卡[{}]修改金额[{}]失败，是否在线卡：{}，卡状态：{}", cardNO, incrAmount, card.getType(), card.getStatus());
            }
        });
        return succNum.intValue();
    }

    /**
     * 获取ic卡余额修改记录
     * @param pageSize
     * @param pageIndex
     * @param type
     * @param adOrgId
     * @param searchKey
     * @return
     */
    @Override
    public Pagination<LyyIcCardFlowListDTO> listChangeIcCardBalance(Integer pageSize, Integer pageIndex, Integer type, Long adOrgId, String searchKey) {
        IcCardFlowQueryRequestDTO icCardFlowQueryRequestDTO = new IcCardFlowQueryRequestDTO();
        icCardFlowQueryRequestDTO.setPageSize(pageSize);
        icCardFlowQueryRequestDTO.setPageIndex(pageIndex);
        icCardFlowQueryRequestDTO.setMerchantId(adOrgId);
        List<Integer> flowTypeList = new ArrayList<>();
        flowTypeList.add(ICCardFlowTypes.MERCHANT_ADD_BALANCE.getVal());
        flowTypeList.add(ICCardFlowTypes.MERCHANT_SUB_BALANCE.getVal());
        icCardFlowQueryRequestDTO.setFlowTypes(flowTypeList);
        Pagination<LyyIcCardFlowListDTO>  pageInfo = icService.getIcCardFlowList(icCardFlowQueryRequestDTO);
        return pageInfo;
    }

    @Override
    public void saveFillingPreferential(Long id, BigDecimal price, BigDecimal value, Long distributorId, Long adUserId) {

        if(id == null) {
            SkuCommodityDTO skuCommodityDTO = new SkuCommodityDTO();
            skuCommodityDTO.setDistributor(distributorId);
            skuCommodityDTO.setAdUser(adUserId);
            CommodityDTO commodities = new CommodityDTO();
            commodities.setCategoryCode(CategoryEnum.IC_SET_MEAL.getCode());
            commodities.setClassifyCode(ClassifyCodeEnum.RECHARGE.getCode());
            commodities.setChannel(ChannelEnum.H5.getId());
            CommodityFixPriceDTO commodityFixPriceDTO = new CommodityFixPriceDTO();
            commodityFixPriceDTO.setPrice(price);

            List<CommodityFixPriceValueDTO> fixPriceValueDTOList = new ArrayList<>();
            CommodityFixPriceValueDTO commodityFixPriceValueDTO = new CommodityFixPriceValueDTO();
            commodityFixPriceValueDTO.setValue(value);
            commodityFixPriceValueDTO.setUnit(ValueUnitEnum.COIN.getId());
            fixPriceValueDTOList.add(commodityFixPriceValueDTO);
            commodityFixPriceDTO.setFixPriceValueList(fixPriceValueDTOList);

//            commodityFixPriceDTO.setValue(value);
//            commodityFixPriceDTO.setUnit(ValueUnitEnum.COIN.getId());

            commodities.setFixPrice(commodityFixPriceDTO);
            commodities.setName("IC卡充值优惠套餐");
            skuCommodityDTO.setCommodities(commodities);

            RelateEquipmentDTO relateEquipmentDTO = new RelateEquipmentDTO();
            relateEquipmentDTO.setStore(-1l);
            skuCommodityDTO.setEquipment(relateEquipmentDTO);

            BaseResponse<Long> response = iSkuService.addCommodity(skuCommodityDTO);
            Optional.ofNullable(response).filter(r -> r.getCode() == ResponseCodeEnum.SUCCESS.getCode()).orElseThrow(() -> {
                log.error("优惠增加失败 {}", response.getMessage());
                return new BusinessException(response.getMessage());
            });
        }else{
            // id 存在，更新操作
            SkuCommodityDTO skuCommodityDTO = new SkuCommodityDTO();
            skuCommodityDTO.setDistributor(distributorId);
            skuCommodityDTO.setAdUser(adUserId);
            CommodityDTO commodities = new CommodityDTO();
            commodities.setDisplay(id);  // 设备ID 一遍编辑
            commodities.setCategoryCode(CategoryEnum.IC_SET_MEAL.getCode());
            commodities.setClassifyCode(ClassifyCodeEnum.RECHARGE.getCode());
            commodities.setChannel(ChannelEnum.H5.getId());
            CommodityFixPriceDTO commodityFixPriceDTO = new CommodityFixPriceDTO();
            commodityFixPriceDTO.setPrice(price);
            //commodityFixPriceDTO.setValue(value);
            //commodityFixPriceDTO.setUnit(ValueUnitEnum.COIN.getId());
            List<CommodityFixPriceValueDTO> fixPriceValueDTOList = new ArrayList<>();
            CommodityFixPriceValueDTO commodityFixPriceValueDTO = new CommodityFixPriceValueDTO();
            commodityFixPriceValueDTO.setValue(value);
            commodityFixPriceValueDTO.setUnit(ValueUnitEnum.COIN.getId());
            fixPriceValueDTOList.add(commodityFixPriceValueDTO);
            commodityFixPriceDTO.setFixPriceValueList(fixPriceValueDTOList);

            commodities.setFixPrice(commodityFixPriceDTO);
            commodities.setName("IC卡充值优惠套餐");
            skuCommodityDTO.setCommodities(commodities);

            RelateEquipmentDTO relateEquipmentDTO = new RelateEquipmentDTO();
            relateEquipmentDTO.setStore(-1l);
            skuCommodityDTO.setEquipment(relateEquipmentDTO);

            BaseResponse<Boolean> response = commodityRelatedService.updateCommodity(skuCommodityDTO);
            Optional.ofNullable(response).filter(r -> r.getCode() == ResponseCodeEnum.SUCCESS.getCode()).orElseThrow(() -> {
                log.error("优惠修改失败 {}", response.getMessage());
                return new BusinessException(response.getMessage());
            });

        }

    }

    @Override
    public List<PreferentialListDTO> fillingPreferentialList(Long distributorId) {

        DisplayReqDTO displayReqDTO = new DisplayReqDTO();
        displayReqDTO.setCategoryCode(CategoryEnum.IC_SET_MEAL.getCode());
        displayReqDTO.setClassifyCode(ClassifyCodeEnum.RECHARGE.getCode());
        displayReqDTO.setDistributor(distributorId);
        displayReqDTO.setStore(-1L);
        BaseResponse<List<EquipmentDisplayDTO>> response = commodityRelatedService.selectDisplay(displayReqDTO);
        List<EquipmentDisplayDTO> equipmentDisplayDTOList = Optional.ofNullable(response)
                .filter(r -> r.getCode() == ResponseCodeEnum.SUCCESS.getCode()).map(BaseResponse::getData)
                .orElseThrow(() -> new BusinessException(response.getMessage()));
        List<PreferentialListDTO> list = equipmentDisplayDTOList.stream()
                .filter(displayDTO -> Objects.isNull(displayDTO.getRelateEquipment()) || Objects.equals(
                        displayDTO.getRelateEquipment().getStore(), -1L))
                .map(r -> {
                    PreferentialListDTO preferentialListDTO = new PreferentialListDTO();
                    preferentialListDTO.setId(r.getDisplay().getId());
                    preferentialListDTO.setPrice(r.getFixPrice().getPrice());
                    List<FixPriceValueDTO> fixPriceValueDTOS = r.getFixPriceValueList();
                    if (fixPriceValueDTOS != null && fixPriceValueDTOS.size() > 0) {
                        preferentialListDTO.setValue(fixPriceValueDTOS.get(0).getValue());
                    }
                    //preferentialListDTO.setValue(r.getFixPriceValue().getValue());
                    return preferentialListDTO;
                }).collect(Collectors.toList());
        return list;

    }

    /**
     * 保存商户用户操作记录
     * @param incrAmount
     */
    private Long saveChangeRecord( LyyIcCardDTO card , BigDecimal incrAmount ){
        LyyIcCardFlowDTO lyyIcCardFlowDTO = new LyyIcCardFlowDTO();
        // 增加余额
        if (incrAmount.compareTo(BigDecimal.ZERO) > -1) {
            lyyIcCardFlowDTO.setAmount(card.getAmount().subtract(ofNullable(card.getPresent()).orElse(BigDecimal.ZERO)));
            lyyIcCardFlowDTO.setPresent(card.getPresent());
            lyyIcCardFlowDTO.setFlowAmount(BigDecimal.ZERO);
            lyyIcCardFlowDTO.setFlowPresent(incrAmount);
            lyyIcCardFlowDTO.setFlowType(ICCardFlowTypes.MERCHANT_ADD_BALANCE.getVal().byteValue());
            lyyIcCardFlowDTO.setMemo(ICCardFlowTypes.MERCHANT_ADD_BALANCE.getDesc());
        } else {
            lyyIcCardFlowDTO.setFlowType(ICCardFlowTypes.MERCHANT_SUB_BALANCE.getVal().byteValue());
            lyyIcCardFlowDTO.setFlowAmount(incrAmount.negate());
            lyyIcCardFlowDTO.setMemo(ICCardFlowTypes.MERCHANT_SUB_BALANCE.getDesc());
        }
        lyyIcCardFlowDTO.setLyyUserId(card.getUserId());
        lyyIcCardFlowDTO.setMerchantId(card.getMerchantId());
        lyyIcCardFlowDTO.setCardNo(card.getCardNo());
        log.debug("invoke param:{}",JSON.toJSONString(lyyIcCardFlowDTO));
        Long l = icService.addCardFlow(lyyIcCardFlowDTO);
        log.info("result: {}", l);
        return l;
    }

    private StatisticsItemDTO genStatisticsItemDTO(String dateName , String beginDate  , String endDate, Long merchantId){
        LyyIcRecordsFlowParamDTO icRecordsFlowParamDTO = new LyyIcRecordsFlowParamDTO();
        icRecordsFlowParamDTO.setMerchantId(merchantId);
        icRecordsFlowParamDTO.setStartDate(beginDate);
        icRecordsFlowParamDTO.setEndDate(endDate);

        LyyIcCardTotalEarningDTO result = icService.totalEarningsByOrdersParam(icRecordsFlowParamDTO);
        StatisticsItemDTO statisticsItemDTO = new StatisticsItemDTO();
        statisticsItemDTO.setAmount(Objects.isNull(result) ? BigDecimal.ZERO : result.getConsumeAmount().setScale(2,RoundingMode.FLOOR));
        statisticsItemDTO.setReChargeAmount(Objects.isNull(result) ? BigDecimal.ZERO :  result.getTopUpAmount().setScale(2, RoundingMode.FLOOR));
        statisticsItemDTO.setDate(dateName);
        statisticsItemDTO.setCardCount(result == null ? 0 : result.getPayByCardAmount().intValue());
        return statisticsItemDTO;
    }

    private static BigDecimal handleMoney(BigDecimal money) {
        if (money == null) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(money.toString().replace("+", "").replace("-", ""));
    }
}
