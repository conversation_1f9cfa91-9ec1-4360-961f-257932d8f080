package cn.lyy.merchant.service.impl;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.base.converter.CommonConverterTools;
import cn.lyy.base.util.StringUtil;
import cn.lyy.equipment.constant.ClientTypeEnum;
import cn.lyy.equipment.constant.CommandFunctionConstants;
import cn.lyy.equipment.constant.EquipBuinessTypeEnum;
import cn.lyy.equipment.constant.MessageConstants;
import cn.lyy.equipment.constant.MqSourceConstants;
import cn.lyy.equipment.constant.RequestConstant;
import cn.lyy.equipment.dto.EquipmentInfoDTO;
import cn.lyy.equipment.dto.equipment.EquipmentAssociationDTO;
import cn.lyy.equipment.dto.equipment.EquipmentDTO;
import cn.lyy.equipment.dto.equipment.EquipmentListRequest;
import cn.lyy.equipment.dto.equipment.EquipmentListResponseDTO;
import cn.lyy.equipment.dto.equipment.EquipmentStatusDTO;
import cn.lyy.equipment.dto.equipment.EquipmentTypeDTO;
import cn.lyy.equipment.dto.equipment.EquipmentUpdateDTO;
import cn.lyy.equipment.dto.equipment.MainboardDTO;
import cn.lyy.equipment.dto.equipment.PositionDTO;
import cn.lyy.equipment.dto.equipment.RemoteCoinsSaveDTO;
import cn.lyy.equipment.dto.operation.OperationParam;
import cn.lyy.equipment.dto.query.EquipmentNumber;
import cn.lyy.equipment.sdk.CarChargingKit;
import cn.lyy.equipment.sdk.ChargingKit;
import cn.lyy.equipment.sdk.GashaponKit;
import cn.lyy.equipment.sdk.MassageKit;
import cn.lyy.equipment.sdk.WashKit;
import cn.lyy.equipment.sdk.constants.WasherConstants;
import cn.lyy.equipment.sdk.result.charging.EquipmentMacDTO;
import cn.lyy.equipment.service.EquipmentService;
import cn.lyy.equipment.service.IEquipmentFuncQueueService;
import cn.lyy.equipment.service.IEquipmentRedisService;
import cn.lyy.equipment.service.IEquipmentStatusService;
import cn.lyy.equipment.service.IEquipmentTypeService;
import cn.lyy.equipment.service.MainboardMicroService;
import cn.lyy.equipment.service.ProtocolMicroService;
import cn.lyy.equipment.service.RemoteCoinsMicroService;
import cn.lyy.lyy_data_service_api.iotcard.IotCardStatusDTO;
import cn.lyy.lyy_data_service_api.iotcard.QueryBatchIoTCardParam;
import cn.lyy.merchant.api.service.AdOrgClient;
import cn.lyy.merchant.api.service.MerchantEquipmentService;
import cn.lyy.merchant.api.service.MerchantGroupService;
import cn.lyy.merchant.config.EquipmentExtendFunctionConfig;
import cn.lyy.merchant.constants.BusinessExceptionEnums;
import cn.lyy.merchant.constants.EquipmentBusinessType;
import cn.lyy.merchant.constants.EquipmentLoginFlagTypeEnums;
import cn.lyy.merchant.constants.EquipmentTypeEnums;
import cn.lyy.merchant.constants.ProtocolCodeEnum;
import cn.lyy.merchant.constants.ProtocolFunctionConstants;
import cn.lyy.merchant.dto.MerchantGroupDTO;
import cn.lyy.merchant.dto.RemoteStartParamDTO;
import cn.lyy.merchant.dto.RemoteStopParamDTO;
import cn.lyy.merchant.dto.WashFaultEquipmentTDO;
import cn.lyy.merchant.dto.WashFaultStatusDTO;
import cn.lyy.merchant.dto.equipment.EquipmentExtendFunctionDTO;
import cn.lyy.merchant.dto.equipment.EquipmentListDTO;
import cn.lyy.merchant.dto.equipment.EquipmentQueryDTO;
import cn.lyy.merchant.dto.equipment.EquipmentUpdateRequestDTO;
import cn.lyy.merchant.dto.merchant.response.MerchantEquipmentUniqueCodeDTO;
import cn.lyy.merchant.dto.merchant.response.MerchantGroupEquipmentDTO;
import cn.lyy.merchant.dto.request.MerchantEquipmentRequest;
import cn.lyy.merchant.dto.response.EquipmentExtendFunctionResponseDTO;
import cn.lyy.merchant.dto.response.EquipmentFunctionDTO;
import cn.lyy.merchant.dto.response.EquipmentProfileDTO;
import cn.lyy.merchant.dto.response.EquipmentResponseDTO;
import cn.lyy.merchant.dto.response.MerchantEquipmentCountDTO;
import cn.lyy.merchant.dto.response.MerchantEquipmentDTO;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.microservice.CommodityRelatedService;
import cn.lyy.merchant.microservice.IDataServiceClient;
import cn.lyy.merchant.microservice.SystemChargeEquipmentApi;
import cn.lyy.merchant.redis.MerchantRedisClient;
import cn.lyy.merchant.redis.MerchantRedisKeyEnum;
import cn.lyy.merchant.service.IEquipmentBusinessService;
import cn.lyy.merchant.util.EquipmentUtils;
import cn.lyy.tools.equipment.LyySender;
import cn.lyy.tools.equipment.LyyUtil;
import cn.lyy.tools.util.GsonUtils;
import cn.lyy.tools.util.NettyUtil;
import cn.lyy.tools.util.chraging.CDZTypeUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.lyy.charge.dto.equipment.EquipmentStatusChangeDTO;
import com.lyy.charge.enums.equipment.EquipmentStatusMsgTypeEnum;
import com.lyy.commodity.rpc.constants.CategoryEnum;
import com.lyy.commodity.rpc.dto.request.EquipmentUnbindDTO;
import com.lyy.equipment.interfaces.feign.equipment.IotEquipmentServiceFeignClient;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.Optional.ofNullable;

/**
 * <AUTHOR> {<EMAIL>}
 * @date 2020/11/2 23:00
 **/
@Service
@Slf4j
public class EquipmentBusinessServiceImpl implements IEquipmentBusinessService {
    private static final Integer SINGLE_MAX_SIZE = 500;

    /**
     * 告警耗时
     */
    private static final Long MAX_WARN_COST = 1000L;

    @Autowired
    private MerchantEquipmentService merchantEquipmentService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private EquipmentService microEquipmentService;
    @Autowired
    private IEquipmentStatusService equipmentStatusService;

    @Autowired
    private MainboardMicroService mainboardMicroService;

    @Autowired
    private RemoteCoinsMicroService remoteCoinsMicroService;

    @Autowired
    private MerchantGroupService merchantGroupService;

    @Autowired
    private IDataServiceClient dataServiceClient;

    @Autowired
    private AdOrgClient adOrgClient;

    @Autowired
    private EquipmentExtendFunctionConfig extendFunctionConfig;

    @Autowired
    private ProtocolMicroService protocolMicroService;

    @Autowired
    private IEquipmentFuncQueueService equipmentFuncQueueService;

    @Autowired
    private IEquipmentRedisService equipmentRedisService;

    @Autowired
    private IEquipmentTypeService equipmentTypeService;

    @Resource
    private CommodityRelatedService commodityRelatedService;

    @Autowired
    private SystemChargeEquipmentApi systemChargeApi;

    @Resource
    private ExecutorService equipmentListPool;


    /**
     * 3.0洗衣机清除故障或停止请求-异步开关
     */
    @Value("${operation.async.mb.enabled:true}")
    private Boolean washerOperationAsyncEnable;


    @Autowired
    private EquipmentService equipmentService;

    @Override
    public Boolean getWasherOperationAsyncEnable() {
        log.info("[3.0洗衣机同步改异步]-0洗衣机清除故障或停止请求：{}",washerOperationAsyncEnable);
        return washerOperationAsyncEnable;
    }

    @Override
    public EquipmentDTO getByUniqueCode(String uniqueCode) {
        return ofNullable(microEquipmentService.getInfoByUniqueCode(uniqueCode))
                .filter(r -> r.getCode() == ResponseCodeEnum.SUCCESS.getCode())
                .map(BaseResponse::getData).orElse(new EquipmentDTO());
    }

    /**
     * 根据value获取设备信息
     * @param value
     * @return
     */
    @Override
    public EquipmentDTO getByValue(String value) {
        String uniqueCode = String.format("%016d", Integer.parseInt(value));
        return getByUniqueCode(uniqueCode);
    }

    /**
     * 查询设备列表
     * @param request
     * @return
     */
    @Override
    public MerchantEquipmentDTO selectEquipment(MerchantEquipmentRequest request) {
        boolean isPrimary = ofNullable(adOrgClient.checkPrimary(request.getAdUser())).map(BaseResponse::getData).orElse(false);
        request.setPrimary(isPrimary);
        // 1、查询商户场地、设备列表
        List<MerchantGroupEquipmentDTO> mGroupEquipments = getMerchantGroupEquipment(request);
        // 2、查询设备信息
        if (!CollectionUtils.isEmpty(mGroupEquipments)) {

            List<EquipmentListResponseDTO> equipments = getEquipmentList(request, mGroupEquipments,false);
            // 3、数据组装
            List<MerchantGroupDTO> newGroups = new ArrayList<>();
            List<EquipmentListResponseDTO> finalEquipments = equipments;
            AtomicInteger onLineNum = new AtomicInteger(0);
            AtomicInteger workNum = new AtomicInteger(0);
            AtomicInteger xyjNum = new AtomicInteger(0);
            AtomicInteger xyjWorkNum = new AtomicInteger(0);
            mGroupEquipments.parallelStream().forEach(g -> {
                MerchantGroupDTO group = new MerchantGroupDTO();
                BeanUtils.copyProperties(g, group);
                List<Long> gEquipment = g.getEquipment();
                AtomicInteger online = new AtomicInteger(0);
                AtomicInteger offline = new AtomicInteger(0);
                List<EquipmentListResponseDTO> newEquipments = new ArrayList<>();
                finalEquipments.stream().filter(e -> gEquipment.contains(e.getEquipmentId()))
                        .forEach(e -> {
                            newEquipments.add(e);
                            if (e.getOnline()) {
                                online.incrementAndGet();
                                onLineNum.incrementAndGet();
                            } else {
                                offline.incrementAndGet();
                            }
                            if(e.getWorkStatus() == 0){
                                //工作中设备数量
                                workNum.incrementAndGet();
                            }
                            if("XYJ".equals(e.getTypeValue())){
                                xyjNum.incrementAndGet();
                                if(e.getWorkStatus() == 0){
                                    xyjWorkNum.incrementAndGet();
                                }
                            }
                        });
                if (!CollectionUtils.isEmpty(newEquipments)) {
                    //TODO 排序修改
                    List<EquipmentListResponseDTO> sortList = newEquipments.stream()
                            .sorted(Comparator.comparing(EquipmentListResponseDTO::getUniqueCode))
                            .collect(Collectors.toList());
                    //按设备类型排序
                    Map<String,Long> map = new HashMap<>();
                    for (EquipmentListResponseDTO dto : newEquipments){
                        if(map.containsKey(dto.getTypeValue())){
                            long num = map.get(dto.getTypeValue());
                            num+=1;
                            map.put(dto.getTypeValue(),num);
                        }else {
                            map.put(dto.getTypeValue(),1L);
                        }
                    }
                    //遍历Map,按key值大小排序
                    List<Map.Entry<String,Long>> list = map.entrySet().stream()
                            .sorted(Map.Entry.<String,Long>comparingByValue()
                                    .reversed()).collect(Collectors.toList());
                    List<EquipmentListResponseDTO> lastList = new ArrayList<>();
                    for (Map.Entry<String,Long> e : list){
                        for (EquipmentListResponseDTO dto : sortList){
                            if(Objects.nonNull(e.getKey()) && e.getKey().equals(dto.getTypeValue())){
                                lastList.add(dto);
                            }
                        }
                    }
                    group.setEquipments(lastList);
                    group.setAllNum(gEquipment.size());
                    group.setOffLineNum(offline.get());
                    group.setOnLineNum(online.get());
                    newGroups.add(group);
                }
            });
            // 排序
            List<MerchantGroupDTO> collect = newGroups.stream()
                    .sorted(Comparator.comparingLong(MerchantGroupDTO::getEquipmentGroupId).reversed())
                    .collect(Collectors.toList());
            int size = equipments.size();
            MerchantEquipmentCountDTO cont = MerchantEquipmentCountDTO.builder().allNum(size)
                    .onLineNum(onLineNum.get()).offLineNum(size - onLineNum.get()).workNum(workNum.get())
                    .idleNum(xyjNum.get()-xyjWorkNum.get()).build();
            MerchantEquipmentDTO info = MerchantEquipmentDTO.builder().count(cont).groups(collect).build();
            return info;
        }
        return new MerchantEquipmentDTO();
    }

    /**
     * 查询设备列表(超过400台设备)
     *
     * @param request
     * @return
     */
    @Override
    public MerchantEquipmentDTO equipmentCount(MerchantEquipmentRequest request) {
        boolean isPrimary = ofNullable(adOrgClient.checkPrimary(request.getAdUser())).map(BaseResponse::getData).orElse(false);
        request.setPrimary(isPrimary);
        // 1、查询商户场地、设备列表
        List<MerchantGroupEquipmentDTO> mGroupEquipments = getMerchantGroupEquipment(request);
        // 2、查询设备信息
        if (!CollectionUtils.isEmpty(mGroupEquipments)) {
            List<EquipmentListResponseDTO> equipments = getEquipmentList(request, mGroupEquipments,true);
            // 3、数据组装
            List<MerchantGroupDTO> newGroups = new ArrayList<>();
            List<EquipmentListResponseDTO> finalEquipments = equipments;
            AtomicInteger onLineNum = new AtomicInteger(0);
            AtomicInteger workNum = new AtomicInteger(0);
            AtomicInteger xyjNum = new AtomicInteger(0);
            AtomicInteger xyjWorkNum = new AtomicInteger(0);
            mGroupEquipments.stream().forEach(g -> {
                MerchantGroupDTO group = new MerchantGroupDTO();
                BeanUtils.copyProperties(g, group);
                List<Long> gEquipment = g.getEquipment();
                AtomicInteger online = new AtomicInteger(0);
                AtomicInteger offline = new AtomicInteger(0);
                List<EquipmentListResponseDTO> equipmentList  = new ArrayList<>();
                finalEquipments.stream().filter(e -> gEquipment.contains(e.getEquipmentId()))
                        .forEach(e -> {
                            if (e.getOnline()) {
                                online.incrementAndGet();
                                onLineNum.incrementAndGet();
                            } else {
                                offline.incrementAndGet();
                            }
                            if(e.getWorkStatus() == 0){
                                //工作中设备数量
                                workNum.incrementAndGet();
                            }
                            if("XYJ".equals(e.getTypeValue())){
                                xyjNum.incrementAndGet();
                                if(e.getWorkStatus() == 0){
                                    xyjWorkNum.incrementAndGet();
                                }
                            }
                            equipmentList.add(e);
                        });
                // 排序，按设备的编号的大小
                List<EquipmentListResponseDTO> eqList = equipmentList.stream().sorted(Comparator.comparing(EquipmentListResponseDTO::getValue)).collect(Collectors.toList());
                group.setEquipments(eqList);
                group.setAllNum(gEquipment.size());
                group.setOffLineNum(offline.get());
                group.setOnLineNum(online.get());
                newGroups.add(group);
            });
            // 排序
            List<MerchantGroupDTO> collect = newGroups.stream()
                    .sorted(Comparator.comparingLong(MerchantGroupDTO::getEquipmentGroupId).reversed())
                    .collect(Collectors.toList());
            int size = equipments.size();
            MerchantEquipmentCountDTO cont = MerchantEquipmentCountDTO.builder().allNum(size)
                    .onLineNum(onLineNum.get()).offLineNum(size - onLineNum.get()).workNum(workNum.get())
                    .idleNum(xyjNum.get()-xyjWorkNum.get()).build();
            MerchantEquipmentDTO info = MerchantEquipmentDTO.builder().count(cont).groups(collect).build();
            return info;
        }
        return new MerchantEquipmentDTO();
    }

    /**
     * 更新设备机台号和备注
     *
     * @param equipmentUpdateDTO
     * @param adOrgId
     * @param adUserId
     * @return
     */
    @Override
    public BaseResponse updateEquipmentInfo(EquipmentUpdateDTO equipmentUpdateDTO, Long adOrgId, Long adUserId) {
        BaseResponse baseResponse = new BaseResponse();
        log.info("更新设备机台号和备注,equipmentUpdateDTO:{},adOrgId:{},adUserId:{}",equipmentUpdateDTO,adOrgId,adUserId);
        if(equipmentUpdateDTO.getGroupNum() != null){
            //校验设备和机台编号
            Boolean isUsed = microEquipmentService.doesGroupNumberInUsed(equipmentUpdateDTO.getGroupNum(), equipmentUpdateDTO.getGroupId(), equipmentUpdateDTO.getEquipmentId()).getData();
            if(isUsed == null || isUsed){
                baseResponse.setCode(ResponseCodeEnum.PARAMETER_ERROR.getCode());
                baseResponse.setMessage(String.format("机台号[%d]已经被占用",equipmentUpdateDTO.getGroupNum()));
                return baseResponse;
            }
        }
        equipmentUpdateDTO.setAdUserId(adUserId);
        Boolean flag = microEquipmentService.update(equipmentUpdateDTO).getData();
        baseResponse.setData(flag);
        return baseResponse;
    }

    /**
     * 获取设备详情信息
     *
     * @param equipmentId
     * @return
     */
    @Override
    public EquipmentResponseDTO getEquipmentDetailById(Long equipmentId) {
        EquipmentListResponseDTO dto = microEquipmentService.getDetailInfoById(equipmentId).getData();
        if(dto != null){
            EquipmentResponseDTO responseDTO = CommonConverterTools.convert(EquipmentResponseDTO.class,dto);
            //获取场地地址
            cn.lyy.merchant.dto.merchant.response.MerchantGroupDTO groupDTO = merchantGroupService.getGroupById(dto.getGroupId()).getData();
            if(groupDTO != null){
                responseDTO.setProvinceName(groupDTO.getProvinceName());
                responseDTO.setCityName(groupDTO.getCityName());
                responseDTO.setDistrict(groupDTO.getDistrict());
                responseDTO.setGroupAddress(groupDTO.getAddress());
            }

            //支付宝设备
            boolean isAlipayEquip = isAlipayEquip(dto.getVersionNo(),dto.getValue(),dto.getBusinessVersionNo());
            boolean isAlipaySdkEquip = isAlipaySDKEquip(dto.getVersionNo(),dto.getEquipmentType());
            if(isAlipayEquip || isAlipaySdkEquip){
                responseDTO.setAlipayEquip(true);
            }else {
                responseDTO.setAlipayEquip(false);
            }
            try {
                //流量卡状态
                QueryBatchIoTCardParam param = new QueryBatchIoTCardParam();
                param.setValues(Arrays.asList(dto.getValue()));
                List<IotCardStatusDTO> list = dataServiceClient.queryBatchIoTCardStatus(param);
                if(list != null && list.size() > 0){
                    responseDTO.setCardStatus(list.get(0).getStatus());
                }
            } catch (Exception e) {
                log.warn("获取流量卡状态异常:{}",e);
            }

            return  responseDTO;
        }
        return null;
    }

    private boolean isAlipayEquip(String versionno,String equipmentValue,String businessVersionNo) {
        // 后续设备管理后台删除需要删除缓存数据
        if(StringUtils.isEmpty(versionno)){
            return false;
        }
        String key = null;
        if (StringUtil.isNotEmptyORNull(businessVersionNo)){
            key = versionno+"_"+businessVersionNo;
            if (MerchantRedisClient.hexists(MerchantRedisKeyEnum.FIRMWARE_ALIPAY_REVERSE_EQUIPMENT, key)){
                return Integer.parseInt(ofNullable(MerchantRedisClient.hget(MerchantRedisKeyEnum.FIRMWARE_ALIPAY_REVERSE_EQUIPMENT, key)).orElse("0")) > 0;
            }else {
                Integer value = microEquipmentService.isAliPayEquipment(equipmentValue).getData();
                if(value == null){
                    return false;
                }
                MerchantRedisClient.hset(MerchantRedisKeyEnum.FIRMWARE_ALIPAY_REVERSE_EQUIPMENT, key, String.valueOf(value));
                return value > 0;
            }
        }else if (MerchantRedisClient.hexists(MerchantRedisKeyEnum.FIRMWARE_ALIPAY_REVERSE_EQUIPMENT, versionno)) {
            String count = MerchantRedisClient.hget(MerchantRedisKeyEnum.FIRMWARE_ALIPAY_REVERSE_EQUIPMENT, versionno);
            return Integer.parseInt(ofNullable(count).orElse("0")) > 0;
        }else {
            Integer value = microEquipmentService.isAliPayEquipment(equipmentValue).getData();
            if(value == null){
                return false;
            }
            MerchantRedisClient.hset(MerchantRedisKeyEnum.FIRMWARE_ALIPAY_REVERSE_EQUIPMENT, versionno, String.valueOf(value));
            return value > 0;
        }
    }

    private boolean isAlipaySDKEquip(String versionno, Long equipmentTypeId){
        if(StringUtils.isEmpty(versionno)){
            return false;
        }
        String field = versionno + "_" + equipmentTypeId;
        if (MerchantRedisClient.hexists(MerchantRedisKeyEnum.APP_ALIPAY_EQUIPMENT, field)) {
            String count = MerchantRedisClient.hget(MerchantRedisKeyEnum.APP_ALIPAY_EQUIPMENT, field);
            return Integer.parseInt(count) > 0;
        }
        Integer value =  microEquipmentService.aliPaySdkEquipment(versionno,equipmentTypeId).getData();
        if(value == null){
            return false;
        }
        MerchantRedisClient.hset(MerchantRedisKeyEnum.APP_ALIPAY_EQUIPMENT, field, String.valueOf(value));
        return value > 0;
    }


    /**
     * 获取设备仓位信息
     * @param equipmentId
     */
    @Override
    public List<PositionDTO> getPositionInfoByEquipmentId(Long equipmentId) {
        return microEquipmentService.getPositionInfoByEquipmentId(equipmentId).getData();
    }

    /**
     * 刷新设备信号
     * @param uniqueCode
     * @return
     */
    @Override
    public Integer getEquipmentSignal(String uniqueCode) {
        Integer signal = microEquipmentService.equipmentSignalByUniqueCode(uniqueCode).getData();
        List<EquipmentAssociationDTO> list = microEquipmentService.getAssociationEquipmentInfo(uniqueCode).getData();
        if(list != null && list.size() > 0 ){
            List<EquipmentMacDTO> equipmentMacDTOList = list.stream().map(item -> new EquipmentMacDTO(item.getNumber(), item.getMacId())).collect(Collectors.toList());
            ChargingKit.netList(uniqueCode, equipmentMacDTOList);
        }
        return signal;
    }

    /**
     * 更新故障状态
     *
     * @param faultStatusDTO
     * @param token
     * @return
     */
    @Override
    public Integer updateFaultStatus(WashFaultStatusDTO faultStatusDTO, String token) {
        log.debug("洗衣机清除故障:{}",faultStatusDTO.toString());
        Long groupId = faultStatusDTO.getGroupId();
        List <WashFaultEquipmentTDO> equipmentList = faultStatusDTO.getEquipmentList();
        List<OperationParam> paramList =new ArrayList <>();
        for (WashFaultEquipmentTDO equipmentTDO : equipmentList){
            OperationParam param = new OperationParam();
            param.setUniqueCode(equipmentTDO.getUniqueCode());
            param.setIdentity(CommandFunctionConstants.BC_XYJ_GAIN_STATUS.getIdentity());
            param.setCallbackMqConfig(MqSourceConstants.BUSINESS);
            param.setCallbackExchange(MessageConstants.XYJ_GAIN_STATUS.getExchange());
            param.setCallbackRouteKey(MessageConstants.XYJ_GAIN_STATUS.getRouteKey());
            param.setToUniqueId(token);
            param.setToClientType(ClientTypeEnum.MERCHANT_H5.getType());
            Map<String, String> attach = Maps.newHashMap();
            attach.put("lyyEquipmentId", String.valueOf(equipmentTDO.getEquipmentId()));
            attach.put("equipmentValue",equipmentTDO.getEquipmentValue());
            attach.put("businessType", EquipBuinessTypeEnum.WASHER_B_FAULT_STATUS.getCode());
            attach.put("deviceListIndex",String.valueOf(equipmentTDO.getDeviceListIndex()));
            attach.put("groupId",String.valueOf(groupId));
            param.setParam(GsonUtils.toJson(attach));
            paramList.add(param);
        }
        microEquipmentService.batchOperationParam(paramList);
        return 2;
    }

    /**
     * 设备解绑
     * @param dto
     */
    @Override
    public boolean unbind(EquipmentUpdateRequestDTO dto) {
        //获取设备信息
        EquipmentDTO equipmentDTO = microEquipmentService.getInfoByUniqueCode(dto.getUniqueCode()).getData();
        boolean result = false;
        if (equipmentDTO != null) {
            dto.setGroupId(equipmentDTO.getEquipmentGroupId());
            dto.setValue(equipmentDTO.getValue());
            result = microEquipmentService.unbind(null,equipmentDTO.getDistributorId(),equipmentDTO.getEquipmentId()).getData();

            if(result){
                // 设备为洗衣机时，需同时解绑加液机的商品信息
                BaseResponse<EquipmentTypeDTO>  equipmentTypeResponse =  equipmentTypeService.getByKey(equipmentDTO.getEquipmentTypeId());
                EquipmentTypeDTO equipmentTypeDTO = Optional.ofNullable(equipmentTypeResponse).filter(r -> r.getCode() == ResponseCodeEnum.SUCCESS.getCode()).map(r->r.getData()).orElse(null);
                if( equipmentDTO != null && equipmentTypeDTO.getValue().equals("XYJ")){

                    BaseResponse<EquipmentTypeDTO>  jyjResponse =  equipmentTypeService.getByValue("JYJ");
                    EquipmentTypeDTO jyjEquipmentType = Optional.ofNullable(jyjResponse).filter(r -> r.getCode() == ResponseCodeEnum.SUCCESS.getCode()).map(r->r.getData()).orElse(null);
                    EquipmentUnbindDTO unbindParam = new EquipmentUnbindDTO();
                    unbindParam.setEquipmentId(equipmentDTO.getEquipmentId());
                    unbindParam.setCategoryCode(CategoryEnum.DEVICE_SERVICE.getCode());
                    unbindParam.setEquipmentTypeId(jyjEquipmentType.getEquipmentTypeId());
                    unbindParam.setStoreId(equipmentDTO.getEquipmentGroupId());
                    unbindParam.setDistributorId(dto.getAdOrgId());
                    BaseResponse unbindResponse =  commodityRelatedService.unbindDistributorEquipment(unbindParam);
                    if(unbindResponse.getCode() != ResponseCodeEnum.SUCCESS.getCode()){
                        log.error("解绑加液机套餐失败,msg " + unbindResponse.getMessage());
                    }
                }

                merchantEquipmentService.unbind(dto);
                //TODO 删除设备队列信息
                equipmentFuncQueueService.deleteByEquipmentValue(equipmentDTO.getValue());
            }

            //设备解绑推送消息
            EquipmentStatusChangeDTO equipmentStatusChangeDTO = new EquipmentStatusChangeDTO();
            equipmentStatusChangeDTO.setMsgType(EquipmentStatusMsgTypeEnum.EQUIPMENT_UNBIND.getMsgType());
            equipmentStatusChangeDTO.setEquipmentId(dto.getEquipmentId().longValue());
            equipmentStatusChangeDTO.setEquipmentTypeId(Objects.isNull(equipmentDTO.getEquipmentTypeId()) ? 0L : equipmentDTO.getEquipmentTypeId().longValue());
            log.info("设备解绑消息推送内容-->{}", equipmentStatusChangeDTO);
            systemChargeApi.sendEquipmentChangeMsg(equipmentStatusChangeDTO);


        }
        return result;
    }

    /**
     * 根据设备类型id获取设备类型的扩展功能列表
     * @param equipmentTypeId
     * @return
     */
    @Override
    public List<EquipmentExtendFunctionResponseDTO> extendFunctionList(Long equipmentTypeId) {
        List<EquipmentExtendFunctionDTO> list = merchantEquipmentService.getExtendFunctionByEquipmentType(equipmentTypeId).getData();
        List<EquipmentExtendFunctionResponseDTO> vos = Lists.newArrayList();
        list.stream().collect(Collectors.groupingBy(EquipmentExtendFunctionDTO::getType)).forEach((k, v) -> {
            EquipmentExtendFunctionResponseDTO vo = new EquipmentExtendFunctionResponseDTO();
            vo.setName(v.get(0).getName());
            vo.setType(k);
            vo.setIcon(v.get(0).getIcon());
            vo.setTypeName(extendFunctionConfig.getMaps().get(k));
            vo.setRelateAuthCode(v.get(0).getRelateAuthCode());
            vos.add(vo);
        });

        return vos;
    }

    @Override
    public Boolean onlineStatus(String equipmentValue) {
        EquipmentInfoDTO equipmentInfoDTO = microEquipmentService.getEquipmentInfoByValue(equipmentValue).getData();
        if (equipmentInfoDTO == null) {
            throw new BusinessException(BusinessExceptionEnums.DEVICE_NOT_EXISTS);
        }
        Map<String, Boolean> result = microEquipmentService.equipmentStatus(Collections.singletonList(equipmentInfoDTO.getUniqueCode())).getData();
        if (result == null) {
            return false;
        }
        return result.get(equipmentInfoDTO.getUniqueCode());
    }

    @Override
    public EquipmentProfileDTO equipmentProfile(String equipmentValue, Long currentAdOrgId) {
        EquipmentProfileDTO profile = new EquipmentProfileDTO();
        profile.setEquipmentValue(equipmentValue);
        EquipmentInfoDTO equipmentInfoDTO = getEquipInfoAfterCheck(equipmentValue,currentAdOrgId);
        profile.setEquipmentTypeValue(equipmentInfoDTO.getEquipmentTypeValue());
        profile.setEquipmentTypeName(equipmentInfoDTO.getEquipmentTypeName());
        profile.setRemarks(equipmentInfoDTO.getRemarks());
        EquipmentListResponseDTO detail = microEquipmentService.getDetailInfoById(equipmentInfoDTO.getEquipmentId().longValue()).getData();
        cn.lyy.merchant.dto.merchant.response.MerchantGroupDTO groupInfo =null;
        if (equipmentInfoDTO.getEquipmentGroupId() != null) {
            groupInfo = merchantGroupService.getGroup(equipmentInfoDTO.getEquipmentGroupId()).getData();
        }
        profile.setGroupName(ofNullable(groupInfo).map(cn.lyy.merchant.dto.merchant.response.MerchantGroupDTO::getName).orElse(""));
        profile.setEquipmentTypeIcon(ofNullable(detail).map(EquipmentListResponseDTO::getIconUrl).orElse(""));
        return profile;
    }

    @Override
    public List<EquipmentListDTO> queryByTypeAndGroup(EquipmentQueryDTO query, Boolean checkOnline) {

        List<EquipmentListDTO> result = ofNullable(merchantEquipmentService.selectByEquipmentTypeAndGroup(query))
                .map(BaseResponse::getData).orElse(new ArrayList<>());
        if (!result.isEmpty() && checkOnline) {
            // 查询并设置在线状态
            ofNullable(microEquipmentService.equipmentStatus(result.stream().map(EquipmentListDTO::getUniqueCode).collect(Collectors.toList())))
                    .map(BaseResponse::getData).ifPresent(map -> {
                result.forEach(r -> {
                    r.setIsOnline(map.get(r.getUniqueCode()));
                });
            });
        }
        return result;
    }

    @Override
    public void remoteStart(RemoteStartParamDTO param) {
        String equipmentValue = param.getEquipmentValue();
        EquipmentInfoDTO equipment = getEquipInfoAfterCheck(equipmentValue,null);
        OperationParam operationParam = new OperationParam();
        String equipmentTypeValue = equipment.getEquipmentTypeValue();
        //时间
        Integer time = param.getTime();
        //电量
        BigDecimal elec = param.getElec();
        if (EquipmentTypeEnums.CHARGING_PILE.getValue().equals(equipmentTypeValue)
                && CDZTypeUtil.EquipemntCDZtype.CK.equals(CDZTypeUtil.getCDZtype(equipment.getLoginFlag()))) {
            // 串口充电桩
            if (null == param.getChannel()) {
                throw new BusinessException(BusinessExceptionEnums.CHANNEL_ARGS_MISSING);
            }
            if (null == time && elec==null) {
                throw new BusinessException(BusinessExceptionEnums.PARAM_ERROR);
            }
            // 充电桩串口上分逻辑
            String identity = "BC_CDZ_REMOTE_STARTUP";
            operationParam.setUniqueCode(equipment.getUniqueCode());
            Map<String, Object> data = new HashMap<>(6);
            data.put("num", param.getChannel().toString());
            String costWay = equipment.getGroupServiceCostWay();
            if(StringUtils.isEmpty(costWay)){
                // 默认时间启动
                costWay = "TIME";
            }

            boolean isElec  = false;
            if (StringUtils.isNotEmpty(costWay)&&ProtocolFunctionConstants.FEE_MODE_ELEC.equals(costWay)) {
                isElec = true;
                Integer elecPulseValue = Integer.valueOf(param.getElec().multiply(new BigDecimal(100)).setScale(0, BigDecimal.ROUND_HALF_UP).toString());
                data.put("pulse", elecPulseValue);
                data.put(LyySender.TIME_OR_ELEC, time == null ? null : time * 10);
                identity = "BC_CDZ_ELEC_REMOTE_STARTUP";
            } else {
                data.put("pulse", time);
                data.put(LyySender.TIME_OR_ELEC, elec==null?null:
                        Integer.valueOf(param.getElec().multiply(new BigDecimal(100)).setScale(0, BigDecimal.ROUND_HALF_UP).toString()));
            }
            data.put("coins", param.getCoins());
            data.put("time", time);
            String price = param.getPrice() == null ? null :
                    param.getPrice().multiply(new BigDecimal(100)).setScale(0, BigDecimal.ROUND_HALF_UP).toString();
            data.put(LyySender.GROUP_SERVICE_PRICE_ORIGINAL, price);
            data.put(LyySender.GROUP_SERVICE_PRICE, price);
            data.put("payUniqueCommand", NettyUtil.generateUniqueCommand());
            try {
                operationParam.setParam(objectMapper.writeValueAsString(data));
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
            operationParam.setIdentity(identity);
            microEquipmentService.operation(operationParam);
            String operate = time + "分钟";
            if(isElec){
                BigDecimal electric = param.getElec().setScale(2,BigDecimal.ROUND_HALF_UP);
                operate = electric +"度";
            }
            saveRemoteCoins(equipment, param.getCoins(), param.getChannel(), param.getAdUserId(), "", operate, null, null, null, null);
            return;
        }else if(EquipmentTypeEnums.CHARGING_PILE_FOR_CAR.getValue().equals(equipmentTypeValue)){
            //汽车充电桩远程启动(2.0代码 2度电，下发pulse：200，下发金额：200000)
            Map<String, Object> data = new HashMap<>(6);
            //端口
            data.put("num", param.getChannel().toString());
            //电量
            Integer elecPulseValue = Integer.valueOf(param.getElec().multiply(new BigDecimal(100)).setScale(0, BigDecimal.ROUND_HALF_UP).toString());
            data.put("pulse", elecPulseValue);
            //下发金额
            String price = param.getElec().multiply(new BigDecimal(100000)).setScale(0, BigDecimal.ROUND_HALF_UP).toString();
            data.put(LyySender.GROUP_SERVICE_PRICE_ORIGINAL, price);
            data.put(LyySender.GROUP_SERVICE_PRICE, price);
            data.put("payUniqueCommand", NettyUtil.generateUniqueCommand());
            //下发启动
            CarChargingKit.remoteStart(equipment.getUniqueCode(),data);
            BigDecimal electric = param.getElec().setScale(2,BigDecimal.ROUND_HALF_UP);
            String operate = electric +"度";
            saveRemoteCoins(equipment, param.getElec().intValue(), param.getChannel(), param.getAdUserId(), "", operate, null, null, null, null);
            return;
        } else if (EquipmentTypeEnums.GASHAPON.getValue().equals(equipmentTypeValue)) {
            if (null == param.getChannel()) {
                throw new BusinessException(BusinessExceptionEnums.CHANNEL_ARGS_MISSING);
            }
            if (null == param.getMainBoard()) {
                throw new BusinessException(BusinessExceptionEnums.MAIN_BOARD_ARGS_MISSING);
            }
            String mainboardUniquCode = ofNullable(mainboardMicroService.queryByEquipmentAndValue(equipment.getEquipmentId().longValue(), param.getMainBoard()))
                    .map(BaseResponse::getData)
                    .map(MainboardDTO::getUniqueCode)
                    .orElse(null);
            //新的方法进行远程启动
            Map<String, Object> remoteStartParam = new HashMap<>();
            remoteStartParam.put("payUniqueCommand", NettyUtil.generateUniqueCommand());
            remoteStartParam.put("equipmentType", EquipmentTypeEnums.GASHAPON.getValue());
            remoteStartParam.put("board_addr", param.getChannel().toString());
            remoteStartParam.put("pulse", param.getCoins());
            remoteStartParam.put("coins", param.getCoins());
            remoteStartParam.put("board_unique_code", mainboardUniquCode);
            GashaponKit.remoteStart(equipment.getUniqueCode(), remoteStartParam);
            saveRemoteCoins(equipment, param.getCoins(), param.getChannel(), param.getAdUserId(), mainboardUniquCode, "", null, null, null, null);
            return;
        } else if (EquipmentTypeEnums.MASSAGE_CHAIR.getValue().equals(equipmentTypeValue) ||
                EquipmentTypeEnums.MASSAGE_MAT.getValue().equals(equipmentTypeValue) ||
                EquipmentTypeEnums.FOOT_MASSAGE_MACHINE.getValue().equals(equipmentTypeValue)) {

            Integer pulse = param.getCoins();
            if(null != equipment.getPulseDurationRatio() && equipment.getPulseDurationRatio() != 0){
                Integer pulseDurationRatio = equipment.getPulseDurationRatio();
                pulse = pulse % pulseDurationRatio == 0 ? (pulse / pulseDurationRatio) : (pulse / pulseDurationRatio) + 1;
            }

            String mainboardValue = "";
            //按摩垫  判断是否1+n
            if (EquipmentTypeEnums.MASSAGE_MAT.getValue().equals(equipmentTypeValue)) {
                if (EquipmentBusinessType.MASSAGE_MAT_BUSINESS_TYPE.getValue().equals(equipment.getBusinessType())) {
                    if (StringUtils.isEmpty(param.getMainBoard())) {
                        throw new BusinessException(BusinessExceptionEnums.MAIN_BOARD_ARGS_MISSING);
                    }
                    mainboardValue = param.getMainBoard();
                }
            }

            // 按摩类远程启动
            String functionType = "01";
            Map<String, Object> remoteStartParam = new HashMap<>();
            remoteStartParam.put("payUniqueCommand", NettyUtil.generateUniqueCommand());
            remoteStartParam.put("equipmentType", equipmentTypeValue);
            remoteStartParam.put("coins", param.getCoins());
            remoteStartParam.put("extraServiceTime", param.getCoins());
            remoteStartParam.put("pulse", pulse);
            remoteStartParam.put("mainboardValue",mainboardValue);
            remoteStartParam.put("functionType",functionType);
            remoteStartParam.put("payAmount", 0);
            remoteStartParam.put("interval","0");
            remoteStartParam.put("unique_code", equipment.getUniqueCode());
            MassageKit.remoteStart(equipment.getUniqueCode(), equipmentTypeValue, remoteStartParam);
            saveRemoteCoins(equipment, param.getCoins(), null, param.getAdUserId(), mainboardValue,
                    "", param.getCoins(), functionType, null, null);
            return;
        } else if("CGJ".equals(equipmentTypeValue) && EquipmentUtils.isOpenEquipment(equipment.getLoginFlag())){
            //反向唱歌机特殊处理
            String identity = "BC_" + equipmentTypeValue + "_STARTUP_CHECK";
            OperationParam operation = new OperationParam();
            operation.setUniqueCode(equipment.getUniqueCode());
            operation.setIdentity(identity);
            HashMap<String, String> attach = new HashMap<>();
            attach.put("payUniqueCommand", NettyUtil.generateUniqueCommand());
            attach.put("coins", param.getCoins().toString());
            attach.put("pulse", param.getCoins().toString());
            attach.put("interval", "0");
            attach.put("workflow", "Y");
            attach.put("isRemote","Y");
            try {
                operation.setParam(objectMapper.writeValueAsString(attach));
            } catch (JsonProcessingException e) {
                log.error(e.getMessage(), e);
            }
            microEquipmentService.operation(operation);
            saveRemoteCoins(equipment, param.getCoins(), null, param.getAdUserId(), "", "", null, null, null, null);
            return;
        } else if(EquipmentTypeEnums.WASHER.getValue().equals(equipmentTypeValue)
                && EquipmentUtils.isOpenEquipment(equipment.getLoginFlag())){
            //洗衣机远程启动
            operationParam.setUniqueCode(equipment.getUniqueCode());
            operationParam.setIdentity("BC_XYJ_REMOTE_STARTUP");
            Map<String, Object> data = new HashMap<>();
            data.put("pulse", param.getCoins());
            data.put("payUniqueCommand", NettyUtil.generateUniqueCommand());
            data.put("group_service_time",param.getTime());
            data.put("group_service_name",param.getGroupServiceName());
            String price = param.getPrice() == null ? null :
                    param.getPrice().multiply(new BigDecimal(100)).toString();
            data.put(LyySender.GROUP_SERVICE_PRICE_ORIGINAL, price);
            data.put(LyySender.GROUP_SERVICE_PRICE, price);
            operationParam.setParam(new Gson().toJson(data));
            microEquipmentService.operation(operationParam);
            saveRemoteCoins(equipment, param.getCoins(), null, param.getAdUserId(), "", param.getGroupServiceName(), null, null, null, null);
            return;
        } else if( EquipmentUtils.isOpenEquipment(equipment.getLoginFlag()) &&
                EquipmentUtils.checkPulse(equipmentTypeValue)){
            //脉冲设备启动
            operationParam.setUniqueCode(equipment.getUniqueCode());
            operationParam.setIdentity("BC_"+equipmentTypeValue+"_REMOTE_STARTUP");
            Map<String, Object> data = new HashMap<>();
            data.put("pulse", param.getCoins());
            data.put("payUniqueCommand", NettyUtil.generateUniqueCommand());
            data.put("group_service_name",param.getGroupServiceName());
            BigDecimal price = param.getPrice() == null ? BigDecimal.ZERO :
                    param.getPrice().multiply(new BigDecimal(100));
            data.put(LyySender.GROUP_SERVICE_PRICE_ORIGINAL, price.intValue());
            data.put(LyySender.GROUP_SERVICE_PRICE, price.intValue());
            operationParam.setParam(new Gson().toJson(data));
            microEquipmentService.operation(operationParam);
            saveRemoteCoins(equipment, param.getCoins(), null, param.getAdUserId(), "", param.getGroupServiceName(), null, null, null, null);
            return;
        }else {
            boolean isTrafficWWJ = ofNullable(microEquipmentService.checkLoginFlag(equipmentValue, EquipmentLoginFlagTypeEnums.TRAFFIC_DOLL_MACHINE.getType()))
                    .map(BaseResponse::getData)
                    .orElse(false);
            if (isTrafficWWJ) {
                log.info(" ----> 娃娃机智慧模式远程上分 ");
                Map<String, Object> remoteStartParam = new HashMap<>();
                remoteStartParam.put("payUniqueCommand", NettyUtil.generateUniqueCommand());
                remoteStartParam.put("equipmentType", cn.lyy.equipment.constant.EquipmentTypeConstant.WWJ.getCode());
                remoteStartParam.put("coins", param.getCoins());
                remoteStartParam.put("pulse", param.getCoins());
                operationParam.setUniqueCode(equipment.getUniqueCode());
                operationParam.setIdentity("BC_WWJ_REMOTE_STARTUP");
                try {
                    operationParam.setParam(objectMapper.writeValueAsString(remoteStartParam));
                } catch (JsonProcessingException e) {
                    log.error(e.getMessage(), e);
                }
            } else {
                Map<String, Object> remoteStartParam = new HashMap<>();
                remoteStartParam.put("payUniqueCommand", NettyUtil.generateUniqueCommand());
                remoteStartParam.put("equipmentType", equipmentTypeValue);
                remoteStartParam.put("coins", param.getCoins());
                remoteStartParam.put("pulse", time);
                remoteStartParam.put("payAmount", 0);
                remoteStartParam.put("interval","0");
                remoteStartParam.put("isRemote","Y");
                remoteStartParam.put("unique_code", equipment.getUniqueCode());
                operationParam.setUniqueCode(equipment.getUniqueCode());
                operationParam.setIdentity("BC_" + equipmentTypeValue + "_REMOTE_STARTUP");
                try {
                    operationParam.setParam(objectMapper.writeValueAsString(remoteStartParam));
                } catch (JsonProcessingException e) {
                    log.error(e.getMessage(), e);
                }
            }
            saveRemoteCoins(equipment, param.getCoins(), null, param.getAdUserId(), "", "", null, null, null, null);

        }
        microEquipmentService.operation(operationParam);
    }

    private EquipmentInfoDTO getEquipInfoAfterCheck(String equipmentValue, Long currentAdOrgId) {
        EquipmentInfoDTO equipment = ofNullable(microEquipmentService.getEquipmentInfoByValue(equipmentValue))
                .map(BaseResponse::getData).orElse(null);
        if (equipment == null) {
            throw new BusinessException(BusinessExceptionEnums.DEVICE_NOT_EXISTS);
        }

        if (currentAdOrgId!=null && (equipment.getDistributorId()==null||  !currentAdOrgId.equals(Long.valueOf(equipment.getDistributorId())))) {
            throw new BusinessException(BusinessExceptionEnums.MERCHANT_ALREADY_REGISTER_EQUIPMENT);
        }
        BaseResponse<Map<String, Boolean>> response = microEquipmentService.equipmentStatus(Collections.singletonList(equipment.getUniqueCode()));
        if (response.getCode() == ResponseCodeEnum.FAIL.getCode()) {
            throw new BusinessException(BusinessExceptionEnums.OPERATE_ERROR);
        }
        if (response.getCode() == ResponseCodeEnum.SUCCESS.getCode()) {
            Map<String, Boolean> data = response.getData();
            Boolean aBoolean = data.get(equipment.getUniqueCode());
            if (aBoolean ==null || !aBoolean) {
                throw new BusinessException(BusinessExceptionEnums.EQUIPMENT_OFFLINE);
            }
        }
        return equipment;
    }


    @Override
    public Boolean remoteStop(RemoteStopParamDTO param, String webSocketToken) {
        EquipmentInfoDTO equipment = getEquipInfoAfterCheck(param.getEquipmentValue(),null);
        String equipmentTypeValue = equipment.getEquipmentTypeValue();
        if (EquipmentTypeEnums.CHARGING_PILE.getValue().equals(equipmentTypeValue)
                && CDZTypeUtil.EquipemntCDZtype.CK.equals(CDZTypeUtil.getCDZtype(equipment.getLoginFlag()))) {
            // 串口充电桩
            Integer channel = param.getChannel();
            if (null == channel) {
                throw new BusinessException(BusinessExceptionEnums.CHANNEL_ARGS_MISSING);
            }
            log.info("设备：{} 异步远程停止",param.getEquipmentValue());
           return ChargingKit.asyncStop(equipment.getUniqueCode(), Integer.valueOf(channel),equipment.getGroupServiceCostWay(),null,null,null );
//           return ChargingKit.stop(equipment.getUniqueCode(), Integer.valueOf(channel),equipment.getGroupServiceCostWay() );
        }else if(EquipmentTypeEnums.WASHER.getValue().equals(equipmentTypeValue)) {
            //洗衣机远程停止
            return washStop(equipment.getUniqueCode(),webSocketToken);
        } else if(EquipmentTypeEnums.CHARGING_PILE_FOR_CAR.getValue().equals(equipmentTypeValue)){
            //汽车充电桩
            Integer channel = param.getChannel();
            if (null == channel) {
                throw new BusinessException(BusinessExceptionEnums.CHANNEL_ARGS_MISSING);
            }
            return asyncStop(equipment.getUniqueCode(), channel);
        }
        return false;
    }

    private boolean washStop(String uniqueCode, String webSocketToken) {
        if (washerOperationAsyncEnable) {
            log.debug("[洗衣机远程停止]-同步改异步");
            OperationParam param = new OperationParam();
            param.setUniqueCode(uniqueCode);
            param.setIdentity(CommandFunctionConstants.BC_XYJ_TERMINATION.getIdentity());
            param.setCallbackMqConfig(MqSourceConstants.BUSINESS);
            param.setCallbackExchange(MessageConstants.XYJ_TERMINATION_MERCHANT_BFF.getExchange());
            param.setCallbackRouteKey(MessageConstants.XYJ_TERMINATION_MERCHANT_BFF.getRouteKey());
            param.setToUniqueId(webSocketToken);
            param.setToClientType(cn.lyy.websocket.constant.ClientTypeEnum.SERVER.getType());
            Map<String, String> attach = Maps.newHashMap();
            attach.put("businessType", EquipBuinessTypeEnum.WASHER_MB_TERMINATION_STOP.getCode());
            param.setParam(GsonUtils.toJson(attach));
            equipmentService.operation(param);
            return Boolean.TRUE;
        }
        return WashKit.stop(uniqueCode);
    }

    @Override
    public List<EquipmentStatusDTO> listErrorWasher(EquipmentQueryDTO query) {
        Set<String> errorEquipIds = ofNullable(equipmentRedisService.smember(WasherConstants.WASHER_MALFUNCTION_LIST + query.getLyyDistributorId()))
                .filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode())
                .map(BaseResponse::getData).orElse(new HashSet<>());
        if (errorEquipIds.isEmpty()) {
            return new ArrayList<>();
        }
        List<Long> equipmentIds = new ArrayList<>();
        errorEquipIds.stream().forEach(e-> equipmentIds.add(Long.valueOf(e)));

        BaseResponse<List<EquipmentStatusDTO>> baseResponse = equipmentStatusService.listEquipmentStatus(equipmentIds);
        log.debug("查询故障洗衣机列表,商家ID:{},equipmentIds:{} ,baseResponse: {} ",query.getLyyDistributorId(),equipmentIds,baseResponse);
        return  ofNullable(baseResponse)
                .filter(r->ResponseCodeEnum.SUCCESS.getCode() == r.getCode())
                .map(BaseResponse::getData)
                .orElse(new ArrayList<>());


    }

    /**
     * 查询指定设备类型在场地的列表
     * @param request
     * @return
     */
    @Override
    public MerchantEquipmentDTO selectGroupEquipmentList(MerchantEquipmentRequest request) {
        //查询是否是主账号
        boolean isPrimary = ofNullable(adOrgClient.checkPrimary(request.getAdUser())).map(BaseResponse::getData).orElse(false);
        request.setPrimary(isPrimary);
        //根据设备类型名称获取设备类型id
        if(!CollectionUtils.isEmpty(request.getEquipmentTypeValues())){
            List<EquipmentTypeDTO> typeDTOList = equipmentTypeService.getByTypeValues(request.getEquipmentTypeValues()).getData();
            if (!CollectionUtils.isEmpty(typeDTOList)) {
                List<Long> typeIds = typeDTOList.stream().map(EquipmentTypeDTO::getEquipmentTypeId).collect(Collectors.toList());
                if(request.getEquipmentType() != null){
                    request.getEquipmentType().addAll(typeIds);
                }else {
                    request.setEquipmentType(typeIds);
                }
            }
        }
        // 1、查询商户场地、设备列表
        List<MerchantGroupEquipmentDTO> mGroupEquipments = getMerchantGroupEquipment(request);
        if (!CollectionUtils.isEmpty(mGroupEquipments)) {
            AtomicInteger size = new AtomicInteger();
            //数据组装
            List<MerchantGroupDTO> newGroups = new ArrayList<>();
            mGroupEquipments.stream().forEach(g -> {
                MerchantGroupDTO group = new MerchantGroupDTO();
                BeanUtils.copyProperties(g, group);
                group.setAllNum(g.getEquipment().size());
                newGroups.add(group);
                size.addAndGet(g.getEquipment().size());
            });
            // 排序
            List<MerchantGroupDTO> collect = newGroups.stream()
                    .sorted(Comparator.comparingLong(MerchantGroupDTO::getEquipmentGroupId).reversed())
                    .collect(Collectors.toList());
            MerchantEquipmentCountDTO cont = MerchantEquipmentCountDTO.builder().allNum(size.get()).build();
            MerchantEquipmentDTO info = MerchantEquipmentDTO.builder().count(cont).groups(collect).build();
            return info;
        }
        return new MerchantEquipmentDTO();
    }

    /**
     * 查询场地设备信息，只显示场地及场地下面的信息，内容与list接口类似，但是没有其他详细的设备信息，
     * 主要用于某些选择场地的请求
     * @param request
     * @return
     */
    @Override
    public MerchantEquipmentDTO findGroupEquipment(MerchantEquipmentRequest request) {
        boolean isPrimary = ofNullable(adOrgClient.checkPrimary(request.getAdUser())).map(BaseResponse::getData).orElse(false);
        request.setPrimary(isPrimary);
        // 1、查询商户场地、设备列表
        List<MerchantGroupEquipmentDTO> mGroupEquipments = getMerchantGroupEquipment(request);
        log.debug(" 获取到 {} 场地",mGroupEquipments.size());
        if (!CollectionUtils.isEmpty(mGroupEquipments)) {
            List<Long> equipmentIds = getEquipment(mGroupEquipments);
            List<EquipmentListResponseDTO> allEquipmentList = new ArrayList<>(equipmentIds.size());
            if (!CollectionUtils.isEmpty(equipmentIds)) {
                // 数据切割查询
                int num = countStep(equipmentIds.size());
                List<List<Long>> collect =
                        Stream.iterate(0, n -> n + 1).limit(num).parallel()
                                .map(a -> equipmentIds.stream().skip(a * SINGLE_MAX_SIZE).limit(SINGLE_MAX_SIZE).parallel().collect(Collectors.toList()))
                                .filter(b -> !b.isEmpty()).collect(Collectors.toList());
                //获取设备数据,并行获取
                collect.parallelStream().forEach(ids -> {
                    ofNullable(microEquipmentService.selectInfo(ids))
                            .filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode())
                            .map(BaseResponse::getData)
                            .ifPresent(response->allEquipmentList.addAll(response));
                });

            }
            log.debug(" 共有{} 个设备id，获取{} 个设备信息 ",equipmentIds.size(),allEquipmentList.size());
            List<MerchantGroupDTO> collect = mGroupEquipments.stream()
                    .map(g -> {
                        MerchantGroupDTO group = new MerchantGroupDTO();
                        BeanUtils.copyProperties(g, group);
                        List<Long> gEquipment = g.getEquipment();
                        if(CollectionUtils.isEmpty(gEquipment)) {
                            group.setEquipments(Collections.EMPTY_LIST);
                            group.setAllNum(0);
                        } else {
                            List<EquipmentListResponseDTO>  equipments = allEquipmentList.stream()
                                    .filter(e->gEquipment.contains(e.getEquipmentId()))
                                    //排序,先排品类，再排设备号
                                    .sorted(Comparator.comparing(eq-> eq.getTypeValue()+"-"+eq.getUniqueCode()))
                                    .collect(Collectors.toList());
                            group.setEquipments(equipments);
                            group.setAllNum(equipments.size());
                        }
                        return group;
                    })
                    //排序
                    .sorted(Comparator.comparingLong(MerchantGroupDTO::getEquipmentGroupId).reversed())
                    .collect(Collectors.toList());
            MerchantEquipmentCountDTO cont = MerchantEquipmentCountDTO.builder().allNum(allEquipmentList.size()).build();
            MerchantEquipmentDTO info = MerchantEquipmentDTO.builder().count(cont).groups(collect).build();
            return info;
        }
        return new MerchantEquipmentDTO();
    }

    /**
     * 查询在线离线数
     *
     * @param merchantId 商户id
     * @param userId     用户id
     * @return 返回数据
     */
    @Override
    public Map<String, Integer> onlineAndOfflineNumber(Long merchantId, Long userId) {
        if(log.isDebugEnabled()){
            log.debug("getEquipmentNum  获取用户设备总数信息,adUserId:{},orgId:{}", userId, merchantId);
        }
        MerchantEquipmentUniqueCodeDTO data = merchantEquipmentService.getMerchantEquipmentUniqueCodeList(userId, merchantId).getData();
        Map<String, Integer> map = new HashMap<>(6);
        if(data==null){
            map = new HashMap<>(2);
            map.put("onlineCount", 0);
            map.put("offlineCount", 0);
        }else{
            // 分组，100个查询一次
            List<String> allUniqueCode = data.getUniqueCodeList();
            List<List<String>> listAfterGroup = new ArrayList<>(allUniqueCode.stream()
                    .collect(Collectors.groupingBy(i -> (allUniqueCode.indexOf(i) / 100),
                            Collectors.mapping(i -> i, Collectors.toList()))).values());
            CountDownLatch latch = new CountDownLatch(listAfterGroup.size());
            AtomicInteger online = new AtomicInteger(0);
            AtomicInteger offline = new AtomicInteger(0);
            for (List<String> subList : listAfterGroup) {
                    equipmentListPool.execute(() -> {
                        try {
                            EquipmentNumber number =
                                    ofNullable(microEquipmentService.onOffNumber(subList))
                                            .filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode())
                                            .map(cn.lyy.base.communal.bean.BaseResponse::getData).orElse(new EquipmentNumber());
                            //实际在线设备数 = 非蓝牙非刷脸关联设备在线数 + 蓝牙设备数
                            online.addAndGet(number.getOnline() + data.getBluetoothCount());
                            offline.addAndGet(number.getOffline());
                        } finally {
                            latch.countDown();
                        }
                    });

            }

            try {
                boolean finish = latch.await(5, TimeUnit.SECONDS);
                if (!finish) {
                    log.warn("查询在线离线数超时5s, 参数为:merchantId={}, userId={}", merchantId, userId);
                }
            } catch (InterruptedException e) {
                log.error(e.getMessage(), e);
            }
            map.put("onlineCount", online.get());
            map.put("offlineCount", offline.get());
            return map;
        }
        return null;
    }

    /**
     * 查询设备场地信息
     *
     * @param request 请求参数
     * @return
     */
    private List<MerchantGroupEquipmentDTO> getMerchantGroupEquipment(MerchantEquipmentRequest request) {
        cn.lyy.merchant.dto.merchant.request.MerchantEquipmentRequest mRequest =
                cn.lyy.merchant.dto.merchant.request.MerchantEquipmentRequest.builder()
                        .adUser(request.getAdUser()).groups(request.getGroups())
                        .deviceType(request.getDeviceType()).distributor(request.getDistributor())
                        .primary(request.getPrimary())
                        .equipmentTypes(request.getEquipmentType()).labels(request.getLabels())
                        .context(request.getContext()).build();
        List<MerchantGroupEquipmentDTO> mGroupEquipments = ofNullable(merchantGroupService.selectNewGroupEquipment(mRequest))
                .filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode())
                .map(BaseResponse::getData).orElse(new ArrayList<>());
        return mGroupEquipments;
    }

    /**
     * 查询设备列表
     *
     * @param request          请求参数
     * @param mGroupEquipments 场地
     * @param notShowDetailMsg 不显示详情状态信息
     * @return
     */
    private List<EquipmentListResponseDTO> getEquipmentList(MerchantEquipmentRequest request, List<MerchantGroupEquipmentDTO> mGroupEquipments,boolean notShowDetailMsg) {
        RequestConstant status = convert(request.getStatus());
        List<Long> equipmentIds = getEquipment(mGroupEquipments);
        List<EquipmentListResponseDTO> equipments = new ArrayList<>();
        if (!CollectionUtils.isEmpty(equipmentIds)) {
            // 数据切割查询
            int num = countStep(equipmentIds.size());
            List<List<Long>> collect =
                    Stream.iterate(0, n -> n + 1).limit(num).parallel()
                            .map(a -> equipmentIds.stream().skip(a * SINGLE_MAX_SIZE).limit(SINGLE_MAX_SIZE).parallel().collect(Collectors.toList()))
                            .filter(b -> !b.isEmpty()).collect(Collectors.toList());
            long startTime = System.currentTimeMillis();
            collect.forEach(ids -> {
                List<EquipmentListResponseDTO> response = getEquipmentResponse(ids, status,notShowDetailMsg);
                equipments.addAll(response);
            });
            long cost = System.currentTimeMillis() - startTime;
            log.info("[设备列表查询] 查询耗时{}ms，分组{}", cost, num);
            if (cost >= MAX_WARN_COST) {
                log.warn("[设备列表查询] 查询耗时过长({}ms)，关注性能，参数={}", cost, request);
            }
        }
        return equipments;
    }

    private List<Long> getEquipment(List<MerchantGroupEquipmentDTO> groups) {
        List<Long> equipments = new ArrayList<>();
        groups.stream().forEach(g -> g.getEquipment().forEach(equipments::add));
        return equipments;

    }

    private List<EquipmentListResponseDTO> getEquipmentResponse(List<Long> equipments, RequestConstant status,boolean notShowDetailMsg) {
        EquipmentListRequest eRequest = EquipmentListRequest.builder().equipments(equipments).status(status).notShowDetailMsg(notShowDetailMsg).build();
        return ofNullable(microEquipmentService.selectNewEquipment(eRequest))
                .filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode())
                .map(BaseResponse::getData).orElse(new ArrayList<>());
    }

    /**
     * 计算切分次数
     */
    private static Integer countStep(Integer size) {
        return (size + SINGLE_MAX_SIZE - 1) / SINGLE_MAX_SIZE;
    }

    private RequestConstant convert(Integer status) {
        if(status == null){
            return null;
        }
        return Stream.of(RequestConstant.values()).filter(r -> status.equals(r.getCode()))
                .findFirst().orElse(null);
    }



    public void saveRemoteCoins(EquipmentInfoDTO equipment, Integer coins, Integer position,
                                Long adUserId, String mainboardCode, String operate,
                                Integer extraServiceTime, String functionType,
                                Long materialId, Integer remoteType) {
        saveRemoteCoins(equipment.getEquipmentGroupId().longValue(), equipment.getEquipmentId().longValue(), coins, position,
                adUserId, mainboardCode, operate,
                extraServiceTime, functionType,
                materialId, remoteType);

    }

    public void saveRemoteCoins(Long groupId, Long equipmentId, Integer coins, Integer position,
                                Long adUserId, String mainboardCode, String operate,
                                Integer extraServiceTime, String functionType,
                                Long materialId, Integer remoteType) {
        log.debug("保存远程启动记录,设备{}, 场地{}", equipmentId, groupId);
        RemoteCoinsSaveDTO saveParam = new RemoteCoinsSaveDTO();
        saveParam.setLyyEquipmentId(equipmentId);
        saveParam.setLyyEquipmentGroupId(groupId);
        saveParam.setCoins(coins);
        saveParam.setRemoteType(remoteType);
        saveParam.setLocate(position);
        saveParam.setMainboardCode(mainboardCode);
        saveParam.setOperate(operate);
        saveParam.setLyyMaterialId(materialId);
        saveParam.setExtraServiceTime(extraServiceTime);
        saveParam.setFunctionType(functionType);
        saveParam.setCreatedby(adUserId);


        remoteCoinsMicroService.remoteCoinsSave(saveParam);

    }

    @Override
    public EquipmentFunctionDTO supportFunction(String equipmentValue) {
        EquipmentInfoDTO equipment = ofNullable(microEquipmentService.getEquipmentInfoByValue(equipmentValue))
                .map(BaseResponse::getData).orElse(null);
        if (equipment == null) {
            throw new BusinessException(BusinessExceptionEnums.DEVICE_NOT_EXISTS);
        }
        EquipmentFunctionDTO result = new EquipmentFunctionDTO();
        result.setSupportRemoteStop(false);
        result.setSupportMultiMode(false);
        result.setSupportLiquid(false);
        EquipmentTypeEnums deviceType = EquipmentTypeEnums.of(equipment.getEquipmentTypeValue());
        if (deviceType != null) {
            switch (deviceType) {
                case CHARGING_PILE:
                    chargingSupportFunction(equipment, result);
                    break;
                case CHARGING_PILE_FOR_CAR:
                    carChargingSupportFunction(equipment, result);
                    break;
                case WASHER:
                    washerSupportFunction(equipment, result);
                    break;
                default:
                    log.warn("不存在的设备类型!!,{}", equipment.getEquipmentTypeValue());
            }
        }
        return result;
    }
    private void chargingSupportFunction(EquipmentInfoDTO equipment,EquipmentFunctionDTO result) {
        if ( CDZTypeUtil.EquipemntCDZtype.CK.equals(CDZTypeUtil.getCDZtype(equipment.getLoginFlag()))) {
            ofNullable(protocolMicroService.listTypeFunction(equipment.getValue()))
                    .filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode())
                    .map(BaseResponse::getData).orElse(new ArrayList<>())
                    .forEach(e -> {
                        if (ProtocolCodeEnum.SUP_REMOTE_STOP.name().equals(e.getName())) {
                            result.setSupportRemoteStop(true);
                        }
                        // 是否支持多个计费方式下发
                        if (ProtocolCodeEnum.DELIVER_WAY_BOTH.name().equals(e.getCode())) {
                            result.setSupportMultiMode(true);
                        }
                    });

        }
    }

    private void washerSupportFunction( EquipmentInfoDTO equipment, EquipmentFunctionDTO result) {
        if (LyyUtil.isOpenEquipment(equipment.getLoginFlag())) {
            ofNullable(protocolMicroService.listTypeFunction(equipment.getValue()))
                    .filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode())
                    .map(BaseResponse::getData).orElse(new ArrayList<>())
                    .forEach(e -> {
                        if (ProtocolCodeEnum.START_ADD_LIQUID.name().equals(e.getCode())) {
                            result.setSupportLiquid(true);
                            return;
                        }
                        if (ProtocolCodeEnum.SUP_REMOTE_STOP.name().equals(e.getName())) {
                            result.setSupportRemoteStop(true);
                        }
                    });

        }
    }

    /**
     * 汽车充电桩支持功能
     * @param equipment
     * @param result
     */
    private void carChargingSupportFunction( EquipmentInfoDTO equipment, EquipmentFunctionDTO result){
        ofNullable(protocolMicroService.listTypeFunction(equipment.getValue()))
                .filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode())
                .map(BaseResponse::getData).orElse(new ArrayList<>())
                .forEach(e -> {
                    if (ProtocolCodeEnum.SUP_REMOTE_STOP.name().equals(e.getName())) {
                        result.setSupportRemoteStop(true);
                    }
                    // 是否支持多个计费方式下发
                    if (ProtocolCodeEnum.DELIVER_WAY_BOTH.name().equals(e.getCode())) {
                        result.setSupportMultiMode(true);
                    }
                });
    }


    public boolean asyncStop(String uniqueCode, Integer num) {
        OperationParam param = new OperationParam();
        param.setUniqueCode(uniqueCode);
        param.setIdentity(CommandFunctionConstants.BC_QCCDZ_STOP.toString());
        Map<String, Object> dataMap = new HashMap(1);
        dataMap.put("num", num);
        param.setParam(GsonUtils.toJson(dataMap));
        BaseResponse response = microEquipmentService.operation(param);
        log.debug("汽车充电桩远程停止，设备编号：{}，停止设备结果:{}", uniqueCode, response);
        return response.getCode() == ResponseCodeEnum.SUCCESS.getCode();
    }

}
