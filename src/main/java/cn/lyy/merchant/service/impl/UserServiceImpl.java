package cn.lyy.merchant.service.impl;

import cn.lyy.authority_service_api.AdResourcesDTO;
import cn.lyy.authority_service_api.AdRoleDTO;
import cn.lyy.authority_service_api.UserMapRolesDTO;
import cn.lyy.authority_service_api.merchant.EditableResourcesDTO;
import cn.lyy.authority_service_api.merchant.UserRoleInfo;
import cn.lyy.authority_service_api.miscroservice.AuthorityService;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.base.converter.CommonConverterTools;
import cn.lyy.base.dto.JsonObject;
import cn.lyy.base.dto.Status;
import cn.lyy.base.util.MD5Util;
import cn.lyy.base.utils.GsonUtils;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.merchant.api.service.AdOrgClient;
import cn.lyy.merchant.api.service.MerchantAccountService;
import cn.lyy.merchant.api.service.MerchantEquipmentService;
import cn.lyy.merchant.api.service.MerchantUserService;
import cn.lyy.merchant.api.service.MerchantWhiteClient;
import cn.lyy.merchant.config.DefaultRolesConfig;
import cn.lyy.merchant.constants.BusinessExceptionEnums;
import cn.lyy.merchant.constants.SystemConstants;
import cn.lyy.merchant.constants.WhiteDistributorEnum;
import cn.lyy.merchant.dto.account.*;
import cn.lyy.merchant.dto.auth.AuthUserDTO;
import cn.lyy.merchant.dto.auth.RoleDTO;
import cn.lyy.merchant.dto.common.IdDTO;
import cn.lyy.merchant.dto.common.UserInfoDTO;
import cn.lyy.merchant.dto.divide.MerchantDivideChildrenUserDTO;
import cn.lyy.merchant.dto.menu.MerchantAuthMenuDTO;
import cn.lyy.merchant.dto.menu.MerchantAuthMenuGetDTO;
import cn.lyy.merchant.dto.menu.MerchantMenuDTO;
import cn.lyy.merchant.dto.merchant.DistributorWhiteListSaveDTO;
import cn.lyy.merchant.dto.template.IdNameVO;
import cn.lyy.merchant.dto.user.AdUserDTO;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import cn.lyy.merchant.dto.user.SubAccountDTO;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.exception.CustomAssert;
import cn.lyy.merchant.exception.IncomeConstants;
import cn.lyy.merchant.redis.MerchantRedisClient;
import cn.lyy.merchant.redis.MerchantRedisKeyEnum;
import cn.lyy.merchant.service.AuthService;
import cn.lyy.merchant.service.UserService;
import cn.lyy.merchant.service.remote.SmsService;
import cn.lyy.merchant.utils.ResponseCheckUtil;
import cn.lyy.message.SmsUnifyServiceClient;
import cn.lyy.message.constants.MessageEnum;
import cn.lyy.message.constants.SmsEnum;
import cn.lyy.message.dto.sms.SmsMessage;
import cn.lyy.tools.util.PasswordUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.lyy.equipment.interfaces.dto.equipment.resp.EquipmentDeviceTypeDTO;
import com.lyy.equipment.interfaces.feign.equipment.IotEquipmentServiceFeignClient;
import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.user.account.infrastructure.constant.UserMemberSysConstants;
import com.lyy.user.account.infrastructure.constant.UserSourceEnum;
import com.lyy.user.account.infrastructure.resp.RespBody;
import com.lyy.user.account.infrastructure.user.dto.UserCreateDTO;
import com.lyy.user.account.infrastructure.user.feign.UserFeignClient;
import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.user.account.infrastructure.benefit.feign.BenefitConsumeRuleFeignClient;
import com.lyy.user.account.infrastructure.resp.RespBody;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static cn.lyy.merchant.constant.UserAccountConstants.MENU_CODE_AUTODIVIDE;

/**
 * <p>Title:saas2</p>
 * <p>Desc: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020/11/16
 */
@Service
@Slf4j
public class UserServiceImpl implements UserService {

    @Autowired
    private AuthorityService authorityService;
    @Autowired
    private DefaultRolesConfig defaultRolesConfig;
    @Autowired
    private MerchantUserService merchantUserService;
    @Autowired
    private MerchantAccountService merchantAccountService;
    @Autowired
    private AuthService authService;
    @Autowired
    private SmsService smsService;

    @Autowired
    private SmsUnifyServiceClient smsUnifyServiceClient;
    @Autowired
    private MerchantWhiteClient merchantWhiteClient;

    @Autowired
    private UserFeignClient userFeignClient;

    @Autowired
    private AdOrgClient adOrgClient;

    @Autowired
    private UserRoleServiceImpl userRoleService;

    @Autowired
    private BenefitConsumeRuleFeignClient benefitConsumeRuleFeignClient;

    private String registerSmsTemplate = "SMS_202300000027";

    @Autowired
    private IotEquipmentServiceFeignClient iotEquipmentServiceFeignClient;
    @Autowired
    private MerchantEquipmentService merchantEquipmentService;

    @Value("${register.account.whiteList:1717,1718,1810,1818,2001,2007}")
    private String registerAccountWhiteList;


    @Override
    public AdUserInfoDTO getUserInfo(String phone) throws cn.lyy.merchant.exception.BusinessException {
        BaseResponse<AdUserInfoDTO> response = merchantUserService.getUserInfo(phone);
        if (Objects.isNull(response)) {
            return null;
        }

        AdUserInfoDTO userInfo = response.getData();
        /**
         * 2. 再读取权限库的用户信息
         */
        AuthUserDTO authorityUser = getUserInfoFromAuth(userInfo, 0);
        /**
         * 3. 获取用户已有权限
         */
        buildUserInfo(userInfo, authorityUser);
        List<AdRoleDTO> roles = authorityService.getUserMapRoles(authorityUser.getAdUserId());
        List<EditableResourcesDTO> auths = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(roles)) {
            userInfo.setRoleValue(new ArrayList<>());
            userInfo.setResources(auths);

            List<EditableResourcesDTO> resourcesDTOS = authorityService.getEditableResource(SystemConstants.MERCHANT_BACKEND_SYSTEM_ID);

            roles.stream().forEach(item -> {
                userInfo.getRoleValue().add(String.valueOf(item.getAdRoleId()));
                auths.addAll(getResources(item.getAdRoleId() , resourcesDTOS));
            });
        }
        log.debug("get userInfo isApprover[{}]" , userInfo.getIsApprover());
        if(userInfo.getIsApprover()){
            //判断有是不是主账号，并且有没有默认三个角色
            try {
                authService.createDefaultRole(userInfo.getAuthorityUserId(), userInfo.getAdUserId() , userInfo.getAdOrgId());
            }catch(Exception e){
                log.warn("创建默认角色失败" , e);
            }
        }

        return userInfo;
    }

    /**
     * 获取权限库的用户信息
     * @param userVO
     * @param retryTime
     * @return
     */
    private AuthUserDTO getUserInfoFromAuth(AdUserInfoDTO userVO, int retryTime) throws cn.lyy.merchant.exception.BusinessException {
        String phone = userVO.getPhone();
        // 超过指定重试次数，抛出异常
        if(retryTime == 4) {
            log.warn("用户:[{}] 同步3次【权限库添加用户记录和用户角色记录】失败！", phone);
            throw new BusinessException("获取用户信息失败！");
        }
        AuthUserDTO user = authService.loadAuthorityUser(phone, SystemConstants.MERCHANT_BACKEND_SYSTEM_ID);
        if(Objects.isNull(user)) {
            /**
             * 兼容1.0，将1.0的老用户多绑定一个2.0角色，同步到2.0
             * 权限库添加用户记录和用户角色记录
             * 主账号才可以操作
             */
            if(userVO.getIsApprover()) {
                log.debug("用户:[{}] 1.0 用户权限数据未同步到2.0，开始同步！", phone);
                AdUserDTO userDTO = new AdUserDTO();
                userDTO.setPhone(userVO.getPhone());
                userDTO.setUserOrgId(userVO.getAdOrgId());
                userDTO.setPassword(userVO.getPassword());
                JsonObject result = authService.addAuthorityUser(userDTO, 0L, SystemConstants.MERCHANT_BACKEND_SYSTEM_ID);
                log.debug("用户:[{}] 同步结果：{}", phone, result);
                return getUserInfoFromAuth(userVO, ++ retryTime);
            }

        }
        return user;
    }

    private void buildUserInfo(AdUserInfoDTO userVO, AuthUserDTO user) {
        userVO.setName(userVO.getUserName());
        if(Objects.nonNull(user)) {
            userVO.setBelongTo(user.getBelongTo());
            userVO.setAdSystemId(user.getAdSystemId());
            userVO.setAuthorityUserId(user.getAdUserId());
        }
    }


    @Override
    public BaseResponse register(AdUserDTO adUserDTO) throws cn.lyy.merchant.exception.BusinessException {
        //防止重复提交
        String num = MerchantRedisClient.get(MerchantRedisKeyEnum.MERCHANT_REGISTER_CONCURRENT_LIMIT, adUserDTO.getPhone());
        if (StringUtils.isNotEmpty(num)) {
            throw new BusinessException("正在处理中！");
        }

        MerchantRedisClient.setex(MerchantRedisKeyEnum.MERCHANT_REGISTER_CONCURRENT_LIMIT, adUserDTO.getPhone(), "1");

        BaseResponse<RegisterResultDTO> response = merchantUserService.register(adUserDTO);
        RegisterResultDTO registerResultDTO = Optional.ofNullable(response).filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode())
                .map(BaseResponse::getData)
                .orElseThrow(() -> new BusinessException("注册失败！"));

        Long adOrgId = registerResultDTO.getAdOrgId();
        adUserDTO.setUserOrgId(adOrgId);
        // 创建角色
        JsonObject result = authService.addAuthorityUser(adUserDTO, SystemConstants.MERCHANT_BACKEND_SYSTEM_ID_VERSION_1, SystemConstants.MERCHANT_BACKEND_SYSTEM_ID);
        if(Objects.isNull(result.getResult()) || result.getResult() != Status.STATUS_SUCCESS) {
            log.warn("权限库注册失败\n{}", result.getDescription());
        }

        // 用户加入 341 灰度名单
        DistributorWhiteListSaveDTO distributorWhiteListSaveDTO = new DistributorWhiteListSaveDTO();
        distributorWhiteListSaveDTO.setDistributorId(adOrgId);
        distributorWhiteListSaveDTO.setName(adUserDTO.getUserName());
        distributorWhiteListSaveDTO.setType(WhiteDistributorEnum.GRAY_MERCHANT.getType());
        Boolean whiteResult =  Optional.ofNullable(merchantWhiteClient.saveDistributorWhiteList(distributorWhiteListSaveDTO)).filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode())
                .map(BaseResponse::getData).get();
        if(!Boolean.TRUE.equals(whiteResult)){
            log.warn("加入灰度名单失败,adOrgId:{}", adOrgId);
        }

        for (String type : registerAccountWhiteList.split(",")) {
            DistributorWhiteListSaveDTO registerDistributorWhiteListSaveDTO = new DistributorWhiteListSaveDTO();
            registerDistributorWhiteListSaveDTO.setDistributorId(adOrgId);
            registerDistributorWhiteListSaveDTO.setName(adUserDTO.getUserName());
            registerDistributorWhiteListSaveDTO.setType(Integer.valueOf(type));
            Boolean registerWhiteResult =  Optional.ofNullable(merchantWhiteClient.saveDistributorWhiteList(registerDistributorWhiteListSaveDTO)).filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode())
                    .map(BaseResponse::getData).get();
            if(!Boolean.TRUE.equals(registerWhiteResult)){
                log.warn("加入灰度名单{}失败,adOrgId:{}", type,adOrgId);
            }
        }

        //初始化商户的平台用户并保存关系
        initMerchantPlatformUser(adOrgId, adUserDTO.getUserId(), adUserDTO.getUserName());
        //注册初始化权益使用规则
        RespBody<Void> respBody = benefitConsumeRuleFeignClient.initMerchantBenefitConsume(adOrgId, registerResultDTO.getAdUserId());
        if(!GlobalErrorCode.OK.getCode().equals(respBody.getCode())) {
            log.warn("初始化权益使用规则失败:{}", adOrgId);
        }
        userRoleService.updateSystemUserAndRole(true,adUserDTO.getPhone());

        return response;
    }

    @Override
    public BaseResponse modifyPassword(AdUserDTO adUserDTO) {
        BaseResponse response = merchantUserService.modifyPassword(adUserDTO);

        if(response.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            return response;
        }
        String phone = adUserDTO.getPhone();
        String password = MD5Util.MD5(adUserDTO.getPassword());
        //修改1.0和2.0的密码
        AuthUserDTO userDTO = authService.loadAuthorityUser(phone, SystemConstants.MERCHANT_BACKEND_SYSTEM_ID);
        if(Objects.nonNull(userDTO)) {
            userDTO.setPassword(password);
            cn.lyy.authority_service_api.AdUserDTO authDTO = CommonConverterTools.convert(cn.lyy.authority_service_api.AdUserDTO.class, userDTO);
            authorityService.updateUser(authDTO);
        }

        AuthUserDTO userDTO2 = authService.loadAuthorityUser(phone, SystemConstants.MERCHANT_BACKEND_SYSTEM_ID_VERSION_1);
        if(Objects.nonNull(userDTO)) {
            userDTO2.setPassword(password);
            cn.lyy.authority_service_api.AdUserDTO authDTO = CommonConverterTools.convert(cn.lyy.authority_service_api.AdUserDTO.class, userDTO2);
            authorityService.updateUser(authDTO);
        }
        return response;
    }

    @Override
    public List<MerchantAuthMenuDTO> getUserAuthMenu(MerchantAuthMenuGetDTO param) {
        log.info("用户{}_菜单请求参数：{}",param.getUserId(), JSON.toJSONString(param));

        List<AdRoleDTO> roles = authorityService.getUserMapRoles(param.getAuthorityUserId());
        log.info("用户{}_查询到的用户角色信息：{}",param.getUserId(),JSON.toJSONString(roles));

        List<EditableResourcesDTO> auths = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(roles)) {
            List<EditableResourcesDTO> resourcesDTOS = authorityService.getEditableResource(SystemConstants.MERCHANT_BACKEND_SYSTEM_ID);
            for(AdRoleDTO item : roles) {
                auths.addAll(getResources(item.getAdRoleId() , resourcesDTOS));
            }
        }

        log.info("用户{}_查询到的角色菜单1：{}",param.getUserId(),JSON.toJSONString(auths));
        if(auths == null)
            auths = authorityService.getEditableResource(SystemConstants.MERCHANT_BACKEND_SYSTEM_ID).stream().filter(item -> !item.getValue().equalsIgnoreCase("SaasMerchant-Default"))
                    .collect(Collectors.toList());

        log.info("用户{}_查询到的角色菜单2：{}",param.getUserId(),JSON.toJSONString(auths));
        if(auths == null || auths.size() == 0)
            return Collections.emptyList();

        Map<String , EditableResourcesDTO> resourcesDTOMap = new HashMap<>();

        //资源列表组装成map
        auths.stream().filter(Objects::nonNull).forEach(f -> {
            f.getChilds().forEach(child -> {
                if (child != null && child.getAdResourcesId() > 0) {
                    resourcesDTOMap.put(child.getValue(), child);
                }
            });
        });
        BaseResponse<List<MerchantMenuDTO>> response = new BaseResponse<>();
        List<Long> equipmentIdList = ResponseCheckUtil.getData(merchantEquipmentService.findEquipmentIdsByMerchantId(param.getUserOrgId()));
        if(!CollectionUtils.isEmpty(equipmentIdList)){
            log.warn("未找到商家:{}的设备信息",param.getUserOrgId());
            List<EquipmentDeviceTypeDTO> deviceTypeList = ResponseCheckUtil.getData(iotEquipmentServiceFeignClient.getEquipmentIdListByDeviceTypeSelect(equipmentIdList));
            if(CollectionUtils.isEmpty(deviceTypeList)){
                log.warn("未找到商家:{} 的设备信息",param.getUserOrgId());
                return Collections.emptyList();
            }

            List<Long> equipmentIdCollect = deviceTypeList.stream().filter(i -> "LYY2".equals(i.getDeviceType()))
                    .map(EquipmentDeviceTypeDTO::getId).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(equipmentIdCollect)){
                param.setEquipmentIdList(equipmentIdCollect);
            }
            response = merchantUserService.getUserMenu(param);
            log.info("用户{}_查询到的工厂配置插件菜单：{}",param.getUserId(),JSON.toJSONString(response));

            if(Objects.isNull(response) || response.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
                return Collections.emptyList();
            }
        }else {
            response = merchantUserService.getUserMenu(param);
        }




        Map<String, DefaultRolesConfig.Menu> defaultMenuMap = defaultRolesConfig.getMenus().stream().collect(Collectors.toMap(k -> k.getMenuCode(), v -> v));
        log.info("用户{}_查询到的菜单map配置：{}",param.getUserId(),JSON.toJSONString(defaultMenuMap));

        List<MerchantMenuDTO> menuVOs = Lists.newArrayList();
        response.getData().stream()
            .filter(item -> {
                //筛选掉没有权限的菜单
                return resourcesDTOMap.containsKey(item.getValue());
            }).forEach(item -> {
                String menuCode = item.getValue();
                DefaultRolesConfig.Menu defaultMenu = defaultMenuMap.get(menuCode);
                if(Objects.isNull(defaultMenu))
                    return;
                if(! defaultMenu.isShowEnabled())
                    return;

                Boolean canChange = defaultMenu.getShowIndexPage().isCanChange();
                Boolean defaultShow = defaultMenu.getShowIndexPage().isDefaultShow();
                //判断展示权限能否修改，不能修改以默认值为准
                if(canChange) {
                    //是否显示在首页以数据库保存的值为准,否则以默认值
                    if(Objects.nonNull(item.getMenuIndexPage().getIsShow()))
                        defaultShow = item.getMenuIndexPage().getIsShow();
                }
                // 系统配置的更新时间
                Long updated = null;
                // 系统配置的菜单附加参数
                JSONObject attachParam = null;
                // icon
                String icon = null;
                icon = item.getIcon();
                updated = item.getUpdated();
                attachParam = Objects.isNull(item.getAttchParam()) ? null : JSON.parseObject(item.getAttchParam().toString());
                MerchantMenuDTO build = MerchantMenuDTO.build(defaultMenu.getId(), defaultMenu.getTypeCode(), defaultMenu.getMenuName(), menuCode, resourcesDTOMap.get(defaultMenu.getMenuCode()).getAdResourcesId(), canChange, defaultShow, updated, attachParam);
                build.setIcon(icon);
                menuVOs.add(build);
            });
        log.info("用户{}_过滤之后的菜单：{}",param.getUserId(),JSON.toJSONString(menuVOs));

        //如果是首页展示时就需要按时间排序
        if(Objects.nonNull(param.getSort()) && param.getSort()) {
            sortedMenu(menuVOs);
        }

        //依据类目分类插入菜单
        List<MerchantAuthMenuDTO> authMenuList = defaultRolesConfig.getMenuTypes().stream().map(item -> {
            MerchantAuthMenuDTO authMenuVO = new MerchantAuthMenuDTO();
            authMenuVO.setId(item.getId());
            authMenuVO.setValue(item.getCode());
            authMenuVO.setName(item.getName());
            authMenuVO.setChilds(menuVOs.stream().filter(menu -> menu.getTypeCode().equalsIgnoreCase(item.getCode())).collect(Collectors.toList()));
            return authMenuVO;
        }).collect(Collectors.toList());


        return authMenuList;
    }

    private void sortedMenu(List<MerchantMenuDTO> menuVOs){
        //排序，时间早的排最前
        Collections.sort(menuVOs, (childLeft, childRight) -> {
            if (childLeft.getUpdated() == null) {
                childLeft.setUpdated(System.currentTimeMillis());
            }
            if (childRight.getUpdated() == null) {
                childRight.setUpdated(System.currentTimeMillis());
            }

            int c = childLeft.getUpdated().compareTo(childRight.getUpdated());
            //如果时间一样，就以id小的优先
            if(c == 0){
                c = childLeft.getId().compareTo(childRight.getId());
            }
            return c;
        });
    }

    @Override
    public List<EditableResourcesDTO> getResources(Long roleId, List<EditableResourcesDTO> resourcesDTOS) {
        List<AdResourcesDTO> adResourcesDTOS = authorityService.getMenusByRoleId(roleId);
        List<Long> myResources = adResourcesDTOS.stream()
                .map(item -> item.getAdResourcesId())
                .collect(Collectors.toList());

        if (resourcesDTOS == null) {
            resourcesDTOS = authorityService.getEditableResource(SystemConstants.MERCHANT_BACKEND_SYSTEM_ID);
        }

        List<EditableResourcesDTO> newResultList = Lists.newArrayList();
        resourcesDTOS.stream()
                .filter(item -> !item.getValue().equalsIgnoreCase("SaasMerchant-Default"))
                .forEach(item ->  {
                    List<EditableResourcesDTO> newChilds = item.getChilds().stream().filter(f -> myResources.contains(f.getAdResourcesId())).collect(Collectors.toList());
                    if(CollectionUtils.isEmpty(newChilds)) {
                        return;
                    }
                    EditableResourcesDTO newRes = new EditableResourcesDTO();
                    BeanUtils.copyProperties(item , newRes);
                    newRes.setChilds(newChilds);
                    newResultList.add(newRes);
                });

        return newResultList;
    }

    @Override
    public List<SubAccountUserInfoDTO> getSubAccountInfos(IdDTO userInfoDTO) {
        //这里不能带userId
        userInfoDTO.setUserId(null);
        if (userInfoDTO.getId() != null && userInfoDTO.getId() > 0) {
            userInfoDTO.setUserId(userInfoDTO.getId());
        }
        Long userId = userInfoDTO.getUserId();
        boolean hasUserId = userId != null;
        if (hasUserId) {
            String cacheJson = MerchantRedisClient.hget(MerchantRedisKeyEnum.MERCHANT_USER_REFRESH_LOGIN_STATUS, String.valueOf(userId));
            if (StringUtils.isNotBlank(cacheJson)) {
                List<SubAccountUserInfoDTO> dtos = new Gson().fromJson(cacheJson, new TypeToken<List<SubAccountUserInfoDTO>>() {
                }.getType());
                return dtos;
            }
        }
        Long authorityUserId = userInfoDTO.getAuthorityUserId();
        List<SubAccountUserInfoDTO> vos = Lists.newArrayList();
        List<UserRoleInfo> childAccountsRoles = authorityService.getChildAccountsRole(authorityUserId, SystemConstants.MERCHANT_BACKEND_SYSTEM_ID);
        if(CollectionUtils.isEmpty(childAccountsRoles)) {
            return vos;
        }
        Map<String, List<UserRoleInfo>> userRoleMap = childAccountsRoles.stream().collect(Collectors.toMap(k -> k.getUsername(), v -> Lists.newArrayList(v), (v1, v2) -> {v1.addAll(v2); return v1;}));
        BaseResponse<SubAccountUserInfoResultDTO> subAccountInfos = merchantAccountService.getSubAccountInfos(userInfoDTO);
        SubAccountUserInfoResultDTO subAccountUserInfoResultDTO = Optional.ofNullable(subAccountInfos).filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode())
                .map(BaseResponse::getData)
                .orElse(null);
        if (subAccountUserInfoResultDTO == null) {
            throw new BusinessException(String.valueOf(Status.STATUS_500), "系统错误，请稍后重试");
        }
        List<SubAccountUserInfoDTO> list = subAccountUserInfoResultDTO.getList();
        List<Long> divideUserIds = subAccountUserInfoResultDTO.getDivideUserIds();

        list.stream().forEach(user ->{
            List<UserRoleInfo> userRoleInfos = userRoleMap.get(user.getName());
            if(Objects.isNull(userRoleInfos)) {
                return;
            }
            List<MerchantRoleDTO> roles = userRoleInfos.stream().map(m -> new MerchantRoleDTO(m.getAdRoleId(), m.getRoleName())).collect(Collectors.toList());
            user.setRoles(roles);
            Long roleId = roles.get(0).getRoleId();
            List<AdResourcesDTO> adResourcesDTOS = authorityService.getMenusByRoleId(roleId);
            long count = adResourcesDTOS.stream().filter(f -> MENU_CODE_AUTODIVIDE.equalsIgnoreCase(f.getValue())).count();
            if(count > 0) {
                user.setDivideUserIds(divideUserIds);
            }
            vos.add(user);
        });
        if (hasUserId) {
            MerchantRedisClient.hset(MerchantRedisKeyEnum.MERCHANT_USER_REFRESH_LOGIN_STATUS, String.valueOf(userId), GsonUtils.toJson(vos));
        }
        return vos;
    }

    @Override
    public int saveOrUpdateSubAccount(SubAccountDTO adUserDTO) throws BusinessException {
        String phone = adUserDTO.getPhone();
        if(Objects.nonNull(adUserDTO.getUserId())) {
            if (handleWithUserId(adUserDTO)) {
                return -1;
            }
        } else {
            // 是否已注册
            try {
                BaseResponse baseResponse = merchantUserService.checkMobileExists(phone);
                log.debug("注册检查:{}", baseResponse);
                Optional.ofNullable(baseResponse).filter(r -> ResponseCodeEnum.SUCCESS.getCode() != r.getCode())
                        .map(r -> {
                            throw new BusinessException(r.getMessage());
                        });
                // 注册并关联角色
                BaseResponse<RegisterResultDTO> response = merchantUserService.register(adUserDTO);
                RegisterResultDTO registerResultDTO = Optional.ofNullable(response).filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode())
                        .map(BaseResponse::getData)
                        .orElseThrow(() -> new BusinessException(IncomeConstants.FAIL.getCode(), IncomeConstants.FAIL.getMessage()));
                adUserDTO.setUserId(registerResultDTO.getAdUserId());

            } catch (BusinessException e) {
                // 判断是否所属组织
                SubAccountSaveOrUpdateResult updateResult = handleWhenUserIdNotExist(adUserDTO);
                if (updateResult==null || updateResult.getResult() != 0) {
                        throw e;
                }
            }
            // 创建角色 || 因之前删除用户时删过用户和角色，这里重新创建角色
            JsonObject result = authService.addAuthorityUser(adUserDTO, SystemConstants.MERCHANT_BACKEND_SYSTEM_ID_VERSION_1, SystemConstants.MERCHANT_BACKEND_SYSTEM_ID);
            if (Objects.nonNull(result.getResult()) && result.getResult() != Status.STATUS_SUCCESS) {
                log.error("权限库注册失败\n{}", result.getDescription());
                throw new BusinessException("绑定员工账号失败！");
            }

            // 发送短信
            Map<String, Object> smsMap = new HashMap<>();
            smsMap.put("phone", phone);
            smsMap.put("password", adUserDTO.getPassword());
            SmsMessage message = new SmsMessage();
            message.setTemplateId(registerSmsTemplate);
            message.setSmsType("N");
            message.setType(MessageEnum.SMS);
            message.setRetryCount(0);
            message.setPhone(phone);
            message.setSmsEnum(SmsEnum.SEND_CONTENT);
            message.setData(smsMap);
            smsUnifyServiceClient.sendSms(message);
        }

        if(CollectionUtils.isNotEmpty(adUserDTO.getGroupIds())) {
            //变更场地用户关联关系
            merchantAccountService.changeGroupAdUser(adUserDTO);
        }

        if(CollectionUtils.isNotEmpty(adUserDTO.getDivideUserIds())) {
            // 2.0绑定分成人员信息
            // 先查看该角色有没有关联自动分账
            List<AdResourcesDTO> adResourcesDTOS = authorityService.getMenusByRoleId(adUserDTO.getRoleIds().get(0));
            long count = adResourcesDTOS.stream().filter(f -> MENU_CODE_AUTODIVIDE.equalsIgnoreCase(f.getValue())).count();
            if(count > 0) {
                //记录B端用户的所有功能菜单是否显示在首页  --2.0使用的，应该可以废弃
                merchantAccountService.saveMerchantUserMenu(adUserDTO);
            }
        }
        //3.0更新绑定分成人员信息
        MerchantDivideChildrenUserDTO divideUser = new MerchantDivideChildrenUserDTO();
        divideUser.setAdUserId(adUserDTO.getUserId());
        divideUser.setLyyDivideUserId(adUserDTO.getDivideUserIds());
        merchantAccountService.updateDivideChildrenUser(divideUser);
        return 0;
    }

    private SubAccountSaveOrUpdateResult handleWhenUserIdNotExist(SubAccountDTO adUserDTO) {
        BaseResponse<SubAccountSaveOrUpdateResult> baseResponse = merchantAccountService.updateUserInfo(adUserDTO);
        SubAccountSaveOrUpdateResult updateResult = Optional.ofNullable(baseResponse)
                .filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode())
                .map(BaseResponse::getData)
                .orElse(null);
        if (updateResult == null) {
            log.warn("请求权限服务接口异常:{}", baseResponse);
        }

        return updateResult;
    }

    private boolean handleWithUserId(SubAccountDTO adUserDTO) throws BusinessException {
      // 是否所属同个组织
        BaseResponse<SubAccountSaveOrUpdateResult> baseResponse = merchantAccountService.updateUserInfo(adUserDTO);
        SubAccountSaveOrUpdateResult updateResult = Optional.ofNullable(baseResponse)
                .filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode())
                .map(BaseResponse::getData)
                .orElse(null);
        if (updateResult == null) {
            log.error("请求权限服务接口异常:{}", baseResponse);
            throw new BusinessException("创建员工账号失败!");
        }
        if (updateResult.getResult() != 0) {
            throw new BusinessException(updateResult.getError());
        }
        String phone = adUserDTO.getPhone();
        // 给用户添加角色
        AuthUserDTO userDTO = authService.loadAuthorityUser(phone, SystemConstants.MERCHANT_BACKEND_SYSTEM_ID);
        UserMapRolesDTO userRoles = new UserMapRolesDTO();
        userRoles.setRoleIds(adUserDTO.getRoleIds());
        userRoles.setUserId(userDTO.getAdUserId());
        JsonObject result = authorityService.saveUserMapRoles(userRoles);
        if(Objects.nonNull(result.getResult()) && result.getResult() != Status.STATUS_SUCCESS) {
           log.error("权限库账号角色关联失败！{}", result.getDescription());
            throw new BusinessException("创建员工账号失败！");
        }
        //修改密码
        if (StringUtils.isNotBlank(adUserDTO.getPassword())) {
            // 重置密码
            String password = MD5Util.MD5(adUserDTO.getPassword());
            Map<String, String> params = new HashMap<>(3);
            params.put("password", password);
            params.put("username", phone);
            params.put("adSystemId", String.valueOf(SystemConstants.MERCHANT_BACKEND_SYSTEM_ID));
            authorityService.updatePassword(params);
        }

        delSubAccountInfoCacheByUserId(String.valueOf(adUserDTO.getUserId()));
        return false;
    }

    @Override
    public BaseResponse deleteSubAccount(IdDTO idDTO) throws BusinessException {
        BaseResponse<AdUserInfoDTO> deleteSubAccount = merchantAccountService.deleteSubAccount(idDTO);
        log.debug("deleteSubAccount:{}", deleteSubAccount);
        if (deleteSubAccount == null || ResponseCodeEnum.SUCCESS.getCode() != deleteSubAccount.getCode()) {
            return ResponseUtils.error(Status.STATUS_FAIL, "删除子账户失败");
        }
        // TODO: 2021/3/5  ID与userId 一样？?
        AdUserInfoDTO one = deleteSubAccount.getData();
        if (one != null) {
            authorityService.deleteUserByName(one.getPhone(), SystemConstants.MERCHANT_BACKEND_SYSTEM_ID);
            authorityService.deleteUserByName(one.getPhone(), SystemConstants.MERCHANT_BACKEND_SYSTEM_ID_VERSION_1);
        }
        delSubAccountInfoCacheByUserId(String.valueOf(idDTO.getId()));
        return ResponseUtils.success("删除子账户成功");
    }

    @Override
    public List<IdNameVO> listSubAccountRole(UserInfoDTO userInfoDTO) {
        //TODO 查询的权限库userId需要是登录人的主账号id，如果是子账号需要进一步查询
        List<AdRoleDTO> list = authorityService.getAllRolesBelongTo(SystemConstants.MERCHANT_BACKEND_SYSTEM_ID , userInfoDTO.getAuthorityUserId());

        Map<String, Integer> roles = getDefaultRoles();

        List<IdNameVO> retList = Lists.newLinkedList();
        list.stream().filter(f -> roles.keySet().contains(f.getName())).collect(Collectors.toList()).forEach(item -> {
            Integer sort = roles.get(item.getName());
            retList.add(new IdNameVO(item.getAdRoleId(), item.getName(), sort));
        });
        Collections.sort(retList, Comparator.comparing(IdNameVO::getSort));
        list.stream().filter(f -> ! roles.keySet().contains(f.getName())).forEach(item ->  {
            retList.add(new IdNameVO(item.getAdRoleId(), item.getName()));
        });
        return retList;
    }

    private Map<String, Integer> getDefaultRoles() {
        Map<String, Integer> roleNames = Maps.newHashMap();
        if(Objects.nonNull(defaultRolesConfig) && CollectionUtils.isNotEmpty(defaultRolesConfig.getRoles())) {
            roleNames = defaultRolesConfig.getRoles().stream().collect(Collectors.toMap(k -> k.getName(), v-> v.getId()));
        }
        return roleNames;
    }

    @Override
    public List<String> getUserAuthMenu(IdDTO idDTO) {
        List<AdResourcesDTO> adResourcesDTOS = authorityService.getMenusByRoleId(idDTO.getId());
        return adResourcesDTOS.stream()
                .map(item -> item.getValue())
                .collect(Collectors.toList());

    }

    @Override
    public BaseResponse saveSubAccountRole(RoleDTO roleDTO) {
        JsonObject result  = authService.saveOrUpdateRole(roleDTO);
        Long roleId = roleDTO.getRoleId();
        if(result.getResult() == 0 && roleDTO.getRoleId() != null){
            settingSubAccountRefreshLoginStatus(roleId);
        }
        return result.getResult() == 0?ResponseUtils.success():ResponseUtils.error(Status.STATUS_FAIL,result.getDescription());
    }

    /**
     * 修改角色权限时，让拥有该角色的子账号重新加载登录信息
     * @param roleId
     */
    private void settingSubAccountRefreshLoginStatus(Long roleId){
        JsonObject result = authorityService.getUserByRoleId(roleId.intValue() , SystemConstants.MERCHANT_BACKEND_SYSTEM_ID );
        if (result != null && result.getResult() == 0 && result.getData() != null) {
            JSONArray array = (JSONArray) JSON.toJSON(result.getData());
            List<SubAccountRefreshDTO> list = new ArrayList<>();
            for (int i = 0; i < array.size(); i++) {
                JSONObject item = array.getJSONObject(i);
                SubAccountRefreshDTO refreshDTO = new SubAccountRefreshDTO();
                refreshDTO.setAdOrgId(item.getLong("belongTo"));
                refreshDTO.setName(item.getString("username"));
                list.add(refreshDTO);
            }
            BaseResponse<List<String>> baseResponse = merchantAccountService.getRefreshUerIds(list);
            List<String> refreshUserIds = Optional.ofNullable(baseResponse).filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode())
                    .map(BaseResponse::getData)
                    .orElse(new ArrayList<>());
            log.debug("role[{}] found users[{}]", roleId, refreshUserIds);

            refreshUserIds.forEach(userId -> {
                //标记子账号需要重新加载登录信息，主要是拥有的角色
                delSubAccountInfoCacheByUserId(userId);
            });

        } else {
            log.debug("role[{}] not found users", roleId);
        }
    }

    private void delSubAccountInfoCacheByUserId(String userId) {
        MerchantRedisClient.hdel(MerchantRedisKeyEnum.MERCHANT_USER_REFRESH_LOGIN_STATUS.getKey(), userId);
    }

    @Override
    public BaseResponse deleteSubAccountRole(IdDTO idDTO) {
        AdRoleDTO adRoleDTO = new AdRoleDTO();
        adRoleDTO.setAdRoleId(idDTO.getId());
        Map<String, Integer> roleNames = getDefaultRoles();
        List<AdRoleDTO> roles = authorityService.getAllRolesBelongTo(SystemConstants.MERCHANT_BACKEND_SYSTEM_ID , idDTO.getAuthorityUserId());
        for (int i = 0; i < roles.size(); i++) {
            if(roles.get(i).getAdRoleId().compareTo(idDTO.getId()) == 0){
                if(roleNames.keySet().contains(roles.get(i).getName())){
                    throw new BusinessException(BusinessExceptionEnums.POST_CAN_NOT_DELETE);
                }
                else {
                    adRoleDTO.setValue(roles.get(i).getValue());
                }
            }
        }
        JsonObject result =  authorityService.deleteRole(adRoleDTO);
        if(result.getResult() == 0){
            settingSubAccountRefreshLoginStatus(idDTO.getId());
        }
        return result.getResult() == 0?ResponseUtils.success():ResponseUtils.error(Status.STATUS_FAIL,result.getDescription());
    }

    /**
     * 是否在3.0灰度商户名单中
     * @param userInfoDTO
     * @return
     */
    @Override
    public boolean isWhiteDistributor(AdUserInfoDTO userInfoDTO) {
        return Optional
            .ofNullable(merchantWhiteClient.isWhiteDistributor(userInfoDTO.getAdOrgId(), WhiteDistributorEnum.GRAY_MERCHANT.getType()))
            .map(a -> a.getData()).orElse(false);
    }

    /**
     * 是否在2.0的商户名单内
     * @param userInfoDTO
     * @return
     */
    public boolean isSecondWhiteDistributor(AdUserInfoDTO userInfoDTO) {
        return Optional
            .ofNullable(merchantWhiteClient.isWhiteDistributor(userInfoDTO.getAdOrgId(), WhiteDistributorEnum.SECOND_MERCHANT.getType()))
            .map(BaseResponse::getData).orElse(false);
    }

    @Override
    public boolean isOneWhiteDistributor(AdUserInfoDTO userInfoDTO) {
        return Optional
                .ofNullable(merchantWhiteClient.isWhiteDistributor(userInfoDTO.getAdOrgId(), WhiteDistributorEnum.CDZ_JUMP_ONE_ACCOUNT.getType()))
                .map(BaseResponse::getData).orElse(false);
    }

    /**
     * 初始化商户的平台用户并保存关系
     *
     * @param adOrgId   商户id
     * @param operationUserId  操作人id
     * @param name  名称
     */
    private void initMerchantPlatformUser(Long adOrgId, Long operationUserId, String name) {
        try {
            UserCreateDTO userCreateDTO = new UserCreateDTO();
            userCreateDTO.setUserSourceEnum(UserSourceEnum.WE_CHAT_WEB);
            userCreateDTO.setNickname(name);
            userCreateDTO.setOpenid(UserMemberSysConstants.MERCHANT_PREFIX + adOrgId.toString());
            userCreateDTO.setUnionid(UserMemberSysConstants.MERCHANT_PREFIX + adOrgId.toString());
            userCreateDTO.setOperationUserId(operationUserId);
            userCreateDTO.setPassword(PasswordUtil.generateEncryptPassword());
            RespBody<com.lyy.user.account.infrastructure.user.dto.UserInfoDTO> respBody = userFeignClient.initUserInfo(userCreateDTO);
            if (GlobalErrorCode.OK.getCode().equals(respBody.getCode())
                    && respBody.getBody() != null
                    && respBody.getBody().getLyyUserId() != null) {
                adOrgClient.updateMerchantPlatformUser(adOrgId, respBody.getBody().getLyyUserId(), operationUserId);
            } else {
                log.error("商户注册初始化平台用户失败,adOrgId:{}", adOrgId);
            }
        } catch (Exception e) {
            log.error("初始化商户的平台用户并保存关系失败,adOrgId:{}", adOrgId);
            log.error(e.getMessage(), e);
        }
    }
}
