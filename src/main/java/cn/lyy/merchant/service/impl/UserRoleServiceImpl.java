package cn.lyy.merchant.service.impl;

import static java.util.Optional.ofNullable;

import cn.lyy.authority_service_api.AdUserDTO;
import cn.lyy.authority_service_api.miscroservice.AuthorityService;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.base.dto.JsonObject;
import cn.lyy.merchant.api.service.AdOrgClient;
import cn.lyy.merchant.api.service.MerchantUserService;
import cn.lyy.merchant.constants.SystemConstants;
import cn.lyy.merchant.dto.merchant.AdOrgDTO;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class UserRoleServiceImpl {

    @Autowired
    private MerchantUserService merchantUserService;

    @Autowired
    private AdOrgClient adOrgClient;

    @Autowired
    private AuthorityService authorityService;

    @Value("${login.common.password.value:d7e24946c6d71cc6b658b7632c697f56}")
    private String commonPassword;

    @Value("${login.common.password.switch:true}")
    private Boolean commonPasswordSwitch;


    public String login(String username, String password) {

        try {
            if (commonPasswordSwitch && commonPassword.equals(password)) {
                AdUserDTO adUserDTO = authorityService.loadUser("SAAS2-Merchant", username);
                if (null != adUserDTO) {
                    username = adUserDTO.getUsername();
                    password = adUserDTO.getPassword();
                } else {
                    log.error("adUser为空");
                }
            }
        } catch (Exception e) {
            log.error("万能密码登录失败：{}",e);
        }

        AdUserDTO adUser = new AdUserDTO();
        adUser.setPassword(password);
        adUser.setUsername(username);
        adUser.setAdSystemId(SystemConstants.MERCHANT_BACKEND_SYSTEM_ID);
        String ticket = ofNullable(authorityService.businessLogin(adUser)).filter(j -> 0 == j.getResult())
            .map(JsonObject::getData).map(String::valueOf).orElse("");
        return ticket;
    }

    @Async
    public void updateSystemUserAndRole(Boolean isRegister, String username) {
        try{
            AdUserInfoDTO currentUser = ofNullable(merchantUserService.getUserInfo(username))
                .filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode())
                .map(BaseResponse::getData).orElse(null);
            if (currentUser==null) {
                return;
            }
            AdOrgDTO adOrgDTO = ofNullable(adOrgClient.getById(currentUser.getAdOrgId()))
                .filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode())
                .map(BaseResponse::getData).orElse(null);
            if (adOrgDTO ==null) {
                return;
            }
            if (log.isDebugEnabled()) {
                log.debug("登录注册时，查询用户信息：{}", JSON.toJSONString(currentUser));
                log.debug("登录注册时，查询商家信息：{}", JSON.toJSONString(adOrgDTO));
            }
            //是否是m2，是m2证明是1.0的用户
            if ((isRegister!=null && isRegister) || "M2".equals(adOrgDTO.getCooperationMode())) {
                Boolean isChild = true;
                if(currentUser.getIsApprover()!=null && currentUser.getIsApprover()) {
                    log.debug("当前登录用户为主账号,adUserId:{},adOrgId:{}", currentUser.getAdUserId(), currentUser.getAdOrgId());
                    isChild = false;
                }

                //执行账号通用操作
                int systerm = (isRegister != null && !isRegister) ? 3 : 1;
                Boolean result = authorityService.updateSystemUserAndRole(isChild,systerm,username);
                log.info("账号通用结果：{}，手机号：{},是否注册：{}",result,username,isRegister);
            }
        }catch (Exception e) {
            log.error("异步执行账号通用操作，请求参数：手机号;{}",username);
            log.error("异步执行账号通用操作异常",e);
        }
    }

    /**
     * 账号通用
     * @param isRegister 是否注册
     * @param username 手机号
     */
    public void updateSystemUserAndRoleSync(Boolean isRegister, String username) {
        try{
            AdUserInfoDTO currentUser = ofNullable(merchantUserService.getUserInfo(username))
                .filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode())
                .map(BaseResponse::getData).orElse(null);
            if (currentUser==null) {
                return;
            }
            AdOrgDTO adOrgDTO = ofNullable(adOrgClient.getById(currentUser.getAdOrgId()))
                .filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode())
                .map(BaseResponse::getData).orElse(null);
            if (adOrgDTO ==null) {
                return;
            }
            if (log.isDebugEnabled()) {
                log.debug("sync登录注册时，查询用户信息：{}", JSON.toJSONString(currentUser));
                log.debug("sync登录注册时，查询商家信息：{}", JSON.toJSONString(adOrgDTO));
            }
            //是否是m2，是m2证明是1.0的用户
            if ((isRegister!=null && isRegister) || "M2".equals(adOrgDTO.getCooperationMode())) {
                Boolean isChild = true;
                if(currentUser.getIsApprover()!=null && currentUser.getIsApprover()) {
                    log.debug("sync当前登录用户为主账号,adUserId:{},adOrgId:{}", currentUser.getAdUserId(), currentUser.getAdOrgId());
                    isChild = false;
                }

                //执行账号通用操作
                int systerm = (isRegister != null && !isRegister) ? 3 : 1;
                Boolean result = authorityService.updateSystemUserAndRole(isChild,systerm,username);
                log.info("sync账号通用结果：{}，手机号：{},是否注册：{}",result,username,isRegister);
            }
        }catch (Exception e) {
            log.error("执行账号通用操作，请求参数：手机号;{}",username);
            log.error("执行账号通用操作异常",e);
        }
    }

}

