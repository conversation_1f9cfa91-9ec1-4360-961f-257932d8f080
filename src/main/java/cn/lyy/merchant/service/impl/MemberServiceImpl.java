package cn.lyy.merchant.service.impl;

import static cn.lyy.merchant.service.order.impl.RefundServiceImpl.generateStoredRefund;
import static cn.lyy.merchant.utils.PaymentRefactorUtils.getData;
import static java.util.Optional.ofNullable;

import cn.hutool.core.map.MapUtil;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.base.dto.Pagination;
import cn.lyy.equipment.dto.equipment.EquipmentTypeDTO;
import cn.lyy.equipment.service.IEquipmentTypeService;
import cn.lyy.lyy_consumption_api.customer.LyyGrantCoinsDTO;
import cn.lyy.lyy_consumption_api.merchant.GrantCoinsDTO;
import cn.lyy.lyy_consumption_api.merchant.LyyGrantCoinsPara;
import cn.lyy.merchant.api.service.MerchantMemberAppealService;
import cn.lyy.merchant.constants.BusinessExceptionEnums;
import cn.lyy.merchant.dto.common.UserInfoDTO;
import cn.lyy.merchant.dto.member.AppealHandleParam;
import cn.lyy.merchant.dto.member.AppealHandleResult;
import cn.lyy.merchant.dto.member.AppealRecordDTO;
import cn.lyy.merchant.dto.member.MemberGiftRedPacketSettingDTO;
import cn.lyy.merchant.dto.member.MemberInfoDTO;
import cn.lyy.merchant.dto.member.MemberInfoQueryDTO;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.service.MemberService;
import cn.lyy.merchant.service.remote.ConsumptionQueryService;
import cn.lyy.merchant.service.remote.ConsumptionService;
import cn.lyy.merchant.util.DateUtils;
import cn.lyy.merchant.utils.UserMemberUtils;
import cn.lyy.open.order.api.OrderServiceInterface;
import cn.lyy.open.order.api.v2.OrderClient;
import cn.lyy.open.order.api.v2.RefundOrderClient;
import cn.lyy.open.order.constans.OrderEnum.OrderPayTypeEnum;
import cn.lyy.open.order.constans.ServiceSourceEnum;
import cn.lyy.open.order.dto.request.OrderDTO;
import cn.lyy.open.order.dto.request.OrderDTO.CommonExtend;
import cn.lyy.open.order.dto.request.v2.OrderGoodsDTO;
import cn.lyy.open.order.dto.request.v2.OrderRefundRequest;
import cn.lyy.open.order.dto.request.v2.OrderRefundRequest.RefundOrderGoodsRequest;
import cn.lyy.open.order.dto.request.v2.query.OrderDetailQueryRequest;
import cn.lyy.open.order.dto.response.v2.OrderQueryResponse;
import cn.lyy.open.order.dto.response.v2.OrderRefundResponse;
import cn.lyy.open.order.dto.response.v2.StoredRefundDetail;
import cn.lyy.open.payment.constant.PaymentEnum;
import cn.lyy.user.api.service.DistributorUserService;
import cn.lyy.user.api.service.IGroupUserService;
import cn.lyy.user.api.service.IUserService;
import cn.lyy.user.dto.balance.UserBalanceInfoDTO;
import cn.lyy.user.dto.user.UserDTO;
import cn.lyy.user_statistics_api.UserStatisticsService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitAdjustDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountQueryDTO;
import com.lyy.user.account.infrastructure.account.feign.AccountFeignClient;
import com.lyy.user.account.infrastructure.constant.AccountRecordTypeEnum;
import com.lyy.user.account.infrastructure.constant.AdjustTypeEnum;
import com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum;
import com.lyy.user.account.infrastructure.constant.BenefitClassifyGroupEnum;
import com.lyy.user.account.infrastructure.constant.TimeScopeEnum;
import com.lyy.user.account.infrastructure.resp.RespBody;
import com.lyy.user.account.infrastructure.statistics.dto.MerchantUserStatisticsListDTO;
import com.lyy.user.account.infrastructure.statistics.dto.StatisticsUserQueryDTO;
import com.lyy.user.account.infrastructure.statistics.feign.StatisticsFeignClient;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <p>Title:fork</p>
 * <p>Desc: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020/11/23
 */
@Service
@Slf4j
public class MemberServiceImpl implements MemberService {

    @Autowired
    private OrderServiceInterface orderServiceInterface;
    @Autowired
    private MerchantMemberAppealService merchantMemberAppealService;
    @Autowired
    private UserStatisticsService userStatisticsService;
    @Autowired
    private ConsumptionQueryService consumptionQueryService;
    @Autowired
    private ConsumptionService consumptionService;
    @Autowired
    private IGroupUserService groupUserService;
    @Autowired
    private DistributorUserService distributorUserService;
    @Autowired
    private IUserService userService;
    @Autowired
    private IEquipmentTypeService equipmentTypeService;

    @Autowired
    private UserMemberUtils userMemberUtils;

    @Autowired
    private AccountFeignClient accountFeignClient;

    @Autowired
    private StatisticsFeignClient statisticsFeignClient;

    @Autowired
    private RefundOrderClient refundOrderClient;
    @Autowired
    private OrderClient orderClient;
    @Override
    public Pagination<MemberInfoDTO> memberList(MemberInfoQueryDTO param) {
        Pagination<MemberInfoDTO> result = getUserBalancePage(param.getTelephone(), param.getLyyUserId(), param.getPageIndex(), param.getPageSize(), param.getUserOrgId());
        return result;
    }

    @Override
    public MemberInfoDTO memberDetail(MemberInfoQueryDTO memberInfoRequestDTO) {
        MemberInfoDTO memberInfoDTO;
        Pagination<MemberInfoDTO> userBalancePage = getUserBalancePage(memberInfoRequestDTO.getTelephone(), memberInfoRequestDTO.getLyyUserId(), 1, 1, memberInfoRequestDTO.getUserOrgId());
        if(CollectionUtils.isEmpty(userBalancePage.getItems())){
            throw new BusinessException(BusinessExceptionEnums.MEMBER_NOT_EXIST);
        }
        memberInfoDTO = userBalancePage.getItems().get(0);

        //用户余额查询
        Map<Integer, BigDecimal> balanceMap = userMemberUtils.getUserBenefitCount(memberInfoRequestDTO.getUserOrgId(), null, memberInfoRequestDTO.getLyyUserId(), 2, Collections.singletonList(BenefitClassifyEnum.ADVERT_RED_PACKET.getCode()), null);
        AtomicReference<BigDecimal> allValue = new AtomicReference<>(BigDecimal.ZERO);
        if (MapUtil.isNotEmpty(balanceMap)) {
            balanceMap.forEach((k, v) -> {
                allValue.getAndSet(allValue.get().add(ofNullable(v).orElse(BigDecimal.ZERO)));
            });
        }
        memberInfoDTO.setBalance(allValue.get());

//        cn.lyy.base.communal.bean.BaseResponse<UserBalanceInfoDTO> userBalanceInfoDTOBaseResponse = groupUserService.balanceInfo(memberInfoRequestDTO.getUserOrgId(), memberInfoRequestDTO.getLyyUserId());
//        ofNullable(userBalanceInfoDTOBaseResponse).ifPresent(response -> {
//            ofNullable(response.getData()).ifPresent(data -> {
//                memberInfoDTO.setBalance(data.getAmountSum());
//            });
//        });

        memberInfoDTO.setConsumeAmount(Objects.isNull(memberInfoDTO.getConsumeAmount()) ? 0L : memberInfoDTO.getConsumeAmount());
        //获取赠送红包金额数以及历史
        cn.lyy.user_statistics_api.response.BaseResponse totalRedAmountResponse = consumptionQueryService.getRedAmountTotalHistory(memberInfoRequestDTO.getUserOrgId(), memberInfoRequestDTO.getLyyUserId());
        if(totalRedAmountResponse != null && numberOfObject(totalRedAmountResponse.getData()).compareTo(BigDecimal.ZERO) > 0){
            memberInfoDTO.setTotalRedAmount(numberOfObject(totalRedAmountResponse.getData()));
        }else{
            memberInfoDTO.setTotalRedAmount(BigDecimal.ZERO);
        }
        return memberInfoDTO;
    }

    @Override
    public int giftRedPacket(MemberGiftRedPacketSettingDTO settingDTO) {
        if(settingDTO.getRedAmount().compareTo(BigDecimal.ZERO)<=0){
            throw new BusinessException("-1", "赠送红包必须大于0");
        }
        Long userOrgId = settingDTO.getUserOrgId();
        Long lyyUserId = settingDTO.getLyyUserId();
        BigDecimal redAmount = settingDTO.getRedAmount();
        //增加记录
        LyyGrantCoinsDTO grantCoinsDTO = new LyyGrantCoinsDTO();
        grantCoinsDTO.setAdUserId(userOrgId);
        grantCoinsDTO.setLyyDistributorId(userOrgId.intValue());
        grantCoinsDTO.setLyyUserId(lyyUserId);
        grantCoinsDTO.setCoins(BigDecimal.ZERO);
        grantCoinsDTO.setDescription("赠送红包");
        grantCoinsDTO.setIsread("N");
        grantCoinsDTO.setAmount(redAmount);
        grantCoinsDTO.setType(1);
        consumptionService.saveGrantCoins(grantCoinsDTO);
        userMemberUtils.handlerUserBenefit(userOrgId, lyyUserId, settingDTO.getUserId(),
                AdjustTypeEnum.INCREMENT, BenefitClassifyEnum.RED_BALANCE,
                AccountRecordTypeEnum.MERCHANT_ADJUST_RED_BALANCE, "赠送红包", redAmount);
        return 0;
    }

    @Override
    public Pagination<GrantCoinsDTO> giftRedPacketRecordList(MemberInfoQueryDTO param) {
        LyyGrantCoinsPara para = new LyyGrantCoinsPara();
        para.setPageIndex(param.getPageIndex());
        para.setPageSize(param.getPageSize());
        para.setLyyUserId(param.getLyyUserId());
        para.setLyyDistributorId(param.getUserOrgId().intValue());
        para.setParentAdOrg(true);
        Pagination<GrantCoinsDTO> pagination =  consumptionQueryService.getByLyyUserIdAndDistributorId(para);
        return pagination;
    }

    @Override
    public int clearBalance(MemberInfoQueryDTO memberInfoRequestDTO) {
        UserBalanceInfoDTO userBalanceInfoDTO = new UserBalanceInfoDTO();
        userBalanceInfoDTO.setDistributorId(memberInfoRequestDTO.getUserOrgId());
        userBalanceInfoDTO.setUserId(memberInfoRequestDTO.getLyyUserId());
        userBalanceInfoDTO.setOperatorId(memberInfoRequestDTO.getUserId());

        List<AccountBenefitAdjustDTO> adjustDTOList = new ArrayList<>();
        //清空余额
        AccountQueryDTO param = new AccountQueryDTO();
        param.setMerchantId(memberInfoRequestDTO.getUserOrgId());
        param.setUserId(memberInfoRequestDTO.getLyyUserId());
        param.setBenefitClassify(BenefitClassifyGroupEnum.getClassifyByType(2));
        // 去除广告红包
        param.setExcludeClassify(Arrays.asList(BenefitClassifyEnum.ADVERT_RED_PACKET.getCode(),BenefitClassifyEnum.DELAYED_SETTELEMENT_BALANCE.getCode(),BenefitClassifyEnum.DELAYED_SETTELEMENT_COIN.getCode()));
        Map<Integer, BigDecimal> balanceMap = userMemberUtils.getUserBenefitCount(memberInfoRequestDTO.getUserOrgId(), null, memberInfoRequestDTO.getLyyUserId(), 2, Collections.singletonList(BenefitClassifyEnum.ADVERT_RED_PACKET.getCode()), null);
        AtomicReference<BigDecimal> groupAmount = new AtomicReference<>(BigDecimal.ZERO);
        AtomicReference<BigDecimal> redAmount = new AtomicReference<>(BigDecimal.ZERO);
        AtomicReference<BigDecimal> grantAmount = new AtomicReference<>(BigDecimal.ZERO);
        if (MapUtil.isNotEmpty(balanceMap)) {
            balanceMap.forEach((k, v) -> {
                if (BenefitClassifyEnum.MERCHANT_PAYOUT_BALANCE.getCode().equals(k)
                        || BenefitClassifyEnum.THIRD_PLATFORM_AMOUNT.getCode().equals(k)) {
                    grantAmount.getAndSet(grantAmount.get().add(ofNullable(v).orElse(BigDecimal.ZERO)));
                }
                if (BenefitClassifyEnum.RED_BALANCE.getCode().equals(k)) {
                    redAmount.getAndSet(redAmount.get().add(ofNullable(v).orElse(BigDecimal.ZERO)));
                }
                if(BenefitClassifyEnum.USER_RECHARGE_BALANCE.getCode().equals(k)){
                    groupAmount.getAndSet(groupAmount.get().add(ofNullable(v).orElse(BigDecimal.ZERO)));
                }
            });
        }
        if(groupAmount.get().compareTo(BigDecimal.ZERO) > 0){
            AccountBenefitAdjustDTO adjustDTO = new AccountBenefitAdjustDTO();
            adjustDTO.setMerchantId(memberInfoRequestDTO.getUserOrgId());
            adjustDTO.setUserId(memberInfoRequestDTO.getLyyUserId());
            adjustDTO.setOperator(memberInfoRequestDTO.getUserId());
            adjustDTO.setAdjustType(AdjustTypeEnum.DECREMENT);
            adjustDTO.setClassify(BenefitClassifyEnum.USER_RECHARGE_BALANCE.getCode());
            adjustDTO.setRecordType(AccountRecordTypeEnum.MERCHANT_ADJUST_BALANCE.getCode());
            adjustDTOList.add(adjustDTO);
        }

        //如果还有红包，同样变为负数，清除
        if(redAmount.get().compareTo(BigDecimal.ZERO) > 0){
            AccountBenefitAdjustDTO adjustDTO = new AccountBenefitAdjustDTO();
            adjustDTO.setMerchantId(memberInfoRequestDTO.getUserOrgId());
            adjustDTO.setUserId(memberInfoRequestDTO.getLyyUserId());
            adjustDTO.setOperator(memberInfoRequestDTO.getUserId());
            adjustDTO.setAdjustType(AdjustTypeEnum.DECREMENT);
            adjustDTO.setClassify(BenefitClassifyEnum.RED_BALANCE.getCode());
            adjustDTO.setRecordType(AccountRecordTypeEnum.MERCHANT_ADJUST_RED_BALANCE.getCode());
            adjustDTOList.add(adjustDTO);
        }

        //如果有派发金额，同样变为负数，清除
        if(grantAmount.get().compareTo(BigDecimal.ZERO) > 0){
            AccountBenefitAdjustDTO adjustDTO = new AccountBenefitAdjustDTO();
            adjustDTO.setMerchantId(memberInfoRequestDTO.getUserOrgId());
            adjustDTO.setUserId(memberInfoRequestDTO.getLyyUserId());
            adjustDTO.setOperator(memberInfoRequestDTO.getUserId());
            adjustDTO.setAdjustType(AdjustTypeEnum.DECREMENT);
            adjustDTO.setClassify(BenefitClassifyEnum.MERCHANT_PAYOUT_BALANCE.getCode());
            adjustDTO.setRecordType(AccountRecordTypeEnum.MERCHANT_ADJUST_PAYOUT_BALANCE.getCode());
            adjustDTOList.add(adjustDTO);
        }
        if (!CollectionUtils.isEmpty(adjustDTOList)) {
            RespBody<Void> respBody = accountFeignClient.merchantClearBenefit(adjustDTOList);
            log.info("新会员清空用户余额,请求:{},返回结果:{}", adjustDTOList, respBody);
            if (respBody.getCode().equals(GlobalErrorCode.OK.getCode())) {
                return ResponseCodeEnum.SUCCESS.getCode();
            } else {
                return ResponseCodeEnum.FAIL.getCode();
            }
        }
        return ResponseCodeEnum.SUCCESS.getCode();
    }


    @Override
    public  BaseResponse<Integer> appealCount(UserInfoDTO userInfoDTO) {
        return merchantMemberAppealService.appealCount(userInfoDTO);
    }

    @Override
    public void handleAppeal(AppealHandleParam param) {

        if (org.apache.commons.lang3.StringUtils.isNotBlank(param.getRemarksInfo())) {
            if (param.getRemarksInfo().length() > 20) {
                throw new BusinessException("备注信息字段长度不可超过20个字符");
            }
        }
        // 申诉记录校验
        BaseResponse<AppealHandleResult> resp = merchantMemberAppealService.handleAppeal(param);
        if (!Objects.equals(resp.getCode(), ResponseCodeEnum.SUCCESS.getCode())) {
            throw new BusinessException(resp.getMessage());
        }
        AppealHandleResult result = resp.getData();
        // 退款流程
        if (param.getHandleResult() == 1) {
            OrderDetailQueryRequest request = new OrderDetailQueryRequest();
            request.setOutTradeNo(result.getOutTradeNo());
            request.setQueryGoods(true);
            BaseResponse<OrderQueryResponse> response = orderClient.detail(request);
            OrderQueryResponse order = getData(response, null);
            OrderRefundRequest refundRequest = assembleRefundOrder(order, param.getRefundAmount());
            log.info("V2申诉退款, 请求参数:{}", refundRequest);
            BaseResponse<OrderRefundResponse> refund = refundOrderClient.refund(refundRequest);
            if (!(ResponseCodeEnum.SUCCESS.getCode() == refund.getCode())) {
                log.error("V2申诉退款失败: {}", refund);
                throw new BusinessException("退款失败");
            }
            log.info("V2申诉完成, 响应参数:{}", refund);
        }
    }
    private OrderRefundRequest assembleRefundOrder(OrderQueryResponse order, BigDecimal refundAmount) {
        OrderRefundRequest orderRefundRequest = new OrderRefundRequest();
        orderRefundRequest.setDistributorId(order.getDistributorId());
        orderRefundRequest.setOutTradeNo(order.getOutTradeNo());
        orderRefundRequest.setRemark(PaymentEnum.RefundTypeEnum.REFUND_TYPE_APPEAL.getDesc());
        orderRefundRequest.setRefundType(PaymentEnum.RefundTypeEnum.REFUND_TYPE_APPEAL.getCode());
        orderRefundRequest.setSource(ServiceSourceEnum.MERCHANT.getCode());

        if (ObjectUtils.compare(order.getActualAmount(), BigDecimal.ZERO)>0) {
            orderRefundRequest.setStoredRefundDetailList(assembleDetail(refundAmount, true));
        } else {
            List<OrderGoodsDTO> goodsList = ofNullable(order.getOrderGoodsList())
                    .orElse(Lists.newArrayList());
            if (isMemberTimesCard(order.getPayDetailList())) {
                // 次卡启动申诉退款视为全额退款
                log.debug("次卡启动申诉退款: {}", order.getPayDetailList());
                orderRefundRequest.setStoredRefundDetailList(generateStoredRefund(order.getPayDetailList()));
                List<RefundOrderGoodsRequest> refundGoodsList = goodsList.stream()
                        .map(v -> {
                            RefundOrderGoodsRequest refundGoods = new RefundOrderGoodsRequest();
                            refundGoods.setOrderGoodsId(v.getId());
                            refundGoods.setRefundQuantity(v.getQuantity());
                            refundGoods.setRefundAmount(v.getActualAmount().setScale(2, RoundingMode.HALF_UP));
                            return refundGoods;
                        })
                        .collect(Collectors.toList());
                orderRefundRequest.setRefundOrderGoodsList(refundGoodsList);
            } else {
                log.debug("余额启动申诉退款: {}", order.getPayDetailList());
                orderRefundRequest.setStoredRefundDetailList(assembleDetail(refundAmount));
                List<RefundOrderGoodsRequest> refundGoodsList = generateRefundGoods(refundAmount, goodsList);
                orderRefundRequest.setRefundOrderGoodsList(refundGoodsList);
            }

        }
        return orderRefundRequest;
    }

    static List<RefundOrderGoodsRequest> generateRefundGoods(BigDecimal refundAmount, List<OrderGoodsDTO> goodsList) {
        List<RefundOrderGoodsRequest> refundGoodsList = Lists.newArrayList();
        BigDecimal remainAmount = refundAmount;
        for (OrderGoodsDTO v : goodsList) {
            RefundOrderGoodsRequest refundGoods = new RefundOrderGoodsRequest();
            refundGoods.setOrderGoodsId(v.getId());
            refundGoods.setRefundQuantity(v.getQuantity());
            if (remainAmount.compareTo(v.getActualAmount())<=0) {
                refundGoods.setRefundAmount(remainAmount.setScale(2, RoundingMode.HALF_UP));
                refundGoodsList.add(refundGoods);
                break;
            } else {
                remainAmount = refundAmount.subtract(v.getActualAmount());
                refundGoods.setRefundAmount(v.getActualAmount().setScale(2, RoundingMode.HALF_UP));
                refundGoodsList.add(refundGoods);
            }
        }
        return refundGoodsList;
    }

    public static boolean isMemberTimesCard(List<OrderDTO.PayDetail> payDetails) {
        return ofNullable(payDetails)
                .orElse(Lists.newArrayList())
                .stream()
                .anyMatch(md -> Objects.equals(md.getPayType(), OrderPayTypeEnum.MEMBER_CARD_TIMES.getCode()));
    }

    private static List<StoredRefundDetail> assembleDetail(BigDecimal refundAmount) {
        return assembleDetail(refundAmount, false);
    }

    private static List<StoredRefundDetail> assembleDetail(BigDecimal refundAmount, boolean channelRefundStore) {
        // 构造支付退余额
        List<StoredRefundDetail> details = Lists.newArrayList();
        StoredRefundDetail channelRefundStored = new StoredRefundDetail();
        channelRefundStored.setPayAmount(refundAmount.setScale(2, RoundingMode.HALF_UP));
        channelRefundStored.setPayType(OrderPayTypeEnum.BALANCED_PAY.getCode());
        channelRefundStored.setChannelRefundStore(channelRefundStore);
        log.info("V2退储值明细对象构建:{}", channelRefundStored);
        details.add(channelRefundStored);
        return details;
    }

    @Override
    public Pagination<AppealRecordDTO> appealList(Integer status, Long adUserId, Long adOrgId, Integer pageIndex, Integer pageSize) {
        Pagination<AppealRecordDTO> list = merchantMemberAppealService.appealList(status, adUserId, adOrgId, pageIndex, pageSize).getData();

        if (!CollectionUtils.isEmpty(list.getItems())) {
            list.getItems().parallelStream().forEach(this::convertAppealData);
        }

        return list;
    }

    @Override
    public AppealRecordDTO appealDetail(Long appealRecordId) {
        AppealRecordDTO record = merchantMemberAppealService.appealDetail(appealRecordId).getData();
        convertAppealData(record);
        return record;
    }

    private void convertAppealData(AppealRecordDTO record) {
        if (record == null) {
            return;
        }
        if (record.getEquipmentTypeId() != null) {
            EquipmentTypeDTO type = equipmentTypeService.getByKey(record.getEquipmentTypeId()).getData();
            record.setEquipmentTypeValue(ofNullable(type).map(EquipmentTypeDTO::getValue).orElse(null));
        }
        if (record.getLyyUserId() != null) {
            UserDTO user = userService.get(record.getLyyUserId()).getData();
            if (user == null) {
                return;
            }
            String userType = ofNullable(user.getUserType()).orElse("W");
            record.setPayChannel("W".equals(userType) ? "微信" : "支付宝");
            MemberInfoDTO info = new MemberInfoDTO();
            info.setName(user.getName());
            info.setGender(user.getGender());
            info.setHeadImg(user.getHeadImg());
            info.setUserType(user.getUserType());
            record.setMemberInfoDTO(info);
        }
    }

        /**
         * 获取会员余额列表
         * @param telephone
         * @param queryUserId
         * @param pageIndex
         * @param pageSize
         * @param lyyDistributorId
         * @return
         */
    private Pagination<MemberInfoDTO> getUserBalancePage(String telephone , Long queryUserId , Integer pageIndex , Integer pageSize , Long lyyDistributorId){
        Pagination<MemberInfoDTO> pagination = new Pagination();

        StatisticsUserQueryDTO statisticsUserQueryDTO = new StatisticsUserQueryDTO();
        statisticsUserQueryDTO.setMerchantId(lyyDistributorId);
        statisticsUserQueryDTO.setPageIndex(pageIndex);
        statisticsUserQueryDTO.setPageSize(pageSize);
        statisticsUserQueryDTO.setTimeScope(TimeScopeEnum.ALL);
        if(StringUtils.isNotBlank(telephone)) {
            statisticsUserQueryDTO.setTelephone(telephone);
        }
        if(queryUserId != null) {
            statisticsUserQueryDTO.setUserId(queryUserId);
        }
        Page<MerchantUserStatisticsListDTO> result = ofNullable(statisticsFeignClient.queryStatisticsUserList(statisticsUserQueryDTO))
                .filter(r -> GlobalErrorCode.OK.getCode().equals(r.getCode())).map(RespBody::getBody)
                .orElse(null);

//        UserAllStatisticsQueryDTO queryDTO = new UserAllStatisticsQueryDTO();
//        if (StringUtils.isNotBlank(telephone)) {
//            queryDTO.setTelephone(telephone);
//        }
//        if (queryUserId != null) {
//            queryDTO.setLyyUserId(queryUserId);
//        }
//        queryDTO.setLyyDistributorId(lyyDistributorId.intValue());
//        queryDTO.setPageIndex(pageIndex);
//        queryDTO.setPageSize(pageSize);
//        // 排序依据，1：总充值，2：余币，3：余额，4：消费时间
//        queryDTO.setSortField("1");
//        log.debug("获取会员列表参数：{}" , queryDTO);
//        cn.lyy.user_statistics_api.response.BaseResponse<Pagination> result = userStatisticsService.getUserStatisticsPage(queryDTO);

        if(result instanceof Page) {
            List<MemberInfoDTO> list = Lists.newArrayList();
            Page<MerchantUserStatisticsListDTO> data = result;
            data.getRecords().stream().forEach(member -> {
                MemberInfoDTO user = new MemberInfoDTO();
                user.setLyyUserId(member.getUserId());
                user.setTelephone(member.getTelephone());
                user.setHeadImg(member.getHeadImg());
                user.setGender(member.getGender());
                user.setUserType(member.getUserType());
                if (StringUtils.isEmpty(member.getNickName())) {
                    user.setName("A".equals(user.getUserType()) ? "支付宝用户" : "微信用户");
                } else {
                    user.setName(member.getNickName());
                }
                user.setTelephone(member.getTelephone());
                user.setHeadImg(member.getHeadImg());
                user.setGender(member.getGender());
                user.setBalanceAmount(member.getBalanceAmount().floatValue());
                user.setBalanceCoins(member.getBalanceCoins().floatValue());
                BigDecimal totalRecharge = member.getTotalRechargeMoney().add(member.getTotalPayServiceMoney());
                user.setTotalRecharge(totalRecharge.floatValue());
                list.add(user);
            });

            pagination.setItems(list);
            pagination.setMaxPage((int)data.getPages());
            pagination.setPage((int)data.getCurrent());
            pagination.setPageSize(pageSize);
            pagination.setTimes(0);
            pagination.setTotal((int)data.getTotal());
            return pagination;
        }
        pagination.setItems(Collections.emptyList());
        return pagination;
    }

    /**
     * 获取会员充值列表
     * @param telephone
     * @param queryUserId
     * @param pageIndex
     * @param pageSize
     * @param lyyDistributorId
     * @return
     */
    private Pagination<MemberInfoDTO> getUserRechargePage(String telephone , Long queryUserId , Integer pageIndex , Integer pageSize , Long lyyDistributorId){
        Pagination<MemberInfoDTO> pagination = new Pagination();

        Map<String ,Object> param = new HashMap<>();
        if (StringUtils.isNotBlank(telephone)) {
            param.put("telephone", telephone);
        }
        if (queryUserId != null) {
            param.put("lyyUserId", queryUserId);
        }

//        param.put("authorized" , "Y");
        param.put("pageIndex" , pageIndex);
        param.put("pageSize" , pageSize);
        String startDate = DateUtils.dateFormat(DateUtils.nowDate().plusDays(-30) , DateUtils.DatePattern.yyyy_MM_dd);
        String endDate = DateUtils.dateFormat(DateUtils.nowDate().plusDays(1) , DateUtils.DatePattern.yyyy_MM_dd);
        param.put("startDate" , startDate);
        param.put("endDate" , endDate);
        param.put("lyyDistributorId" , lyyDistributorId);
        log.debug("获取会员列表参数：{}" , param);
        cn.lyy.user_statistics_api.response.BaseResponse<Pagination> result = userStatisticsService.getUserRechargePage(param);

        if(result.getData() instanceof Pagination) {
            List<MemberInfoDTO> list = Lists.newArrayList();
            Pagination data = result.getData();
            data.getItems().stream().forEach(map -> {
                JSONObject item = (JSONObject) JSON.toJSON(map);
                MemberInfoDTO user = item.toJavaObject(MemberInfoDTO.class);
                list.add(user);
            });
            pagination.setItems(list);
            pagination.setMaxPage(data.getMaxPage());
            pagination.setPage(data.getPage());
            pagination.setPageSize(data.getPageSize());
            pagination.setTimes(data.getTimes());
            pagination.setTotal(data.getTotal());
            return pagination;
        }
        pagination.setItems(Collections.emptyList());
        return pagination;
    }

    private BigDecimal numberOfObject(Object obj){
        if(obj == null){
            return BigDecimal.ZERO;
        }
        try {
            return new BigDecimal(String.valueOf(obj)).setScale(2, RoundingMode.HALF_DOWN);
        } catch (Exception e) {
            return BigDecimal.ZERO;
        }
    }
}
