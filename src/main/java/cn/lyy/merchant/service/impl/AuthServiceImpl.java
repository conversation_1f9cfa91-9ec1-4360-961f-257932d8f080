package cn.lyy.merchant.service.impl;

import cn.lyy.auth.api.fegin.AuthorizationServiceInterface;
import cn.lyy.auth.request.AuthTokenRequest;
import cn.lyy.auth.response.AuthTokenResponse;
import cn.lyy.authority_service_api.AdRoleDTO;
import cn.lyy.authority_service_api.UserBatchSaveDTO;
import cn.lyy.authority_service_api.merchant.AdRoleSaveParam;
import cn.lyy.authority_service_api.merchant.EditableResourcesDTO;
import cn.lyy.authority_service_api.miscroservice.AuthorityService;
import cn.lyy.base.dto.JsonObject;
import cn.lyy.base.dto.Status;
import cn.lyy.base.util.MD5Util;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.base.utils.exception.BizException;
import cn.lyy.merchant.config.DefaultRolesConfig;
import cn.lyy.merchant.constants.JwtTimeConstants;
import cn.lyy.merchant.constants.SystemConstants;
import cn.lyy.merchant.dto.auth.AuthUserDTO;
import cn.lyy.merchant.dto.auth.RoleDTO;
import cn.lyy.merchant.service.AuthService;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>Title:saas2</p>
 * <p>Desc: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020/11/16
 */
@Service
@Slf4j
public class AuthServiceImpl implements AuthService {

    @Autowired
    private AuthorityService authorityService;
    @Autowired
    private DefaultRolesConfig defaultRolesConfig;
    @Autowired
    private AuthorizationServiceInterface authorizationServiceInterface;

    /**
     * 默认通过鉴权功能
     */
    private static final String SAASMERCHANT_PASS_AUTH = "SaasMerchant-PassAuth";

    @Override
    public int createDefaultRole(Long authorityUserId, Long userId, Long userOrgId) {
        List<AdRoleDTO> childAccountsRoles = null;
        try {
            childAccountsRoles = authorityService.getAllRolesBelongTo(SystemConstants.MERCHANT_BACKEND_SYSTEM_ID, authorityUserId);
        }catch (Exception e){
            log.warn("get roles error: " , e);
        }

        if(childAccountsRoles == null) return -1;

        List<String> existRole = childAccountsRoles.stream().map(role -> role.getName()).collect(Collectors.toList());

        //如果有默认角色没有添加的话
        Map<String,List<String>> defaultRoleAuthMap = getDefaultRoles();
        if(defaultRoleAuthMap.keySet().stream()
                .filter(roleKey -> !existRole.contains(roleKey))
                .count() > 0
        ) {
            //所有权限列表
            List<EditableResourcesDTO> authList = authorityService.getEditableResource(SystemConstants.MERCHANT_BACKEND_SYSTEM_ID);

            defaultRoleAuthMap.keySet().stream()
                    .filter(roleKey -> !existRole.contains(roleKey))  //如果没该角色就创建
                    .forEach(item -> {
                        List<Long> resIds = new ArrayList<>();
                        RoleDTO role = new RoleDTO();
                        role.setRoleName(item);
                        for (int i = 0; i < authList.size(); i++) {
                            resIds.addAll(authList.get(i).getChilds().stream().filter(c -> defaultRoleAuthMap.get(item).contains(c.getValue()))
                                    .map(r -> r.getAdResourcesId())
                                    .collect(Collectors.toList()));
                        }
                        log.debug("role[{}] resources[{}]", item, resIds);
                        role.setResourceIds(resIds);
                        role.setUserId(userId);
                        role.setUserOrgId(userOrgId);
                        role.setAuthorityUserId(authorityUserId);

                        log.debug("save subaccount role : {}", JSON.toJSONString(role));
                        saveOrUpdateRole(role);
                    });
        }
        return 0;
    }

    @Override
    public JsonObject saveOrUpdateRole(RoleDTO roleDTO) {
        EditableResourcesDTO commonCatalog = authorityService.getEditableResource(SystemConstants.MERCHANT_BACKEND_SYSTEM_ID).stream()
                .filter(item -> "SaasMerchant-Default".equalsIgnoreCase(item.getValue()))
                .findFirst().orElse(null);
        //.orElseThrow(() -> new SaasException(500 , "找不到默认权限"));



        AdRoleSaveParam roleSaveParam = new AdRoleSaveParam();
        roleSaveParam.setAdResourceIds(roleDTO.getResourceIds() == null ? new ArrayList<>() : roleDTO.getResourceIds());
        //自动添加
        List<EditableResourcesDTO> autoPassAuth = this.getAutoPassAuth();
        if (!CollectionUtils.isEmpty(autoPassAuth)) {
            List<Long> resourceIds = autoPassAuth.stream().map(EditableResourcesDTO::getAdResourcesId).collect(Collectors.toList());
            roleSaveParam.getAdResourceIds().addAll(resourceIds);
        }
        Optional.ofNullable(commonCatalog).ifPresent(item -> {
            if(item.getChilds() != null){
                item.getChilds().stream().forEach(r -> {
                    if(!roleSaveParam.getAdResourceIds().contains(r.getAdResourcesId()))
                        roleSaveParam.getAdResourceIds().add(r.getAdResourcesId());
                });
            }
        });

        roleSaveParam.setAdRoleId(roleDTO.getRoleId());
        roleSaveParam.setAdSystemId(SystemConstants.MERCHANT_BACKEND_SYSTEM_ID);
        roleSaveParam.setAdUserId(roleDTO.getAuthorityUserId());
        roleSaveParam.setName(roleDTO.getRoleName());
        roleSaveParam.setValue("");
       return authorityService.saveOrUpdateRole(roleSaveParam);
    }

    /**
     * 获取默认鉴权通过的资源
     * @return
     */
    @Override
    public List<EditableResourcesDTO> getAutoPassAuth() {
        List<EditableResourcesDTO> newResultList = Lists.newArrayList();
        List<EditableResourcesDTO> autoAuth = authorityService.getEditableResource(SystemConstants.MERCHANT_BACKEND_SYSTEM_ID)
                .stream()
                .filter(item -> SAASMERCHANT_PASS_AUTH.equalsIgnoreCase(item.getValue()))
                .collect(Collectors.toList());
        if (autoAuth == null) {
            return newResultList;
        }

        autoAuth.stream() .forEach(item ->  {
            List<EditableResourcesDTO> newChilds = item.getChilds();
            if(CollectionUtils.isEmpty(newChilds)) {
                return;
            }
            newResultList.addAll(newChilds);
        });
        return newResultList;
    }

    @Override
    public Map<String, List<String>> getDefaultRoles() {
        Map<String,List<String>> defaultRoleAuthMap = Maps.newHashMap();
        List<DefaultRolesConfig.Role> roles = defaultRolesConfig.getRoles();
        if(CollectionUtils.isNotEmpty(roles)) {
            defaultRoleAuthMap = roles.stream().collect(Collectors.toMap(k -> k.getName(), v -> {
                String authorities = v.getAuthorities();
                authorities = authorities.replaceAll(" ", "");
                return Arrays.asList(authorities.split(","));
            }));
        }
        return defaultRoleAuthMap;
    }

    @Override
    public AuthUserDTO loadAuthorityUser(String phone, long systemId) {
        JsonObject result = requestAuthorityUser(phone, systemId);
        log.debug("用户:[{}] 登录，权限库返回用户信息：{}", phone, result);
        AuthUserDTO userDTO = null;
        if(Objects.nonNull(result) && result.getResult() == Status.STATUS_SUCCESS) {
            userDTO = JSON.parseObject((String) result.getData(), AuthUserDTO.class);
        }
        return userDTO;
    }


    @Override
    public JsonObject addAuthorityUser(cn.lyy.merchant.dto.user.AdUserDTO adUserDTO, long systemVersion1, long systemVersion2) {
        List<UserBatchSaveDTO> dtos = Lists.newArrayList();
        if(SystemConstants.MERCHANT_BACKEND_SYSTEM_ID_VERSION_1 == systemVersion1) {
            dtos.add(mergeAuthorityUser(adUserDTO, SystemConstants.MERCHANT_BACKEND_SYSTEM_ID_VERSION_1, Lists.newArrayList(SystemConstants.MERCHANT_BACKEND_ROLE_ID_VERSION_1)));
        }
        if(SystemConstants.MERCHANT_BACKEND_SYSTEM_ID == systemVersion2) {
            dtos.add(mergeAuthorityUser(adUserDTO, SystemConstants.MERCHANT_BACKEND_SYSTEM_ID, Lists.newArrayList(SystemConstants.MERCHANT_BACKEND_ROLE_ID)));
        }
        JsonObject result = authorityService.batchSave(dtos);
        return result;
    }

    private UserBatchSaveDTO mergeAuthorityUser(cn.lyy.merchant.dto.user.AdUserDTO adUserDTO, long systemId, List<Long> defaultRoleIds) {
        UserBatchSaveDTO userDTOVersion = new UserBatchSaveDTO();
        userDTOVersion.setUsername(adUserDTO.getPhone());
        userDTOVersion.setPassword(MD5Util.MD5(adUserDTO.getPassword()));
        userDTOVersion.setAdSystemId(systemId);
        userDTOVersion.setBelongTo(adUserDTO.getUserOrgId());
        if(CollectionUtils.isEmpty(adUserDTO.getRoleIds())) {
            // 主账号关联默认角色
            userDTOVersion.setRoleIds(defaultRoleIds);
        } else {
            // 子账户关联选择角色，并需要设置createby，用于调用getChildAccountsRole
            userDTOVersion.setRoleIds(adUserDTO.getRoleIds());
            userDTOVersion.setCreatedby(adUserDTO.getAuthorityUserId());
        }
        return userDTOVersion;
    }

    /**
     * 请求获取权限库用户信息
     * @param phone
     * @param systemId
     * @return
     */
    private JsonObject requestAuthorityUser(String phone, long systemId) {
        cn.lyy.authority_service_api.AdUserDTO authorityUser = new cn.lyy.authority_service_api.AdUserDTO();
        authorityUser.setUsername(phone);
        JsonObject result = authorityService.loadUserByUsername(systemId, authorityUser);
        return result;
    }

    /**
     * 生成token
     *
     * @param authTokenRequest
     * @return
     */
    @Override
    public String generateToken(AuthTokenRequest authTokenRequest) {
        //默认B端token存活一天
        authTokenRequest.setTtlSeconds(JwtTimeConstants.ONE_DAY.getSecond());
        AuthTokenResponse response = ResponseUtils.checkResponse(authorizationServiceInterface.generateToken(authTokenRequest));
        if (response == null) {
            throw new BizException("生成token失败,请重试!");
        }
        return response.getToken();
    }
}
