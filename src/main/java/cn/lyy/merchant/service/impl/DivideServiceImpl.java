package cn.lyy.merchant.service.impl;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.merchant.api.service.MerchantAccountService;
import cn.lyy.merchant.dto.account.DivideUserDTO;
import cn.lyy.merchant.dto.common.DivideDTO;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.service.DivideService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * @description:
 * @author: qgw
 * @date on 2021/3/5.
 * @Version: 1.0
 */
@Slf4j
@Service
public class DivideServiceImpl  implements DivideService {

    @Autowired
    private MerchantAccountService merchantAccountService;


    @Override
    public List<DivideUserDTO> getDivideUsers(DivideDTO divideUserDTO) {
        BaseResponse<List<DivideUserDTO>> baseResponse = merchantAccountService.getDivideUsers(divideUserDTO);

        List<DivideUserDTO> divideUserDTOS = Optional.ofNullable(baseResponse).filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode())
                .map(BaseResponse::getData)
                .orElseGet(() -> {
                    log.debug("根据商户获取分成人员信息{}", baseResponse);
                  throw   new BusinessException("查询异常,稍后重试!");
                });
        return divideUserDTOS;
    }
}
