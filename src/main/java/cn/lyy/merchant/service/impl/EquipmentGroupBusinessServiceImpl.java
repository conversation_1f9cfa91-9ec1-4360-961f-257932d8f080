package cn.lyy.merchant.service.impl;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.equipment.dto.equipment.*;
import cn.lyy.equipment.service.EquipmentService;
import cn.lyy.equipment.service.ProtocolMicroService;
import cn.lyy.merchant.api.service.AdOrgClient;
import cn.lyy.merchant.api.service.MerchantGroupService;
import cn.lyy.merchant.constants.BusinessExceptionEnums;
import cn.lyy.merchant.dto.group.GroupVO;
import cn.lyy.merchant.dto.group.SaveGroupDTO;
import cn.lyy.merchant.dto.merchant.request.MerchantGroupRequest;

import cn.lyy.merchant.dto.merchant.response.MerchantGroupDTO;
import cn.lyy.merchant.dto.merchant.response.MerchantGroupEqDTO;
import cn.lyy.merchant.dto.merchant.response.MerchantGroupEquipmentDTO;
import cn.lyy.merchant.dto.response.EquipmentResponseDTO;
import cn.lyy.merchant.dto.response.MerchantEquipmentDTO;
import cn.lyy.merchant.dto.response.MerchantGroupListDTO;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.redis.MerchantRedisClient;
import cn.lyy.merchant.redis.MerchantRedisKeyEnum;
import cn.lyy.merchant.service.IEquipmentGroupBusinessService;
import cn.lyy.tools.equipment.BaseJsonObject;
import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.Optional.ofNullable;

/**
 * @ClassName: IEquipmentGroupBusinessServiceImpl
 * @description: TODO
 * @author: pengkun
 * @create: 2020-11-03 14:53
 * @Version 1.0
 **/
@Slf4j
@Service
public class EquipmentGroupBusinessServiceImpl implements IEquipmentGroupBusinessService {

    @Autowired
    private MerchantGroupService merchantGroupService;

    @Autowired
    private EquipmentService equipmentService;

    @Autowired
    private AdOrgClient adOrgClient;

    @Resource
    private ProtocolMicroService protocolMicroService;

    @Override
    public List<MerchantGroupListDTO> getGroupInfoDistributorId(Long distributorId, Long userId,boolean isShowCount,
                                                                String context, Long labelId) {
        List<MerchantGroupListDTO> groupListDTOList = new ArrayList<>();
        MerchantGroupRequest request = MerchantGroupRequest.builder()
                .adUser(userId)
                .distributor(distributorId)
                .context(context)
                .showGroupLabel(true)
                .isActive(1)
                .labelId(labelId)
                .build();
        List<MerchantGroupDTO> list = ofNullable(merchantGroupService.selectGroup(request).getData()).orElse(null);
        if(list != null && list.size() > 0 ){
            if(isShowCount){
                Set<Long> groupIdSet = list.stream().map(MerchantGroupDTO::getEquipmentGroupId).collect(Collectors.toSet());
                EquipmentNumberRequestDTO requestDTO = new EquipmentNumberRequestDTO();
                requestDTO.setDistributorId(distributorId);
                requestDTO.setGroupIds(new ArrayList<>(groupIdSet));
                //获取场地的设备数量
                List<EquipmentNumberResponseDTO> responseDTOS = equipmentService.getEquipmentNumByGroupIds(requestDTO).getData();
                if(responseDTOS != null && responseDTOS.size() > 0 ){
                    Map<Long, Integer> map = responseDTOS.stream().collect(Collectors.toMap(EquipmentNumberResponseDTO::getGroupId,EquipmentNumberResponseDTO::getCount));
                    for (MerchantGroupDTO dto : list){
                        if(map.containsKey(dto.getEquipmentGroupId())){
                            dto.setEquipmentCount(map.get(dto.getEquipmentGroupId()));
                        }
                    }
                }
            }
            //获取所有的标签
            Set<Long> set = new HashSet<>();
            //没有标签的场地
            List<MerchantGroupDTO> noLabList = new ArrayList<>();
            List<MerchantGroupDTO> hasLabGroups = new ArrayList<>();
            for (MerchantGroupDTO dto : list){
                if(dto.getLabelId() != null){
                    set.add(dto.getLabelId());
                    hasLabGroups.add(dto);
                }else {
                    noLabList.add(dto);
                }
            }
            //数据组装
            if(set.size() > 0){
                for (Long l : set){
                    List<MerchantGroupDTO> groupDTOS = new ArrayList<>();
                    if(hasLabGroups.size() > 0){
                        for (MerchantGroupDTO dto : list){
                            if(dto.getLabelId() != null && l.equals(dto.getLabelId())){
                                groupDTOS.add(dto);
                            }
                        }
                    }
                    if(groupDTOS.size() > 0){
                        MerchantGroupListDTO groupListDTO = new MerchantGroupListDTO();
                        groupListDTO.setLabelId(l);
                        groupListDTO.setLabelName(groupDTOS.get(0).getLabelName());
                        //场地
                        groupListDTO.setGroups(groupDTOS);
                        groupListDTOList.add(groupListDTO);
                    }
                }
            }
            if(noLabList.size() > 0){
                MerchantGroupListDTO groupListDTO = new MerchantGroupListDTO();
                groupListDTO.setLabelId(0L);
                groupListDTO.setLabelName("其他");
                //场地
                groupListDTO.setGroups(noLabList);
                groupListDTOList.add(groupListDTO);
            }
            //标签排序
            groupListDTOList = groupListDTOList.stream().sorted(Comparator.comparingLong(MerchantGroupListDTO::getLabelId).reversed()).collect(Collectors.toList());
        }
        return groupListDTOList;
    }

    /**
     * 获取没有标签的场地列表
     * @param distributorId
     * @param userId
     * @param isShowCont
     * @param typeValue
     * @return
     */
    @Override
    public List<MerchantGroupDTO> getGroupNameAndAddressNoLabel(Long distributorId, Long userId,boolean isShowCont,String typeValue) {
        MerchantGroupRequest request = MerchantGroupRequest.builder()
                .adUser(userId)
                .distributor(distributorId)
                .showGroupLabel(false)
                .isActive(1)
                .build();
        List<MerchantGroupDTO> list = ofNullable(merchantGroupService.selectGroup(request).getData()).orElse(null);
        if(isShowCont){
            Set<Long> groupIdSet = list.stream().map(MerchantGroupDTO::getEquipmentGroupId).collect(Collectors.toSet());
            EquipmentNumberRequestDTO requestDTO = new EquipmentNumberRequestDTO();
            requestDTO.setDistributorId(distributorId);
            requestDTO.setGroupIds(new ArrayList<>(groupIdSet));
            if(StringUtils.isNotBlank(typeValue)){
                requestDTO.setEquipmentTypes(Arrays.asList(typeValue));
            }
            //获取场地的设备数量
            List<EquipmentNumberResponseDTO> responseDTOS = equipmentService.getEquipmentNumByGroupIds(requestDTO).getData();
            if(responseDTOS != null && responseDTOS.size() > 0 ){
                Map<Long, Integer> map = responseDTOS.stream().collect(Collectors.toMap(EquipmentNumberResponseDTO::getGroupId,EquipmentNumberResponseDTO::getCount));
                for (MerchantGroupDTO dto : list){
                    if(map.containsKey(dto.getEquipmentGroupId())){
                        dto.setEquipmentCount(map.get(dto.getEquipmentGroupId()));
                    }
                }
            }
        }
        return list;
    }

    @Override
    public Long saveGroup(SaveGroupDTO param) {
        BaseResponse<Long> resp = merchantGroupService.saveGroup(param);
        if (resp.getCode() != ResponseCodeEnum.SUCCESS.getCode()
                && resp.getMessage() != null) {
            throw new BusinessException(resp.getMessage());
        }
        return resp.getData();
    }

    /**
     * 获取省市区级联信息
     * @return
     */
    @Override
    public List<Map<String, Object>> getAllDistrict() {
        Gson gson = new Gson();
        BaseJsonObject districtData = gson.fromJson(MerchantRedisClient.get(MerchantRedisKeyEnum.COMMON_REGION_ALL), BaseJsonObject.class);
        List<Map<String, Object>> list = null;
        if (districtData == null) {
            list = merchantGroupService.loadDistrictData().getData();
            if(list != null && list.size() > 0){
                districtData = new BaseJsonObject();
                districtData.setPara(list);
                MerchantRedisClient.set(MerchantRedisKeyEnum.COMMON_REGION_ALL, gson.toJson(districtData));
            }
        }else {
            list = (List<Map<String, Object>>) districtData.getPara();
        }
        return list;
    }

    @Override
    public void deleteGroup(Long groupId, Long distributorId, Long userId) {
        Integer count = ofNullable(equipmentService.countByGroup(groupId, distributorId))
                .map(BaseResponse::getData).orElse(1);
        if (count > 0) {
            throw new BusinessException(BusinessExceptionEnums.GROUP_NOT_ALLOW_DELETED);
        }

        boolean hasDelete;
        boolean isPrimary = ofNullable(adOrgClient.checkPrimary(userId)).map(BaseResponse::getData).orElse(false);
        if(isPrimary) {
            hasDelete = true;
        } else {
            //子账号是否有操作当前场地的权限
            hasDelete = ofNullable(merchantGroupService.hasPermissionsOperationGroup(groupId, userId))
                    .map(BaseResponse::getData).orElse(false);
            log.debug("子账号是否有场地的操作权限,groupId:{},userId:{},hasPermission:{}", groupId, userId, hasDelete);
        }
        if(hasDelete) {
            MerchantGroupDTO update = new MerchantGroupDTO();
            update.setEquipmentGroupId(groupId);
            update.setDistributorId(distributorId);
            update.setIsactive("N");
            update.setUpdatedby(userId);
            merchantGroupService.updateGroup(update);
        }
//        merchantGroupService.removeGroupRelation(userId, groupId);
    }

    /**
     * 根据场地id获取场地信息
     * @param groupId
     * @return
     */
    @Override
    public MerchantGroupDTO getGroupInfoById(Long groupId) {
        return merchantGroupService.getGroupById(groupId).getData();
    }

    @Override
    public List<Integer> queryGroupNumber(Long groupId, Long distributorId) {
        return equipmentService.queryGroupNumber(groupId, distributorId).getData();
    }

    @Override
    public List<GroupVO> getGroupByEtype(Long equipmentTypeId, Long distributorId) {
        BaseResponse<List<GroupVO>> result =  merchantGroupService.getGroupByEtype(equipmentTypeId,distributorId);
        Optional.ofNullable(result).filter(r -> r.getCode() == ResponseCodeEnum.SUCCESS.getCode()).orElseThrow(()-> new BusinessException(result.getMessage()));
        return result.getData();
    }

    @Override
    public MerchantEquipmentDTO getGroupEquipmentByTypeProduct(String equipmentType, Long adUserId, Boolean isApprove,Long distributorId, Long productId) {

        MerchantEquipmentDTO merchantEquipmentDTO = new MerchantEquipmentDTO();

        BaseResponse<List<EquipmentDTO>> baseResponse = equipmentService.getEquipmentByTypeMainBoard(equipmentType,adUserId,isApprove,distributorId,productId);
        List<EquipmentDTO> equipmentDTOList =  Optional.ofNullable(baseResponse).filter(r -> r.getCode() == ResponseCodeEnum.SUCCESS.getCode()).map(BaseResponse::getData).orElse(Collections.emptyList());
        Set<Long> groupIdSet =  equipmentDTOList.stream().map(r -> r.getEquipmentGroupId()).collect(Collectors.toSet());
        List<cn.lyy.merchant.dto.MerchantGroupDTO> list = new ArrayList<>();
        groupIdSet.forEach(groupId -> {
            cn.lyy.merchant.dto.MerchantGroupDTO merchantGroupDTO = new cn.lyy.merchant.dto.MerchantGroupDTO();

            BaseResponse<MerchantGroupDTO> groupResponse = merchantGroupService.getGroupById(groupId);
            Optional.ofNullable(groupResponse).filter(r ->r.getCode() == ResponseCodeEnum.SUCCESS.getCode()).map(r -> r.getData()).ifPresent(r ->{
                merchantGroupDTO.setName(r.getName());
                merchantGroupDTO.setAddress(r.getAddress());
            });

            merchantGroupDTO.setEquipmentGroupId(groupId);
            List<EquipmentListResponseDTO> equipments = new ArrayList<>();
            equipmentDTOList.forEach( equipmentDTO -> {
               if(equipmentDTO.getEquipmentGroupId().longValue() == groupId.longValue()){
                   EquipmentListResponseDTO equipmentListResponseDTO = new EquipmentResponseDTO();
                   BeanUtils.copyProperties(equipmentDTO,equipmentListResponseDTO);
                   equipmentListResponseDTO.setTypeName(equipmentDTO.getName());
                   equipments.add(equipmentListResponseDTO);
               }
            });
            merchantGroupDTO.setEquipments(equipments);
            list.add(merchantGroupDTO);
            merchantEquipmentDTO.setGroups(list);
        });
        return merchantEquipmentDTO;
    }
}
