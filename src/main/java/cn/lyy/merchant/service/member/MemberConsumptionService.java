package cn.lyy.merchant.service.member;

import cn.lyy.bigdata.api.dto.BigDataAccountConsumption;
import cn.lyy.bigdata.api.dto.BigDataAccountRecordQueryDTO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.user.account.infrastructure.account.dto.AccountConsumption;
import com.lyy.user.account.infrastructure.account.dto.AccountRecordQueryDTO;

/**
 * @description: 会员体系-消费记录
 * @author: qgw
 * @date on 2021-06-03.
 * @Version: 1.0
 */
public interface MemberConsumptionService {
    /**
     * 查询消费记录
     * @param recordQueryDTO
     * @return
     */
    Page<AccountConsumption> listConsumptionRecord(AccountRecordQueryDTO recordQueryDTO) throws Exception;
    /**
     * 查询消费记录v2
     * @param recordQueryDTO
     * @return
     */
    Page<BigDataAccountConsumption> listConsumptionRecordV2(BigDataAccountRecordQueryDTO recordQueryDTO) throws Exception;
}
