package cn.lyy.merchant.service.member;

import cn.lyy.merchant.dto.member.PayoutWelfareDTO;
import cn.lyy.merchant.dto.request.UserBalanceAdjustReqDTO;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserListDTO;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserQueryDTO;

/**
 * <AUTHOR>
 * @create 2021/4/19 11:29
 */
public interface UserMemberService {

    /**
     * 商户查询用户会员数据
     *
     * @param merchantUserQueryDTO
     * @param currentUser
     * @return
     */
    Page<MerchantUserListDTO> queryUserListByMerchant(MerchantUserQueryDTO merchantUserQueryDTO, AdUserInfoDTO currentUser);

    /**
     * 商家派送福利
     * @param payoutWelfareDTO
     * @param merchantId
     */
    void payoutWelfare(PayoutWelfareDTO payoutWelfareDTO,Long merchantId);

    /**
     * 查询用户会员总数
     * @param merchantUserQueryDTO
     * @return
     */
    Long countUserListByMerchant(MerchantUserQueryDTO merchantUserQueryDTO);

    void adjustUserBenefit(UserBalanceAdjustReqDTO userBalanceAdjustReqDTO, Long merchantId, Long operatorId);
}
