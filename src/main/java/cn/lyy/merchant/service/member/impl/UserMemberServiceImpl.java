package cn.lyy.merchant.service.member.impl;

import static java.util.Optional.ofNullable;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.lyy.bigdata.api.client.MerchantUserClient;
import cn.lyy.lyy_consumption_api.GrantCoinsType;
import cn.lyy.lyy_consumption_api.customer.LyyGrantCoinsDTO;
import cn.lyy.merchant.config.MemberQueryConfig;
import cn.lyy.merchant.constants.BusinessExceptionEnums;
import cn.lyy.merchant.dto.member.BenefitDetailQueryDTO;
import cn.lyy.merchant.dto.member.PayoutWelfareDTO;
import cn.lyy.merchant.dto.merchant.response.MerchantGroupDTO;
import cn.lyy.merchant.dto.request.UserBalanceAdjustReqDTO;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.repository.equipment.StoreRepository;
import cn.lyy.merchant.repository.member.ConsumptionRepository;
import cn.lyy.merchant.service.member.UserMemberService;
import cn.lyy.merchant.service.tag.TagUserService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitAdjustDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.ConsumeDTO;
import com.lyy.user.account.infrastructure.account.dto.ConsumeDetailDTO;
import com.lyy.user.account.infrastructure.account.feign.AccountFeignClient;
import com.lyy.user.account.infrastructure.constant.AccountRecordTypeEnum;
import com.lyy.user.account.infrastructure.constant.AdjustTypeEnum;
import com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum;
import com.lyy.user.account.infrastructure.constant.BenefitClassifyGroupEnum;
import com.lyy.user.account.infrastructure.constant.TagBusinessTypeEnum;
import com.lyy.user.account.infrastructure.constant.TagCategoryEnum;
import com.lyy.user.account.infrastructure.constant.UserMemberSysConstants;
import com.lyy.user.account.infrastructure.constant.ExpiryDateCategoryEnum;
import com.lyy.user.account.infrastructure.resp.RespBody;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserListDTO;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserQueryDTO;
import com.lyy.user.account.infrastructure.user.dto.PayoutBenefitDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserListDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserQueryDTO;
import com.lyy.user.account.infrastructure.user.feign.UserFeignClient;
import com.lyy.user.app.interfaces.facade.dto.UserDTO;
import com.lyy.user.app.interfaces.facade.rpc.IUserRpc;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

/**
 * 新用户会员业务类
 *
 * <AUTHOR>
 * @create 2021/4/19 11:31
 */
@Service
@Slf4j
public class UserMemberServiceImpl implements UserMemberService {

    @Autowired
    private UserFeignClient userFeignClient;
    @Autowired
    private AccountFeignClient accountFeignClient;

    @Autowired
    private TagUserService tagUserService;

    @Autowired
    private IUserRpc iUserRpc;
    @Autowired
    private StoreRepository storeRepository;
    @Autowired
    private ConsumptionRepository consumptionRepository;

    /**
     * 是否对所有用户开放会员列表的“限制子账号查看的场地标签”功能
     */
    @Value("${member.list.group.tag.full-switch:false}")
    private Boolean fullSwitch;

    /**
     * 默认的最大场地数量
     */
    private final Integer groupMaxNum =  1000;

    @Autowired
    private MerchantUserClient merchantUserClient;

    @Autowired
    private MemberQueryConfig memberQueryConfig;



    @Override
    public Page<MerchantUserListDTO> queryUserListByMerchant(MerchantUserQueryDTO merchantUserQueryDTO, AdUserInfoDTO currentUser) {
        // 子账号没带场地标签请求时需要补充场地标签ID
        boolean rewriteGroupTagId = BooleanUtil.isTrue(fullSwitch) && BooleanUtil.isFalse(currentUser.getIsApprover()) &&
                !ofNullable(merchantUserQueryDTO).filter(param -> CollectionUtil.isNotEmpty(param.getGroupTagIdList())).isPresent();
        if(rewriteGroupTagId) {
            TagUserQueryDTO tagUserQueryDTO = buildDefaultRequestParam(currentUser.getAdOrgId());
            List<TagUserListDTO> tagList = tagUserService.listAllTag(tagUserQueryDTO, currentUser);
            // 子账号查询必须带场地标签，没有则返回空
            if(CollectionUtil.isEmpty(tagList)) {
                return new Page<MerchantUserListDTO>();
            }
            List<Long> newGroupTagIdList = tagList.stream().map(TagUserListDTO::getId).collect(Collectors.toList());
            log.debug("会员列表限制子账号看到的场地标签，原场地标签ID：{}，新场地标签ID：{}", merchantUserQueryDTO.getGroupTagIdList(), newGroupTagIdList);
            merchantUserQueryDTO.setGroupTagIdList(newGroupTagIdList);
        }
        RespBody<Page<MerchantUserListDTO>> result = null;
        if (memberQueryConfig.getMemberSelectdbQuery(currentUser.getAdOrgId())){
            result =  merchantUserClient.queryUserListByMerchant(merchantUserQueryDTO);
            log.debug("selectdb查询");
        }else {
            result =  userFeignClient.queryUserListByMerchant(merchantUserQueryDTO);
        }

        RespBody<Page<MerchantUserListDTO>> finalResult = result;
        Page<MerchantUserListDTO> pageInfo = ofNullable(result).filter(r -> r.getCode().equals(GlobalErrorCode.OK.getCode()))
                .map(RespBody::getBody).orElseThrow(() -> new BusinessException(finalResult.getCode(), finalResult.getMessage()));
        List<MerchantUserListDTO> records = pageInfo.getRecords();
        if(CollUtil.isNotEmpty(records)){
             List<Long> userIds = records.stream().map(MerchantUserListDTO::getUserId).collect(Collectors.toList());
             List<UserDTO> userList = Optional.ofNullable(iUserRpc.findByIds(userIds).getBody()).orElse(new ArrayList<>());
             Map<Long, UserDTO> userGroup = userList.stream().collect(Collectors.toMap(UserDTO::getId, r -> r, (oldV, newV) -> newV));
             records.forEach(item->{
                 // 获取最新的用户头像昵称
                  UserDTO userDTO = userGroup.get(item.getUserId());
                  if(Objects.nonNull(userDTO)){
                      if(StringUtils.isEmpty(item.getHeadImg())){
                          item.setHeadImg(userDTO.getHeadImg());
                      }
                      if(StringUtils.isEmpty(item.getNickName())){
                          item.setNickName(userDTO.getName());
                      }
                      if(StringUtils.isEmpty(item.getGender()) || "未知".equals(item.getGender())){
                          item.setGender(userDTO.getGender());
                      }
                  }
             });
            pageInfo.setRecords(records);
        }
        return pageInfo;
    }

    private TagUserQueryDTO buildDefaultRequestParam(Long merchantId) {
        TagUserQueryDTO tagUserQueryDTO = new TagUserQueryDTO();
        tagUserQueryDTO.setActive(true);
        tagUserQueryDTO.setBusinessType(TagBusinessTypeEnum.GROUP_NAME.getStatus());
        tagUserQueryDTO.setCategory(TagCategoryEnum.AUTO.getStatus());
        tagUserQueryDTO.setName("");
        tagUserQueryDTO.setQueryUserNumber(false);
        tagUserQueryDTO.setMerchantId(merchantId);
        tagUserQueryDTO.setTagType(UserMemberSysConstants.TAG_MERCHANT_USER);
        // 以下分页参数没有用，但是不传过不了参数校验
        tagUserQueryDTO.setPageIndex(1);
        tagUserQueryDTO.setPageSize(1000);
        return tagUserQueryDTO;
    }

    @Override
    public void payoutWelfare(PayoutWelfareDTO payoutWelfareDTO, Long merchantId) {

        if(payoutWelfareDTO.getIsAllEquipmentType()){
            payoutWelfareDTO.setEquipmentTypeIdList(new ArrayList<>());
        }

        if(payoutWelfareDTO.getIsAllGroup()) {
            payoutWelfareDTO.setGroupIdList(new ArrayList<>());
        }

        PayoutBenefitDTO payoutBenefitDTO = new PayoutBenefitDTO();
        BeanUtils.copyProperties(payoutWelfareDTO,payoutBenefitDTO);
        payoutBenefitDTO.setMerchantId(merchantId);
        RespBody<Void> respBody = userFeignClient.payoutBenefit(payoutBenefitDTO);
        ofNullable(respBody).filter(r -> r.getCode().equals(GlobalErrorCode.OK.getCode())).orElseThrow(() -> new BusinessException("派发福利失败"));

    }

    @Override
    public Long countUserListByMerchant(MerchantUserQueryDTO merchantUserQueryDTO) {
        RespBody<Long> result =  null;

        RespBody<Page<TagUserListDTO>> respBody = null;
        if (memberQueryConfig.getMemberSelectdbQuery(merchantUserQueryDTO.getMerchantId())){
            result =  merchantUserClient.countUserListByMerchant(merchantUserQueryDTO);
            log.debug("selectdb查询");
        }else {
            result = userFeignClient.countUserListByMerchant(merchantUserQueryDTO);
        }


        Long l = ofNullable(result).filter( r -> GlobalErrorCode.OK.getCode().equals(r.getCode())).map(r -> r.getBody()).orElse(0L);
        return l;
    }

    @Override
    public void adjustUserBenefit(UserBalanceAdjustReqDTO param, Long merchantId, Long operatorId) {
        log.debug("商户调整用户权益,param:{},adOrgId:{},operatorId:{}", param, merchantId, operatorId);
        if (param.getGroupBalances() != null && param.getGroupBalances().size() > 0) {
            // 用户充值余额账户变更
            log.debug("用户充值余额账户变更");
            param.getGroupBalances().forEach(groupBalanceDTO -> {
                RespBody respBody = null;
                AccountBenefitAdjustDTO accountBenefitAdjustDTO = new AccountBenefitAdjustDTO();
                accountBenefitAdjustDTO.setMerchantId(merchantId);
                accountBenefitAdjustDTO.setUserId(param.getUserId());
                accountBenefitAdjustDTO.setOperator(operatorId);
                if(groupBalanceDTO.getAmount() != null) {
                    if (groupBalanceDTO.getAmount().compareTo(BigDecimal.ZERO) >= 0) {
                        accountBenefitAdjustDTO.setAdjustType(AdjustTypeEnum.INCREMENT);
                        accountBenefitAdjustDTO.setStoreId(groupBalanceDTO.getGroupId());
                        accountBenefitAdjustDTO.setAmount(groupBalanceDTO.getAmount());
                        accountBenefitAdjustDTO.setClassify(BenefitClassifyEnum.USER_RECHARGE_BALANCE.getCode());
                        accountBenefitAdjustDTO.setRecordType(AccountRecordTypeEnum.MERCHANT_ADJUST_BALANCE.getCode());
                        respBody = accountFeignClient.merchantAdjustBenefit(accountBenefitAdjustDTO);
                    } else {
                        // 扣减权益
                        respBody = decreaseBenefit(param.getUserId(), merchantId, groupBalanceDTO.getGroupId(),
                            operatorId, BenefitClassifyEnum.USER_RECHARGE_BALANCE.getCode(),groupBalanceDTO.getAmount().abs(),
                            "商户调整余额扣减权益", AccountRecordTypeEnum.MERCHANT_ADJUST_BALANCE.getCode(),
                            null, param.getRemark());
                    }
                    if (!respBody.getCode().equals(GlobalErrorCode.OK.getCode())) {
                        log.warn("用户充值余额账户变更失败，msg:{}", respBody.getMessage());
                        throw new BusinessException(BusinessExceptionEnums.MERCHANT_ADJUST_ERROR.getExCode(), respBody.getMessage());
                    }
                }else if(groupBalanceDTO.getCoins() != null){
                    if (groupBalanceDTO.getCoins().compareTo(BigDecimal.ZERO) >= 0) {
                        accountBenefitAdjustDTO.setAdjustType(AdjustTypeEnum.INCREMENT);
                        accountBenefitAdjustDTO.setStoreId(groupBalanceDTO.getGroupId());
                        accountBenefitAdjustDTO.setAmount(groupBalanceDTO.getCoins());
                        accountBenefitAdjustDTO.setClassify(BenefitClassifyEnum.USER_RECHARGE_COIN.getCode());
                        accountBenefitAdjustDTO.setRecordType(AccountRecordTypeEnum.MERCHANT_ADJUST_COIN.getCode());
                        respBody = accountFeignClient.merchantAdjustBenefit(accountBenefitAdjustDTO);
                    } else {
                        // 扣减权益
                        respBody = decreaseBenefit(param.getUserId(), merchantId, groupBalanceDTO.getGroupId(),
                            operatorId, BenefitClassifyEnum.USER_RECHARGE_COIN.getCode(),  groupBalanceDTO.getCoins().abs(),
                            "商户调整余币扣减权益", AccountRecordTypeEnum.MERCHANT_ADJUST_COIN.getCode(),
                            null, param.getRemark());
                    }
                    if (!respBody.getCode().equals(GlobalErrorCode.OK.getCode())) {
                        log.warn("用户充值余币账户变更失败，msg:{}", respBody.getMessage());
                        throw new BusinessException(BusinessExceptionEnums.MERCHANT_ADJUST_ERROR.getExCode(), respBody.getMessage());
                    }
                }else if(groupBalanceDTO.getIdleCoins() != null){
                    if (groupBalanceDTO.getIdleCoins().compareTo(BigDecimal.ZERO) >= 0) {
                        accountBenefitAdjustDTO.setAdjustType(AdjustTypeEnum.INCREMENT);
                        accountBenefitAdjustDTO.setStoreId(groupBalanceDTO.getGroupId());
                        accountBenefitAdjustDTO.setAmount(groupBalanceDTO.getIdleCoins());
                        accountBenefitAdjustDTO.setClassify(BenefitClassifyEnum.IDLE_TIME_COIN.getCode());
                        accountBenefitAdjustDTO.setRecordType(AccountRecordTypeEnum.MERCHANT_ADJUST_IDLE_COIN.getCode());
                        respBody = accountFeignClient.merchantAdjustBenefit(accountBenefitAdjustDTO);
                    } else {
                        // 扣减权益
                        respBody = decreaseBenefit(param.getUserId(), merchantId, groupBalanceDTO.getGroupId(),
                            operatorId, BenefitClassifyEnum.IDLE_TIME_COIN.getCode(), groupBalanceDTO.getIdleCoins().abs(),
                            "商户调整闲时币扣减权益", AccountRecordTypeEnum.MERCHANT_ADJUST_IDLE_COIN.getCode(),
                            null, param.getRemark());
                    }
                    if (!respBody.getCode().equals(GlobalErrorCode.OK.getCode())) {
                        log.warn("用户闲时币账户变更失败，msg:{}", respBody.getMessage());
                        throw new BusinessException(BusinessExceptionEnums.MERCHANT_ADJUST_ERROR.getExCode(), respBody.getMessage());
                    }
                }else if(groupBalanceDTO.getAdCoins() != null){
                    // 处理广告币的调整
                    if (groupBalanceDTO.getAdCoins().compareTo(BigDecimal.ZERO) >= 0) {
                        accountBenefitAdjustDTO.setAdjustType(AdjustTypeEnum.INCREMENT);
                        accountBenefitAdjustDTO.setStoreId(groupBalanceDTO.getGroupId());
                        accountBenefitAdjustDTO.setAmount(groupBalanceDTO.getAdCoins());
                        accountBenefitAdjustDTO.setClassify(BenefitClassifyEnum.ADVERT_COIN.getCode());
                        accountBenefitAdjustDTO.setRecordType(AccountRecordTypeEnum.MERCHANT_ADJUST_ADVERT_COIN.getCode());
                        respBody = accountFeignClient.merchantAdjustBenefit(accountBenefitAdjustDTO);
                    } else {
                        // 扣减权益
                        respBody = decreaseBenefit(param.getUserId(), merchantId, groupBalanceDTO.getGroupId(),
                            operatorId, BenefitClassifyEnum.ADVERT_COIN.getCode(), groupBalanceDTO.getAdCoins().abs(),
                            "商户调整广告币扣减权益", AccountRecordTypeEnum.MERCHANT_ADJUST_ADVERT_COIN.getCode(),
                            null, param.getRemark());
                    }
                    if (!respBody.getCode().equals(GlobalErrorCode.OK.getCode())) {
                        log.warn("用户广告币账户变更失败，msg:{}", respBody.getMessage());
                        throw new BusinessException(BusinessExceptionEnums.MERCHANT_ADJUST_ERROR.getExCode(), respBody.getMessage());
                    }
                }
            });

        }

        boolean existGroup = Objects.nonNull(param.getGroupId());
        consumptionRecord(param, merchantId, operatorId);
        if (param.getAmount() != null) {
            if (param.getAmount().compareTo(BigDecimal.ZERO) < 0) {
                // 余额根据消耗规则减少
                List<BenefitClassifyEnum> balanceClassifyEnumList = BenefitClassifyEnum.getBenefitClassifyBalanceEnum();
                List<Integer> balanceClassifyList = balanceClassifyEnumList.stream().map(BenefitClassifyEnum::getCode).collect(Collectors.toList());
                RespBody<Void> respBody = decreaseBenefit(param.getUserId(), merchantId, null,
                    operatorId, balanceClassifyList, param.getAmount().abs(),
                    "商户调整余额扣减权益", AccountRecordTypeEnum.MERCHANT_ADJUST_All.getCode(), true, param.getRemark());
                if (!respBody.getCode().equals(GlobalErrorCode.OK.getCode())) {
                    log.warn("余额账户变更失败，msg:{}", respBody.getMessage());
                    throw new BusinessException(BusinessExceptionEnums.MERCHANT_ADJUST_ERROR.getExCode(), respBody.getMessage());
                }
            }else {
                //增加商家派发余额
                AccountBenefitAdjustDTO accountBenefitAdjustDTO = new AccountBenefitAdjustDTO();
                accountBenefitAdjustDTO.setMerchantId(merchantId);
                accountBenefitAdjustDTO.setAdjustType(AdjustTypeEnum.INCREMENT);
                accountBenefitAdjustDTO.setAmount(param.getAmount());
                accountBenefitAdjustDTO.setClassify(existGroup ? BenefitClassifyEnum.MERCHANT_PAYOUT_GROUP_BALANCE.getCode()
                        : BenefitClassifyEnum.MERCHANT_PAYOUT_BALANCE.getCode());
                accountBenefitAdjustDTO.setRecordType(AccountRecordTypeEnum.MERCHANT_ADJUST_PAYOUT_BALANCE.getCode());
                accountBenefitAdjustDTO.setResource("商家派发余额");
                assignBenefitInfo(param, operatorId, accountBenefitAdjustDTO);
                RespBody respBody = accountFeignClient.merchantAdjustBenefit(accountBenefitAdjustDTO);
                if (!respBody.getCode().equals(GlobalErrorCode.OK.getCode())) {
                    log.warn("商家派送余额账户变更失败，request:{},respBody:{}", accountBenefitAdjustDTO, respBody);
                    throw new BusinessException(BusinessExceptionEnums.MERCHANT_ADJUST_ERROR.getExCode(), respBody.getMessage());
                }
            }
        }
        if (param.getCoins() != null) {
            if (param.getCoins().compareTo(BigDecimal.ZERO) < 0) {
                // 余币根据消耗规则减少
                List<BenefitClassifyEnum> coinClassifyEnumList = BenefitClassifyEnum.getBenefitClassifyCoinEnum();
                List<Integer> coinClassifyList = coinClassifyEnumList.stream().map(BenefitClassifyEnum::getCode).collect(Collectors.toList());
                RespBody<Void> respBody = decreaseBenefit(param.getUserId(), merchantId, null,
                    operatorId, coinClassifyList, param.getCoins().abs(),
                    "商户调整余币扣减权益", AccountRecordTypeEnum.MERCHANT_ADJUST_All.getCode(), true, param.getRemark());
                if (!respBody.getCode().equals(GlobalErrorCode.OK.getCode())) {
                    log.warn("余币账户变更失败，msg:{}", respBody.getMessage());
                    throw new BusinessException(BusinessExceptionEnums.MERCHANT_ADJUST_ERROR.getExCode(), respBody.getMessage());
                }
            }else {
                AccountBenefitAdjustDTO accountBenefitAdjustDTO = new AccountBenefitAdjustDTO();
                accountBenefitAdjustDTO.setMerchantId(merchantId);
                accountBenefitAdjustDTO.setAdjustType(AdjustTypeEnum.INCREMENT);
                accountBenefitAdjustDTO.setAmount(param.getCoins());
                accountBenefitAdjustDTO.setClassify(existGroup ? BenefitClassifyEnum.MERCHANT_PAYOUT_GROUP_COIN.getCode()
                        : BenefitClassifyEnum.MERCHANT_PAYOUT_COIN.getCode());
                accountBenefitAdjustDTO.setResource("商家派发余币");
                accountBenefitAdjustDTO.setRecordType(AccountRecordTypeEnum.MERCHANT_ADJUST_PAYOUT_COIN.getCode());
                assignBenefitInfo(param, operatorId, accountBenefitAdjustDTO);
                RespBody respBody = accountFeignClient.merchantAdjustBenefit(accountBenefitAdjustDTO);
                if (!respBody.getCode().equals(GlobalErrorCode.OK.getCode())) {
                    log.warn("商家派送余币账户变更失败，request:{},respBody:{}", accountBenefitAdjustDTO, respBody);
                    throw new BusinessException(BusinessExceptionEnums.MERCHANT_ADJUST_ERROR.getExCode(), respBody.getMessage());
                }
            }
        }

        if (param.getRedAmount() != null && param.getRedAmount().compareTo(BigDecimal.ZERO) < 0) {
            // 红包余额 账户金额减少
            RespBody<Void> respBody = decreaseBenefit(param.getUserId(), merchantId, null,
                operatorId, BenefitClassifyEnum.RED_BALANCE.getCode(), param.getRedAmount().abs(),
                "商户调整红包余额扣减权益", AccountRecordTypeEnum.MERCHANT_ADJUST_RED_BALANCE.getCode(), null, param.getRemark());
            if (!respBody.getCode().equals(GlobalErrorCode.OK.getCode())) {
                log.warn("商家派送红包余额账户变更失败，msg:{}", respBody.getMessage());
                throw new BusinessException(BusinessExceptionEnums.MERCHANT_ADJUST_ERROR.getExCode(), respBody.getMessage());
            }
        }
        if (param.getRedCoins() != null && param.getRedCoins().compareTo(BigDecimal.ZERO) < 0) {
            // 红包币只能减少，走权益消耗接口
            RespBody<Void> respBody = decreaseBenefit(param.getUserId(), merchantId, null,
                operatorId, BenefitClassifyEnum.RED_COIN.getCode(), param.getRedCoins().abs(),
                "商户调整红包余币扣减权益", AccountRecordTypeEnum.MERCHANT_ADJUST_RED_COIN.getCode(), null, param.getRemark());
            if(!respBody.getCode().equals(GlobalErrorCode.OK.getCode())){
                log.warn("商家派送红包币账户变更失败，msg:{}",respBody.getMessage());
                throw new BusinessException(BusinessExceptionEnums.MERCHANT_ADJUST_ERROR.getExCode(), respBody.getMessage());
            }
        }
        if (param.getGrantCoins() != null && param.getGrantCoins().compareTo(BigDecimal.ZERO) < 0) {
            // 派币减少
            RespBody respBody = decreaseBenefit(param.getUserId(), merchantId, null, operatorId,
                Arrays.asList(BenefitClassifyEnum.MERCHANT_PAYOUT_COIN.getCode(), BenefitClassifyEnum.THIRD_PLATFORM_COINS.getCode()),
                param.getGrantCoins().abs(), "商户调整派币扣减权益", AccountRecordTypeEnum.MERCHANT_ADJUST_PAYOUT_COIN.getCode(),
                null, param.getRemark());
            if (!respBody.getCode().equals(GlobalErrorCode.OK.getCode())) {
                log.warn("商家派送余币账户变更失败，msg:{}", respBody.getMessage());
                throw new BusinessException(BusinessExceptionEnums.MERCHANT_ADJUST_ERROR.getExCode(), respBody.getMessage());
            }
        }
        if (param.getGrantAmount() != null && param.getGrantAmount().compareTo(BigDecimal.ZERO) < 0) {
            // 派发余额减少
            RespBody respBody = decreaseBenefit(param.getUserId(), merchantId, null, operatorId,
                Arrays.asList(BenefitClassifyEnum.MERCHANT_PAYOUT_BALANCE.getCode(), BenefitClassifyEnum.THIRD_PLATFORM_AMOUNT.getCode()),
                param.getGrantAmount().abs(), "商户调整派发余额扣减权益", AccountRecordTypeEnum.MERCHANT_ADJUST_PAYOUT_BALANCE.getCode(),
                null, param.getRemark());
            if (!respBody.getCode().equals(GlobalErrorCode.OK.getCode())) {
                log.warn("商家派送余额账户变更失败，msg:{}", respBody.getMessage());
                throw new BusinessException(BusinessExceptionEnums.MERCHANT_ADJUST_ERROR.getExCode(), respBody.getMessage());
            }
        }

        // 清空余额
        List<Integer> excludeClassify = Arrays.asList(BenefitClassifyEnum.ADVERT_RED_PACKET.getCode(),BenefitClassifyEnum.DELAYED_SETTELEMENT_BALANCE.getCode(),BenefitClassifyEnum.DELAYED_SETTELEMENT_COIN.getCode());
        if(Objects.equals(param.getIsEmpty(), 1)){
            List<AccountBenefitAdjustDTO> accountBenefitAdjustDTOList = new ArrayList<>();
            // 用户充值余额
            AccountBenefitAdjustDTO accountBenefitAdjustDTO = new AccountBenefitAdjustDTO();
            accountBenefitAdjustDTO.setMerchantId(merchantId);
            accountBenefitAdjustDTO.setUserId(param.getUserId());
            accountBenefitAdjustDTO.setOperator(operatorId);
            accountBenefitAdjustDTO.setClassify(BenefitClassifyEnum.USER_RECHARGE_BALANCE.getCode());
            accountBenefitAdjustDTO.setRecordType(AccountRecordTypeEnum.MERCHANT_ADJUST_BALANCE.getCode());
            accountBenefitAdjustDTOList.add(accountBenefitAdjustDTO);

            // 商家赠送余额
            AccountBenefitAdjustDTO merchantPayoutAccountBenefit = new AccountBenefitAdjustDTO();
            merchantPayoutAccountBenefit.setMerchantId(merchantId);
            merchantPayoutAccountBenefit.setUserId(param.getUserId());
            merchantPayoutAccountBenefit.setOperator(operatorId);
            merchantPayoutAccountBenefit.setClassify(BenefitClassifyEnum.MERCHANT_PAYOUT_BALANCE.getCode());
            merchantPayoutAccountBenefit.setRecordType(AccountRecordTypeEnum.MERCHANT_ADJUST_PAYOUT_BALANCE.getCode());
            accountBenefitAdjustDTOList.add(merchantPayoutAccountBenefit);

            AccountBenefitAdjustDTO redAmountAccountBenefit = new AccountBenefitAdjustDTO();
            redAmountAccountBenefit.setMerchantId(merchantId);
            redAmountAccountBenefit.setUserId(param.getUserId());
            redAmountAccountBenefit.setOperator(operatorId);
            redAmountAccountBenefit.setClassify(BenefitClassifyEnum.RED_BALANCE.getCode());
            redAmountAccountBenefit.setRecordType(AccountRecordTypeEnum.MERCHANT_ADJUST_RED_BALANCE.getCode());
            accountBenefitAdjustDTOList.add(redAmountAccountBenefit);

            // 第三方赠送余额
            AccountBenefitAdjustDTO thirdPayoutAccountBenefit = new AccountBenefitAdjustDTO();
            thirdPayoutAccountBenefit.setMerchantId(merchantId);
            thirdPayoutAccountBenefit.setUserId(param.getUserId());
            thirdPayoutAccountBenefit.setOperator(operatorId);
            thirdPayoutAccountBenefit.setClassify(BenefitClassifyEnum.THIRD_PLATFORM_AMOUNT.getCode());
            thirdPayoutAccountBenefit.setRecordType(AccountRecordTypeEnum.MERCHANT_ADJUST_PAYOUT_BALANCE.getCode());
            accountBenefitAdjustDTOList.add(thirdPayoutAccountBenefit);

            RespBody respBody = accountFeignClient.merchantClearBenefit(accountBenefitAdjustDTOList);
            if (!respBody.getCode().equals(GlobalErrorCode.OK.getCode())) {
                log.warn("清空账户余额变更失败，msg:{}", respBody.getMessage());
                throw new BusinessException(BusinessExceptionEnums.MERCHANT_ADJUST_ERROR.getExCode(), respBody.getMessage());
            }
        }
        else if (Objects.equals(param.getIsEmpty(), 2)){
            // 清空余币
            List<AccountBenefitAdjustDTO> accountBenefitAdjustDTOList = new ArrayList<>();
            // 用户充值余币
            AccountBenefitAdjustDTO accountBenefitAdjustDTO = new AccountBenefitAdjustDTO();
            accountBenefitAdjustDTO.setMerchantId(merchantId);
            accountBenefitAdjustDTO.setUserId(param.getUserId());
            accountBenefitAdjustDTO.setOperator(operatorId);
            accountBenefitAdjustDTO.setClassify(BenefitClassifyEnum.USER_RECHARGE_COIN.getCode());
            accountBenefitAdjustDTO.setRecordType(AccountRecordTypeEnum.MERCHANT_ADJUST_COIN.getCode());
            accountBenefitAdjustDTOList.add(accountBenefitAdjustDTO);

            // 商家赠送余币
            AccountBenefitAdjustDTO merchantPayoutCoinBenefit = new AccountBenefitAdjustDTO();
            merchantPayoutCoinBenefit.setMerchantId(merchantId);
            merchantPayoutCoinBenefit.setUserId(param.getUserId());
            merchantPayoutCoinBenefit.setOperator(operatorId);
            merchantPayoutCoinBenefit.setClassify(BenefitClassifyEnum.MERCHANT_PAYOUT_COIN.getCode());
            merchantPayoutCoinBenefit.setRecordType(AccountRecordTypeEnum.MERCHANT_ADJUST_PAYOUT_COIN.getCode());
            accountBenefitAdjustDTOList.add(merchantPayoutCoinBenefit);

            // 第三方赠送余币
            AccountBenefitAdjustDTO thirdPayoutCoinBenefit = new AccountBenefitAdjustDTO();
            thirdPayoutCoinBenefit.setMerchantId(merchantId);
            thirdPayoutCoinBenefit.setUserId(param.getUserId());
            thirdPayoutCoinBenefit.setOperator(operatorId);
            thirdPayoutCoinBenefit.setClassify(BenefitClassifyEnum.THIRD_PLATFORM_COINS.getCode());
            thirdPayoutCoinBenefit.setRecordType(AccountRecordTypeEnum.MERCHANT_ADJUST_PAYOUT_COIN.getCode());
            accountBenefitAdjustDTOList.add(thirdPayoutCoinBenefit);

            // 红包币
            AccountBenefitAdjustDTO redCoinsBenefit = new AccountBenefitAdjustDTO();
            redCoinsBenefit.setMerchantId(merchantId);
            redCoinsBenefit.setUserId(param.getUserId());
            redCoinsBenefit.setOperator(operatorId);
            redCoinsBenefit.setClassify(BenefitClassifyEnum.RED_COIN.getCode());
            redCoinsBenefit.setRecordType(AccountRecordTypeEnum.MERCHANT_ADJUST_RED_COIN.getCode());
            accountBenefitAdjustDTOList.add(redCoinsBenefit);

            // 闲时币
            AccountBenefitAdjustDTO idleCoinsBenefit = new AccountBenefitAdjustDTO();
            idleCoinsBenefit.setMerchantId(merchantId);
            idleCoinsBenefit.setUserId(param.getUserId());
            idleCoinsBenefit.setOperator(operatorId);
            idleCoinsBenefit.setClassify(BenefitClassifyEnum.IDLE_TIME_COIN.getCode());
            idleCoinsBenefit.setRecordType(AccountRecordTypeEnum.MERCHANT_ADJUST_IDLE_COIN.getCode());
            accountBenefitAdjustDTOList.add(idleCoinsBenefit);

            // 广告币
            AccountBenefitAdjustDTO advertCoinsBenefit = new AccountBenefitAdjustDTO();
            advertCoinsBenefit.setMerchantId(merchantId);
            advertCoinsBenefit.setUserId(param.getUserId());
            advertCoinsBenefit.setOperator(operatorId);
            advertCoinsBenefit.setClassify(BenefitClassifyEnum.ADVERT_COIN.getCode());
            advertCoinsBenefit.setRecordType(AccountRecordTypeEnum.MERCHANT_ADJUST_ADVERT_COIN.getCode());
            accountBenefitAdjustDTOList.add(advertCoinsBenefit);

            RespBody respBody = accountFeignClient.merchantClearBenefit(accountBenefitAdjustDTOList);
            if (!respBody.getCode().equals(GlobalErrorCode.OK.getCode())) {
                log.warn("清空余币变更失败，msg:{}", respBody.getMessage());
                throw new BusinessException(BusinessExceptionEnums.MERCHANT_ADJUST_ERROR.getExCode(), respBody.getMessage());
            }
        }
        else if (Objects.equals(param.getIsEmpty(), 4)) {
            // 清除指定批次
            Assert.notNull(param.getAccountBenefitId(),"权益ID不能为空");
            Assert.notNull(param.getClassify(),"权益类型不能为空");
            if (excludeClassify.contains(param.getClassify())) {
                return;
            }
            AccountBenefitAdjustDTO batchBenefit = new AccountBenefitAdjustDTO();
            batchBenefit.setMerchantId(merchantId);
            batchBenefit.setUserId(param.getUserId());
            batchBenefit.setOperator(operatorId);
            batchBenefit.setClassify(param.getClassify());
            batchBenefit.setRecordType(getRecordType(param.getClassify()));
            batchBenefit.setIsEmpty(false);
            batchBenefit.setId(param.getAccountBenefitId());
            RespBody respBody = accountFeignClient.merchantClearBatchBenefit(Collections.singletonList(batchBenefit));
            if (!respBody.getCode().equals(GlobalErrorCode.OK.getCode())) {
                log.warn("清空指定批次权益变更失败，msg:{}", respBody.getMessage());
                throw new BusinessException(BusinessExceptionEnums.MERCHANT_ADJUST_ERROR.getExCode(), respBody.getMessage());
            }
        }
        else if (Objects.equals(param.getIsEmpty(), 5)) {
            // 清除指定类型
            Assert.notNull(param.getClassify(),"权益类型不能为空");
            if (excludeClassify.contains(param.getClassify())) {
                return;
            }
            AccountBenefitAdjustDTO classifyBenefit = new AccountBenefitAdjustDTO();
            classifyBenefit.setMerchantId(merchantId);
            classifyBenefit.setUserId(param.getUserId());
            classifyBenefit.setOperator(operatorId);
            classifyBenefit.setClassify(param.getClassify());
            classifyBenefit.setRecordType(getRecordType(param.getClassify()));
            classifyBenefit.setIsEmpty(true);
            RespBody respBody = accountFeignClient.merchantClearBatchBenefit(Collections.singletonList(classifyBenefit));
            if (!respBody.getCode().equals(GlobalErrorCode.OK.getCode())) {
                log.warn("清空指定类型权益变更失败，msg:{}", respBody.getMessage());
                throw new BusinessException(BusinessExceptionEnums.MERCHANT_ADJUST_ERROR.getExCode(), respBody.getMessage());
            }
        }
        else if (Objects.equals(param.getIsEmpty(), 6) && param.getBenefitDetailQuery() != null) {
            AccountBenefitQueryDTO dto = new AccountBenefitQueryDTO();
            BenefitDetailQueryDTO benefitDetailQueryDTO = param.getBenefitDetailQuery();
            BeanUtils.copyProperties(benefitDetailQueryDTO, dto);
            dto.setMerchantUserId(benefitDetailQueryDTO.getMerchantUserId());
            dto.setMerchantId(merchantId);
            dto.setExcludeClassify(excludeClassify);
            dto.setPageSize(ofNullable(dto.getPageSize()).orElse(9999));
            dto.setPageIndex(ofNullable(dto.getPageIndex()).orElse(1));
            if (benefitDetailQueryDTO.getClassify()==null && benefitDetailQueryDTO.getBenefitGroupType() != null) {
                Integer benefitGroupType = benefitDetailQueryDTO.getBenefitGroupType();
                dto.setClassify(BenefitClassifyGroupEnum.getClassifyByType(benefitGroupType));
            } else {
                dto.setClassify(Collections.singletonList(benefitDetailQueryDTO.getClassify()));
            }
            dto.setCountSql(false);
            RespBody<Page<AccountBenefitDTO>> resp = accountFeignClient.listBenefitDetail(dto);
            if (!GlobalErrorCode.OK.getCode().equals(resp.getCode())) {
                throw new BusinessException(resp.getMessage());
            }
            List<AccountBenefitDTO> benefitList = ofNullable(resp.getBody()).map(Page::getRecords).orElse(new ArrayList<>());
            if (benefitList.isEmpty()) {
                return;
            }
            List<AccountBenefitAdjustDTO> adjustList = new ArrayList<>();
            for (AccountBenefitDTO benefit : benefitList) {
                if (benefit.getBalance().compareTo(BigDecimal.ZERO) <= 0) {
                    continue;
                }
                AccountBenefitAdjustDTO batchBenefit = new AccountBenefitAdjustDTO();
                batchBenefit.setMerchantId(merchantId);
                batchBenefit.setUserId(param.getUserId());
                batchBenefit.setOperator(operatorId);
                batchBenefit.setClassify(benefit.getClassify());
                batchBenefit.setRecordType(getRecordType(benefit.getClassify()));
                batchBenefit.setIsEmpty(false);
                batchBenefit.setId(benefit.getId());
                adjustList.add(batchBenefit);
            }
            RespBody respBody = accountFeignClient.merchantClearBatchBenefit(adjustList);
            if (!respBody.getCode().equals(GlobalErrorCode.OK.getCode())) {
                log.warn("清空查询批次权益变更失败，msg:{}", respBody.getMessage());
                throw new BusinessException(BusinessExceptionEnums.MERCHANT_ADJUST_ERROR.getExCode(), respBody.getMessage());
            }
        }
    }

    private void consumptionRecord(UserBalanceAdjustReqDTO param, Long merchantId, Long operatorId) {
        if (Objects.nonNull(param.getAmount()) || Objects.nonNull(param.getCoins())) {
            LyyGrantCoinsDTO grantCoinsDTO = new LyyGrantCoinsDTO();
            grantCoinsDTO.setAdUserId(operatorId);
            grantCoinsDTO.setLyyDistributorId(merchantId.intValue());
            grantCoinsDTO.setLyyUserId(param.getUserId());
            grantCoinsDTO.setCoins(param.getCoins());
            grantCoinsDTO.setDescription(param.getRemark());
            grantCoinsDTO.setIsread("N");
            grantCoinsDTO.setAmount(param.getAmount());
            grantCoinsDTO.setType(GrantCoinsType.DISTRIBUTOR.getType());
            consumptionRepository.saveGrantCoins(grantCoinsDTO);
        }
    }

    private void assignBenefitInfo(UserBalanceAdjustReqDTO param, Long operatorId, AccountBenefitAdjustDTO accountBenefitAdjustDTO) {
        String storeName = null;
        if (Objects.nonNull(param.getGroupId())) {
            storeName = storeRepository.getStore(param.getGroupId()).map(MerchantGroupDTO::getName).orElse(null);
        }
        accountBenefitAdjustDTO.setOperator(operatorId);
        accountBenefitAdjustDTO.setUserId(param.getUserId());
        accountBenefitAdjustDTO.setDescription(param.getRemark());
        accountBenefitAdjustDTO.setStoreName(storeName);
        accountBenefitAdjustDTO.setStoreId(param.getGroupId());
        ExpiryDateCategoryEnum.getCategory(param.getExpiryDateCategory()).ifPresent(c-> {
            accountBenefitAdjustDTO.setExpiryDateCategory(c);
            accountBenefitAdjustDTO.setDownTime(param.getDownTime());
            accountBenefitAdjustDTO.setUpTime(param.getUpTime());
        });
    }

    private Integer getRecordType(Integer classify) {
        BenefitClassifyEnum classifyEnum = BenefitClassifyEnum.of(classify);
        switch (classifyEnum) {
            case RED_COIN:
                return AccountRecordTypeEnum.MERCHANT_ADJUST_RED_COIN.getCode();
            case ADVERT_COIN:
                return AccountRecordTypeEnum.MERCHANT_ADJUST_ADVERT_COIN.getCode();
            case IDLE_TIME_COIN:
                return AccountRecordTypeEnum.MERCHANT_ADJUST_IDLE_COIN.getCode();
            case THIRD_PLATFORM_COINS:
            case MERCHANT_PAYOUT_COIN:
                return AccountRecordTypeEnum.MERCHANT_ADJUST_PAYOUT_COIN.getCode();
            case USER_RECHARGE_COIN:
                return AccountRecordTypeEnum.MERCHANT_ADJUST_COIN.getCode();
            case RED_BALANCE:
                return AccountRecordTypeEnum.MERCHANT_ADJUST_RED_BALANCE.getCode();
            case THIRD_PLATFORM_AMOUNT:
            case MERCHANT_PAYOUT_BALANCE:
                return AccountRecordTypeEnum.MERCHANT_ADJUST_PAYOUT_BALANCE.getCode();
            case USER_RECHARGE_BALANCE:
                return AccountRecordTypeEnum.MERCHANT_ADJUST_BALANCE.getCode();
            default:
                return AccountRecordTypeEnum.MERCHANT_ADJUST_All.getCode();
        }
    }

    /**
     * 权益扣减
     * @param userId 用户ID
     * @param merchantId 商户ID
     * @param storeId  场地ID
     * @param operatorId 操作者ID
     * @param classifyCode
     * @param amount 涉及数值
     * @param resource 来源描述
     * @param recordType 记录类型
     * @return
     */
    private RespBody<Void> decreaseBenefit(Long userId,Long merchantId,Long storeId,Long operatorId,Integer classifyCode,BigDecimal amount,String resource,Integer recordType, Boolean excludeGroup, String remark) {
        return decreaseBenefit(userId, merchantId, storeId, operatorId, Collections.singletonList(classifyCode), amount, resource, recordType, excludeGroup, remark);
    }

    /**
     * 权益扣减
     *
     * @param userId     用户ID
     * @param merchantId 商户ID
     * @param storeId    场地ID
     * @param operatorId 操作者ID
     * @param classify   权益组
     * @param amount     涉及数值
     * @param resource   来源描述
     * @param recordType 记录类型
     * @return
     */
    private RespBody<Void> decreaseBenefit(Long userId, Long merchantId, Long storeId, Long operatorId, List<Integer> classify, BigDecimal amount, String resource, Integer recordType, Boolean excludeGroup, String remark) {
        ConsumeDTO consume = new ConsumeDTO();
        consume.setUserId(userId);
        consume.setMerchantId(merchantId);
        consume.setStoreId(storeId);
        consume.setOperator(operatorId);
        consume.setResource(resource);
        consume.setRecordType(recordType);
        consume.setExcludeGroup(excludeGroup);
        consume.setDescription(remark);

        ConsumeDetailDTO detail = new ConsumeDetailDTO();
        detail.setClassify(classify);
        detail.setAmount(amount);
        consume.setConsume(Collections.singletonList(detail));
        consume.setExcludeClassify(Arrays.asList(BenefitClassifyEnum.ADVERT_RED_PACKET.getCode(),BenefitClassifyEnum.DELAYED_SETTELEMENT_BALANCE.getCode(),BenefitClassifyEnum.DELAYED_SETTELEMENT_COIN.getCode()));
        return accountFeignClient.benefitConsume(consume);
    }
}
