package cn.lyy.merchant.service.member.impl;

import cn.lyy.bigdata.api.client.BigDataAccountFeignClient;
import cn.lyy.bigdata.api.dto.BigDataAccountConsumption;
import cn.lyy.bigdata.api.dto.BigDataAccountRecordQueryDTO;
import cn.lyy.merchant.service.member.MemberConsumptionService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.user.account.infrastructure.account.dto.AccountConsumption;
import com.lyy.user.account.infrastructure.account.dto.AccountRecordQueryDTO;
import com.lyy.user.account.infrastructure.account.feign.AccountFeignClient;
import com.lyy.user.account.infrastructure.resp.RespBody;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @description:
 * @author: qgw
 * @date on 2021-06-03.
 * @Version: 1.0
 */
@Slf4j
@Service
public class MemberConsumptionServiceImpl implements MemberConsumptionService {

    @Autowired
    private AccountFeignClient accountFeignClient;
    @Autowired
    private BigDataAccountFeignClient bigDataAccountFeignClient;

    @Override
    public Page<AccountConsumption> listConsumptionRecord(AccountRecordQueryDTO recordQueryDTO) throws Exception {
        recordQueryDTO.setCountSql(false);
        log.debug("查询消费记录参数:{}", recordQueryDTO);
        RespBody<Page<AccountConsumption>> respBody = accountFeignClient.listRecord(recordQueryDTO);
        log.debug("查询消费记录结果:{}", respBody);
         if (!GlobalErrorCode.OK.getCode().equals(respBody.getCode())) {
            throw new Exception(respBody.toString());
        }

        return respBody.getBody();
    }

    @Override
    public Page<BigDataAccountConsumption> listConsumptionRecordV2(BigDataAccountRecordQueryDTO recordQueryDTO) throws Exception {
        cn.lyy.bigdata.api.base.RespBody<Page<BigDataAccountConsumption>> respBody = bigDataAccountFeignClient.listRecord(recordQueryDTO);
        log.debug("查询消费记录v2结果:{}", respBody);
         if (!GlobalErrorCode.OK.getCode().equals(respBody.getCode())) {
            throw new Exception(respBody.toString());
        }

        return respBody.getBody();
    }



}
