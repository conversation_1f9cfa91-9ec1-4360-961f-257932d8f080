package cn.lyy.merchant.service.messagecenter.handler.base;

import cn.lyy.merchant.service.merchant.dto.MerchantInfoStatusDTO;
import cn.lyy_dto.AdUserInfoDTO;
import com.lyy.charge.dto.bill.ProductNotifyDTO;
import com.lyy.merchant.applyment.dto.material.response.MerchantInfoStatusRespDTO;
import com.lyy.platform.dto.response.inform.PlatformChargeReceiptOrderRemindDTO;
import com.lyy.sysrisk.dto.MerchantStatusQueryRespDTO;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/05/28
 */
@Data
public class TodoContext {

    private Long userId;
    private Long adOrgId;
    private Long lyySwiftpassMerchantId;
    /**
     * 风控状态
     * 渠道风控待办，ChannelPayRiskTodoHandler
     */
    private MerchantStatusQueryRespDTO merchantStatusQueryRespDTO;
    /**
     * 商户信息
     * 体验期过期待办，ExperienceExpireNoticeTodoHandler
     * 收款资料待办，WrongCollectionInfoTodoHandler
     */
    private List<MerchantInfoStatusRespDTO> merchantInfoStatusRespDTOS;
    /**
     * 商家设备数据
     * 商家设备数据 --> 判断是否有动态柜 --> 服务缴费状态
     */
    private List<String> equipmentTypeValueList;
    /**
     * 是否绑定过设备
     * 新手任务-绑定设备待办，BindEquipmentTodoHandler
     */
    private Boolean hasFirstRegister;
    /**
     * 新手任务-开通收款
     * 新手任务-开通收款待办，OpenCollectionTodoHandler
     */
    private MerchantInfoStatusDTO merchantInfoNewUserTaskStatusDTO;
    /**
     * 服务费缴费
     * 服务费缴费待办，ChargingRemindTodoHandler
     */
    private PlatformChargeReceiptOrderRemindDTO platformChargeReceiptOrderRemindDTO;
    /**
     * 是否为主账号
     */
    private AdUserInfoDTO adUserInfoDTO;

    /**
     * 收款资料信息
     * 实名待办，RealNameTodoHandler
     * 收款资料待办，WrongCollectionInfoTodoHandler
     */
    private MerchantInfoStatusDTO merchantInfoStatusDTO;

    /**
     * SAAS 续订提醒
     * SAAS续订提醒待办，SAASProductChargeNotifyHandler
     */
    private ProductNotifyDTO notifyDTO;
}
