package cn.lyy.merchant.service.messagecenter.handler;

import cn.lyy.merchant.constants.TodoGroupEnum;
import cn.lyy.merchant.constants.TodoTypeEnum;
import cn.lyy.merchant.dto.positionmessage.TodoDTO;
import cn.lyy.merchant.service.messagecenter.handler.base.AbstractTodoHandler;
import cn.lyy.merchant.service.messagecenter.handler.base.TodoContext;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

@Service
public class BindEquipmentTodoHandler extends AbstractTodoHandler {

    @Override
    public TodoDTO handle(TodoContext context) {
        if(!isApprover(context.getAdUserInfoDTO())) {
            return null;
        }
        if (!context.getHasFirstRegister()) {
            return createTodoDTO(context.getUserId(), context.getAdOrgId(),
                    "还没有设备", "绑定设备并开通收款后，就可以开始经营啦",
                    "/merchant-saas/#/pages/device/bind/index", TodoTypeEnum.BIND_EQUIPMENT.getCode(), 0,null,"去绑设备");
        }
        return null;
    }

    @Override
    public String group() {
        return TodoGroupEnum.NEW_USER_TASK.getGroup();
    }
}