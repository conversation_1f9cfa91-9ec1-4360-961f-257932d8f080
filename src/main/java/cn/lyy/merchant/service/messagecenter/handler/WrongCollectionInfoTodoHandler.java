package cn.lyy.merchant.service.messagecenter.handler;

import cn.hutool.core.collection.CollectionUtil;
import cn.lyy.merchant.constants.TodoGroupEnum;
import cn.lyy.merchant.constants.TodoTypeEnum;
import cn.lyy.merchant.dto.positionmessage.TodoDTO;
import cn.lyy.merchant.dto.positionmessage.TodoDTO.Extend;
import cn.lyy.merchant.service.merchant.dto.MerchantInfoStatusConstant.StatusEnum;
import cn.lyy.merchant.service.messagecenter.handler.base.AbstractTodoHandler;
import cn.lyy.merchant.service.messagecenter.handler.base.TodoContext;
import com.aliyun.odps.utils.StringUtils;
import com.lyy.merchant.applyment.dto.material.response.MerchantInfoStatusRespDTO;
import java.util.List;
import org.springframework.stereotype.Service;

@Service
public class WrongCollectionInfoTodoHandler extends AbstractTodoHandler {

    @Override
    public TodoDTO handle(TodoContext context) {
        if (!isApprover(context.getAdUserInfoDTO())) {
            return null;
        }
        String status = context.getMerchantInfoStatusDTO().getStatus();
        Extend extend = new Extend();
        extend.setStatus(status);
        boolean expireExpirNotice = false;
        List<MerchantInfoStatusRespDTO> merchantInfoStatusRespDTOList = context.getMerchantInfoStatusRespDTOS();
        if (CollectionUtil.isNotEmpty(merchantInfoStatusRespDTOList)) {
            MerchantInfoStatusRespDTO merchantInfoStatusRespDTO = merchantInfoStatusRespDTOList.stream()
                    .filter(v -> StatusEnum.EXPERIENCE_EXPIRE_NOTICE.getCode().equals(v.getStatus())).findFirst().orElse(null);
            if (merchantInfoStatusRespDTO != null) {
                expireExpirNotice = true;
            }
        }
        //体验期预警不存在资料驳回
        if (StringUtils.isNotBlank(status) && !expireExpirNotice) {
            if (StatusEnum.REVIEW_REJECTED.getCode().equals(status)) {
                return createTodoDTO(context.getUserId(), context.getAdOrgId(),
                        "收款资料有误", "可能会影响您的交易，点击立即处理",
                        "/merchantBusinessInfo/audit", TodoTypeEnum.WRONG_COLLECTION_INFO.getCode(), 0, extend, "去修改");
            }
            if (StatusEnum.TO_NE_MODIFY.getCode().equals(status)) {
                return createTodoDTO(context.getUserId(), context.getAdOrgId(),
                        "收款资料有误", "可能会影响您的交易，点击立即处理",
                        "/merchantBusinessInfo/reject", TodoTypeEnum.WRONG_COLLECTION_INFO.getCode(), 0, extend, "去修改");
            }
        }
        return null;
    }

    @Override
    public String group() {
        return TodoGroupEnum.DEFAULT.getGroup();
    }
}