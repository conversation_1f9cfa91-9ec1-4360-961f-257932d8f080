package cn.lyy.merchant.service.messagecenter.handler;

import cn.lyy.merchant.constants.TodoGroupEnum;
import cn.lyy.merchant.constants.TodoTypeEnum;
import cn.lyy.merchant.dto.positionmessage.TodoDTO;
import cn.lyy.merchant.service.messagecenter.handler.base.AbstractTodoHandler;
import cn.lyy.merchant.service.messagecenter.handler.base.TodoContext;
import com.google.common.collect.Lists;
import com.lyy.charge.dto.bill.ProductNotifyDTO;
import com.lyy.platform.enums.product.isms.NotifyType;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * SAAS订阅缴费提醒
 * <AUTHOR>
 * @date 2024/09/02
 */
@Service
public class SAASProductChargeNotifyHandler extends AbstractTodoHandler {

    @Override
    public TodoDTO handle(TodoContext context) {
        ProductNotifyDTO notifyDTO = context.getNotifyDTO();
        if (notifyDTO == null || BooleanUtils.isNotTrue(notifyDTO.getDisplay())) {
            return null;
        }
        List<Integer> notifyTypes = Lists.newArrayList(NotifyType.CHARGING_MERCHANT_NOTIFY.getCode(),
            NotifyType.CANCEL_TO_STOP_NOTIFY.getCode());
        if (!notifyTypes.contains(notifyDTO.getNotifyType())) {
            return null;
        }
        return createTodoDTO(context.getUserId(), context.getAdOrgId(),
                "企业版SAAS续订提醒", notifyDTO.getContent(),
                "", TodoTypeEnum.SAAS_PRODUCT_CHARGING_NOTIFY.getCode(), 0,null,"立即续订");
    }

    @Override
    public String group() {
        return TodoGroupEnum.DEFAULT.getGroup();
    }
}
