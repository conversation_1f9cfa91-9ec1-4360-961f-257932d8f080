package cn.lyy.merchant.service.messagecenter.impl;

import static java.util.Optional.ofNullable;

import cn.hutool.core.collection.CollUtil;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.lyy_api.AdUserClient;
import cn.lyy.marketing.api.service.ActivityOperationClient;
import cn.lyy.marketing.dto.promotion.ActivityIsEffectDTO;
import cn.lyy.merchant.api.service.EquipmentTransferClient;
import cn.lyy.merchant.api.service.ExperienceMerchantClient;
import cn.lyy.merchant.api.service.MerchantEquipmentService;
import cn.lyy.merchant.api.service.PositionMessageClient;
import cn.lyy.merchant.assembler.consumer.MessageOrTodoAssembler;
import cn.lyy.merchant.assembler.consumer.UmsMerchantPositionMessageAssembler;
import cn.lyy.merchant.config.UmsGrayConfig;
import cn.lyy.merchant.constants.TodoTypeEnum;
import cn.lyy.merchant.dto.AllTodo.AllTodoResDTO;
import cn.lyy.merchant.dto.equipment.param.EquipmentTotalDTO;
import cn.lyy.merchant.dto.merchant.response.WwjEquipmentDTO;
import cn.lyy.merchant.dto.positionmessage.GetALLMessageDTO;
import cn.lyy.merchant.dto.positionmessage.MessageOrTodoDTO;
import cn.lyy.merchant.dto.positionmessage.MessageOrTodoPageDTO;
import cn.lyy.merchant.dto.positionmessage.PositionMessageDTO;
import cn.lyy.merchant.dto.positionmessage.TodoDTO;
import cn.lyy.merchant.dto.request.GetAllMessageOrTodoDTO;
import cn.lyy.merchant.dto.request.GetTopMessageAndTodoDTO;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.service.merchant.MerchantMaterialService;
import cn.lyy.merchant.service.merchant.dto.MerchantInfoStatusDTO;
import cn.lyy.merchant.service.messagecenter.MessageAndTodoService;
import cn.lyy.merchant.service.messagecenter.handler.base.TodoContext;
import cn.lyy.merchant.service.messagecenter.handler.base.TodoHandler;
import cn.lyy.merchant.service.messagecenter.handler.base.TodoHandlerFactory;
import cn.lyy.merchant.service.messagecenter.handler.group.HandlerGroupExecutor;
import cn.lyy.merchant.utils.ResponseCheckUtil;
import cn.lyy.message.UmsPositionMessageClient;
import cn.lyy.message.dto.positionmessage.request.PositionDetailRequest;
import cn.lyy_dto.AdUserInfoDTO;
import com.github.pagehelper.PageInfo;
import com.lyy.equipment.interfaces.feign.equipment.IotEquipmentExtensionServiceFeignClient;
import com.lyy.merchant.applyment.dto.material.response.MerchantInfoStatusRespDTO;
import com.lyy.merchant.bff.feign.merchant.material.MerchantMaterialFeignClient;
import com.lyy.platform.dto.response.inform.PlatformChargeReceiptOrderRemindDTO;
import com.lyy.platform.rpc.PlatformChargeInformApi;
import com.lyy.sysrisk.client.SysRiskClient;
import com.lyy.sysrisk.dto.MerchantStatusQueryRespDTO;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Slf4j
@Service
public class MessageAndTodoServiceImpl implements MessageAndTodoService {


    private static final int TOP_MESSAGE_AND_TODO_NUM = 5; // 展示的前几条消息或待办的数量

    private static final int MESSAGE = 0; //枚举类型为消息

    private static final int TODO = 1; //枚举类型为待办
    private final String participantAppId = "LEYAOYAO";

    @Autowired
    private UmsGrayConfig umsGrayConfig;
    @Autowired
    private UmsPositionMessageClient umsPositionMessageClient;
    @Autowired
    private PositionMessageClient positionMessageClient;
    @Autowired
    private MerchantEquipmentService merchantEquipmentService;
    @Autowired
    private ActivityOperationClient activityOperationClient;
    @Autowired
    private MerchantMaterialFeignClient merchantMaterialFeignClient;
    @Autowired
    private SysRiskClient sysRiskClient;

    @Autowired
    private TodoHandlerFactory todoHandlerFactory;

    @Autowired
    private MerchantMaterialService merchantMaterialService;
    @Autowired
    private EquipmentTransferClient equipmentTransferClient;

    @Autowired
    private IotEquipmentExtensionServiceFeignClient iotEquipmentExtensionServiceFeignClient;

    @Autowired
    private PlatformChargeInformApi platformChargeInformApi;
    @Autowired
    private AdUserClient adUserClient;
    @Autowired
    private ExperienceMerchantClient experienceMerchantClient;

    @Autowired
    private HandlerGroupExecutor handlerGroupExecutor;

    @Autowired
    private MessageOrTodoAssembler messageOrTodoAssembler;

    @Resource
    protected Executor todoMessageContextExecutor;

    @Override
    public MessageOrTodoPageDTO getAllMessageOrTodo(GetAllMessageOrTodoDTO getAllMessageOrTodoDTO) {
        if (getAllMessageOrTodoDTO.getMessageOrTodo() == MESSAGE) {
            // 获取全部消息
            return getAllMessage(getAllMessageOrTodoDTO);
        } else if (getAllMessageOrTodoDTO.getMessageOrTodo() == TODO) {
            // 获取全部待办
            return getAllTodo(getAllMessageOrTodoDTO);
        }
        throw new BusinessException("消息类型错误");
    }

    @Override
    public List<MessageOrTodoDTO> getTopMessageAndTodo(GetTopMessageAndTodoDTO getTopMessageAndTodoDTO) {
        // 获取待办事项
        GetAllMessageOrTodoDTO getAllMessageOrTodoDTO = new GetAllMessageOrTodoDTO();
        getAllMessageOrTodoDTO.setAdUserId(getTopMessageAndTodoDTO.getAdUserId());
        getAllMessageOrTodoDTO.setMerchantId(getTopMessageAndTodoDTO.getMerchantId());
        getAllMessageOrTodoDTO.setLyySwiftpassMerchantId(getTopMessageAndTodoDTO.getLyySwiftpassMerchantId());
        AllTodoResDTO allTodoResDTO = new AllTodoResDTO();
        try {
            allTodoResDTO = doGetAllTodo(getAllMessageOrTodoDTO);
        } catch (Exception e) {
            log.warn("获取待办异常:{}", e.getMessage(), e);
        }
        List<TodoDTO> todoDTOS = ofNullable(allTodoResDTO.getTodoDTOS()).orElse(new ArrayList<>());
        // 新手任务特殊逻辑
        if (BooleanUtils.isTrue(allTodoResDTO.getNewUserTaskFlag())) {
            return convertTodoDTOs(todoDTOS, TODO);
        }
        // 如果待办事项数量足够，则直接返回待办事项
        if (todoDTOS.size() >= TOP_MESSAGE_AND_TODO_NUM) {
            todoDTOS = sortAndLimitTodo(todoDTOS);
            return convertTodoDTOs(todoDTOS, TODO);
        }
        long startTime = System.currentTimeMillis();
        // 仍不满足，获取消息
        getAllMessageOrTodoDTO = new GetAllMessageOrTodoDTO();
        getAllMessageOrTodoDTO.setAdUserId(getTopMessageAndTodoDTO.getAdUserId());
        getAllMessageOrTodoDTO.setMerchantId(getTopMessageAndTodoDTO.getMerchantId());
        getAllMessageOrTodoDTO.setLyySwiftpassMerchantId(getTopMessageAndTodoDTO.getLyySwiftpassMerchantId());
        getAllMessageOrTodoDTO.setPageNo(1);
        getAllMessageOrTodoDTO.setPageSize(TOP_MESSAGE_AND_TODO_NUM - todoDTOS.size());
        getAllMessageOrTodoDTO.setRead(0);
        List<PositionMessageDTO> messageDTOS = new ArrayList<>();
        try {
            messageDTOS = doGetAllMessage(getAllMessageOrTodoDTO).getList();
        } catch (Exception e) {
            log.warn("获取消息异常:{}", e.getMessage(), e);
        }
        log.debug("adUserId:[{}], merchantId:[{}] doGetAllMessage() ===> 耗时[{}]ms", getTopMessageAndTodoDTO.getAdUserId(), getTopMessageAndTodoDTO.getMerchantId(), (System.currentTimeMillis() - startTime));
        // 将待办事项和消息合并
        return sortAndLimitMessageOrTodo(todoDTOS, messageDTOS);
    }

    @Override
    public Boolean hasFirstRegister(Long merchantId) {
        return ResponseUtils.checkResponse(merchantEquipmentService.hasFirstEquipment(merchantId));
    }

    @Override
    public cn.lyy.message.dto.positionmessage.PositionMessageDTO getPositionDetail(Long id, String messageId, cn.lyy.merchant.dto.user.AdUserInfoDTO currentUser) {
        PositionDetailRequest request = new PositionDetailRequest();
        request.setId(id);
        request.setMessageId(messageId);
        request.setAdUserId(currentUser.getAdUserId().toString());
        request.setMerchantId(currentUser.getAdOrgId().toString());
        request.setParticipantAppId(participantAppId);
        return ResponseUtils.checkResponse(umsPositionMessageClient.detail(request));
    }

    public AllTodoResDTO doGetAllTodo(GetAllMessageOrTodoDTO getAllMessageOrTodoDTO) {
        TodoContext todoContext = new TodoContext();
        todoContext.setUserId(getAllMessageOrTodoDTO.getAdUserId());
        todoContext.setAdOrgId(getAllMessageOrTodoDTO.getMerchantId());
        todoContext.setLyySwiftpassMerchantId(getAllMessageOrTodoDTO.getLyySwiftpassMerchantId());
        long startTime = System.currentTimeMillis();
        // 前置部分上下文参数获取
        CompletableFuture<Void> merchantInfoAsync = CompletableFuture.runAsync(() -> {
            // 获取商户信息
            List<MerchantInfoStatusRespDTO> merchantInfoStatusRespDTOS = ResponseCheckUtil.getData(
                    merchantMaterialFeignClient.getMerchantInfoStatus(todoContext.getAdOrgId(), todoContext.getLyySwiftpassMerchantId()));
            log.debug("adUserId:[{}], merchantId:[{}], beforeBuildTodoContext, 获取商户信息 耗时[{}]ms", todoContext.getUserId(), todoContext.getAdOrgId(), System.currentTimeMillis() - startTime);
            todoContext.setMerchantInfoStatusRespDTOS(merchantInfoStatusRespDTOS);
        }, todoMessageContextExecutor);
        CompletableFuture<Void> equipmentAsync = CompletableFuture.runAsync(() -> {
            // 商家设备数据
            List<String> equipmentTypeValueList = getEquipmentTypeValueList(todoContext);
            todoContext.setEquipmentTypeValueList(equipmentTypeValueList);
            log.debug("adUserId:[{}], merchantId:[{}], beforeBuildTodoContext 商家设备数据 耗时[{}]ms", todoContext.getUserId(), todoContext.getAdOrgId(), System.currentTimeMillis() - startTime);
        }, todoMessageContextExecutor);
        CompletableFuture.allOf(merchantInfoAsync, equipmentAsync).join();
        log.debug("adUserId:[{}], merchantId:[{}], beforeBuildTodoContext 前置构建上下文参数 耗时[{}]ms", todoContext.getUserId(), todoContext.getAdOrgId(), System.currentTimeMillis() - startTime);
        // 2、商家设备数据
        return handlerGroupExecutor.executor(todoContext);
    }

    private List<String> getEquipmentTypeValueList(TodoContext todoContext) {
        // 商家设备数据
        List<WwjEquipmentDTO> equipmentList = ResponseUtils.checkResponse(merchantEquipmentService.findListUserEquipments(
                EquipmentTotalDTO.builder()
                        .merchantId(todoContext.getAdOrgId())
                        .adUserId(todoContext.getUserId())
                        .build()));
        // 获取equipmentTypeValue集合
        return equipmentList.stream()
                .map(WwjEquipmentDTO::getTypeValue)
                .collect(Collectors.toList());
    }

    private ActivityIsEffectDTO getActivityEffectDTO(Long adOrgId, List<WwjEquipmentDTO> equipmentList) {
        if (CollUtil.isEmpty(equipmentList)) {
            log.warn("设备列表为空，无法查询活动开关:{}", adOrgId);
            return new ActivityIsEffectDTO();
        }
        List<Long> equipmentTypeIdArr = equipmentList.stream()
                .map(WwjEquipmentDTO::getTypeId)
                .collect(Collectors.toList());
        return ResponseUtils.checkResponse(
                activityOperationClient.getUpgradeActivityIsEffect(adOrgId, equipmentTypeIdArr.stream()
                        .map(Long::intValue)
                        .collect(Collectors.toList())));
    }

    /**
     * 排序并限制待办事项的数量
     */
    public List<TodoDTO> sortAndLimitTodo(List<TodoDTO> todoDTOS) {
        return ofNullable(todoDTOS)
                .map(list -> list.stream()
                        .filter(todo -> todo.getPrimaryOrder() != null && todo.getCreated() != null)
                        .sorted(Comparator.comparing(TodoDTO::getPrimaryOrder)
                                .thenComparing(TodoDTO::getCreated, Comparator.reverseOrder()))
                        .limit(TOP_MESSAGE_AND_TODO_NUM)
                        .collect(Collectors.toList()))
                .orElse(Collections.emptyList());
    }

    public List<MessageOrTodoDTO> sortAndLimitMessageOrTodo(List<TodoDTO> todoDTOS, List<PositionMessageDTO> messageDTOS) {
        // 排序待办事项
        List<TodoDTO> sortedTodos = sortAndLimitTodo(todoDTOS);
        // 将待办事项转换为 MessageOrTodoDTO
        List<MessageOrTodoDTO> messageOrTodoList = convertTodoDTOs(sortedTodos, TODO);

        // 计算剩余的消息数量
        int remainingSlots = TOP_MESSAGE_AND_TODO_NUM - messageOrTodoList.size();
        // 排序并限制消息的数量
        List<PositionMessageDTO> sortedMessages = sortAndLimitMessages(messageDTOS, remainingSlots);
        // 将消息转换为 MessageOrTodoDTO
        List<MessageOrTodoDTO> messageList = convertPositionMessageDTOs(sortedMessages, MESSAGE);

        // 合并待办事项和消息
        messageOrTodoList.addAll(messageList);
        return messageOrTodoList;
    }

    /**
     * 排序并限制消息的数量
     *
     * @param messageDTOS 消息列表
     * @param limit       限制的数量
     * @return 排序并限制后的消息列表
     */
    private List<PositionMessageDTO> sortAndLimitMessages(List<PositionMessageDTO> messageDTOS, int limit) {
        return ofNullable(messageDTOS)
                .map(list -> list.stream()
                        .filter(message -> message.getPrimaryOrder() != null && message.getCreated() != null && message.getRead() != null)
                        .sorted(Comparator.comparing(PositionMessageDTO::getRead)
                                .thenComparing(PositionMessageDTO::getPrimaryOrder)
                                .thenComparing(PositionMessageDTO::getCreated, Comparator.reverseOrder()))
                        .limit(limit)
                        .collect(Collectors.toList()))
                .orElse(Collections.emptyList());
    }

    private List<MessageOrTodoDTO> convertTodoDTOs(List<TodoDTO> list, int messageOrTodoFlag) {
        return list.stream().map(item -> {
            MessageOrTodoDTO messageOrTodoDTO = messageOrTodoAssembler.convertTodoDTO(item);
            messageOrTodoDTO.setMessageOrTodo(messageOrTodoFlag); // 设置 messageOrTodo 标识
            if (TODO == messageOrTodoFlag) {
                messageOrTodoDTO.setCreated(null);
            }
            return messageOrTodoDTO;
        }).collect(Collectors.toList());
    }

    // 转换 PositionMessageDTO
    private List<MessageOrTodoDTO> convertPositionMessageDTOs(List<PositionMessageDTO> list, int messageOrTodoFlag){
        return list.stream().map(item -> {
            MessageOrTodoDTO messageOrTodoDTO = messageOrTodoAssembler.convertPositionMessageDTO(item);
            messageOrTodoDTO.setMessageOrTodo(messageOrTodoFlag); // 设置 messageOrTodo 标识
            if (TODO == messageOrTodoFlag) {
                messageOrTodoDTO.setCreated(null);
            }
            return messageOrTodoDTO;
        }).collect(Collectors.toList());
    }

    public MessageOrTodoPageDTO getAllMessage(GetAllMessageOrTodoDTO getAllMessageOrTodoDTO) {
        // 分页获取站内信
        PageInfo<PositionMessageDTO> responseMessage = doGetAllMessage(getAllMessageOrTodoDTO);
        //类型转换
        MessageOrTodoPageDTO messageOrTodoPageDTO = new MessageOrTodoPageDTO();
        messageOrTodoPageDTO.setTotal((int) responseMessage.getTotal());
        messageOrTodoPageDTO.setTotalPage(responseMessage.getPages());
        messageOrTodoPageDTO.setMessageOrTodoDTOS(convertPositionMessageDTOs(responseMessage.getList(), MESSAGE));
        return messageOrTodoPageDTO;
    }

    public MessageOrTodoPageDTO getAllTodo(GetAllMessageOrTodoDTO getAllMessageOrTodoDTO) {
        //获取全部待办
        List<TodoDTO> todoDTOS = doGetAllTodo(getAllMessageOrTodoDTO).getTodoDTOS();
        //类型转换
        MessageOrTodoPageDTO messageOrTodoPageDTO = new MessageOrTodoPageDTO();
        messageOrTodoPageDTO.setMessageOrTodoDTOS(convertTodoDTOs(todoDTOS, TODO));
        return messageOrTodoPageDTO;
    }


    private PageInfo<PositionMessageDTO> doGetAllMessage(GetAllMessageOrTodoDTO getAllMessageOrTodoDTO) {
        //获取站内信
        GetALLMessageDTO getALLMessageDTO = new GetALLMessageDTO();
        getALLMessageDTO.setMerchantId(getAllMessageOrTodoDTO.getMerchantId());
        getALLMessageDTO.setAdUserId(getAllMessageOrTodoDTO.getAdUserId());
        getALLMessageDTO.setPageNo(getAllMessageOrTodoDTO.getPageNo());
        getALLMessageDTO.setPageSize(getAllMessageOrTodoDTO.getPageSize());
        getALLMessageDTO.setLyySwiftpassMerchantId(getAllMessageOrTodoDTO.getLyySwiftpassMerchantId());
        getALLMessageDTO.setMainType(getAllMessageOrTodoDTO.getMainType());
        getALLMessageDTO.setRead(getAllMessageOrTodoDTO.getRead());
        if (umsGrayConfig.isUmsGray(getAllMessageOrTodoDTO.getMerchantId())) {
            return doGetUmsMessage(getAllMessageOrTodoDTO);
        }
        return ResponseUtils.checkResponse(
                positionMessageClient.pageMerchantMsg(getALLMessageDTO));
    }

    private PageInfo<PositionMessageDTO> doGetUmsMessage(GetAllMessageOrTodoDTO getAllMessageOrTodoDTO) {
        cn.lyy.message.dto.positionmessage.GetALLMessageDTO umsMessageDTO =
                new  cn.lyy.message.dto.positionmessage.GetALLMessageDTO();
        umsMessageDTO.setMerchantId(getAllMessageOrTodoDTO.getMerchantId());
        umsMessageDTO.setAdUserId(getAllMessageOrTodoDTO.getAdUserId());
        umsMessageDTO.setPageNo(getAllMessageOrTodoDTO.getPageNo());
        umsMessageDTO.setPageSize(getAllMessageOrTodoDTO.getPageSize());
        umsMessageDTO.setLyySwiftpassMerchantId(getAllMessageOrTodoDTO.getLyySwiftpassMerchantId());
        umsMessageDTO.setMainType(getAllMessageOrTodoDTO.getMainType());
        umsMessageDTO.setRead(getAllMessageOrTodoDTO.getRead());
        umsMessageDTO.setParticipantAppId(participantAppId);
        PageInfo<cn.lyy.message.dto.positionmessage.PositionMessageDTO> positionMessageDTOPageInfo = ResponseUtils.checkResponse(
                umsPositionMessageClient.pageMerchantMsg(umsMessageDTO));
        List<PositionMessageDTO> positionMessageDTOList = UmsMerchantPositionMessageAssembler.INSTANCE.toPositionMessageDTOList(
                positionMessageDTOPageInfo.getList());
        PageInfo<PositionMessageDTO> pageInfo = new PageInfo<>(positionMessageDTOList);
        pageInfo.setTotal(positionMessageDTOPageInfo.getTotal());
        pageInfo.setPages(positionMessageDTOPageInfo.getPages());
        return pageInfo;
    }
}
