package cn.lyy.merchant.service.messagecenter.handler;

import cn.lyy.merchant.constants.TodoGroupEnum;
import cn.lyy.merchant.constants.TodoTypeEnum;
import cn.lyy.merchant.dto.positionmessage.TodoDTO;
import cn.lyy.merchant.dto.positionmessage.TodoDTO.Extend;
import cn.lyy.merchant.service.merchant.dto.MerchantInfoStatusConstant;
import cn.lyy.merchant.service.merchant.dto.MerchantInfoStatusConstant.StatusEnum;
import cn.lyy.merchant.service.merchant.dto.MerchantInfoStatusDTO;
import cn.lyy.merchant.service.messagecenter.handler.base.AbstractTodoHandler;
import cn.lyy.merchant.service.messagecenter.handler.base.TodoContext;
import com.aliyun.odps.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class OpenCollectionTodoHandler extends AbstractTodoHandler {

    @Override
    public TodoDTO handle(TodoContext context) {
        if (!isApprover(context.getAdUserInfoDTO())) {
            return null;
        }
        MerchantInfoStatusDTO merchantInfoNewUserTaskStatusDTO = context.getMerchantInfoNewUserTaskStatusDTO();
        if (merchantInfoNewUserTaskStatusDTO == null) {
            return null;
        }
        String status = merchantInfoNewUserTaskStatusDTO.getStatus();
        if (merchantInfoNewUserTaskStatusDTO.getIsExperience()) {
            log.debug("商家在体验期,已开通支付：{}", context.getAdOrgId());
            return null;
        }
        if (StringUtils.isNotBlank(status) && !StatusEnum.APPROVED_EFFECTIVE.getCode().equals(status)) {
            Extend extend = new Extend();
            extend.setStatus(status);
            StatusEnum statusEnum = MerchantInfoStatusConstant.StatusEnum.getStatusByCode(status);
            if (statusEnum == null) {
                log.error("ModifyAllRejectTodoHandler,商户资料非法状态：{},{}", status, context.getAdOrgId());
                return null;
            }
            switch (statusEnum) {
                case NOT_SUBMIT_INFO:
                    return createTodoDTO(context.getUserId(), context.getAdOrgId(),
                            "还未开通收款", "开通收款并绑定设备后，就可以开始经营啦",
                            "/group/account/pages/bind-card-instru.html", TodoTypeEnum.OPEN_COLLECTION.getCode(),
                            0, extend, "去绑卡");
                case TO_NE_MODIFY:
                case REVIEW_REJECTED:
                    return createTodoDTO(context.getUserId(), context.getAdOrgId(), "收款资料被驳回",
                            "收款资料被驳回了，请尽快修改",
                            "/group/account/pages/walletBindCard/step-end.html", TodoTypeEnum.OPEN_COLLECTION.getCode(),
                            0, extend, "去修改");
                case IN_REVIEW:
                    return createTodoDTO(context.getUserId(), context.getAdOrgId(),
                            "收款资料审核中", "正在审核您的资料，请1~3个工作日后登录查看",
                            "/group/account/pages/walletBindCard/step-end.html", TodoTypeEnum.OPEN_COLLECTION.getCode(),
                            0, extend, "审核中");
                case MICRO_MERCHANT_SIGN_CONTRACT:
                case WECHAT_INTERNET_CONNECTION:
                case ALIPAY_SIGN_CONTRACT:
                    return createTodoDTO(context.getUserId(), context.getAdOrgId(), "收款渠道待实名", "完成实名认证后，即可开通收款",
                            "/group/account/pages/walletBindCard/step-end.html", TodoTypeEnum.OPEN_COLLECTION.getCode(),
                            0, extend, "去实名");
            }
        }
        return null;
    }

    @Override
    public String group() {
        return TodoGroupEnum.NEW_USER_TASK.getGroup();
    }
}