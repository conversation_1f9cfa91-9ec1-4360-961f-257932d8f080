package cn.lyy.merchant.service.messagecenter.handler.group;

import cn.lyy.merchant.constants.TodoGroupEnum;
import cn.lyy.merchant.dto.AllTodo.AllTodoResDTO;
import cn.lyy.merchant.dto.positionmessage.TodoDTO;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.service.messagecenter.handler.base.TodoContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 待办事项分组执行器
 * @create 2025/7/21 11:33
 */
@Slf4j
@Component
public class HandlerGroupExecutor {

    /**
     * 待办事项分组，有两个组：新手用户组、默认分组
     */
    private final List<TodoHandlerGroup> handlerGroupList;

    @Autowired
    public HandlerGroupExecutor(List<TodoHandlerGroup> handlerGroupList) {
        this.handlerGroupList = handlerGroupList;
    }

    public AllTodoResDTO executor(TodoContext todoContext) {
        if (handlerGroupList.isEmpty()) {
            log.error("待办事项分组初始化异常");
            throw new BusinessException("待办事项分组初始化异常");
        }
        List<TodoDTO> todoDTOS = new ArrayList<>();
        // 标识是否存在新手任务
        boolean newUserTaskFlag = Boolean.FALSE;
        for (TodoHandlerGroup handlerGroup : handlerGroupList) {
            long startTime = System.currentTimeMillis();
            // 1. 构建分组参数
            handlerGroup.buildTodoContext(todoContext);
            // 2. 执行
            List<TodoDTO> list = handlerGroup.executor(todoContext);
            log.debug("adUserId:[{}], merchantId:[{}], [{}] 待办事项分类，存在[{}]个待办 ===> 消耗 [{}]ms", todoContext.getUserId(), todoContext.getAdOrgId(), handlerGroup.group(), list.size(), System.currentTimeMillis() - startTime);
            todoDTOS.addAll(list);
            // 3. 是否继续
            if (!handlerGroup.shouldContinue(todoContext, todoDTOS)) {
                if (Objects.equals(handlerGroup.group(), TodoGroupEnum.NEW_USER_TASK.getGroup())) {
                    newUserTaskFlag = Boolean.TRUE;
                }
                break;
            }
        }
        // 原有逻辑
        todoDTOS = todoDTOS.stream()
                .sorted(Comparator.comparing(TodoDTO::getCreated))
                .collect(Collectors.toList());
        AllTodoResDTO allTodoResDTO = new AllTodoResDTO();
        allTodoResDTO.setTodoDTOS(todoDTOS);
        allTodoResDTO.setNewUserTaskFlag(newUserTaskFlag);
        return allTodoResDTO;
    }
}
