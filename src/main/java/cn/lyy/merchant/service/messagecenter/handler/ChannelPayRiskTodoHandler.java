package cn.lyy.merchant.service.messagecenter.handler;

import cn.lyy.merchant.constant.PositionMessagePrimaryOrderEnum;
import cn.lyy.merchant.constants.TodoGroupEnum;
import cn.lyy.merchant.constants.TodoTypeEnum;
import cn.lyy.merchant.dto.positionmessage.TodoDTO;
import cn.lyy.merchant.service.messagecenter.handler.base.AbstractTodoHandler;
import cn.lyy.merchant.service.messagecenter.handler.base.TodoContext;
import com.lyy.sysrisk.dto.MerchantStatusQueryRespDTO;
import org.springframework.stereotype.Service;

@Service
public class ChannelPayRiskTodoHandler extends AbstractTodoHandler {

    private static final String UN_APPROVE = "unApprove";
    private static final String UN_MODIFY = "unModify";
    private static final String UN_APPEAL = "unAppeal";

    @Override
    public TodoDTO handle(TodoContext context) {
        MerchantStatusQueryRespDTO merchantStatusQueryRespDTO = context.getMerchantStatusQueryRespDTO();
        if(merchantStatusQueryRespDTO == null) {
            return null;
        }
        if (isBan(merchantStatusQueryRespDTO) || isWarning(merchantStatusQueryRespDTO)) {
            String warnAppealStatus = context.getMerchantStatusQueryRespDTO().getWarnStatusDTO().getWarnAppealStatus();
            String text = UN_APPROVE.equals(warnAppealStatus) ? "已申诉，审核中" : "";

            if (isWarning(merchantStatusQueryRespDTO) && UN_MODIFY.equals(warnAppealStatus)) {
                return createTodoDTO(context.getUserId(), context.getAdOrgId(), "渠道风控提醒", "申诉资料审核不通过，请及时处理！" + text,
                        "/appeal/index.html", TodoTypeEnum.CHANNEL_RISK.getCode(), PositionMessagePrimaryOrderEnum.COMMON.getCode(),
                        null, getButtonText(warnAppealStatus,merchantStatusQueryRespDTO));
            }
            else if ((isBanPay(merchantStatusQueryRespDTO) && !isBanAllChannel(merchantStatusQueryRespDTO))
                    || (isWarning(merchantStatusQueryRespDTO) && !isWarningAllChannel(merchantStatusQueryRespDTO)) && UN_APPEAL.equals(
                    warnAppealStatus)) {
                return createTodoDTO(context.getUserId(), context.getAdOrgId(), "渠道风控提醒", "您有设备涉及渠道风险，请及时处理" + text,
                        "/appeal/index.html", TodoTypeEnum.CHANNEL_RISK.getCode(), PositionMessagePrimaryOrderEnum.COMMON.getCode(),
                        null, getButtonText(warnAppealStatus,merchantStatusQueryRespDTO));
            }
            else if ((isBanPay(merchantStatusQueryRespDTO) && !isBanAllChannel(merchantStatusQueryRespDTO))
                    || (isWarning(merchantStatusQueryRespDTO) && !isWarningAllChannel(merchantStatusQueryRespDTO)) && !UN_APPEAL.equals(
                    warnAppealStatus)) {
                return createTodoDTO(context.getUserId(), context.getAdOrgId(), "渠道风控提醒", "您有设备涉及渠道风控！" + text,
                        "/appeal/index.html", TodoTypeEnum.CHANNEL_RISK.getCode(), PositionMessagePrimaryOrderEnum.COMMON.getCode(),
                        null, getButtonText(warnAppealStatus,merchantStatusQueryRespDTO));
            }
            else if (isWarning(merchantStatusQueryRespDTO) &&
                    ((isWarning(merchantStatusQueryRespDTO) && isBanPay(merchantStatusQueryRespDTO) && isBanAllChannel(
                            merchantStatusQueryRespDTO)) || (isWarning(merchantStatusQueryRespDTO) && isWarnType(merchantStatusQueryRespDTO)
                            && isWarningAllChannel(merchantStatusQueryRespDTO)))) {
                return createTodoDTO(context.getUserId(), context.getAdOrgId(), "渠道风控提醒", "触发支付渠道风控，已暂停支付服务，请处理！" + text,
                        "/appeal/index.html", TodoTypeEnum.CHANNEL_RISK.getCode(), PositionMessagePrimaryOrderEnum.URGENCY.getCode(),
                        null, getButtonText(warnAppealStatus,merchantStatusQueryRespDTO));
            }
            else {
                return createTodoDTO(context.getUserId(), context.getAdOrgId(), "渠道风控提醒", "触发支付渠道风控，已暂停支付服务！" + text,
                        "/appeal/index.html", TodoTypeEnum.CHANNEL_RISK.getCode(), PositionMessagePrimaryOrderEnum.URGENCY.getCode(),
                        null, getButtonText(warnAppealStatus,merchantStatusQueryRespDTO));
            }
        }
        return null;
    }

    private String getButtonText(String warnAppealStatus,MerchantStatusQueryRespDTO merchantStatusQueryRespDTO) {
        if (UN_APPROVE.equals(warnAppealStatus)) {
            return "";
        }
        if(isWarning(merchantStatusQueryRespDTO) && UN_MODIFY.equals(warnAppealStatus)){
            return "去处理";
        } else if((isWarning(merchantStatusQueryRespDTO) && isBanPay(merchantStatusQueryRespDTO) && isBanAllChannel(merchantStatusQueryRespDTO))
                ||(isWarningAllChannel(merchantStatusQueryRespDTO) && isWarning(merchantStatusQueryRespDTO) && isWarnType(merchantStatusQueryRespDTO))){
            return "去处理";
        } else if(isBanPay(merchantStatusQueryRespDTO) && isBanAllChannel(merchantStatusQueryRespDTO) && !isWarning(merchantStatusQueryRespDTO)) {
            return "去处理";
        } else if(isWarning(merchantStatusQueryRespDTO)){
            return "去处理";
        } else {
            return "";
        }
    }

    @Override
    public String group() {
        return TodoGroupEnum.DEFAULT.getGroup();
    }
}