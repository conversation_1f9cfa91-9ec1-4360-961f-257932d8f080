package cn.lyy.merchant.service.messagecenter.handler.group;

import cn.lyy.merchant.dto.positionmessage.TodoDTO;
import cn.lyy.merchant.service.messagecenter.handler.base.TodoContext;
import cn.lyy.merchant.service.messagecenter.handler.base.TodoHandler;

import java.util.List;

/**
 * <AUTHOR>
 * @description 待办事项处理器 分组
 * @create 2025/7/21 10:10
 */
public interface TodoHandlerGroup {

    /**
     * 初始化分组 handler
     */
    void initHandlers();

    /**
     * 待办事项分组类型
     *
     * @return 分组类型
     */
    String group();

    /**
     * 获取当前分组的待办事项集合
     *
     * @return 待办事项集合
     */
    List<TodoHandler> getTodoHandlers();

    /**
     * 构建当前待办事项组的上下文参数
     *
     * @param todoContext 上下文参数
     */
    void buildTodoContext(TodoContext todoContext);

    /**
     * 是否可以提前终止获取待办消息
     *
     * @param todoContext 上下文参数
     * @param todoDTOList 待办消息列表
     * @return false 终止; true 继续
     */
    Boolean shouldContinue(TodoContext todoContext, List<TodoDTO> todoDTOList);

    /**
     * 组内执行待办事项 handler
     *
     * @param todoContext 上下文参数
     * @return 待办事项
     */
    List<TodoDTO> executor(TodoContext todoContext);
}
