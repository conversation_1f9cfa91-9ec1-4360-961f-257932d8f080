package cn.lyy.merchant.service.messagecenter.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.lyy.base.util.CollectionUtil;
import cn.lyy.merchant.constants.TodoGroupEnum;
import cn.lyy.merchant.constants.TodoTypeEnum;
import cn.lyy.merchant.dto.positionmessage.TodoDTO;
import cn.lyy.merchant.dto.positionmessage.TodoDTO.Extend;
import cn.lyy.merchant.service.merchant.dto.MerchantInfoStatusConstant.StatusEnum;
import cn.lyy.merchant.service.messagecenter.handler.base.AbstractTodoHandler;
import cn.lyy.merchant.service.messagecenter.handler.base.TodoContext;
import com.lyy.merchant.applyment.dto.material.response.MerchantInfoStatusRespDTO;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

/**
 * 体验期到期提醒待办
 */
@Slf4j
@Service
public class ExperienceExpireNoticeTodoHandler extends AbstractTodoHandler {

    @Override
    public TodoDTO handle(TodoContext context) {
        if (!isApprover(context.getAdUserInfoDTO())) {
            return null;
        }

        List<MerchantInfoStatusRespDTO> merchantInfoStatusRespDTOList = context.getMerchantInfoStatusRespDTOS();
        log.info("体验期预警：{}",merchantInfoStatusRespDTOList);
        if (CollectionUtil.isEmpty(merchantInfoStatusRespDTOList)) {
            log.info("体验期预警返回信息为空");
            return null;
        }
        MerchantInfoStatusRespDTO merchantInfoStatusRespDTO = merchantInfoStatusRespDTOList.stream()
                .filter(v -> StatusEnum.EXPERIENCE_EXPIRE_NOTICE.getCode().equals(v.getStatus())).findFirst().orElse(null);

        if (merchantInfoStatusRespDTO != null) {
            return createTodoDTO(context.getUserId(), context.getAdOrgId(), "支付体验期过期预警",
                    "支付权限剩余【" + merchantInfoStatusRespDTO.getReason() + "】天可用，请及时完善资料并完成实名认证",
                    "", TodoTypeEnum.EXPERIENCE_EXPIRE_NOTICE.getCode(),
                    0, getExtend(merchantInfoStatusRespDTOList), "去操作");
        }
        log.info("体验期预警信息为空");
        return null;
    }

    private Extend getExtend(List<MerchantInfoStatusRespDTO> merchantInfoStatusRespDTOList) {
        MerchantInfoStatusRespDTO realNameResp = merchantInfoStatusRespDTOList.stream()
                .filter(v -> StrUtil.isNotEmpty(v.getStatus()) && StrUtil
                        .equalsAny(v.getStatus(), StatusEnum.MICRO_MERCHANT_SIGN_CONTRACT.getCode(),
                                StatusEnum.WECHAT_INTERNET_CONNECTION.getCode(),
                                StatusEnum.ALIPAY_SIGN_CONTRACT.getCode())).findFirst().orElse(null);
        MerchantInfoStatusRespDTO wrongCollectionResp = merchantInfoStatusRespDTOList.stream()
                .filter(v -> StrUtil.isNotEmpty(v.getStatus()) && StrUtil
                        .equalsAny(v.getStatus(), StatusEnum.REVIEW_REJECTED.getCode(), StatusEnum.TO_NE_MODIFY.getCode())).findFirst()
                .orElse(null);
        Extend extend = new Extend();
        if (realNameResp != null && wrongCollectionResp != null) {
            extend.setStatus(wrongCollectionResp.getStatus());
        } else {
            if (realNameResp != null) {
                extend.setStatus(realNameResp.getStatus());
            }
            if (wrongCollectionResp != null) {
                extend.setStatus(wrongCollectionResp.getStatus());
            }
        }
        return extend;
    }

    @Override
    public String group() {
        return TodoGroupEnum.DEFAULT.getGroup();
    }
}