package cn.lyy.merchant.service.messagecenter.handler.group;

import cn.lyy.base.util.CollectionUtil;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.merchant.constants.TodoGroupEnum;
import cn.lyy.merchant.dto.positionmessage.TodoDTO;
import cn.lyy.merchant.exception.CustomAssert;
import cn.lyy.merchant.service.merchant.dto.MerchantInfoStatusDTO;
import cn.lyy.merchant.service.messagecenter.handler.base.TodoContext;
import cn.lyy.merchant.utils.ResponseCheckUtil;
import cn.lyy_dto.AdUserInfoDTO;
import com.lyy.merchant.applyment.dto.material.response.MerchantInfoStatusRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 新手任务，待办事项分组
 * @create 2025/7/21 10:16
 */
@Order(-1)
@Service("newUserTaskTodoHandlerGroup")
@Slf4j
public class NewUserTaskTodoHandlerGroup extends AbstractTodoHandlerGroup {

    /**
     * 初始化分组
     */
    @PostConstruct
    @Override
    public void initHandlers() {
        this.groupHandlers = handlers.stream()
                .filter(h -> Objects.equals(h.group(), this.group()))
                .collect(Collectors.toList());

        log.debug("NewUserTaskTodoHandlerGroup initialized with handlers size: {}",
                groupHandlers.size());
    }

    /**
     * 返回当前分组类型
     *
     * @return 分组类型
     */
    @Override
    public String group() {
        return TodoGroupEnum.NEW_USER_TASK.getGroup();
    }


    /**
     * 构建参数
     *
     * @param todoContext 上下文参数
     */
    @Override
    public void buildTodoContext(TodoContext todoContext) {
        Long adUserId = todoContext.getUserId();
        Long adOrgId = todoContext.getAdOrgId();
        Long lyySwiftpassMerchantId = todoContext.getLyySwiftpassMerchantId();
        // 1. 是否绑定过设备
        long startTime = System.currentTimeMillis();
        Boolean hasFirstRegister = ResponseUtils.checkResponse(merchantEquipmentService.hasFirstEquipment(adOrgId));
        log.debug("adUserId:[{}], merchantId:[{}], [{}], buildTodoContext 是否绑定过设备 耗时[{}]ms", adUserId, adOrgId, this.group(), System.currentTimeMillis() - startTime);
        // 2. 新手任务-开通收款
        // 依赖商户信息的数据 merchantInfoStatusRespDTOS
        startTime = System.currentTimeMillis();
        MerchantInfoStatusDTO merchantInfoStatusNewUserTaskDTO = merchantMaterialService
                .getMerchantInfoTipStatus(adOrgId, lyySwiftpassMerchantId, todoContext.getMerchantInfoStatusRespDTOS());
        log.debug("adUserId:[{}], merchantId:[{}], [{}], buildTodoContext 新手任务-开通收款 耗时[{}]ms", adUserId, adOrgId, this.group(), System.currentTimeMillis() - startTime);
        log.debug("新手任务进件接口,merchantInfoStatusNewUserTaskDTO:{}", merchantInfoStatusNewUserTaskDTO);
        // 3. 获取是否为主账号
        startTime = System.currentTimeMillis();
        AdUserInfoDTO adUserInfoDTO = ResponseCheckUtil.getData(adUserClient.getAdUserInfo(adOrgId, adUserId));
        log.debug("adUserId:[{}], merchantId:[{}], [{}], buildTodoContext 是否为主账号 耗时[{}]ms", adUserId, adOrgId, this.group(), System.currentTimeMillis() - startTime);
        CustomAssert.assertNotNull(adUserInfoDTO, "获取账号信息失败");
        log.debug("是否为主账号信息,adUserInfoDTO:{}", adUserInfoDTO);
        todoContext.setHasFirstRegister(hasFirstRegister);
        todoContext.setMerchantInfoNewUserTaskStatusDTO(merchantInfoStatusNewUserTaskDTO);
        todoContext.setAdUserInfoDTO(adUserInfoDTO);
    }

    /**
     * 存在新手任务待办，需要终止
     *
     * @param todoContext 上下文参数
     * @param todoDTOList 待办消息列表
     * @return false 终止; true 继续
     */
    @Override
    public Boolean shouldContinue(TodoContext todoContext, List<TodoDTO> todoDTOList) {
        return CollectionUtil.isEmpty(todoDTOList);
    }
}
