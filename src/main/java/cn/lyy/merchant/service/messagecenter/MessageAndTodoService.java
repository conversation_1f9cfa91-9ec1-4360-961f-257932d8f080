package cn.lyy.merchant.service.messagecenter;

import cn.lyy.merchant.dto.positionmessage.MessageOrTodoDTO;
import cn.lyy.merchant.dto.positionmessage.MessageOrTodoPageDTO;
import cn.lyy.merchant.dto.request.GetAllMessageOrTodoDTO;
import cn.lyy.merchant.dto.request.GetTopMessageAndTodoDTO;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import cn.lyy.message.dto.positionmessage.PositionMessageDTO;
import java.util.List;


public interface MessageAndTodoService {

    /**
     * 分页查询消息或待办
     */
    MessageOrTodoPageDTO getAllMessageOrTodo(GetAllMessageOrTodoDTO getAllMessageOrTodoDTO);

    /**
     * 获取首页前n条消息和待办
     */
    List<MessageOrTodoDTO> getTopMessageAndTodo(GetTopMessageAndTodoDTO getTopMessageAndTodoDTO);

    /**
     *
     * @param merchantId
     * @return
     */
    Boolean hasFirstRegister(Long merchantId);

    PositionMessageDTO getPositionDetail(Long id, String messageId, AdUserInfoDTO currentUser);
}
