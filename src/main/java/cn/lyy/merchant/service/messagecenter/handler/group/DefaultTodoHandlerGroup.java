package cn.lyy.merchant.service.messagecenter.handler.group;

import cn.hutool.core.collection.CollUtil;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.merchant.constants.TodoGroupEnum;
import cn.lyy.merchant.dto.equipment.param.EquipmentTotalDTO;
import cn.lyy.merchant.dto.merchant.response.WwjEquipmentDTO;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.service.merchant.dto.MerchantInfoStatusDTO;
import cn.lyy.merchant.service.messagecenter.handler.base.TodoContext;
import cn.lyy.merchant.utils.ResponseCheckUtil;
import cn.lyy_dto.AdUserInfoDTO;
import com.lyy.charge.dto.bill.ProductNotifyDTO;
import com.lyy.charge.enums.bill.ProductCode;
import com.lyy.merchant.applyment.dto.material.response.MerchantInfoStatusRespDTO;
import com.lyy.platform.dto.response.inform.PlatformChargeReceiptOrderRemindDTO;
import com.lyy.sysrisk.dto.MerchantStatusQueryRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 默认待办事项分组
 * @create 2025/7/21 10:16
 */
@Order(1)
@Service("defaultTodoHandlerGroup")
@Slf4j
public class DefaultTodoHandlerGroup extends AbstractTodoHandlerGroup {

    /**
     * 初始化分组
     */
    @PostConstruct
    @Override
    public void initHandlers() {
        this.groupHandlers = handlers.stream()
                .filter(h -> Objects.equals(h.group(), this.group()))
                .collect(Collectors.toList());

        log.debug("DefaultTodoHandlerGroup initialized with handlers size: {}",
                groupHandlers.size());
    }

    /**
     * 当前分组类型
     *
     * @return 分组类型
     */
    @Override
    public String group() {
        return TodoGroupEnum.DEFAULT.getGroup();
    }

    /**
     * 默认分组构建上下文参数
     * 使用多线程并发执行任务，设置了两个线程去构建
     *
     * @param todoContext 上下文参数
     */
    @Override
    public void buildTodoContext(TodoContext todoContext) {
        CompletableFuture<Void> runAsync1 = CompletableFuture.runAsync(() -> {
            long startTime = System.currentTimeMillis();
            // 1. 服务缴费通知标题和内容
            // 依赖 equipmentTypeValueList 数据
            PlatformChargeReceiptOrderRemindDTO platformChargeReceiptOrderRemindDTO = getPlatformChargeReceiptOrderRemindDTO(todoContext);
            todoContext.setPlatformChargeReceiptOrderRemindDTO(platformChargeReceiptOrderRemindDTO);
            log.debug("adUserId:[{}], merchantId:[{}], [{}], buildTodoContext thread1 服务缴费通知 耗时[{}]ms", todoContext.getUserId(), todoContext.getAdOrgId(), this.group(), System.currentTimeMillis() - startTime);
            // 2. 风控状态
            startTime = System.currentTimeMillis();
            log.debug("远程调用风控状态前");
            MerchantStatusQueryRespDTO merchantStatusQueryRespDTO = sysRiskClient.getRiskStatus(String.valueOf(todoContext.getAdOrgId()), "lyy");
            log.debug("远程调用风控状态后,merchantStatusQueryRespDTO:{}", merchantStatusQueryRespDTO);
            if (merchantStatusQueryRespDTO == null) {
                throw new BusinessException("远程调用风控状态失败");
            }
            todoContext.setMerchantStatusQueryRespDTO(merchantStatusQueryRespDTO);
            log.debug("adUserId:[{}], merchantId:[{}], [{}], buildTodoContext thread1 风控状态 耗时[{}]ms", todoContext.getUserId(), todoContext.getAdOrgId(), this.group(), System.currentTimeMillis() - startTime);
        }, todoMessageExecutor);

        CompletableFuture<Void> runAsync2 = CompletableFuture.runAsync(() -> {
            long startTime = System.currentTimeMillis();
            // 3. 收款资料有误
            // 依赖 merchantInfoStatusRespDTOS 数据, 新手任务构建上下文时获取
            List<MerchantInfoStatusRespDTO> merchantInfoStatusRespDTOS = todoContext.getMerchantInfoStatusRespDTOS();
            MerchantInfoStatusDTO merchantInfoStatusDTO = merchantMaterialService
                    .getMerchantInfoStatus(todoContext.getAdOrgId(), todoContext.getLyySwiftpassMerchantId(), merchantInfoStatusRespDTOS);
            log.debug("正常进件接口,merchantInfoStatusDTO:{}", merchantInfoStatusDTO);
            todoContext.setMerchantInfoStatusDTO(merchantInfoStatusDTO);
            log.debug("adUserId:[{}], merchantId:[{}], [{}], buildTodoContext thread2 收款资料 耗时[{}]ms", todoContext.getUserId(), todoContext.getAdOrgId(), this.group(), System.currentTimeMillis() - startTime);

            // 4. SAAS续订提醒
            startTime = System.currentTimeMillis();
            ProductNotifyDTO notifyDTO = ResponseCheckUtil.getDataSkipError(productSubscribeClient.getProductRemind(todoContext.getAdOrgId(),
                    ProductCode.SAAS.getCode(), todoContext.getUserId()));
            todoContext.setNotifyDTO(notifyDTO);
            log.debug("adUserId:[{}], merchantId:[{}], [{}], buildTodoContext thread2 SAAS续订提醒 耗时[{}]ms", todoContext.getUserId(), todoContext.getAdOrgId(), this.group(), System.currentTimeMillis() - startTime);
        }, todoMessageExecutor);
        CompletableFuture.allOf(runAsync1, runAsync2).join();
    }

    private PlatformChargeReceiptOrderRemindDTO getPlatformChargeReceiptOrderRemindDTO(TodoContext todoContext) {
        // 1. 是否有动态柜
        boolean getDynamicArkIsExist = false;
        List<String> equipmentTypeValueList = todoContext.getEquipmentTypeValueList();
        if (CollUtil.isNotEmpty(equipmentTypeValueList) && equipmentTypeValueList.contains("SHJ")) {
            List<Long> equipIds = ResponseCheckUtil.getData(merchantEquipmentService.findEquipmentIdsByMerchantId(todoContext.getAdOrgId(), Boolean.TRUE));
            if (CollectionUtils.isEmpty(equipIds)) {
                log.warn("商家没有设备ID，adOrgId:{}", todoContext.getAdOrgId());
            } else {
                Integer countDynamic = ResponseCheckUtil.getData(
                        iotEquipmentExtensionServiceFeignClient.countByBusinessTypeAndEids(82, equipIds));
                if (countDynamic > 0) {
                    getDynamicArkIsExist = true;
                }
            }
        }
        // 2. 服务缴费相关
        AdUserInfoDTO adUserInfoDTO = todoContext.getAdUserInfoDTO();
        PlatformChargeReceiptOrderRemindDTO platformChargeReceiptOrderRemindDTO;
        // 获取缴费通知标题和内容
        Long chargeAdUserId = todoContext.getUserId();
        if ("Y".equals(adUserInfoDTO.getIsApprover())) {
            chargeAdUserId = null;
        }
        if (getDynamicArkIsExist) {
            platformChargeReceiptOrderRemindDTO = ResponseCheckUtil.getData(
                    platformChargeInformApi.findVendingChargeReceiptOrderRemind(10000L, todoContext.getAdOrgId(), chargeAdUserId, 1026L));
        } else {
            platformChargeReceiptOrderRemindDTO = ResponseCheckUtil.getData(
                    platformChargeInformApi.findChargeReceiptOrderRemind(10000L, todoContext.getAdOrgId(), chargeAdUserId));
        }
        log.debug("服务费缴费相关信息, platformChargeReceiptOrderRemindDTO:{}", platformChargeReceiptOrderRemindDTO);
        return platformChargeReceiptOrderRemindDTO;
    }
}
