package cn.lyy.merchant.service.messagecenter.handler;

import cn.hutool.core.util.StrUtil;
import cn.lyy.merchant.constants.TodoGroupEnum;
import cn.lyy.merchant.constants.TodoTypeEnum;
import cn.lyy.merchant.dto.positionmessage.TodoDTO;
import cn.lyy.merchant.dto.positionmessage.TodoDTO.Extend;
import cn.lyy.merchant.service.merchant.dto.MerchantInfoStatusConstant.StatusEnum;
import cn.lyy.merchant.service.messagecenter.handler.base.AbstractTodoHandler;
import cn.lyy.merchant.service.messagecenter.handler.base.TodoContext;
import com.aliyun.odps.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

/**
 * 实名待办
 */
@Slf4j
@Service
public class RealNameTodoHandler extends AbstractTodoHandler {

    @Override
    public TodoDTO handle(TodoContext context) {
        String status = context.getMerchantInfoStatusDTO().getStatus();
        if (StringUtils.isNotBlank(status) && StrUtil
                .equalsAny(status, StatusEnum.MICRO_MERCHANT_SIGN_CONTRACT.getCode(), StatusEnum.WECHAT_INTERNET_CONNECTION.getCode(),
                        StatusEnum.ALIPAY_SIGN_CONTRACT.getCode())) {
            Extend extend = new Extend();
            extend.setStatus(status);
            return createTodoDTO(context.getUserId(), context.getAdOrgId(), "收款渠道待实名", "完成实名认证后，即可开通收款",
                    "", TodoTypeEnum.REAL_NAME.getCode(),
                    0, extend, "去实名");
        }
        return null;
    }

    @Override
    public String group() {
        return TodoGroupEnum.DEFAULT.getGroup();
    }
}