package cn.lyy.merchant.service.messagecenter.handler.base;

import cn.lyy.merchant.dto.positionmessage.TodoDTO;
import cn.lyy.merchant.dto.positionmessage.TodoDTO.Extend;
import cn.lyy_dto.AdUserInfoDTO;
import com.lyy.platform.dto.response.inform.PlatformChargeReceiptOrderRemindDTO;
import com.lyy.sysrisk.dto.BannedStatusDTO;
import com.lyy.sysrisk.dto.MerchantStatusQueryRespDTO;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/05/28
 */
public abstract class AbstractTodoHandler implements TodoHandler {

    private static final String WARN_STATUS = "Warn";
    private static final String BAN_PAY_TYPE = "pay";
    private static final String STATUS_BAN = "Ban";
    private static final String STATUS_BLACK = "Black";
    private static final String ALL_CHANNEL = "all";

    protected boolean hasRemind(PlatformChargeReceiptOrderRemindDTO platformChargeReceiptOrderRemindDTO) {
        return platformChargeReceiptOrderRemindDTO != null && platformChargeReceiptOrderRemindDTO.getBannerContent() != null;
    }

    protected boolean isApprover(AdUserInfoDTO adUserInfoDTO){
        return adUserInfoDTO != null && "Y".equals(adUserInfoDTO.getIsApprover());
    }

    protected boolean isWarning(MerchantStatusQueryRespDTO merchantStatusQueryRespDTO) {
        return WARN_STATUS.equals(merchantStatusQueryRespDTO.getWarnStatusDTO().getWarnStatus());
    }

    protected boolean isBanPay(MerchantStatusQueryRespDTO merchantStatusQueryRespDTO) {
        return merchantStatusQueryRespDTO.getBannedStatusDTOList().stream()
                .filter(item -> BAN_PAY_TYPE.equals(item.getBannedType()))
                .map(BannedStatusDTO::getWindStatus)
                .anyMatch(status -> Arrays.asList(STATUS_BAN, STATUS_BLACK).contains(status));
    }

    protected boolean isBanAllChannel(MerchantStatusQueryRespDTO merchantStatusQueryRespDTO) {
        return ALL_CHANNEL.equals(merchantStatusQueryRespDTO.getBannedPayChannelStatus());
    }

    protected boolean isWarningAllChannel(MerchantStatusQueryRespDTO merchantStatusQueryRespDTO) {
        return ALL_CHANNEL.equals(merchantStatusQueryRespDTO.getWarnPayChannelStatus());
    }

    protected boolean isWarnType(MerchantStatusQueryRespDTO merchantStatusQueryRespDTO) {
        return "channel".equals(merchantStatusQueryRespDTO.getWarnStatusDTO().getWarnType());
    }

    protected boolean isBan(MerchantStatusQueryRespDTO merchantStatusQueryRespDTO) {
        return (isBanPay(merchantStatusQueryRespDTO) && isBanAllChannel(merchantStatusQueryRespDTO)) ||
                (isWarning(merchantStatusQueryRespDTO) && isWarnType(merchantStatusQueryRespDTO) && isWarningAllChannel(merchantStatusQueryRespDTO));
    }

    protected TodoDTO createTodoDTO(Long userId, Long adOrgId, String title, String msgContent, String pageUrl, int todoType,
            int primaryOrder, Extend extend, String buttonText) {
        TodoDTO todoDTO = new TodoDTO();
        todoDTO.setAdUserId(userId);
        todoDTO.setMerchantId(adOrgId);
        todoDTO.setTitle(title);
        todoDTO.setMsgContent(msgContent);
        todoDTO.setPrimaryOrder(primaryOrder);
        todoDTO.setPageUrl(pageUrl);
        todoDTO.setTodoType(todoType);
        todoDTO.setButtonText(buttonText);
        todoDTO.setExtend(extend);
        todoDTO.setCreated(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        return todoDTO;
    }
}
