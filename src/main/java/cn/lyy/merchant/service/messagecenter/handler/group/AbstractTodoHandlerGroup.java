package cn.lyy.merchant.service.messagecenter.handler.group;

import cn.lyy.lyy_api.AdUserClient;
import cn.lyy.merchant.api.service.MerchantEquipmentService;
import cn.lyy.merchant.dto.positionmessage.TodoDTO;
import cn.lyy.merchant.service.merchant.MerchantMaterialService;
import cn.lyy.merchant.service.messagecenter.handler.base.TodoContext;
import cn.lyy.merchant.service.messagecenter.handler.base.TodoHandler;
import com.lyy.charge.rpc.product.ProductSubscribeClient;
import com.lyy.equipment.interfaces.feign.equipment.IotEquipmentExtensionServiceFeignClient;
import com.lyy.merchant.bff.feign.merchant.material.MerchantMaterialFeignClient;
import com.lyy.platform.rpc.PlatformChargeInformApi;
import com.lyy.sysrisk.client.SysRiskClient;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 * @description 待办事项处理器分组
 * @create 2025/7/21 10:52
 */
public abstract class AbstractTodoHandlerGroup implements TodoHandlerGroup {
    @Autowired
    protected List<TodoHandler> handlers;

    @Autowired
    protected MerchantEquipmentService merchantEquipmentService;

    @Autowired
    protected MerchantMaterialFeignClient merchantMaterialFeignClient;

    @Autowired
    protected MerchantMaterialService merchantMaterialService;

    @Autowired
    protected AdUserClient adUserClient;

    @Autowired
    protected SysRiskClient sysRiskClient;

    @Autowired
    protected IotEquipmentExtensionServiceFeignClient iotEquipmentExtensionServiceFeignClient;

    @Autowired
    protected PlatformChargeInformApi platformChargeInformApi;

    @Autowired
    protected ProductSubscribeClient productSubscribeClient;

    @Resource
    protected Executor todoMessageExecutor;

    protected List<TodoHandler> groupHandlers;

    /**
     * 默认继续
     * @param todoContext 上下文参数
     * @param todoDTOList 待办消息列表
     * @return false 终止; true 继续
     */
    @Override
    public Boolean shouldContinue(TodoContext todoContext, List<TodoDTO> todoDTOList) {
        // 当前业务逻辑，默认继续
        return Boolean.TRUE;
    }

    /**
     * 获取当前分组下的handler
     * @return 分组handler
     */
    @Override
    public List<TodoHandler> getTodoHandlers() {
        return this.groupHandlers;
    }

    /**
     * 组内执行待办事项 handler
     * @param todoContext 上下文参数
     * @return 待办事项
     */
    @Override
    public List<TodoDTO> executor(TodoContext todoContext) {
        List<TodoDTO> list = new ArrayList<>();
        for (TodoHandler todoHandler : getTodoHandlers()) {
            TodoDTO dto = todoHandler.handle(todoContext);
            if (dto != null) {
                list.add(dto);
            }
        }
        return list;
    }
}
