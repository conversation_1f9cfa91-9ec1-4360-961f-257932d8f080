package cn.lyy.merchant.service.messagecenter.handler.base;

import cn.lyy.merchant.service.messagecenter.handler.BindEquipmentTodoHandler;
import cn.lyy.merchant.service.messagecenter.handler.ChannelPayRiskTodoHandler;
import cn.lyy.merchant.service.messagecenter.handler.ChargingRemindTodoHandler;
import cn.lyy.merchant.service.messagecenter.handler.ExperienceExpireNoticeTodoHandler;
import cn.lyy.merchant.service.messagecenter.handler.OpenCollectionTodoHandler;
import cn.lyy.merchant.service.messagecenter.handler.RealNameTodoHandler;
import cn.lyy.merchant.service.messagecenter.handler.SAASProductChargeNotifyHandler;
import cn.lyy.merchant.service.messagecenter.handler.WrongCollectionInfoTodoHandler;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/09/02
 */
@Slf4j
@Component
public class TodoHandlerFactory {

    private final List<TodoHandler> handlers = new ArrayList<>();

    @Autowired
    public TodoHandlerFactory(ApplicationContext context) {
        // 新手任务-绑定设备
        handlers.add(context.getBean(BindEquipmentTodoHandler.class));
        // 新手任务-开通收款
        handlers.add(context.getBean(OpenCollectionTodoHandler.class));
        // 体验期过期预警
        handlers.add(context.getBean(ExperienceExpireNoticeTodoHandler.class));
        // SAAS续订提醒
        handlers.add(context.getBean(SAASProductChargeNotifyHandler.class));
        // 实名提醒
        handlers.add(context.getBean(RealNameTodoHandler.class));
        // 渠道风控
        handlers.add(context.getBean(ChannelPayRiskTodoHandler.class));
        // 服务费缴费
        handlers.add(context.getBean(ChargingRemindTodoHandler.class));
        // 收款资料有误
        handlers.add(context.getBean(WrongCollectionInfoTodoHandler.class));
    }


    public List<TodoHandler> getHandlers() {
        if (CollectionUtils.isEmpty(handlers)) {
            log.warn("首页待办处理类初始化失败");
            return null;
        }
        return handlers;
    }
}
