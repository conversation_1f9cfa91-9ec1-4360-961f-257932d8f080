package cn.lyy.merchant.service.messagecenter.handler;

import cn.lyy.merchant.constant.PositionMessagePrimaryOrderEnum;
import cn.lyy.merchant.constants.TodoGroupEnum;
import cn.lyy.merchant.constants.TodoTypeEnum;
import cn.lyy.merchant.dto.positionmessage.TodoDTO;
import cn.lyy.merchant.service.messagecenter.handler.base.AbstractTodoHandler;
import cn.lyy.merchant.service.messagecenter.handler.base.TodoContext;
import org.springframework.stereotype.Service;

@Service
public class ChargingRemindTodoHandler extends AbstractTodoHandler {

    @Override
    public TodoDTO handle(TodoContext context) {
        if (!isApprover(context.getAdUserInfoDTO())) {
            return null;
        }
        if (hasRemind(context.getPlatformChargeReceiptOrderRemindDTO())
        ) {
            String title = context.getPlatformChargeReceiptOrderRemindDTO().getRemindType();
            String msgContent = context.getPlatformChargeReceiptOrderRemindDTO().getBannerContent();
            return createTodoDTO(context.getUserId(), context.getAdOrgId(), title, msgContent,
                    "/pluginCenterModule/servicePay/home", TodoTypeEnum.CHARGING_REMIND.getCode(),
                    PositionMessagePrimaryOrderEnum.COMMON.getCode(), null, "去缴费");
        }
        return null;
    }

    @Override
    public String group() {
        return TodoGroupEnum.DEFAULT.getGroup();
    }
}