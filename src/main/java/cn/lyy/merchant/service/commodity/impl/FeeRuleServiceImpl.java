package cn.lyy.merchant.service.commodity.impl;

import static java.util.Optional.ofNullable;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.base.util.StringUtil;
import cn.lyy.equipment.dto.EquipmentInfoDTO;
import cn.lyy.equipment.dto.equipment.EquipmentDTO;
import cn.lyy.equipment.dto.equipment.EquipmentTypeDTO;
import cn.lyy.equipment.dto.equipment.EquipmentTypeFunctionDTO;
import cn.lyy.equipment.dto.equipment.MainboardServicePackageDTO;
import cn.lyy.equipment.service.IEquipmentAttachService;
import cn.lyy.equipment.service.IEquipmentService;
import cn.lyy.equipment.service.IEquipmentTypeService;
import cn.lyy.equipment.service.MainboardMicroService;
import cn.lyy.equipment.service.ProtocolMicroService;
import cn.lyy.merchant.api.service.FeeRuleClient;
import cn.lyy.merchant.api.service.MerchantEquipmentService;
import cn.lyy.merchant.api.service.RegisterTemplateClient;
import cn.lyy.merchant.constants.BusinessExceptionEnums;
import cn.lyy.merchant.constants.ChargingConstants;
import cn.lyy.merchant.constants.EquipmentTypeEnums;
import cn.lyy.merchant.constants.FeeModeEnum;
import cn.lyy.merchant.constants.FeeRuleEnum;
import cn.lyy.merchant.constants.FeeRuleModeSettingEnum;
import cn.lyy.merchant.constants.FeeRuleModeTimeElecEnum;
import cn.lyy.merchant.constants.ProtocolCodeEnum;
import cn.lyy.merchant.constants.SystemRoleEnum;
import cn.lyy.merchant.dto.equipment.CommoditySettingsDTO;
import cn.lyy.merchant.dto.equipment.CommoditySettingsSaveDTO;
import cn.lyy.merchant.dto.equipment.EquipmentChargingTimeRuleDTO;
import cn.lyy.merchant.dto.merchant.MerchantEquipmentDTO;
import cn.lyy.merchant.dto.request.BatchFeeRuleSaveDTO;
import cn.lyy.merchant.dto.request.EquipmentRegisterDTO;
import cn.lyy.merchant.dto.request.FeeCommodityDTO;
import cn.lyy.merchant.dto.request.FeeRuleComposeDTO;
import cn.lyy.merchant.dto.request.FeeRuleSaveReqDTO;
import cn.lyy.merchant.dto.request.PriceValueDTO;
import cn.lyy.merchant.dto.request.RuleDelDTO;
import cn.lyy.merchant.dto.request.SetProDTO;
import cn.lyy.merchant.dto.request.UpdateFeeModeDTO;
import cn.lyy.merchant.dto.response.FeeModeVO;
import cn.lyy.merchant.dto.response.ListFeeModeDTO;
import cn.lyy.merchant.dto.template.EquipmentRegisterTmplDTO;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.microservice.CommodityRelatedService;
import cn.lyy.merchant.microservice.CommoditySkuService;
import cn.lyy.merchant.service.business.SaasBusinessFactory;
import cn.lyy.merchant.service.commodity.FeeRuleService;
import cn.lyy.merchant.service.equipment.EquipmentOperateService;
import cn.lyy.merchant.service.feerule.AbstractFeeRuleBusinessProcess;
import cn.lyy.merchant.utils.RemoteResponseUtils;
import cn.lyy.tools.constants.merchant.EquipmentTypeConstant;
import cn.lyy.tools.equipment.LyyConstant;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.lyy.commodity.rpc.constants.CategoryEnum;
import com.lyy.commodity.rpc.constants.ChannelEnum;
import com.lyy.commodity.rpc.constants.ClassifyCodeEnum;
import com.lyy.commodity.rpc.constants.ValueUnitEnum;
import com.lyy.commodity.rpc.dto.EquipmentDisplayDTO;
import com.lyy.commodity.rpc.dto.request.CommodityDTO;
import com.lyy.commodity.rpc.dto.request.CommodityFixPriceDTO;
import com.lyy.commodity.rpc.dto.request.CommodityFixPriceValueDTO;
import com.lyy.commodity.rpc.dto.request.DisplayReqDTO;
import com.lyy.commodity.rpc.dto.request.EquipmentUnbindDTO;
import com.lyy.commodity.rpc.dto.request.RelateEquipmentDTO;
import com.lyy.commodity.rpc.dto.request.SetUseDTO;
import com.lyy.commodity.rpc.dto.request.SkuCommodityDTO;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import com.lyy.equipment.interfaces.dto.equipment.resp.EquipmentInfoByValuesDTO;
import com.lyy.equipment.interfaces.feign.equipment.IotEquipmentServiceFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

/**
 * 类描述：计费规则
 * <p>
 *
 * <AUTHOR>
 * @since 2020/11/18 16:34
 */
@Slf4j
@Service
public class FeeRuleServiceImpl implements FeeRuleService {

    @Resource
    private CommodityRelatedService commodityRelatedService;

    @Resource
    private CommoditySkuService commoditySkuService;

    @Resource
    private IEquipmentTypeService equipmentTypeService;

    @Resource
    private IEquipmentService equipmentService;

    @Resource
    private IEquipmentAttachService equipmentAttachService;

    @Resource
    private MerchantEquipmentService merchantEquipmentService;

    @Resource
    private RegisterTemplateClient registerTemplateClient;

    @Resource
    private ProtocolMicroService protocolMicroService;

    @Resource
    private EquipmentOperateService equipmentOperateService;

    @Resource
    private MainboardMicroService mainboardMicroService;

    @Autowired
    IotEquipmentServiceFeignClient iotEquipmentServiceFeignClient;

    /**
     * 批处理最大数量
     */
    private final Integer maxBatchNum = 400;

    @Autowired
    private FeeRuleClient feeRuleClient;

    private final Set<String> generalPulseSet = new HashSet<String>(){{
        // 游戏机
        add("YXJ");
        // 儿童类
        add("ETL");
        // 售水机
        add("SSJ");
        // 拳击机
        add("QJJ");
    }};

    @Override
    public List<FeeCommodityDTO> listByCondition(Long distributorId, Long groupId,
                                                 Long equipmentTypeId, Long equipmentId,
                                                 String categoryCode, String classifyCode) {
        return listByCondition(distributorId, groupId, equipmentTypeId, equipmentId, categoryCode, classifyCode, false);
    }

    @Override
    public List<FeeCommodityDTO> listByCondition(Long distributorId, Long groupId, Long equipmentTypeId, Long equipmentId, String categoryCode, String classifyCode, Boolean isCompose) {
        DisplayReqDTO param = new DisplayReqDTO();
        param.setDistributor(distributorId);
        param.setStore(groupId);
        param.setEquipmentType(equipmentTypeId);
        param.setEquipment(equipmentId);
        param.setClassifyCode(classifyCode);
        param.setCategoryCode(categoryCode);
        // 查询设备注册商品列表
        List<EquipmentDisplayDTO>  commoditys= RemoteResponseUtils.getData(commodityRelatedService.selectDisplay(param));
        if(CollectionUtils.isEmpty(commoditys)){
            return Collections.emptyList();
        }
        List<FeeCommodityDTO> feeCommoditys = convertToRule(commoditys);
        if(Objects.nonNull(equipmentId)){
            CommoditySettingsDTO commoditySettings= RemoteResponseUtils.getData(merchantEquipmentService.queryCommoditySettings(distributorId, equipmentId));
            log.debug("加载商品配置信息,equipmentId:{},commoditySettings:{}",equipmentId,commoditySettings);
            feeCommoditys.forEach(feeCommodityDTO -> {
                feeCommodityDTO.setIsShowTitle(handleShowTitle(commoditySettings));
                // 设置是否显示单价
                feeCommodityDTO.setIsShowFeeRuleUnit(handleShowUnit(commoditySettings));
            });
        }
        return feeCommoditys;
    }

    private Boolean handleShowUnit(CommoditySettingsDTO commoditySettings) {
        if(Objects.isNull(commoditySettings)){
            return false;
        }
      if(commoditySettings.getShowUnit()!= null && commoditySettings.getShowUnit() == 1) {
           return true;
        }
       return  false;
    }

    private Boolean handleShowTitle(CommoditySettingsDTO commoditySettings) {
        if(Objects.isNull(commoditySettings)){
            return false;
        }
        // 是否展示标题：0/1
        if (commoditySettings.getShowTitle() != null && commoditySettings.getShowTitle() == 1) {
           return true;
        }
        return false;
    }

    @Override
    public void saveOrUpdate(FeeRuleSaveReqDTO feeRuleSaveReqDTO, Long distributorId, Long adUserId) {
        EquipmentInfoDTO equipmentInfoDTO = equipmentService.getEquipmentInfoById(feeRuleSaveReqDTO.getEquipmentId()).getData();
        if (equipmentInfoDTO == null) {
            throw new BusinessException(BusinessExceptionEnums.DEVICE_NOT_EXISTS);
        }
        Long equipmentTypeId = null;
        if(StringUtils.isEmpty(feeRuleSaveReqDTO.getEquipmentTypeValue())){
            equipmentTypeId = equipmentInfoDTO.getEquipmentTypeId().longValue();
        }else{
            //设备类型按指定的来
            BaseResponse<EquipmentTypeDTO> equipmentTypeBaseResponse = equipmentTypeService.getByValue(feeRuleSaveReqDTO.getEquipmentTypeValue());
            equipmentTypeId =  Optional.ofNullable(equipmentTypeBaseResponse).filter(r -> r.getCode() == ResponseCodeEnum.SUCCESS.getCode()).map(r -> r.getData().getEquipmentTypeId()).orElseThrow(()-> new BusinessException(equipmentTypeBaseResponse.getMessage()));
        }

        if (feeRuleSaveReqDTO.getDetailId() == null) {
            // 如果包含组合商品，先保存子商品
            List<Long> composeIdList = null;
            if (CollectionUtils.isNotEmpty(feeRuleSaveReqDTO.getComposes())) {
                composeIdList = saveCompose(feeRuleSaveReqDTO, distributorId,
                        equipmentInfoDTO.getEquipmentGroupId().longValue(), adUserId, equipmentTypeId);
            }
            // 新增计费规则
            SkuCommodityDTO skuCommodityDTO = new SkuCommodityDTO();
            skuCommodityDTO.setDistributor(distributorId);
            skuCommodityDTO.setAdUser(adUserId);

            // 设置关联设备

            RelateEquipmentDTO relateEquipment = new RelateEquipmentDTO();
            relateEquipment.setEquipment(feeRuleSaveReqDTO.getEquipmentId());
            relateEquipment.setEquipmentType(equipmentTypeId);
            relateEquipment.setStore(equipmentInfoDTO.getEquipmentGroupId().longValue());
            skuCommodityDTO.setEquipment(relateEquipment);

            // 设置商品

            // 定价
            CommodityFixPriceDTO fixPrice = new CommodityFixPriceDTO();
            fixPrice.setPrice(feeRuleSaveReqDTO.getPrice());
            fixPrice.setRangeCategory(feeRuleSaveReqDTO.getRangeCategory());
            fixPrice.setStart(feeRuleSaveReqDTO.getRangeStart());
            fixPrice.setEnd(feeRuleSaveReqDTO.getRangeEnd());
            List<CommodityFixPriceValueDTO> fixPriceValueDTOList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(feeRuleSaveReqDTO.getFixPriceValueList())) {
                feeRuleSaveReqDTO.getFixPriceValueList().stream().forEach(fixPriceValueDTO -> {
                    CommodityFixPriceValueDTO commodityFixPriceValueDTO = new CommodityFixPriceValueDTO();
                    commodityFixPriceValueDTO.setValue(fixPriceValueDTO.getValue());
                    commodityFixPriceValueDTO.setUnit(fixPriceValueDTO.getValueUnit());
                    fixPriceValueDTOList.add(commodityFixPriceValueDTO);
                });
            }
            fixPrice.setFixPriceValueList(fixPriceValueDTOList);

            // 商品
            CommodityDTO commodity = new CommodityDTO();
            commodity.setCategoryCode(ofNullable(feeRuleSaveReqDTO.getCategoryCode())
                    .orElse(CategoryEnum.DEVICE_SERVICE.getCode()));
            commodity.setChannel(ChannelEnum.H5.getId());
            commodity.setName(ofNullable(feeRuleSaveReqDTO.getTitle()).orElse("商品"));
            commodity.setClassifyCode(feeRuleSaveReqDTO.getClassifyCode());
            commodity.setFixPrice(fixPrice);
            commodity.setComposes(composeIdList);
            skuCommodityDTO.setCommodities(commodity);

            log.debug("[新增计费规则] 参数: -> {} ", JSON.toJSONString(skuCommodityDTO));
            BaseResponse resp = commoditySkuService.addCommodity(skuCommodityDTO);
            if (resp.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
                log.error("[新增计费规则] 调用商品中心保存商品结果 -> {}", resp);
                throw new BusinessException(resp.getMessage());
            }
        } else {
            List<Long> composeIdList = null;
            if (CollectionUtils.isNotEmpty(feeRuleSaveReqDTO.getComposes())) {
                composeIdList = saveCompose(feeRuleSaveReqDTO, distributorId,
                        equipmentInfoDTO.getEquipmentGroupId().longValue(), adUserId, equipmentTypeId);
            }
            // 更新计费规则
            SkuCommodityDTO skuCommodityDTO = new SkuCommodityDTO();
            skuCommodityDTO.setDistributor(distributorId);
            skuCommodityDTO.setAdUser(adUserId);

            // 设置关联设备
            RelateEquipmentDTO relateEquipment = new RelateEquipmentDTO();
            relateEquipment.setEquipment(feeRuleSaveReqDTO.getEquipmentId());
            relateEquipment.setEquipmentType(equipmentTypeId);
            relateEquipment.setStore(equipmentInfoDTO.getEquipmentGroupId().longValue());
            skuCommodityDTO.setEquipment(relateEquipment);

            // 设置商品
            // 定价
            CommodityFixPriceDTO fixPrice = new CommodityFixPriceDTO();
            fixPrice.setPrice(feeRuleSaveReqDTO.getPrice());
            fixPrice.setRangeCategory(feeRuleSaveReqDTO.getRangeCategory());
            fixPrice.setStart(feeRuleSaveReqDTO.getRangeStart());
            fixPrice.setEnd(feeRuleSaveReqDTO.getRangeEnd());
            List<CommodityFixPriceValueDTO> fixPriceValueDTOList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(feeRuleSaveReqDTO.getFixPriceValueList())) {
                feeRuleSaveReqDTO.getFixPriceValueList().stream().forEach(fixPriceValueDTO -> {
                    CommodityFixPriceValueDTO commodityFixPriceValueDTO = new CommodityFixPriceValueDTO();
                    commodityFixPriceValueDTO.setValue(fixPriceValueDTO.getValue());
                    commodityFixPriceValueDTO.setUnit(fixPriceValueDTO.getValueUnit());
                    fixPriceValueDTOList.add(commodityFixPriceValueDTO);
                });
                fixPrice.setFixPriceValueList(fixPriceValueDTOList);
            }
            // 商品
            CommodityDTO commodity = new CommodityDTO();
            commodity.setDisplay(feeRuleSaveReqDTO.getDetailId());
            commodity.setCategoryCode(ofNullable(feeRuleSaveReqDTO.getCategoryCode())
                    .orElse(CategoryEnum.DEVICE_SERVICE.getCode()));
            commodity.setChannel(ChannelEnum.H5.getId());
            commodity.setName(ofNullable(feeRuleSaveReqDTO.getTitle()).orElse("商品"));
            commodity.setClassifyCode(feeRuleSaveReqDTO.getClassifyCode());
            commodity.setFixPrice(fixPrice);
            commodity.setComposes(composeIdList);
            skuCommodityDTO.setCommodities(commodity);

            log.debug("[更新计费规则] 参数: -> {} ", JSON.toJSONString(skuCommodityDTO));
            BaseResponse resp = commodityRelatedService.updateCommodity(skuCommodityDTO);
            if (resp.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
                log.error("[更新计费规则] 调用商品中心保存商品结果 -> {}", resp);
                throw new BusinessException(resp.getMessage());
            }
        }
    }


    /**
     * 保存组合商品
     * @param rule
     * @param distributorId
     * @param adUserId
     * @param equipmentTypeId
     * @return
     */
    private List<Long> saveCompose(FeeRuleSaveReqDTO rule, Long distributorId, Long adUserId,
                                   Long groupId, Long equipmentTypeId) {
        List<Long> composeIdList = new ArrayList<>();
        rule.getComposes().forEach(compose -> {
            // 新增计费规则
            SkuCommodityDTO skuCommodityDTO = new SkuCommodityDTO();
            skuCommodityDTO.setDistributor(distributorId);
            skuCommodityDTO.setAdUser(adUserId);

            // 定价
            CommodityFixPriceDTO fixPrice = new CommodityFixPriceDTO();
            fixPrice.setPrice(compose.getPrice());

            List<CommodityFixPriceValueDTO> fixPriceValueDTOList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(compose.getPriceValueList())) {
                compose.getPriceValueList().forEach(fixPriceValueDTO -> {
                    CommodityFixPriceValueDTO commodityFixPriceValueDTO = new CommodityFixPriceValueDTO();
                    commodityFixPriceValueDTO.setValue(fixPriceValueDTO.getValue());
                    commodityFixPriceValueDTO.setUnit(fixPriceValueDTO.getUnit());
                    fixPriceValueDTOList.add(commodityFixPriceValueDTO);
                });
            }
            fixPrice.setFixPriceValueList(fixPriceValueDTOList);

            // 商品
            CommodityDTO commodity = new CommodityDTO();
            commodity.setCategoryCode(compose.getCategoryCode());
            commodity.setChannel(ChannelEnum.H5.getId());
            commodity.setName(ofNullable(compose.getName()).orElse("商品"));
            commodity.setClassifyCode(compose.getClassifyCode());
            commodity.setFixPrice(fixPrice);
            skuCommodityDTO.setCommodities(commodity);

            log.debug("[新增计费规则] 参数: -> {} ", JSON.toJSONString(skuCommodityDTO));
            BaseResponse<Long> resp = commoditySkuService.addCommodity(skuCommodityDTO);
            composeIdList.add(resp.getData());
        });
        return composeIdList;
    }

    @Override
    public void delFee(RuleDelDTO ruleDelDTO, Long adOrgId) {
        BaseResponse<EquipmentInfoDTO> baseResponse = equipmentService.getEquipmentInfoByValue(ruleDelDTO.getEquipmentValue());
        EquipmentInfoDTO equipmentInfoDTO = Optional.ofNullable(baseResponse).filter(r -> r.getCode() == ResponseCodeEnum.SUCCESS.getCode()).map(BaseResponse::getData).orElseThrow(()->new BusinessException(BusinessExceptionEnums.DEVICE_NOT_EXISTS));
        EquipmentUnbindDTO equipmentUnbindDTO = new EquipmentUnbindDTO();
        equipmentUnbindDTO.setDistributorId(adOrgId);
        equipmentUnbindDTO.setDisplayId(ruleDelDTO.getDetailId());
        equipmentUnbindDTO.setStoreId(ruleDelDTO.getGroupId());
        equipmentUnbindDTO.setEquipmentId(equipmentInfoDTO.getEquipmentId().longValue());
        BaseResponse resp = commodityRelatedService.unbindDistributorEquipment(equipmentUnbindDTO);
        if (resp.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            log.error("[删除计费规则] 调用商品中心解绑设备商品结果 -> {}", resp);
            throw new BusinessException(BusinessExceptionEnums.FEE_RULE_DEL_ERROR);
        }
    }


    @Override
    public void batchSaveOrUpdate(BatchFeeRuleSaveDTO batchFeeRuleSaveDTO, Long adOrgId, Long adUserId) {

        // 参数校验
        String equipmentTypeValue = batchFeeRuleSaveDTO.getEquipmentType();
        Long equipmentTypeId  = batchFeeRuleSaveDTO.getEquipmentTypeId();
        if (org.apache.commons.lang3.StringUtils.equals(equipmentTypeValue, EquipmentTypeConstant.CDZ.getCode())
                || org.apache.commons.lang3.StringUtils.equals(equipmentTypeValue, EquipmentTypeConstant.CDZ_GW.getCode())
                || org.apache.commons.lang3.StringUtils.equals(equipmentTypeValue, "MCCDZ")
                || org.apache.commons.lang3.StringUtils.equals(equipmentTypeValue, EquipmentTypeConstant.YXJ.getCode())
                || (equipmentTypeId != null && (equipmentTypeId.intValue() == 1000078 || equipmentTypeId.intValue() == 1001253
                || equipmentTypeId.intValue() == 1001246
                || equipmentTypeId.intValue() == LyyConstant.YXJ_TYPE_ID)
            )
                && CollectionUtils.isNotEmpty(batchFeeRuleSaveDTO.getCodes())
        ) {
            List<String> equipmentValues = batchFeeRuleSaveDTO.getCodes();
            List<EquipmentInfoByValuesDTO> system1EqmtList = ofNullable(iotEquipmentServiceFeignClient.getEquipmentInfoByValues(equipmentValues).getBody()).orElse(new ArrayList<>()).stream()
                    .filter(item -> org.apache.commons.lang3.StringUtils.equals("LYY", item.getDeviceType())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(system1EqmtList)) {
                log.warn("当前设备中 {}存在1.0设备",equipmentValues);
                throw new BusinessException(BusinessExceptionEnums.NOT_SUPPORT_BATCH_SERVICE);
            }
        }
        if(StringUtils.isEmpty(equipmentTypeValue)){
            BaseResponse<EquipmentTypeDTO> eqTypeResponse =  equipmentTypeService.getByKey(batchFeeRuleSaveDTO.getEquipmentTypeId());
            equipmentTypeValue = Optional.ofNullable(eqTypeResponse).filter(r -> r.getCode() == ResponseCodeEnum.SUCCESS.getCode()).map(r -> r.getData().getValue()).orElseThrow(()->new BusinessException(eqTypeResponse.getMessage()));
            Assert.notNull(equipmentTypeValue,"设备类型Id与设备类型不能同时为空");
        }else {
            BaseResponse<EquipmentTypeDTO> eqTypeResponse = equipmentTypeService.getByValue(equipmentTypeValue);
            equipmentTypeId = Optional.ofNullable(eqTypeResponse).filter(r -> r.getCode() == ResponseCodeEnum.SUCCESS.getCode()).map(r -> r.getData().getEquipmentTypeId()).orElseThrow(()->new BusinessException(eqTypeResponse.getMessage()));
        }
        List<String> codes = batchFeeRuleSaveDTO.getCodes();
//       TODO 此限制下个版本再上
        //        if(codes.size() > maxBatchNum){
//            throw new BusinessException(BusinessExceptionEnums.BATCH_OPERATE_NUM,maxBatchNum.toString());
//        }
        List<Long> equipmentIdList = new ArrayList<>();

        AbstractFeeRuleBusinessProcess feeRuleBusinessProcess = SaasBusinessFactory.create(equipmentTypeValue).feeRule(SystemRoleEnum.BUSINESS, true);

        List<EquipmentDTO> equipments = new ArrayList<>();
        Long finalEquipmentTypeId = equipmentTypeId;
        codes.stream().forEach(equipmentValue -> {
            BaseResponse<EquipmentInfoDTO> equipmentResult = equipmentService.getEquipmentInfoByValue(equipmentValue);
            EquipmentInfoDTO equipmentInfoDTO = Optional.ofNullable(equipmentResult).filter(r -> r.getCode() == 0).map(BaseResponse::getData).orElseThrow(() -> new BusinessException(equipmentResult.getMessage()));
            EquipmentDTO equipmentDTO = new EquipmentDTO();
            log.debug("[debug] {}", equipmentInfoDTO);
            BeanUtils.copyProperties(equipmentInfoDTO, equipmentDTO);
            equipmentDTO.setEquipmentGroupId(equipmentInfoDTO.getEquipmentGroupId().longValue());
            equipmentDTO.setEquipmentId(equipmentInfoDTO.getEquipmentId().longValue());
            equipmentDTO.setUniqueCode(equipmentInfoDTO.getUniqueCode());
            // 按照参数传输的设备类型保存
            equipmentDTO.setEquipmentTypeId(finalEquipmentTypeId);
            log.debug("[debug] {}", equipmentDTO);
            equipmentIdList.add(equipmentInfoDTO.getEquipmentId().longValue());
            equipments.add(equipmentDTO);
        });
        EquipmentRegisterDTO equipmentRegisterDTO = new EquipmentRegisterDTO();
        equipmentRegisterDTO.setDistributorId(adOrgId);
        equipmentRegisterDTO.setUserId(adUserId);

        List<FeeCommodityDTO> feeRuleList = new ArrayList<>();
        if(batchFeeRuleSaveDTO.getFeeRules() != null){
        batchFeeRuleSaveDTO.getFeeRules().forEach(feeRule->{
            FeeCommodityDTO feeCommodityDTO = new FeeCommodityDTO();
            feeCommodityDTO.setPrice(feeRule.getPrice());
            // 参数兼容老接口
            if(feeRule.getPriceValueList() == null){
                if(feeRule.getFixPriceValueList() != null) {
                    feeRule.setPriceValueList(feeRule.getFixPriceValueList());
                }else{
                    // 兼容游戏机 批量设置参数
                    PriceValueDTO priceValueDTO=  new PriceValueDTO();
                    priceValueDTO.setValue(feeRule.getValue());
                    priceValueDTO.setUnit(feeRule.getValueUnit());
                    List priceValueList = new ArrayList();
                    priceValueList.add(priceValueDTO);
                    feeRule.setPriceValueList(priceValueList);
                }
            }
            if(feeRule.getPriceValueList() != null) {
                feeCommodityDTO.setPriceValueList(feeRule.getPriceValueList());
            }

            if(StringUtils.isEmpty(feeRule.getName())){
                feeCommodityDTO.setName("商品名称");
            }else {
                feeCommodityDTO.setName(feeRule.getName());
            }
            feeCommodityDTO.setIsUse(feeRule.getIsUse());
            feeCommodityDTO.setClassifyCode(batchFeeRuleSaveDTO.getClassifyCode());
            feeCommodityDTO.setComposes(feeRule.getComposes());
            feeCommodityDTO.setRangeCategory(feeRule.getRangeCategory());
            feeCommodityDTO.setRangeStart(feeRule.getRangeStart());
            feeCommodityDTO.setRangeEnd(feeRule.getRangeEnd());
            feeRuleList.add(feeCommodityDTO);
        });
        }
        equipmentRegisterDTO.setFeeRule(feeRuleList);

        feeRuleBusinessProcess.saveOrUpdateFeeRule(equipments,equipmentRegisterDTO);
        feeRuleBusinessProcess.afterSave(equipments, equipmentRegisterDTO);
        CommoditySettingsSaveDTO commoditySettingsSaveDTO = new CommoditySettingsSaveDTO();
        commoditySettingsSaveDTO.setDistributorId(adOrgId);
        if (Boolean.TRUE.equals(batchFeeRuleSaveDTO.getIsShowTitle())) {
            commoditySettingsSaveDTO.setShowTitle(1);
        } else{
            commoditySettingsSaveDTO.setShowTitle(0);
        }
        if (Boolean.TRUE.equals(batchFeeRuleSaveDTO.getIsShowFeeRuleUnit())) {
            commoditySettingsSaveDTO.setShowUnit(1);
        } else{
            commoditySettingsSaveDTO.setShowUnit(0);
        }
        commoditySettingsSaveDTO.setEquipmentId(equipmentIdList);
        BaseResponse baseResponse = merchantEquipmentService.saveCommoditySetting(commoditySettingsSaveDTO);
        Optional.ofNullable(baseResponse).filter(r -> r.getCode() == ResponseCodeEnum.SUCCESS.getCode()).orElseThrow(()->new BusinessException(baseResponse.getMessage()));
    }

    @Override
    public ListFeeModeDTO listFeeMode(String equipmentCode, Long distributorId) {
        ListFeeModeDTO listFeeModeDTO = new ListFeeModeDTO();
        List<FeeModeVO> feeModeVOList = new ArrayList<>();

        if (StringUtils.isEmpty(equipmentCode)) {
            throw new BusinessException("设备code 为空");
        }
        BaseResponse<EquipmentInfoDTO> rs1 = equipmentService.getEquipmentInfoByValue(equipmentCode);
        EquipmentInfoDTO equipmentInfoDTO = Optional.ofNullable(rs1).filter(r -> r.getCode() == ResponseCodeEnum.SUCCESS.getCode())
                .map(BaseResponse::getData).orElseThrow(() -> new BusinessException(rs1.getMessage()));

        if( generalPulseSet.contains(equipmentInfoDTO.getEquipmentTypeValue())){
            FeeModeVO feeModeVO = new FeeModeVO();
            feeModeVO.setName("按脉冲次数计费");
            feeModeVO.setCode(ClassifyCodeEnum.NUM.getCode());
            feeModeVO.setUnitDesc("脉冲次数");
            feeModeVOList.add(feeModeVO);
            listFeeModeDTO.setFeeModeList(feeModeVOList);
            listFeeModeDTO.setEquipmentInfo(equipmentInfoDTO);
            return listFeeModeDTO;
        }
        BaseResponse<EquipmentRegisterTmplDTO> rs2 = registerTemplateClient.queryByEquipmentType(equipmentInfoDTO.getEquipmentTypeValue());
        EquipmentRegisterTmplDTO equipmentRegisterTmplDTO = Optional.ofNullable(rs2).filter(r -> r.getCode() == ResponseCodeEnum.SUCCESS.getCode())
                .map(BaseResponse::getData).orElseThrow(() -> {
                    return new BusinessException(rs2.getMessage());
                });

        List<FeeCommodityDTO> feeRuleList = listByCondition(distributorId,
                Optional.ofNullable(equipmentInfoDTO.getEquipmentGroupId()).map(Integer::longValue).orElse(null),
                Optional.ofNullable(equipmentInfoDTO.getEquipmentTypeId()).map(Integer::longValue).orElse(null),
                Optional.ofNullable(equipmentInfoDTO.getEquipmentId()).map(Integer::longValue).orElse(null),
                CategoryEnum.DEVICE_SERVICE.getCode(), null);
        String currentFeeMode = this.getCurrentFeeRuleMode(equipmentInfoDTO.getEquipmentTypeValue(), equipmentCode, feeRuleList);
        // 按时间或电量（功率）计费，目前是充电桩
        if (FeeRuleModeSettingEnum.TIME_ELEC.getKey().equals(equipmentRegisterTmplDTO.getFeeRuleMode())
                && null != equipmentCode) {
            List<EquipmentTypeFunctionDTO> feeModeList = protocolMicroService.typeFunctionByEquipment(equipmentCode, ProtocolCodeEnum.FEE_MODE.name()).getData();

            if (CollectionUtils.isNotEmpty(feeModeList)) {
                for (EquipmentTypeFunctionDTO typeFunc : feeModeList) {
                    String typeFuncName = typeFunc.getName();

                    //当前设备的计费标准放在首位
                    if (StringUtil.isNotEmpty(currentFeeMode)
                            && currentFeeMode.equalsIgnoreCase(typeFuncName)) {
                        feeModeVOList.add(0, settingFeeMode(typeFuncName));
                        continue;
                    }
                    feeModeVOList.add(settingFeeMode(typeFuncName));
                }
            }
        }
        if (feeModeVOList.isEmpty()) {
            feeModeVOList.add(FeeModeVO.ofFeeModeEnum(currentFeeMode));
        }
        listFeeModeDTO.setFeeModeList(feeModeVOList);
        listFeeModeDTO.setEquipmentInfo(equipmentInfoDTO);
        return listFeeModeDTO;
    }

    @Override
    public void setPro(SetProDTO setProDTO, Long distributorId) {

        CommoditySettingsSaveDTO commoditySettingsSaveDTO = new CommoditySettingsSaveDTO();
        commoditySettingsSaveDTO.setDistributorId(distributorId);
        if (Boolean.TRUE.equals(setProDTO.getIsShowTitle())) {
            commoditySettingsSaveDTO.setShowTitle(1);
        } else{
            commoditySettingsSaveDTO.setShowTitle(0);
        }
        if (Boolean.TRUE.equals(setProDTO.getIsShowFeeRuleUnit())) {
            commoditySettingsSaveDTO.setShowUnit(1);
        } else{
            commoditySettingsSaveDTO.setShowUnit(0);
        }
        List<Long> equipmentIdList = new ArrayList<>();
        equipmentIdList.add(setProDTO.getEquipmentId());
        commoditySettingsSaveDTO.setEquipmentId(equipmentIdList);
        BaseResponse baseResponse = merchantEquipmentService.saveCommoditySetting(commoditySettingsSaveDTO);
        Optional.ofNullable(baseResponse).filter(r -> r.getCode() == ResponseCodeEnum.SUCCESS.getCode()).orElseThrow(()->new BusinessException(baseResponse.getMessage()));
    }

    @Override
    public void updateFeeMode(UpdateFeeModeDTO updateFeeModeDTO, Long distributorId) {

        BaseResponse<EquipmentInfoDTO> rs1 = equipmentService.getEquipmentInfoById(updateFeeModeDTO.getEquipmentId());
        Optional.ofNullable(rs1).filter(r -> r.getCode() == ResponseCodeEnum.SUCCESS.getCode()).orElseThrow(() -> new BusinessException(rs1.getMessage()));
        EquipmentInfoDTO equipmentInfoDTO = rs1.getData();
        // 1 发指令给具体设备
        equipmentOperateService.settingEquipmentFeeMode(equipmentInfoDTO.getUniqueCode(), updateFeeModeDTO.getFeeMode());

        // 2 清除现有的规则
        EquipmentUnbindDTO param = new EquipmentUnbindDTO();
        param.setEquipmentId(updateFeeModeDTO.getEquipmentId());
        param.setDistributorId(distributorId);
        param.setCategoryCode(CategoryEnum.DEVICE_SERVICE.getCode());
        BaseResponse result = commodityRelatedService.unbindDistributorEquipment(param);
        Optional.ofNullable(result).filter(r -> r.getCode() == ResponseCodeEnum.SUCCESS.getCode()).orElseThrow(() -> new BusinessException(result.getMessage()));
        log.info("updateFeeMode {}", Thread.currentThread().getName());

        // 3 设置attach表的计费模式
        String groupServiceCostWay = "TIME";
        if("1".equals(updateFeeModeDTO.getFeeMode())){
            groupServiceCostWay = "TIME";
        }else if("2".equals(updateFeeModeDTO.getFeeMode())){
            groupServiceCostWay ="ELEC";
        }
        equipmentAttachService.updateGroupServiceCostWay(groupServiceCostWay,updateFeeModeDTO.getEquipmentId().intValue());

    }


    @Override
    public List<FeeCommodityDTO> getDefaultFeeRule(Long productId, String equipmentTypeValue) {

        List<FeeCommodityDTO> list = new ArrayList<>();
        BaseResponse<MainboardServicePackageDTO> baseResponse =  mainboardMicroService.queryMainBoardServiceConfig(equipmentTypeValue, productId);
        MainboardServicePackageDTO mainboardServicePackageDTO =  Optional.ofNullable(baseResponse).filter(r -> r.getCode() == ResponseCodeEnum.SUCCESS.getCode()).map(r -> r.getData()).orElseGet(()->{
            log.debug("获取主板信息异常:{}",JSON.toJSONString(baseResponse));
            return null;
        });

        if(mainboardServicePackageDTO != null){
            String jsonConfigStr = mainboardServicePackageDTO.getServicePackageConfig();
            JSONArray jsonArray = JSONArray.parseArray(jsonConfigStr);

            jsonArray.forEach(r ->{
                JSONObject jsonObject = (JSONObject) r;
                String isActive = jsonObject.getString("isactive");
                if("Y".equalsIgnoreCase(isActive)) {
                    FeeCommodityDTO feeRuleDetailDTO = new FeeCommodityDTO();
                    List<PriceValueDTO> priceValueDTOList = new ArrayList<>();
                    // 分钟定价
                    PriceValueDTO minPriceValueDTO = new PriceValueDTO();
                    minPriceValueDTO.setValue(jsonObject.getBigDecimal("serviceTime"));
                    minPriceValueDTO.setUnit(ValueUnitEnum.MIN.getId());
                    priceValueDTOList.add(minPriceValueDTO);

                    // 币定价
                    PriceValueDTO coinPriceValueDTO = new PriceValueDTO();
                    coinPriceValueDTO.setValue(jsonObject.getBigDecimal("coins"));
                    coinPriceValueDTO.setUnit(ValueUnitEnum.COIN.getId());
                    priceValueDTOList.add(coinPriceValueDTO);
                    feeRuleDetailDTO.setPriceValueList(priceValueDTOList);
                    feeRuleDetailDTO.setPrice(jsonObject.getBigDecimal("price"));
                    feeRuleDetailDTO.setName(jsonObject.getString("pattern"));
                    // 默认开启
                    feeRuleDetailDTO.setIsUse(true);
                    list.add(feeRuleDetailDTO);
                }
            });
            // 数据按从小到大排序
            list.sort(Comparator.comparing(r -> r.getPrice()));
        }
        return list;
    }

    @Override
    public void setFeeFuleUse(SetUseDTO setUseDTO, Long adOrgId) {
        BaseResponse baseResponse = commodityRelatedService.setUse(setUseDTO);
        Optional.ofNullable(baseResponse).filter(r -> r.getCode() == ResponseCodeEnum.SUCCESS.getCode()).orElseThrow(()->new BusinessException(baseResponse.getMessage()));
    }

    private FeeModeVO settingFeeMode(String feeModeCode) {
        FeeModeEnum feeModeEnum = FeeModeEnum.getFeeModeByCode(feeModeCode);

        FeeModeVO feeModeVO = new FeeModeVO();
        feeModeVO.setName(feeModeEnum.getName());
        feeModeVO.setCode(feeModeEnum.getCode());
        feeModeVO.setUnitDesc(feeModeEnum.getUnitDesc());
        return feeModeVO;
    }


    private List<FeeCommodityDTO> convertToRule(List<EquipmentDisplayDTO> equipmentDisplayDTO) {
        List<FeeCommodityDTO> list = new ArrayList<>();
        for (EquipmentDisplayDTO item : equipmentDisplayDTO) {
            FeeCommodityDTO commodity = new FeeCommodityDTO();
            commodity.setRelateEquipmentId(item.getRelateEquipment().getRelateEquipmentId());
            commodity.setPrice(item.getFixPrice().getPrice());
            commodity.setName(item.getSku().getName());
            commodity.setIsUse(item.getRelateEquipment().getIsUse());
            Optional.ofNullable(item.getFixPriceValueList()).ifPresent(r -> {
                List<PriceValueDTO> priceValueDTOList =  r.stream().map(fixPriceValueDTO -> {
                    PriceValueDTO priceValueDTO = new PriceValueDTO();
                    priceValueDTO.setUnit(fixPriceValueDTO.getUnit());
                    priceValueDTO.setValue(fixPriceValueDTO.getValue());
                    return priceValueDTO;
                }).collect(Collectors.toList());
                commodity.setPriceValueList(priceValueDTOList);
            });
            commodity.setClassifyCode(item.getClassify().getCode());
            commodity.setCategoryCode(item.getCategory().getCode());
            commodity.setDisplayId(item.getDisplay().getId());
            commodity.setRangeCategory(item.getFixPrice().getRangeCategory());
            commodity.setRangeStart(item.getFixPrice().getRangeStart());
            commodity.setRangeEnd(item.getFixPrice().getRangeEnd());
            // 组合商品
            if (CollectionUtils.isNotEmpty(item.getComposes())) {
                List<FeeRuleComposeDTO> feeRuleComposeList = new ArrayList<>();
                item.getComposes().forEach(compose -> {
                    FeeRuleComposeDTO feeRuleCompose = new FeeRuleComposeDTO();
                    feeRuleCompose.setPrice(compose.getFixPrice().getPrice());
                    feeRuleCompose.setName(compose.getSku().getName());
                    Optional.ofNullable(compose.getFixPriceValueList()).ifPresent(r -> {
                        List<PriceValueDTO> priceValueDTOList =  r.stream().map(fixPriceValueDTO -> {
                            PriceValueDTO priceValueDTO = new PriceValueDTO();
                            priceValueDTO.setUnit(fixPriceValueDTO.getUnit());
                            priceValueDTO.setValue(fixPriceValueDTO.getValue());
                            return priceValueDTO;
                        }).collect(Collectors.toList());
                        feeRuleCompose.setPriceValueList(priceValueDTOList);
                    });

                    feeRuleCompose.setClassifyCode(compose.getClassify().getCode());
                    feeRuleCompose.setCategoryCode(compose.getCategory().getCode());

                    feeRuleComposeList.add(feeRuleCompose);
                });
                commodity.setComposes(feeRuleComposeList);
            }
            list.add(commodity);
        }
        return list;
    }

    private String getCurrentFeeRuleMode(String equipmentTypeValue, String equipmentCode, List<FeeCommodityDTO> feeRuleList) {
        // 区分按场地、按设备

        EquipmentRegisterTmplDTO registerTmpl = ofNullable(registerTemplateClient.queryByEquipmentType(equipmentTypeValue))
                .map(BaseResponse::getData)
                .orElseThrow(() -> new BusinessException(BusinessExceptionEnums.REGISTER_TEMPLATE_MISSING));

        // 计费规则模式
        String settingFeeRuleModeStyle = registerTmpl.getFeeRuleMode();

        // 获取单位
        String feeRuleMode = null;
        // 由于充电桩可以让商户手动配置计费标准，所以需要特殊处理，默认都是取注册规则的计费标准
        if (EquipmentTypeEnums.isCDZLogic(equipmentTypeValue)) {
            // 计费规则：按时长、按电量

            if (CollectionUtils.isNotEmpty(feeRuleList)) {
                feeRuleMode = feeRuleList.get(0).getClassifyCode();
            }
            // 充电桩默认按时长
            if (StringUtil.isEmpty(feeRuleMode)) {
                //汽车桩默认电量计费
                if (EquipmentTypeEnums.CHARGING_PILE_FOR_CAR.getValue().equalsIgnoreCase(equipmentTypeValue)) {
                    feeRuleMode = FeeRuleModeTimeElecEnum.ELEC.getCode();
                } else {
                    feeRuleMode = FeeRuleModeSettingEnum.TIME.getKey();
                }
            }
        }
        if (EquipmentTypeEnums.AMMETER.getValue().equalsIgnoreCase(equipmentTypeValue)) {
            List<EquipmentTypeFunctionDTO> feeModeList = protocolMicroService.typeFunctionByEquipment(equipmentCode, ProtocolCodeEnum.FEE_MODE.name()).getData();
            if (CollectionUtils.isNotEmpty(feeModeList)) {
                feeRuleMode = feeModeList.get(0).getName();
            }
        }

        if (StringUtil.isEmpty(feeRuleMode)) {
            feeRuleMode = settingFeeRuleModeStyle;
        }

        return feeRuleMode;
    }

    /**
     * 电量乘以100
     * @param fixPrice
     */
    private void multiplyOnehundred(CommodityFixPriceDTO fixPrice) {
        if (!org.springframework.util.CollectionUtils.isEmpty(fixPrice.getFixPriceValueList())) {
            fixPrice.getFixPriceValueList().stream().forEach(rule -> {
                if (FeeRuleEnum.DU.getValue().equals(rule.getUnit()) && rule.getValue()!=null) {
                    rule.setValue(rule.getValue().multiply(ChargingConstants.MULTIPLE_100));
                }
            });
        }
    }

    @Override
    public boolean save(EquipmentChargingTimeRuleDTO timeRuleDTO) {
        BaseResponse<Boolean> response = feeRuleClient.saveRules(timeRuleDTO);
        return response.getData();
    }

    @Override
    public EquipmentChargingTimeRuleDTO getRule(Long equipmentGroupId, String equipmentValue) {
        BaseResponse<EquipmentChargingTimeRuleDTO> response = feeRuleClient.getMerchantRule(equipmentGroupId,equipmentValue);
        return response.getData();
    }
}
