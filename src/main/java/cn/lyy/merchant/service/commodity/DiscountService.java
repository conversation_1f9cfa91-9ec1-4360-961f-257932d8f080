package cn.lyy.merchant.service.commodity;

import cn.lyy.merchant.dto.request.BatchDiscountRuleSaveDTO;
import cn.lyy.merchant.dto.request.DiscountRuleSaveDTO;
import cn.lyy.merchant.dto.request.RestroreDefaultDTO;
import cn.lyy.merchant.dto.response.DiscountRuleDTO;

public interface DiscountService {


    Long saveOrUpdate(DiscountRuleSaveDTO discountRuleSaveDTO, Long distributorId, Long adUserId);

    /**
     * 删除折扣
     * @param detailId
     * @param groupId
     * @param adOrgId
     */
    void delDiscount(Long detailId,Long groupId, Long adOrgId);

    DiscountRuleDTO getDiscountRuleByGroup(Long groupId, Long equipmentTypeId, Long distributorId);

    void batchSaveOrUpdate(BatchDiscountRuleSaveDTO batchDiscountRuleSaveDTO, Long adOrgId, Long adUserId);

    void restoreDefault(RestroreDefaultDTO restroreDefaultDTO, Long adOrgId, Long adUserId);
}
