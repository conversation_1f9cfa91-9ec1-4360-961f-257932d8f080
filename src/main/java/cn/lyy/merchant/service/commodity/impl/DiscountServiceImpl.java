package cn.lyy.merchant.service.commodity.impl;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.base.utils.exception.BizException;
import cn.lyy.equipment.dto.equipment.EquipmentTypeDTO;
import cn.lyy.equipment.service.IEquipmentFuncQueueService;
import cn.lyy.equipment.service.IEquipmentTypeService;
import cn.lyy.merchant.api.service.MerchantEquipmentService;
import cn.lyy.merchant.api.service.RegisterTemplateClient;
import cn.lyy.merchant.constants.BusinessExceptionEnums;
import cn.lyy.merchant.dto.equipment.EquipmentListDTO;
import cn.lyy.merchant.dto.equipment.EquipmentQueryDTO;
import cn.lyy.merchant.dto.merchant.MerchantEquipmentDTO;
import cn.lyy.merchant.dto.merchant.equpiment.request.MerchantEquipmentBaseInfoQueryRequest;
import cn.lyy.merchant.dto.request.BatchDiscountRuleSaveDTO;
import cn.lyy.merchant.dto.request.DiscountRuleSaveDTO;
import cn.lyy.merchant.dto.request.RestroreDefaultDTO;
import cn.lyy.merchant.dto.response.DiscountRuleDTO;
import cn.lyy.merchant.dto.template.EquipmentRegisterTmplDTO;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.microservice.CommodityRelatedService;
import cn.lyy.merchant.microservice.CommoditySkuService;
import cn.lyy.merchant.service.commodity.DiscountService;
import cn.lyy.tools.equipment.LyyConstant;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.lyy.commodity.rpc.constants.CategoryEnum;
import com.lyy.commodity.rpc.constants.ChannelEnum;
import com.lyy.commodity.rpc.constants.ClassifyCodeEnum;
import com.lyy.commodity.rpc.constants.ValueUnitEnum;
import com.lyy.commodity.rpc.dto.EquipmentDisplayDTO;
import com.lyy.commodity.rpc.dto.FixPriceValueDTO;
import com.lyy.commodity.rpc.dto.mo.SkuSimpleMO;
import com.lyy.commodity.rpc.dto.request.*;
import com.lyy.equipment.interfaces.dto.equipment.EquipmentDTO;
import com.lyy.equipment.interfaces.feign.equipment.IotEquipmentServiceFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DiscountServiceImpl implements DiscountService {

    @Resource
    private CommodityRelatedService commodityRelatedService;

    @Resource
    private CommoditySkuService commoditySkuService;

    @Resource
    private RegisterTemplateClient registerTemplateClient;

    @Resource
    private IEquipmentTypeService equipmentTypeService;

    @Resource
    private MerchantEquipmentService merchantEquipmentService;

    @Autowired
    IotEquipmentServiceFeignClient iotEquipmentServiceFeignClient;


    @Override
    public Long saveOrUpdate(DiscountRuleSaveDTO discountRuleSaveDTO, Long distributorId, Long adUserId) {

        Long displayId = null;
        // 新增计费规则
        SkuCommodityDTO skuCommodityDTO = new SkuCommodityDTO();
        skuCommodityDTO.setDistributor(distributorId);
        skuCommodityDTO.setAdUser(adUserId);
        if(discountRuleSaveDTO.getGroupId() != null) {  //
            // 查找设备类型下的所有设备信息
            List<EquipmentListDTO> equipmentListDTOList = getEquipmentListByGroupEqType(discountRuleSaveDTO.getEquipmentTypeId(),discountRuleSaveDTO.getGroupId(), distributorId);
            List<RelateEquipmentDTO> relateEquipmentDTOList = new ArrayList<>();
            equipmentListDTOList.forEach( e ->{
                // 设置关联设备
                RelateEquipmentDTO relateEquipment = new RelateEquipmentDTO();
                relateEquipment.setEquipment(e.getEquipmentId());
                relateEquipment.setEquipmentType(discountRuleSaveDTO.getEquipmentTypeId());
                relateEquipment.setStore(discountRuleSaveDTO.getGroupId());
                relateEquipmentDTOList.add(relateEquipment);

            });

            skuCommodityDTO.setEquipmentList(relateEquipmentDTOList);
        }else{
            // 设置关联设备
            RelateEquipmentDTO relateEquipment = new RelateEquipmentDTO();
            relateEquipment.setEquipmentType(discountRuleSaveDTO.getEquipmentTypeId());
            //relateEquipment.setStore(discountRuleSaveDTO.getGroupId());
            skuCommodityDTO.setEquipment(relateEquipment);
        }
        // 设置商品
        // 定价
        CommodityFixPriceDTO fixPrice = new CommodityFixPriceDTO();
        fixPrice.setPrice(discountRuleSaveDTO.getPrice());

        List<CommodityFixPriceValueDTO> fixPriceValueList = new ArrayList<>();
        CommodityFixPriceValueDTO fixPriceValueDTO = new CommodityFixPriceValueDTO();
        fixPriceValueDTO.setValue(discountRuleSaveDTO.getUnitValue());
        fixPriceValueDTO.setUnit(discountRuleSaveDTO.getUnit());
        fixPriceValueList.add(fixPriceValueDTO);
        fixPrice.setFixPriceValueList(fixPriceValueList);
        // 商品
        CommodityDTO commodity = new CommodityDTO();
        commodity.setCategoryCode(CategoryEnum.SET_MEAL.getCode());
        commodity.setChannel(ChannelEnum.H5.getId());
        if (StringUtils.isEmpty(discountRuleSaveDTO.getTitle())) {
            String templateTitle = "充${1}元送${2}元";
            String t1 = templateTitle.replace("${1}", discountRuleSaveDTO.getPrice().toString());
            String t2 = t1.replace("${2}", discountRuleSaveDTO.getUnitValue().subtract(discountRuleSaveDTO.getPrice()).toString());
            commodity.setName(t2);
        } else {
            commodity.setName(discountRuleSaveDTO.getTitle());
        }
        commodity.setClassifyCode(ClassifyCodeEnum.RECHARGE.getCode());
        commodity.setFixPrice(fixPrice);
        skuCommodityDTO.setCommodities(commodity);

        if (discountRuleSaveDTO.getDetailId() == null) {
            log.debug("[新增优惠规则] 参数: -> {} ", JSON.toJSONString(skuCommodityDTO));
            BaseResponse<Long> resp = commoditySkuService.addCommodity(skuCommodityDTO);
            Optional.ofNullable(resp).filter(r -> r.getCode() == ResponseCodeEnum.SUCCESS.getCode()).orElseThrow(() -> {
                log.error("[新增优惠规则] 调用商品中心保存商品结果 -> {}", JSON.toJSONString(resp));
                return new BusinessException(resp.getMessage());
            });
            displayId = resp.getData();
        } else {
            skuCommodityDTO.getCommodities().setDisplay(discountRuleSaveDTO.getDetailId());
            displayId = discountRuleSaveDTO.getDetailId();
            log.debug("[更新优惠规则] 参数: -> {} ", JSON.toJSONString(skuCommodityDTO));
            BaseResponse resp = commodityRelatedService.updateCommodity(skuCommodityDTO);
            if (resp.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
                log.error("[更新优惠规则] 调用商品中心保存商品结果 -> {}", JSON.toJSONString(resp));
                throw new BusinessException(BusinessExceptionEnums.FEE_RULE_SAVE_ERROR);
            }
        }
        return displayId;
    }


    private List<EquipmentListDTO> getEquipmentListByGroupEqType(Long equipmentTypeId,Long groupId, Long distributorId) {
        EquipmentQueryDTO param = new EquipmentQueryDTO();
        param.setLyyDistributorId(distributorId.intValue());
        param.setLyyEquipmentTypeId(equipmentTypeId.intValue());
        param.setEquipmentGroupId(groupId.intValue());
        BaseResponse<List<EquipmentListDTO>> result = merchantEquipmentService.selectByEquipmentTypeAndGroup(param);
        List<EquipmentListDTO> equipmentList = Optional.ofNullable(result).filter(r -> r.getCode() == ResponseCodeEnum.SUCCESS.getCode()).map(BaseResponse::getData).orElseThrow(() -> new BusinessException(result.getMessage()));
        return equipmentList;
    }

    @Override
    public void delDiscount(Long detailId,Long groupId, Long adOrgId) {
        EquipmentUnbindDTO equipmentUnbindDTO = new EquipmentUnbindDTO();
        equipmentUnbindDTO.setDistributorId(adOrgId);
        equipmentUnbindDTO.setDisplayId(detailId);
        equipmentUnbindDTO.setStoreId(groupId);
        equipmentUnbindDTO.setCategoryCode(CategoryEnum.SET_MEAL.getCode());
        //BaseResponse resp = commodityRelatedService.unbind(equipmentUnbindDTO);
        BaseResponse resp = commodityRelatedService.unbindDistributorEquipment(equipmentUnbindDTO);
        Optional.ofNullable(resp).filter(r -> r.getCode() == ResponseCodeEnum.SUCCESS.getCode()).orElseThrow(() -> {
            log.error("[删除优惠规则] 调用商品中心解绑设备商品结果 -> {}", Optional.ofNullable(resp).map(BaseResponse::getMessage));
            return new BusinessException(BusinessExceptionEnums.FEE_RULE_DEL_ERROR);
        });
    }


    public DiscountRuleDTO getDiscountRuleByGroup(Long groupId, Long equipmentTypeId, Long distributorId) {

        DiscountRuleDTO discountRuleDTO = new DiscountRuleDTO();
        EquipmentTypeDTO equipmentTypeDTO = Optional.ofNullable(equipmentTypeService.getByKey(equipmentTypeId)).filter(r -> r.getCode() == ResponseCodeEnum.SUCCESS.getCode()).map(BaseResponse::getData).get();
        Assert.notNull(equipmentTypeDTO, "数据异常，无效的equipmentId " + equipmentTypeId);

        if(groupId == null){
            //场地为空，加载默认的优惠规则，默认规则 从模板配置中读
            BaseResponse<EquipmentRegisterTmplDTO> resultTmpl = registerTemplateClient.queryByEquipmentType(equipmentTypeDTO.getValue());
            EquipmentRegisterTmplDTO registerTmplDTO = Optional.ofNullable(resultTmpl).filter(r -> r.getCode() == 0).map(BaseResponse::getData).orElse(null);
            List<DiscountRuleDTO.Rule> rulelist = new ArrayList<>();
            if(registerTmplDTO != null) {
                JSONObject json = JSON.parseObject(registerTmplDTO.getRuleJson());

                if (json.containsKey("rechargeDefault")) {
                    JSONArray jsonAry = json.getJSONArray("rechargeDefault");
                    JSONObject jsonObj = null;

                    for (int i = 0; i < jsonAry.size(); i++) {
                        jsonObj = jsonAry.getJSONObject(i);
                        DiscountRuleDTO.Rule rule = discountRuleDTO.new Rule();
                        rule.setPrice(jsonObj.getBigDecimal("price"));
                        // 计算最终得到的价值
                        BigDecimal value = jsonObj.getBigDecimal("price").add(jsonObj.getBigDecimal("amount"));
                        rule.setCoin(value);
                        rulelist.add(rule);
                    }
                }
            }else{
                log.info("该设备类型没有默认模板配置，equipmentTypeId:{}",equipmentTypeId);
            }
            discountRuleDTO.setDiscountRule(rulelist);
            discountRuleDTO.setEquipmentTypeName(equipmentTypeDTO.getName());
            // 封装结束
            return discountRuleDTO;
        }

        DisplayReqDTO displayDTO = new DisplayReqDTO();
        displayDTO.setStore(groupId);
        displayDTO.setEquipmentType(equipmentTypeId);
        displayDTO.setDistributor(distributorId);
        displayDTO.setClassifyCode(ClassifyCodeEnum.RECHARGE.getCode());
        displayDTO.setCategoryCode(CategoryEnum.SET_MEAL.getCode());
        // 过滤1.0充值套餐
        displayDTO.setNeedEquipment(true);
        BaseResponse<List<EquipmentDisplayDTO>> result = commodityRelatedService.selectStoreDisplay(displayDTO);
        List<EquipmentDisplayDTO> equipmentDisplayDTOList = Optional.ofNullable(result).filter(r -> r.getCode() == 0).map(r -> r.getData()).orElse(Collections.EMPTY_LIST);

        List<DiscountRuleDTO.Rule> rulelist = new ArrayList<>();
        equipmentDisplayDTOList.stream().forEach(r -> {
            DiscountRuleDTO.Rule rule = discountRuleDTO.new Rule();
            rule.setId(r.getDisplay().getId());
            rule.setPrice(r.getFixPrice().getPrice());
            if (groupId != null) {
                rule.setLyyEquipmentGroupId(groupId);
            }
            List<FixPriceValueDTO> fixPriceValueDTOList =  r.getFixPriceValueList();
            Optional.ofNullable(fixPriceValueDTOList).ifPresent(fixPriceValueDTOS -> {
                if(fixPriceValueDTOS.size()>0) {
                    Optional.ofNullable(fixPriceValueDTOS.get(0)).ifPresent(fixPriceValueDTO -> {
                        rule.setCoin(fixPriceValueDTO.getValue());
                    });
                }else{
                    log.error("数据异常");
                }
            });
            rule.setLyyEquipmentTypeId(equipmentTypeId);
            rulelist.add(rule);
        });
        discountRuleDTO.setDiscountRule(rulelist);
        discountRuleDTO.setEquipmentTypeName(equipmentTypeDTO.getName());
        return discountRuleDTO;
    }

    @Override
    public void batchSaveOrUpdate(BatchDiscountRuleSaveDTO batchDiscountRuleSaveDTO, Long adOrgId, Long adUserId) {

        List<Long> codes = batchDiscountRuleSaveDTO.getCodes();
        if (batchDiscountRuleSaveDTO.getEquipmentTypeId() != null && (
                batchDiscountRuleSaveDTO.getEquipmentTypeId().intValue() == 1000078
                        || batchDiscountRuleSaveDTO.getEquipmentTypeId().intValue() == 1001246
                        || batchDiscountRuleSaveDTO.getEquipmentTypeId().intValue() == 1001253
                        || batchDiscountRuleSaveDTO.getEquipmentTypeId().intValue() == LyyConstant.YXJ_TYPE_ID
        )){
            List<Long> equipmentIdList = Optional.ofNullable(merchantEquipmentService.findByGroupIds(codes).getData()).orElse(new ArrayList<>())
                    .stream().map(MerchantEquipmentDTO::getEquipmentId).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(equipmentIdList)) {
                List<EquipmentDTO> system1EqmtList = Optional.ofNullable(iotEquipmentServiceFeignClient.findEquipmentsByIds(equipmentIdList).getBody()).orElse(new ArrayList<>())
                        .stream().filter(item -> org.apache.commons.lang3.StringUtils.equals("LYY", item.getDeviceType())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(system1EqmtList)) {
                    log.info("当前场地id下{}存在1.0设备", codes);
                    throw new BusinessException(BusinessExceptionEnums.NOT_SUPPORT_BATCH_SERVICE);
                }
            }
        }

        codes.stream().forEach(groupId -> {

            // 将设备原有计费规则删除
            EquipmentUnbindDTO param  = new EquipmentUnbindDTO();
            param.setDistributorId(adOrgId);
            param.setCategoryCode(CategoryEnum.SET_MEAL.getCode());
            param.setEquipmentTypeId(batchDiscountRuleSaveDTO.getEquipmentTypeId());
            param.setStoreId(groupId);
            BaseResponse clearResult =  commodityRelatedService.unbindDistributorEquipment(param);
            Optional.ofNullable(clearResult).filter(r -> r.getCode() == ResponseCodeEnum.SUCCESS.getCode()).orElseThrow(()-> new BusinessException(clearResult.getMessage()));

            List<BatchDiscountRuleSaveDTO.DiscountRuleDetailDTO> discountRuleList = batchDiscountRuleSaveDTO.getDiscountRule();
            discountRuleList.forEach(
                    discountRule -> {
                        // 判断此充值规则是否存在（规则绑定与设备）
                        CommodityQueryDTO commodityQueryDTO = new CommodityQueryDTO();
                        commodityQueryDTO.setDistributorId(adOrgId);
                        commodityQueryDTO.setCategoryCode(CategoryEnum.SET_MEAL.getCode());
                        commodityQueryDTO.setClassifyCode(ClassifyCodeEnum.RECHARGE.getCode());
                        commodityQueryDTO.setPrice(discountRule.getPrice());
                        commodityQueryDTO.setValue(discountRule.getCoin());
                        // 充值优惠业务，固定单位类型
                        commodityQueryDTO.setValueUnit(ValueUnitEnum.COIN.getId().toString());

                        BaseResponse<SkuSimpleMO> getSkuByClassifyPriceResult = commoditySkuService.getSkuByClassifyPrice(commodityQueryDTO);
                        SkuSimpleMO skuSimpleMO = Optional.ofNullable(getSkuByClassifyPriceResult).filter(r2 -> r2.getCode() == ResponseCodeEnum.SUCCESS.getCode()).map(BaseResponse::getData).orElseThrow(() -> new BusinessException(getSkuByClassifyPriceResult.getMessage()));
                        // 查询场地 设备类型下的所有设备
                        List<EquipmentListDTO> equipmentListDTOList = getEquipmentListByGroupEqType(batchDiscountRuleSaveDTO.getEquipmentTypeId(), groupId, adOrgId);
                        equipmentListDTOList.forEach(equipment -> {
                            // 绑定商品与 场地 设备类型
                            StoreBindDTO storeBindDTO = new StoreBindDTO();
                            storeBindDTO.setDisplayId(skuSimpleMO.getDisplayId());
                            storeBindDTO.setDistributorId(adOrgId);
                            storeBindDTO.setAdUserId(adUserId);
                            storeBindDTO.setStoreId(groupId);
                            storeBindDTO.setEquipmentId(equipment.getEquipmentId());
                            storeBindDTO.setEquipmentTypeId(batchDiscountRuleSaveDTO.getEquipmentTypeId());
                            BaseResponse result = commodityRelatedService.bindStoreEquipmentType(storeBindDTO);
                            if (result.getCode() != 0) {
                                log.info("绑定场地信息失败 {}", result.getMessage());
                            }
                        });


                    }
            );

        });
    }

    @Override
    public void restoreDefault(RestroreDefaultDTO restroreDefaultDTO, Long adOrgId, Long adUserId) {

        BaseResponse<EquipmentRegisterTmplDTO> resultTmpl = registerTemplateClient.queryByEquipmentType(restroreDefaultDTO.getEquipmentType());
        EquipmentRegisterTmplDTO registerTmplDTO = Optional.ofNullable(resultTmpl).filter(r -> r.getCode() == 0).map(BaseResponse::getData).orElse(null);

        List<RestoreDefaultDiscountDTO.DiscountRule> discountRules = new ArrayList<>();
        if(registerTmplDTO != null) {
            JSONObject json = JSON.parseObject(registerTmplDTO.getRuleJson());
            if (json.containsKey("rechargeDefault")) {
                JSONArray jsonAry = json.getJSONArray("rechargeDefault");
                JSONObject jsonObj = null;
                for (int i = 0; i < jsonAry.size(); i++) {
                    jsonObj = jsonAry.getJSONObject(i);
                    RestoreDefaultDiscountDTO.DiscountRule rule = new RestoreDefaultDiscountDTO.DiscountRule();
                    rule.setPrice(jsonObj.getBigDecimal("price"));
                    // 计算最终得到的价值
                    BigDecimal value = jsonObj.getBigDecimal("price").add(jsonObj.getBigDecimal("amount"));
                    rule.setUnitValue(value);
                    rule.setUnitType(ValueUnitEnum.COIN.getId());
                    discountRules.add(rule);
                }
            }
        }else{
            log.info("该设备类型没有默认模板配置，equipmentType:{}",restroreDefaultDTO.getEquipmentType());
        }

        if(restroreDefaultDTO.getGroupId() != null) {  // 批量操作无需调接口
            // 查找设备类型下的所有设备信息
            List<RelateEquipmentDTO> relateEquipmentDTOList =  getRelateEquipmentByEqTypeAndStore(restroreDefaultDTO.getEquipmentTypeId(),restroreDefaultDTO.getGroupId(),adOrgId);

            RestoreDefaultDiscountDTO restoreDefaultDiscountDTO = new RestoreDefaultDiscountDTO();
            restoreDefaultDiscountDTO.setGroupId(restroreDefaultDTO.getGroupId());
            restoreDefaultDiscountDTO.setEquipmentTypeId(restroreDefaultDTO.getEquipmentTypeId());
            restoreDefaultDiscountDTO.setAdUserId(adUserId);
            restoreDefaultDiscountDTO.setDistributorId(adOrgId);
            restoreDefaultDiscountDTO.setDiscountRules(discountRules);
            restoreDefaultDiscountDTO.setRelateEquipmentList(relateEquipmentDTOList);
            BaseResponse resultRes = commodityRelatedService.restoreDefaultDiscount(restoreDefaultDiscountDTO);
            Optional.ofNullable(resultRes).filter(r -> r.getCode() == 0).orElseThrow(() -> {

                return new BusinessException("恢复优惠设置失败");
            });
        }
    }

    /**
     * 获取场地设备类型下的所有设备
     *    此方法处理，优惠设置，绑定到具体设备
     * @param equipmentTypeId
     * @param groupId
     * @param distributorId
     * @return
     */
    private List<RelateEquipmentDTO> getRelateEquipmentByEqTypeAndStore(Long equipmentTypeId,Long groupId,Long distributorId) {
        List<EquipmentListDTO> equipmentListDTOList = getEquipmentListByGroupEqType(equipmentTypeId,groupId, distributorId);
        List<RelateEquipmentDTO> relateEquipmentDTOList = new ArrayList<>();
        equipmentListDTOList.forEach( e ->{
            // 设置关联设备
            RelateEquipmentDTO relateEquipment = new RelateEquipmentDTO();
            relateEquipment.setEquipment(e.getEquipmentId());
            relateEquipment.setEquipmentType(equipmentTypeId);
            relateEquipment.setStore(groupId);
            relateEquipmentDTOList.add(relateEquipment);

        });
        return relateEquipmentDTOList;
    }
}
