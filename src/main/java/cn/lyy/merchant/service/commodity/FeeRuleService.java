package cn.lyy.merchant.service.commodity;

import cn.lyy.merchant.dto.equipment.EquipmentChargingTimeRuleDTO;
import cn.lyy.merchant.dto.request.*;
import cn.lyy.merchant.dto.response.ListFeeModeDTO;
import com.lyy.commodity.rpc.dto.request.SetUseDTO;

import java.util.List;

/**
 * 类描述：
 * <p>
 *
 * <AUTHOR>
 * @since 2020/11/18 16:34
 */
public interface FeeRuleService {

    /**
     * 根据条件查询当前已设置的计费规则
     * @param groupId 场地
     * @param equipmentTypeId 设备类型
     * @return
     */
    List<FeeCommodityDTO> listByCondition(Long distributorId, Long groupId, Long equipmentTypeId,
                                          Long equipmentId, String categoryCode, String classifyCode);

    /**
     * 根据条件查询当前已设置的计费规则
     * @param groupId 场地
     * @param equipmentTypeId 设备类型
     * @return
     */
    List<FeeCommodityDTO> listByCondition(Long distributorId, Long groupId, Long equipmentTypeId,
                                          Long equipmentId, String categoryCode, String classifyCode, Boolean isCompose);

    /**
     *  保存或更新计费规则
     * @param feeRuleSaveReqDTO
     * @param distributorId
     * @param adUserId
     */
    void saveOrUpdate(FeeRuleSaveReqDTO feeRuleSaveReqDTO, Long distributorId, Long adUserId);

    /**
     * 删除计费规则
     * @param ruleDelDTO
     * @param adOrgId
     */
    void delFee(RuleDelDTO ruleDelDTO, Long adOrgId);

     /**
     * 批量设置计费规则
     * @param batchFeeRuleSaveDTO
     * @param adOrgId
     * @param adUserId
     */
    void batchSaveOrUpdate(BatchFeeRuleSaveDTO batchFeeRuleSaveDTO, Long adOrgId, Long adUserId);

    ListFeeModeDTO listFeeMode(String equipmentCode, Long distributorId);

    /**
     * 设置属性
     * @param setProDTO
     */
    void setPro(SetProDTO setProDTO,Long distributorId);

    void updateFeeMode(UpdateFeeModeDTO updateFeeModeDTO, Long distributorId);


    /**
     * 获取设备默认的计费规则
     * @param productId
     * @param equipmentTypeValue
     * @return
     */
    List<FeeCommodityDTO> getDefaultFeeRule(Long productId, String equipmentTypeValue);

    /**
     * 设置规则 启用禁用
     * @param setUseDTO
     * @param adOrgId
     */
    void setFeeFuleUse(SetUseDTO setUseDTO, Long adOrgId);

    boolean save(EquipmentChargingTimeRuleDTO timeRuleDTO);

    EquipmentChargingTimeRuleDTO getRule(Long equipmentGroupId, String equipmentValue);
}
