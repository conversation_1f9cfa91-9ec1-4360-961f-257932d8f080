package cn.lyy.merchant.service;

import cn.lyy.authority_service_api.merchant.EditableResourcesDTO;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.merchant.dto.account.SubAccountUserInfoDTO;
import cn.lyy.merchant.dto.auth.RoleDTO;
import cn.lyy.merchant.dto.common.IdDTO;
import cn.lyy.merchant.dto.common.UserInfoDTO;
import cn.lyy.merchant.dto.menu.MerchantAuthMenuDTO;
import cn.lyy.merchant.dto.menu.MerchantAuthMenuGetDTO;
import cn.lyy.merchant.dto.template.IdNameVO;
import cn.lyy.merchant.dto.user.AdUserDTO;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import cn.lyy.merchant.dto.user.SubAccountDTO;
import cn.lyy.merchant.exception.BusinessException;
import java.util.List;

/**
 * <p>Title:saas2</p>
 * <p>Desc: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020/11/16
 */
public interface UserService {

    /**
     * 获取用户信息
     * @param phone
     * @return
     * @throws cn.lyy.merchant.exception.BusinessException
     */
    AdUserInfoDTO getUserInfo(String phone) throws cn.lyy.merchant.exception.BusinessException;

    /**
     * 注册后添加权限
     * @param adUserDTO
     * @return
     */
    BaseResponse register(AdUserDTO adUserDTO) throws BusinessException;

    /**
     * 修改密码
     * @param adUserDTO
     * @return
     */
    BaseResponse modifyPassword(AdUserDTO adUserDTO);

    /**
     * 获取用户权限菜单，即所有功能
     * @param param
     * @return
     */
    List<MerchantAuthMenuDTO> getUserAuthMenu(MerchantAuthMenuGetDTO param);

    /**
     * 根据角色获取得相应权限
     * @param roleId
     * @param resourcesDTOS
     * @return
     */
    List<EditableResourcesDTO> getResources(Long roleId , List<EditableResourcesDTO> resourcesDTOS);

    /**
     * 获取子账户信息
     * @param userInfoDTO    主账号信息
     * @return
     */
    List<SubAccountUserInfoDTO> getSubAccountInfos(IdDTO userInfoDTO);

    /**
     * 创建子账户
     * @return
     */
    int saveOrUpdateSubAccount(SubAccountDTO adUserDTO) throws BusinessException;

    /**
     * 删除子账户
     * @param idDTO
     * @return
     */
    BaseResponse deleteSubAccount(IdDTO idDTO) throws BusinessException;

    /**
     * 获取岗位列表
     * @param userInfoDTO
     * @return
     */
    List<IdNameVO> listSubAccountRole(UserInfoDTO userInfoDTO);

    /**
     * 获取岗位详情(拥有的权限列表)
     * @param idDTO
     * @return
     */
    List<String> getUserAuthMenu(IdDTO idDTO);

    /**
     * 创建/修改岗位
     * @param roleDTO
     * @return
     */
    BaseResponse saveSubAccountRole(RoleDTO roleDTO);

    /**
     * 删除岗位
     * @param idDTO
     * @return
     */
    BaseResponse deleteSubAccountRole(IdDTO idDTO);

    /**
     * 是否在3.0灰度商户名单中
     * @param userInfoDTO
     * @return
     */
    boolean isWhiteDistributor(AdUserInfoDTO userInfoDTO);

    boolean isSecondWhiteDistributor(AdUserInfoDTO userInfoDTO);

    boolean isOneWhiteDistributor(AdUserInfoDTO userInfoDTO);
}
