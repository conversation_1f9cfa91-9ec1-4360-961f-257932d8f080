package cn.lyy.merchant.service;

import cn.lyy.base.dto.Pagination;
import cn.lyy.lyy_ic_service_api.dto.*;
import cn.lyy.merchant.constants.BusinessChangeEnum;
import cn.lyy.merchant.dto.ChangeIcCardBalanceDTO;
import cn.lyy.merchant.dto.StatisticsItemDTO;
import cn.lyy.merchant.dto.response.IcCardConsumeDTO;
import cn.lyy.merchant.dto.response.PreferentialListDTO;

import java.math.BigDecimal;
import java.util.List;

public interface IcBusinessService {

    /**
     * 统计卡信息
     * @param adOrgId
     * @param queryStr
     * @param type
     * @param status
     * @return
     */
    IcCardTotalCountDTO findTotalCount(Long adOrgId, String queryStr, Integer type, Integer status);

    /**
     * 获取Ic卡列表
     * @param lyyIcListParamDTO
     * @return
     */
    Pagination<LyyIcCardDTO> queryListForMerchant(LyyIcListParamDTO lyyIcListParamDTO);

    /**
     * 根据卡号获取卡信息
     * @param adOrgId
     * @param cardNo
     * @return
     */
    LyyIcCardDetailDTO findByCardNo(Long adOrgId,String cardNo);

    /**
     * 获取IC卡banner图
     * @param adOrgId
     * @return
     */
    LyyIcConfigDTO icBanner(Long adOrgId);

    /**
     * 更新IC卡banner图
     * @param lyyIcConfigDTO
     * @param adOrgId
     * @return
     */
    Integer setBanner(LyyIcConfigDTO lyyIcConfigDTO, Long adOrgId);

    /**
     * IC卡管理页面-明细
     * @param requestDTO
     * @return
     */
    Pagination<IcCardConsumeDTO> listConsume(IcCardFlowQueryRequestDTO requestDTO);

    /**
     * 保存Ic卡信息
     * @param lyyIcCardDTO
     * @return
     */
    Long saveCard(LyyIcCardDTO lyyIcCardDTO);


    List<LyyIcFillingPreferentialGroupDto> getFillingPreferentialGroups(Long merchantId);

    /**
     * IC卡每日分析
     * @param date
     * @param beforeNum
     * @param merchantId
     * @return
     */
    List<StatisticsItemDTO> statisticsIcCardEveryDay(String date,Integer beforeNum,Long merchantId);

    /**
     * IC卡每月分析
     * @param date
     * @param beforeNum
     * @param merchantId
     * @return
     */
    List<StatisticsItemDTO> statisticsIcCardEveryMonth(String date,Integer beforeNum,Long merchantId);

    /**
     * 增减ic卡余额，只支持在线卡
     * @param changeIcCardBalanceDTO
     * @param businessChangeEnum
     * @param userType
     * @return
     */
    int incrOnlineCardBalance(ChangeIcCardBalanceDTO changeIcCardBalanceDTO, BusinessChangeEnum businessChangeEnum , Integer userType);

    /**
     * 获取ic卡余额修改记录
     * @param pageSize
     * @param pageIndex
     * @param type
     * @param adOrgId
     * @param searchKey
     * @return
     */
    Pagination<LyyIcCardFlowListDTO> listChangeIcCardBalance(Integer pageSize,Integer pageIndex,Integer type,Long adOrgId,String searchKey);

    /**
     * 保存充值优惠格则设置
     * @param id
     * @param price
     * @param value
     * @param distributorId
     * @param  adUserId
     */
    void saveFillingPreferential(Long id, BigDecimal price, BigDecimal value, Long distributorId, Long adUserId);

    /**
     * 查询充值套餐列表
     * @param adOrgIdNotNull
     * @return
     */
    List<PreferentialListDTO> fillingPreferentialList(Long adOrgIdNotNull);
}
