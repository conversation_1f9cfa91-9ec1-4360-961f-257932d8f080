package cn.lyy.merchant.service.merchant.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 资料状态
 */
@Getter
@Setter
@ToString
public class MerchantInfoStatusDTO {
    /**
     * 类型,定义见MerchantInfoStatusConstant#TypeEnum的code枚举值
     */
    private String type;
    /**
     * 状态,定义见MerchantInfoStatusConstant#StatusEnum的code枚举值
     */
    private String status;
    /**
     * 状态描述
     */
    private String desc;
    /**
     * 是否体验期
     */
    private Boolean isExperience;

    /**
     * 实名弹窗
     */
    private Boolean realNameDialog;

    public MerchantInfoStatusDTO(String type, String status, String desc) {
        this.type = type;
        this.status = status;
        this.desc = desc;
    }

    public MerchantInfoStatusDTO(String type, String status, String desc, Boolean realNameDialog) {
        this.type = type;
        this.status = status;
        this.desc = desc;
        this.realNameDialog = realNameDialog;
    }

}
