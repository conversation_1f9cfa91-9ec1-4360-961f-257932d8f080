package cn.lyy.merchant.service.merchant.impl;

import cn.lyy.merchant.api.service.MerchantWhiteClient;
import cn.lyy.merchant.config.MerchantWhitelistConfig;
import cn.lyy.merchant.service.merchant.MerchantWhitelistSwitchService;
import cn.lyy.merchant.utils.ResponseCheckUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j
@Service
public class MerchantWhitelistSwitchServiceImpl implements MerchantWhitelistSwitchService {
    @Autowired
    private MerchantWhitelistConfig merchantWhitelistConfig;
    @Autowired
    private MerchantWhiteClient merchantWhiteClient;
    @Override
    public Boolean switchMerchantWhitelist(Long distributorId, Integer type) {
        //如果当前白名单没有操作权限，返回false
        if(!merchantWhitelistConfig.include(type)){
            return false;
        }
        Boolean flag = ResponseCheckUtil.getData(merchantWhiteClient.switchMerchantWhitelist(distributorId,type));
        return flag;
    }
}
