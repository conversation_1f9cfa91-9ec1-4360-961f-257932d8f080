package cn.lyy.merchant.service.merchant.impl;

import static java.util.Optional.ofNullable;

import cn.hutool.core.collection.CollUtil;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.merchant.api.service.AdOrgAttachClient;
import cn.lyy.merchant.api.service.MerchantWhiteClient;
import cn.lyy.merchant.dto.merchant.AdOrgDTO;
import cn.lyy.merchant.dto.merchant.response.AdOrgAttachRespDTO;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import cn.lyy.merchant.exception.SessionTimeoutException;
import cn.lyy.merchant.service.merchant.MerchantMaterialService;
import cn.lyy.merchant.service.merchant.dto.MerchantInfoStatusConstant.StatusEnum;
import cn.lyy.merchant.service.merchant.dto.MerchantInfoStatusConstant.TypeEnum;
import cn.lyy.merchant.service.merchant.dto.MerchantInfoStatusDTO;
import cn.lyy.merchant.utils.ResponseCheckUtil;
import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.merchant.applyment.constants.material.ClearingBusinessSceneTypEnum;
import com.lyy.merchant.applyment.dto.channel.request.MerchantChannelAuthCompleteRequestDTO;
import com.lyy.merchant.applyment.dto.channel.response.MerchantChannelAuthCountResponseDTO;
import com.lyy.merchant.applyment.dto.material.response.MerchantInfoStatusRespDTO;
import com.lyy.merchant.bff.base.ResponseBody;
import com.lyy.merchant.bff.feign.MerchantChannelFeignClient;
import com.lyy.merchant.bff.feign.merchant.material.MerchantMaterialFeignClient;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 商户资料查询
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MerchantMaterialServiceImpl implements MerchantMaterialService {

    @Autowired
    private MerchantMaterialFeignClient merchantMaterialFeignClient;

    @Autowired
    private MerchantWhiteClient merchantWhiteClient;

    @Autowired
    private MerchantChannelFeignClient merchantChannelFeignClient;

    @Autowired
    private AdOrgAttachClient adOrgAttachClient;

    @Override
    public MerchantInfoStatusDTO getMerchantInfoStatus(Long adOrgId, Long cardId, List<MerchantInfoStatusRespDTO> merchantInfoStatusRespDTOS) {
        List<MerchantInfoStatusRespDTO> merchantInfoStatusResp = merchantInfoStatusRespDTOS;
        if (CollUtil.isEmpty(merchantInfoStatusResp)) {
            merchantInfoStatusResp = ResponseCheckUtil.getData(
                    merchantMaterialFeignClient.getMerchantInfoStatus(adOrgId, cardId));
        }
        boolean isExperience = isExperience(merchantInfoStatusResp);
        boolean realNameDialog = isRealNameDialog(merchantInfoStatusResp);

        log.info("[资料状态]商户ID:{},是否体验期:{},查询结果:{}", adOrgId, isExperience, merchantInfoStatusResp);

        Map<String, List<MerchantInfoStatusRespDTO>> infoStatusMap = ofNullable(merchantInfoStatusResp).orElse(new ArrayList<>()).stream()
                .filter(a -> Objects.nonNull(a.getStatus()))
                .collect(Collectors.groupingBy(t -> String.valueOf(t.getStatus())));
        infoStatusMap = ofNullable(infoStatusMap).orElse(new HashMap<>(1));

        MerchantInfoStatusDTO experienceExpireNotice = initExperienceExpireNotice(adOrgId, infoStatusMap);
        if (Objects.nonNull(experienceExpireNotice)) {
            experienceExpireNotice.setIsExperience(isExperience);
            experienceExpireNotice.setRealNameDialog(realNameDialog);
            return experienceExpireNotice;
        }

        MerchantInfoStatusDTO noSubmit = initSubmitInfoNoSubmit(adOrgId, infoStatusMap);
        if (Objects.nonNull(noSubmit)) {

            noSubmit.setIsExperience(isExperience);
            noSubmit.setRealNameDialog(realNameDialog);
            return noSubmit;
        }

        MerchantInfoStatusDTO reject = initSubmitInfoReject(adOrgId, infoStatusMap);
        if (Objects.nonNull(reject)) {
            reject.setIsExperience(isExperience);
            reject.setRealNameDialog(realNameDialog);
            return reject;
        }

        MerchantInfoStatusDTO modify = initSubmitInfoModify(adOrgId, infoStatusMap);
        if (Objects.nonNull(modify)) {
            modify.setIsExperience(isExperience);
            modify.setRealNameDialog(realNameDialog);
            return modify;
        }

        MerchantInfoStatusDTO realName = initSubmitInfoRealName(adOrgId, infoStatusMap);
        if (Objects.nonNull(realName)) {
            realName.setIsExperience(isExperience);
            realName.setRealNameDialog(realNameDialog);
            return realName;
        }

        MerchantInfoStatusDTO realNameDirect = initSubmitInfoWxRealNameDirect(adOrgId, infoStatusMap);
        if (Objects.nonNull(realNameDirect)) {
            realNameDirect.setIsExperience(isExperience);
            realNameDirect.setRealNameDialog(realNameDialog);
            return realNameDirect;
        }

        MerchantInfoStatusDTO aliRealNameDirect = initSubmitInfoAliRealNameDirect(adOrgId, infoStatusMap);
        if (Objects.nonNull(aliRealNameDirect)) {
            aliRealNameDirect.setIsExperience(isExperience);
            aliRealNameDirect.setRealNameDialog(realNameDialog);
            return aliRealNameDirect;
        }

        MerchantInfoStatusDTO inReview = initSubmitInfoInReview(adOrgId, infoStatusMap);
        if (Objects.nonNull(inReview)) {
            inReview.setIsExperience(isExperience);
            inReview.setRealNameDialog(realNameDialog);
            return inReview;
        }

        MerchantInfoStatusDTO approvedEffective = initSubmitInfoApprovedEffective(adOrgId, infoStatusMap);
        if (Objects.nonNull(approvedEffective)) {
            approvedEffective.setIsExperience(isExperience);
            approvedEffective.setRealNameDialog(realNameDialog);
            return approvedEffective;
        }

        MerchantInfoStatusDTO statusDefault = initSubmitInfoDefault(adOrgId);
        statusDefault.setIsExperience(isExperience);
        statusDefault.setRealNameDialog(realNameDialog);

        log.info("[资料状态]商户ID:{},转换结果:{}", adOrgId, statusDefault);
        return statusDefault;
    }

    @Override
    public MerchantInfoStatusDTO getMerchantInfoTipStatus(Long adOrgId, Long cardId, List<MerchantInfoStatusRespDTO> merchantInfoStatusRespDTOS) {
        return getMerchantInfoTipStatus(adOrgId, cardId, merchantInfoStatusRespDTOS, Boolean.FALSE);
    }

    /**
     * 新手任务消息通知卡状态查询
     * @param adOrgId
     * @param cardId 资料id 非必传
     * @param merchantInfoStatusRespDTOS 商户资料状态集合 非必传
     * @param isTipPage 是否新手任务弹窗入口
     * @return
     */
    @Override
    public MerchantInfoStatusDTO getMerchantInfoTipStatus(Long adOrgId, Long cardId, List<MerchantInfoStatusRespDTO> merchantInfoStatusRespDTOS, Boolean isTipPage) {
        List<MerchantInfoStatusRespDTO> merchantInfoStatusResp = merchantInfoStatusRespDTOS;
        if (CollUtil.isEmpty(merchantInfoStatusResp)) {
            merchantInfoStatusResp = ResponseCheckUtil.getData(
                    merchantMaterialFeignClient.getMerchantInfoStatus(adOrgId, cardId));
        }
        boolean isExperience = isExperience(merchantInfoStatusResp);
        boolean realNameDialog = isRealNameDialog(merchantInfoStatusResp);

        log.info("[资料状态提醒]商户ID:{},是否体验期:{},查询结果:{}", adOrgId, isExperience, merchantInfoStatusResp);

        Map<String, List<MerchantInfoStatusRespDTO>> infoStatusMap = ofNullable(merchantInfoStatusResp).orElse(new ArrayList<>()).stream()
                .filter(a -> Objects.nonNull(a.getStatus()))
                .collect(Collectors.groupingBy(t -> String.valueOf(t.getStatus())));
        infoStatusMap = ofNullable(infoStatusMap).orElse(new HashMap<>(1));

        if(isExperience){
            MerchantInfoStatusDTO statusDefault = initSubmitInfoDefault(adOrgId);
            log.info("[资料状态提醒]商户ID:{},已开通体验期", adOrgId);
            statusDefault.setIsExperience(isExperience);
            statusDefault.setRealNameDialog(realNameDialog);
            return statusDefault;
        }

        MerchantInfoStatusDTO noSubmit = initSubmitInfoNoSubmit(adOrgId, infoStatusMap);
        if (Objects.nonNull(noSubmit)) {
            // 如果是资金池下运营商商户并且是消息通知入口（非新手任务弹窗入口）,不给绑卡提醒,在消息通知里面不作提醒,默认绑卡成功
            if (!ofNullable(isTipPage).orElse(Boolean.FALSE) && isDealerWalletSettlementMch(adOrgId)) {
                MerchantInfoStatusDTO statusDefault = initSubmitInfoDefault(adOrgId);
                statusDefault.setIsExperience(isExperience);
                statusDefault.setRealNameDialog(realNameDialog);
                return statusDefault;
            } else {
                noSubmit.setIsExperience(isExperience);
                noSubmit.setRealNameDialog(realNameDialog);
                return noSubmit;
            }
        }

        boolean isExistAuth = isExistAuthComplete(adOrgId);
        if (isExistAuth) {
            MerchantInfoStatusDTO statusDefault = initSubmitInfoDefault(adOrgId);
            log.info("[资料状态提醒]商户ID:{},存在已实名单设置为绑卡成功", adOrgId);
            statusDefault.setIsExperience(isExperience);
            statusDefault.setRealNameDialog(realNameDialog);
            return statusDefault;
        }

        MerchantInfoStatusDTO reject = initSubmitInfoReject(adOrgId, infoStatusMap);
        if (Objects.nonNull(reject)) {
            reject.setIsExperience(isExperience);
            reject.setRealNameDialog(realNameDialog);
            return reject;
        }

        MerchantInfoStatusDTO modify = initSubmitInfoModify(adOrgId, infoStatusMap);
        if (Objects.nonNull(modify)) {
            modify.setIsExperience(isExperience);
            modify.setRealNameDialog(realNameDialog);
            return modify;
        }

        MerchantInfoStatusDTO realName = initSubmitInfoRealName(adOrgId, infoStatusMap);
        if (Objects.nonNull(realName)) {
            realName.setIsExperience(isExperience);
            realName.setRealNameDialog(realNameDialog);
            return realName;
        }

        MerchantInfoStatusDTO realNameDirect = initSubmitInfoWxRealNameDirect(adOrgId, infoStatusMap);
        if (Objects.nonNull(realNameDirect)) {
            realNameDirect.setIsExperience(isExperience);
            realNameDirect.setRealNameDialog(realNameDialog);
            return realNameDirect;
        }

        MerchantInfoStatusDTO aliRealNameDirect = initSubmitInfoAliRealNameDirect(adOrgId, infoStatusMap);
        if (Objects.nonNull(aliRealNameDirect)) {
            aliRealNameDirect.setIsExperience(isExperience);
            aliRealNameDirect.setRealNameDialog(realNameDialog);
            return aliRealNameDirect;
        }

        MerchantInfoStatusDTO inReview = initSubmitInfoInReview(adOrgId, infoStatusMap);
        if (Objects.nonNull(inReview)) {
            inReview.setIsExperience(isExperience);
            inReview.setRealNameDialog(realNameDialog);
            return inReview;
        }

        MerchantInfoStatusDTO approvedEffective = initSubmitInfoApprovedEffective(adOrgId, infoStatusMap);
        if (Objects.nonNull(approvedEffective)) {
            approvedEffective.setIsExperience(isExperience);
            approvedEffective.setRealNameDialog(realNameDialog);
            return approvedEffective;
        }

        MerchantInfoStatusDTO statusDefault = initSubmitInfoDefault(adOrgId);
        statusDefault.setIsExperience(isExperience);
        statusDefault.setRealNameDialog(realNameDialog);

        log.info("[资料状态提醒]商户ID:{},转换结果:{}", adOrgId, statusDefault);
        return statusDefault;
    }


    /**
     * 未提交资料验证
     *
     * @param infoStatusMap
     * @return
     */
    public MerchantInfoStatusDTO initSubmitInfoNoSubmit(Long adOrgId, Map<String, List<MerchantInfoStatusRespDTO>> infoStatusMap) {
        MerchantInfoStatusDTO result = null;
        boolean isExist = infoStatusMap.containsKey(StatusEnum.NOT_SUBMIT_INFO.getCode());
        if (isExist) {
            if (isNoBindCardWhite(adOrgId)) {
                log.info("[资料状态]白名单内可不提醒提交资料,商户ID:{}", adOrgId);
            } else {
                result = new MerchantInfoStatusDTO(TypeEnum.SUBMIT_INFO.getCode(), StatusEnum.NOT_SUBMIT_INFO.getCode(),
                        StatusEnum.NOT_SUBMIT_INFO.getMessage());
            }
        }
        return result;
    }


    /**
     * 资料审核不通过
     *
     * @param infoStatusMap
     * @return
     */
    public MerchantInfoStatusDTO initSubmitInfoReject(Long adOrgId, Map<String, List<MerchantInfoStatusRespDTO>> infoStatusMap) {
        MerchantInfoStatusDTO result = null;
        List<MerchantInfoStatusRespDTO> reject = infoStatusMap.get(StatusEnum.REVIEW_REJECTED.getCode());
        boolean isExist = CollectionUtils.isNotEmpty(reject);
        if (isExist) {
            if (isNoBindCardWhite(adOrgId)) {
                log.info("[资料状态]白名单内可不提交全驳回,商户ID:{}", adOrgId);
            } else {
                result = new MerchantInfoStatusDTO(TypeEnum.SUBMIT_INFO.getCode(), StatusEnum.REVIEW_REJECTED.getCode(),
                        StatusEnum.REVIEW_REJECTED.getMessage());
                log.info("[资料状态]资料审核不通过,商户ID:{}", adOrgId);
            }
        }
        return result;
    }

    /**
     * 资料驳回驳回
     *
     * @param infoStatusMap
     * @return
     */
    public MerchantInfoStatusDTO initSubmitInfoModify(Long adOrgId, Map<String, List<MerchantInfoStatusRespDTO>> infoStatusMap) {
        MerchantInfoStatusDTO result = null;
        List<MerchantInfoStatusRespDTO> reject = infoStatusMap.get(StatusEnum.TO_NE_MODIFY.getCode());
        boolean isExist = CollectionUtils.isNotEmpty(reject);
        if (isExist) {
            if (isNoBindCardWhite(adOrgId)) {
                log.info("[资料状态]白名单内可不提交部分驳回,商户ID:{}", adOrgId);
            } else {
                result = new MerchantInfoStatusDTO(TypeEnum.SUBMIT_INFO.getCode(), StatusEnum.TO_NE_MODIFY.getCode(),
                        StatusEnum.TO_NE_MODIFY.getMessage());
                log.info("[资料状态]资料驳回驳回,商户ID:{}", adOrgId);
            }
        }
        return result;
    }

    /**
     * 微信直连待实名
     *
     * @param infoStatusMap
     * @return
     */
    public MerchantInfoStatusDTO initSubmitInfoWxRealNameDirect(Long adOrgId, Map<String, List<MerchantInfoStatusRespDTO>> infoStatusMap) {
        MerchantInfoStatusDTO result = null;
        List<MerchantInfoStatusRespDTO> realNameDirect = infoStatusMap.get(StatusEnum.MICRO_MERCHANT_SIGN_CONTRACT.getCode());
        boolean isExist = CollectionUtils.isNotEmpty(realNameDirect);
        if (isExist) {
            log.info("[资料状态]微信直连待实名,商户ID:{}", adOrgId);
            result = new MerchantInfoStatusDTO(TypeEnum.REAL_NAME.getCode(), StatusEnum.MICRO_MERCHANT_SIGN_CONTRACT.getCode(),
                    StatusEnum.TO_NE_MODIFY.getMessage());
        }
        return result;
    }

    /**
     * 间连待实名
     *
     * @param infoStatusMap
     * @return
     */
    public MerchantInfoStatusDTO initSubmitInfoRealName(Long adOrgId, Map<String, List<MerchantInfoStatusRespDTO>> infoStatusMap) {
        MerchantInfoStatusDTO result = null;
        List<MerchantInfoStatusRespDTO> realNameDirect = infoStatusMap.get(StatusEnum.WECHAT_INTERNET_CONNECTION.getCode());
        boolean isExist = CollectionUtils.isNotEmpty(realNameDirect);
        if (isExist) {
            log.info("[资料状态]间连待实名,商户ID:{}", adOrgId);
            result = new MerchantInfoStatusDTO(TypeEnum.REAL_NAME.getCode(), StatusEnum.WECHAT_INTERNET_CONNECTION.getCode(),
                    StatusEnum.WECHAT_INTERNET_CONNECTION.getMessage());
        }
        return result;
    }

    /**
     * 直付通待签约
     *
     * @param infoStatusMap
     * @return
     */
    public MerchantInfoStatusDTO initSubmitInfoAliRealNameDirect(Long adOrgId, Map<String, List<MerchantInfoStatusRespDTO>> infoStatusMap) {
        MerchantInfoStatusDTO result = null;
        boolean isExist = infoStatusMap.containsKey(StatusEnum.ALIPAY_SIGN_CONTRACT.getCode());
        if (isExist) {
            log.info("[资料状态]直付通待签约,商户ID:{}", adOrgId);
            result = new MerchantInfoStatusDTO(TypeEnum.REAL_NAME.getCode(), StatusEnum.ALIPAY_SIGN_CONTRACT.getCode(),
                    StatusEnum.ALIPAY_SIGN_CONTRACT.getMessage());
        }
        return result;
    }

    /**
     * 审核通过
     *
     * @param infoStatusMap
     * @return
     */
    public MerchantInfoStatusDTO initSubmitInfoApprovedEffective(Long adOrgId, Map<String, List<MerchantInfoStatusRespDTO>> infoStatusMap) {
        MerchantInfoStatusDTO result = null;
        boolean isExist = infoStatusMap.containsKey(StatusEnum.APPROVED_EFFECTIVE.getCode());
        if (isExist) {
            log.info("[资料状态]审核通过,商户ID:{}", adOrgId);
            result = new MerchantInfoStatusDTO(TypeEnum.SUBMIT_INFO.getCode(), StatusEnum.APPROVED_EFFECTIVE.getCode(),
                    StatusEnum.APPROVED_EFFECTIVE.getMessage());
        }
        return result;
    }

    /**
     * 体验期过期预警
     *
     * @param infoStatusMap
     * @return
     */
    public MerchantInfoStatusDTO initExperienceExpireNotice(Long adOrgId, Map<String, List<MerchantInfoStatusRespDTO>> infoStatusMap) {
        MerchantInfoStatusDTO result = null;
        List<MerchantInfoStatusRespDTO> merchantInfoStatusRespDTOS = infoStatusMap.get(StatusEnum.EXPERIENCE_EXPIRE_NOTICE.getCode());
        if (CollUtil.isEmpty(merchantInfoStatusRespDTOS)) {
            return null;
        }
        MerchantInfoStatusRespDTO merchantInfoStatusRespDTO = merchantInfoStatusRespDTOS.stream()
                .filter(v -> v.getStatus().equals(StatusEnum.EXPERIENCE_EXPIRE_NOTICE.getCode())).findFirst().orElse(null);
        if (merchantInfoStatusRespDTO != null) {
            log.info("[体验期过期预警],商户ID:{}", adOrgId);
            result = new MerchantInfoStatusDTO(TypeEnum.EXPERIENCE_EXPIRE_NOTICE.getCode(), StatusEnum.EXPERIENCE_EXPIRE_NOTICE.getCode(),
                    merchantInfoStatusRespDTO.getReason());
        }
        return result;
    }

    /**
     * 资料审核中
     *
     * @param infoStatusMap
     * @return
     */
    public MerchantInfoStatusDTO initSubmitInfoInReview(Long adOrgId, Map<String, List<MerchantInfoStatusRespDTO>> infoStatusMap) {
        MerchantInfoStatusDTO result = null;
        boolean isExist = infoStatusMap.containsKey(StatusEnum.IN_REVIEW.getCode());
        if (isExist) {
            log.info("[资料状态]资料审核中,商户ID:{}", adOrgId);
            result = new MerchantInfoStatusDTO(TypeEnum.SUBMIT_INFO.getCode(), StatusEnum.IN_REVIEW.getCode(),
                    StatusEnum.IN_REVIEW.getMessage());
        }
        return result;
    }


    /**
     * 审核通过-默认
     *
     * @return
     */
    public MerchantInfoStatusDTO initSubmitInfoDefault(Long adOrgId) {
        MerchantInfoStatusDTO result = new MerchantInfoStatusDTO(TypeEnum.SUBMIT_INFO.getCode(), StatusEnum.APPROVED_EFFECTIVE.getCode(),
                StatusEnum.APPROVED_EFFECTIVE.getMessage());
        log.info("[资料状态]默认审核通过,商户ID:{}", adOrgId);
        return result;
    }

    /**
     * 可不绑卡白名单
     *
     * @param adOrgId
     * @return
     */
    public boolean isNoBindCardWhite(Long adOrgId) {
        boolean isWhite = ResponseCheckUtil.getData(merchantWhiteClient.isWhiteDistributor(adOrgId, 678)) ||
                ResponseCheckUtil.getData(merchantWhiteClient.isWhiteDistributor(adOrgId, 688)) ||
                ResponseCheckUtil.getData(merchantWhiteClient.isWhiteDistributor(adOrgId, 698));
        return isWhite;
    }

    /**
     * 是否存在已实名单
     *
     * @param adOrgId
     * @return
     */
    public boolean isExistAuthComplete(Long adOrgId) {
        MerchantChannelAuthCompleteRequestDTO req = new MerchantChannelAuthCompleteRequestDTO();
        req.setAdOrgId(adOrgId);
        MerchantChannelAuthCountResponseDTO authCount = null;
        ResponseBody<MerchantChannelAuthCountResponseDTO> resp = merchantChannelFeignClient.getAuthCompleteCount(req);
        if (GlobalErrorCode.OK.getCode().equals(resp.getCode()) && Objects.nonNull(resp.getBody())) {
            authCount = resp.getBody();
        }
        boolean isExist = Objects.nonNull(authCount) && ofNullable(authCount.getWxRealNameCount()).orElse(0) > 0;
        log.info("[资料状态]查询已完成的实名单数量,商户ID:{},是否存在已实名单:{},响应:{}", adOrgId, isExist, resp);
        return isExist;
    }

    /**
     * 判断是否体验期
     *
     * @param list
     * @return
     */
    public boolean isExperience(List<MerchantInfoStatusRespDTO> list) {
        MerchantInfoStatusRespDTO first = ofNullable(list).orElse(new ArrayList<>()).stream()
                .filter(o -> Objects.nonNull(o.getIsExperience()) && Boolean.TRUE.equals(o.getIsExperience())).findFirst().orElse(null);
        if (Objects.nonNull(first) && first.getIsExperience()) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 判断实名弹窗
     *
     * @param list
     * @return
     */
    public boolean isRealNameDialog(List<MerchantInfoStatusRespDTO> list) {
        MerchantInfoStatusRespDTO first = ofNullable(list).orElse(new ArrayList<>()).stream()
                .filter(o -> Objects.nonNull(o.getRealNameDialog()) && Boolean.TRUE.equals(o.getRealNameDialog())).findFirst().orElse(null);
        if (Objects.nonNull(first) && first.getRealNameDialog()) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 判断是否资金池经销商下面的商家
     * isWalletSettlement=ture运营商是经销商下面的商家
     * @param adOrgId 机构ID
     * @return 如果满足初始化条件（钱包结算或经销商场景），则返回true；否则返回false
     */
    public boolean isDealerWalletSettlementMch(Long adOrgId) {
        if (Objects.isNull(adOrgId)) {
            return false;
        }
        AdOrgAttachRespDTO adOrgAttach = ofNullable(adOrgAttachClient.getAttachByOrgId(adOrgId))
                .filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode())
                .map(BaseResponse::getData).orElse(null);
        if (Objects.isNull(adOrgAttach)) {
            return false;
        }
        boolean isWalletSettlement = Boolean.TRUE.equals(adOrgAttach.getIsWalletSettlement());
        if (isWalletSettlement) {
            log.info("[商户绑卡状态查询]资金池商户进件斗拱默认手动结算,商户ID:{}", adOrgId);
        }
        return isWalletSettlement;
    }

}
