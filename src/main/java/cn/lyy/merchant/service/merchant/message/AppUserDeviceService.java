package cn.lyy.merchant.service.merchant.message;

import cn.lyy.merchant.config.message.AppMessageConfig;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import cn.lyy.merchant.service.merchant.dto.message.AppDeviceRegisterDTO;
import cn.lyy.merchant.utils.RemoteResponseUtils;
import cn.lyy.message.UnifiedAppUserFeignClient;
import cn.lyy.message.UnifiedParticipantFeignClient;
import cn.lyy.message.constants.app.DeviceType;
import cn.lyy.message.constants.participant.ChannelAppTypeEnum;
import cn.lyy.message.dto.app.DeviceRegisterDTO;
import cn.lyy.message.dto.app.UnBindingUserDTO;
import cn.lyy.message.dto.app.response.ParticipantChannelRelationResponse;
import com.lyy.starter.common.resp.RespBody;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025/6/26 - 14:42
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AppUserDeviceService {

    private final UnifiedAppUserFeignClient unifiedAppUserFeignClient;
    private final UnifiedParticipantFeignClient unifiedParticipantFeignClient;
    private final AppMessageConfig appMessageConfig;

    private final static String USER_TYPE_KEY = "MERCHANT_AD_USER";

    public Boolean registerDevice(AppDeviceRegisterDTO dto, AdUserInfoDTO currentUser) {
        String participantAppId = appMessageConfig.getParticipantAppId();
        String appKey = getAppKey(dto, participantAppId);
        if (appKey == null) {
            return false;
        }

        DeviceRegisterDTO registerDTO = new DeviceRegisterDTO();
        registerDTO.setParticipantAppId(participantAppId);
        registerDTO.setDeviceId(dto.getDeviceId());
        registerDTO.setUserId(String.valueOf(currentUser.getAdUserId()));
        registerDTO.setUserType(USER_TYPE_KEY);
        registerDTO.setAppKey(appKey);
        RespBody<Boolean> respBody = unifiedAppUserFeignClient.registerDevice(registerDTO);
        return RemoteResponseUtils.getData(respBody);
    }

    public Boolean bindingUser(AppDeviceRegisterDTO dto) {
        return null;
    }

    public Boolean unbindingUser(AppDeviceRegisterDTO dto, AdUserInfoDTO currentUser) {
        String participantAppId = appMessageConfig.getParticipantAppId();
        String appKey = getAppKey(dto, participantAppId);
        if (appKey == null) {
            return false;
        }

        UnBindingUserDTO unbinding = new UnBindingUserDTO();
        unbinding.setParticipantAppId(participantAppId);
        unbinding.setDeviceId(dto.getDeviceId());
        unbinding.setUserId(String.valueOf(currentUser.getAdUserId()));
        unbinding.setAppKey(appKey);
        RespBody<Boolean> respBody = unifiedAppUserFeignClient.unbindingUser(unbinding);
        return RemoteResponseUtils.getData(respBody);
    }

    private String getAppKey(AppDeviceRegisterDTO dto, String participantAppId) {
        ChannelAppTypeEnum channelAppType = DeviceType.getChannelAppType(dto.getDeviceType());
        if (channelAppType == null) {
            log.error("设备类型错误");
            return null;
        }

        String appKey = RemoteResponseUtils.getData(unifiedParticipantFeignClient.listByParticipantAppId(participantAppId)).stream()
                .filter(o -> o.getChannelAppType().equals(channelAppType.getCode()))
                .findFirst()
                .map(ParticipantChannelRelationResponse::getChannelAppKey)
                .orElse(null);
        if (appKey == null) {
            log.error("appKey为空");
            return null;
        }
        log.info("appKey:{}", appKey);
        return appKey;
    }
}
