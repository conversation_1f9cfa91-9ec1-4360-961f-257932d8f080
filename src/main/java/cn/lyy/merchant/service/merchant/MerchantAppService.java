package cn.lyy.merchant.service.merchant;

import cn.lyy.base.communal.constant.PrimaryCategoryEnum;
import cn.lyy.merchant.constants.SysPageFunctionConstants;
import cn.lyy.merchant.dto.merchant.AdOrgAttachDTO;
import cn.lyy.merchant.dto.merchant.PrimaryCategoryInitDTO;
import cn.lyy.merchant.repository.merchant.MerchantRepository;
import com.lyy.ram.service.interfaces.dto.function.request.InitPrimaryCategoryFunctionReq;
import com.lyy.ram.service.interfaces.feign.IFunctionFeignClient;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025/6/10 - 18:34
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MerchantAppService {

    private final IFunctionFeignClient functionFeignClient;
    private final MerchantRepository merchantRepository;

    public Boolean initPrimaryCategoryFunction(PrimaryCategoryInitDTO dto, Long authUserId, Long merchantId) {
        String primaryCategoryCode = dto.getPrimaryCategory();
        return Optional.ofNullable(primaryCategoryCode)
                .map(PrimaryCategoryEnum::getCategory)
                .map(primaryCategory -> {
                    AdOrgAttachDTO attach = merchantRepository.getMerchantAttach(merchantId);
                    if (Objects.nonNull(attach) && Objects.nonNull(attach.getPrimaryCategory())) {
                        return false;
                    }
                    Boolean updated = merchantRepository.updateMerchantAttach(merchantId, primaryCategory.getCode());
                    log.info("初始化商户主营品类, {}, {}", merchantId, primaryCategory);
                    if (updated) {
                        InitPrimaryCategoryFunctionReq req = new InitPrimaryCategoryFunctionReq();
                        req.setPageId(SysPageFunctionConstants.MERCHANT_HOME_PAGE_ID);
                        req.setAuthSystemId(SysPageFunctionConstants.AUTH_SYSTEM_ID);
                        req.setAuthUserId(String.valueOf(authUserId));
                        req.setPrimaryCategory(primaryCategory.name());
                        functionFeignClient.initPrimaryCategoryFunction(req);
                    }
                    return true;
                })
                .orElse(false);
    }

}
