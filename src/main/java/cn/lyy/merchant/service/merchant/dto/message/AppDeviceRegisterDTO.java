package cn.lyy.merchant.service.merchant.dto.message;

import cn.lyy.message.constants.app.DeviceModelEnum;
import cn.lyy.message.constants.app.DeviceType;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/6/26 - 15:33
 */
@Data
public class AppDeviceRegisterDTO {

    @NotNull
    private String deviceId;
    /**
     * 操作系统类型
     *
     * @see DeviceType#getCode()
     */
    @NotNull
    private String deviceType;
    /**
     * 安卓手机品牌
     *
     * @see DeviceModelEnum#getCode()
     */
    private String deviceModel;
    private String accountId;
    private String appVersion;
}
