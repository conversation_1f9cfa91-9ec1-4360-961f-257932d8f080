package cn.lyy.merchant.service.merchant.dto;

import lombok.Data;
import lombok.Getter;

/**
 * 商户资料状态
 */
@Data
public class MerchantInfoStatusConstant {

    /**
     * 资料类型
     */
    @Getter
    public enum TypeEnum{



        SUBMIT_INFO("1", 3,"提交资料"),
        EXPERIENCE_EXPIRE_NOTICE("6", 7,"体验期过期预警"),
        SUPPLEMENT_INFO("2", 10,"补充资料"),
        REAL_NAME("3", 15,"实名"),
        RISK_MANAGEMENT("4", 2,"风控"),
        PAY_SWITCH("5", 1, "支付开关")
        ;

        TypeEnum(String code, Integer sort, String message) {
            this.code = code;
            this.sort = sort;
            this.message = message;
        }

        private String code;
        private Integer sort;
        private String message;

        public static Integer getSortByCode(Integer code){
            for (TypeEnum value : TypeEnum.values()) {
                if (value.getCode().equals(code)) {
                    return value.getSort();
                }
            }
            return 0;
        }
    }


    /**
     * 状态值
     */
    @Getter
    public enum StatusEnum{

        //TypeEnum.SUBMIT_INFO
        NOT_SUBMIT_INFO("-1", "商户未提交资料"),
        REVIEW_REJECTED("0", "资料审核不通过"),
        IN_REVIEW("1", "资料审核中"),
        APPROVED_NOT_EFFECTIVE("2", " 审核通过，12点生效"),
        APPROVED_EFFECTIVE("3", "审核通过,已生效"),
        TO_NE_MODIFY("40", "您有资料需要修改，请及时补充填写"),

        //TypeEnum.SUPPLEMENT_INFO
        TO_BE_ADDED("20", "商户资料待补充"),
        SUPPLEMENT_IN_REVIEW("21", "商户补充资料审核中"),
        SUPPLEMENT_APPROVED("22", "商户补充资料审核通过"),
        SUPPLEMENT_REVIEW_REJECTED("23", "商户补充资料审核不通过"),

        //TypeEnum.REAL_NAME
        MICRO_MERCHANT_SIGN_CONTRACT( "30", "商户申请-小微商签约"),
        WECHAT_INTERNET_CONNECTION("31", "微信间连"),
        ALIPAY_SIGN_CONTRACT("32", "直付通签约"),

        //TypeEnum.EXPERIENCE_EXPIRE_NOTICE
        EXPERIENCE_EXPIRE_NOTICE("33","体验期过期预警"),

        //TypeEnum.RISK_MANAGEMENT
        RISK_MANAGEMENT( "35", "风控"),
        ;

        StatusEnum(String code, String message) {
            this.code = code;
            this.message = message;
        }

        private String code;
        private String message;

        public static StatusEnum getStatusByCode(String code){
            for (StatusEnum value : StatusEnum.values()) {
                if (value.getCode().equals(code)) {
                    return value;
                }
            }
            return null;
        }
    }
}
