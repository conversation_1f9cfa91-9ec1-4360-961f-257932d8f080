package cn.lyy.merchant.service.merchant;

import cn.lyy.merchant.service.merchant.dto.MerchantInfoStatusDTO;
import com.lyy.merchant.applyment.dto.material.response.MerchantInfoStatusRespDTO;
import java.util.List;

/**
 * 商户资料查询
 *
 * <AUTHOR>
 */
public interface MerchantMaterialService {

    /**
     * 资料状态
     *
     * @param adOrgId
     * @param cardId 资料id 非必传
     * @param merchantInfoStatusRespDTOS 商户资料状态集合 非必传
     * @return
     */
    MerchantInfoStatusDTO getMerchantInfoStatus(Long adOrgId, Long cardId, List<MerchantInfoStatusRespDTO> merchantInfoStatusRespDTOS);

    /**
     * 资料状态
     *
     * @param adOrgId
     * @param cardId 资料id 非必传
     * @param merchantInfoStatusRespDTOS 商户资料状态集合 非必传
     * @return
     */
    MerchantInfoStatusDTO getMerchantInfoTipStatus(Long adOrgId, Long cardId, List<MerchantInfoStatusRespDTO> merchantInfoStatusRespDTOS);

    /**
     * 资料状态
     *
     * @param adOrgId
     * @param cardId 资料id 非必传
     * @param merchantInfoStatusRespDTOS 商户资料状态集合 非必传
     * @param isTipPage 是否新手任务弹窗入口
     * @return
     */
    MerchantInfoStatusDTO getMerchantInfoTipStatus(Long adOrgId, Long cardId, List<MerchantInfoStatusRespDTO> merchantInfoStatusRespDTOS, Boolean isTipPage);

}
