package cn.lyy.merchant.service.onedata.columns.selectdb;

public class CityColumnArray {

	public static final String CITY_COMMON_COLUMN_ARRAY =  "city_name as `cityName`"
    		+" ,count(distinct equipment_id) as `equipmentCount`"
    		+" ,count(distinct equipment_group_id) as `equipmentGroupCount`"
    		+" ,sum(pay_amount) - sum(online_service_amount) as `totalAmount`"
    		+" ,sum(order_counts) as `orderCount`"
    		+" ,sum(total_start_counts) as `totalStartCount`"
    		+" ,cast(case when sum(order_counts) = 0 then 0 else (sum(pay_amount) - sum(online_service_amount)) / sum(order_counts) end as decimal(15,2)) as `perOrderAmount`"
    		+" ,cast(case when (sum(last_cycle_pay_amount) - sum(last_cycle_online_service_amount)) = 0 then 1 else (sum(pay_amount) - sum(online_service_amount) - sum(last_cycle_pay_amount) + sum(last_cycle_online_service_amount)) / (sum(last_cycle_pay_amount) - sum(last_cycle_online_service_amount)) end as decimal(15,2)) as `amountUp`"
    		+" ,cast(case when sum(last_cycle_order_counts) = 0 then 1 else (sum(order_counts) - sum(last_cycle_order_counts)) / sum(last_cycle_order_counts) end as decimal(15,2)) as `orderCountUp`"
    		+" ,cast(case when sum(last_cycle_total_start_counts) = 0 then 1 else (sum(total_start_counts) - sum(last_cycle_total_start_counts)) /sum(last_cycle_total_start_counts) end as decimal(15,2)) as `startCountUp`" ;

    public static final String CITY_AVG_COLUMN_ARRAY = "cast(case when count(distinct city_name) =0 then 0 else sum(order_counts) / count(distinct city_name) end as decimal(15,2)) as `avgOrderCount`"
    		+ "	,cast(case when sum(order_counts) = 0 then 0 else (sum(pay_amount) - sum(online_service_amount)) / sum(order_counts) end as decimal(15,2)) as `perOrderAmount`"
    		+ "	,cast(case when count(distinct city_name) = 0 then 0 else sum(total_start_counts) /  count(distinct city_name) end as decimal(15,2)) as `avgStartCount`"
    		+ "	,cast(case when count(distinct city_name) = 0 then 0 else (sum(pay_amount) - sum(online_service_amount)) / count(distinct city_name) end as decimal(15,2)) as `avgAmount`" ;
    
}
