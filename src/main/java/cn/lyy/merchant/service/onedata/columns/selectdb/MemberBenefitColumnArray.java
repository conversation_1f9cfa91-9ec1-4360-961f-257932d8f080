package cn.lyy.merchant.service.onedata.columns.selectdb;

public class MemberBenefitColumnArray {

	public static final String DAY_MEMBER_BENEFIT_COLUMN_ARRAY = 
			"merchant_id as `merchantId`, benefit_kind as `benefitType`, " +
            //新增储值额度
            "increase_benefit_amount as `addAmount`, " +
            "cast(case when last_cycle_increase_benefit_amount =0 and increase_benefit_amount>0 then 1 when last_cycle_increase_benefit_amount=0 and increase_benefit_amount=0 then 0 else (increase_benefit_amount-last_cycle_increase_benefit_amount)/last_cycle_increase_benefit_amount end as decimal(15,2)) as `addAmountUp`," +

            //储值支付金额
            "benefit_payment_amount  as `benefitPayAmount`, " +
            "cast(case when last_cycle_benefit_payment_amount  =0 and benefit_payment_amount>0 then 1 when last_cycle_benefit_payment_amount =0 and benefit_payment_amount=0 then 0 else (benefit_payment_amount-last_cycle_benefit_payment_amount )/last_cycle_benefit_payment_amount  end as decimal(15,2)) as `benefitPayAmountUp`," +

            //储值订单数
            "benefit_payment_order_num as `benefitOrders`, " +
            "cast(case when last_cycle_benefit_payment_order_num =0 and benefit_payment_order_num>0 then 1 when benefit_payment_order_num=0 and last_cycle_benefit_payment_order_num=0 then 0 else (benefit_payment_order_num-last_cycle_benefit_payment_order_num)/last_cycle_benefit_payment_order_num end as decimal(15,2)) as `benefitOrdersUp`," +

            //储值会员
            "benefit_payment_user_num as `benefitUsers`, " +
            "cast(case when last_cycle_benefit_payment_user_num =0 and benefit_payment_user_num>0 then 1 when last_cycle_benefit_payment_user_num=0 and benefit_payment_user_num=0 then 0 else (benefit_payment_user_num-last_cycle_benefit_payment_user_num)/last_cycle_benefit_payment_user_num end as decimal(15,2)) as `benefitUsersUp`," +

            //储值客单价
            "avg_payment_amount_per_user as `orderPrice`, " +
            "cast(case when last_cycle_avg_payment_amount_per_user =0 and avg_payment_amount_per_user>0 then 1 when last_cycle_avg_payment_amount_per_user=0 and avg_payment_amount_per_user=0 then 0 else (avg_payment_amount_per_user-last_cycle_avg_payment_amount_per_user)/last_cycle_avg_payment_amount_per_user end as decimal(15,2)) as `orderPriceUp`," +

            //储值设备数
            "benefit_payment_equip_num as `benefitEquipments`, " +
            "cast(case when last_cycle_benefit_payment_equip_num =0 and benefit_payment_equip_num>0 then 1 when last_cycle_benefit_payment_equip_num=0 and benefit_payment_equip_num=0 then 0 else (benefit_payment_equip_num-last_cycle_benefit_payment_equip_num)/last_cycle_benefit_payment_equip_num end as decimal(15,2)) as `benefitEquipmentsUp`," +

            //储值场地数
            "benefit_payment_group_num as `benefitGroups`, " +
            "cast(case when last_cycle_benefit_payment_group_num =0 and benefit_payment_group_num>0 then 1 when last_cycle_benefit_payment_group_num=0 and benefit_payment_group_num=0 then 0 else (benefit_payment_group_num-last_cycle_benefit_payment_group_num)/last_cycle_benefit_payment_group_num end as decimal(15,2)) as `benefitGroupsUp`," +

            //消耗储值
            "comsume_benefit_amount as `consumerBenefit`, " +
            "cast(case when last_cycle_comsume_benefit_amount =0 and comsume_benefit_amount>0 then 1 when last_cycle_comsume_benefit_amount=0 and comsume_benefit_amount=0 then 0 else (comsume_benefit_amount-last_cycle_comsume_benefit_amount)/last_cycle_comsume_benefit_amount end as decimal(15,2)) as `consumerBenefitUp`," +

            //消耗储值订单数
            "benefit_consume_order_num as `consumerBenefitOrders`, " +
            "cast(case when last_cycle_benefit_consume_order_num =0 and benefit_consume_order_num>0 then 1 when last_cycle_benefit_consume_order_num=0 and benefit_consume_order_num=0 then 0 else (benefit_consume_order_num-last_cycle_benefit_consume_order_num)/last_cycle_benefit_consume_order_num end as decimal(15,2)) as `consumerBenefitOrdersUp`," +


            //消耗储值会员
            "benefit_consume_user_num as `consumerBenefitUsers`, " +
            "cast(case when last_cycle_benefit_consume_user_num =0 and benefit_consume_user_num>0 then 1 when last_cycle_benefit_consume_user_num=0 and benefit_consume_user_num=0 then 0 else (benefit_consume_user_num-last_cycle_benefit_consume_user_num)/last_cycle_benefit_consume_user_num end as decimal(15,2)) as `consumerBenefitUsersUp`," +


            //消耗储值客单价
            "avg_consume_amount_per_user as `consumerBenefitOrderPrice`, " +
            "cast(case when last_cycle_avg_consume_amount_per_user =0 and avg_consume_amount_per_user>0 then 1 when last_cycle_avg_consume_amount_per_user=0 and avg_consume_amount_per_user=0 then 0 else (avg_consume_amount_per_user-last_cycle_avg_consume_amount_per_user)/last_cycle_avg_consume_amount_per_user end as decimal(15,2)) as `consumerBenefitOrderPriceUp`," +

            //消耗储值设备数
            "benefit_consume_equip_num as `consumerBenefitEquipments`, " +
            "cast(case when last_cycle_benefit_consume_equip_num =0 and benefit_consume_equip_num>0 then 1 when last_cycle_benefit_consume_equip_num=0 and benefit_consume_equip_num=0 then 0 else (benefit_consume_equip_num-last_cycle_benefit_consume_equip_num)/last_cycle_benefit_consume_equip_num end as decimal(15,2)) as `consumerBenefitEquipmentsUp`," +

            //消耗储值场地数
            "benefit_consume_group_num as `consumerBenefitGroups`, " +
            "cast(case when last_cycle_benefit_consume_group_num =0 and benefit_consume_group_num>0 then 1 when last_cycle_benefit_consume_group_num=0 and benefit_consume_group_num=0 then 0 else (benefit_consume_group_num-last_cycle_benefit_consume_group_num)/last_cycle_benefit_consume_group_num end as decimal(15,2)) as `consumerBenefitGroupsUp`," +


            //消耗储值启动设备数
            "consume_benefit_tostart_equip_num as `consumerBenefitStartEquipments`, " +
            "cast(case when last_cycle_consume_benefit_tostart_equip_num =0 and consume_benefit_tostart_equip_num>0 then 1 when last_cycle_consume_benefit_tostart_equip_num=0 and consume_benefit_tostart_equip_num=0 then 0 else (consume_benefit_tostart_equip_num-last_cycle_consume_benefit_tostart_equip_num)/last_cycle_consume_benefit_tostart_equip_num end as decimal(15,2)) as `consumerBenefitStartEquipmentsUp`," +

            //消耗储值启动设备日均数
            "per_day_consumetostart_equip_num as `perDayConsumerBenefitStartEquipments`, " +
            "cast(case when last_cycle_per_day_consumetostart_equip_num =0 and per_day_consumetostart_equip_num>0 then 1 when last_cycle_per_day_consumetostart_equip_num=0 and per_day_consumetostart_equip_num=0 then 0 else (per_day_consumetostart_equip_num-last_cycle_per_day_consumetostart_equip_num)/last_cycle_per_day_consumetostart_equip_num end as decimal(15,2)) as `perDayConsumerBenefitStartEquipmentsUp`" ;

    
	
	public static final String WEEK_MEMBER_BENEFIT_COLUMN_ARRAY = 
			"merchant_id as `merchantId`, benefit_kind as `benefitType`, " +
			//新增储值额度
			"increase_benefit_amount as `addAmount`, " +
			"cast(case when last_cycle_increase_benefit_amount =0 and increase_benefit_amount>0 then 1 when last_cycle_increase_benefit_amount=0 and increase_benefit_amount=0 then 0 else (increase_benefit_amount-last_cycle_increase_benefit_amount)/last_cycle_increase_benefit_amount end as decimal(15,2)) as `addAmountUp`," +

			//储值支付金额
			"benefit_payment_amount  as `benefitPayAmount`, " +
			"cast(case when last_cycle_benefit_payment_amount  =0 and benefit_payment_amount>0 then 1 when last_cycle_benefit_payment_amount =0 and benefit_payment_amount=0 then 0 else (benefit_payment_amount-last_cycle_benefit_payment_amount )/last_cycle_benefit_payment_amount  end as decimal(15,2)) as `benefitPayAmountUp`," +


			//储值订单数
			"benefit_payment_order_num as `benefitOrders`, " +
			"cast(case when last_cycle_benefit_payment_order_num =0 and benefit_payment_order_num>0 then 1 when benefit_payment_order_num=0 and last_cycle_benefit_payment_order_num=0 then 0 else (benefit_payment_order_num-last_cycle_benefit_payment_order_num)/last_cycle_benefit_payment_order_num end as decimal(15,2)) as `benefitOrdersUp`," +

			//储值会员
			"benefit_payment_user_num as `benefitUsers`, " +
			"cast(case when last_cycle_benefit_payment_user_num =0 and benefit_payment_user_num>0 then 1 when last_cycle_benefit_payment_user_num=0 and benefit_payment_user_num=0 then 0 else (benefit_payment_user_num-last_cycle_benefit_payment_user_num)/last_cycle_benefit_payment_user_num end as decimal(15,2)) as `benefitUsersUp`," +

			//储值客单价
			"avg_payment_amount_per_user as `orderPrice`, " +
			"cast(case when last_cycle_avg_payment_amount_per_user =0 and avg_payment_amount_per_user>0 then 1 when last_cycle_avg_payment_amount_per_user=0 and avg_payment_amount_per_user=0 then 0 else (avg_payment_amount_per_user-last_cycle_avg_payment_amount_per_user)/last_cycle_avg_payment_amount_per_user end as decimal(15,2)) as `orderPriceUp`," +

			//储值设备数
			"benefit_payment_equip_num as `benefitEquipments`, " +
			"cast(case when last_cycle_benefit_payment_equip_num =0 and benefit_payment_equip_num>0 then 1 when last_cycle_benefit_payment_equip_num=0 and benefit_payment_equip_num=0 then 0 else (benefit_payment_equip_num-last_cycle_benefit_payment_equip_num)/last_cycle_benefit_payment_equip_num end as decimal(15,2)) as `benefitEquipmentsUp`," +

			//储值场地数
			"benefit_payment_group_num as `benefitGroups`, " +
			"cast(case when last_cycle_benefit_payment_group_num =0 and benefit_payment_group_num>0 then 1 when last_cycle_benefit_payment_group_num=0 and benefit_payment_group_num=0 then 0 else (benefit_payment_group_num-last_cycle_benefit_payment_group_num)/last_cycle_benefit_payment_group_num end as decimal(15,2)) as `benefitGroupsUp`," +

			//消耗储值
			"comsume_benefit_amount as `consumerBenefit`, " +
			"cast(case when last_cycle_comsume_benefit_amount =0 and comsume_benefit_amount>0 then 1 when last_cycle_comsume_benefit_amount=0 and comsume_benefit_amount=0 then 0 else (comsume_benefit_amount-last_cycle_comsume_benefit_amount)/last_cycle_comsume_benefit_amount end as decimal(15,2)) as `consumerBenefitUp`," +

			//消耗储值订单数
			"benefit_consume_order_num as `consumerBenefitOrders`, " +
			"cast(case when last_cycle_benefit_consume_order_num =0 and benefit_consume_order_num>0 then 1 when last_cycle_benefit_consume_order_num=0 and benefit_consume_order_num=0 then 0 else (benefit_consume_order_num-last_cycle_benefit_consume_order_num)/last_cycle_benefit_consume_order_num end as decimal(15,2)) as `consumerBenefitOrdersUp`," +


			//消耗储值会员
			"benefit_consume_user_num as `consumerBenefitUsers`, " +
			"cast(case when last_cycle_benefit_consume_user_num =0 and benefit_consume_user_num>0 then 1 when last_cycle_benefit_consume_user_num=0 and benefit_consume_user_num=0 then 0 else (benefit_consume_user_num-last_cycle_benefit_consume_user_num)/last_cycle_benefit_consume_user_num end as decimal(15,2)) as `consumerBenefitUsersUp`," +


			//消耗储值客单价
			"avg_consume_amount_per_user as `consumerBenefitOrderPrice`, " +
			"cast(case when last_cycle_avg_consume_amount_per_user =0 and avg_consume_amount_per_user>0 then 1 when last_cycle_avg_consume_amount_per_user=0 and avg_consume_amount_per_user=0 then 0 else (avg_consume_amount_per_user-last_cycle_avg_consume_amount_per_user)/last_cycle_avg_consume_amount_per_user end as decimal(15,2)) as `consumerBenefitOrderPriceUp`," +

			//消耗储值设备数
			"benefit_consume_equip_num as `consumerBenefitEquipments`, " +
			"cast(case when last_cycle_benefit_consume_equip_num =0 and benefit_consume_equip_num>0 then 1 when last_cycle_benefit_consume_equip_num=0 and benefit_consume_equip_num=0 then 0 else (benefit_consume_equip_num-last_cycle_benefit_consume_equip_num)/last_cycle_benefit_consume_equip_num end as decimal(15,2)) as `consumerBenefitEquipmentsUp`," +

			//消耗储值场地数
			"benefit_consume_group_num as `consumerBenefitGroups`, " +
			"cast(case when last_cycle_benefit_consume_group_num =0 and benefit_consume_group_num>0 then 1 when last_cycle_benefit_consume_group_num=0 and benefit_consume_group_num=0 then 0 else (benefit_consume_group_num-last_cycle_benefit_consume_group_num)/last_cycle_benefit_consume_group_num end as decimal(15,2)) as `consumerBenefitGroupsUp`," +


			//消耗储值启动设备数
			"consume_benefit_tostart_equip_num as `consumerBenefitStartEquipments`, " +
			"cast(case when last_cycle_consume_benefit_tostart_equip_num =0 and consume_benefit_tostart_equip_num>0 then 1 when last_cycle_consume_benefit_tostart_equip_num=0 and consume_benefit_tostart_equip_num=0 then 0 else (consume_benefit_tostart_equip_num-last_cycle_consume_benefit_tostart_equip_num)/last_cycle_consume_benefit_tostart_equip_num end as decimal(15,2)) as `consumerBenefitStartEquipmentsUp`," +

			//消耗储值启动设备日均数
			"per_day_consumetostart_equip_num as `perDayConsumerBenefitStartEquipments`, " +
			"cast(case when last_cycle_per_day_consumetostart_equip_num =0 and per_day_consumetostart_equip_num>0 then 1 when last_cycle_per_day_consumetostart_equip_num=0 and per_day_consumetostart_equip_num=0 then 0 else (per_day_consumetostart_equip_num-last_cycle_per_day_consumetostart_equip_num)/last_cycle_per_day_consumetostart_equip_num end as decimal(15,2)) as `perDayConsumerBenefitStartEquipmentsUp`" ;

	
	public static final String MONTH_MEMBER_BENEFIT_COLUMN_ARRAY = 
		  "merchant_id as `merchantId`, benefit_kind as `benefitType`, " +
          //新增储值额度
          "increase_benefit_amount as `addAmount`, " +
          "cast(case when last_cycle_increase_benefit_amount =0 and increase_benefit_amount>0 then 1 when last_cycle_increase_benefit_amount=0 and increase_benefit_amount=0 then 0 else (increase_benefit_amount-last_cycle_increase_benefit_amount)/last_cycle_increase_benefit_amount end as decimal(15,2)) as `addAmountUp`," +

          //储值支付金额
          "benefit_payment_amount  as `benefitPayAmount`, " +
          "cast(case when last_cycle_benefit_payment_amount  =0 and benefit_payment_amount>0 then 1 when last_cycle_benefit_payment_amount =0 and benefit_payment_amount=0 then 0 else (benefit_payment_amount-last_cycle_benefit_payment_amount )/last_cycle_benefit_payment_amount  end as decimal(15,2)) as `benefitPayAmountUp`," +

          //储值订单数
          "benefit_payment_order_num as `benefitOrders`, " +
          "cast(case when last_cycle_benefit_payment_order_num =0 and benefit_payment_order_num>0 then 1 when benefit_payment_order_num=0 and last_cycle_benefit_payment_order_num=0 then 0 else (benefit_payment_order_num-last_cycle_benefit_payment_order_num)/last_cycle_benefit_payment_order_num end as decimal(15,2)) as `benefitOrdersUp`," +

          //储值会员
          "benefit_payment_user_num as `benefitUsers`, " +
          "cast(case when last_cycle_benefit_payment_user_num =0 and benefit_payment_user_num>0 then 1 when last_cycle_benefit_payment_user_num=0 and benefit_payment_user_num=0 then 0 else (benefit_payment_user_num-last_cycle_benefit_payment_user_num)/last_cycle_benefit_payment_user_num end as decimal(15,2)) as `benefitUsersUp`," +

          //储值客单价
          "avg_payment_amount_per_user as `orderPrice`, " +
          "cast(case when last_cycle_avg_payment_amount_per_user =0 and avg_payment_amount_per_user>0 then 1 when last_cycle_avg_payment_amount_per_user=0 and avg_payment_amount_per_user=0 then 0 else (avg_payment_amount_per_user-last_cycle_avg_payment_amount_per_user)/last_cycle_avg_payment_amount_per_user end as decimal(15,2)) as `orderPriceUp`," +

          //储值设备数
          "benefit_payment_equip_num as `benefitEquipments`, " +
          "cast(case when last_cycle_benefit_payment_equip_num =0 and benefit_payment_equip_num>0 then 1 when last_cycle_benefit_payment_equip_num=0 and benefit_payment_equip_num=0 then 0 else (benefit_payment_equip_num-last_cycle_benefit_payment_equip_num)/last_cycle_benefit_payment_equip_num end as decimal(15,2)) as `benefitEquipmentsUp`," +

          //储值场地数
          "benefit_payment_group_num as `benefitGroups`, " +
          "cast(case when last_cycle_benefit_payment_group_num =0 and benefit_payment_group_num>0 then 1 when last_cycle_benefit_payment_group_num=0 and benefit_payment_group_num=0 then 0 else (benefit_payment_group_num-last_cycle_benefit_payment_group_num)/last_cycle_benefit_payment_group_num end as decimal(15,2)) as `benefitGroupsUp`," +

          //消耗储值
          "comsume_benefit_amount as `consumerBenefit`, " +
          "cast(case when last_cycle_comsume_benefit_amount =0 and comsume_benefit_amount>0 then 1 when last_cycle_comsume_benefit_amount=0 and comsume_benefit_amount=0 then 0 else (comsume_benefit_amount-last_cycle_comsume_benefit_amount)/last_cycle_comsume_benefit_amount end as decimal(15,2)) as `consumerBenefitUp`," +

          //消耗储值订单数
          "benefit_consume_order_num as `consumerBenefitOrders`, " +
          "cast(case when last_cycle_benefit_consume_order_num =0 and benefit_consume_order_num>0 then 1 when last_cycle_benefit_consume_order_num=0 and benefit_consume_order_num=0 then 0 else (benefit_consume_order_num-last_cycle_benefit_consume_order_num)/last_cycle_benefit_consume_order_num end as decimal(15,2)) as `consumerBenefitOrdersUp`," +


          //消耗储值会员
          "benefit_consume_user_num as `consumerBenefitUsers`, " +
          "cast(case when last_cycle_benefit_consume_user_num =0 and benefit_consume_user_num>0 then 1 when last_cycle_benefit_consume_user_num=0 and benefit_consume_user_num=0 then 0 else (benefit_consume_user_num-last_cycle_benefit_consume_user_num)/last_cycle_benefit_consume_user_num end as decimal(15,2)) as `consumerBenefitUsersUp`," +


          //消耗储值客单价
          "avg_consume_amount_per_user as `consumerBenefitOrderPrice`, " +
          "cast(case when last_cycle_avg_consume_amount_per_user =0 and avg_consume_amount_per_user>0 then 1 when last_cycle_avg_consume_amount_per_user=0 and avg_consume_amount_per_user=0 then 0 else (avg_consume_amount_per_user-last_cycle_avg_consume_amount_per_user)/last_cycle_avg_consume_amount_per_user end as decimal(15,2)) as `consumerBenefitOrderPriceUp`," +

          //消耗储值设备数
          "benefit_consume_equip_num as `consumerBenefitEquipments`, " +
          "cast(case when last_cycle_benefit_consume_equip_num =0 and benefit_consume_equip_num>0 then 1 when last_cycle_benefit_consume_equip_num=0 and benefit_consume_equip_num=0 then 0 else (benefit_consume_equip_num-last_cycle_benefit_consume_equip_num)/last_cycle_benefit_consume_equip_num end as decimal(15,2)) as `consumerBenefitEquipmentsUp`," +

          //消耗储值场地数
          "benefit_consume_group_num as `consumerBenefitGroups`, " +
          "cast(case when last_cycle_benefit_consume_group_num =0 and benefit_consume_group_num>0 then 1 when last_cycle_benefit_consume_group_num=0 and benefit_consume_group_num=0 then 0 else (benefit_consume_group_num-last_cycle_benefit_consume_group_num)/last_cycle_benefit_consume_group_num end as decimal(15,2)) as `consumerBenefitGroupsUp`," +


          //消耗储值启动设备数
          "consume_benefit_tostart_equip_num as `consumerBenefitStartEquipments`, " +
          "cast(case when last_cycle_consume_benefit_tostart_equip_num =0 and consume_benefit_tostart_equip_num>0 then 1 when last_cycle_consume_benefit_tostart_equip_num=0 and consume_benefit_tostart_equip_num=0 then 0 else (consume_benefit_tostart_equip_num-last_cycle_consume_benefit_tostart_equip_num)/last_cycle_consume_benefit_tostart_equip_num end as decimal(15,2)) as `consumerBenefitStartEquipmentsUp`," +

          //消耗储值启动设备日均数
          "per_day_consumetostart_equip_num as `perDayConsumerBenefitStartEquipments`, " +
          "cast(case when last_cycle_per_day_consumetostart_equip_num =0 and per_day_consumetostart_equip_num>0 then 1 when last_cycle_per_day_consumetostart_equip_num=0 and per_day_consumetostart_equip_num=0 then 0 else (per_day_consumetostart_equip_num-last_cycle_per_day_consumetostart_equip_num)/last_cycle_per_day_consumetostart_equip_num end as decimal(15,2)) as `perDayConsumerBenefitStartEquipmentsUp`";
			
	public static final String YEAR_MEMBER_BENEFIT_COLUMN_ARRAY = 
			"merchant_id as `merchantId`, benefit_kind as `benefitType`, " +
            //新增储值额度
            "increase_benefit_amount as `addAmount`, " +
            "cast(case when last_cycle_increase_benefit_amount =0 and increase_benefit_amount>0 then 1 when last_cycle_increase_benefit_amount=0 and increase_benefit_amount=0 then 0 else (increase_benefit_amount-last_cycle_increase_benefit_amount)/last_cycle_increase_benefit_amount end as decimal(15,2)) as `addAmountUp`," +

            //储值支付金额
            "benefit_payment_amount  as `benefitPayAmount`, " +
            "cast(case when last_cycle_benefit_payment_amount  =0 and benefit_payment_amount>0 then 1 when last_cycle_benefit_payment_amount =0 and benefit_payment_amount=0 then 0 else (benefit_payment_amount-last_cycle_benefit_payment_amount )/last_cycle_benefit_payment_amount  end as decimal(15,2)) as `benefitPayAmountUp`," +

            //储值订单数
            "benefit_payment_order_num as `benefitOrders`, " +
            "cast(case when last_cycle_benefit_payment_order_num =0 and benefit_payment_order_num>0 then 1 when benefit_payment_order_num=0 and last_cycle_benefit_payment_order_num=0 then 0 else (benefit_payment_order_num-last_cycle_benefit_payment_order_num)/last_cycle_benefit_payment_order_num end as decimal(15,2)) as `benefitOrdersUp`," +

            //储值会员
            "benefit_payment_user_num as `benefitUsers`, " +
            "cast(case when last_cycle_benefit_payment_user_num =0 and benefit_payment_user_num>0 then 1 when last_cycle_benefit_payment_user_num=0 and benefit_payment_user_num=0 then 0 else (benefit_payment_user_num-last_cycle_benefit_payment_user_num)/last_cycle_benefit_payment_user_num end as decimal(15,2)) as `benefitUsersUp`," +

            //储值客单价
            "avg_payment_amount_per_user as `orderPrice`, " +
            "cast(case when last_cycle_avg_payment_amount_per_user =0 and avg_payment_amount_per_user>0 then 1 when last_cycle_avg_payment_amount_per_user=0 and avg_payment_amount_per_user=0 then 0 else (avg_payment_amount_per_user-last_cycle_avg_payment_amount_per_user)/last_cycle_avg_payment_amount_per_user end as decimal(15,2)) as `orderPriceUp`," +

            //储值设备数
            "benefit_payment_equip_num as `benefitEquipments`, " +
            "cast(case when last_cycle_benefit_payment_equip_num =0 and benefit_payment_equip_num>0 then 1 when last_cycle_benefit_payment_equip_num=0 and benefit_payment_equip_num=0 then 0 else (benefit_payment_equip_num-last_cycle_benefit_payment_equip_num)/last_cycle_benefit_payment_equip_num end as decimal(15,2)) as `benefitEquipmentsUp`," +

            //储值场地数
            "benefit_payment_group_num as `benefitGroups`, " +
            "cast(case when last_cycle_benefit_payment_group_num =0 and benefit_payment_group_num>0 then 1 when last_cycle_benefit_payment_group_num=0 and benefit_payment_group_num=0 then 0 else (benefit_payment_group_num-last_cycle_benefit_payment_group_num)/last_cycle_benefit_payment_group_num end as decimal(15,2)) as `benefitGroupsUp`," +

            //消耗储值
            "comsume_benefit_amount as `consumerBenefit`, " +
            "cast(case when last_cycle_comsume_benefit_amount =0 and comsume_benefit_amount>0 then 1 when last_cycle_comsume_benefit_amount=0 and comsume_benefit_amount=0 then 0 else (comsume_benefit_amount-last_cycle_comsume_benefit_amount)/last_cycle_comsume_benefit_amount end as decimal(15,2)) as `consumerBenefitUp`," +

            //消耗储值订单数
            "benefit_consume_order_num as `consumerBenefitOrders`, " +
            "cast(case when last_cycle_benefit_consume_order_num =0 and benefit_consume_order_num>0 then 1 when last_cycle_benefit_consume_order_num=0 and benefit_consume_order_num=0 then 0 else (benefit_consume_order_num-last_cycle_benefit_consume_order_num)/last_cycle_benefit_consume_order_num end as decimal(15,2)) as `consumerBenefitOrdersUp`," +


            //消耗储值会员
            "benefit_consume_user_num as `consumerBenefitUsers`, " +
            "cast(case when last_cycle_benefit_consume_user_num =0 and benefit_consume_user_num>0 then 1 when last_cycle_benefit_consume_user_num=0 and benefit_consume_user_num=0 then 0 else (benefit_consume_user_num-last_cycle_benefit_consume_user_num)/last_cycle_benefit_consume_user_num end as decimal(15,2)) as `consumerBenefitUsersUp`," +


            //消耗储值客单价
            "avg_consume_amount_per_user as `consumerBenefitOrderPrice`, " +
            "cast(case when last_cycle_avg_consume_amount_per_user =0 and avg_consume_amount_per_user>0 then 1 when last_cycle_avg_consume_amount_per_user=0 and avg_consume_amount_per_user=0 then 0 else (avg_consume_amount_per_user-last_cycle_avg_consume_amount_per_user)/last_cycle_avg_consume_amount_per_user end as decimal(15,2)) as `consumerBenefitOrderPriceUp`," +

            //消耗储值设备数
            "benefit_consume_equip_num as `consumerBenefitEquipments`, " +
            "cast(case when last_cycle_benefit_consume_equip_num =0 and benefit_consume_equip_num>0 then 1 when last_cycle_benefit_consume_equip_num=0 and benefit_consume_equip_num=0 then 0 else (benefit_consume_equip_num-last_cycle_benefit_consume_equip_num)/last_cycle_benefit_consume_equip_num end as decimal(15,2)) as `consumerBenefitEquipmentsUp`," +

            //消耗储值场地数
            "benefit_consume_group_num as `consumerBenefitGroups`, " +
            "cast(case when last_cycle_benefit_consume_group_num =0 and benefit_consume_group_num>0 then 1 when last_cycle_benefit_consume_group_num=0 and benefit_consume_group_num=0 then 0 else (benefit_consume_group_num-last_cycle_benefit_consume_group_num)/last_cycle_benefit_consume_group_num end as decimal(15,2)) as `consumerBenefitGroupsUp`," +


            //消耗储值启动设备数
            "consume_benefit_tostart_equip_num as `consumerBenefitStartEquipments`, " +
            "cast(case when last_cycle_consume_benefit_tostart_equip_num =0 and consume_benefit_tostart_equip_num>0 then 1 when last_cycle_consume_benefit_tostart_equip_num=0 and consume_benefit_tostart_equip_num=0 then 0 else (consume_benefit_tostart_equip_num-last_cycle_consume_benefit_tostart_equip_num)/last_cycle_consume_benefit_tostart_equip_num end as decimal(15,2)) as `consumerBenefitStartEquipmentsUp`," +

            //消耗储值启动设备日均数
            "per_day_consumetostart_equip_num as `perDayConsumerBenefitStartEquipments`, " +
            "cast(case when last_cycle_per_day_consumetostart_equip_num =0 and per_day_consumetostart_equip_num>0 then 1 when last_cycle_per_day_consumetostart_equip_num=0 and per_day_consumetostart_equip_num=0 then 0 else (per_day_consumetostart_equip_num-last_cycle_per_day_consumetostart_equip_num)/last_cycle_per_day_consumetostart_equip_num end as decimal(15,2)) as `perDayConsumerBenefitStartEquipmentsUp`" ;

}
