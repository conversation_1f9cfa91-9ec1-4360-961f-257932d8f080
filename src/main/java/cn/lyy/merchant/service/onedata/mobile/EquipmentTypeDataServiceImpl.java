package cn.lyy.merchant.service.onedata.mobile;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import cn.lyy.merchant.controller.onedata.dto.DayParamDto;
import cn.lyy.merchant.controller.onedata.dto.MonthParamDto;
import cn.lyy.merchant.controller.onedata.dto.WeekParamDto;
import cn.lyy.merchant.controller.onedata.dto.YearParamDto;
import cn.lyy.merchant.service.onedata.FeignUtil;
import cn.lyy.merchant.service.onedata.ParamDto;
import cn.lyy.merchant.service.onedata.columns.ColumnArrayMap;
import cn.lyy.merchant.service.onedata.columns.ColumnArrayMapKey;

@Service
public class EquipmentTypeDataServiceImpl implements EquipmentTypeDataService{

	@Autowired
    private FeignUtil feignUtil ;
	
    @Value("${oneData.dayOrderDayAnalyseCode}")
    private String dayOrderDayAnalyseCode;
    @Value("${oneData.weekOrderDayAnalyseCode}")
    private String weekOrderDayAnalyseCode;
    @Value("${oneData.monthOrderDayAnalyseCode}")
    private String monthOrderDayAnalyseCode;
    @Value("${oneData.yearOrderDayAnalyseCode}")
    private String yearOrderDayAnalyseCode;
    
    @Value("${oneData.dayGroupEquipmentAnalyseCode}")
    private String dayGroupEquipmentAnalyseCode;
    @Value("${oneData.weekGroupEquipmentAnalyseCode}")
    private String weekGroupEquipmentAnalyseCode;
    @Value("${oneData.monthGroupEquipmentAnalyseCode}")
    private String monthGroupEquipmentAnalyseCode;
    @Value("${oneData.yearGroupEquipmentAnalyseCode}")
    private String yearGroupEquipmentAnalyseCode;
    
    @Value("${oneData.dayMemberDataCode}")
    private String dayMemberDataCode;
    @Value("${oneData.weekMemberDataCode}")
    private String weekMemberDataCode;
    @Value("${oneData.monthMemberDataCode}")
    private String monthMemberDataCode;
    @Value("${oneData.yearMemberDataCode}")
    private String yearMemberDataCode;
    
    /**
     * 日报获取设备类型列表数据
     * @param dayParamDto
     * @return
     */
    @Override
    public JSONObject getDayEquipmentTypeData(DayParamDto dayParamDto) {

        ParamDto params = new ParamDto();
        params.setCode(dayOrderDayAnalyseCode);
        //要把排序给排除
        //String orderBy = dayParamDto.getOrderBy();
        dayParamDto.setOrderBy(null);
        //维度加上设备类型
        String comboType = dayParamDto.getComboType();
        if(dayParamDto.getEquipmentTypeId() == null){
            dayParamDto.setEquipmentTypeId(9999999);
            comboType = dayParamDto.getComboType();
            dayParamDto.setEquipmentTypeId(null);
        }

        params.getQueryParams().setCombo_type(comboType);
//        params.setColumnArray(QuerySql.DayEquipmentTypeListSql);
        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.DayEquipmentTypeListSql) ;
        params.setColumnArray(columnArray);
        JSONObject orderResult = feignUtil.getData(params, dayParamDto);

        params.setCode(dayMemberDataCode);
//        params.setColumnArray(QuerySql.DayMemberListSql);
        columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.DayMemberEquipmentListSql) ;
        params.setColumnArray(columnArray);
        JSONObject memberResult = feignUtil.getData(params, dayParamDto);

        params.setCode(dayGroupEquipmentAnalyseCode);
        //params.setColumnArray(QuerySql.DayGroupListSql);
        columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.DayGroupListSql) ;
        params.setColumnArray(columnArray) ;
        JSONObject groupResult = feignUtil.getData(params, dayParamDto);

        if(orderResult == null && memberResult == null && groupResult == null){
            return null;
        }

        Map<String,JSONObject> data = new HashMap<>();

        List<JSONObject> orders = new ArrayList<>();

        if(groupResult != null && groupResult.getInteger("code") == 200 && groupResult.getJSONObject("info") != null && groupResult.getJSONObject("info").getJSONArray("list") != null){
            JSONArray groupList = groupResult.getJSONObject("info").getJSONArray("list");
            if(groupList != null && groupList.size()>0){
                for(int i=0; i<groupList.size(); i++){
                    JSONObject obj = groupList.getJSONObject(i);
                    obj.put("memberCount","0");
                    obj.put("memberCountUp","0");
                    obj.put("payAmount","0");
                    obj.put("payAmountUp","0");
                    obj.put("payCount","0");
                    obj.put("payCountUp","0");
                    obj.put("perOrderAmount","0");
                    obj.put("perOrderAmountUp","0");
                    data.put(feignUtil.idProcess(obj.getString("provinceId"))+feignUtil.idProcess(obj.getString("cityId"))+feignUtil.idProcess(obj.getString("districtId"))
                                    +feignUtil.idProcess(obj.getString("groupId"))+feignUtil.idProcess(obj.getString("equipmentTypeId"))
                            ,obj);
                }
            }
        }

        if(memberResult != null && memberResult.getInteger("code") == 200 && memberResult.getJSONObject("info") != null && memberResult.getJSONObject("info").getJSONArray("list") != null){
            JSONArray memberList = memberResult.getJSONObject("info").getJSONArray("list");
            if(memberList != null && memberList.size()>0){
                for(int i=0; i<memberList.size(); i++){
                    JSONObject obj = memberList.getJSONObject(i);
                    JSONObject mapObj = data.get(feignUtil.idProcess(obj.getString("provinceId"))+feignUtil.idProcess(obj.getString("cityId"))+feignUtil.idProcess(obj.getString("districtId"))
                            +feignUtil.idProcess(obj.getString("groupId"))+feignUtil.idProcess(obj.getString("equipmentTypeId")));
                    if(mapObj == null){
                        obj.put("equipmentCounts","0");
                        obj.put("equipmentCountsUp","0");
                        data.put(feignUtil.idProcess(obj.getString("provinceId"))+feignUtil.idProcess(obj.getString("cityId"))+feignUtil.idProcess(obj.getString("districtId"))
                                        +feignUtil.idProcess(obj.getString("groupId"))+feignUtil.idProcess(obj.getString("equipmentTypeId"))
                                ,obj);
                    }else{
                        mapObj.put("memberCount",obj.getIntValue("memberCount"));
                        mapObj.put("memberCountUp",obj.getDoubleValue("memberCountUp"));
                    }
                }
            }
        }

        if(orderResult != null && orderResult.getInteger("code") == 200 && orderResult.getJSONObject("info") != null && orderResult.getJSONObject("info").getJSONArray("list") != null){
            JSONArray orderList = orderResult.getJSONObject("info").getJSONArray("list");
            if(orderList != null && orderList.size()>0){
                for(int i=0; i<orderList.size(); i++){
                    JSONObject obj = orderList.getJSONObject(i);
                    JSONObject mapObj = data.get(feignUtil.idProcess(obj.getString("provinceId"))+feignUtil.idProcess(obj.getString("cityId"))+feignUtil.idProcess(obj.getString("districtId"))
                            +feignUtil.idProcess(obj.getString("groupId"))+feignUtil.idProcess(obj.getString("equipmentTypeId")));
                    if(mapObj == null){
                        obj.put("memberCount",0);
                        obj.put("memberCountUp",0);
                        obj.put("equipmentCounts",0);
                        obj.put("equipmentCountsUp",0);
                        data.put(feignUtil.idProcess(obj.getString("provinceId"))+feignUtil.idProcess(obj.getString("cityId"))+feignUtil.idProcess(obj.getString("districtId"))
                                +feignUtil.idProcess(obj.getString("groupId"))+feignUtil.idProcess(obj.getString("equipmentTypeId")),obj);
                    }else{
                        obj.put("memberCount",mapObj.getIntValue("memberCount"));
                        obj.put("memberCountUp",mapObj.getDoubleValue("memberCountUp"));
                        obj.put("equipmentCounts",mapObj.getIntValue("equipmentCounts"));
                        obj.put("equipmentCountsUp",mapObj.getDoubleValue("equipmentCountsUp"));
                        data.put(feignUtil.idProcess(obj.getString("provinceId"))+feignUtil.idProcess(obj.getString("cityId"))+feignUtil.idProcess(obj.getString("districtId"))
                                +feignUtil.idProcess(obj.getString("groupId"))+feignUtil.idProcess(obj.getString("equipmentTypeId")),obj);
                    }
                }
            }
        }

        if(data != null){
            Set<String> keys = data.keySet();
            //倒序
            Map<Double,List<JSONObject>> treeMap = new TreeMap<>(Comparator.reverseOrder());
            //正序
            if("asc".equals(dayParamDto.getOrder())){
                treeMap = new TreeMap<>();
            }
            for(String key : keys){
                //如何排序,目前只做了按金钱排序
                JSONObject obj = data.get(key);
                if(obj.getString("equipmentTypeName") == null || "未知".equals(obj.getString("equipmentTypeName"))){
                    continue;
                }
                List<JSONObject> list = treeMap.get(obj.getDoubleValue("payAmount"));
                if(list == null){
                    list = new ArrayList<JSONObject>();
                }
                list.add(obj);
                treeMap.put(obj.getDoubleValue("payAmount"),list);
            }

            //开始按照顺序重新拼装list
            Set<Double> tmKeys = treeMap.keySet();
            for(Double key : tmKeys){
                orders.addAll(treeMap.get(key));
            }

        }

        orderResult.getJSONObject("info").remove("list");
        orderResult.getJSONObject("info").put("list",orders);

        orderResult.getJSONObject("info").put("totalCount",orders.size());
        return orderResult;
    }

    /**
     * 周报获取设备类型列表数据
     * @param dayParamDto
     * @return
     */
    @Override
    public JSONObject getWeekEquipmentTypeData(WeekParamDto weekParamDto) {
        ParamDto params = new ParamDto();
        params.setCode(weekOrderDayAnalyseCode);
        //要把排序给排除
        //String orderBy = weekParamDto.getOrderBy();
        weekParamDto.setOrderBy(null);
        //维度加上设备类型
        String comboType = weekParamDto.getComboType();
        if(weekParamDto.getEquipmentTypeId() == null){
            weekParamDto.setEquipmentTypeId(9999999);
            comboType = weekParamDto.getComboType();
            weekParamDto.setEquipmentTypeId(null);
        }

        params.getQueryParams().setCombo_type(comboType);
//        params.setColumnArray(QuerySql.WeekEquipmentTypeListSql);
        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.WeekEquipmentTypeListSql) ;
        params.setColumnArray(columnArray);
        JSONObject orderResult = feignUtil.getData(params, weekParamDto);

        params.setCode(weekMemberDataCode);
        //params.setColumnArray(QuerySql.WeekMemberListSql);
        columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.WeekMemberEquipmentListSql) ;
        params.setColumnArray(columnArray);
        JSONObject memberResult = feignUtil.getData(params, weekParamDto);

        params.setCode(weekGroupEquipmentAnalyseCode);
        //params.setColumnArray(QuerySql.WeekGroupListSql);
        columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.WeekGroupListSql) ;
        params.setColumnArray(columnArray);
        JSONObject groupResult = feignUtil.getData(params, weekParamDto);

        if(orderResult == null && memberResult == null && groupResult == null){
            return null;
        }

        Map<String,JSONObject> data = new HashMap<>();

        List<JSONObject> orders = new ArrayList<>();

        if(groupResult != null && groupResult.getInteger("code") == 200 && groupResult.getJSONObject("info") != null && groupResult.getJSONObject("info").getJSONArray("list") != null){
            JSONArray groupList = groupResult.getJSONObject("info").getJSONArray("list");
            if(groupList != null && groupList.size()>0){
                for(int i=0; i<groupList.size(); i++){
                    JSONObject obj = groupList.getJSONObject(i);
                    obj.put("memberCount","0");
                    obj.put("memberCountUp","0");
                    obj.put("payAmount","0");
                    obj.put("payAmountUp","0");
                    obj.put("payCount","0");
                    obj.put("payCountUp","0");
                    obj.put("perOrderAmount","0");
                    obj.put("perOrderAmountUp","0");
                    data.put(feignUtil.idProcess(obj.getString("provinceId"))+feignUtil.idProcess(obj.getString("cityId"))+feignUtil.idProcess(obj.getString("districtId"))
                                    +feignUtil.idProcess(obj.getString("groupId"))+feignUtil.idProcess(obj.getString("equipmentTypeId"))
                            ,obj);
                }
            }
        }

        if(memberResult != null && memberResult.getInteger("code") == 200 && memberResult.getJSONObject("info") != null && memberResult.getJSONObject("info").getJSONArray("list") != null){
            JSONArray memberList = memberResult.getJSONObject("info").getJSONArray("list");
            if(memberList != null && memberList.size()>0){
                for(int i=0; i<memberList.size(); i++){
                    JSONObject obj = memberList.getJSONObject(i);
                    JSONObject mapObj = data.get(feignUtil.idProcess(obj.getString("provinceId"))+feignUtil.idProcess(obj.getString("cityId"))+feignUtil.idProcess(obj.getString("districtId"))
                            +feignUtil.idProcess(obj.getString("groupId"))+feignUtil.idProcess(obj.getString("equipmentTypeId")));
                    if(mapObj == null){
                        obj.put("equipmentCounts","0");
                        obj.put("equipmentCountsUp","0");
                        data.put(feignUtil.idProcess(obj.getString("provinceId"))+feignUtil.idProcess(obj.getString("cityId"))+feignUtil.idProcess(obj.getString("districtId"))
                                        +feignUtil.idProcess(obj.getString("groupId"))+feignUtil.idProcess(obj.getString("equipmentTypeId"))
                                ,obj);
                    }else{
                        mapObj.put("memberCount",obj.getIntValue("memberCount"));
                        mapObj.put("memberCountUp",obj.getDoubleValue("memberCountUp"));
                    }
                }
            }
        }

        if(orderResult != null && orderResult.getInteger("code") == 200 && orderResult.getJSONObject("info") != null && orderResult.getJSONObject("info").getJSONArray("list") != null){
            JSONArray orderList = orderResult.getJSONObject("info").getJSONArray("list");
            if(orderList != null && orderList.size()>0){
                for(int i=0; i<orderList.size(); i++){
                    JSONObject obj = orderList.getJSONObject(i);
                    JSONObject mapObj = data.get(feignUtil.idProcess(obj.getString("provinceId"))+feignUtil.idProcess(obj.getString("cityId"))+feignUtil.idProcess(obj.getString("districtId"))
                            +feignUtil.idProcess(obj.getString("groupId"))+feignUtil.idProcess(obj.getString("equipmentTypeId")));
                    if(mapObj == null){
                        obj.put("memberCount",0);
                        obj.put("memberCountUp",0);
                        obj.put("equipmentCounts",0);
                        obj.put("equipmentCountsUp",0);
                        data.put(feignUtil.idProcess(obj.getString("provinceId"))+feignUtil.idProcess(obj.getString("cityId"))+feignUtil.idProcess(obj.getString("districtId"))
                                +feignUtil.idProcess(obj.getString("groupId"))+feignUtil.idProcess(obj.getString("equipmentTypeId")),obj);
                    }else{
                        obj.put("memberCount",mapObj.getIntValue("memberCount"));
                        obj.put("memberCountUp",mapObj.getDoubleValue("memberCountUp"));
                        obj.put("equipmentCounts",mapObj.getIntValue("equipmentCounts"));
                        obj.put("equipmentCountsUp",mapObj.getDoubleValue("equipmentCountsUp"));
                        data.put(feignUtil.idProcess(obj.getString("provinceId"))+feignUtil.idProcess(obj.getString("cityId"))+feignUtil.idProcess(obj.getString("districtId"))
                                +feignUtil.idProcess(obj.getString("groupId"))+feignUtil.idProcess(obj.getString("equipmentTypeId")),obj);
                    }
                }
            }
        }

        if(data != null){
            Set<String> keys = data.keySet();
            //倒序
            Map<Double,List<JSONObject>> treeMap = new TreeMap<>(Comparator.reverseOrder());
            //正序
            if("asc".equals(weekParamDto.getOrder())){
                treeMap = new TreeMap<>();
            }
            for(String key : keys){
                //如何排序,目前只做了按金钱排序
                JSONObject obj = data.get(key);
                if(obj.getString("equipmentTypeName") == null || "未知".equals(obj.getString("equipmentTypeName"))){
                    continue;
                }
                List<JSONObject> list = treeMap.get(obj.getDoubleValue("payAmount"));
                if(list == null){
                    list = new ArrayList<JSONObject>();
                }
                list.add(obj);
                treeMap.put(obj.getDoubleValue("payAmount"),list);
            }

            //开始按照顺序重新拼装list
            Set<Double> tmKeys = treeMap.keySet();
            for(Double key : tmKeys){
                orders.addAll(treeMap.get(key));
            }

        }

        orderResult.getJSONObject("info").remove("list");
        orderResult.getJSONObject("info").put("list",orders);

        orderResult.getJSONObject("info").put("totalCount",orders.size());
        return orderResult;
    }

    /**
     * 月报获取设备类型列表数据
     * @param dayParamDto
     * @return
     */
    @Override
    public JSONObject getMonthEquipmentTypeData(MonthParamDto monthParamDto) {
        ParamDto params = new ParamDto();
        params.setCode(monthOrderDayAnalyseCode);
        //要把排序给排除
        //String orderBy = monthParamDto.getOrderBy();
        monthParamDto.setOrderBy(null);
        //维度加上设备类型
        String comboType = monthParamDto.getComboType();
        if(monthParamDto.getEquipmentTypeId() == null){
            monthParamDto.setEquipmentTypeId(9999999);
            comboType = monthParamDto.getComboType();
            monthParamDto.setEquipmentTypeId(null);
        }

        params.getQueryParams().setCombo_type(comboType);
        //params.setColumnArray(QuerySql.MonthEquipmentTypeListSql);
        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.MonthEquipmentTypeListSql) ;
        params.setColumnArray(columnArray);
        JSONObject orderResult = feignUtil.getData(params, monthParamDto);

        params.setCode(monthMemberDataCode);
//        params.setColumnArray(QuerySql.MonthMemberListSql);
        columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.MonthMemberEquipmentListSql) ;
        params.setColumnArray(columnArray);
        JSONObject memberResult = feignUtil.getData(params, monthParamDto);

        params.setCode(monthGroupEquipmentAnalyseCode);
        //params.setColumnArray(QuerySql.WeekGroupListSql);
        columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.WeekGroupListSql) ;
        params.setColumnArray(columnArray);
        JSONObject groupResult = feignUtil.getData(params, monthParamDto);

        if(orderResult == null && memberResult == null && groupResult == null){
            return null;
        }

        Map<String,JSONObject> data = new HashMap<>();

        List<JSONObject> orders = new ArrayList<>();

        if(groupResult != null && groupResult.getInteger("code") == 200 && groupResult.getJSONObject("info") != null && groupResult.getJSONObject("info").getJSONArray("list") != null){
            JSONArray groupList = groupResult.getJSONObject("info").getJSONArray("list");
            if(groupList != null && groupList.size()>0){
                for(int i=0; i<groupList.size(); i++){
                    JSONObject obj = groupList.getJSONObject(i);
                    obj.put("memberCount","0");
                    obj.put("memberCountUp","0");
                    obj.put("payAmount","0");
                    obj.put("payAmountUp","0");
                    obj.put("payCount","0");
                    obj.put("payCountUp","0");
                    obj.put("perOrderAmount","0");
                    obj.put("perOrderAmountUp","0");
                    data.put(feignUtil.idProcess(obj.getString("provinceId"))+feignUtil.idProcess(obj.getString("cityId"))+feignUtil.idProcess(obj.getString("districtId"))
                                    +feignUtil.idProcess(obj.getString("groupId"))+feignUtil.idProcess(obj.getString("equipmentTypeId"))
                            ,obj);
                }
            }
        }

        if(memberResult != null && memberResult.getInteger("code") == 200 && memberResult.getJSONObject("info") != null && memberResult.getJSONObject("info").getJSONArray("list") != null){
            JSONArray memberList = memberResult.getJSONObject("info").getJSONArray("list");
            if(memberList != null && memberList.size()>0){
                for(int i=0; i<memberList.size(); i++){
                    JSONObject obj = memberList.getJSONObject(i);
                    JSONObject mapObj = data.get(feignUtil.idProcess(obj.getString("provinceId"))+feignUtil.idProcess(obj.getString("cityId"))+feignUtil.idProcess(obj.getString("districtId"))
                            +feignUtil.idProcess(obj.getString("groupId"))+feignUtil.idProcess(obj.getString("equipmentTypeId")));
                    if(mapObj == null){
                        obj.put("equipmentCounts","0");
                        obj.put("equipmentCountsUp","0");
                        data.put(feignUtil.idProcess(obj.getString("provinceId"))+feignUtil.idProcess(obj.getString("cityId"))+feignUtil.idProcess(obj.getString("districtId"))
                                        +feignUtil.idProcess(obj.getString("groupId"))+feignUtil.idProcess(obj.getString("equipmentTypeId"))
                                ,obj);
                    }else{
                        mapObj.put("memberCount",obj.getIntValue("memberCount"));
                        mapObj.put("memberCountUp",obj.getDoubleValue("memberCountUp"));
                    }
                }
            }
        }

        if(orderResult != null && orderResult.getInteger("code") == 200 && orderResult.getJSONObject("info") != null && orderResult.getJSONObject("info").getJSONArray("list") != null){
            JSONArray orderList = orderResult.getJSONObject("info").getJSONArray("list");
            if(orderList != null && orderList.size()>0){
                for(int i=0; i<orderList.size(); i++){
                    JSONObject obj = orderList.getJSONObject(i);
                    JSONObject mapObj = data.get(feignUtil.idProcess(obj.getString("provinceId"))+feignUtil.idProcess(obj.getString("cityId"))+feignUtil.idProcess(obj.getString("districtId"))
                            +feignUtil.idProcess(obj.getString("groupId"))+feignUtil.idProcess(obj.getString("equipmentTypeId")));
                    if(mapObj == null){
                        obj.put("memberCount",0);
                        obj.put("memberCountUp",0);
                        obj.put("equipmentCounts",0);
                        obj.put("equipmentCountsUp",0);
                        data.put(feignUtil.idProcess(obj.getString("provinceId"))+feignUtil.idProcess(obj.getString("cityId"))+feignUtil.idProcess(obj.getString("districtId"))
                                +feignUtil.idProcess(obj.getString("groupId"))+feignUtil.idProcess(obj.getString("equipmentTypeId")),obj);
                    }else{
                        obj.put("memberCount",mapObj.getIntValue("memberCount"));
                        obj.put("memberCountUp",mapObj.getDoubleValue("memberCountUp"));
                        obj.put("equipmentCounts",mapObj.getIntValue("equipmentCounts"));
                        obj.put("equipmentCountsUp",mapObj.getDoubleValue("equipmentCountsUp"));
                        data.put(feignUtil.idProcess(obj.getString("provinceId"))+feignUtil.idProcess(obj.getString("cityId"))+feignUtil.idProcess(obj.getString("districtId"))
                                +feignUtil.idProcess(obj.getString("groupId"))+feignUtil.idProcess(obj.getString("equipmentTypeId")),obj);
                    }
                }
            }
        }

        if(data != null){
            Set<String> keys = data.keySet();
            //倒序
            Map<Double,List<JSONObject>> treeMap = new TreeMap<>(Comparator.reverseOrder());
            //正序
            if("asc".equals(monthParamDto.getOrder())){
                treeMap = new TreeMap<>();
            }
            for(String key : keys){
                //如何排序,目前只做了按金钱排序
                JSONObject obj = data.get(key);
                if(obj.getString("equipmentTypeName") == null || "未知".equals(obj.getString("equipmentTypeName"))){
                    continue;
                }
                List<JSONObject> list = treeMap.get(obj.getDoubleValue("payAmount"));
                if(list == null){
                    list = new ArrayList<JSONObject>();
                }
                list.add(obj);
                treeMap.put(obj.getDoubleValue("payAmount"),list);
            }

            //开始按照顺序重新拼装list
            Set<Double> tmKeys = treeMap.keySet();
            for(Double key : tmKeys){
                orders.addAll(treeMap.get(key));
            }

        }

        orderResult.getJSONObject("info").remove("list");
        orderResult.getJSONObject("info").put("list",orders);

        orderResult.getJSONObject("info").put("totalCount",orders.size());
        return orderResult;
    }

    /**
     * 年报获取设备类型列表数据
     * @param dayParamDto
     * @return
     */
    @Override
    public JSONObject getYearEquipmentTypeData(YearParamDto yearParamDto) {
        ParamDto params = new ParamDto();
        params.setCode(yearOrderDayAnalyseCode);
        //要把排序给排除
        //String orderBy = yearParamDto.getOrderBy();
        yearParamDto.setOrderBy(null);
        //维度加上设备类型
        String comboType = yearParamDto.getComboType();
        if(yearParamDto.getEquipmentTypeId() == null){
            yearParamDto.setEquipmentTypeId(9999999);
            comboType = yearParamDto.getComboType();
            yearParamDto.setEquipmentTypeId(null);
        }

        params.getQueryParams().setCombo_type(comboType);
        //params.setColumnArray(QuerySql.YearEquipmentTypeListSql);
        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.YearEquipmentTypeListSql) ;
        params.setColumnArray(columnArray);
        JSONObject orderResult = feignUtil.getData(params, yearParamDto);

        params.setCode(yearMemberDataCode);
//        params.setColumnArray(QuerySql.YearMemberListSql);
        columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.YearMemberEquipmentListSql) ;
        params.setColumnArray(columnArray);
        JSONObject memberResult = feignUtil.getData(params, yearParamDto);

        params.setCode(yearGroupEquipmentAnalyseCode);
        //params.setColumnArray(QuerySql.WeekGroupListSql);
        columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.WeekGroupListSql) ;
        params.setColumnArray(columnArray);
        JSONObject groupResult = feignUtil.getData(params, yearParamDto);

        if(orderResult == null && memberResult == null && groupResult == null){
            return null;
        }

        Map<String,JSONObject> data = new HashMap<>();

        List<JSONObject> orders = new ArrayList<>();

        if(groupResult != null && groupResult.getInteger("code") == 200 && groupResult.getJSONObject("info") != null && groupResult.getJSONObject("info").getJSONArray("list") != null){
            JSONArray groupList = groupResult.getJSONObject("info").getJSONArray("list");
            if(groupList != null && groupList.size()>0){
                for(int i=0; i<groupList.size(); i++){
                    JSONObject obj = groupList.getJSONObject(i);
                    obj.put("memberCount","0");
                    obj.put("memberCountUp","0");
                    obj.put("payAmount","0");
                    obj.put("payAmountUp","0");
                    obj.put("payCount","0");
                    obj.put("payCountUp","0");
                    obj.put("perOrderAmount","0");
                    obj.put("perOrderAmountUp","0");
                    data.put(feignUtil.idProcess(obj.getString("provinceId"))+feignUtil.idProcess(obj.getString("cityId"))+feignUtil.idProcess(obj.getString("districtId"))
                                    +feignUtil.idProcess(obj.getString("groupId"))+feignUtil.idProcess(obj.getString("equipmentTypeId"))
                            ,obj);
                }
            }
        }

        if(memberResult != null && memberResult.getInteger("code") == 200 && memberResult.getJSONObject("info") != null && memberResult.getJSONObject("info").getJSONArray("list") != null){
            JSONArray memberList = memberResult.getJSONObject("info").getJSONArray("list");
            if(memberList != null && memberList.size()>0){
                for(int i=0; i<memberList.size(); i++){
                    JSONObject obj = memberList.getJSONObject(i);
                    JSONObject mapObj = data.get(feignUtil.idProcess(obj.getString("provinceId"))+feignUtil.idProcess(obj.getString("cityId"))+feignUtil.idProcess(obj.getString("districtId"))
                            +feignUtil.idProcess(obj.getString("groupId"))+feignUtil.idProcess(obj.getString("equipmentTypeId")));
                    if(mapObj == null){
                        obj.put("equipmentCounts","0");
                        obj.put("equipmentCountsUp","0");
                        data.put(feignUtil.idProcess(obj.getString("provinceId"))+feignUtil.idProcess(obj.getString("cityId"))+feignUtil.idProcess(obj.getString("districtId"))
                                        +feignUtil.idProcess(obj.getString("groupId"))+feignUtil.idProcess(obj.getString("equipmentTypeId"))
                                ,obj);
                    }else{
                        mapObj.put("memberCount",obj.getIntValue("memberCount"));
                        mapObj.put("memberCountUp",obj.getDoubleValue("memberCountUp"));
                    }
                }
            }
        }

        if(orderResult != null && orderResult.getInteger("code") == 200 && orderResult.getJSONObject("info") != null && orderResult.getJSONObject("info").getJSONArray("list") != null){
            JSONArray orderList = orderResult.getJSONObject("info").getJSONArray("list");
            if(orderList != null && orderList.size()>0){
                for(int i=0; i<orderList.size(); i++){
                    JSONObject obj = orderList.getJSONObject(i);
                    JSONObject mapObj = data.get(feignUtil.idProcess(obj.getString("provinceId"))+feignUtil.idProcess(obj.getString("cityId"))+feignUtil.idProcess(obj.getString("districtId"))
                            +feignUtil.idProcess(obj.getString("groupId"))+feignUtil.idProcess(obj.getString("equipmentTypeId")));
                    if(mapObj == null){
                        obj.put("memberCount",0);
                        obj.put("memberCountUp",0);
                        obj.put("equipmentCounts",0);
                        obj.put("equipmentCountsUp",0);
                        data.put(feignUtil.idProcess(obj.getString("provinceId"))+feignUtil.idProcess(obj.getString("cityId"))+feignUtil.idProcess(obj.getString("districtId"))
                                +feignUtil.idProcess(obj.getString("groupId"))+feignUtil.idProcess(obj.getString("equipmentTypeId")),obj);
                    }else{
                        obj.put("memberCount",mapObj.getIntValue("memberCount"));
                        obj.put("memberCountUp",mapObj.getDoubleValue("memberCountUp"));
                        obj.put("equipmentCounts",mapObj.getIntValue("equipmentCounts"));
                        obj.put("equipmentCountsUp",mapObj.getDoubleValue("equipmentCountsUp"));
                        data.put(feignUtil.idProcess(obj.getString("provinceId"))+feignUtil.idProcess(obj.getString("cityId"))+feignUtil.idProcess(obj.getString("districtId"))
                                +feignUtil.idProcess(obj.getString("groupId"))+feignUtil.idProcess(obj.getString("equipmentTypeId")),obj);
                    }
                }
            }
        }

        if(data != null){
            Set<String> keys = data.keySet();
            //倒序
            Map<Double,List<JSONObject>> treeMap = new TreeMap<>(Comparator.reverseOrder());
            //正序
            if("asc".equals(yearParamDto.getOrder())){
                treeMap = new TreeMap<>();
            }
            for(String key : keys){
                //如何排序,目前只做了按金钱排序
                JSONObject obj = data.get(key);
                if(obj.getString("equipmentTypeName") == null || "未知".equals(obj.getString("equipmentTypeName"))){
                    continue;
                }
                List<JSONObject> list = treeMap.get(obj.getDoubleValue("payAmount"));
                if(list == null){
                    list = new ArrayList<JSONObject>();
                }
                list.add(obj);
                treeMap.put(obj.getDoubleValue("payAmount"),list);
            }

            //开始按照顺序重新拼装list
            Set<Double> tmKeys = treeMap.keySet();
            for(Double key : tmKeys){
                orders.addAll(treeMap.get(key));
            }

        }

        orderResult.getJSONObject("info").remove("list");
        orderResult.getJSONObject("info").put("list",orders);

        orderResult.getJSONObject("info").put("totalCount",orders.size());
        return orderResult;
    }

}
