package cn.lyy.merchant.service.onedata.mobile;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.alibaba.fastjson.JSONObject;
import cn.lyy.merchant.controller.onedata.dto.DayParamDto;
import cn.lyy.merchant.controller.onedata.dto.MonthParamDto;
import cn.lyy.merchant.controller.onedata.dto.WeekParamDto;
import cn.lyy.merchant.controller.onedata.dto.YearParamDto;
import cn.lyy.merchant.service.onedata.FeignUtil;
import cn.lyy.merchant.service.onedata.ParamDto;
import cn.lyy.merchant.service.onedata.columns.ColumnArrayMap;
import cn.lyy.merchant.service.onedata.columns.ColumnArrayMapKey;

@Service
public class EquipmentDataServiceImpl implements EquipmentDataService{

	@Autowired
	private FeignUtil feignUtil ;
	@Value("${oneData.dayEquipmentAnalyseCode}")
    private String dayEquipmentAnalyseCode;

	@Value("${oneData.weekEquipmentAnalyseCode}")
    private String weekEquipmentAnalyseCode;
    @Value("${oneData.monthEquipmentAnalyseCode}")
    private String monthEquipmentAnalyseCode;
    @Value("${oneData.yearEquipmentAnalyseCode}")
    private String yearEquipmentAnalyseCode;
    
	@Value("${oneData.dayGroupEquipmentAnalyseCode}")
    private String dayGroupEquipmentAnalyseCode;
    @Value("${oneData.weekGroupEquipmentAnalyseCode}")
    private String weekGroupEquipmentAnalyseCode;
    @Value("${oneData.monthGroupEquipmentAnalyseCode}")
    private String monthGroupEquipmentAnalyseCode;
    @Value("${oneData.yearGroupEquipmentAnalyseCode}")
    private String yearGroupEquipmentAnalyseCode;
	    
    /**
     * 日报设备维度数
     * @param monthParamDto
     * @return
     */
    @Override
    public JSONObject getDayEquipmentData(DayParamDto dayParamDto) {

        ParamDto params = new ParamDto();
        params.setCode(dayEquipmentAnalyseCode);
        //params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
        //params.setColumnArray(QuerySql.EquipmentSql);
        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.EquipmentSql) ;
        params.setColumnArray(columnArray);
        
        JSONObject orderResult = feignUtil.getData(params, dayParamDto);

        return orderResult;
    }

    /**
     * 周报设备维度数
     * @param monthParamDto
     * @return
     */
    @Override
    public JSONObject getWeekEquipmentData(WeekParamDto weekParamDto) {
        ParamDto params = new ParamDto();
        params.setCode(weekEquipmentAnalyseCode);
        //params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//        params.setColumnArray(QuerySql.EquipmentSql);
        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.EquipmentSql) ;
        params.setColumnArray(columnArray);
        JSONObject orderResult = feignUtil.getData(params, weekParamDto);

        return orderResult;
    }

    /**
     * 月报设备维度数
     * @param monthParamDto
     * @return
     */
    @Override
    public JSONObject getMonthEquipmentData(MonthParamDto monthParamDto) {
        ParamDto params = new ParamDto();
        params.setCode(monthEquipmentAnalyseCode);
        //params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//        params.setColumnArray(QuerySql.EquipmentSql);
        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.EquipmentSql) ;
        params.setColumnArray(columnArray);
        JSONObject orderResult = feignUtil.getData(params, monthParamDto);

        return orderResult;
    }

    /**
     * 年报设备维度数
     * @param yearParamDto
     * @return
     */
    @Override
    public JSONObject getYearEquipmentData(YearParamDto yearParamDto) {
        ParamDto params = new ParamDto();
        params.setCode(yearEquipmentAnalyseCode);
        //params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//        params.setColumnArray(QuerySql.EquipmentSql);
        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.EquipmentSql) ;
        params.setColumnArray(columnArray);
        JSONObject orderResult = feignUtil.getData(params, yearParamDto);

        return orderResult;
    }

    /**
     * 获取二级页面统计数据-日
     * @param paramDto
     * @return
     */
    public JSONObject getDaySumEquipmentData(DayParamDto dayParamDto){
        ParamDto params = new ParamDto();
        params.setCode(dayEquipmentAnalyseCode);
        //params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//        params.setColumnArray(QuerySql.EquipmentCountSql);
        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.EquipmentCountSql) ;
        params.setColumnArray(columnArray);
        JSONObject orderResult = feignUtil.getData(params, dayParamDto);

        return orderResult;
    }

    /**
     * 获取二级页面统计数据-周
     * @param paramDto
     * @return
     */
    public JSONObject getWeekSumEquipmentData(WeekParamDto weekParamDto){
        ParamDto params = new ParamDto();
        params.setCode(weekEquipmentAnalyseCode);
        //params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//        params.setColumnArray(QuerySql.EquipmentCountSql);
        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.EquipmentCountSql) ;
        params.setColumnArray(columnArray);
        JSONObject orderResult = feignUtil.getData(params, weekParamDto);

        return orderResult;
    }

    /**
     * 获取二级页面统计数据-月
     * @param paramDto
     * @return
     */
    public JSONObject getMonthSumEquipmentData(MonthParamDto monthParamDto){
        ParamDto params = new ParamDto();
        params.setCode(monthEquipmentAnalyseCode);
        //params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//        params.setColumnArray(QuerySql.EquipmentCountSql);
        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.EquipmentCountSql) ;
        params.setColumnArray(columnArray);
        JSONObject orderResult = feignUtil.getData(params, monthParamDto);

        return orderResult;
    }

    /**
     * 获取二级页面统计数据-年
     * @param paramDto
     * @return
     */
    public JSONObject getYearSumEquipmentData(YearParamDto yearParamDto){
        ParamDto params = new ParamDto();
        params.setCode(yearEquipmentAnalyseCode);
        //params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//        params.setColumnArray(QuerySql.EquipmentCountSql);
        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.EquipmentCountSql) ;
        params.setColumnArray(columnArray);
        JSONObject orderResult = feignUtil.getData(params, yearParamDto);

        return orderResult;
    }
    
}
