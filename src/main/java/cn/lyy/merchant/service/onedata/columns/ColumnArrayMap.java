package cn.lyy.merchant.service.onedata.columns;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class ColumnArrayMap {

	private static final Map<String,String> COLUMN_ARRAY_MAP = new ConcurrentHashMap<String, String>() ;
	
	static {
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.DROP_DOWN_PROVINCE_COLUMN_ARRAY,cn.lyy.merchant.service.onedata.columns.pg.EquipmentGroupColumnArray.DROP_DOWN_PROVINCE_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.DROP_DOWN_CITY_COLUMN_ARRAY,cn.lyy.merchant.service.onedata.columns.pg.EquipmentGroupColumnArray.DROP_DOWN_CITY_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.DROP_DOWN_DISTRICT_COLUMN_ARRAY,cn.lyy.merchant.service.onedata.columns.pg.EquipmentGroupColumnArray.DROP_DOWN_DISTRICT_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.DROP_DOWN_EQUIPMENT_GROUP_COLUMN_ARRAY,cn.lyy.merchant.service.onedata.columns.pg.EquipmentGroupColumnArray.DROP_DOWN_EQUIPMENT_GROUP_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.DROP_DOWN_EQUIPMENT_TYPE_COLUMN_ARRAY,cn.lyy.merchant.service.onedata.columns.pg.EquipmentGroupColumnArray.DROP_DOWN_EQUIPMENT_TYPE_COLUMN_ARRAY) ;
		
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.DAY_ORDER_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.pg.OrderColumnArray.DAY_ORDER_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.WEEK_ORDER_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.pg.OrderColumnArray.WEEK_ORDER_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.MONTH_ORDER_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.pg.OrderColumnArray.MONTH_ORDER_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.YEAR_ORDER_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.pg.OrderColumnArray.YEAR_ORDER_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.DAY_PER_ORDER_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.pg.OrderColumnArray.DAY_PER_ORDER_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.WEEK_PER_ORDER_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.pg.OrderColumnArray.WEEK_PER_ORDER_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.MONTH_PER_ORDER_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.pg.OrderColumnArray.MONTH_PER_ORDER_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.YEAR_PER_ORDER_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.pg.OrderColumnArray.YEAR_PER_ORDER_COLUMN_ARRAY) ;
		
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.DAY_PER_ORDER_PRICE_COUNT, cn.lyy.merchant.service.onedata.columns.pg.OrderColumnArray.DAY_PER_ORDER_PRICE_COUNT) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.WEEK_PER_ORDER_PRICE_COUNT, cn.lyy.merchant.service.onedata.columns.pg.OrderColumnArray.WEEK_PER_ORDER_PRICE_COUNT) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.MONTH_PER_ORDER_PRICE_COUNT, cn.lyy.merchant.service.onedata.columns.pg.OrderColumnArray.MONTH_PER_ORDER_PRICE_COUNT) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.YEAR_PER_ORDER_PRICE_COUNT, cn.lyy.merchant.service.onedata.columns.pg.OrderColumnArray.YEAR_PER_ORDER_PRICE_COUNT) ;
		
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.DayOrderQuerySql, cn.lyy.merchant.service.onedata.columns.pg.OrderColumnArray.DayOrderQuerySql) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.WeekOrderQuerySql, cn.lyy.merchant.service.onedata.columns.pg.OrderColumnArray.WeekOrderQuerySql) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.MonthOrderQuerySql, cn.lyy.merchant.service.onedata.columns.pg.OrderColumnArray.MonthOrderQuerySql) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.YearOrderQuerySql, cn.lyy.merchant.service.onedata.columns.pg.OrderColumnArray.YearOrderQuerySql) ;
		
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.EveryDayOrderQuerySql, cn.lyy.merchant.service.onedata.columns.pg.OrderColumnArray.EveryDayOrderQuerySql) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.EveryWeekOrderQuerySql, cn.lyy.merchant.service.onedata.columns.pg.OrderColumnArray.EveryWeekOrderQuerySql) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.EveryMonthOrderQuerySql, cn.lyy.merchant.service.onedata.columns.pg.OrderColumnArray.EveryMonthOrderQuerySql) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.EveryYearOrderQuerySql, cn.lyy.merchant.service.onedata.columns.pg.OrderColumnArray.EveryYearOrderQuerySql) ;
		
		//COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.OrderCountSql, cn.lyy.merchant.service.onedata.columns.pg.OrderColumnArray.OrderCountSql) ;
		//COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.OrderGroupCountSql, cn.lyy.merchant.service.onedata.columns.pg.OrderColumnArray.OrderGroupCountSql) ;
		
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.DAY_EQUIPMENT_GROUP_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.pg.EquipmentGroupColumnArray.DAY_EQUIPMENT_GROUP_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.WEEK_EQUIPMENT_GROUP_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.pg.EquipmentGroupColumnArray.WEEK_EQUIPMENT_GROUP_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.MONTH_EQUIPMENT_GROUP_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.pg.EquipmentGroupColumnArray.MONTH_EQUIPMENT_GROUP_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.YEAR_EQUIPMENT_GROUP_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.pg.EquipmentGroupColumnArray.YEAR_EQUIPMENT_GROUP_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.DAY_SUB_EQUIPMENT_GROUP_LIST_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.pg.EquipmentGroupColumnArray.DAY_SUB_EQUIPMENT_GROUP_LIST_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.COMMON_SUB_EQUIPMENT_GROUP_LIST_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.pg.EquipmentGroupColumnArray.COMMON_SUB_EQUIPMENT_GROUP_LIST_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.COMMON_AVG_SUB_EQUIPMENT_GROUP_LIST_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.pg.EquipmentGroupColumnArray.COMMON_AVG_SUB_EQUIPMENT_GROUP_LIST_COLUMN_ARRAY) ;
		
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.GroupEquipmentSql, cn.lyy.merchant.service.onedata.columns.pg.EquipmentGroupColumnArray.GroupEquipmentSql) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.EquipmentSql, cn.lyy.merchant.service.onedata.columns.pg.EquipmentColumnArray.EquipmentSql) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.EquipmentCountSql, cn.lyy.merchant.service.onedata.columns.pg.EquipmentColumnArray.EquipmentCountSql) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.DayGroupEquipmentSql, cn.lyy.merchant.service.onedata.columns.pg.EquipmentGroupColumnArray.DayGroupEquipmentSql) ;
		
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.PROVINCE_AVG_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.pg.ProvinceColumnArray.PROVINCE_AVG_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.PROVINCE_COMMON_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.pg.ProvinceColumnArray.PROVINCE_COMMON_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.CITY_AVG_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.pg.CityColumnArray.CITY_AVG_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.CITY_COMMON_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.pg.CityColumnArray.CITY_COMMON_COLUMN_ARRAY) ;
		
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.DayMemberEquipmentSql, cn.lyy.merchant.service.onedata.columns.pg.MemberEquipmentColumnArray.DayMemberSql) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.WeekMemberEquipmentSql, cn.lyy.merchant.service.onedata.columns.pg.MemberEquipmentColumnArray.WeekMemberSql) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.MonthMemberEquipmentSql, cn.lyy.merchant.service.onedata.columns.pg.MemberEquipmentColumnArray.MonthMemberSql) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.YearMemberEquipmentSql, cn.lyy.merchant.service.onedata.columns.pg.MemberEquipmentColumnArray.YearMemberSql) ;
		
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.DayMemberEquipmentListSql, cn.lyy.merchant.service.onedata.columns.pg.MemberEquipmentColumnArray.DayMemberListSql) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.WeekMemberEquipmentListSql, cn.lyy.merchant.service.onedata.columns.pg.MemberEquipmentColumnArray.WeekMemberListSql) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.MonthMemberEquipmentListSql, cn.lyy.merchant.service.onedata.columns.pg.MemberEquipmentColumnArray.MonthMemberListSql) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.YearMemberEquipmentListSql, cn.lyy.merchant.service.onedata.columns.pg.MemberEquipmentColumnArray.YearMemberListSql) ;
		
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.DayEquipmentTypeListSql, cn.lyy.merchant.service.onedata.columns.pg.EquipmentTypeColumnArray.DayEquipmentTypeListSql) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.WeekEquipmentTypeListSql, cn.lyy.merchant.service.onedata.columns.pg.EquipmentTypeColumnArray.WeekEquipmentTypeListSql) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.MonthEquipmentTypeListSql, cn.lyy.merchant.service.onedata.columns.pg.EquipmentTypeColumnArray.MonthEquipmentTypeListSql) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.YearEquipmentTypeListSql, cn.lyy.merchant.service.onedata.columns.pg.EquipmentTypeColumnArray.YearEquipmentTypeListSql) ;
		
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.DAY_MEMBER_BENEFIT_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.pg.MemberBenefitColumnArray.DAY_MEMBER_BENEFIT_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.WEEK_MEMBER_BENEFIT_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.pg.MemberBenefitColumnArray.WEEK_MEMBER_BENEFIT_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.MONTH_MEMBER_BENEFIT_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.pg.MemberBenefitColumnArray.MONTH_MEMBER_BENEFIT_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.YEAR_MEMBER_BENEFIT_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.pg.MemberBenefitColumnArray.YEAR_MEMBER_BENEFIT_COLUMN_ARRAY) ;
		
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.DAY_MEMBER_DATA_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.pg.MemberColumnArray.DAY_MEMBER_DATA_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.WEEK_MEMBER_DATA_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.pg.MemberColumnArray.WEEK_MEMBER_DATA_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.MONTH_MEMBER_DATA_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.pg.MemberColumnArray.MONTH_MEMBER_DATA_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.YEAR_MEMBER_DATA_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.pg.MemberColumnArray.YEAR_MEMBER_DATA_COLUMN_ARRAY) ;
		
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.DAY_PAY_TYPE_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.pg.PayTypeColumnArray.DAY_PAY_TYPE_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.WEEK_PAY_TYPE_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.pg.PayTypeColumnArray.WEEK_PAY_TYPE_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.MONTH_PAY_TYPE_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.pg.PayTypeColumnArray.MONTH_PAY_TYPE_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.YEAR_PAY_TYPE_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.pg.PayTypeColumnArray.YEAR_PAY_TYPE_COLUMN_ARRAY) ;
		
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.DAY_TIME_RANGE_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.pg.TimeRangeColumnArray.DAY_TIME_RANGE_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.WEEK_TIME_RANGE_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.pg.TimeRangeColumnArray.WEEK_TIME_RANGE_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.MONTH_TIME_RANGE_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.pg.TimeRangeColumnArray.MONTH_TIME_RANGE_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.YEAR_TIME_RANGE_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.pg.TimeRangeColumnArray.YEAR_TIME_RANGE_COLUMN_ARRAY) ;

		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.DayGroupListSql, cn.lyy.merchant.service.onedata.columns.pg.EquipmentGroupColumnArray.DayGroupListSql) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.WeekGroupListSql, cn.lyy.merchant.service.onedata.columns.pg.EquipmentGroupColumnArray.WeekGroupListSql) ;
		
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.RT_ORDER_DATA_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.pg.RealTimeColumnArray.RT_ORDER_DATA_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.RT_TIME_RANGE_DATA_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.pg.RealTimeColumnArray.RT_TIME_RANGE_DATA_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.RT_PER_ORDER_PRICE_COUNT_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.pg.RealTimeColumnArray.RT_PER_ORDER_PRICE_COUNT_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.RT_MEMBER_DATA_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.pg.RealTimeColumnArray.RT_MEMBER_DATA_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.PG + ColumnArrayMapKey.RT_GROUP_DATA_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.pg.RealTimeColumnArray.RT_GROUP_DATA_COLUMN_ARRAY) ;
		
		//=================================================================
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.DROP_DOWN_PROVINCE_COLUMN_ARRAY,cn.lyy.merchant.service.onedata.columns.selectdb.EquipmentGroupColumnArray.DROP_DOWN_PROVINCE_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.DROP_DOWN_CITY_COLUMN_ARRAY,cn.lyy.merchant.service.onedata.columns.selectdb.EquipmentGroupColumnArray.DROP_DOWN_CITY_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.DROP_DOWN_DISTRICT_COLUMN_ARRAY,cn.lyy.merchant.service.onedata.columns.selectdb.EquipmentGroupColumnArray.DROP_DOWN_DISTRICT_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.DROP_DOWN_EQUIPMENT_GROUP_COLUMN_ARRAY,cn.lyy.merchant.service.onedata.columns.selectdb.EquipmentGroupColumnArray.DROP_DOWN_EQUIPMENT_GROUP_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.DROP_DOWN_EQUIPMENT_TYPE_COLUMN_ARRAY,cn.lyy.merchant.service.onedata.columns.selectdb.EquipmentGroupColumnArray.DROP_DOWN_EQUIPMENT_TYPE_COLUMN_ARRAY) ;
											   
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.DAY_ORDER_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.selectdb.OrderColumnArray.DAY_ORDER_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.WEEK_ORDER_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.selectdb.OrderColumnArray.WEEK_ORDER_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.MONTH_ORDER_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.selectdb.OrderColumnArray.MONTH_ORDER_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.YEAR_ORDER_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.selectdb.OrderColumnArray.YEAR_ORDER_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.DAY_PER_ORDER_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.selectdb.OrderColumnArray.DAY_PER_ORDER_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.WEEK_PER_ORDER_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.selectdb.OrderColumnArray.WEEK_PER_ORDER_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.MONTH_PER_ORDER_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.selectdb.OrderColumnArray.MONTH_PER_ORDER_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.YEAR_PER_ORDER_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.selectdb.OrderColumnArray.YEAR_PER_ORDER_COLUMN_ARRAY) ;
											   
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.DAY_PER_ORDER_PRICE_COUNT, cn.lyy.merchant.service.onedata.columns.selectdb.OrderColumnArray.DAY_PER_ORDER_PRICE_COUNT) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.WEEK_PER_ORDER_PRICE_COUNT, cn.lyy.merchant.service.onedata.columns.selectdb.OrderColumnArray.WEEK_PER_ORDER_PRICE_COUNT) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.MONTH_PER_ORDER_PRICE_COUNT, cn.lyy.merchant.service.onedata.columns.selectdb.OrderColumnArray.MONTH_PER_ORDER_PRICE_COUNT) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.YEAR_PER_ORDER_PRICE_COUNT, cn.lyy.merchant.service.onedata.columns.selectdb.OrderColumnArray.YEAR_PER_ORDER_PRICE_COUNT) ;
											   
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.DayOrderQuerySql, cn.lyy.merchant.service.onedata.columns.selectdb.OrderColumnArray.DayOrderQuerySql) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.WeekOrderQuerySql, cn.lyy.merchant.service.onedata.columns.selectdb.OrderColumnArray.WeekOrderQuerySql) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.MonthOrderQuerySql, cn.lyy.merchant.service.onedata.columns.selectdb.OrderColumnArray.MonthOrderQuerySql) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.YearOrderQuerySql, cn.lyy.merchant.service.onedata.columns.selectdb.OrderColumnArray.YearOrderQuerySql) ;
											   
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.EveryDayOrderQuerySql, cn.lyy.merchant.service.onedata.columns.selectdb.OrderColumnArray.EveryDayOrderQuerySql) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.EveryWeekOrderQuerySql, cn.lyy.merchant.service.onedata.columns.selectdb.OrderColumnArray.EveryWeekOrderQuerySql) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.EveryMonthOrderQuerySql, cn.lyy.merchant.service.onedata.columns.selectdb.OrderColumnArray.EveryMonthOrderQuerySql) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.EveryYearOrderQuerySql, cn.lyy.merchant.service.onedata.columns.selectdb.OrderColumnArray.EveryYearOrderQuerySql) ;
											   
//		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.OrderCountSql, cn.lyy.merchant.service.onedata.columns.selectdb.OrderColumnArray.OrderCountSql) ;
//		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.OrderGroupCountSql, cn.lyy.merchant.service.onedata.columns.selectdb.OrderColumnArray.OrderGroupCountSql) ;
											   
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.DAY_EQUIPMENT_GROUP_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.selectdb.EquipmentGroupColumnArray.DAY_EQUIPMENT_GROUP_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.WEEK_EQUIPMENT_GROUP_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.selectdb.EquipmentGroupColumnArray.WEEK_EQUIPMENT_GROUP_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.MONTH_EQUIPMENT_GROUP_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.selectdb.EquipmentGroupColumnArray.MONTH_EQUIPMENT_GROUP_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.YEAR_EQUIPMENT_GROUP_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.selectdb.EquipmentGroupColumnArray.YEAR_EQUIPMENT_GROUP_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.DAY_SUB_EQUIPMENT_GROUP_LIST_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.selectdb.EquipmentGroupColumnArray.DAY_SUB_EQUIPMENT_GROUP_LIST_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.COMMON_SUB_EQUIPMENT_GROUP_LIST_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.selectdb.EquipmentGroupColumnArray.COMMON_SUB_EQUIPMENT_GROUP_LIST_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.COMMON_AVG_SUB_EQUIPMENT_GROUP_LIST_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.selectdb.EquipmentGroupColumnArray.COMMON_AVG_SUB_EQUIPMENT_GROUP_LIST_COLUMN_ARRAY) ;
		
		
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.GroupEquipmentSql, cn.lyy.merchant.service.onedata.columns.selectdb.EquipmentGroupColumnArray.GroupEquipmentSql) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.EquipmentSql, cn.lyy.merchant.service.onedata.columns.selectdb.EquipmentColumnArray.EquipmentSql) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.EquipmentCountSql, cn.lyy.merchant.service.onedata.columns.selectdb.EquipmentColumnArray.EquipmentCountSql) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.DayGroupEquipmentSql, cn.lyy.merchant.service.onedata.columns.selectdb.EquipmentGroupColumnArray.DayGroupEquipmentSql) ;
											   
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.PROVINCE_AVG_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.selectdb.ProvinceColumnArray.PROVINCE_AVG_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.PROVINCE_COMMON_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.selectdb.ProvinceColumnArray.PROVINCE_COMMON_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.CITY_AVG_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.selectdb.CityColumnArray.CITY_AVG_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.CITY_COMMON_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.selectdb.CityColumnArray.CITY_COMMON_COLUMN_ARRAY) ;
											   
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.DayMemberEquipmentSql, cn.lyy.merchant.service.onedata.columns.selectdb.MemberEquipmentColumnArray.DayMemberSql) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.WeekMemberEquipmentSql, cn.lyy.merchant.service.onedata.columns.selectdb.MemberEquipmentColumnArray.WeekMemberSql) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.MonthMemberEquipmentSql, cn.lyy.merchant.service.onedata.columns.selectdb.MemberEquipmentColumnArray.MonthMemberSql) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.YearMemberEquipmentSql, cn.lyy.merchant.service.onedata.columns.selectdb.MemberEquipmentColumnArray.YearMemberSql) ;
											   
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.DayMemberEquipmentListSql, cn.lyy.merchant.service.onedata.columns.selectdb.MemberEquipmentColumnArray.DayMemberListSql) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.WeekMemberEquipmentListSql, cn.lyy.merchant.service.onedata.columns.selectdb.MemberEquipmentColumnArray.WeekMemberListSql) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.MonthMemberEquipmentListSql, cn.lyy.merchant.service.onedata.columns.selectdb.MemberEquipmentColumnArray.MonthMemberListSql) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.YearMemberEquipmentListSql, cn.lyy.merchant.service.onedata.columns.selectdb.MemberEquipmentColumnArray.YearMemberListSql) ;
											   
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.DayEquipmentTypeListSql, cn.lyy.merchant.service.onedata.columns.selectdb.EquipmentTypeColumnArray.DayEquipmentTypeListSql) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.WeekEquipmentTypeListSql, cn.lyy.merchant.service.onedata.columns.selectdb.EquipmentTypeColumnArray.WeekEquipmentTypeListSql) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.MonthEquipmentTypeListSql, cn.lyy.merchant.service.onedata.columns.selectdb.EquipmentTypeColumnArray.MonthEquipmentTypeListSql) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.YearEquipmentTypeListSql, cn.lyy.merchant.service.onedata.columns.selectdb.EquipmentTypeColumnArray.YearEquipmentTypeListSql) ;
											   
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.DAY_MEMBER_BENEFIT_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.selectdb.MemberBenefitColumnArray.DAY_MEMBER_BENEFIT_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.WEEK_MEMBER_BENEFIT_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.selectdb.MemberBenefitColumnArray.WEEK_MEMBER_BENEFIT_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.MONTH_MEMBER_BENEFIT_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.selectdb.MemberBenefitColumnArray.MONTH_MEMBER_BENEFIT_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.YEAR_MEMBER_BENEFIT_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.selectdb.MemberBenefitColumnArray.YEAR_MEMBER_BENEFIT_COLUMN_ARRAY) ;
											   
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.DAY_MEMBER_DATA_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.selectdb.MemberColumnArray.DAY_MEMBER_DATA_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.WEEK_MEMBER_DATA_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.selectdb.MemberColumnArray.WEEK_MEMBER_DATA_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.MONTH_MEMBER_DATA_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.selectdb.MemberColumnArray.MONTH_MEMBER_DATA_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.YEAR_MEMBER_DATA_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.selectdb.MemberColumnArray.YEAR_MEMBER_DATA_COLUMN_ARRAY) ;
											   
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.DAY_PAY_TYPE_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.selectdb.PayTypeColumnArray.DAY_PAY_TYPE_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.WEEK_PAY_TYPE_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.selectdb.PayTypeColumnArray.WEEK_PAY_TYPE_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.MONTH_PAY_TYPE_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.selectdb.PayTypeColumnArray.MONTH_PAY_TYPE_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.YEAR_PAY_TYPE_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.selectdb.PayTypeColumnArray.YEAR_PAY_TYPE_COLUMN_ARRAY) ;
											   
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.DAY_TIME_RANGE_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.selectdb.TimeRangeColumnArray.DAY_TIME_RANGE_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.WEEK_TIME_RANGE_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.selectdb.TimeRangeColumnArray.WEEK_TIME_RANGE_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.MONTH_TIME_RANGE_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.selectdb.TimeRangeColumnArray.MONTH_TIME_RANGE_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.YEAR_TIME_RANGE_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.selectdb.TimeRangeColumnArray.YEAR_TIME_RANGE_COLUMN_ARRAY) ;
											   
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.DayGroupListSql, cn.lyy.merchant.service.onedata.columns.selectdb.EquipmentGroupColumnArray.DayGroupListSql) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.WeekGroupListSql, cn.lyy.merchant.service.onedata.columns.selectdb.EquipmentGroupColumnArray.WeekGroupListSql) ;
											   
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.RT_ORDER_DATA_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.selectdb.RealTimeColumnArray.RT_ORDER_DATA_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.RT_TIME_RANGE_DATA_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.selectdb.RealTimeColumnArray.RT_TIME_RANGE_DATA_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.RT_PER_ORDER_PRICE_COUNT_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.selectdb.RealTimeColumnArray.RT_PER_ORDER_PRICE_COUNT_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.RT_MEMBER_DATA_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.selectdb.RealTimeColumnArray.RT_MEMBER_DATA_COLUMN_ARRAY) ;
		COLUMN_ARRAY_MAP.put(ColumnArrayMapKey.SELECT_DB + ColumnArrayMapKey.RT_GROUP_DATA_COLUMN_ARRAY, cn.lyy.merchant.service.onedata.columns.selectdb.RealTimeColumnArray.RT_GROUP_DATA_COLUMN_ARRAY) ;
		
	}
	
	public static String columnArray(String dbType,String key) {
		String newKey = buildKey(dbType,key) ;
		return COLUMN_ARRAY_MAP.get(newKey) ;
	}
	
	private static String buildKey(String dbType,String key) {
		return (new StringBuffer()).append(dbType).append("_").append(key).toString() ;
	}
    
}
