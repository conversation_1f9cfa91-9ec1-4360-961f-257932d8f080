package cn.lyy.merchant.service.onedata.mobile;

import com.alibaba.fastjson.JSONObject;

import cn.lyy.merchant.controller.onedata.dto.DayParamDto;
import cn.lyy.merchant.controller.onedata.dto.MonthParamDto;
import cn.lyy.merchant.controller.onedata.dto.WeekParamDto;
import cn.lyy.merchant.controller.onedata.dto.YearParamDto;

public interface MemberBenefitDataService {

	 /**
     * 日报会员储值数据
     * @param dayParamDto
     * @return
     */
    public JSONObject getMemberBenefitData(DayParamDto dayParamDto);

    /**
     * 周报会员储值数据
     * @param weekParamDto
     * @return
     */
    public JSONObject getWeekMemberBenefitData(WeekParamDto weekParamDto);

    /**
     * 月报会员储值数据
     * @param monthParamDto
     * @return
     */
    public JSONObject getMonthMemberBenefitData(MonthParamDto monthParamDto);

    /**
     * 年报会员储值数据
     * @param yearParamDto
     * @return
     */
    public JSONObject getYearMemberBenefitData(YearParamDto yearParamDto);

    
}
