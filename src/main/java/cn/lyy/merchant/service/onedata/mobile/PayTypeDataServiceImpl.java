package cn.lyy.merchant.service.onedata.mobile;

import java.util.HashSet;
import java.util.TreeSet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import cn.lyy.base.util.StringUtil;
import cn.lyy.merchant.controller.onedata.dto.DayParamDto;
import cn.lyy.merchant.controller.onedata.dto.MonthParamDto;
import cn.lyy.merchant.controller.onedata.dto.WeekParamDto;
import cn.lyy.merchant.controller.onedata.dto.YearParamDto;
import cn.lyy.merchant.service.onedata.FeignUtil;
import cn.lyy.merchant.service.onedata.ParamDto;
import cn.lyy.merchant.service.onedata.columns.ColumnArrayMap;
import cn.lyy.merchant.service.onedata.columns.ColumnArrayMapKey;
import lombok.extern.slf4j.Slf4j;

/**
 * 付款方式分析
 * <AUTHOR>
 * @date 2024-02-01
 *
 */
@Service
@Slf4j
public class PayTypeDataServiceImpl implements PayTypeDataService{

	@Autowired
    private FeignUtil feignUtil ;
	
	
    @Value("${oneData.dayPayTypeAnalyseCode}")
    private String dayPayTypeAnalyseCode;
    @Value("${oneData.weekPayTypeAnalyseCode}")
    private String weekPayTypeAnalyseCode;
    @Value("${oneData.monthPayTypeAnalyseCode}")
    private String monthPayTypeAnalyseCode;
    @Value("${oneData.yearPayTypeAnalyseCode}")
    private String yearPayTypeAnalyseCode;
    
    /**
     * 获取日报支付方式分析数据
     * @param dayParamDto
     * @return
     */
    @SuppressWarnings({ "serial", "rawtypes" })
	@Override
    public JSONObject getPayTypeAnalyseData(DayParamDto dayParamDto) {
        ParamDto params = new ParamDto();
        params.setCode(dayPayTypeAnalyseCode);
        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.DAY_PAY_TYPE_COLUMN_ARRAY) ;
        params.setColumnArray(columnArray);
        params.getQueryParams().setDay(dayParamDto.getDay());
        params.getQueryParams().setMerchant_id(dayParamDto.getMerchantId()+"");
        params.getQueryParams().setCombo_type(dayParamDto.getComboType());
        params.getQueryParams().setDay(dayParamDto.getDay());

        params.getQueryParams().setProvince_id(dayParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(dayParamDto.getProvinceId()+"");}});
        params.getQueryParams().setCity_id(dayParamDto.getCityId() == null ? null:new HashSet<String>(){{add(dayParamDto.getCityId()+"");}});
        params.getQueryParams().setDistrict_id(dayParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(dayParamDto.getAreaId()+"");}});
        params.getQueryParams().setEquipment_group_id(dayParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(dayParamDto.getGroupId()+"");}});
        params.getQueryParams().setLyy_equipment_type_id(dayParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(dayParamDto.getEquipmentTypeId()+"");}});

        if(dayParamDto.getGroupIds() != null && dayParamDto.getGroupIds().size()>0){
            if(dayParamDto.getGroupId() == null || !dayParamDto.getGroupIds().contains(dayParamDto.getGroupId()+"")){
                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(dayParamDto.getGroupIds())).first()+"");}});
            }
            
            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
            //params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
        }
        params.resetComboType();

        try{
            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
            JSONObject result = feignUtil.getData(params);
            log.info("{}",result.toJSONString());
            return result;
        }catch (Exception e){
            log.warn("{}",e);
        }

        return null;
    }

    /**
     * 获取周报支付方式分析数据
     * @param yearParamDto
     * @return
     */
    @SuppressWarnings({ "serial", "rawtypes" })
    @Override
    public JSONObject getWeekPayTypeAnalyseData(WeekParamDto weekParamDto) {
        ParamDto params = new ParamDto();
        params.setCode(weekPayTypeAnalyseCode);
        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.WEEK_PAY_TYPE_COLUMN_ARRAY) ;
        params.setColumnArray(columnArray);
        params.getQueryParams().setYear_month(weekParamDto.getMonth());
        params.getQueryParams().setMonth_week_num(weekParamDto.getWeek()+"");
        params.getQueryParams().setMerchant_id(weekParamDto.getMerchantId()+"");
        params.getQueryParams().setCombo_type(weekParamDto.getComboType());

        params.getQueryParams().setProvince_id(weekParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(weekParamDto.getProvinceId()+"");}});
        params.getQueryParams().setCity_id(weekParamDto.getCityId() == null ? null:new HashSet<String>(){{add(weekParamDto.getCityId()+"");}});
        params.getQueryParams().setDistrict_id(weekParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(weekParamDto.getAreaId()+"");}});
        params.getQueryParams().setEquipment_group_id(weekParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(weekParamDto.getGroupId()+"");}});
        //params.getQueryParams().setLyy_equipment_type_id(weekParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(dayParamDto.getEquipmentTypeId()+"");}});

        if(weekParamDto.getGroupIds() != null && weekParamDto.getGroupIds().size()>0){
            if(weekParamDto.getGroupId() == null || !weekParamDto.getGroupIds().contains(weekParamDto.getGroupId()+"")){
                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(weekParamDto.getGroupIds())).first()+"");}});
            }
            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
            //params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
        }
        params.resetComboType();
        try{
            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
            JSONObject result = feignUtil.getData(params);
            log.info("{}",result.toJSONString());
            return result;
        }catch (Exception e){
            log.warn("{}",e);
        }

        return null;
    }

    /**
     * 获取月报支付方式分析数据
     * @param yearParamDto
     * @return
     */
    @SuppressWarnings({ "serial", "rawtypes" })
    @Override
    public JSONObject getMonthPayTypeAnalyseData(MonthParamDto monthParamDto) {
        ParamDto params = new ParamDto();
        params.setCode(monthPayTypeAnalyseCode);
        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.MONTH_PAY_TYPE_COLUMN_ARRAY) ;
        params.setColumnArray(columnArray);

        params.getQueryParams().setYear_month(monthParamDto.getMonth());
        params.getQueryParams().setMerchant_id(monthParamDto.getMerchantId()+"");
        params.getQueryParams().setCombo_type(monthParamDto.getComboType());

        params.getQueryParams().setProvince_id(monthParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(monthParamDto.getProvinceId()+"");}});
        params.getQueryParams().setCity_id(monthParamDto.getCityId() == null ? null:new HashSet<String>(){{add(monthParamDto.getCityId()+"");}});
        params.getQueryParams().setDistrict_id(monthParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(monthParamDto.getAreaId()+"");}});
        params.getQueryParams().setEquipment_group_id(monthParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(monthParamDto.getGroupId()+"");}});
        //params.getQueryParams().setLyy_equipment_type_id(weekParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(dayParamDto.getEquipmentTypeId()+"");}});

        if(monthParamDto.getGroupIds() != null && monthParamDto.getGroupIds().size()>0){
            if(monthParamDto.getGroupId() == null || !monthParamDto.getGroupIds().contains(monthParamDto.getGroupId()+"")){
                 params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(monthParamDto.getGroupIds())).first()+"");}});
            }
            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
            //params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
        }
        params.resetComboType();
        try{
            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
            JSONObject result = feignUtil.getData(params);
            log.info("{}",result.toJSONString());
            //去掉支付方式为0的
            if(result !=null && result.getJSONObject("info") != null){
                JSONArray arr = result.getJSONObject("info").getJSONArray("list");
                if(arr != null && arr.size()>0){
                    for(int i=0; i<arr.size(); i++){
                        JSONObject obj = arr.getJSONObject(i);
                        if(obj.get("payAmount") == null && obj.get("payCount") == null){
                            arr.remove(obj);
                        }

                        if(obj.get("payAmount") != null && obj.getDouble("payAmount") ==0 && obj.get("payCount") != null && obj.getDouble("payCount") == 0){
                            arr.remove(obj);
                        }
                    }
                }
            }
            return result;
        }catch (Exception e){
            log.warn("{}",e);
        }



        return null;
    }

    /**
     * 获取年报支付方式分析数据
     * @param yearParamDto
     * @return
     */
    @SuppressWarnings({ "serial", "rawtypes" })
    @Override
    public JSONObject getYearPayTypeAnalyseData(YearParamDto yearParamDto) {
        ParamDto params = new ParamDto();
        params.setCode(yearPayTypeAnalyseCode);
        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.YEAR_PAY_TYPE_COLUMN_ARRAY) ;
        params.setColumnArray(columnArray);

        params.getQueryParams().setYear_str(yearParamDto.getYear()+"");
        params.getQueryParams().setMerchant_id(yearParamDto.getMerchantId()+"");
        params.getQueryParams().setCombo_type(yearParamDto.getComboType());

        params.getQueryParams().setProvince_id(yearParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(yearParamDto.getProvinceId()+"");}});
        params.getQueryParams().setCity_id(yearParamDto.getCityId() == null ? null:new HashSet<String>(){{add(yearParamDto.getCityId()+"");}});
        params.getQueryParams().setDistrict_id(yearParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(yearParamDto.getAreaId()+"");}});
        params.getQueryParams().setEquipment_group_id(yearParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(yearParamDto.getGroupId()+"");}});
        //params.getQueryParams().setLyy_equipment_type_id(weekParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(dayParamDto.getEquipmentTypeId()+"");}});

        if(yearParamDto.getGroupIds() != null && yearParamDto.getGroupIds().size()>0){
            if(yearParamDto.getGroupId() == null || !yearParamDto.getGroupIds().contains(yearParamDto.getGroupId()+"")){
                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(yearParamDto.getGroupIds())).first()+"");}});
            }
            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
            //params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
        }
        params.resetComboType();
        try{
            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
            JSONObject result = feignUtil.getData(params);
            log.info("{}",result.toJSONString());
            //去掉支付方式为0的
            if(result !=null && result.getJSONObject("info") != null){
                JSONArray arr = result.getJSONObject("info").getJSONArray("list");
                if(arr != null && arr.size()>0){
                    for(int i=0; i<arr.size(); i++){
                        JSONObject obj = arr.getJSONObject(i);
                        if(obj.get("payAmount") == null && obj.get("payCount") == null){
                            arr.remove(obj);
                        }

                        if(obj.get("payAmount") != null && obj.getDouble("payAmount") ==0 && obj.get("payCount") != null && obj.getDouble("payCount") == 0){
                            arr.remove(obj);
                        }
                    }
                }
            }
            return result;
        }catch (Exception e){
            log.warn("{}",e);
        }

        return null;
    }

}
