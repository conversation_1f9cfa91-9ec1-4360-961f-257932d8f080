package cn.lyy.merchant.service.onedata.mobile;

import com.alibaba.fastjson.JSONObject;
import cn.lyy.merchant.controller.onedata.dto.DayParamDto;
import cn.lyy.merchant.controller.onedata.dto.MonthParamDto;
import cn.lyy.merchant.controller.onedata.dto.WeekParamDto;
import cn.lyy.merchant.controller.onedata.dto.YearParamDto;

public interface EquipmentDataService {

	/**
     * 日报设备维度数
     * @param dayParamDto
     * @return
     */
    public JSONObject getDayEquipmentData(DayParamDto dayParamDto);

    /**
     * 周报设备维度数
     * @param weekParamDto
     * @return
     */
    public JSONObject getWeekEquipmentData(WeekParamDto weekParamDto);

    /**
     * 月报设备维度数
     * @param monthParamDto
     * @return
     */
    public JSONObject getMonthEquipmentData(MonthParamDto monthParamDto);

    /**
     * 年报设备维度数
     * @param yearParamDto
     * @return
     */
    public JSONObject getYearEquipmentData(YearParamDto yearParamDto);

    
    /**
     * 获取二级页面统计数据
     * @param paramDto
     * @return
     */
    public JSONObject getDaySumEquipmentData(DayParamDto paramDto);

    /**
     * 获取二级页面统计数据
     * @param paramDto
     * @return
     */
    public JSONObject getWeekSumEquipmentData(WeekParamDto paramDto);

    /**
     * 获取二级页面统计数据
     * @param paramDto
     * @return
     */
    public JSONObject getMonthSumEquipmentData(MonthParamDto paramDto);

    /**
     * 获取二级页面统计数据
     * @param paramDto
     * @return
     */
    public JSONObject getYearSumEquipmentData(YearParamDto paramDto);
    
}
