package cn.lyy.merchant.service.onedata.mobile;

import com.alibaba.fastjson.JSONObject;

import cn.lyy.merchant.controller.onedata.dto.DayParamDto;
import cn.lyy.merchant.controller.onedata.dto.MonthParamDto;
import cn.lyy.merchant.controller.onedata.dto.WeekParamDto;
import cn.lyy.merchant.controller.onedata.dto.YearParamDto;

public interface CityDataService {

	/**
     * 日报获取城市维度统计信息
     * @param dayParamDto
     * @return
     */
    public JSONObject getDayCityData(DayParamDto dayParamDto);

    /**
     * 周报报获取城市维度统计信息
     * @param weekParamDto
     * @return
     */
    public JSONObject getWeekCityData(WeekParamDto weekParamDto);

    /**
     * 月报报获取城市维度统计信息
     * @param monthParamDto
     * @return
     */
    public JSONObject getMonthCityData(MonthParamDto monthParamDto);

    /**
     * 年报报获取城市维度统计信息
     * @param yearParamDto
     * @return
     */
    public JSONObject getYearCityData(YearParamDto yearParamDto);
    
    /**
     * 日报获取城市维度统计信息-底部平均值
     * @param dayParamDto
     * @return
     */
    public JSONObject getDayCityAvgData(DayParamDto dayParamDto);

    /**
     * 周报报获取城市维度统计信息-底部平均值
     * @param weekParamDto
     * @return
     */
    public JSONObject getWeekCityAvgData(WeekParamDto weekParamDto);

    /**
     * 月报报获取城市维度统计信息-底部平均值
     * @param monthParamDto
     * @return
     */
    public JSONObject getMonthCityAvgData(MonthParamDto monthParamDto);

    /**
     * 年报报获取城市维度统计信息-底部平均值
     * @param yearParamDto
     * @return
     */
    public JSONObject getYearCityAvgData(YearParamDto yearParamDto);
    
}
