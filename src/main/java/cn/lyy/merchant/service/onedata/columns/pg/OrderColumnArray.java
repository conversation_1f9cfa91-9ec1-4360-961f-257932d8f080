package cn.lyy.merchant.service.onedata.columns.pg;

public class OrderColumnArray {

	public static String DAY_ORDER_COLUMN_ARRAY = 
		"merchant_id as \"merchantId\", " +

        "cast(online_actual_amount as decimal(15,2)) as \"onlineAmount\"," +
        "cast(case when online_actual_amount_yesterday =0 and online_actual_amount>0 then 1 when online_actual_amount_yesterday=0 and online_actual_amount=0 then 0 else (online_actual_amount-online_actual_amount_yesterday)::numeric/online_actual_amount_yesterday::numeric end as decimal(15,2)) as \"onlineAmountUp\"," +

        "cast(online_pay_amount as decimal(15,2)) as \"onlinePayAmount\", " +
        "cast(case when online_pay_amount_yesterday =0 and online_pay_amount>0 then 1 when online_pay_amount_yesterday=0 and online_pay_amount=0 then 0 else (online_pay_amount-online_pay_amount_yesterday)::numeric/online_pay_amount_yesterday::numeric end as decimal(15,2)) as \"onlinePayAmountUp\"," +

        "online_pay_count as \"onlinePayCounts\", " +
        "cast(case when online_pay_count_yesterday =0 and online_pay_count>0 then 1 when online_pay_count=0 and online_pay_count_yesterday=0 then 0 else (online_pay_count-online_pay_count_yesterday)::numeric/online_pay_count_yesterday::numeric end as decimal(15,2)) as \"onlinePayCountsUp\"," +

        "cast(online_refund_amount as decimal(15,2)) as \"onlineRefundAmount\", " +
        "cast(case when online_refund_amount_yesterday =0 and online_refund_amount>0 then 1 when online_refund_amount_yesterday=0 and online_refund_amount=0 then 0 else (online_refund_amount-online_refund_amount_yesterday)::numeric/online_refund_amount_yesterday::numeric end as decimal(15,2)) as \"onlineRefundAmountUp\"," +

        "online_refund_count as \"onlineRefundCounts\"," +
        "cast(case when online_refund_count_yesterday =0 and online_refund_count>0 then 1 when online_refund_count_yesterday=0 and online_refund_count=0 then 0 else (online_refund_count-online_refund_count_yesterday)::numeric/online_refund_count_yesterday::numeric end as decimal(15,2)) as \"onlineRefundCountsUp\"," +

        "cast(offline_actual_amount as decimal(15,2)) as \"cashAmount\"," +        //现金总收入
        "cast(case when offline_actual_amount_yesterday =0 and offline_actual_amount>0 then 1 when offline_actual_amount_yesterday=0 and offline_actual_amount=0 then 0 else (offline_actual_amount-offline_actual_amount_yesterday)::numeric/offline_actual_amount_yesterday::numeric end as decimal(15,2)) as \"cashAmountUp\"," +

        "cast(offline_pay_amount as decimal(15,2)) as \"cashPayAmount\"," +        //现金收款
        "cast(case when offline_pay_amount_yesterday =0 and offline_pay_amount>0 then 1 when offline_pay_amount_yesterday=0 and offline_pay_amount=0 then 0 else (offline_pay_amount-offline_pay_amount_yesterday)::numeric/offline_pay_amount_yesterday::numeric end as decimal(15,2)) as \"cashPayAmountUp\"," +

        "cast(offline_pay_count as decimal(15,2)) as \"cashPayCounts\"," +        //现金收款笔数
        "cast(case when offline_pay_count_yesterday =0 and offline_pay_count>0 then 1 when offline_pay_count_yesterday=0 and offline_pay_count=0 then 0 else (offline_pay_count-offline_pay_count_yesterday)::numeric/offline_pay_count_yesterday::numeric end as decimal(15,2)) as \"cashPayCountsUp\"," +

        "0 as \"cashRefundAmount\", 0 as \"cashRefundAmountUp\", 0 as \"cashRefundCounts\", 0 as \"cashRefundCountsUp\"," +       //现金没有退款

        "cast(pay_amount as decimal(15,2)) as \"payAmount\"," +
        "cast(case when pay_amount_yesterday =0 and pay_amount>0 then 1 when pay_amount_yesterday=0 and pay_amount=0 then 0 else (pay_amount-pay_amount_yesterday)::numeric/pay_amount_yesterday::numeric end as decimal(15,2)) as \"payAmountUp\"," +

        "pay_count as \"payCounts\"," +
        "cast(case when pay_count_yesterday =0 and pay_count>0 then 1 when pay_count_yesterday=0 and pay_count=0 then 0 else (pay_count-pay_count_yesterday)::numeric/pay_count_yesterday::numeric end as decimal(15,2)) as \"payCountsUp\"," +

        "cast(refund_amount as decimal(15,2)) as \"refundAmount\"," +
        "cast(case when refund_amount_yesterday =0 and refund_amount>0 then 1 when refund_amount_yesterday=0 and refund_amount=0 then 0 else (refund_amount-refund_amount_yesterday)::numeric/refund_amount_yesterday::numeric end as decimal(15,2)) as \"refundAmountUp\"," +

        "refund_count as \"refundCounts\"," +
        "cast(case when refund_count_yesterday =0 and refund_count>0 then 1 when refund_count_yesterday=0 and refund_count=0 then 0 else (refund_count-refund_count_yesterday)::numeric/refund_count_yesterday::numeric end as decimal(15,2)) as \"refundCountsUp\"" ;
	
	
	public static String WEEK_ORDER_COLUMN_ARRAY = 
		"merchant_id as \"merchantId\", " +

        "cast(online_actual_amount as decimal(15,2)) as \"onlineAmount\"," +
        "cast(case when online_actual_amount_last_week =0 and online_actual_amount>0 then 1 when online_actual_amount_last_week=0 and online_actual_amount=0 then 0 else (online_actual_amount-online_actual_amount_last_week)::numeric/online_actual_amount_last_week::numeric end as decimal(15,2)) as \"onlineAmountUp\"," +

        "cast(online_pay_amount as decimal(15,2)) as \"onlinePayAmount\", " +
        "cast(case when online_pay_amount_last_week =0 and online_pay_amount>0 then 1 when online_pay_amount_last_week=0 and online_pay_amount=0 then 0 else (online_pay_amount-online_pay_amount_last_week)::numeric/online_pay_amount_last_week::numeric end as decimal(15,2)) as \"onlinePayAmountUp\"," +

        "online_pay_count as \"onlinePayCounts\", " +
        "cast(case when online_pay_count_last_week =0 and online_pay_count>0 then 1 when online_pay_count=0 and online_pay_count_last_week=0 then 0 else (online_pay_count-online_pay_count_last_week)::numeric/online_pay_count_last_week::numeric end as decimal(15,2)) as \"onlinePayCountsUp\"," +

        "cast(online_refund_amount as decimal(15,2)) as \"onlineRefundAmount\", " +
        "cast(case when online_refund_amount_last_week =0 and online_refund_amount>0 then 1 when online_refund_amount_last_week=0 and online_refund_amount=0 then 0 else (online_refund_amount-online_refund_amount_last_week)::numeric/online_refund_amount_last_week::numeric end as decimal(15,2)) as \"onlineRefundAmountUp\"," +

        "online_refund_count as \"onlineRefundCounts\"," +
        "cast(case when online_refund_count_last_week =0 and online_refund_count>0 then 1 when online_refund_count_last_week=0 and online_refund_count=0 then 0 else (online_refund_count-online_refund_count_last_week)::numeric/online_refund_count_last_week::numeric end as decimal(15,2)) as \"onlineRefundCountsUp\"," +

        "cast(offline_actual_amount as decimal(15,2)) as \"cashAmount\"," +        //现金总收入
        "cast(case when offline_actual_amount_last_week =0 and offline_actual_amount>0 then 1 when offline_actual_amount_last_week=0 and offline_actual_amount=0 then 0 else (offline_actual_amount-offline_actual_amount_last_week)::numeric/offline_actual_amount_last_week::numeric end as decimal(15,2)) as \"cashAmountUp\"," +

        "cast(offline_pay_amount as decimal(15,2)) as \"cashPayAmount\"," +        //现金收款
        "cast(case when offline_pay_amount_last_week =0 and offline_pay_amount>0 then 1 when offline_pay_amount_last_week=0 and offline_pay_amount=0 then 0 else (offline_pay_amount-offline_pay_amount_last_week)::numeric/offline_pay_amount_last_week::numeric end as decimal(15,2)) as \"cashPayAmountUp\"," +

        "cast(offline_pay_count as decimal(15,2)) as \"cashPayCounts\"," +        //现金收款笔数
        "cast(case when offline_pay_count_last_week =0 and offline_pay_count>0 then 1 when offline_pay_count_last_week=0 and offline_pay_count=0 then 0 else (offline_pay_count-offline_pay_count_last_week)::numeric/offline_pay_count_last_week::numeric end as decimal(15,2)) as \"cashPayCountsUp\"," +

        "0 as \"cashRefundAmount\", 0 as \"cashRefundAmountUp\", 0 as \"cashRefundCounts\", 0 as \"cashRefundCountsUp\"," +       //现金没有退款

        "cast(pay_amount as decimal(15,2)) as \"payAmount\"," +
        "cast(case when pay_amount_last_week =0 and pay_amount>0 then 1 when pay_amount_last_week=0 and pay_amount=0 then 0 else (pay_amount-pay_amount_last_week)::numeric/pay_amount_last_week::numeric end as decimal(15,2)) as \"payAmountUp\"," +

        "pay_count as \"payCounts\"," +
        "cast(case when pay_count_last_week =0 and pay_count>0 then 1 when pay_count_last_week=0 and pay_count=0 then 0 else (pay_count-pay_count_last_week)::numeric/pay_count_last_week::numeric end as decimal(15,2)) as \"payCountsUp\"," +

        "cast(refund_amount as decimal(15,2)) as \"refundAmount\"," +
        "cast(case when refund_amount_last_week =0 and refund_amount>0 then 1 when refund_amount_last_week=0 and refund_amount=0 then 0 else (refund_amount-refund_amount_last_week)::numeric/refund_amount_last_week::numeric end as decimal(15,2)) as \"refundAmountUp\"," +

        "refund_count as \"refundCounts\"," +
        "cast(case when refund_count_last_week =0 and refund_count>0 then 1 when refund_count_last_week=0 and refund_count=0 then 0 else (refund_count-refund_count_last_week)::numeric/refund_count_last_week::numeric end as decimal(15,2)) as \"refundCountsUp\"" ;
        
	public static String MONTH_ORDER_COLUMN_ARRAY =  
		"merchant_id as \"merchantId\", " +

        "cast(online_actual_amount as decimal(15,2)) as \"onlineAmount\"," +
        "cast(case when online_actual_amount_last_month =0 and online_actual_amount>0 then 1 when online_actual_amount_last_month=0 and online_actual_amount=0 then 0 else (online_actual_amount-online_actual_amount_last_month)::numeric/online_actual_amount_last_month::numeric end as decimal(15,2)) as \"onlineAmountUp\"," +

        "cast(online_pay_amount as decimal(15,2)) as \"onlinePayAmount\", " +
        "cast(case when online_pay_amount_last_month =0 and online_pay_amount>0 then 1 when online_pay_amount_last_month=0 and online_pay_amount=0 then 0 else (online_pay_amount-online_pay_amount_last_month)::numeric/online_pay_amount_last_month::numeric end as decimal(15,2)) as \"onlinePayAmountUp\"," +

        "online_pay_count as \"onlinePayCounts\", " +
        "cast(case when online_pay_count_last_month =0 and online_pay_count>0 then 1 when online_pay_count=0 and online_pay_count_last_month=0 then 0 else (online_pay_count-online_pay_count_last_month)::numeric/online_pay_count_last_month::numeric end as decimal(15,2)) as \"onlinePayCountsUp\"," +

        "cast(online_refund_amount as decimal(15,2)) as \"onlineRefundAmount\", " +
        "cast(case when online_refund_amount_last_month =0 and online_refund_amount>0 then 1 when online_refund_amount_last_month=0 and online_refund_amount=0 then 0 else (online_refund_amount-online_refund_amount_last_month)::numeric/online_refund_amount_last_month::numeric end as decimal(15,2)) as \"onlineRefundAmountUp\"," +

        "online_refund_count as \"onlineRefundCounts\"," +
        "cast(case when online_refund_count_last_month =0 and online_refund_count>0 then 1 when online_refund_count_last_month=0 and online_refund_count=0 then 0 else (online_refund_count-online_refund_count_last_month)::numeric/online_refund_count_last_month::numeric end as decimal(15,2)) as \"onlineRefundCountsUp\"," +

        "cast(offline_actual_amount as decimal(15,2)) as \"cashAmount\"," +        //现金总收入
        "cast(case when offline_actual_amount_last_month =0 and offline_actual_amount>0 then 1 when offline_actual_amount_last_month=0 and offline_actual_amount=0 then 0 else (offline_actual_amount-offline_actual_amount_last_month)::numeric/offline_actual_amount_last_month::numeric end as decimal(15,2)) as \"cashAmountUp\"," +

        "cast(offline_pay_amount as decimal(15,2)) as \"cashPayAmount\"," +        //现金收款
        "cast(case when offline_pay_amount_last_month =0 and offline_pay_amount>0 then 1 when offline_pay_amount_last_month=0 and offline_pay_amount=0 then 0 else (offline_pay_amount-offline_pay_amount_last_month)::numeric/offline_pay_amount_last_month::numeric end as decimal(15,2)) as \"cashPayAmountUp\"," +

        "cast(offline_pay_count as decimal(15,2)) as \"cashPayCounts\"," +        //现金收款笔数
        "cast(case when offline_pay_count_last_month =0 and offline_pay_count>0 then 1 when offline_pay_count_last_month=0 and offline_pay_count=0 then 0 else (offline_pay_count-offline_pay_count_last_month)::numeric/offline_pay_count_last_month::numeric end as decimal(15,2)) as \"cashPayCountsUp\"," +

        "0 as \"cashRefundAmount\", 0 as \"cashRefundAmountUp\", 0 as \"cashRefundCounts\", 0 as \"cashRefundCountsUp\"," +       //现金没有退款

        "cast(pay_amount as decimal(15,2)) as \"payAmount\"," +
        "cast(case when pay_amount_last_month =0 and pay_amount>0 then 1 when pay_amount_last_month=0 and pay_amount=0 then 0 else (pay_amount-pay_amount_last_month)::numeric/pay_amount_last_month::numeric end as decimal(15,2)) as \"payAmountUp\"," +

        "pay_count as \"payCounts\"," +
        "cast(case when pay_count_last_month =0 and pay_count>0 then 1 when pay_count_last_month=0 and pay_count=0 then 0 else (pay_count-pay_count_last_month)::numeric/pay_count_last_month::numeric end as decimal(15,2)) as \"payCountsUp\"," +

        "cast(refund_amount as decimal(15,2)) as \"refundAmount\"," +
        "cast(case when refund_amount_last_month =0 and refund_amount>0 then 1 when refund_amount_last_month=0 and refund_amount=0 then 0 else (refund_amount-refund_amount_last_month)::numeric/refund_amount_last_month::numeric end as decimal(15,2)) as \"refundAmountUp\"," +

        "refund_count as \"refundCounts\"," +
        "cast(case when refund_count_last_month =0 and refund_count>0 then 1 when refund_count_last_month=0 and refund_count=0 then 0 else (refund_count-refund_count_last_month)::numeric/refund_count_last_month::numeric end as decimal(15,2)) as \"refundCountsUp\"" ;
			
	public static String YEAR_ORDER_COLUMN_ARRAY = 
		"merchant_id as \"merchantId\", " +

        "cast(online_actual_amount as decimal(15,2)) as \"onlineAmount\"," +
        "cast(case when online_actual_amount_last_year =0 and online_actual_amount>0 then 1 when online_actual_amount_last_year=0 and online_actual_amount=0 then 0 else (online_actual_amount-online_actual_amount_last_year)::numeric/online_actual_amount_last_year::numeric end as decimal(15,2)) as \"onlineAmountUp\"," +

        "cast(online_pay_amount as decimal(15,2)) as \"onlinePayAmount\", " +
        "cast(case when online_pay_amount_last_year =0 and online_pay_amount>0 then 1 when online_pay_amount_last_year=0 and online_pay_amount=0 then 0 else (online_pay_amount-online_pay_amount_last_year)::numeric/online_pay_amount_last_year::numeric end as decimal(15,2)) as \"onlinePayAmountUp\"," +

        "online_pay_count as \"onlinePayCounts\", " +
        "cast(case when online_pay_count_last_year =0 and online_pay_count>0 then 1 when online_pay_count=0 and online_pay_count_last_year=0 then 0 else (online_pay_count-online_pay_count_last_year)::numeric/online_pay_count_last_year::numeric end as decimal(15,2)) as \"onlinePayCountsUp\"," +

        "cast(online_refund_amount as decimal(15,2)) as \"onlineRefundAmount\", " +
        "cast(case when online_refund_amount_last_year =0 and online_refund_amount>0 then 1 when online_refund_amount_last_year=0 and online_refund_amount=0 then 0 else (online_refund_amount-online_refund_amount_last_year)::numeric/online_refund_amount_last_year::numeric end as decimal(15,2)) as \"onlineRefundAmountUp\"," +

        "online_refund_count as \"onlineRefundCounts\"," +
        "cast(case when online_refund_count_last_year =0 and online_refund_count>0 then 1 when online_refund_count_last_year=0 and online_refund_count=0 then 0 else (online_refund_count-online_refund_count_last_year)::numeric/online_refund_count_last_year::numeric end as decimal(15,2)) as \"onlineRefundCountsUp\"," +

        "cast(offline_actual_amount as decimal(15,2)) as \"cashAmount\"," +        //现金总收入
        "cast(case when offline_actual_amount_last_year =0 and offline_actual_amount>0 then 1 when offline_actual_amount_last_year=0 and offline_actual_amount=0 then 0 else (offline_actual_amount-offline_actual_amount_last_year)::numeric/offline_actual_amount_last_year::numeric end as decimal(15,2)) as \"cashAmountUp\"," +

        "cast(offline_pay_amount as decimal(15,2)) as \"cashPayAmount\"," +        //现金收款
        "cast(case when offline_pay_amount_last_year =0 and offline_pay_amount>0 then 1 when offline_pay_amount_last_year=0 and offline_pay_amount=0 then 0 else (offline_pay_amount-offline_pay_amount_last_year)::numeric/offline_pay_amount_last_year::numeric end as decimal(15,2)) as \"cashPayAmountUp\"," +

        "cast(offline_pay_count as decimal(15,2)) as \"cashPayCounts\"," +        //现金收款笔数
        "cast(case when offline_pay_count_last_year =0 and offline_pay_count>0 then 1 when offline_pay_count_last_year=0 and offline_pay_count=0 then 0 else (offline_pay_count-offline_pay_count_last_year)::numeric/offline_pay_count_last_year::numeric end as decimal(15,2)) as \"cashPayCountsUp\"," +

        "0 as \"cashRefundAmount\", 0 as \"cashRefundAmountUp\", 0 as \"cashRefundCounts\", 0 as \"cashRefundCountsUp\"," +       //现金没有退款

        "cast(pay_amount as decimal(15,2)) as \"payAmount\"," +
        "cast(case when pay_amount_last_year =0 and pay_amount>0 then 1 when pay_amount_last_year=0 and pay_amount=0 then 0 else (pay_amount-pay_amount_last_year)::numeric/pay_amount_last_year::numeric end as decimal(15,2)) as \"payAmountUp\"," +

        "pay_count as \"payCounts\"," +
        "cast(case when pay_count_last_year =0 and pay_count>0 then 1 when pay_count_last_year=0 and pay_count=0 then 0 else (pay_count-pay_count_last_year)::numeric/pay_count_last_year::numeric end as decimal(15,2)) as \"payCountsUp\"," +

        "cast(refund_amount as decimal(15,2)) as \"refundAmount\"," +
        "cast(case when refund_amount_last_year =0 and refund_amount>0 then 1 when refund_amount_last_year=0 and refund_amount=0 then 0 else (refund_amount-refund_amount_last_year)::numeric/refund_amount_last_year::numeric end as decimal(15,2)) as \"refundAmountUp\"," +

        "refund_count as \"refundCounts\"," +
        "cast(case when refund_count_last_year =0 and refund_count>0 then 1 when refund_count_last_year=0 and refund_count=0 then 0 else (refund_count-refund_count_last_year)::numeric/refund_count_last_year::numeric end as decimal(15,2)) as \"refundCountsUp\"" ;
    
   public static final String DAY_PER_ORDER_COLUMN_ARRAY = 
	    "merchant_id as \"merchantId\", " +
        "cast(pay_amount-refund_amount as decimal(15,2)) as \"payAmount\"," +
        "day as \"day\" " ;
		   
   public static final String WEEK_PER_ORDER_COLUMN_ARRAY = 
	    "merchant_id as \"merchantId\", " +
        "cast(pay_amount-refund_amount as decimal(15,2)) as \"payAmount\"," +
        "month_week_num as \"week\" " ;
   
   public static final String MONTH_PER_ORDER_COLUMN_ARRAY = 
	    "merchant_id as \"merchantId\", " +
        "cast(pay_amount-refund_amount as decimal(15,2)) as \"payAmount\"," +
        "year_month as \"month\" " ;
   
   public static final String YEAR_PER_ORDER_COLUMN_ARRAY = 
   		"merchant_id as \"merchantId\", " +
   		"cast(pay_amount-refund_amount as decimal(15,2)) as \"payAmount\"," +
        "year_str as \"year\" " ;
	   
   public static final String DAY_PER_ORDER_PRICE_COUNT = 
		"merchant_id as \"merchantId\", " +
		//会员总数
		"pay_0_1_count , " +
		"pay_1_3_count , " +
		"pay_3_5_count , " +
		"pay_5_10_count , " +
		"pay_10_15_count , " +
		"pay_15_30_count , " +
		"pay_30_50_count , " +
		"pay_50_100_count , " +
		"pay_100_200_count , " +
		"pay_200_500_count , " +
		"pay_500_1000_count , " +
		"over_1000_count ,  " +
		"pay_0_1_amount , " +
		"pay_1_3_amount , " +
		"pay_3_5_amount , " +
		"pay_5_10_amount , " +
		"pay_10_15_amount , " +
		"pay_15_30_amount , " +
		"pay_30_50_amount , " +
		"pay_50_100_amount , " +
		"pay_100_200_amount , " +
		"pay_200_500_amount , " +
		"pay_500_1000_amount , " +
		"over_1000_amount ,  " +
		"cast(case when pay_0_1_count=0 then 0 else pay_0_1_amount::numeric/pay_0_1_count::numeric end  as decimal(15,2)) as \"payOrderPrice0_1\" , " +
		"cast(case when pay_1_3_count=0 then 0 else pay_1_3_amount::numeric/pay_1_3_count::numeric end  as decimal(15,2)) as \"payOrderPrice1_3\" , " +
		"cast(case when pay_3_5_count=0 then 0 else pay_3_5_amount::numeric/pay_3_5_count::numeric end  as decimal(15,2)) as \"payOrderPrice3_5\" , " +
		"cast(case when pay_5_10_count=0 then 0 else pay_5_10_amount::numeric/pay_5_10_count::numeric end  as decimal(15,2)) as \"payOrderPrice5_10\" , " +
		"cast(case when pay_10_15_count=0 then 0 else pay_10_15_amount::numeric/pay_10_15_count::numeric end  as decimal(15,2)) as \"payOrderPrice10_15\" , " +
		"cast(case when pay_15_30_count=0 then 0 else pay_15_30_amount::numeric/pay_15_30_count::numeric end  as decimal(15,2)) as \"payOrderPrice15_30\" , " +
		"cast(case when pay_30_50_count=0 then 0 else pay_30_50_amount::numeric/pay_30_50_count::numeric end  as decimal(15,2)) as \"payOrderPrice30_50\" , " +
		"cast(case when pay_50_100_count=0 then 0 else pay_50_100_amount::numeric/pay_50_100_count::numeric end  as decimal(15,2)) as \"payOrderPrice50_100\" , " +
		"cast(case when pay_100_200_count=0 then 0 else pay_100_200_amount::numeric/pay_100_200_count::numeric end  as decimal(15,2)) as \"payOrderPrice100_200\" , " +
		"cast(case when pay_200_500_count=0 then 0 else pay_200_500_amount::numeric/pay_200_500_count::numeric end  as decimal(15,2)) as \"payOrderPrice200_500\" , " +
		"cast(case when pay_500_1000_count=0 then 0 else pay_500_1000_amount::numeric/pay_500_1000_count::numeric end  as decimal(15,2)) as \"payOrderPrice500_1000\" , " +
		"cast(case when over_1000_count=0 then 0 else over_1000_amount::numeric/over_1000_count::numeric end  as decimal(15,2)) as \"payOrderPriceOver1000\"  " ;

   public static final String WEEK_PER_ORDER_PRICE_COUNT = 
		"merchant_id as \"merchantId\", " +
		//会员总数
		"pay_0_1_count , " +
		"pay_1_3_count , " +
		"pay_3_5_count , " +
		"pay_5_10_count , " +
		"pay_10_15_count , " +
		"pay_15_30_count , " +
		"pay_30_50_count , " +
		"pay_50_100_count , " +
		"pay_100_200_count , " +
		"pay_200_500_count , " +
		"pay_500_1000_count , " +
		"over_1000_count ,  " +
		"pay_0_1_amount , " +
		"pay_1_3_amount , " +
		"pay_3_5_amount , " +
		"pay_5_10_amount , " +
		"pay_10_15_amount , " +
		"pay_15_30_amount , " +
		"pay_30_50_amount , " +
		"pay_50_100_amount , " +
		"pay_100_200_amount , " +
		"pay_200_500_amount , " +
		"pay_500_1000_amount , " +
		"over_1000_amount ,  " +
		"cast(case when pay_0_1_count=0 then 0 else pay_0_1_amount::numeric/pay_0_1_count::numeric end  as decimal(15,2)) as \"payOrderPrice0_1\" , " +
		"cast(case when pay_1_3_count=0 then 0 else pay_1_3_amount::numeric/pay_1_3_count::numeric end  as decimal(15,2)) as \"payOrderPrice1_3\" , " +
		"cast(case when pay_3_5_count=0 then 0 else pay_3_5_amount::numeric/pay_3_5_count::numeric end  as decimal(15,2)) as \"payOrderPrice3_5\" , " +
		"cast(case when pay_5_10_count=0 then 0 else pay_5_10_amount::numeric/pay_5_10_count::numeric end  as decimal(15,2)) as \"payOrderPrice5_10\" , " +
		"cast(case when pay_10_15_count=0 then 0 else pay_10_15_amount::numeric/pay_10_15_count::numeric end  as decimal(15,2)) as \"payOrderPrice10_15\" , " +
		"cast(case when pay_15_30_count=0 then 0 else pay_15_30_amount::numeric/pay_15_30_count::numeric end  as decimal(15,2)) as \"payOrderPrice15_30\" , " +
		"cast(case when pay_30_50_count=0 then 0 else pay_30_50_amount::numeric/pay_30_50_count::numeric end  as decimal(15,2)) as \"payOrderPrice30_50\" , " +
		"cast(case when pay_50_100_count=0 then 0 else pay_50_100_amount::numeric/pay_50_100_count::numeric end  as decimal(15,2)) as \"payOrderPrice50_100\" , " +
		"cast(case when pay_100_200_count=0 then 0 else pay_100_200_amount::numeric/pay_100_200_count::numeric end  as decimal(15,2)) as \"payOrderPrice100_200\" , " +
		"cast(case when pay_200_500_count=0 then 0 else pay_200_500_amount::numeric/pay_200_500_count::numeric end  as decimal(15,2)) as \"payOrderPrice200_500\" , " +
		"cast(case when pay_500_1000_count=0 then 0 else pay_500_1000_amount::numeric/pay_500_1000_count::numeric end  as decimal(15,2)) as \"payOrderPrice500_1000\" , " +
		"cast(case when over_1000_count=0 then 0 else over_1000_amount::numeric/over_1000_count::numeric end  as decimal(15,2)) as \"payOrderPriceOver1000\"  " ;

   public static final String MONTH_PER_ORDER_PRICE_COUNT = 
		"merchant_id as \"merchantId\", " +
		//会员总数
		"pay_0_1_count , " +
		"pay_1_3_count , " +
		"pay_3_5_count , " +
		"pay_5_10_count , " +
		"pay_10_15_count , " +
		"pay_15_30_count , " +
		"pay_30_50_count , " +
		"pay_50_100_count , " +
		"pay_100_200_count , " +
		"pay_200_500_count , " +
		"pay_500_1000_count , " +
		"over_1000_count ,  " +
		"pay_0_1_amount , " +
		"pay_1_3_amount , " +
		"pay_3_5_amount , " +
		"pay_5_10_amount , " +
		"pay_10_15_amount , " +
		"pay_15_30_amount , " +
		"pay_30_50_amount , " +
		"pay_50_100_amount , " +
		"pay_100_200_amount , " +
		"pay_200_500_amount , " +
		"pay_500_1000_amount , " +
		"over_1000_amount ,  " +
		"cast(case when pay_0_1_count=0 then 0 else pay_0_1_amount::numeric/pay_0_1_count::numeric end  as decimal(15,2)) as \"payOrderPrice0_1\" , " +
		"cast(case when pay_1_3_count=0 then 0 else pay_1_3_amount::numeric/pay_1_3_count::numeric end  as decimal(15,2)) as \"payOrderPrice1_3\" , " +
		"cast(case when pay_3_5_count=0 then 0 else pay_3_5_amount::numeric/pay_3_5_count::numeric end  as decimal(15,2)) as \"payOrderPrice3_5\" , " +
		"cast(case when pay_5_10_count=0 then 0 else pay_5_10_amount::numeric/pay_5_10_count::numeric end  as decimal(15,2)) as \"payOrderPrice5_10\" , " +
		"cast(case when pay_10_15_count=0 then 0 else pay_10_15_amount::numeric/pay_10_15_count::numeric end  as decimal(15,2)) as \"payOrderPrice10_15\" , " +
		"cast(case when pay_15_30_count=0 then 0 else pay_15_30_amount::numeric/pay_15_30_count::numeric end  as decimal(15,2)) as \"payOrderPrice15_30\" , " +
		"cast(case when pay_30_50_count=0 then 0 else pay_30_50_amount::numeric/pay_30_50_count::numeric end  as decimal(15,2)) as \"payOrderPrice30_50\" , " +
		"cast(case when pay_50_100_count=0 then 0 else pay_50_100_amount::numeric/pay_50_100_count::numeric end  as decimal(15,2)) as \"payOrderPrice50_100\" , " +
		"cast(case when pay_100_200_count=0 then 0 else pay_100_200_amount::numeric/pay_100_200_count::numeric end  as decimal(15,2)) as \"payOrderPrice100_200\" , " +
		"cast(case when pay_200_500_count=0 then 0 else pay_200_500_amount::numeric/pay_200_500_count::numeric end  as decimal(15,2)) as \"payOrderPrice200_500\" , " +
		"cast(case when pay_500_1000_count=0 then 0 else pay_500_1000_amount::numeric/pay_500_1000_count::numeric end  as decimal(15,2)) as \"payOrderPrice500_1000\" , " +
		"cast(case when over_1000_count=0 then 0 else over_1000_amount::numeric/over_1000_count::numeric end  as decimal(15,2)) as \"payOrderPriceOver1000\"  " ;
		   
   public static final String YEAR_PER_ORDER_PRICE_COUNT = 
		"merchant_id as \"merchantId\", " +
        //会员总数
        "pay_0_1_count , " +
        "pay_1_3_count , " +
        "pay_3_5_count , " +
        "pay_5_10_count , " +
        "pay_10_15_count , " +
        "pay_15_30_count , " +
        "pay_30_50_count , " +
        "pay_50_100_count , " +
        "pay_100_200_count , " +
        "pay_200_500_count , " +
        "pay_500_1000_count , " +
        "over_1000_count ,  " +
        "pay_0_1_amount , " +
        "pay_1_3_amount , " +
        "pay_3_5_amount , " +
        "pay_5_10_amount , " +
        "pay_10_15_amount , " +
        "pay_15_30_amount , " +
        "pay_30_50_amount , " +
        "pay_50_100_amount , " +
        "pay_100_200_amount , " +
        "pay_200_500_amount , " +
        "pay_500_1000_amount , " +
        "over_1000_amount ,  " +
        "cast(case when pay_0_1_count=0 then 0 else pay_0_1_amount::numeric/pay_0_1_count::numeric end  as decimal(15,2)) as \"payOrderPrice0_1\" , " +
        "cast(case when pay_1_3_count=0 then 0 else pay_1_3_amount::numeric/pay_1_3_count::numeric end  as decimal(15,2)) as \"payOrderPrice1_3\" , " +
        "cast(case when pay_3_5_count=0 then 0 else pay_3_5_amount::numeric/pay_3_5_count::numeric end  as decimal(15,2)) as \"payOrderPrice3_5\" , " +
        "cast(case when pay_5_10_count=0 then 0 else pay_5_10_amount::numeric/pay_5_10_count::numeric end  as decimal(15,2)) as \"payOrderPrice5_10\" , " +
        "cast(case when pay_10_15_count=0 then 0 else pay_10_15_amount::numeric/pay_10_15_count::numeric end  as decimal(15,2)) as \"payOrderPrice10_15\" , " +
        "cast(case when pay_15_30_count=0 then 0 else pay_15_30_amount::numeric/pay_15_30_count::numeric end  as decimal(15,2)) as \"payOrderPrice15_30\" , " +
        "cast(case when pay_30_50_count=0 then 0 else pay_30_50_amount::numeric/pay_30_50_count::numeric end  as decimal(15,2)) as \"payOrderPrice30_50\" , " +
        "cast(case when pay_50_100_count=0 then 0 else pay_50_100_amount::numeric/pay_50_100_count::numeric end  as decimal(15,2)) as \"payOrderPrice50_100\" , " +
        "cast(case when pay_100_200_count=0 then 0 else pay_100_200_amount::numeric/pay_100_200_count::numeric end  as decimal(15,2)) as \"payOrderPrice100_200\" , " +
        "cast(case when pay_200_500_count=0 then 0 else pay_200_500_amount::numeric/pay_200_500_count::numeric end  as decimal(15,2)) as \"payOrderPrice200_500\" , " +
        "cast(case when pay_500_1000_count=0 then 0 else pay_500_1000_amount::numeric/pay_500_1000_count::numeric end  as decimal(15,2)) as \"payOrderPrice500_1000\" , " +
        "cast(case when over_1000_count=0 then 0 else over_1000_amount::numeric/over_1000_count::numeric end  as decimal(15,2)) as \"payOrderPriceOver1000\"  " ;
   
   //订单日表查询数据
    public static final String EveryDayOrderQuerySql = 
    	"merchant_id as \"merchantId\", " +
        "province_id as \"provinceId\", " +
        "city_id as \"cityId\", " +
        "district_id as \"districtId\", " +
        "lyy_equipment_type_id as \"equipmentTypeId\", " +
        "equipment_type_name as \"equipmentTypeName\", " +
        "equipment_group_id as \"groupId\", " +
        "equipment_group_name as \"groupName\", " +
        "equipment_group_address as \"address\", " +

        "cast(pay_amount as decimal(15,2)) as \"payAmount\"," +
        "cast(case when pay_amount_yesterday =0 and pay_amount>0 then 1 " +
        "when pay_amount_yesterday=0 and pay_amount=0 then 0 " +
        "when pay_amount_yesterday>0 and pay_amount=0 then -1 " +
        "else (pay_amount-pay_amount_yesterday)::numeric/pay_amount_yesterday::numeric end as decimal(15,2)) as \"payAmountUp\"," +

        "pay_count as \"payCounts\"," +
        "cast(case when pay_count_yesterday =0 and pay_count>0 then 1 " +
        "when pay_count_yesterday=0 and pay_count=0 then 0 " +
        "when pay_count_yesterday>0 and pay_count=0 then -1 " +
        "else (pay_count-pay_count_yesterday)::numeric/pay_count_yesterday::numeric end as decimal(15,2)) as \"payCountsUp\"," +

        "cast(case when pay_count=0 then 0 else pay_amount::numeric/pay_count::numeric end as decimal(15,2)) as \"perOrderAmount\"," +        //订单均价
        "case when (pay_count =0 or pay_amount=0) and (pay_count_yesterday = 0 or pay_amount_yesterday=0) then 0 " +
        "when (pay_count =0 or pay_amount=0) and  pay_amount_yesterday > 0 then -1 " +
        "when (pay_count_yesterday = 0 or pay_amount_yesterday=0) and pay_amount > 0 then 1 " +
        "else cast( ((pay_amount::numeric/pay_count::numeric) - (pay_amount_yesterday::numeric/pay_count_yesterday::numeric))::numeric/(pay_amount_yesterday::numeric/pay_count_yesterday::numeric)::numeric as decimal(15,2)) end  as \"perOrderAmountUp\","+

        "day " ;


    //订单日表查询数据
    public static final String EveryWeekOrderQuerySql = 
    	"merchant_id as \"merchantId\", " +
        "province_id as \"provinceId\", " +
        "city_id as \"cityId\", " +
        "district_id as \"districtId\", " +
        "lyy_equipment_type_id as \"equipmentTypeId\", " +
        "equipment_type_name as \"equipmentTypeName\", " +
        "equipment_group_id as \"groupId\", " +
        "equipment_group_name as \"groupName\", " +
        "equipment_group_address as \"address\", " +


        "cast(pay_amount as decimal(15,2)) as \"payAmount\"," +
        "cast(case when pay_amount_last_week =0 and pay_amount>0 then 1 " +
        "when pay_amount_last_week=0 and pay_amount=0 then 0 " +
        "when pay_amount_last_week>0 and pay_amount=0 then -1 " +
        "else (pay_amount-pay_amount_last_week)::numeric/pay_amount_last_week::numeric end as decimal(15,2)) as \"payAmountUp\"," +

        "pay_count as \"payCounts\"," +
        "cast(case when pay_count_last_week =0 and pay_count>0 then 1 " +
        "when pay_count_last_week=0 and pay_count=0 then 0 " +
        "when pay_count_last_week>0 and pay_count=0 then -1 " +
        "else (pay_count-pay_count_last_week)::numeric/pay_count_last_week::numeric end as decimal(15,2)) as \"payCountsUp\"," +

        "cast(case when pay_count=0 then 0 else pay_amount::numeric/pay_count::numeric end as decimal(15,2)) as \"perOrderAmount\"," +        //订单均价
        "case when (pay_count =0 or pay_amount=0) and (pay_count_last_week = 0 or pay_amount_last_week=0) then 0 " +
        "when (pay_count =0 or pay_amount=0) and  pay_amount_last_week > 0 then -1 " +
        "when (pay_count_last_week = 0 or pay_amount_last_week=0) and pay_amount > 0 then 1 " +
        "else cast( ((pay_amount::numeric/pay_count::numeric) - (pay_amount_last_week::numeric/pay_count_last_week::numeric))::numeric/(pay_amount_last_week::numeric/pay_count_last_week::numeric)::numeric as decimal(15,2)) end  as \"perOrderAmountUp\","+
        " month_week_num as week "
        ;

    //订单日表查询数据
    public static final String EveryMonthOrderQuerySql = 
    	"merchant_id as \"merchantId\", " +
        "province_id as \"provinceId\", " +
        "city_id as \"cityId\", " +
        "district_id as \"districtId\", " +
        "lyy_equipment_type_id as \"equipmentTypeId\", " +
        "equipment_type_name as \"equipmentTypeName\", " +
        "equipment_group_id as \"groupId\", " +
        "equipment_group_name as \"groupName\", " +
        "equipment_group_address as \"address\", " +

        "cast(pay_amount as decimal(15,2)) as \"payAmount\"," +
        "cast(case when pay_amount_last_month =0 and pay_amount>0 then 1 " +
        "when pay_amount_last_month=0 and pay_amount=0 then 0 " +
        "when pay_amount_last_month>0 and pay_amount=0 then -1 " +
        "else (pay_amount-pay_amount_last_month)::numeric/pay_amount_last_month::numeric end as decimal(15,2)) as \"payAmountUp\"," +

        "pay_count as \"payCounts\"," +
        "cast(case when pay_count_last_month =0 and pay_count>0 then 1 " +
        "when pay_count_last_month=0 and pay_count=0 then 0 " +
        "when pay_count_last_month>0 and pay_count=0 then -1 " +
        "else (pay_count-pay_count_last_month)::numeric/pay_count_last_month::numeric end as decimal(15,2)) as \"payCountsUp\"," +

        "cast(case when pay_count=0 then 0 else pay_amount::numeric/pay_count::numeric end as decimal(15,2)) as \"perOrderAmount\"," +        //订单均价
        "case when (pay_count =0 or pay_amount=0) and (pay_count_last_month = 0 or pay_amount_last_month=0) then 0 " +
        "when (pay_count =0 or pay_amount=0) and  pay_amount_last_month > 0 then -1 " +
        "when (pay_count_last_month = 0 or pay_amount_last_month=0) and pay_amount > 0 then 1 " +
        "else cast( ((pay_amount::numeric/pay_count::numeric) - (pay_amount_last_month::numeric/pay_count_last_month::numeric))::numeric/(pay_amount_last_month::numeric/pay_count_last_month::numeric)::numeric as decimal(15,2)) end  as \"perOrderAmountUp\","+

        "year_month as month"
        ;

    //订单日表查询数据
    public static final String EveryYearOrderQuerySql = 
    	"merchant_id as \"merchantId\", " +
        "province_id as \"provinceId\", " +
        "city_id as \"cityId\", " +
        "district_id as \"districtId\", " +
        "lyy_equipment_type_id as \"equipmentTypeId\", " +
        "equipment_type_name as \"equipmentTypeName\", " +
        "equipment_group_id as \"groupId\", " +
        "equipment_group_name as \"groupName\", " +
        "equipment_group_address as \"address\", " +

        "cast(pay_amount as decimal(15,2)) as \"payAmount\"," +
        "cast(case when pay_amount_last_year =0 and pay_amount>0 then 1 " +
        "when pay_amount_last_year=0 and pay_amount=0 then 0 " +
        "when pay_amount_last_year>0 and pay_amount=0 then -1 " +
        "else (pay_amount-pay_amount_last_year)::numeric/pay_amount_last_year::numeric end as decimal(15,2)) as \"payAmountUp\"," +

        "pay_count as \"payCounts\"," +
        "cast(case when pay_count_last_year =0 and pay_count>0 then 1 " +
        "when pay_count_last_year=0 and pay_count=0 then 0 " +
        "when pay_count_last_year>0 and pay_count=0 then -1 " +
        "else (pay_count-pay_count_last_year)::numeric/pay_count_last_year::numeric end as decimal(15,2)) as \"payCountsUp\"," +

        "cast(case when pay_count=0 then 0 else pay_amount::numeric/pay_count::numeric end as decimal(15,2)) as \"perOrderAmount\"," +        //订单均价
        "case when (pay_count =0 or pay_amount=0) and (pay_count_last_year = 0 or pay_amount_last_year=0) then 0 " +
        "when (pay_count =0 or pay_amount=0) and  pay_count_last_year > 0 then -1 " +
        "when (pay_count_last_year = 0 or pay_amount_last_year=0) and pay_amount > 0 then 1 " +
        "else cast( ((pay_amount::numeric/pay_count::numeric) - (pay_amount_last_year::numeric/pay_count_last_year::numeric))::numeric/(pay_amount_last_year::numeric/pay_count_last_year::numeric)::numeric as decimal(15,2)) end  as \"perOrderAmountUp\","+

        "year_str as year"
        ;
        
   
    
  //订单日表查询数据
    public static final String DayOrderQuerySql = 
    	"merchant_id as \"merchantId\", " +
        "province_id as \"provinceId\", " +
        "city_id as \"cityId\", " +
        "district_id as \"districtId\", " +
        "equipment_group_id as \"groupId\", " +
        "equipment_group_name as \"groupName\", " +
        "equipment_group_address as \"address\", " +
        "cast(online_actual_amount as decimal(15,2)) as \"onlineAmount\"," +
        "cast(case when online_actual_amount_yesterday =0 and online_actual_amount>0 then 1 " +
        "when online_actual_amount_yesterday=0 and online_actual_amount=0 then 0 " +
        "when online_actual_amount_yesterday>0 and online_actual_amount=0 then -1 " +
        "else (online_actual_amount-online_actual_amount_yesterday)::numeric/online_actual_amount_yesterday::numeric end as decimal(15,2)) as \"onlineAmountUp\"," +

        "cast(online_pay_amount as decimal(15,2)) as \"onlinePayAmount\", " +
        "cast(online_pay_amount_yesterday as decimal(15,2)) as \"lastCycleOnlinePayAmount\", " +
        "cast(case when online_pay_amount_yesterday =0 and online_pay_amount>0 then 1 " +
        "when online_pay_amount_yesterday=0 and online_pay_amount=0 then 0 " +
        "when online_pay_amount_yesterday>0 and online_pay_amount=0 then -1 " +
        "else (online_pay_amount-online_pay_amount_yesterday)::numeric/online_pay_amount_yesterday::numeric end as decimal(15,2)) as \"onlinePayAmountUp\"," +

        "online_pay_count as \"onlinePayCounts\", " +
        "cast(case when online_pay_count_yesterday =0 and online_pay_count>0 then 1 " +
        "when online_pay_count=0 and online_pay_count_yesterday=0 then 0 " +
        "when online_pay_count=0 and online_pay_count_yesterday>0 then -1 " +
        "else (online_pay_count-online_pay_count_yesterday)::numeric/online_pay_count_yesterday::numeric end as decimal(15,2)) as \"onlinePayCountsUp\"," +

        "cast(online_refund_amount as decimal(15,2)) as \"onlineRefundAmount\", " +
        "cast(case when online_refund_amount_yesterday =0 and online_refund_amount>0 then 1 " +
        "when online_refund_amount_yesterday=0 and online_refund_amount=0 then 0 " +
        "when online_refund_amount_yesterday>0 and online_refund_amount=0 then -1 " +
        "else (online_refund_amount-online_refund_amount_yesterday)::numeric/online_refund_amount_yesterday::numeric end as decimal(15,2)) as \"onlineRefundAmountUp\"," +

        "online_refund_count as \"onlineRefundCounts\"," +
        "cast(case when online_refund_count_yesterday =0 and online_refund_count>0 then 1 " +
        "when online_refund_count_yesterday=0 and online_refund_count=0 then 0 " +
        "when online_refund_count_yesterday>0 and online_refund_count=0 then -1 " +
        "else (online_refund_count-online_refund_count_yesterday)::numeric/online_refund_count_yesterday::numeric end as decimal(15,2)) as \"onlineRefundCountsUp\"," +

        "cast(offline_actual_amount as decimal(15,2)) as \"cashAmount\"," +        //现金总收入
        "cast(case when offline_actual_amount_yesterday =0 and offline_actual_amount>0 then 1 " +
        "when offline_actual_amount_yesterday=0 and offline_actual_amount=0 then 0 " +
        "when offline_actual_amount_yesterday>0 and offline_actual_amount=0 then -1 " +
        "else (offline_actual_amount-offline_actual_amount_yesterday)::numeric/offline_actual_amount_yesterday::numeric end as decimal(15,2)) as \"cashAmountUp\"," +

        "cast(offline_pay_amount as decimal(15,2)) as \"cashPayAmount\"," +        //现金收款
        "cast(case when offline_pay_amount_yesterday =0 and offline_pay_amount>0 then 1 " +
        "when offline_pay_amount_yesterday=0 and offline_pay_amount=0 then 0 " +
        "when offline_pay_amount_yesterday>0 and offline_pay_amount=0 then -1 " +
        "else (offline_pay_amount-offline_pay_amount_yesterday)::numeric/offline_pay_amount_yesterday::numeric end as decimal(15,2)) as \"cashPayAmountUp\"," +

        "cast(offline_pay_count as decimal(15,2)) as \"cashPayCounts\"," +        //现金收款笔数
        "cast(case when offline_pay_count_yesterday =0 and offline_pay_count>0 then 1 " +
        "when offline_pay_count_yesterday=0 and offline_pay_count=0 then 0 " +
        "when offline_pay_count_yesterday>0 and offline_pay_count=0 then -1 " +
        "else (offline_pay_count-offline_pay_count_yesterday)::numeric/offline_pay_count_yesterday::numeric end as decimal(15,2)) as \"cashPayCountsUp\"," +

        "0 as \"cashRefundAmount\", 0 as \"cashRefundAmountUp\", 0 as \"cashRefundCounts\", 0 as \"cashRefundCountsUp\"," +       //现金没有退款

        "cast(pay_amount as decimal(15,2)) as \"payAmount\"," +
        "cast(case when pay_amount_yesterday =0 and pay_amount>0 then 1 " +
        "when pay_amount_yesterday=0 and pay_amount=0 then 0 " +
        "when pay_amount_yesterday>0 and pay_amount=0 then -1 " +
        "else (pay_amount-pay_amount_yesterday)::numeric/pay_amount_yesterday::numeric end as decimal(15,2)) as \"payAmountUp\"," +

        "pay_count as \"payCounts\"," +
        "cast(case when pay_count_yesterday =0 and pay_count>0 then 1 " +
        "when pay_count_yesterday=0 and pay_count=0 then 0 " +
        "when pay_count_yesterday>0 and pay_count=0 then -1 " +
        "else (pay_count-pay_count_yesterday)::numeric/pay_count_yesterday::numeric end as decimal(15,2)) as \"payCountsUp\"," +

        "cast(case when pay_count=0 then 0 else pay_amount::numeric/pay_count::numeric end as decimal(15,2)) as \"perOrderAmount\"," +        //订单均价
        "case when (pay_count =0 or pay_amount=0) and (pay_count_yesterday = 0 or pay_amount_yesterday=0) then 0 " +
        "when (pay_count =0 or pay_amount=0) and  pay_amount_yesterday > 0 then -1 " +
        "when (pay_count_yesterday = 0 or pay_amount_yesterday=0) and pay_amount > 0 then 1 " +
        "else cast( ((pay_amount::numeric/pay_count::numeric) - (pay_amount_yesterday::numeric/pay_count_yesterday::numeric))::numeric/(pay_amount_yesterday::numeric/pay_count_yesterday::numeric)::numeric as decimal(15,2)) end  as \"perOrderAmountUp\","+


        "cast(case when online_pay_count=0 then 0 else online_pay_amount::numeric/online_pay_count::numeric end as decimal(15,2)) as \"perOnlineOrderAmount\"," +        //订单均价
        "case when (online_pay_count =0 or online_pay_amount=0) and (online_pay_count_yesterday = 0 or online_pay_amount_yesterday=0) then 0 " +
        "when (online_pay_count =0 or online_pay_amount=0) and  online_pay_amount_yesterday > 0 then -1 " +
        "when (online_pay_count_yesterday = 0 or online_pay_amount_yesterday=0) and online_pay_amount > 0 then 1 " +
        "else cast( ((online_pay_amount::numeric/online_pay_count::numeric) - (online_pay_amount_yesterday::numeric/online_pay_count_yesterday::numeric))::numeric/(online_pay_amount_yesterday::numeric/online_pay_count_yesterday::numeric)::numeric as decimal(15,2)) end  as \"perOnlineOrderAmountUp\","+


        "cast(refund_amount as decimal(15,2)) as \"refundAmount\"," +
        "cast(case when refund_amount_yesterday =0 and refund_amount>0 then 1 " +
        "when refund_amount_yesterday=0 and refund_amount=0 then 0 " +
        "when refund_amount_yesterday>0 and refund_amount=0 then -1 " +
        "else (refund_amount-refund_amount_yesterday)::numeric/refund_amount_yesterday::numeric end as decimal(15,2)) as \"refundAmountUp\"," +

        "refund_count as \"refundCounts\"," +
        "cast(case when refund_count_yesterday =0 and refund_count>0 then 1 " +
        "when refund_count_yesterday=0 and refund_count=0 then 0 " +
        "when refund_count_yesterday>0 and refund_count=0 then -1 " +
        "else (refund_count-refund_count_yesterday)::numeric/refund_count_yesterday::numeric end as decimal(15,2)) as \"refundCountsUp\", " +

        "day " ;


    //订单日表查询数据
    public static final String WeekOrderQuerySql = 
    	"merchant_id as \"merchantId\", " +
        "province_id as \"provinceId\", " +
        "city_id as \"cityId\", " +
        "district_id as \"districtId\", " +
        "equipment_group_id as \"groupId\", " +
        "equipment_group_name as \"groupName\", " +
        "equipment_group_address as \"address\", " +
        "cast(online_actual_amount as decimal(15,2)) as \"onlineAmount\"," +
        "cast(case when online_actual_amount_last_week =0 and online_actual_amount>0 then 1 " +
        "when online_actual_amount_last_week=0 and online_actual_amount=0 then 0 " +
        "when online_actual_amount_last_week>0 and online_actual_amount=0 then -1 " +
        "else (online_actual_amount-online_actual_amount_last_week)::numeric/online_actual_amount_last_week::numeric end as decimal(15,2)) as \"onlineAmountUp\"," +

        "cast(online_pay_amount as decimal(15,2)) as \"onlinePayAmount\", " +
        "cast(online_pay_amount_last_week as decimal(15,2)) as \"lastCycleOnlinePayAmount\", " +
        "cast(case when online_pay_amount_last_week =0 and online_pay_amount>0 then 1 " +
        "when online_pay_amount_last_week=0 and online_pay_amount=0 then 0 " +
        "when online_pay_amount_last_week>0 and online_pay_amount=0 then -1 " +
        "else (online_pay_amount-online_pay_amount_last_week)::numeric/online_pay_amount_last_week::numeric end as decimal(15,2)) as \"onlinePayAmountUp\"," +

        "online_pay_count as \"onlinePayCounts\", " +
        "cast(case when online_pay_count_last_week =0 and online_pay_count>0 then 1 " +
        "when online_pay_count=0 and online_pay_count_last_week=0 then 0 " +
        "when online_pay_count=0 and online_pay_count_last_week>0 then -1 " +
        "else (online_pay_count-online_pay_count_last_week)::numeric/online_pay_count_last_week::numeric end as decimal(15,2)) as \"onlinePayCountsUp\"," +

        "cast(online_refund_amount as decimal(15,2)) as \"onlineRefundAmount\", " +
        "cast(case when online_refund_amount_last_week =0 and online_refund_amount>0 then 1 " +
        "when online_refund_amount_last_week=0 and online_refund_amount=0 then 0 " +
        "when online_refund_amount_last_week>0 and online_refund_amount=0 then -1 " +
        "else (online_refund_amount-online_refund_amount_last_week)::numeric/online_refund_amount_last_week::numeric end as decimal(15,2)) as \"onlineRefundAmountUp\"," +

        "online_refund_count as \"onlineRefundCounts\"," +
        "cast(case when online_refund_count_last_week =0 and online_refund_count>0 then 1 " +
        "when online_refund_count_last_week=0 and online_refund_count=0 then 0 " +
        "when online_refund_count_last_week>0 and online_refund_count=0 then -1 " +
        "else (online_refund_count-online_refund_count_last_week)::numeric/online_refund_count_last_week::numeric end as decimal(15,2)) as \"onlineRefundCountsUp\"," +

        "cast(offline_actual_amount as decimal(15,2)) as \"cashAmount\"," +        //现金总收入
        "cast(case when offline_actual_amount_last_week =0 and offline_actual_amount>0 then 1 " +
        "when offline_actual_amount_last_week=0 and offline_actual_amount=0 then 0 " +
        "when offline_actual_amount_last_week>0 and offline_actual_amount=0 then -1 " +
        "else (offline_actual_amount-offline_actual_amount_last_week)::numeric/offline_actual_amount_last_week::numeric end as decimal(15,2)) as \"cashAmountUp\"," +

        "cast(offline_pay_amount as decimal(15,2)) as \"cashPayAmount\"," +        //现金收款
        "cast(case when offline_pay_amount_last_week =0 and offline_pay_amount>0 then 1 " +
        "when offline_pay_amount_last_week=0 and offline_pay_amount=0 then 0 " +
        "when offline_pay_amount_last_week>0 and offline_pay_amount=0 then -1 " +
        "else (offline_pay_amount-offline_pay_amount_last_week)::numeric/offline_pay_amount_last_week::numeric end as decimal(15,2)) as \"cashPayAmountUp\"," +

        "cast(offline_pay_count as decimal(15,2)) as \"cashPayCounts\"," +        //现金收款笔数
        "cast(case when offline_pay_count_last_week =0 and offline_pay_count>0 then 1 " +
        "when offline_pay_count_last_week=0 and offline_pay_count=0 then 0 " +
        "when offline_pay_count_last_week>0 and offline_pay_count=0 then -1 " +
        "else (offline_pay_count-offline_pay_count_last_week)::numeric/offline_pay_count_last_week::numeric end as decimal(15,2)) as \"cashPayCountsUp\"," +

        "0 as \"cashRefundAmount\", 0 as \"cashRefundAmountUp\", 0 as \"cashRefundCounts\", 0 as \"cashRefundCountsUp\"," +       //现金没有退款

        "cast(pay_amount as decimal(15,2)) as \"payAmount\"," +
        "cast(case when pay_amount_last_week =0 and pay_amount>0 then 1 " +
        "when pay_amount_last_week=0 and pay_amount=0 then 0 " +
        "when pay_amount_last_week>0 and pay_amount=0 then -1 " +
        "else (pay_amount-pay_amount_last_week)::numeric/pay_amount_last_week::numeric end as decimal(15,2)) as \"payAmountUp\"," +

        "pay_count as \"payCounts\"," +
        "cast(case when pay_count_last_week =0 and pay_count>0 then 1 " +
        "when pay_count_last_week=0 and pay_count=0 then 0 " +
        "when pay_count_last_week>0 and pay_count=0 then -1 " +
        "else (pay_count-pay_count_last_week)::numeric/pay_count_last_week::numeric end as decimal(15,2)) as \"payCountsUp\"," +

        "cast(case when pay_count=0 then 0 else pay_amount::numeric/pay_count::numeric end as decimal(15,2)) as \"perOrderAmount\"," +        //订单均价
        "case when (pay_count =0 or pay_amount=0) and (pay_count_last_week = 0 or pay_amount_last_week=0) then 0 " +
        "when (pay_count =0 or pay_amount=0) and  pay_amount_last_week > 0 then -1 " +
        "when (pay_count_last_week = 0 or pay_amount_last_week=0) and pay_amount > 0 then 1 " +
        "else cast( ((pay_amount::numeric/pay_count::numeric) - (pay_amount_last_week::numeric/pay_count_last_week::numeric))::numeric/(pay_amount_last_week::numeric/pay_count_last_week::numeric)::numeric as decimal(15,2)) end  as \"perOrderAmountUp\","+

        "cast(case when online_pay_count=0 then 0 else online_pay_amount::numeric/online_pay_count::numeric end as decimal(15,2)) as \"perOnlineOrderAmount\"," +        //订单均价
        "case when (online_pay_count =0 or online_pay_amount=0) and (online_pay_count_last_week = 0 or online_pay_amount_last_week=0) then 0 " +
        "when (online_pay_count =0 or online_pay_amount=0) and  online_pay_amount_last_week > 0 then -1 " +
        "when (online_pay_count_last_week = 0 or online_pay_amount_last_week=0) and online_pay_amount > 0 then 1 " +
        "else cast( ((online_pay_amount::numeric/online_pay_count::numeric) - (online_pay_amount_last_week::numeric/online_pay_count_last_week::numeric))::numeric/(online_pay_amount_last_week::numeric/online_pay_count_last_week::numeric)::numeric as decimal(15,2)) end  as \"perOnlineOrderAmountUp\","+



        "cast(refund_amount as decimal(15,2)) as \"refundAmount\"," +
        "cast(case when refund_amount_last_week =0 and refund_amount>0 then 1 " +
        "when refund_amount_last_week=0 and refund_amount=0 then 0 " +
        "when refund_amount_last_week>0 and refund_amount=0 then -1 " +
        "else (refund_amount-refund_amount_last_week)::numeric/refund_amount_last_week::numeric end as decimal(15,2)) as \"refundAmountUp\"," +

        "refund_count as \"refundCounts\"," +
        "cast(case when refund_count_last_week =0 and refund_count>0 then 1 " +
        "when refund_count_last_week=0 and refund_count=0 then 0 " +
        "when refund_count_last_week>0 and refund_count=0 then -1 " +
        "else (refund_count-refund_count_last_week)::numeric/refund_count_last_week::numeric end as decimal(15,2)) as \"refundCountsUp\""
        ;

    //订单日表查询数据
    public static final String MonthOrderQuerySql = 
    	"merchant_id as \"merchantId\", " +
        "province_id as \"provinceId\", " +
        "city_id as \"cityId\", " +
        "district_id as \"districtId\", " +
        "equipment_group_id as \"groupId\", " +
        "equipment_group_name as \"groupName\", " +
        "equipment_group_address as \"address\", " +
        "cast(online_actual_amount as decimal(15,2)) as \"onlineAmount\"," +
        "cast(case when online_actual_amount_last_month =0 and online_actual_amount>0 then 1 " +
        "when online_actual_amount_last_month=0 and online_actual_amount=0 then 0 " +
        "when online_actual_amount_last_month>0 and online_actual_amount=0 then -1 " +
        "else (online_actual_amount-online_actual_amount_last_month)::numeric/online_actual_amount_last_month::numeric end as decimal(15,2)) as \"onlineAmountUp\"," +

        "cast(online_pay_amount as decimal(15,2)) as \"onlinePayAmount\", " +
        "cast(online_pay_amount_last_month as decimal(15,2)) as \"lastCycleOnlinePayAmount\", " +
        "cast(case when online_pay_amount_last_month =0 and online_pay_amount>0 then 1 " +
        "when online_pay_amount_last_month=0 and online_pay_amount=0 then 0 " +
        "when online_pay_amount_last_month>0 and online_pay_amount=0 then -1 " +
        "else (online_pay_amount-online_pay_amount_last_month)::numeric/online_pay_amount_last_month::numeric end as decimal(15,2)) as \"onlinePayAmountUp\"," +

        "online_pay_count as \"onlinePayCounts\", " +
        "cast(case when online_pay_count_last_month =0 and online_pay_count>0 then 1 " +
        "when online_pay_count=0 and online_pay_count_last_month=0 then 0 " +
        "when online_pay_count=0 and online_pay_count_last_month>0 then -1 " +
        "else (online_pay_count-online_pay_count_last_month)::numeric/online_pay_count_last_month::numeric end as decimal(15,2)) as \"onlinePayCountsUp\"," +

        "cast(online_refund_amount as decimal(15,2)) as \"onlineRefundAmount\", " +
        "cast(case when online_refund_amount_last_month =0 and online_refund_amount>0 then 1 " +
        "when online_refund_amount_last_month=0 and online_refund_amount=0 then 0 " +
        "when online_refund_amount_last_month>0 and online_refund_amount=0 then -1 " +
        "else (online_refund_amount-online_refund_amount_last_month)::numeric/online_refund_amount_last_month::numeric end as decimal(15,2)) as \"onlineRefundAmountUp\"," +

        "online_refund_count as \"onlineRefundCounts\"," +
        "cast(case when online_refund_count_last_month =0 and online_refund_count>0 then 1 " +
        "when online_refund_count_last_month=0 and online_refund_count=0 then 0 " +
        "when online_refund_count_last_month>0 and online_refund_count=0 then -1 " +
        "else (online_refund_count-online_refund_count_last_month)::numeric/online_refund_count_last_month::numeric end as decimal(15,2)) as \"onlineRefundCountsUp\"," +

        "cast(offline_actual_amount as decimal(15,2)) as \"cashAmount\"," +        //现金总收入
        "cast(case when offline_actual_amount_last_month =0 and offline_actual_amount>0 then 1 " +
        "when offline_actual_amount_last_month=0 and offline_actual_amount=0 then 0 " +
        "when offline_actual_amount_last_month>0 and offline_actual_amount=0 then -1 " +
        "else (offline_actual_amount-offline_actual_amount_last_month)::numeric/offline_actual_amount_last_month::numeric end as decimal(15,2)) as \"cashAmountUp\"," +

        "cast(offline_pay_amount as decimal(15,2)) as \"cashPayAmount\"," +        //现金收款
        "cast(case when offline_pay_amount_last_month =0 and offline_pay_amount>0 then 1 " +
        "when offline_pay_amount_last_month=0 and offline_pay_amount=0 then 0 " +
        "when offline_pay_amount_last_month>0 and offline_pay_amount=0 then -1 " +
        "else (offline_pay_amount-offline_pay_amount_last_month)::numeric/offline_pay_amount_last_month::numeric end as decimal(15,2)) as \"cashPayAmountUp\"," +

        "cast(offline_pay_count as decimal(15,2)) as \"cashPayCounts\"," +        //现金收款笔数
        "cast(case when offline_pay_count_last_month =0 and offline_pay_count>0 then 1 " +
        "when offline_pay_count_last_month=0 and offline_pay_count=0 then 0 " +
        "when offline_pay_count_last_month>0 and offline_pay_count=0 then -1 " +
        "else (offline_pay_count-offline_pay_count_last_month)::numeric/offline_pay_count_last_month::numeric end as decimal(15,2)) as \"cashPayCountsUp\"," +

        "0 as \"cashRefundAmount\", 0 as \"cashRefundAmountUp\", 0 as \"cashRefundCounts\", 0 as \"cashRefundCountsUp\"," +       //现金没有退款

        "cast(pay_amount as decimal(15,2)) as \"payAmount\"," +
        "cast(case when pay_amount_last_month =0 and pay_amount>0 then 1 " +
        "when pay_amount_last_month=0 and pay_amount=0 then 0 " +
        "when pay_amount_last_month>0 and pay_amount=0 then -1 " +
        "else (pay_amount-pay_amount_last_month)::numeric/pay_amount_last_month::numeric end as decimal(15,2)) as \"payAmountUp\"," +

        "pay_count as \"payCounts\"," +
        "cast(case when pay_count_last_month =0 and pay_count>0 then 1 " +
        "when pay_count_last_month=0 and pay_count=0 then 0 " +
        "when pay_count_last_month>0 and pay_count=0 then -1 " +
        "else (pay_count-pay_count_last_month)::numeric/pay_count_last_month::numeric end as decimal(15,2)) as \"payCountsUp\"," +

        "cast(case when pay_count=0 then 0 else pay_amount::numeric/pay_count::numeric end as decimal(15,2)) as \"perOrderAmount\"," +        //订单均价
        "case when (pay_count =0 or pay_amount=0) and (pay_count_last_month = 0 or pay_amount_last_month=0) then 0 " +
        "when (pay_count =0 or pay_amount=0) and  pay_amount_last_month > 0 then -1 " +
        "when (pay_count_last_month = 0 or pay_amount_last_month=0) and pay_amount > 0 then 1 " +
        "else cast( ((pay_amount::numeric/pay_count::numeric) - (pay_amount_last_month::numeric/pay_count_last_month::numeric))::numeric/(pay_amount_last_month::numeric/pay_count_last_month::numeric)::numeric as decimal(15,2)) end  as \"perOrderAmountUp\","+

        "cast(case when online_pay_count=0 then 0 else online_pay_amount::numeric/online_pay_count::numeric end as decimal(15,2)) as \"perOnlineOrderAmount\"," +        //订单均价
        "case when (online_pay_count =0 or online_pay_amount=0) and (online_pay_count_last_month = 0 or online_pay_amount_last_month=0) then 0 " +
        "when (online_pay_count =0 or online_pay_amount=0) and  online_pay_amount_last_month > 0 then -1 " +
        "when (online_pay_count_last_month = 0 or online_pay_amount_last_month=0) and online_pay_amount > 0 then 1 " +
        "else cast( ((online_pay_amount::numeric/online_pay_count::numeric) - (online_pay_amount_last_month::numeric/online_pay_count_last_month::numeric))::numeric/(online_pay_amount_last_month::numeric/online_pay_count_last_month::numeric)::numeric as decimal(15,2)) end  as \"perOnlineOrderAmountUp\","+


        "cast(refund_amount as decimal(15,2)) as \"refundAmount\"," +
        "cast(case when refund_amount_last_month =0 and refund_amount>0 then 1 " +
        "when refund_amount_last_month=0 and refund_amount=0 then 0 " +
        "when refund_amount_last_month>0 and refund_amount=0 then -1 " +
        "else (refund_amount-refund_amount_last_month)::numeric/refund_amount_last_month::numeric end as decimal(15,2)) as \"refundAmountUp\"," +

        "refund_count as \"refundCounts\"," +
        "cast(case when refund_count_last_month =0 and refund_count>0 then 1 " +
        "when refund_count_last_month=0 and refund_count=0 then 0 " +
        "when refund_count_last_month>0 and refund_count=0 then -1 " +
        "else (refund_count-refund_count_last_month)::numeric/refund_count_last_month::numeric end as decimal(15,2)) as \"refundCountsUp\""
        ;

    //订单日表查询数据
    public static final String YearOrderQuerySql = 
    	"merchant_id as \"merchantId\", " +
        "province_id as \"provinceId\", " +
        "city_id as \"cityId\", " +
        "district_id as \"districtId\", " +
        "equipment_group_id as \"groupId\", " +
        "equipment_group_name as \"groupName\", " +
        "equipment_group_address as \"address\", " +
        "cast(online_actual_amount as decimal(15,2)) as \"onlineAmount\"," +
        "cast(case when online_actual_amount_last_year =0 and online_actual_amount>0 then 1 " +
        "when online_actual_amount_last_year=0 and online_actual_amount=0 then 0 " +
        "when online_actual_amount_last_year>0 and online_actual_amount=0 then -1 " +
        "else (online_actual_amount-online_actual_amount_last_year)::numeric/online_actual_amount_last_year::numeric end as decimal(15,2)) as \"onlineAmountUp\"," +

        "cast(online_pay_amount as decimal(15,2)) as \"onlinePayAmount\", " +
        "cast(online_pay_amount_last_year as decimal(15,2)) as \"lastCycleOnlinePayAmount\", " +
        "cast(case when online_pay_amount_last_year =0 and online_pay_amount>0 then 1 " +
        "when online_pay_amount_last_year=0 and online_pay_amount=0 then 0 " +
        "when online_pay_amount_last_year>0 and online_pay_amount=0 then -1 " +
        "else (online_pay_amount-online_pay_amount_last_year)::numeric/online_pay_amount_last_year::numeric end as decimal(15,2)) as \"onlinePayAmountUp\"," +

        "online_pay_count as \"onlinePayCounts\", " +
        "cast(case when online_pay_count_last_year =0 and online_pay_count>0 then 1 " +
        "when online_pay_count=0 and online_pay_count_last_year=0 then 0 " +
        "when online_pay_count=0 and online_pay_count_last_year>0 then -1 " +
        "else (online_pay_count-online_pay_count_last_year)::numeric/online_pay_count_last_year::numeric end as decimal(15,2)) as \"onlinePayCountsUp\"," +

        "cast(online_refund_amount as decimal(15,2)) as \"onlineRefundAmount\", " +
        "cast(case when online_refund_amount_last_year =0 and online_refund_amount>0 then 1 " +
        "when online_refund_amount_last_year=0 and online_refund_amount=0 then 0 " +
        "when online_refund_amount_last_year>0 and online_refund_amount=0 then -1 " +
        "else (online_refund_amount-online_refund_amount_last_year)::numeric/online_refund_amount_last_year::numeric end as decimal(15,2)) as \"onlineRefundAmountUp\"," +

        "online_refund_count as \"onlineRefundCounts\"," +
        "cast(case when online_refund_count_last_year =0 and online_refund_count>0 then 1 " +
        "when online_refund_count_last_year=0 and online_refund_count=0 then 0 " +
        "when online_refund_count_last_year>0 and online_refund_count=0 then -1 " +
        "else (online_refund_count-online_refund_count_last_year)::numeric/online_refund_count_last_year::numeric end as decimal(15,2)) as \"onlineRefundCountsUp\"," +

        "cast(offline_actual_amount as decimal(15,2)) as \"cashAmount\"," +        //现金总收入
        "cast(case when offline_actual_amount_last_year =0 and offline_actual_amount>0 then 1 " +
        "when offline_actual_amount_last_year=0 and offline_actual_amount=0 then 0 " +
        "when offline_actual_amount_last_year>0 and offline_actual_amount=0 then -1 " +
        "else (offline_actual_amount-offline_actual_amount_last_year)::numeric/offline_actual_amount_last_year::numeric end as decimal(15,2)) as \"cashAmountUp\"," +

        "cast(offline_pay_amount as decimal(15,2)) as \"cashPayAmount\"," +        //现金收款
        "cast(case when offline_pay_amount_last_year =0 and offline_pay_amount>0 then 1 " +
        "when offline_pay_amount_last_year=0 and offline_pay_amount=0 then 0 " +
        "when offline_pay_amount_last_year>0 and offline_pay_amount=0 then -1 " +
        "else (offline_pay_amount-offline_pay_amount_last_year)::numeric/offline_pay_amount_last_year::numeric end as decimal(15,2)) as \"cashPayAmountUp\"," +

        "cast(offline_pay_count as decimal(15,2)) as \"cashPayCounts\"," +        //现金收款笔数
        "cast(case when offline_pay_count_last_year =0 and offline_pay_count>0 then 1 " +
        "when offline_pay_count_last_year=0 and offline_pay_count=0 then 0 " +
        "when offline_pay_count_last_year>0 and offline_pay_count=0 then -1 " +
        "else (offline_pay_count-offline_pay_count_last_year)::numeric/offline_pay_count_last_year::numeric end as decimal(15,2)) as \"cashPayCountsUp\"," +

        "0 as \"cashRefundAmount\", 0 as \"cashRefundAmountUp\", 0 as \"cashRefundCounts\", 0 as \"cashRefundCountsUp\"," +       //现金没有退款

        "cast(pay_amount as decimal(15,2)) as \"payAmount\"," +
        "cast(case when pay_amount_last_year =0 and pay_amount>0 then 1 " +
        "when pay_amount_last_year=0 and pay_amount=0 then 0 " +
        "when pay_amount_last_year>0 and pay_amount=0 then -1 " +
        "else (pay_amount-pay_amount_last_year)::numeric/pay_amount_last_year::numeric end as decimal(15,2)) as \"payAmountUp\"," +

        "pay_count as \"payCounts\"," +
        "cast(case when pay_count_last_year =0 and pay_count>0 then 1 " +
        "when pay_count_last_year=0 and pay_count=0 then 0 " +
        "when pay_count_last_year>0 and pay_count=0 then -1 " +
        "else (pay_count-pay_count_last_year)::numeric/pay_count_last_year::numeric end as decimal(15,2)) as \"payCountsUp\"," +

        "cast(case when pay_count=0 then 0 else pay_amount::numeric/pay_count::numeric end as decimal(15,2)) as \"perOrderAmount\"," +        //订单均价
        "case when (pay_count =0 or pay_amount=0) and (pay_count_last_year = 0 or pay_amount_last_year=0) then 0 " +
        "when (pay_count =0 or pay_amount=0) and  pay_count_last_year > 0 then -1 " +
        "when (pay_count_last_year = 0 or pay_amount_last_year=0) and pay_amount > 0 then 1 " +
        "else cast( ((pay_amount::numeric/pay_count::numeric) - (pay_amount_last_year::numeric/pay_count_last_year::numeric))::numeric/(pay_amount_last_year::numeric/pay_count_last_year::numeric)::numeric as decimal(15,2)) end  as \"perOrderAmountUp\","+

        "cast(case when online_pay_count=0 then 0 else online_pay_amount::numeric/online_pay_count::numeric end as decimal(15,2)) as \"perOnlineOrderAmount\"," +        //订单均价
        "case when (online_pay_count =0 or online_pay_amount=0) and (online_pay_count_last_year = 0 or online_pay_amount_last_year=0) then 0 " +
        "when (online_pay_count =0 or online_pay_amount=0) and  online_pay_amount_last_year > 0 then -1 " +
        "when (online_pay_count_last_year = 0 or online_pay_amount_last_year=0) and online_pay_amount > 0 then 1 " +
        "else cast( ((online_pay_amount::numeric/online_pay_count::numeric) - (online_pay_amount_last_year::numeric/online_pay_count_last_year::numeric))::numeric/(online_pay_amount_last_year::numeric/online_pay_count_last_year::numeric)::numeric as decimal(15,2)) end  as \"perOnlineOrderAmountUp\","+

        "cast(refund_amount as decimal(15,2)) as \"refundAmount\"," +
        "cast(case when refund_amount_last_year =0 and refund_amount>0 then 1 " +
        "when refund_amount_last_year=0 and refund_amount=0 then 0 " +
        "when refund_amount_last_year>0 and refund_amount=0 then -1 " +
        "else (refund_amount-refund_amount_last_year)::numeric/refund_amount_last_year::numeric end as decimal(15,2)) as \"refundAmountUp\"," +

        "refund_count as \"refundCounts\"," +
        "cast(case when refund_count_last_year =0 and refund_count>0 then 1 " +
        "when refund_count_last_year=0 and refund_count=0 then 0 " +
        "when refund_count_last_year>0 and refund_count=0 then -1 " +
        "else (refund_count-refund_count_last_year)::numeric/refund_count_last_year::numeric end as decimal(15,2)) as \"refundCountsUp\""
        ;

//    public static final String OrderCountSql =
//		"sum(coalesce(online_pay_amount,0)) as \"sumPayAmount\", "+
//		"sum(coalesce(online_pay_count,0)) as \"sumPayCount\", "+
//		"cast(case when sum(online_pay_count) =0 then sum(online_pay_amount) else (sum(online_pay_amount))::numeric/sum(online_pay_count)::numeric end as decimal(15,2)) as \"sumPerOrderAmount\" " ;
//    
//	public static final String OrderGroupCountSql =
//        "cast(case when count(distinct equipment_group_id) =0 then sum(total_start_counts)  else (sum(total_start_counts))::numeric/count(distinct equipment_group_id)::numeric end as decimal(15,2)) as \"sumPerGroupStartCounts\", " +
//        "count(distinct equipment_group_id) as \"sumGroup\" " ;
	
}
