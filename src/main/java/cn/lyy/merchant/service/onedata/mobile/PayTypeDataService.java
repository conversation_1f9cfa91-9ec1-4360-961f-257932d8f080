package cn.lyy.merchant.service.onedata.mobile;

import com.alibaba.fastjson.JSONObject;
import cn.lyy.merchant.controller.onedata.dto.DayParamDto;
import cn.lyy.merchant.controller.onedata.dto.MonthParamDto;
import cn.lyy.merchant.controller.onedata.dto.WeekParamDto;
import cn.lyy.merchant.controller.onedata.dto.YearParamDto;

public interface PayTypeDataService {

	/**
     * 获取日报支付方式分析数据
     * @param dayParamDto
     * @return
     */
    public JSONObject getPayTypeAnalyseData(DayParamDto dayParamDto);

    /**
     * 获取周报支付方式分析数据
     * @param weekParamDto
     * @return
     */
    public JSONObject getWeekPayTypeAnalyseData(WeekParamDto weekParamDto);

    /**
     * 获取月报支付方式分析数据
     * @param monthParamDto
     * @return
     */
    public JSONObject getMonthPayTypeAnalyseData(MonthParamDto monthParamDto);

    /**
     * 获取年报支付方式分析数据
     * @param yearParamDto
     * @return
     */
    public JSONObject getYearPayTypeAnalyseData(YearParamDto yearParamDto);
    
}
