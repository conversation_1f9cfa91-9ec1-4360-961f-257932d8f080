package cn.lyy.merchant.service.onedata;

import java.util.HashSet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.util.MultiValueMap;
import com.alibaba.fastjson.JSONObject;
import cn.lyy.merchant.controller.onedata.dto.DayParamDto;
import cn.lyy.merchant.controller.onedata.dto.MonthParamDto;
import cn.lyy.merchant.controller.onedata.dto.WeekParamDto;
import cn.lyy.merchant.controller.onedata.dto.YearParamDto;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class FeignUtil {
	
	@Value("${oneData.sk}")
    private String sk;
	
    @Value("${oneData.ak}")
    private String ak;
    
	@Autowired
    private OneDataServiceFeign oneDataServiceFeign;
	
	@SuppressWarnings("serial")
	public JSONObject getData(ParamDto params, Object obj){
        MultiValueMap<String, String> headers = new HttpHeaders();
        headers.add("sk",sk);
        headers.add("ak",ak);
        
        if(obj instanceof DayParamDto){
            params.getQueryParams().setDay(((DayParamDto) obj).getDay());
            params.getQueryParams().setMerchant_id(((DayParamDto) obj).getMerchantId()+"");
            params.getQueryParams().setDay(((DayParamDto) obj).getDay());

            params.getQueryParams().setProvince_id(((DayParamDto) obj).getProvinceId() == null ? null: new HashSet<String>(){{add(((DayParamDto) obj).getProvinceId()+"");}});
            params.getQueryParams().setCity_id(((DayParamDto) obj).getCityId() == null ? null:new HashSet<String>(){{add(((DayParamDto) obj).getCityId()+"");}});
            params.getQueryParams().setDistrict_id(((DayParamDto) obj).getAreaId() == null ? null: new HashSet<String>(){{add(((DayParamDto) obj).getAreaId()+"");}});
            params.getQueryParams().setEquipment_group_id(((DayParamDto) obj).getGroupId() == null ? null: new HashSet<String>(){{add(((DayParamDto) obj).getGroupId()+"");}});
            params.getQueryParams().setLyy_equipment_type_id(((DayParamDto) obj).getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(((DayParamDto) obj).getEquipmentTypeId()+"");}});

            String pageNum = ((DayParamDto) obj).getPageNum() == null ? null : (((DayParamDto) obj).getPageNum()-1)+"" ;
            params.getPageParams().setPageNum(pageNum);
            
            if(((DayParamDto) obj).getOrderBy() != null){
                params.getOrderParams().put(FieldMapping.fieldMap.get(((DayParamDto) obj).getOrderBy()),params.buildOrderFile(((DayParamDto) obj).getOrder() == null ?  "asc":((DayParamDto) obj).getOrder()));
            }

            if(((DayParamDto) obj).getGroupIds() != null && ((DayParamDto) obj).getGroupIds().size()>0){
                //params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
                if(((DayParamDto) obj).getGroupId() == null || !((DayParamDto) obj).getGroupIds().contains(((DayParamDto) obj).getGroupId()+"")){
                    params.getQueryParams().setEquipment_group_id(((DayParamDto) obj).getGroupIds());
                }
            }
        }

        if(obj instanceof WeekParamDto){
            params.getQueryParams().setYear_month(((WeekParamDto) obj).getMonth());
            params.getQueryParams().setMerchant_id(((WeekParamDto) obj).getMerchantId()+"");
            if(((WeekParamDto) obj).getWeek() != null){
                params.getQueryParams().setMonth_week_num(((WeekParamDto) obj).getWeek()+"");
            }


            params.getQueryParams().setProvince_id(((WeekParamDto) obj).getProvinceId() == null ? null: new HashSet<String>(){{add(((WeekParamDto) obj).getProvinceId()+"");}});
            params.getQueryParams().setCity_id(((WeekParamDto) obj).getCityId() == null ? null:new HashSet<String>(){{add(((WeekParamDto) obj).getCityId()+"");}});
            params.getQueryParams().setDistrict_id(((WeekParamDto) obj).getAreaId() == null ? null: new HashSet<String>(){{add(((WeekParamDto) obj).getAreaId()+"");}});
            params.getQueryParams().setEquipment_group_id(((WeekParamDto) obj).getGroupId() == null ? null: new HashSet<String>(){{add(((WeekParamDto) obj).getGroupId()+"");}});
            params.getQueryParams().setLyy_equipment_type_id(((WeekParamDto) obj).getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(((WeekParamDto) obj).getEquipmentTypeId()+"");}});

            params.getPageParams().setPageNum(((WeekParamDto) obj).getPageNum() == null ? null : (((WeekParamDto) obj).getPageNum()-1)+"");

            if(((WeekParamDto) obj).getOrderBy() != null){
                params.getOrderParams().put(FieldMapping.fieldMap.get(((WeekParamDto) obj).getOrderBy()),params.buildOrderFile(((WeekParamDto) obj).getOrder() == null ?  "asc":((WeekParamDto) obj).getOrder()));
            }

            if(((WeekParamDto) obj).getGroupIds() != null && ((WeekParamDto) obj).getGroupIds().size()>0){
                //params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
                if(((WeekParamDto) obj).getGroupId() == null || !((WeekParamDto) obj).getGroupIds().contains(((WeekParamDto) obj).getGroupId()+"")){
                    params.getQueryParams().setEquipment_group_id(((WeekParamDto) obj).getGroupIds());
                }
            }

        }

        if(obj instanceof MonthParamDto){
            if(((MonthParamDto) obj).getMonth() != null){
                params.getQueryParams().setYear_month(((MonthParamDto) obj).getMonth());
            }

            params.getQueryParams().setMerchant_id(((MonthParamDto) obj).getMerchantId()+"");
            //params.getQueryParams().setMonth_week_num(((MonthParamDto) obj).getWeek()+"");

            params.getQueryParams().setProvince_id(((MonthParamDto) obj).getProvinceId() == null ? null: new HashSet<String>(){{add(((MonthParamDto) obj).getProvinceId()+"");}});
            params.getQueryParams().setCity_id(((MonthParamDto) obj).getCityId() == null ? null:new HashSet<String>(){{add(((MonthParamDto) obj).getCityId()+"");}});
            params.getQueryParams().setDistrict_id(((MonthParamDto) obj).getAreaId() == null ? null: new HashSet<String>(){{add(((MonthParamDto) obj).getAreaId()+"");}});
            params.getQueryParams().setEquipment_group_id(((MonthParamDto) obj).getGroupId() == null ? null: new HashSet<String>(){{add(((MonthParamDto) obj).getGroupId()+"");}});
            params.getQueryParams().setLyy_equipment_type_id(((MonthParamDto) obj).getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(((MonthParamDto) obj).getEquipmentTypeId()+"");}});

            params.getPageParams().setPageNum(((MonthParamDto) obj).getPageNum() == null ? null : (((MonthParamDto) obj).getPageNum()-1)+"");

            if(((MonthParamDto) obj).getOrderBy() != null){
                params.getOrderParams().put(FieldMapping.fieldMap.get(((MonthParamDto) obj).getOrderBy()),params.buildOrderFile(((MonthParamDto) obj).getOrder() == null ?  "asc":((MonthParamDto) obj).getOrder()));
            }


            if(((MonthParamDto) obj).getGroupIds() != null && ((MonthParamDto) obj).getGroupIds().size()>0){
                //params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
                if(((MonthParamDto) obj).getGroupId() == null || !((MonthParamDto) obj).getGroupIds().contains(((MonthParamDto) obj).getGroupId()+"")){
                    params.getQueryParams().setEquipment_group_id(((MonthParamDto) obj).getGroupIds());
                }
            }
        }

        if(obj instanceof YearParamDto){
            if(((YearParamDto) obj).getYear() != null){
                params.getQueryParams().setYear_str(((YearParamDto) obj).getYear()+"");
            }

            params.getQueryParams().setMerchant_id(((YearParamDto) obj).getMerchantId()+"");
            //params.getQueryParams().setMonth_week_num(((MonthParamDto) obj).getWeek()+"");

            params.getQueryParams().setProvince_id(((YearParamDto) obj).getProvinceId() == null ? null: new HashSet<String>(){{add(((YearParamDto) obj).getProvinceId()+"");}});
            params.getQueryParams().setCity_id(((YearParamDto) obj).getCityId() == null ? null:new HashSet<String>(){{add(((YearParamDto) obj).getCityId()+"");}});
            params.getQueryParams().setDistrict_id(((YearParamDto) obj).getAreaId() == null ? null: new HashSet<String>(){{add(((YearParamDto) obj).getAreaId()+"");}});
            params.getQueryParams().setEquipment_group_id(((YearParamDto) obj).getGroupId() == null ? null: new HashSet<String>(){{add(((YearParamDto) obj).getGroupId()+"");}});
            params.getQueryParams().setLyy_equipment_type_id(((YearParamDto) obj).getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(((YearParamDto) obj).getEquipmentTypeId()+"");}});

            params.getPageParams().setPageNum(((YearParamDto) obj).getPageNum() == null ? null : (((YearParamDto) obj).getPageNum()-1)+"");
            if(((YearParamDto) obj).getOrderBy() != null){
                params.getOrderParams().put(FieldMapping.fieldMap.get(((YearParamDto) obj).getOrderBy()),params.buildOrderFile(((YearParamDto) obj).getOrder() == null ?  "asc":((YearParamDto) obj).getOrder()));
            }


            if(((YearParamDto) obj).getGroupIds() != null && ((YearParamDto) obj).getGroupIds().size()>0){
                //params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
                if(((YearParamDto) obj).getGroupId() == null || !((YearParamDto) obj).getGroupIds().contains(((YearParamDto) obj).getGroupId()+"")){
                    params.getQueryParams().setEquipment_group_id(((YearParamDto) obj).getGroupIds());
                }
            }
        }


        try{
            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
            JSONObject result = oneDataServiceFeign.getData(params,headers);
            log.info("{}",result.toJSONString());
            return result;
        }catch (Exception e){
            log.warn("{}",e);
        }
        return null;
    }

	public JSONObject getData(ParamDto params) {
		MultiValueMap<String, String> headers = new HttpHeaders();
        headers.add("sk",sk);
        headers.add("ak",ak);
		return oneDataServiceFeign.getData(params, headers) ;
	}
	
    public String idProcess(String id){
        if(id == null || "未知".equals(id) || "0".equals(id)){
            return "";
        }
        return id;
    }
	
}
