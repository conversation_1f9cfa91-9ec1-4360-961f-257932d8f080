package cn.lyy.merchant.service.onedata;

import cn.lyy.merchant.controller.onedata.dto.DayParamDto;
import cn.lyy.merchant.controller.onedata.dto.MonthParamDto;
import cn.lyy.merchant.controller.onedata.dto.WeekParamDto;
import cn.lyy.merchant.controller.onedata.dto.YearParamDto;
import com.alibaba.fastjson.JSONObject;

public interface OneDataRealTimeService {

    /**
     * 获取日报订单支付数据
     * @param dayParamDto
     * @return
     */
    public JSONObject getOrderData(DayParamDto dayParamDto);

    /**
     * 经营时段数据
     * @param dayParamDto
     * @return
     */
    public JSONObject getTimeRangeData(DayParamDto dayParamDto);

    public JSONObject getPerOrderPriceCount(DayParamDto dayParamDto);

    /**
     * 日报场地，设备数据
     * @param dayParamDto
     * @return
     */
    public JSONObject getGroupEquipmentData(DayParamDto dayParamDto);

    /**
     * 日报会员数据
     * @param dayParamDto
     * @return
     */
    public JSONObject getMemberData(DayParamDto dayParamDto);

    /**
     * 二级页面场地实时数据
     * @param dayParamDto
     * @return
     */
    public JSONObject getGroupData(DayParamDto dayParamDto);



}
