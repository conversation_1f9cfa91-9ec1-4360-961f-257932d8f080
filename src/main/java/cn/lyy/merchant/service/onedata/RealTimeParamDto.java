package cn.lyy.merchant.service.onedata;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Set;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class RealTimeParamDto {
    private String code;
    private String databaseType ;
    private String tableName;
    private String columnArray;
    private PageParams pageParams = new PageParams();
    private QueryParams queryParams=new QueryParams();
    private RangeParams rangeParams = new RangeParams();
    private JSONObject orderParams = new JSONObject();
    private JSONObject likeParams = new JSONObject();

    //只查询在线订单
    private String fixedCondition = "((status = 'SUCCESS' and type='Pay') or (type='Refund' and status='SUCCESS') OR(type='Refund' and status='FAIL')) and trade_type <> 'offline' and client_type <> '14'";
    private String groupByColumnArray;

    public void setCode(String code) {
    	String[] array = code.split(":") ;
    	this.code = array[0] ;
    	if(array.length >1) {
    		this.databaseType = array[1] ;
    	} else {
    		this.databaseType = "selectdb" ;//pg/selectdb
    	}
    }
    
    @Data
    class PageParams{
        private String pageRow = "100";
        private String pageNum = "0";
    }
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Data
    class QueryParams{
        private String ad_org_id;
        private String benefit_kind;
        private Set<String> city_id;
        private Set<String> province_id;
        private Set<String> district_id;
        private Set<String> lyy_equipment_group_id;
        private Set<String> lyy_equipment_type_id;
        @JsonProperty("T2.city_id")
        private Set<String> cityId;
        @JsonProperty("T3.province_id")
        private Set<String> provinceId;
        @JsonProperty("T1.district_id")
        private Set<String> districtId;
        private String distributor_id;
        private Set<String> equipment_group_id;
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Data
    class RangeParams{
        private Range created = new Range();
    }
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Data
    class Range{
        private String start;
        private String end;
    }

    @Data
    class OrderFile{
        private String direction;

        public OrderFile(){}
        public OrderFile(String direction){
            this.direction = direction;
        }
    }

    public OrderFile buildOrderFile(String direction){
        return new OrderFile(direction);
    }

}
