package cn.lyy.merchant.service.onedata.mobile;

import com.alibaba.fastjson.JSONObject;
import cn.lyy.merchant.controller.onedata.dto.DayParamDto;
import cn.lyy.merchant.controller.onedata.dto.MonthParamDto;
import cn.lyy.merchant.controller.onedata.dto.WeekParamDto;
import cn.lyy.merchant.controller.onedata.dto.YearParamDto;

public interface TimeRangeDataService {
	
	/**
     * 周报经营时段数据
     * @param dayParamDto
     * @return
     */
    public JSONObject getTimeRangeData(DayParamDto dayParamDto);

    /**
     * 周报经营时段数据
     * @param weekParamDto
     * @return
     */
    public JSONObject getWeekTimeRangeData(WeekParamDto weekParamDto);
    
    /**
     * 月报经营时段数据
     * @param monthParamDto
     * @return
     */
    public JSONObject getMonthTimeRangeData(MonthParamDto monthParamDto);

    /**
     * 年报经营时段数据
     * @param yearParamDto
     * @return
     */
    public JSONObject getYearTimeRangeData(YearParamDto yearParamDto);

}
