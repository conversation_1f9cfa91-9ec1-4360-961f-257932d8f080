package cn.lyy.merchant.service.onedata;

import com.alibaba.fastjson.JSONObject;
import org.springframework.util.MultiValueMap;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

@FeignClient(name = "one-data-service",url="${oneData.url}")
public interface OneDataServiceFeign {

    @PostMapping(value = "/rest/v1/getDataByTableColumnAndParams")
    public JSONObject getData(@RequestBody ParamDto params, @RequestHeader MultiValueMap<String,String> headers);

    @PostMapping(value = "/rest/v1/getDataByTableColumnAndParams")
    public JSONObject getRealTimeData(@RequestBody RealTimeParamDto params, @RequestHeader MultiValueMap<String,String> headers);

}
