package cn.lyy.merchant.service.onedata;

import cn.lyy.merchant.controller.onedata.dto.DayParamDto;
import cn.lyy.merchant.controller.onedata.dto.MonthParamDto;
import cn.lyy.merchant.controller.onedata.dto.WeekParamDto;
import cn.lyy.merchant.controller.onedata.dto.YearParamDto;
import cn.lyy.merchant.controller.onedata.util.TimeUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;

//@Service
@Slf4j
public class OneDataServiceImpl2 /*implements OneDataService*/{
//
//    @Autowired
//    private OneDataServiceFeign oneDataServiceFeign;
//    @Value("${oneData.sk}")
//    private String sk;
//    @Value("${oneData.ak}")
//    private String ak;
//    @Value("${oneData.dayPayTypeAnalyseCode}")
//    private String dayPayTypeAnalyseCode;
//    @Value("${oneData.dayOrderDayAnalyseCode}")
//    private String dayOrderDayAnalyseCode;
//    @Value("${oneData.dayGroupEquipmentAnalyseCode}")
//    private String dayGroupEquipmentAnalyseCode;
//    @Value("${oneData.dayTimeRangeDataCode}")
//    private String dayTimeRangeDataCode;
//    @Value("${oneData.dayMemberDataCode}")
//    private String dayMemberDataCode;
//    @Value("${oneData.dayMemberBenefitDataCode}")
//    private String dayMemberBenefitDataCode;
//    @Value("${oneData.groupBaseInfoCode}")
//    private String groupBaseInfoCode;
//    @Value("${oneData.merchantEquipmentTypeCode}")
//    private String merchantEquipmentTypeCode;
//    @Value("${oneData.weekTimeRangeDtaCode}")
//    private String weekTimeRangeDtaCode;
//    @Value("${oneData.weekMemberDataCode}")
//    private String weekMemberDataCode;
//    @Value("${oneData.weekOrderDayAnalyseCode}")
//    private String weekOrderDayAnalyseCode;
//    @Value("${oneData.weekPayTypeAnalyseCode}")
//    private String weekPayTypeAnalyseCode;
//    @Value("${oneData.weekGroupEquipmentAnalyseCode}")
//    private String weekGroupEquipmentAnalyseCode;
//    @Value("${oneData.weekMemberBenefitDataCode}")
//    private String weekMemberBenefitDataCode;
//    @Value("${oneData.monthOrderDayAnalyseCode}")
//    private String monthOrderDayAnalyseCode;
//    @Value("${oneData.monthPayTypeAnalyseCode}")
//    private String monthPayTypeAnalyseCode;
//    @Value("${oneData.monthGroupEquipmentAnalyseCode}")
//    private String monthGroupEquipmentAnalyseCode;
//    @Value("${oneData.monthTimeRangeDtaCode}")
//    private String monthTimeRangeDtaCode;
//    @Value("${oneData.monthMemberDataCode}")
//    private String monthMemberDataCode;
//    @Value("${oneData.monthMemberBenefitDataCode}")
//    private String monthMemberBenefitDataCode;
//    @Value("${oneData.yearMemberBenefitDataCode}")
//    private String yearMemberBenefitDataCode;
//    @Value("${oneData.yearTimeRangeDtaCode}")
//    private String yearTimeRangeDtaCode;
//    @Value("${oneData.yearGroupEquipmentAnalyseCode}")
//    private String yearGroupEquipmentAnalyseCode;
//    @Value("${oneData.yearOrderDayAnalyseCode}")
//    private String yearOrderDayAnalyseCode;
//    @Value("${oneData.yearMemberDataCode}")
//    private String yearMemberDataCode;
//    @Value("${oneData.yearPayTypeAnalyseCode}")
//    private String yearPayTypeAnalyseCode;
//    @Value("${oneData.dayEquipmentAnalyseCode}")
//    private String dayEquipmentAnalyseCode;
//    @Value("${oneData.weekEquipmentAnalyseCode}")
//    private String weekEquipmentAnalyseCode;
//    @Value("${oneData.monthEquipmentAnalyseCode}")
//    private String monthEquipmentAnalyseCode;
//    @Value("${oneData.yearEquipmentAnalyseCode}")
//    private String yearEquipmentAnalyseCode;
//
//    @Value("${oneData.databaseType:pg}")
//    private String databaseType ;//pg、selectdb
//
//    public JSONObject getData(DayParamDto dayParamDto) {
//        MultiValueMap<String, String> headers = new HttpHeaders();
//        headers.add("sk",sk);
//        headers.add("ak",ak);
//
//        ParamDto params = new ParamDto();
//        params.setCode("z57waw1j");
//        params.setTableName("dev_ads.da_lyy_bigdataserver_mobile_pay_type_analyse_di");
//        params.getQueryParams().setDay(dayParamDto.getDay());
//        params.getQueryParams().setMerchant_id(dayParamDto.getMerchantId()+"");
//        params.getQueryParams().setCombo_type(dayParamDto.getComboType());
//        params.getQueryParams().setDay(dayParamDto.getDay());
//
//        params.getQueryParams().setProvince_id(dayParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(dayParamDto.getProvinceId()+"");}});
//        params.getQueryParams().setCity_id(dayParamDto.getCityId() == null ? null:new HashSet<String>(){{add(dayParamDto.getCityId()+"");}});
//        params.getQueryParams().setDistrict_id(dayParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(dayParamDto.getAreaId()+"");}});
//        params.getQueryParams().setEquipment_group_id(dayParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(dayParamDto.getGroupId()+"");}});
//        params.getQueryParams().setLyy_equipment_type_id(dayParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(dayParamDto.getEquipmentTypeId()+"");}});
//
//
//        try{
//            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
//            JSONObject result = oneDataServiceFeign.getData(params,headers);
//            log.info("{}",result.toJSONString());
//            return result;
//        }catch (Exception e){
//            log.warn("{}",e);
//        }
//
//
//
//        return null;
//    }
//
//    @Override
//    public JSONObject getPayTypeAnalyseData(DayParamDto dayParamDto) {
//        MultiValueMap<String, String> headers = new HttpHeaders();
//        headers.add("sk",sk);
//        headers.add("ak",ak);
//
//        ParamDto params = new ParamDto();
//        params.setCode(dayPayTypeAnalyseCode);
//        params.setColumnArray("cast(pay_amount_yesterday as decimal(10,2)) as \"preRangePayAmount\"," +
//                "cast(pay_amount as decimal(10,2)) as \"payAmount\", " +
//                "pay_count as \"payCount\"," +
//                "cast(per_order_amount as decimal(10,2)) as \"perOrderAmount\", " +
//                "pay_type as \"payType\", " +
//                "day, " +
//                "cast(case when pay_amount_yesterday =0 and pay_amount>0 then 1 when pay_amount_yesterday=0 and pay_amount=0 then 0 else (pay_amount-pay_amount_yesterday)::numeric/pay_amount_yesterday::numeric end as decimal(10,2)) as up"
//        );
//
//
//        params.getQueryParams().setDay(dayParamDto.getDay());
//        params.getQueryParams().setMerchant_id(dayParamDto.getMerchantId()+"");
//        params.getQueryParams().setCombo_type(dayParamDto.getComboType());
//        params.getQueryParams().setDay(dayParamDto.getDay());
//
//        params.getQueryParams().setProvince_id(dayParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(dayParamDto.getProvinceId()+"");}});
//        params.getQueryParams().setCity_id(dayParamDto.getCityId() == null ? null:new HashSet<String>(){{add(dayParamDto.getCityId()+"");}});
//        params.getQueryParams().setDistrict_id(dayParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(dayParamDto.getAreaId()+"");}});
//        params.getQueryParams().setEquipment_group_id(dayParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(dayParamDto.getGroupId()+"");}});
//        params.getQueryParams().setLyy_equipment_type_id(dayParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(dayParamDto.getEquipmentTypeId()+"");}});
//
//        if(dayParamDto.getGroupIds() != null && dayParamDto.getGroupIds().size()>0){
//            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//            if(dayParamDto.getGroupId() == null || !dayParamDto.getGroupIds().contains(dayParamDto.getGroupId()+"")){
//                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(dayParamDto.getGroupIds())).first()+"");}});
//            }
//            
//            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
//            params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
//        }
//
//        try{
//            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
//            JSONObject result = oneDataServiceFeign.getData(params,headers);
//            log.info("{}",result.toJSONString());
//            return result;
//        }catch (Exception e){
//            log.warn("{}",e);
//        }
//
//
//
//        return null;
//    }
//
//    @Override
//    public JSONObject getWeekPayTypeAnalyseData(WeekParamDto weekParamDto) {
//        MultiValueMap<String, String> headers = new HttpHeaders();
//        headers.add("sk",sk);
//        headers.add("ak",ak);
//
//        ParamDto params = new ParamDto();
//        params.setCode(weekPayTypeAnalyseCode);
//        params.setColumnArray("distinct cast(pay_amount_last_week as decimal(10,2)) as \"preRangePayAmount\"," +
//                "cast(pay_amount as decimal(10,2)) as \"payAmount\", " +
//                "pay_count as \"payCount\"," +
//                "cast(per_order_amount as decimal(10,2)) as \"perOrderAmount\", " +
//                "pay_type as \"payType\", " +
//                "year_month as \"month\", " +
//                "month_week_num as \"week\", " +
//                "cast(case when pay_amount_last_week =0 and pay_amount>0 then 1 when pay_amount_last_week=0 and pay_amount=0 then 0 else (pay_amount-pay_amount_last_week)::numeric/pay_amount_last_week::numeric end as decimal(10,2)) as up"
//        );
//
//
//        params.getQueryParams().setYear_month(weekParamDto.getMonth());
//        params.getQueryParams().setMonth_week_num(weekParamDto.getWeek()+"");
//        params.getQueryParams().setMerchant_id(weekParamDto.getMerchantId()+"");
//        params.getQueryParams().setCombo_type(weekParamDto.getComboType());
//
//        params.getQueryParams().setProvince_id(weekParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(weekParamDto.getProvinceId()+"");}});
//        params.getQueryParams().setCity_id(weekParamDto.getCityId() == null ? null:new HashSet<String>(){{add(weekParamDto.getCityId()+"");}});
//        params.getQueryParams().setDistrict_id(weekParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(weekParamDto.getAreaId()+"");}});
//        params.getQueryParams().setEquipment_group_id(weekParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(weekParamDto.getGroupId()+"");}});
//        //params.getQueryParams().setLyy_equipment_type_id(weekParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(dayParamDto.getEquipmentTypeId()+"");}});
//
//        if(weekParamDto.getGroupIds() != null && weekParamDto.getGroupIds().size()>0){
//            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//            if(weekParamDto.getGroupId() == null || !weekParamDto.getGroupIds().contains(weekParamDto.getGroupId()+"")){
//                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(weekParamDto.getGroupIds())).first()+"");}});
//            }
//            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
//            params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
//        }
//        try{
//            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
//            JSONObject result = oneDataServiceFeign.getData(params,headers);
//            log.info("{}",result.toJSONString());
//            return result;
//        }catch (Exception e){
//            log.warn("{}",e);
//        }
//
//
//
//        return null;
//    }
//
//    @Override
//    public JSONObject getMonthPayTypeAnalyseData(MonthParamDto monthParamDto) {
//        MultiValueMap<String, String> headers = new HttpHeaders();
//        headers.add("sk",sk);
//        headers.add("ak",ak);
//
//        ParamDto params = new ParamDto();
//        params.setCode(monthPayTypeAnalyseCode);
//        params.setColumnArray("distinct cast(pay_amount_last_month as decimal(10,2)) as \"preRangePayAmount\"," +
//                "cast(pay_amount as decimal(10,2)) as \"payAmount\", " +
//                "pay_count as \"payCount\"," +
//                "cast(per_order_amount as decimal(10,2)) as \"perOrderAmount\", " +
//                "pay_type as \"payType\", " +
//                "year_month as \"month\", " +
//                "cast(case when pay_amount_last_month =0 and pay_amount>0 then 1 when pay_amount_last_month=0 and pay_amount=0 then 0 else (pay_amount-pay_amount_last_month)::numeric/pay_amount_last_month::numeric end as decimal(10,2)) as up"
//        );
//
//
//        params.getQueryParams().setYear_month(monthParamDto.getMonth());
//        params.getQueryParams().setMerchant_id(monthParamDto.getMerchantId()+"");
//        params.getQueryParams().setCombo_type(monthParamDto.getComboType());
//
//        params.getQueryParams().setProvince_id(monthParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(monthParamDto.getProvinceId()+"");}});
//        params.getQueryParams().setCity_id(monthParamDto.getCityId() == null ? null:new HashSet<String>(){{add(monthParamDto.getCityId()+"");}});
//        params.getQueryParams().setDistrict_id(monthParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(monthParamDto.getAreaId()+"");}});
//        params.getQueryParams().setEquipment_group_id(monthParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(monthParamDto.getGroupId()+"");}});
//        //params.getQueryParams().setLyy_equipment_type_id(weekParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(dayParamDto.getEquipmentTypeId()+"");}});
//
//        if(monthParamDto.getGroupIds() != null && monthParamDto.getGroupIds().size()>0){
//            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//            if(monthParamDto.getGroupId() == null || !monthParamDto.getGroupIds().contains(monthParamDto.getGroupId()+"")){
//                 params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(monthParamDto.getGroupIds())).first()+"");}});
//            }
//            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
//            params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
//        }
//        try{
//            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
//            JSONObject result = oneDataServiceFeign.getData(params,headers);
//            log.info("{}",result.toJSONString());
//            //去掉支付方式为0的
//            if(result !=null && result.getJSONObject("info") != null){
//                JSONArray arr = result.getJSONObject("info").getJSONArray("list");
//                if(arr != null && arr.size()>0){
//                    for(int i=0; i<arr.size(); i++){
//                        JSONObject obj = arr.getJSONObject(i);
//                        if(obj.get("payAmount") == null && obj.get("payCount") == null){
//                            arr.remove(obj);
//                        }
//
//                        if(obj.get("payAmount") != null && obj.getDouble("payAmount") ==0 && obj.get("payCount") != null && obj.getDouble("payCount") == 0){
//                            arr.remove(obj);
//                        }
//                    }
//                }
//            }
//            return result;
//        }catch (Exception e){
//            log.warn("{}",e);
//        }
//
//
//
//        return null;
//    }
//
//    @Override
//    public JSONObject getYearPayTypeAnalyseData(YearParamDto yearParamDto) {
//        MultiValueMap<String, String> headers = new HttpHeaders();
//        headers.add("sk",sk);
//        headers.add("ak",ak);
//
//        ParamDto params = new ParamDto();
//        params.setCode(yearPayTypeAnalyseCode);
//        params.setColumnArray("distinct cast(pay_amount_last_year as decimal(10,2)) as \"preRangePayAmount\"," +
//                "cast(pay_amount as decimal(10,2)) as \"payAmount\", " +
//                "pay_count as \"payCount\"," +
//                "cast(per_order_amount as decimal(10,2)) as \"perOrderAmount\", " +
//                "pay_type as \"payType\", " +
//                "year_str as \"year\", " +
//                "cast(case when pay_amount_last_year =0 and pay_amount>0 then 1 when pay_amount_last_year=0 and pay_amount=0 then 0 else (pay_amount-pay_amount_last_year)::numeric/pay_amount_last_year::numeric end as decimal(10,2)) as up"
//        );
//
//
//        params.getQueryParams().setYear_str(yearParamDto.getYear()+"");
//        params.getQueryParams().setMerchant_id(yearParamDto.getMerchantId()+"");
//        params.getQueryParams().setCombo_type(yearParamDto.getComboType());
//
//        params.getQueryParams().setProvince_id(yearParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(yearParamDto.getProvinceId()+"");}});
//        params.getQueryParams().setCity_id(yearParamDto.getCityId() == null ? null:new HashSet<String>(){{add(yearParamDto.getCityId()+"");}});
//        params.getQueryParams().setDistrict_id(yearParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(yearParamDto.getAreaId()+"");}});
//        params.getQueryParams().setEquipment_group_id(yearParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(yearParamDto.getGroupId()+"");}});
//        //params.getQueryParams().setLyy_equipment_type_id(weekParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(dayParamDto.getEquipmentTypeId()+"");}});
//
//        if(yearParamDto.getGroupIds() != null && yearParamDto.getGroupIds().size()>0){
//            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//            if(yearParamDto.getGroupId() == null || !yearParamDto.getGroupIds().contains(yearParamDto.getGroupId()+"")){
//                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(yearParamDto.getGroupIds())).first()+"");}});
//            }
//            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
//            params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
//        }
//        try{
//            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
//            JSONObject result = oneDataServiceFeign.getData(params,headers);
//            log.info("{}",result.toJSONString());
//            //去掉支付方式为0的
//            if(result !=null && result.getJSONObject("info") != null){
//                JSONArray arr = result.getJSONObject("info").getJSONArray("list");
//                if(arr != null && arr.size()>0){
//                    for(int i=0; i<arr.size(); i++){
//                        JSONObject obj = arr.getJSONObject(i);
//                        if(obj.get("payAmount") == null && obj.get("payCount") == null){
//                            arr.remove(obj);
//                        }
//
//                        if(obj.get("payAmount") != null && obj.getDouble("payAmount") ==0 && obj.get("payCount") != null && obj.getDouble("payCount") == 0){
//                            arr.remove(obj);
//                        }
//                    }
//                }
//            }
//            return result;
//        }catch (Exception e){
//            log.warn("{}",e);
//        }
//
//
//
//        return null;
//    }
//
//    @Override
//    public JSONObject getOrderData(DayParamDto dayParamDto) {
//        MultiValueMap<String, String> headers = new HttpHeaders();
//        headers.add("sk",sk);
//        headers.add("ak",ak);
//
//        ParamDto params = new ParamDto();
//        params.setCode(dayOrderDayAnalyseCode);
//
//        params.setColumnArray("merchant_id as \"merchantId\", " +
//
//                "cast(online_actual_amount as decimal(10,2)) as \"onlineAmount\"," +
//                "cast(case when online_actual_amount_yesterday =0 and online_actual_amount>0 then 1 when online_actual_amount_yesterday=0 and online_actual_amount=0 then 0 else (online_actual_amount-online_actual_amount_yesterday)::numeric/online_actual_amount_yesterday::numeric end as decimal(10,2)) as \"onlineAmountUp\"," +
//
//                "cast(online_pay_amount as decimal(10,2)) as \"onlinePayAmount\", " +
//                "cast(case when online_pay_amount_yesterday =0 and online_pay_amount>0 then 1 when online_pay_amount_yesterday=0 and online_pay_amount=0 then 0 else (online_pay_amount-online_pay_amount_yesterday)::numeric/online_pay_amount_yesterday::numeric end as decimal(10,2)) as \"onlinePayAmountUp\"," +
//
//                "online_pay_count as \"onlinePayCounts\", " +
//                "cast(case when online_pay_count_yesterday =0 and online_pay_count>0 then 1 when online_pay_count=0 and online_pay_count_yesterday=0 then 0 else (online_pay_count-online_pay_count_yesterday)::numeric/online_pay_count_yesterday::numeric end as decimal(10,2)) as \"onlinePayCountsUp\"," +
//
//                "cast(online_refund_amount as decimal(10,2)) as \"onlineRefundAmount\", " +
//                "cast(case when online_refund_amount_yesterday =0 and online_refund_amount>0 then 1 when online_refund_amount_yesterday=0 and online_refund_amount=0 then 0 else (online_refund_amount-online_refund_amount_yesterday)::numeric/online_refund_amount_yesterday::numeric end as decimal(10,2)) as \"onlineRefundAmountUp\"," +
//
//                "online_refund_count as \"onlineRefundCounts\"," +
//                "cast(case when online_refund_count_yesterday =0 and online_refund_count>0 then 1 when online_refund_count_yesterday=0 and online_refund_count=0 then 0 else (online_refund_count-online_refund_count_yesterday)::numeric/online_refund_count_yesterday::numeric end as decimal(10,2)) as \"onlineRefundCountsUp\"," +
//
//                "cast(offline_actual_amount as decimal(10,2)) as \"cashAmount\"," +        //现金总收入
//                "cast(case when offline_actual_amount_yesterday =0 and offline_actual_amount>0 then 1 when offline_actual_amount_yesterday=0 and offline_actual_amount=0 then 0 else (offline_actual_amount-offline_actual_amount_yesterday)::numeric/offline_actual_amount_yesterday::numeric end as decimal(10,2)) as \"cashAmountUp\"," +
//
//                "cast(offline_pay_amount as decimal(10,2)) as \"cashPayAmount\"," +        //现金收款
//                "cast(case when offline_pay_amount_yesterday =0 and offline_pay_amount>0 then 1 when offline_pay_amount_yesterday=0 and offline_pay_amount=0 then 0 else (offline_pay_amount-offline_pay_amount_yesterday)::numeric/offline_pay_amount_yesterday::numeric end as decimal(10,2)) as \"cashPayAmountUp\"," +
//
//                "cast(offline_pay_count as decimal(10,2)) as \"cashPayCounts\"," +        //现金收款笔数
//                "cast(case when offline_pay_count_yesterday =0 and offline_pay_count>0 then 1 when offline_pay_count_yesterday=0 and offline_pay_count=0 then 0 else (offline_pay_count-offline_pay_count_yesterday)::numeric/offline_pay_count_yesterday::numeric end as decimal(10,2)) as \"cashPayCountsUp\"," +
//
//                "0 as \"cashRefundAmount\", 0 as \"cashRefundAmountUp\", 0 as \"cashRefundCounts\", 0 as \"cashRefundCountsUp\"," +       //现金没有退款
//
//                "cast(pay_amount as decimal(10,2)) as \"payAmount\"," +
//                "cast(case when pay_amount_yesterday =0 and pay_amount>0 then 1 when pay_amount_yesterday=0 and pay_amount=0 then 0 else (pay_amount-pay_amount_yesterday)::numeric/pay_amount_yesterday::numeric end as decimal(10,2)) as \"payAmountUp\"," +
//
//                "pay_count as \"payCounts\"," +
//                "cast(case when pay_count_yesterday =0 and pay_count>0 then 1 when pay_count_yesterday=0 and pay_count=0 then 0 else (pay_count-pay_count_yesterday)::numeric/pay_count_yesterday::numeric end as decimal(10,2)) as \"payCountsUp\"," +
//
//                "cast(refund_amount as decimal(10,2)) as \"refundAmount\"," +
//                "cast(case when refund_amount_yesterday =0 and refund_amount>0 then 1 when refund_amount_yesterday=0 and refund_amount=0 then 0 else (refund_amount-refund_amount_yesterday)::numeric/refund_amount_yesterday::numeric end as decimal(10,2)) as \"refundAmountUp\"," +
//
//                "refund_count as \"refundCounts\"," +
//                "cast(case when refund_count_yesterday =0 and refund_count>0 then 1 when refund_count_yesterday=0 and refund_count=0 then 0 else (refund_count-refund_count_yesterday)::numeric/refund_count_yesterday::numeric end as decimal(10,2)) as \"refundCountsUp\""
//        );
//
//
//        params.getQueryParams().setDay(dayParamDto.getDay());
//        params.getQueryParams().setMerchant_id(dayParamDto.getMerchantId()+"");
//        params.getQueryParams().setCombo_type(dayParamDto.getComboType());
//        params.getQueryParams().setDay(dayParamDto.getDay());
//
//        params.getQueryParams().setProvince_id(dayParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(dayParamDto.getProvinceId()+"");}});
//        params.getQueryParams().setCity_id(dayParamDto.getCityId() == null ? null:new HashSet<String>(){{add(dayParamDto.getCityId()+"");}});
//        params.getQueryParams().setDistrict_id(dayParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(dayParamDto.getAreaId()+"");}});
//        params.getQueryParams().setEquipment_group_id(dayParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(dayParamDto.getGroupId()+"");}});
//        params.getQueryParams().setLyy_equipment_type_id(dayParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(dayParamDto.getEquipmentTypeId()+"");}});
//
//        if(dayParamDto.getGroupIds() != null && dayParamDto.getGroupIds().size()>0){
//            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//            if(dayParamDto.getGroupId() == null || !dayParamDto.getGroupIds().contains(dayParamDto.getGroupId()+"")){
//                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(dayParamDto.getGroupIds())).first()+"");}});
//            }
//            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
//            params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
//        }
//
//        try{
//            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
//            JSONObject result = oneDataServiceFeign.getData(params,headers);
//            log.info("{}",result.toJSONString());
//            return result;
//        }catch (Exception e){
//            log.warn("{}",e);
//        }
//
//
//
//        return null;
//    }
//
//    @Override
//    public JSONObject getWeekOrderData(WeekParamDto weekParamDto) {
//        MultiValueMap<String, String> headers = new HttpHeaders();
//        headers.add("sk",sk);
//        headers.add("ak",ak);
//
//        ParamDto params = new ParamDto();
//        params.setCode(weekOrderDayAnalyseCode);
//
//        params.setColumnArray("merchant_id as \"merchantId\", " +
//
//                "cast(online_actual_amount as decimal(10,2)) as \"onlineAmount\"," +
//                "cast(case when online_actual_amount_last_week =0 and online_actual_amount>0 then 1 when online_actual_amount_last_week=0 and online_actual_amount=0 then 0 else (online_actual_amount-online_actual_amount_last_week)::numeric/online_actual_amount_last_week::numeric end as decimal(10,2)) as \"onlineAmountUp\"," +
//
//                "cast(online_pay_amount as decimal(10,2)) as \"onlinePayAmount\", " +
//                "cast(case when online_pay_amount_last_week =0 and online_pay_amount>0 then 1 when online_pay_amount_last_week=0 and online_pay_amount=0 then 0 else (online_pay_amount-online_pay_amount_last_week)::numeric/online_pay_amount_last_week::numeric end as decimal(10,2)) as \"onlinePayAmountUp\"," +
//
//                "online_pay_count as \"onlinePayCounts\", " +
//                "cast(case when online_pay_count_last_week =0 and online_pay_count>0 then 1 when online_pay_count=0 and online_pay_count_last_week=0 then 0 else (online_pay_count-online_pay_count_last_week)::numeric/online_pay_count_last_week::numeric end as decimal(10,2)) as \"onlinePayCountsUp\"," +
//
//                "cast(online_refund_amount as decimal(10,2)) as \"onlineRefundAmount\", " +
//                "cast(case when online_refund_amount_last_week =0 and online_refund_amount>0 then 1 when online_refund_amount_last_week=0 and online_refund_amount=0 then 0 else (online_refund_amount-online_refund_amount_last_week)::numeric/online_refund_amount_last_week::numeric end as decimal(10,2)) as \"onlineRefundAmountUp\"," +
//
//                "online_refund_count as \"onlineRefundCounts\"," +
//                "cast(case when online_refund_count_last_week =0 and online_refund_count>0 then 1 when online_refund_count_last_week=0 and online_refund_count=0 then 0 else (online_refund_count-online_refund_count_last_week)::numeric/online_refund_count_last_week::numeric end as decimal(10,2)) as \"onlineRefundCountsUp\"," +
//
//                "cast(offline_actual_amount as decimal(10,2)) as \"cashAmount\"," +        //现金总收入
//                "cast(case when offline_actual_amount_last_week =0 and offline_actual_amount>0 then 1 when offline_actual_amount_last_week=0 and offline_actual_amount=0 then 0 else (offline_actual_amount-offline_actual_amount_last_week)::numeric/offline_actual_amount_last_week::numeric end as decimal(10,2)) as \"cashAmountUp\"," +
//
//                "cast(offline_pay_amount as decimal(10,2)) as \"cashPayAmount\"," +        //现金收款
//                "cast(case when offline_pay_amount_last_week =0 and offline_pay_amount>0 then 1 when offline_pay_amount_last_week=0 and offline_pay_amount=0 then 0 else (offline_pay_amount-offline_pay_amount_last_week)::numeric/offline_pay_amount_last_week::numeric end as decimal(10,2)) as \"cashPayAmountUp\"," +
//
//                "cast(offline_pay_count as decimal(10,2)) as \"cashPayCounts\"," +        //现金收款笔数
//                "cast(case when offline_pay_count_last_week =0 and offline_pay_count>0 then 1 when offline_pay_count_last_week=0 and offline_pay_count=0 then 0 else (offline_pay_count-offline_pay_count_last_week)::numeric/offline_pay_count_last_week::numeric end as decimal(10,2)) as \"cashPayCountsUp\"," +
//
//                "0 as \"cashRefundAmount\", 0 as \"cashRefundAmountUp\", 0 as \"cashRefundCounts\", 0 as \"cashRefundCountsUp\"," +       //现金没有退款
//
//                "cast(pay_amount as decimal(10,2)) as \"payAmount\"," +
//                "cast(case when pay_amount_last_week =0 and pay_amount>0 then 1 when pay_amount_last_week=0 and pay_amount=0 then 0 else (pay_amount-pay_amount_last_week)::numeric/pay_amount_last_week::numeric end as decimal(10,2)) as \"payAmountUp\"," +
//
//                "pay_count as \"payCounts\"," +
//                "cast(case when pay_count_last_week =0 and pay_count>0 then 1 when pay_count_last_week=0 and pay_count=0 then 0 else (pay_count-pay_count_last_week)::numeric/pay_count_last_week::numeric end as decimal(10,2)) as \"payCountsUp\"," +
//
//                "cast(refund_amount as decimal(10,2)) as \"refundAmount\"," +
//                "cast(case when refund_amount_last_week =0 and refund_amount>0 then 1 when refund_amount_last_week=0 and refund_amount=0 then 0 else (refund_amount-refund_amount_last_week)::numeric/refund_amount_last_week::numeric end as decimal(10,2)) as \"refundAmountUp\"," +
//
//                "refund_count as \"refundCounts\"," +
//                "cast(case when refund_count_last_week =0 and refund_count>0 then 1 when refund_count_last_week=0 and refund_count=0 then 0 else (refund_count-refund_count_last_week)::numeric/refund_count_last_week::numeric end as decimal(10,2)) as \"refundCountsUp\""
//        );
//
//
//        params.getQueryParams().setYear_month(weekParamDto.getMonth());
//        params.getQueryParams().setMerchant_id(weekParamDto.getMerchantId()+"");
//        params.getQueryParams().setCombo_type(weekParamDto.getComboType());
//        params.getQueryParams().setMonth_week_num(weekParamDto.getWeek()+"");
//
//        params.getQueryParams().setProvince_id(weekParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(weekParamDto.getProvinceId()+"");}});
//        params.getQueryParams().setCity_id(weekParamDto.getCityId() == null ? null:new HashSet<String>(){{add(weekParamDto.getCityId()+"");}});
//        params.getQueryParams().setDistrict_id(weekParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(weekParamDto.getAreaId()+"");}});
//        params.getQueryParams().setEquipment_group_id(weekParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(weekParamDto.getGroupId()+"");}});
//        params.getQueryParams().setLyy_equipment_type_id(weekParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(weekParamDto.getEquipmentTypeId()+"");}});
//
//        if(weekParamDto.getGroupIds() != null && weekParamDto.getGroupIds().size()>0){
//            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//            if(weekParamDto.getGroupId() == null || !weekParamDto.getGroupIds().contains(weekParamDto.getGroupId()+"")){
//                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(weekParamDto.getGroupIds())).first()+"");}});
//            }
//            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
//            params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
//        }
//
//        try{
//            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
//            JSONObject result = oneDataServiceFeign.getData(params,headers);
//            log.info("{}",result.toJSONString());
//            return result;
//        }catch (Exception e){
//            log.warn("{}",e);
//        }
//
//
//
//        return null;
//    }
//
//    @Override
//    public JSONObject getMonthOrderData(MonthParamDto monthParamDto) {
//        MultiValueMap<String, String> headers = new HttpHeaders();
//        headers.add("sk",sk);
//        headers.add("ak",ak);
//
//        ParamDto params = new ParamDto();
//        params.setCode(monthOrderDayAnalyseCode);
//
//        params.setColumnArray("merchant_id as \"merchantId\", " +
//
//                "cast(online_actual_amount as decimal(10,2)) as \"onlineAmount\"," +
//                "cast(case when online_actual_amount_last_month =0 and online_actual_amount>0 then 1 when online_actual_amount_last_month=0 and online_actual_amount=0 then 0 else (online_actual_amount-online_actual_amount_last_month)::numeric/online_actual_amount_last_month::numeric end as decimal(10,2)) as \"onlineAmountUp\"," +
//
//                "cast(online_pay_amount as decimal(10,2)) as \"onlinePayAmount\", " +
//                "cast(case when online_pay_amount_last_month =0 and online_pay_amount>0 then 1 when online_pay_amount_last_month=0 and online_pay_amount=0 then 0 else (online_pay_amount-online_pay_amount_last_month)::numeric/online_pay_amount_last_month::numeric end as decimal(10,2)) as \"onlinePayAmountUp\"," +
//
//                "online_pay_count as \"onlinePayCounts\", " +
//                "cast(case when online_pay_count_last_month =0 and online_pay_count>0 then 1 when online_pay_count=0 and online_pay_count_last_month=0 then 0 else (online_pay_count-online_pay_count_last_month)::numeric/online_pay_count_last_month::numeric end as decimal(10,2)) as \"onlinePayCountsUp\"," +
//
//                "cast(online_refund_amount as decimal(10,2)) as \"onlineRefundAmount\", " +
//                "cast(case when online_refund_amount_last_month =0 and online_refund_amount>0 then 1 when online_refund_amount_last_month=0 and online_refund_amount=0 then 0 else (online_refund_amount-online_refund_amount_last_month)::numeric/online_refund_amount_last_month::numeric end as decimal(10,2)) as \"onlineRefundAmountUp\"," +
//
//                "online_refund_count as \"onlineRefundCounts\"," +
//                "cast(case when online_refund_count_last_month =0 and online_refund_count>0 then 1 when online_refund_count_last_month=0 and online_refund_count=0 then 0 else (online_refund_count-online_refund_count_last_month)::numeric/online_refund_count_last_month::numeric end as decimal(10,2)) as \"onlineRefundCountsUp\"," +
//
//                "cast(offline_actual_amount as decimal(10,2)) as \"cashAmount\"," +        //现金总收入
//                "cast(case when offline_actual_amount_last_month =0 and offline_actual_amount>0 then 1 when offline_actual_amount_last_month=0 and offline_actual_amount=0 then 0 else (offline_actual_amount-offline_actual_amount_last_month)::numeric/offline_actual_amount_last_month::numeric end as decimal(10,2)) as \"cashAmountUp\"," +
//
//                "cast(offline_pay_amount as decimal(10,2)) as \"cashPayAmount\"," +        //现金收款
//                "cast(case when offline_pay_amount_last_month =0 and offline_pay_amount>0 then 1 when offline_pay_amount_last_month=0 and offline_pay_amount=0 then 0 else (offline_pay_amount-offline_pay_amount_last_month)::numeric/offline_pay_amount_last_month::numeric end as decimal(10,2)) as \"cashPayAmountUp\"," +
//
//                "cast(offline_pay_count as decimal(10,2)) as \"cashPayCounts\"," +        //现金收款笔数
//                "cast(case when offline_pay_count_last_month =0 and offline_pay_count>0 then 1 when offline_pay_count_last_month=0 and offline_pay_count=0 then 0 else (offline_pay_count-offline_pay_count_last_month)::numeric/offline_pay_count_last_month::numeric end as decimal(10,2)) as \"cashPayCountsUp\"," +
//
//                "0 as \"cashRefundAmount\", 0 as \"cashRefundAmountUp\", 0 as \"cashRefundCounts\", 0 as \"cashRefundCountsUp\"," +       //现金没有退款
//
//                "cast(pay_amount as decimal(10,2)) as \"payAmount\"," +
//                "cast(case when pay_amount_last_month =0 and pay_amount>0 then 1 when pay_amount_last_month=0 and pay_amount=0 then 0 else (pay_amount-pay_amount_last_month)::numeric/pay_amount_last_month::numeric end as decimal(10,2)) as \"payAmountUp\"," +
//
//                "pay_count as \"payCounts\"," +
//                "cast(case when pay_count_last_month =0 and pay_count>0 then 1 when pay_count_last_month=0 and pay_count=0 then 0 else (pay_count-pay_count_last_month)::numeric/pay_count_last_month::numeric end as decimal(10,2)) as \"payCountsUp\"," +
//
//                "cast(refund_amount as decimal(10,2)) as \"refundAmount\"," +
//                "cast(case when refund_amount_last_month =0 and refund_amount>0 then 1 when refund_amount_last_month=0 and refund_amount=0 then 0 else (refund_amount-refund_amount_last_month)::numeric/refund_amount_last_month::numeric end as decimal(10,2)) as \"refundAmountUp\"," +
//
//                "refund_count as \"refundCounts\"," +
//                "cast(case when refund_count_last_month =0 and refund_count>0 then 1 when refund_count_last_month=0 and refund_count=0 then 0 else (refund_count-refund_count_last_month)::numeric/refund_count_last_month::numeric end as decimal(10,2)) as \"refundCountsUp\""
//        );
//
//
//        params.getQueryParams().setYear_month(monthParamDto.getMonth());
//        params.getQueryParams().setMerchant_id(monthParamDto.getMerchantId()+"");
//        params.getQueryParams().setCombo_type(monthParamDto.getComboType());
//
//        params.getQueryParams().setProvince_id(monthParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(monthParamDto.getProvinceId()+"");}});
//        params.getQueryParams().setCity_id(monthParamDto.getCityId() == null ? null:new HashSet<String>(){{add(monthParamDto.getCityId()+"");}});
//        params.getQueryParams().setDistrict_id(monthParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(monthParamDto.getAreaId()+"");}});
//        params.getQueryParams().setEquipment_group_id(monthParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(monthParamDto.getGroupId()+"");}});
//        params.getQueryParams().setLyy_equipment_type_id(monthParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(monthParamDto.getEquipmentTypeId()+"");}});
//
//
//        if(monthParamDto.getGroupIds() != null && monthParamDto.getGroupIds().size()>0){
//            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//            if(monthParamDto.getGroupId() == null || !monthParamDto.getGroupIds().contains(monthParamDto.getGroupId()+"")){
//                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(monthParamDto.getGroupIds())).first()+"");}});
//            }
//            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
//            params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
//        }
//
//        try{
//            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
//            JSONObject result = oneDataServiceFeign.getData(params,headers);
//            log.info("{}",result.toJSONString());
//            return result;
//        }catch (Exception e){
//            log.warn("{}",e);
//        }
//
//
//
//        return null;
//    }
//
//    @Override
//    public JSONObject getYearOrderData(YearParamDto yearParamDto) {
//        MultiValueMap<String, String> headers = new HttpHeaders();
//        headers.add("sk",sk);
//        headers.add("ak",ak);
//
//        ParamDto params = new ParamDto();
//        params.setCode(yearOrderDayAnalyseCode);
//
//        params.setColumnArray("merchant_id as \"merchantId\", " +
//
//                "cast(online_actual_amount as decimal(10,2)) as \"onlineAmount\"," +
//                "cast(case when online_actual_amount_last_year =0 and online_actual_amount>0 then 1 when online_actual_amount_last_year=0 and online_actual_amount=0 then 0 else (online_actual_amount-online_actual_amount_last_year)::numeric/online_actual_amount_last_year::numeric end as decimal(10,2)) as \"onlineAmountUp\"," +
//
//                "cast(online_pay_amount as decimal(10,2)) as \"onlinePayAmount\", " +
//                "cast(case when online_pay_amount_last_year =0 and online_pay_amount>0 then 1 when online_pay_amount_last_year=0 and online_pay_amount=0 then 0 else (online_pay_amount-online_pay_amount_last_year)::numeric/online_pay_amount_last_year::numeric end as decimal(10,2)) as \"onlinePayAmountUp\"," +
//
//                "online_pay_count as \"onlinePayCounts\", " +
//                "cast(case when online_pay_count_last_year =0 and online_pay_count>0 then 1 when online_pay_count=0 and online_pay_count_last_year=0 then 0 else (online_pay_count-online_pay_count_last_year)::numeric/online_pay_count_last_year::numeric end as decimal(10,2)) as \"onlinePayCountsUp\"," +
//
//                "cast(online_refund_amount as decimal(10,2)) as \"onlineRefundAmount\", " +
//                "cast(case when online_refund_amount_last_year =0 and online_refund_amount>0 then 1 when online_refund_amount_last_year=0 and online_refund_amount=0 then 0 else (online_refund_amount-online_refund_amount_last_year)::numeric/online_refund_amount_last_year::numeric end as decimal(10,2)) as \"onlineRefundAmountUp\"," +
//
//                "online_refund_count as \"onlineRefundCounts\"," +
//                "cast(case when online_refund_count_last_year =0 and online_refund_count>0 then 1 when online_refund_count_last_year=0 and online_refund_count=0 then 0 else (online_refund_count-online_refund_count_last_year)::numeric/online_refund_count_last_year::numeric end as decimal(10,2)) as \"onlineRefundCountsUp\"," +
//
//                "cast(offline_actual_amount as decimal(10,2)) as \"cashAmount\"," +        //现金总收入
//                "cast(case when offline_actual_amount_last_year =0 and offline_actual_amount>0 then 1 when offline_actual_amount_last_year=0 and offline_actual_amount=0 then 0 else (offline_actual_amount-offline_actual_amount_last_year)::numeric/offline_actual_amount_last_year::numeric end as decimal(10,2)) as \"cashAmountUp\"," +
//
//                "cast(offline_pay_amount as decimal(10,2)) as \"cashPayAmount\"," +        //现金收款
//                "cast(case when offline_pay_amount_last_year =0 and offline_pay_amount>0 then 1 when offline_pay_amount_last_year=0 and offline_pay_amount=0 then 0 else (offline_pay_amount-offline_pay_amount_last_year)::numeric/offline_pay_amount_last_year::numeric end as decimal(10,2)) as \"cashPayAmountUp\"," +
//
//                "cast(offline_pay_count as decimal(10,2)) as \"cashPayCounts\"," +        //现金收款笔数
//                "cast(case when offline_pay_count_last_year =0 and offline_pay_count>0 then 1 when offline_pay_count_last_year=0 and offline_pay_count=0 then 0 else (offline_pay_count-offline_pay_count_last_year)::numeric/offline_pay_count_last_year::numeric end as decimal(10,2)) as \"cashPayCountsUp\"," +
//
//                "0 as \"cashRefundAmount\", 0 as \"cashRefundAmountUp\", 0 as \"cashRefundCounts\", 0 as \"cashRefundCountsUp\"," +       //现金没有退款
//
//                "cast(pay_amount as decimal(10,2)) as \"payAmount\"," +
//                "cast(case when pay_amount_last_year =0 and pay_amount>0 then 1 when pay_amount_last_year=0 and pay_amount=0 then 0 else (pay_amount-pay_amount_last_year)::numeric/pay_amount_last_year::numeric end as decimal(10,2)) as \"payAmountUp\"," +
//
//                "pay_count as \"payCounts\"," +
//                "cast(case when pay_count_last_year =0 and pay_count>0 then 1 when pay_count_last_year=0 and pay_count=0 then 0 else (pay_count-pay_count_last_year)::numeric/pay_count_last_year::numeric end as decimal(10,2)) as \"payCountsUp\"," +
//
//                "cast(refund_amount as decimal(10,2)) as \"refundAmount\"," +
//                "cast(case when refund_amount_last_year =0 and refund_amount>0 then 1 when refund_amount_last_year=0 and refund_amount=0 then 0 else (refund_amount-refund_amount_last_year)::numeric/refund_amount_last_year::numeric end as decimal(10,2)) as \"refundAmountUp\"," +
//
//                "refund_count as \"refundCounts\"," +
//                "cast(case when refund_count_last_year =0 and refund_count>0 then 1 when refund_count_last_year=0 and refund_count=0 then 0 else (refund_count-refund_count_last_year)::numeric/refund_count_last_year::numeric end as decimal(10,2)) as \"refundCountsUp\""
//        );
//
//
//        params.getQueryParams().setYear_str(yearParamDto.getYear()+"");
//        params.getQueryParams().setMerchant_id(yearParamDto.getMerchantId()+"");
//        params.getQueryParams().setCombo_type(yearParamDto.getComboType());
//
//        params.getQueryParams().setProvince_id(yearParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(yearParamDto.getProvinceId()+"");}});
//        params.getQueryParams().setCity_id(yearParamDto.getCityId() == null ? null:new HashSet<String>(){{add(yearParamDto.getCityId()+"");}});
//        params.getQueryParams().setDistrict_id(yearParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(yearParamDto.getAreaId()+"");}});
//        params.getQueryParams().setEquipment_group_id(yearParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(yearParamDto.getGroupId()+"");}});
//        params.getQueryParams().setLyy_equipment_type_id(yearParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(yearParamDto.getEquipmentTypeId()+"");}});
//
//        if(yearParamDto.getGroupIds() != null && yearParamDto.getGroupIds().size()>0){
//            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//            if(yearParamDto.getGroupId() == null || !yearParamDto.getGroupIds().contains(yearParamDto.getGroupId()+"")){
//                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(yearParamDto.getGroupIds())).first()+"");}});
//            }
//            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
//            params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
//        }
//
//        try{
//            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
//            JSONObject result = oneDataServiceFeign.getData(params,headers);
//            log.info("{}",result.toJSONString());
//            return result;
//        }catch (Exception e){
//            log.warn("{}",e);
//        }
//
//
//
//        return null;
//    }
//
//    @Override
//    public JSONObject getDayOrderData(DayParamDto dayParamDto) {
//        MultiValueMap<String, String> headers = new HttpHeaders();
//        headers.add("sk",sk);
//        headers.add("ak",ak);
//
//        ParamDto params = new ParamDto();
//        params.setCode(dayOrderDayAnalyseCode);
//
//        params.setColumnArray("merchant_id as \"merchantId\", " +
//                "cast(pay_amount-refund_amount as decimal(10,2)) as \"payAmount\"," +
//                "day as \"day\" "
//        );
//
//        params.getPageParams().setPageRow("31");
//        params.getQueryParams().setMerchant_id(dayParamDto.getMerchantId()+"");
//        params.getQueryParams().setCombo_type(dayParamDto.getComboType());
//        params.getQueryParams().setDay(dayParamDto.getDay());
//
//        params.getQueryParams().setProvince_id(dayParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(dayParamDto.getProvinceId()+"");}});
//        params.getQueryParams().setCity_id(dayParamDto.getCityId() == null ? null:new HashSet<String>(){{add(dayParamDto.getCityId()+"");}});
//        params.getQueryParams().setDistrict_id(dayParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(dayParamDto.getAreaId()+"");}});
//        params.getQueryParams().setEquipment_group_id(dayParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(dayParamDto.getGroupId()+"");}});
//        params.getQueryParams().setLyy_equipment_type_id(dayParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(dayParamDto.getEquipmentTypeId()+"");}});
//        params.getRangeParams().getDay().setStart(dayParamDto.getMonth()+"-01");
//        params.getRangeParams().getDay().setEnd(dayParamDto.getMonth()+"-32");
//
//        if(dayParamDto.getGroupIds() != null && dayParamDto.getGroupIds().size()>0){
//            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//            if(dayParamDto.getGroupId() == null || !dayParamDto.getGroupIds().contains(dayParamDto.getGroupId()+"")){
//                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(dayParamDto.getGroupIds())).first()+"");}});
//            }
//            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
//            params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
//        }
//
//        try{
//            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
//            JSONObject result = oneDataServiceFeign.getData(params,headers);
//            log.info("{}",result.toJSONString());
//            return result;
//        }catch (Exception e){
//            log.warn("{}",e);
//        }
//
//
//
//        return null;
//    }
//
//    @Override
//    public JSONObject getPerWeekOrderData(WeekParamDto weekParamDto) {
//        MultiValueMap<String, String> headers = new HttpHeaders();
//        headers.add("sk",sk);
//        headers.add("ak",ak);
//
//        ParamDto params = new ParamDto();
//        params.setCode(weekOrderDayAnalyseCode);
//
//        params.setColumnArray("merchant_id as \"merchantId\", " +
//                "cast(pay_amount-refund_amount as decimal(10,2)) as \"payAmount\"," +
//                "month_week_num as \"week\" "
//        );
//
//        params.getPageParams().setPageRow("10");
//        params.getQueryParams().setMerchant_id(weekParamDto.getMerchantId()+"");
//        params.getQueryParams().setCombo_type(weekParamDto.getComboType());
//        params.getQueryParams().setYear_month(weekParamDto.getMonth());
//
//        params.getQueryParams().setProvince_id(weekParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(weekParamDto.getProvinceId()+"");}});
//        params.getQueryParams().setCity_id(weekParamDto.getCityId() == null ? null:new HashSet<String>(){{add(weekParamDto.getCityId()+"");}});
//        params.getQueryParams().setDistrict_id(weekParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(weekParamDto.getAreaId()+"");}});
//        params.getQueryParams().setEquipment_group_id(weekParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(weekParamDto.getGroupId()+"");}});
//        params.getQueryParams().setLyy_equipment_type_id(weekParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(weekParamDto.getEquipmentTypeId()+"");}});
//        //params.getRangeParams().getDay().setStart(dayParamDto.getMonth()+"-01");
//        //params.getRangeParams().getDay().setEnd(dayParamDto.getMonth()+"-32");
//
//
//        if(weekParamDto.getGroupIds() != null && weekParamDto.getGroupIds().size()>0){
//            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//            if(weekParamDto.getGroupId() == null || !weekParamDto.getGroupIds().contains(weekParamDto.getGroupId()+"")){
//                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(weekParamDto.getGroupIds())).first()+"");}});
//            }
//            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
//            params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
//        }
//
//        try{
//            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
//            JSONObject result = oneDataServiceFeign.getData(params,headers);
//            log.info("{}",result.toJSONString());
//            return result;
//        }catch (Exception e){
//            log.warn("{}",e);
//        }
//
//
//
//        return null;
//    }
//
//    @Override
//    public JSONObject getPerMonthOrderData(MonthParamDto monthParamDto) {
//        MultiValueMap<String, String> headers = new HttpHeaders();
//        headers.add("sk",sk);
//        headers.add("ak",ak);
//
//        ParamDto params = new ParamDto();
//        params.setCode(monthOrderDayAnalyseCode);
//
//        params.setColumnArray("merchant_id as \"merchantId\", " +
//                "cast(pay_amount-refund_amount as decimal(10,2)) as \"payAmount\"," +
//                "year_month as \"month\" "
//        );
//
//        params.getPageParams().setPageRow("20");
//        params.getQueryParams().setMerchant_id(monthParamDto.getMerchantId()+"");
//        params.getQueryParams().setCombo_type(monthParamDto.getComboType());
//        params.getQueryParams().setYear_month(monthParamDto.getMonth());
//
//        params.getQueryParams().setProvince_id(monthParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(monthParamDto.getProvinceId()+"");}});
//        params.getQueryParams().setCity_id(monthParamDto.getCityId() == null ? null:new HashSet<String>(){{add(monthParamDto.getCityId()+"");}});
//        params.getQueryParams().setDistrict_id(monthParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(monthParamDto.getAreaId()+"");}});
//        params.getQueryParams().setEquipment_group_id(monthParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(monthParamDto.getGroupId()+"");}});
//        params.getQueryParams().setLyy_equipment_type_id(monthParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(monthParamDto.getEquipmentTypeId()+"");}});
//        params.getRangeParams().getYear_month().setStart(monthParamDto.getYear()+"-01");
//        params.getRangeParams().getYear_month().setEnd(monthParamDto.getYear()+"-12");
//
//        if(monthParamDto.getGroupIds() != null && monthParamDto.getGroupIds().size()>0){
//            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//            if(monthParamDto.getGroupId() == null || !monthParamDto.getGroupIds().contains(monthParamDto.getGroupId()+"")){
//                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(monthParamDto.getGroupIds())).first()+"");}});
//            }
//            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
//            params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
//        }
//
//        try{
//            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
//            JSONObject result = oneDataServiceFeign.getData(params,headers);
//            log.info("{}",result.toJSONString());
//            return result;
//        }catch (Exception e){
//            log.warn("{}",e);
//        }
//
//
//
//        return null;
//    }
//
//    @Override
//    public JSONObject getPerYearOrderData(YearParamDto yearParamDto) {
//        MultiValueMap<String, String> headers = new HttpHeaders();
//        headers.add("sk",sk);
//        headers.add("ak",ak);
//
//        ParamDto params = new ParamDto();
//        params.setCode(yearOrderDayAnalyseCode);
//
//        params.setColumnArray("merchant_id as \"merchantId\", " +
//                "cast(pay_amount-refund_amount as decimal(10,2)) as \"payAmount\"," +
//                "year_str as \"year\" "
//        );
//
//        params.getPageParams().setPageRow("10");
//        params.getQueryParams().setMerchant_id(yearParamDto.getMerchantId()+"");
//        params.getQueryParams().setCombo_type(yearParamDto.getComboType());
//        params.getQueryParams().setYear_str(yearParamDto.getYear()+"");
//
//        params.getQueryParams().setProvince_id(yearParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(yearParamDto.getProvinceId()+"");}});
//        params.getQueryParams().setCity_id(yearParamDto.getCityId() == null ? null:new HashSet<String>(){{add(yearParamDto.getCityId()+"");}});
//        params.getQueryParams().setDistrict_id(yearParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(yearParamDto.getAreaId()+"");}});
//        params.getQueryParams().setEquipment_group_id(yearParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(yearParamDto.getGroupId()+"");}});
//        params.getQueryParams().setLyy_equipment_type_id(yearParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(yearParamDto.getEquipmentTypeId()+"");}});
//        //params.getRangeParams().getYear_month().setStart(monthParamDto.getYear()+"-01");
//        //params.getRangeParams().getYear_month().setEnd(monthParamDto.getYear()+"-12");
//
//        if(yearParamDto.getGroupIds() != null && yearParamDto.getGroupIds().size()>0){
//            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//            if(yearParamDto.getGroupId() == null || !yearParamDto.getGroupIds().contains(yearParamDto.getGroupId()+"")){
//                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(yearParamDto.getGroupIds())).first()+"");}});
//            }
//            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
//            params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
//        }
//
//        try{
//            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
//            JSONObject result = oneDataServiceFeign.getData(params,headers);
//            log.info("{}",result.toJSONString());
//            return result;
//        }catch (Exception e){
//            log.warn("{}",e);
//        }
//
//
//
//        return null;
//    }
//
//    @Override
//    public JSONObject getGroupEquipmentData(DayParamDto dayParamDto) {
//        MultiValueMap<String, String> headers = new HttpHeaders();
//        headers.add("sk",sk);
//        headers.add("ak",ak);
//
//        ParamDto params = new ParamDto();
//        params.setCode(dayGroupEquipmentAnalyseCode);
//
//        params.setColumnArray("merchant_id as \"merchantId\", " +
//
//                "pay_equipment as \"payEquipments\"," +     //支付设备
//                "cast(case when pay_equipment_yesterday =0 and pay_equipment>0 then 1 when pay_equipment_yesterday=0 and pay_equipment=0 then 0 else (pay_equipment-pay_equipment_yesterday)::numeric/pay_equipment_yesterday::numeric end as decimal(10,2)) as \"payEquipmentsUp\"," +
//
//                "equipment_count as \"equipmentCounts\", " +        //设备总数
//                "cast(case when equipment_count_yesterday =0 and equipment_count>0 then 1 when equipment_count_yesterday=0 and equipment_count=0 then 0 else (equipment_count-equipment_count_yesterday)::numeric/equipment_count_yesterday::numeric end as decimal(10,2)) as \"equipmentCountsUp\"," +
//
//                "online_00_position_num as \"onlineEquipmentCounts\", " +       //在线设备数
//                "cast(case when online_00_position_num_yesterday =0 and online_00_position_num>0 then 1 when online_00_position_num_yesterday=0 and online_00_position_num=0 then 0 else (online_00_position_num-online_00_position_num_yesterday)::numeric/online_00_position_num_yesterday::numeric end as decimal(10,2)) as \"onlineEquipmentCountsUp\"," +
//
//                "total_start_counts as \"startCounts\", " +       //启动次数
//                "cast(case when total_start_counts_yesterday =0 and total_start_counts>0 then 1 when total_start_counts_yesterday=0 and total_start_counts=0 then 0 else (total_start_counts-total_start_counts_yesterday)::numeric/total_start_counts_yesterday::numeric end as decimal(10,2)) as \"startCountsUp\"," +
//                //每台设备平均启动次数
//                "cast(start_per_equipment as decimal(10,2)) as \"perEquipmentStarts\"," +   //设备启动均次
//                "cast(case when start_per_equipment_yesterday =0 and start_per_equipment>0 then 1 when start_per_equipment_yesterday=0 and start_per_equipment=0 then 0 else (start_per_equipment-start_per_equipment_yesterday)::numeric/start_per_equipment_yesterday::numeric end as decimal(10,2)) as \"perEquipmentStartsUp\"," +
//
//                "total_start_counts/1 as \"perEquipmentDayStarts\"," +   //设备日均启动均次 设备启动次数/账期日
//                "cast(case when total_start_counts_yesterday =0 and total_start_counts>0 then 1 when total_start_counts_yesterday=0 and total_start_counts=0 then 0 else (total_start_counts-total_start_counts_yesterday)::numeric/total_start_counts_yesterday::numeric end as decimal(10,2)) as \"perEquipmentDayStartsUp\"," +
//
//                "equipment_group_count as \"groupCounts\"," +        //场地总数
//                "cast(case when equipment_group_count_yesterday =0 and equipment_group_count>0 then 1 when equipment_group_count_yesterday=0 and equipment_group_count=0 then 0 else (equipment_group_count-equipment_group_count_yesterday)::numeric/equipment_group_count_yesterday::numeric end as decimal(10,2)) as \"groupCountsUp\"," +
//
//                "cast(case when equipment_group_count=0 then 0 else pay_amount::numeric/equipment_group_count::numeric end as decimal(10,2)) as \"perGroupAmount\"," +        //场地均收
//                "case when (equipment_group_count =0 or pay_amount=0) and (equipment_group_count_yesterday = 0 or pay_amount_yesterday=0) then 0 " +
//                "when (equipment_group_count_yesterday = 0 or pay_amount_yesterday=0) and pay_amount > 0 then 1 " +
//                "else cast( ((pay_amount::numeric/equipment_group_count::numeric) - (pay_amount_yesterday::numeric/equipment_group_count_yesterday::numeric))::numeric/(pay_amount_yesterday::numeric/equipment_group_count_yesterday::numeric)::numeric as decimal(10,2)) end  as \"perGroupAmountUp\","+
//
//                "cast(case when equipment_count=0 then 0 else pay_amount::numeric/equipment_count::numeric end as decimal(10,2)) as \"perEquipmentAmount\"," +        //设备均收
//                "case when (equipment_count =0 or pay_amount=0) and (equipment_count_yesterday = 0 or pay_amount_yesterday=0) then 0 " +
//                "when (equipment_count_yesterday = 0 or pay_amount_yesterday=0) and pay_amount > 0 then 1 " +
//                "else cast( ((pay_amount::numeric/equipment_count::numeric) - (pay_amount_yesterday::numeric/equipment_count_yesterday::numeric))::numeric/(pay_amount_yesterday::numeric/equipment_count_yesterday::numeric)::numeric as decimal(10,2)) end  as \"perEquipmentAmountUp\","+
//
//                "cast(case when equipment_group_count=0 then 0 else order_count::numeric/equipment_group_count::numeric end as decimal(10,2)) as \"perGroupOrder\"," +        //场地订单均数
//                "case when (equipment_group_count =0 or order_count=0) and (equipment_group_count_yesterday = 0 or order_count_yesterday=0) then 0 " +
//                "when (equipment_group_count_yesterday = 0 or order_count_yesterday=0) and order_count > 0 then 1 " +
//                "else cast( ((order_count::numeric/equipment_group_count::numeric) - (order_count_yesterday::numeric/equipment_group_count_yesterday::numeric))::numeric/(order_count_yesterday::numeric/equipment_group_count_yesterday::numeric)::numeric as decimal(10,2)) end  as \"perGroupOrderUp\","+
//
//
//                "cast(case when equipment_count=0 then 0 else order_count::numeric/equipment_count::numeric end as decimal(10,2)) as \"perEquipmentOrder\"," +        //设备订单均数
//                "case when (equipment_count =0 or order_count=0) and (equipment_count_yesterday = 0 or order_count_yesterday=0) then 0 " +
//                "when (equipment_count_yesterday = 0 or order_count_yesterday=0) and order_count > 0 then 1 " +
//                "else cast( ((order_count::numeric/equipment_count::numeric) - (order_count_yesterday::numeric/equipment_count_yesterday::numeric))::numeric/(order_count_yesterday::numeric/equipment_count_yesterday::numeric)::numeric as decimal(10,2)) end  as \"perEquipmentOrderUp\","+
//
//
//
//                "cast(case when equipment_group_count=0 then 0 else total_start_counts::numeric/equipment_group_count::numeric end as decimal(10,2)) as \"perGroupStart\"," +        //场地启动均次
//                "case when (equipment_group_count_yesterday = 0 or total_start_counts_yesterday=0) and (total_start_counts = 0 or equipment_group_count =0) then 0 when (equipment_group_count_yesterday = 0 or total_start_counts_yesterday=0) and total_start_counts > 0 then 1 " +
//                "else cast( ((total_start_counts::numeric/equipment_group_count::numeric) - (total_start_counts_yesterday::numeric/equipment_group_count_yesterday::numeric))/(total_start_counts_yesterday::numeric/equipment_group_count_yesterday::numeric) as decimal(10,2)) end  as \"perGroupStartUp\","+
//
//
//                "cast(case when equipment_group_count=0 then 0 else equipment_count::numeric/equipment_group_count::numeric end as decimal(10,2)) as \"perGroupEquipment\","+        //场地设备均数
//                "case when (equipment_group_count_yesterday =0 or equipment_count_yesterday =0) and (equipment_count=0 or equipment_group_count=0) then 0 "+
//                "when (equipment_group_count_yesterday = 0 or equipment_count_yesterday=0) and equipment_count > 0 then 1 " +
//                "else cast( ((equipment_count::numeric/equipment_group_count::numeric) - (equipment_count_yesterday::numeric/equipment_group_count_yesterday::numeric))/(equipment_count_yesterday::numeric/equipment_group_count_yesterday::numeric) as decimal(10,2)) end as \"perGroupEquipmentUp\""
//
//
//        );
//
//
//        params.getQueryParams().setDay(dayParamDto.getDay());
//        params.getQueryParams().setMerchant_id(dayParamDto.getMerchantId()+"");
//        params.getQueryParams().setCombo_type(dayParamDto.getComboType());
//
//        params.getQueryParams().setProvince_id(dayParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(dayParamDto.getProvinceId()+"");}});
//        params.getQueryParams().setCity_id(dayParamDto.getCityId() == null ? null:new HashSet<String>(){{add(dayParamDto.getCityId()+"");}});
//        params.getQueryParams().setDistrict_id(dayParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(dayParamDto.getAreaId()+"");}});
//        params.getQueryParams().setEquipment_group_id(dayParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(dayParamDto.getGroupId()+"");}});
//        params.getQueryParams().setLyy_equipment_type_id(dayParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(dayParamDto.getEquipmentTypeId()+"");}});
//
//        if(dayParamDto.getGroupIds() != null && dayParamDto.getGroupIds().size()>0){
//            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//            if(dayParamDto.getGroupId() == null || !dayParamDto.getGroupIds().contains(dayParamDto.getGroupId()+"")){
//                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(dayParamDto.getGroupIds())).first()+"");}});
//            }
//            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
//            params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
//        }
//
//        try{
//            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
//            JSONObject result = oneDataServiceFeign.getData(params,headers);
//            log.info("{}",result.toJSONString());
//            return result;
//        }catch (Exception e){
//            log.warn("{}",e);
//        }
//
//
//
//        return null;
//    }
//
//
//    @Override
//    public JSONObject getWeekGroupEquipmentData(WeekParamDto weekParamDto) {
//        MultiValueMap<String, String> headers = new HttpHeaders();
//        headers.add("sk",sk);
//        headers.add("ak",ak);
//
//        ParamDto params = new ParamDto();
//        params.setCode(weekGroupEquipmentAnalyseCode);
//
//        params.setColumnArray("merchant_id as \"merchantId\", " +
//
//                "pay_equipment as \"payEquipments\"," +     //支付设备
//                "cast(case when last_cycle_pay_equipment =0 and pay_equipment>0 then 1 when last_cycle_pay_equipment=0 and pay_equipment=0 then 0 else (pay_equipment-last_cycle_pay_equipment)::numeric/last_cycle_pay_equipment::numeric end as decimal(10,2)) as \"payEquipmentsUp\"," +
//
//                "equipment_count as \"equipmentCounts\", " +        //设备总数
//                "cast(case when last_cycle_equipment_count =0 and equipment_count>0 then 1 when last_cycle_equipment_count=0 and equipment_count=0 then 0 else (equipment_count-last_cycle_equipment_count)::numeric/last_cycle_equipment_count::numeric end as decimal(10,2)) as \"equipmentCountsUp\"," +
//
//                "online_00_position_num as \"onlineEquipmentCounts\", " +       //在线设备数
//                "cast(case when last_cycle_online_00_position_num =0 and online_00_position_num>0 then 1 when last_cycle_online_00_position_num=0 and online_00_position_num=0 then 0 else (online_00_position_num-last_cycle_online_00_position_num)::numeric/last_cycle_online_00_position_num::numeric end as decimal(10,2)) as \"onlineEquipmentCountsUp\"," +
//
//                "total_start_counts as \"startCounts\", " +       //启动次数
//                "cast(case when last_cycle_total_start_counts =0 and total_start_counts>0 then 1 when last_cycle_total_start_counts=0 and total_start_counts=0 then 0 else (total_start_counts-last_cycle_total_start_counts)::numeric/last_cycle_total_start_counts::numeric end as decimal(10,2)) as \"startCountsUp\"," +
//                //每台设备平均启动次数
//                "cast(start_per_equipment as decimal(10,2)) as \"perEquipmentStarts\"," +   //设备启动均次
//                "cast(case when last_cycle_start_per_equipment =0 and start_per_equipment>0 then 1 when last_cycle_start_per_equipment=0 and start_per_equipment=0 then 0 else (start_per_equipment-last_cycle_start_per_equipment)::numeric/last_cycle_start_per_equipment::numeric end as decimal(10,2)) as \"perEquipmentStartsUp\"," +
//
//                "cast(total_start_counts::numeric/7 as decimal(10,2)) as \"perEquipmentDayStarts\"," +   //设备日均启动均次 设备启动次数/账期日
//                "cast(case when last_cycle_total_start_counts =0 and total_start_counts>0 then 1 when last_cycle_total_start_counts=0 and total_start_counts=0 then 0 else (total_start_counts-last_cycle_total_start_counts)::numeric/last_cycle_total_start_counts::numeric end as decimal(10,2)) as \"perEquipmentDayStartsUp\"," +
//
//                "equipment_group_count as \"groupCounts\"," +        //场地总数
//                "cast(case when last_cycle_equipment_group_count =0 and equipment_group_count>0 then 1 when last_cycle_equipment_group_count=0 and equipment_group_count=0 then 0 else (equipment_group_count-last_cycle_equipment_group_count)::numeric/last_cycle_equipment_group_count::numeric end as decimal(10,2)) as \"groupCountsUp\"," +
//
//                "cast(case when equipment_group_count=0 then 0 else pay_amount::numeric/equipment_group_count::numeric end as decimal(10,2)) as \"perGroupAmount\"," +        //场地均收
//                "case when (equipment_group_count =0 or pay_amount=0) and (last_cycle_equipment_group_count = 0 or last_cycle_pay_amount=0) then 0 " +
//                "when (last_cycle_equipment_group_count = 0 or last_cycle_pay_amount=0) and pay_amount > 0 then 1 " +
//                "else cast( ((pay_amount::numeric/equipment_group_count::numeric) - (last_cycle_pay_amount::numeric/last_cycle_equipment_group_count::numeric))::numeric/(last_cycle_pay_amount::numeric/last_cycle_equipment_group_count::numeric)::numeric as decimal(10,2)) end  as \"perGroupAmountUp\","+
//
//
//                "cast(case when equipment_count=0 then 0 else pay_amount::numeric/equipment_count::numeric end as decimal(10,2)) as \"perEquipmentAmount\"," +        //设备均收
//                "case when (equipment_count =0 or pay_amount=0) and (last_cycle_equipment_count = 0 or last_cycle_pay_amount=0) then 0 " +
//                "when (last_cycle_equipment_count = 0 or last_cycle_pay_amount=0) and pay_amount > 0 then 1 " +
//                "else cast( ((pay_amount::numeric/equipment_count::numeric) - (last_cycle_pay_amount::numeric/last_cycle_equipment_count::numeric))::numeric/(last_cycle_pay_amount::numeric/last_cycle_equipment_count::numeric)::numeric as decimal(10,2)) end  as \"perEquipmentAmountUp\","+
//
//
//
//
//                "cast(case when equipment_count=0 then 0 else order_count::numeric/equipment_count::numeric end as decimal(10,2)) as \"perEquipmentOrder\"," +        //设备订单均数
//                "case when (equipment_count =0 or order_count=0) and (last_cycle_equipment_count = 0 or last_cycle_order_count=0) then 0 " +
//                "when (last_cycle_equipment_count = 0 or last_cycle_order_count=0) and order_count > 0 then 1 " +
//                "else cast( ((order_count::numeric/equipment_count::numeric) - (last_cycle_order_count::numeric/last_cycle_equipment_count::numeric))::numeric/(last_cycle_order_count::numeric/last_cycle_equipment_count::numeric)::numeric as decimal(10,2)) end  as \"perEquipmentOrderUp\","+
//
//
//
//                "cast(case when equipment_group_count=0 then 0 else order_count::numeric/equipment_group_count::numeric end as decimal(10,2)) as \"perGroupOrder\"," +        //场地订单均数
//                "case when (equipment_group_count =0 or order_count=0) and (last_cycle_equipment_group_count = 0 or last_cycle_order_count=0) then 0 " +
//                "when (last_cycle_equipment_group_count = 0 or last_cycle_order_count=0) and order_count > 0 then 1 " +
//                "else cast( ((order_count::numeric/equipment_group_count::numeric) - (last_cycle_order_count::numeric/last_cycle_equipment_group_count::numeric))::numeric/(last_cycle_order_count::numeric/last_cycle_equipment_group_count::numeric)::numeric as decimal(10,2)) end  as \"perGroupOrderUp\","+
//
//
//                "cast(case when equipment_group_count=0 then 0 else total_start_counts::numeric/equipment_group_count::numeric end as decimal(10,2)) as \"perGroupStart\"," +        //场地启动均次
//                "case when (last_cycle_equipment_group_count = 0 or last_cycle_total_start_counts=0) and (total_start_counts = 0 or equipment_group_count =0) then 0 when (last_cycle_equipment_group_count = 0 or last_cycle_total_start_counts=0) and total_start_counts > 0 then 1 " +
//                "else cast( ((total_start_counts::numeric/equipment_group_count::numeric) - (last_cycle_total_start_counts::numeric/last_cycle_equipment_group_count::numeric))/(last_cycle_total_start_counts::numeric/last_cycle_equipment_group_count::numeric) as decimal(10,2)) end  as \"perGroupStartUp\","+
//
//
//                "cast(case when equipment_group_count=0 then 0 else equipment_count::numeric/equipment_group_count::numeric end as decimal(10,2)) as \"perGroupEquipment\","+        //场地设备均数
//                "case when (last_cycle_equipment_group_count =0 or last_cycle_equipment_count =0) and (equipment_count=0 or equipment_group_count=0) then 0 "+
//                "when (last_cycle_equipment_group_count = 0 or last_cycle_equipment_count=0) and equipment_count > 0 then 1 " +
//                "else cast( ((equipment_count::numeric/equipment_group_count::numeric) - (last_cycle_equipment_count::numeric/last_cycle_equipment_group_count::numeric))/(last_cycle_equipment_count::numeric/last_cycle_equipment_group_count::numeric) as decimal(10,2)) end as \"perGroupEquipmentUp\""
//
//
//        );
//
//
//        params.getQueryParams().setYear_month(weekParamDto.getMonth());
//        params.getQueryParams().setMonth_week_num(weekParamDto.getWeek()+"");
//        params.getQueryParams().setMerchant_id(weekParamDto.getMerchantId()+"");
//        params.getQueryParams().setCombo_type(weekParamDto.getComboType());
//
//        params.getQueryParams().setProvince_id(weekParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(weekParamDto.getProvinceId()+"");}});
//        params.getQueryParams().setCity_id(weekParamDto.getCityId() == null ? null:new HashSet<String>(){{add(weekParamDto.getCityId()+"");}});
//        params.getQueryParams().setDistrict_id(weekParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(weekParamDto.getAreaId()+"");}});
//        params.getQueryParams().setEquipment_group_id(weekParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(weekParamDto.getGroupId()+"");}});
//        params.getQueryParams().setLyy_equipment_type_id(weekParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(weekParamDto.getEquipmentTypeId()+"");}});
//
//        if(weekParamDto.getGroupIds() != null && weekParamDto.getGroupIds().size()>0){
//            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//            if(weekParamDto.getGroupId() == null || !weekParamDto.getGroupIds().contains(weekParamDto.getGroupId()+"")){
//                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(weekParamDto.getGroupIds())).first()+"");}});
//            }
//            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
//            params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
//        }
//
//        try{
//            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
//            JSONObject result = oneDataServiceFeign.getData(params,headers);
//            log.info("{}",result.toJSONString());
//            return result;
//        }catch (Exception e){
//            log.warn("{}",e);
//        }
//
//
//
//        return null;
//    }
//
//    @Override
//    public JSONObject getMonthGroupEquipmentData(MonthParamDto monthParamDto) {
//        MultiValueMap<String, String> headers = new HttpHeaders();
//        headers.add("sk",sk);
//        headers.add("ak",ak);
//
//        ParamDto params = new ParamDto();
//        params.setCode(monthGroupEquipmentAnalyseCode);
//
//        params.setColumnArray("merchant_id as \"merchantId\", " +
//
//                "pay_equipment as \"payEquipments\"," +     //支付设备
//                "cast(case when last_cycle_pay_equipment =0 and pay_equipment>0 then 1 when last_cycle_pay_equipment=0 and pay_equipment=0 then 0 else (pay_equipment-last_cycle_pay_equipment)::numeric/last_cycle_pay_equipment::numeric end as decimal(10,2)) as \"payEquipmentsUp\"," +
//
//                "equipment_count as \"equipmentCounts\", " +        //设备总数
//                "cast(case when last_cycle_equipment_count =0 and equipment_count>0 then 1 when last_cycle_equipment_count=0 and equipment_count=0 then 0 else (equipment_count-last_cycle_equipment_count)::numeric/last_cycle_equipment_count::numeric end as decimal(10,2)) as \"equipmentCountsUp\"," +
//
//                "online_00_position_num as \"onlineEquipmentCounts\", " +       //在线设备数
//                "cast(case when last_cycle_online_00_position_num =0 and online_00_position_num>0 then 1 when last_cycle_online_00_position_num=0 and online_00_position_num=0 then 0 else (online_00_position_num-last_cycle_online_00_position_num)::numeric/last_cycle_online_00_position_num::numeric end as decimal(10,2)) as \"onlineEquipmentCountsUp\"," +
//
//                "total_start_counts as \"startCounts\", " +       //启动次数
//                "cast(case when last_cycle_total_start_counts =0 and total_start_counts>0 then 1 when last_cycle_total_start_counts=0 and total_start_counts=0 then 0 else (total_start_counts-last_cycle_total_start_counts)::numeric/last_cycle_total_start_counts::numeric end as decimal(10,2)) as \"startCountsUp\"," +
//                //每台设备平均启动次数
//                "cast(start_per_equipment as decimal(10,2)) as \"perEquipmentStarts\"," +   //设备启动均次
//                "cast(case when last_cycle_start_per_equipment =0 and start_per_equipment>0 then 1 when last_cycle_start_per_equipment=0 and start_per_equipment=0 then 0 else (start_per_equipment-last_cycle_start_per_equipment)::numeric/last_cycle_start_per_equipment::numeric end as decimal(10,2)) as \"perEquipmentStartsUp\"," +
//
//                "cast(total_start_counts::numeric/30 as decimal(10,2)) as \"perEquipmentDayStarts\"," +   //设备日均启动均次 设备启动次数/账期日
//                "cast(case when last_cycle_total_start_counts =0 and total_start_counts>0 then 1 when last_cycle_total_start_counts=0 and total_start_counts=0 then 0 else (total_start_counts-last_cycle_total_start_counts)::numeric/last_cycle_total_start_counts::numeric end as decimal(10,2)) as \"perEquipmentDayStartsUp\"," +
//
//                "equipment_group_count as \"groupCounts\"," +        //场地总数
//                "cast(case when last_cycle_equipment_group_count =0 and equipment_group_count>0 then 1 when last_cycle_equipment_group_count=0 and equipment_group_count=0 then 0 else (equipment_group_count-last_cycle_equipment_group_count)::numeric/last_cycle_equipment_group_count::numeric end as decimal(10,2)) as \"groupCountsUp\"," +
//
//                "cast(case when equipment_group_count=0 then 0 else pay_amount::numeric/equipment_group_count::numeric end as decimal(10,2)) as \"perGroupAmount\"," +        //场地均收
//                "case when (equipment_group_count =0 or pay_amount=0) and (last_cycle_equipment_group_count = 0 or last_cycle_pay_amount=0) then 0 " +
//                "when (last_cycle_equipment_group_count = 0 or last_cycle_pay_amount=0) and pay_amount > 0 then 1 " +
//                "else cast( ((pay_amount::numeric/equipment_group_count::numeric) - (last_cycle_pay_amount::numeric/last_cycle_equipment_group_count::numeric))::numeric/(last_cycle_pay_amount::numeric/last_cycle_equipment_group_count::numeric)::numeric as decimal(10,2)) end  as \"perGroupAmountUp\","+
//
//
//                "cast(case when equipment_group_count=0 then 0 else order_count::numeric/equipment_group_count::numeric end as decimal(10,2)) as \"perGroupOrder\"," +        //场地订单均数
//                "case when (equipment_group_count =0 or order_count=0) and (last_cycle_equipment_group_count = 0 or last_cycle_order_count=0) then 0 " +
//                "when (last_cycle_equipment_group_count = 0 or last_cycle_order_count=0) and order_count > 0 then 1 " +
//                "else cast( ((order_count::numeric/equipment_group_count::numeric) - (last_cycle_order_count::numeric/last_cycle_equipment_group_count::numeric))::numeric/(last_cycle_order_count::numeric/last_cycle_equipment_group_count::numeric)::numeric as decimal(10,2)) end  as \"perGroupOrderUp\","+
//
//
//                "cast(case when equipment_count=0 then 0 else pay_amount::numeric/equipment_count::numeric end as decimal(10,2)) as \"perEquipmentAmount\"," +        //设备均收
//                "case when (equipment_count =0 or pay_amount=0) and (last_cycle_equipment_count = 0 or last_cycle_pay_amount=0) then 0 " +
//                "when (last_cycle_equipment_count = 0 or last_cycle_pay_amount=0) and pay_amount > 0 then 1 " +
//                "else cast( ((pay_amount::numeric/equipment_count::numeric) - (last_cycle_pay_amount::numeric/last_cycle_equipment_count::numeric))::numeric/(last_cycle_pay_amount::numeric/last_cycle_equipment_count::numeric)::numeric as decimal(10,2)) end  as \"perEquipmentAmountUp\","+
//
//
//
//
//                "cast(case when equipment_count=0 then 0 else order_count::numeric/equipment_count::numeric end as decimal(10,2)) as \"perEquipmentOrder\"," +        //设备订单均数
//                "case when (equipment_count =0 or order_count=0) and (last_cycle_equipment_count = 0 or last_cycle_order_count=0) then 0 " +
//                "when (last_cycle_equipment_count = 0 or last_cycle_order_count=0) and order_count > 0 then 1 " +
//                "else cast( ((order_count::numeric/equipment_count::numeric) - (last_cycle_order_count::numeric/last_cycle_equipment_count::numeric))::numeric/(last_cycle_order_count::numeric/last_cycle_equipment_count::numeric)::numeric as decimal(10,2)) end  as \"perEquipmentOrderUp\","+
//
//
//
//                "cast(case when equipment_group_count=0 then 0 else total_start_counts::numeric/equipment_group_count::numeric end as decimal(10,2)) as \"perGroupStart\"," +        //场地启动均次
//                "case when (last_cycle_equipment_group_count = 0 or last_cycle_total_start_counts=0) and (total_start_counts = 0 or equipment_group_count =0) then 0 when (last_cycle_equipment_group_count = 0 or last_cycle_total_start_counts=0) and total_start_counts > 0 then 1 " +
//                "else cast( ((total_start_counts::numeric/equipment_group_count::numeric) - (last_cycle_total_start_counts::numeric/last_cycle_equipment_group_count::numeric))/(last_cycle_total_start_counts::numeric/last_cycle_equipment_group_count::numeric) as decimal(10,2)) end  as \"perGroupStartUp\","+
//
//
//                "cast(case when equipment_group_count=0 then 0 else equipment_count::numeric/equipment_group_count::numeric end as decimal(10,2)) as \"perGroupEquipment\","+        //场地设备均数
//                "case when (last_cycle_equipment_group_count =0 or last_cycle_equipment_count =0) and (equipment_count=0 or equipment_group_count=0) then 0 "+
//                "when (last_cycle_equipment_group_count = 0 or last_cycle_equipment_count=0) and equipment_count > 0 then 1 " +
//                "else cast( ((equipment_count::numeric/equipment_group_count::numeric) - (last_cycle_equipment_count::numeric/last_cycle_equipment_group_count::numeric))/(last_cycle_equipment_count::numeric/last_cycle_equipment_group_count::numeric) as decimal(10,2)) end as \"perGroupEquipmentUp\""
//
//
//        );
//
//
//        params.getQueryParams().setYear_month(monthParamDto.getMonth());
//        params.getQueryParams().setMerchant_id(monthParamDto.getMerchantId()+"");
//        params.getQueryParams().setCombo_type(monthParamDto.getComboType());
//
//        params.getQueryParams().setProvince_id(monthParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(monthParamDto.getProvinceId()+"");}});
//        params.getQueryParams().setCity_id(monthParamDto.getCityId() == null ? null:new HashSet<String>(){{add(monthParamDto.getCityId()+"");}});
//        params.getQueryParams().setDistrict_id(monthParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(monthParamDto.getAreaId()+"");}});
//        params.getQueryParams().setEquipment_group_id(monthParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(monthParamDto.getGroupId()+"");}});
//        params.getQueryParams().setLyy_equipment_type_id(monthParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(monthParamDto.getEquipmentTypeId()+"");}});
//
//        if(monthParamDto.getGroupIds() != null && monthParamDto.getGroupIds().size()>0){
//            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//            if(monthParamDto.getGroupId() == null || !monthParamDto.getGroupIds().contains(monthParamDto.getGroupId()+"")){
//                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(monthParamDto.getGroupIds())).first()+"");}});
//            }
//            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
//            params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
//        }
//
//        try{
//            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
//            JSONObject result = oneDataServiceFeign.getData(params,headers);
//            log.info("{}",result.toJSONString());
//            return result;
//        }catch (Exception e){
//            log.warn("{}",e);
//        }
//
//
//
//        return null;
//    }
//
//
//    @Override
//    public JSONObject getYearGroupEquipmentData(YearParamDto yearParamDto) {
//        MultiValueMap<String, String> headers = new HttpHeaders();
//        headers.add("sk",sk);
//        headers.add("ak",ak);
//
//        ParamDto params = new ParamDto();
//        params.setCode(yearGroupEquipmentAnalyseCode);
//
//        params.setColumnArray("merchant_id as \"merchantId\", " +
//
//                "pay_equipment as \"payEquipments\"," +     //支付设备
//                "cast(case when last_cycle_pay_equipment =0 and pay_equipment>0 then 1 when last_cycle_pay_equipment=0 and pay_equipment=0 then 0 else (pay_equipment-last_cycle_pay_equipment)::numeric/last_cycle_pay_equipment::numeric end as decimal(10,2)) as \"payEquipmentsUp\"," +
//
//                "equipment_count as \"equipmentCounts\", " +        //设备总数
//                "cast(case when last_cycle_equipment_count =0 and equipment_count>0 then 1 when last_cycle_equipment_count=0 and equipment_count=0 then 0 else (equipment_count-last_cycle_equipment_count)::numeric/last_cycle_equipment_count::numeric end as decimal(10,2)) as \"equipmentCountsUp\"," +
//
//                "online_00_position_num as \"onlineEquipmentCounts\", " +       //在线设备数
//                "cast(case when last_cycle_online_00_position_num =0 and online_00_position_num>0 then 1 when last_cycle_online_00_position_num=0 and online_00_position_num=0 then 0 else (online_00_position_num-last_cycle_online_00_position_num)::numeric/last_cycle_online_00_position_num::numeric end as decimal(10,2)) as \"onlineEquipmentCountsUp\"," +
//
//                "total_start_counts as \"startCounts\", " +       //启动次数
//                "cast(case when last_cycle_total_start_counts =0 and total_start_counts>0 then 1 when last_cycle_total_start_counts=0 and total_start_counts=0 then 0 else (total_start_counts-last_cycle_total_start_counts)::numeric/last_cycle_total_start_counts::numeric end as decimal(10,2)) as \"startCountsUp\"," +
//                //每台设备平均启动次数
//                "cast(start_per_equipment as decimal(10,2)) as \"perEquipmentStarts\"," +   //设备启动均次
//                "cast(case when last_cycle_start_per_equipment =0 and start_per_equipment>0 then 1 when last_cycle_start_per_equipment=0 and start_per_equipment=0 then 0 else (start_per_equipment-last_cycle_start_per_equipment)::numeric/last_cycle_start_per_equipment::numeric end as decimal(10,2)) as \"perEquipmentStartsUp\"," +
//
//                "cast(total_start_counts::numeric/365 as decimal(10,2)) as \"perEquipmentDayStarts\"," +   //设备日均启动均次 设备启动次数/账期日
//                "cast(case when last_cycle_total_start_counts =0 and total_start_counts>0 then 1 when last_cycle_total_start_counts=0 and total_start_counts=0 then 0 else (total_start_counts-last_cycle_total_start_counts)::numeric/last_cycle_total_start_counts::numeric end as decimal(10,2)) as \"perEquipmentDayStartsUp\"," +
//
//                "equipment_group_count as \"groupCounts\"," +        //场地总数
//                "cast(case when last_cycle_equipment_group_count =0 and equipment_group_count>0 then 1 when last_cycle_equipment_group_count=0 and equipment_group_count=0 then 0 else (equipment_group_count-last_cycle_equipment_group_count)::numeric/last_cycle_equipment_group_count::numeric end as decimal(10,2)) as \"groupCountsUp\"," +
//
//                "cast(case when equipment_group_count=0 then 0 else pay_amount::numeric/equipment_group_count::numeric end as decimal(10,2)) as \"perGroupAmount\"," +        //场地均收
//                "case when (equipment_group_count =0 or pay_amount=0) and (last_cycle_equipment_group_count = 0 or last_cycle_pay_amount=0) then 0 " +
//                "when (last_cycle_equipment_group_count = 0 or last_cycle_pay_amount=0) and pay_amount > 0 then 1 " +
//                "else cast( ((pay_amount::numeric/equipment_group_count::numeric) - (last_cycle_pay_amount::numeric/last_cycle_equipment_group_count::numeric))::numeric/(last_cycle_pay_amount::numeric/last_cycle_equipment_group_count::numeric)::numeric as decimal(10,2)) end  as \"perGroupAmountUp\","+
//
//
//                "cast(case when equipment_group_count=0 then 0 else order_count::numeric/equipment_group_count::numeric end as decimal(10,2)) as \"perGroupOrder\"," +        //场地订单均数
//                "case when (equipment_group_count =0 or order_count=0) and (last_cycle_equipment_group_count = 0 or last_cycle_order_count=0) then 0 " +
//                "when (last_cycle_equipment_group_count = 0 or last_cycle_order_count=0) and order_count > 0 then 1 " +
//                "else cast( ((order_count::numeric/equipment_group_count::numeric) - (last_cycle_order_count::numeric/last_cycle_equipment_group_count::numeric))::numeric/(last_cycle_order_count::numeric/last_cycle_equipment_group_count::numeric)::numeric as decimal(10,2)) end  as \"perGroupOrderUp\","+
//
//
//                "cast(case when equipment_count=0 then 0 else pay_amount::numeric/equipment_count::numeric end as decimal(10,2)) as \"perEquipmentAmount\"," +        //设备均收
//                "case when (equipment_count =0 or pay_amount=0) and (last_cycle_equipment_count = 0 or last_cycle_pay_amount=0) then 0 " +
//                "when (last_cycle_equipment_count = 0 or last_cycle_pay_amount=0) and pay_amount > 0 then 1 " +
//                "else cast( ((pay_amount::numeric/equipment_count::numeric) - (last_cycle_pay_amount::numeric/last_cycle_equipment_count::numeric))::numeric/(last_cycle_pay_amount::numeric/last_cycle_equipment_count::numeric)::numeric as decimal(10,2)) end  as \"perEquipmentAmountUp\","+
//
//
//
//
//                "cast(case when equipment_count=0 then 0 else order_count::numeric/equipment_count::numeric end as decimal(10,2)) as \"perEquipmentOrder\"," +        //设备订单均数
//                "case when (equipment_count =0 or order_count=0) and (last_cycle_equipment_count = 0 or last_cycle_order_count=0) then 0 " +
//                "when (last_cycle_equipment_count = 0 or last_cycle_order_count=0) and order_count > 0 then 1 " +
//                "else cast( ((order_count::numeric/equipment_count::numeric) - (last_cycle_order_count::numeric/last_cycle_equipment_count::numeric))::numeric/(last_cycle_order_count::numeric/last_cycle_equipment_count::numeric)::numeric as decimal(10,2)) end  as \"perEquipmentOrderUp\","+
//
//
//
//                "cast(case when equipment_group_count=0 then 0 else total_start_counts::numeric/equipment_group_count::numeric end as decimal(10,2)) as \"perGroupStart\"," +        //场地启动均次
//                "case when (last_cycle_equipment_group_count = 0 or last_cycle_total_start_counts=0) and (total_start_counts = 0 or equipment_group_count =0) then 0 when (last_cycle_equipment_group_count = 0 or last_cycle_total_start_counts=0) and total_start_counts > 0 then 1 " +
//                "else cast( ((total_start_counts::numeric/equipment_group_count::numeric) - (last_cycle_total_start_counts::numeric/last_cycle_equipment_group_count::numeric))/(last_cycle_total_start_counts::numeric/last_cycle_equipment_group_count::numeric) as decimal(10,2)) end  as \"perGroupStartUp\","+
//
//
//                "cast(case when equipment_group_count=0 then 0 else equipment_count::numeric/equipment_group_count::numeric end as decimal(10,2)) as \"perGroupEquipment\","+        //场地设备均数
//                "case when (last_cycle_equipment_group_count =0 or last_cycle_equipment_count =0) and (equipment_count=0 or equipment_group_count=0) then 0 "+
//                "when (last_cycle_equipment_group_count = 0 or last_cycle_equipment_count=0) and equipment_count > 0 then 1 " +
//                "else cast( ((equipment_count::numeric/equipment_group_count::numeric) - (last_cycle_equipment_count::numeric/last_cycle_equipment_group_count::numeric))/(last_cycle_equipment_count::numeric/last_cycle_equipment_group_count::numeric) as decimal(10,2)) end as \"perGroupEquipmentUp\""
//
//
//        );
//
//
//        params.getQueryParams().setYear_str(yearParamDto.getYear()+"");
//        params.getQueryParams().setMerchant_id(yearParamDto.getMerchantId()+"");
//        params.getQueryParams().setCombo_type(yearParamDto.getComboType());
//
//        params.getQueryParams().setProvince_id(yearParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(yearParamDto.getProvinceId()+"");}});
//        params.getQueryParams().setCity_id(yearParamDto.getCityId() == null ? null:new HashSet<String>(){{add(yearParamDto.getCityId()+"");}});
//        params.getQueryParams().setDistrict_id(yearParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(yearParamDto.getAreaId()+"");}});
//        params.getQueryParams().setEquipment_group_id(yearParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(yearParamDto.getGroupId()+"");}});
//        params.getQueryParams().setLyy_equipment_type_id(yearParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(yearParamDto.getEquipmentTypeId()+"");}});
//
//        if(yearParamDto.getGroupIds() != null && yearParamDto.getGroupIds().size()>0){
//            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//            if(yearParamDto.getGroupId() == null || !yearParamDto.getGroupIds().contains(yearParamDto.getGroupId()+"")){
//                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(yearParamDto.getGroupIds())).first()+"");}});
//            }
//            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
//            params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
//        }
//
//        try{
//            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
//            JSONObject result = oneDataServiceFeign.getData(params,headers);
//            log.info("{}",result.toJSONString());
//            return result;
//        }catch (Exception e){
//            log.warn("{}",e);
//        }
//
//
//
//        return null;
//    }
//
//
//    @Override
//    public JSONObject getTimeRangeData(DayParamDto dayParamDto) {
//        MultiValueMap<String, String> headers = new HttpHeaders();
//        headers.add("sk",sk);
//        headers.add("ak",ak);
//
//        ParamDto params = new ParamDto();
//        params.setCode(dayTimeRangeDataCode);
//
//        params.setColumnArray("merchant_id as \"merchantId\", " +
//                "cast(pay_amount_0_1 as decimal(10,2)) as \"amount_0_1\", " +
//                "cast(pay_amount_1_2 as decimal(10,2)) as \"amount_1_2\", " +
//                "cast(pay_amount_2_3 as decimal(10,2)) as \"amount_2_3\", " +
//                "cast(pay_amount_3_4 as decimal(10,2)) as \"amount_3_4\", " +
//                "cast(pay_amount_4_5 as decimal(10,2)) as \"amount_4_5\", " +
//                "cast(pay_amount_5_6 as decimal(10,2)) as \"amount_5_6\", " +
//                "cast(pay_amount_6_7 as decimal(10,2)) as \"amount_6_7\", " +
//                "cast(pay_amount_7_8 as decimal(10,2)) as \"amount_7_8\", " +
//                "cast(pay_amount_8_9 as decimal(10,2)) as \"amount_8_9\", " +
//                "cast(pay_amount_9_10 as decimal(10,2)) as \"amount_9_10\", " +
//                "cast(pay_amount_10_11 as decimal(10,2)) as \"amount_10_11\", " +
//                "cast(pay_amount_11_12 as decimal(10,2)) as \"amount_11_12\", " +
//                "cast(pay_amount_12_13 as decimal(10,2)) as \"amount_12_13\", " +
//                "cast(pay_amount_13_14 as decimal(10,2)) as \"amount_13_14\", " +
//                "cast(pay_amount_14_15 as decimal(10,2)) as \"amount_14_15\", " +
//                "cast(pay_amount_15_16 as decimal(10,2)) as \"amount_15_16\", " +
//                "cast(pay_amount_16_17 as decimal(10,2)) as \"amount_16_17\", " +
//                "cast(pay_amount_17_18 as decimal(10,2)) as \"amount_17_18\", " +
//                "cast(pay_amount_18_19 as decimal(10,2)) as \"amount_18_19\", " +
//                "cast(pay_amount_19_20 as decimal(10,2)) as \"amount_19_20\", " +
//                "cast(pay_amount_20_21 as decimal(10,2)) as \"amount_20_21\", " +
//                "cast(pay_amount_21_22 as decimal(10,2)) as \"amount_21_22\", " +
//                "cast(pay_amount_22_23 as decimal(10,2)) as \"amount_22_23\", " +
//                "cast(pay_amount_23_24 as decimal(10,2)) as \"amount_23_24\", " +
//
//                "pay_count_0_1 as \"count_0_1\", " +
//                "pay_count_1_2 as \"count_1_2\", " +
//                "pay_count_2_3 as \"count_2_3\", " +
//                "pay_count_3_4 as \"count_3_4\", " +
//                "pay_count_4_5 as \"count_4_5\", " +
//                "pay_count_5_6 as \"count_5_6\", " +
//                "pay_count_6_7 as \"count_6_7\", " +
//                "pay_count_7_8 as \"count_7_8\", " +
//                "pay_count_8_9 as \"count_8_9\", " +
//                "pay_count_9_10 as \"count_9_10\", " +
//                "pay_count_10_11 as \"count_10_11\", " +
//                "pay_count_11_12 as \"count_11_12\", " +
//                "pay_count_12_13 as \"count_12_13\", " +
//                "pay_count_13_14 as \"count_13_14\", " +
//                "pay_count_14_15 as \"count_14_15\", " +
//                "pay_count_15_16 as \"count_15_16\", " +
//                "pay_count_16_17 as \"count_16_17\", " +
//                "pay_count_17_18 as \"count_17_18\", " +
//                "pay_count_18_19 as \"count_18_19\", " +
//                "pay_count_19_20 as \"count_19_20\", " +
//                "pay_count_20_21 as \"count_20_21\", " +
//                "pay_count_21_22 as \"count_21_22\", " +
//                "pay_count_22_23 as \"count_22_23\", " +
//                "pay_count_23_24 as \"count_23_24\", " +
//
//
//                "case when pay_count_0_1=0 then 0 else cast(pay_amount_0_1::numeric/pay_count_0_1::numeric as decimal(10,2)) end as \"perOrderPrice_0_1\", " +
//                "case when pay_count_1_2=0 then 0 else cast(pay_amount_1_2::numeric/pay_count_1_2::numeric as decimal(10,2)) end as \"perOrderPrice_1_2\", " +
//                "case when pay_count_2_3=0 then 0 else cast(pay_amount_2_3::numeric/pay_count_2_3::numeric as decimal(10,2)) end as \"perOrderPrice_2_3\", " +
//                "case when pay_count_3_4=0 then 0 else cast(pay_amount_3_4::numeric/pay_count_3_4::numeric as decimal(10,2)) end as \"perOrderPrice_3_4\", " +
//                "case when pay_count_4_5=0 then 0 else cast(pay_amount_4_5::numeric/pay_count_4_5::numeric as decimal(10,2)) end as \"perOrderPrice_4_5\", " +
//                "case when pay_count_5_6=0 then 0 else cast(pay_amount_5_6::numeric/pay_count_5_6::numeric as decimal(10,2)) end as \"perOrderPrice_5_6\", " +
//                "case when pay_count_6_7=0 then 0 else cast(pay_amount_6_7::numeric/pay_count_6_7::numeric as decimal(10,2)) end as \"perOrderPrice_6_7\", " +
//                "case when pay_count_7_8=0 then 0 else cast(pay_amount_7_8::numeric/pay_count_7_8::numeric as decimal(10,2)) end as \"perOrderPrice_7_8\", " +
//                "case when pay_count_8_9=0 then 0 else cast(pay_amount_8_9::numeric/pay_count_8_9::numeric as decimal(10,2)) end as \"perOrderPrice_8_9\", " +
//                "case when pay_count_9_10=0 then 0 else cast(pay_amount_9_10::numeric/pay_count_9_10::numeric as decimal(10,2)) end as \"perOrderPrice_9_10\", " +
//                "case when pay_count_10_11=0 then 0 else cast(pay_amount_10_11::numeric/pay_count_10_11::numeric as decimal(10,2)) end as \"perOrderPrice_10_11\", " +
//                "case when pay_count_11_12=0 then 0 else cast(pay_amount_11_12::numeric/pay_count_11_12::numeric as decimal(10,2)) end as \"perOrderPrice_11_12\", " +
//                "case when pay_count_12_13=0 then 0 else cast(pay_amount_12_13::numeric/pay_count_12_13::numeric as decimal(10,2)) end as \"perOrderPrice_12_13\", " +
//                "case when pay_count_13_14=0 then 0 else cast(pay_amount_13_14::numeric/pay_count_13_14::numeric as decimal(10,2)) end as \"perOrderPrice_13_14\", " +
//                "case when pay_count_14_15=0 then 0 else cast(pay_amount_14_15::numeric/pay_count_14_15::numeric as decimal(10,2)) end as \"perOrderPrice_14_15\", " +
//                "case when pay_count_15_16=0 then 0 else cast(pay_amount_15_16::numeric/pay_count_15_16::numeric as decimal(10,2)) end as \"perOrderPrice_15_16\", " +
//                "case when pay_count_16_17=0 then 0 else cast(pay_amount_16_17::numeric/pay_count_16_17::numeric as decimal(10,2)) end as \"perOrderPrice_16_17\", " +
//                "case when pay_count_17_18=0 then 0 else cast(pay_amount_17_18::numeric/pay_count_17_18::numeric as decimal(10,2)) end as \"perOrderPrice_17_18\", " +
//                "case when pay_count_18_19=0 then 0 else cast(pay_amount_18_19::numeric/pay_count_18_19::numeric as decimal(10,2)) end as \"perOrderPrice_18_19\", " +
//                "case when pay_count_19_20=0 then 0 else cast(pay_amount_19_20::numeric/pay_count_19_20::numeric as decimal(10,2)) end as \"perOrderPrice_19_20\", " +
//                "case when pay_count_20_21=0 then 0 else cast(pay_amount_20_21::numeric/pay_count_20_21::numeric as decimal(10,2)) end as \"perOrderPrice_20_21\", " +
//                "case when pay_count_21_22=0 then 0 else cast(pay_amount_21_22::numeric/pay_count_21_22::numeric as decimal(10,2)) end as \"perOrderPrice_21_22\", " +
//                "case when pay_count_22_23=0 then 0 else cast(pay_amount_22_23::numeric/pay_count_22_23::numeric as decimal(10,2)) end as \"perOrderPrice_22_23\", " +
//                "case when pay_count_23_24=0 then 0 else cast(pay_amount_23_24::numeric/pay_count_23_24::numeric as decimal(10,2)) end as \"perOrderPrice_23_24\", " +
//
//                "day as \"day\" "
//        );
//
//
//        params.getQueryParams().setDay(dayParamDto.getDay());
//        params.getQueryParams().setMerchant_id(dayParamDto.getMerchantId()+"");
//        params.getQueryParams().setCombo_type(dayParamDto.getComboType());
//
//        params.getQueryParams().setProvince_id(dayParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(dayParamDto.getProvinceId()+"");}});
//        params.getQueryParams().setCity_id(dayParamDto.getCityId() == null ? null:new HashSet<String>(){{add(dayParamDto.getCityId()+"");}});
//        params.getQueryParams().setDistrict_id(dayParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(dayParamDto.getAreaId()+"");}});
//        params.getQueryParams().setEquipment_group_id(dayParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(dayParamDto.getGroupId()+"");}});
//        params.getQueryParams().setLyy_equipment_type_id(dayParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(dayParamDto.getEquipmentTypeId()+"");}});
//
//        if(dayParamDto.getGroupIds() != null && dayParamDto.getGroupIds().size()>0){
//            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//            if(dayParamDto.getGroupId() == null || !dayParamDto.getGroupIds().contains(dayParamDto.getGroupId()+"")){
//                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(dayParamDto.getGroupIds())).first()+"");}});
//            }
//            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
//            params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
//        }
//
//        try{
//            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
//            JSONObject result = oneDataServiceFeign.getData(params,headers);
//            log.info("{}",result.toJSONString());
//            return result;
//        }catch (Exception e){
//            log.warn("{}",e);
//        }
//
//
//
//        return null;
//    }
//
//
//    public JSONObject getWeekTimeRangeData(WeekParamDto weekParamDto) {
//        MultiValueMap<String, String> headers = new HttpHeaders();
//        headers.add("sk",sk);
//        headers.add("ak",ak);
//
//        ParamDto params = new ParamDto();
//        params.setCode(weekTimeRangeDtaCode);
//
//        params.setColumnArray("merchant_id as \"merchantId\", " +
//                "monday_pay_count as \"mondayPayCount\", " +
//                "cast(monday_pay_amount as decimal(10,2)) as \"mondayPayAmount\", " +
//                "case when monday_pay_count =0 then 0 else cast(monday_pay_amount::numeric/monday_pay_count::numeric as decimal(10,2)) end as \"mondayPerOrderAmount\", " +
//
//                "tuesday_pay_count as \"tuesdayPayCount\", " +
//                "cast(tuesday_pay_amount as decimal(10,2)) as \"tuesdayPayAmount\", " +
//                "case when tuesday_pay_count =0 then 0 else cast(tuesday_pay_amount::numeric/tuesday_pay_count::numeric as decimal(10,2)) end as \"tuesdayPerOrderAmount\", " +
//
//                "wednesday_pay_count as \"wednesdayPayCount\", " +
//                "cast(wednesday_pay_amount as decimal(10,2)) as \"wednesdayPayAmount\", " +
//                "case when wednesday_pay_count =0 then 0 else cast(wednesday_pay_amount::numeric/wednesday_pay_count::numeric as decimal(10,2)) end as \"wednesdayPerOrderAmount\", " +
//
//                "thursday_pay_count as \"thursdayPayCount\", " +
//                "cast(thursday_pay_amount as decimal(10,2)) as \"thursdayPayAmount\", " +
//                "case when thursday_pay_count =0 then 0 else cast(thursday_pay_amount::numeric/thursday_pay_count::numeric as decimal(10,2)) end as \"thursdayPerOrderAmount\", " +
//
//                "friday_pay_count as \"fridayPayCount\", " +
//                "cast(friday_pay_amount as decimal(10,2)) as \"fridayPayAmount\", " +
//                "case when friday_pay_count =0 then 0 else cast(friday_pay_amount::numeric/friday_pay_count::numeric as decimal(10,2)) end as \"fridayPerOrderAmount\", " +
//
//                "saturday_pay_count as \"saturdayPayCount\", " +
//                "cast(saturday_pay_amount as decimal(10,2)) as \"saturdayPayAmount\", " +
//                "case when saturday_pay_count =0 then 0 else cast(saturday_pay_amount::numeric/saturday_pay_count::numeric as decimal(10,2)) end as \"saturdayPerOrderAmount\", " +
//
//                "sunday_pay_count as \"sundayPayCount\", " +
//                "cast(sunday_pay_amount as decimal(10,2)) as \"sundayPayAmount\", " +
//                "case when sunday_pay_count =0 then 0 else cast(sunday_pay_amount::numeric/sunday_pay_count::numeric as decimal(10,2)) end as \"sundayPerOrderAmount\", " +
//
//                "cast(pay_amount_0_1 as decimal(10,2)) as \"amount_0_1\", " +
//                "cast(pay_amount_1_2 as decimal(10,2)) as \"amount_1_2\", " +
//                "cast(pay_amount_2_3 as decimal(10,2)) as \"amount_2_3\", " +
//                "cast(pay_amount_3_4 as decimal(10,2)) as \"amount_3_4\", " +
//                "cast(pay_amount_4_5 as decimal(10,2)) as \"amount_4_5\", " +
//                "cast(pay_amount_5_6 as decimal(10,2)) as \"amount_5_6\", " +
//                "cast(pay_amount_6_7 as decimal(10,2)) as \"amount_6_7\", " +
//                "cast(pay_amount_7_8 as decimal(10,2)) as \"amount_7_8\", " +
//                "cast(pay_amount_8_9 as decimal(10,2)) as \"amount_8_9\", " +
//                "cast(pay_amount_9_10 as decimal(10,2)) as \"amount_9_10\", " +
//                "cast(pay_amount_10_11 as decimal(10,2)) as \"amount_10_11\", " +
//                "cast(pay_amount_11_12 as decimal(10,2)) as \"amount_11_12\", " +
//                "cast(pay_amount_12_13 as decimal(10,2)) as \"amount_12_13\", " +
//                "cast(pay_amount_13_14 as decimal(10,2)) as \"amount_13_14\", " +
//                "cast(pay_amount_14_15 as decimal(10,2)) as \"amount_14_15\", " +
//                "cast(pay_amount_15_16 as decimal(10,2)) as \"amount_15_16\", " +
//                "cast(pay_amount_16_17 as decimal(10,2)) as \"amount_16_17\", " +
//                "cast(pay_amount_17_18 as decimal(10,2)) as \"amount_17_18\", " +
//                "cast(pay_amount_18_19 as decimal(10,2)) as \"amount_18_19\", " +
//                "cast(pay_amount_19_20 as decimal(10,2)) as \"amount_19_20\", " +
//                "cast(pay_amount_20_21 as decimal(10,2)) as \"amount_20_21\", " +
//                "cast(pay_amount_21_22 as decimal(10,2)) as \"amount_21_22\", " +
//                "cast(pay_amount_22_23 as decimal(10,2)) as \"amount_22_23\", " +
//                "cast(pay_amount_23_24 as decimal(10,2)) as \"amount_23_24\", " +
//
//                "pay_count_0_1 as \"count_0_1\", " +
//                "pay_count_1_2 as \"count_1_2\", " +
//                "pay_count_2_3 as \"count_2_3\", " +
//                "pay_count_3_4 as \"count_3_4\", " +
//                "pay_count_4_5 as \"count_4_5\", " +
//                "pay_count_5_6 as \"count_5_6\", " +
//                "pay_count_6_7 as \"count_6_7\", " +
//                "pay_count_7_8 as \"count_7_8\", " +
//                "pay_count_8_9 as \"count_8_9\", " +
//                "pay_count_9_10 as \"count_9_10\", " +
//                "pay_count_10_11 as \"count_10_11\", " +
//                "pay_count_11_12 as \"count_11_12\", " +
//                "pay_count_12_13 as \"count_12_13\", " +
//                "pay_count_13_14 as \"count_13_14\", " +
//                "pay_count_14_15 as \"count_14_15\", " +
//                "pay_count_15_16 as \"count_15_16\", " +
//                "pay_count_16_17 as \"count_16_17\", " +
//                "pay_count_17_18 as \"count_17_18\", " +
//                "pay_count_18_19 as \"count_18_19\", " +
//                "pay_count_19_20 as \"count_19_20\", " +
//                "pay_count_20_21 as \"count_20_21\", " +
//                "pay_count_21_22 as \"count_21_22\", " +
//                "pay_count_22_23 as \"count_22_23\", " +
//                "pay_count_23_24 as \"count_23_24\", " +
//
//
//                "case when pay_count_0_1=0 then 0 else cast(pay_amount_0_1::numeric/pay_count_0_1::numeric as decimal(10,2)) end as \"perOrderPrice_0_1\", " +
//                "case when pay_count_1_2=0 then 0 else cast(pay_amount_1_2::numeric/pay_count_1_2::numeric as decimal(10,2)) end as \"perOrderPrice_1_2\", " +
//                "case when pay_count_2_3=0 then 0 else cast(pay_amount_2_3::numeric/pay_count_2_3::numeric as decimal(10,2)) end as \"perOrderPrice_2_3\", " +
//                "case when pay_count_3_4=0 then 0 else cast(pay_amount_3_4::numeric/pay_count_3_4::numeric as decimal(10,2)) end as \"perOrderPrice_3_4\", " +
//                "case when pay_count_4_5=0 then 0 else cast(pay_amount_4_5::numeric/pay_count_4_5::numeric as decimal(10,2)) end as \"perOrderPrice_4_5\", " +
//                "case when pay_count_5_6=0 then 0 else cast(pay_amount_5_6::numeric/pay_count_5_6::numeric as decimal(10,2)) end as \"perOrderPrice_5_6\", " +
//                "case when pay_count_6_7=0 then 0 else cast(pay_amount_6_7::numeric/pay_count_6_7::numeric as decimal(10,2)) end as \"perOrderPrice_6_7\", " +
//                "case when pay_count_7_8=0 then 0 else cast(pay_amount_7_8::numeric/pay_count_7_8::numeric as decimal(10,2)) end as \"perOrderPrice_7_8\", " +
//                "case when pay_count_8_9=0 then 0 else cast(pay_amount_8_9::numeric/pay_count_8_9::numeric as decimal(10,2)) end as \"perOrderPrice_8_9\", " +
//                "case when pay_count_9_10=0 then 0 else cast(pay_amount_9_10::numeric/pay_count_9_10::numeric as decimal(10,2)) end as \"perOrderPrice_9_10\", " +
//                "case when pay_count_10_11=0 then 0 else cast(pay_amount_10_11::numeric/pay_count_10_11::numeric as decimal(10,2)) end as \"perOrderPrice_10_11\", " +
//                "case when pay_count_11_12=0 then 0 else cast(pay_amount_11_12::numeric/pay_count_11_12::numeric as decimal(10,2)) end as \"perOrderPrice_11_12\", " +
//                "case when pay_count_12_13=0 then 0 else cast(pay_amount_12_13::numeric/pay_count_12_13::numeric as decimal(10,2)) end as \"perOrderPrice_12_13\", " +
//                "case when pay_count_13_14=0 then 0 else cast(pay_amount_13_14::numeric/pay_count_13_14::numeric as decimal(10,2)) end as \"perOrderPrice_13_14\", " +
//                "case when pay_count_14_15=0 then 0 else cast(pay_amount_14_15::numeric/pay_count_14_15::numeric as decimal(10,2)) end as \"perOrderPrice_14_15\", " +
//                "case when pay_count_15_16=0 then 0 else cast(pay_amount_15_16::numeric/pay_count_15_16::numeric as decimal(10,2)) end as \"perOrderPrice_15_16\", " +
//                "case when pay_count_16_17=0 then 0 else cast(pay_amount_16_17::numeric/pay_count_16_17::numeric as decimal(10,2)) end as \"perOrderPrice_16_17\", " +
//                "case when pay_count_17_18=0 then 0 else cast(pay_amount_17_18::numeric/pay_count_17_18::numeric as decimal(10,2)) end as \"perOrderPrice_17_18\", " +
//                "case when pay_count_18_19=0 then 0 else cast(pay_amount_18_19::numeric/pay_count_18_19::numeric as decimal(10,2)) end as \"perOrderPrice_18_19\", " +
//                "case when pay_count_19_20=0 then 0 else cast(pay_amount_19_20::numeric/pay_count_19_20::numeric as decimal(10,2)) end as \"perOrderPrice_19_20\", " +
//                "case when pay_count_20_21=0 then 0 else cast(pay_amount_20_21::numeric/pay_count_20_21::numeric as decimal(10,2)) end as \"perOrderPrice_20_21\", " +
//                "case when pay_count_21_22=0 then 0 else cast(pay_amount_21_22::numeric/pay_count_21_22::numeric as decimal(10,2)) end as \"perOrderPrice_21_22\", " +
//                "case when pay_count_22_23=0 then 0 else cast(pay_amount_22_23::numeric/pay_count_22_23::numeric as decimal(10,2)) end as \"perOrderPrice_22_23\", " +
//                "case when pay_count_23_24=0 then 0 else cast(pay_amount_23_24::numeric/pay_count_23_24::numeric as decimal(10,2)) end as \"perOrderPrice_23_24\", " +
//
//                "year_month as \"month\","+
//                "month_week_num as \"week\""
//        );
//
//
//        params.getQueryParams().setYear_month(weekParamDto.getMonth());
//        params.getQueryParams().setMerchant_id(weekParamDto.getMerchantId()+"");
//        params.getQueryParams().setCombo_type(weekParamDto.getComboType());
//        params.getQueryParams().setMonth_week_num(weekParamDto.getWeek()+"");
//
//        params.getQueryParams().setProvince_id(weekParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(weekParamDto.getProvinceId()+"");}});
//        params.getQueryParams().setCity_id(weekParamDto.getCityId() == null ? null:new HashSet<String>(){{add(weekParamDto.getCityId()+"");}});
//        params.getQueryParams().setDistrict_id(weekParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(weekParamDto.getAreaId()+"");}});
//        params.getQueryParams().setEquipment_group_id(weekParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(weekParamDto.getGroupId()+"");}});
//        params.getQueryParams().setLyy_equipment_type_id(weekParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(weekParamDto.getEquipmentTypeId()+"");}});
//
//        if(weekParamDto.getGroupIds() != null && weekParamDto.getGroupIds().size()>0){
//            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//            if(weekParamDto.getGroupId() == null || !weekParamDto.getGroupIds().contains(weekParamDto.getGroupId()+"")){
//                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(weekParamDto.getGroupIds())).first()+"");}});
//            }
//            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
//            params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
//        }
//
//        try{
//            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
//            JSONObject result = oneDataServiceFeign.getData(params,headers);
//            log.info("{}",result.toJSONString());
//            return result;
//        }catch (Exception e){
//            log.warn("{}",e);
//        }
//
//
//
//        return null;
//    }
//
//    public JSONObject getMonthTimeRangeData(MonthParamDto monthParamDto) {
//        MultiValueMap<String, String> headers = new HttpHeaders();
//        headers.add("sk",sk);
//        headers.add("ak",ak);
//
//        ParamDto params = new ParamDto();
//        params.setCode(monthTimeRangeDtaCode);
//
//        params.setColumnArray("merchant_id as \"merchantId\", " +
//                "monday_pay_count as \"mondayPayCount\", " +
//                "cast(monday_pay_amount as decimal(10,2)) as \"mondayPayAmount\", " +
//                "case when monday_pay_count =0 then 0 else cast(monday_pay_amount::numeric/monday_pay_count::numeric as decimal(10,2)) end as \"mondayPerOrderAmount\", " +
//
//                "tuesday_pay_count as \"tuesdayPayCount\", " +
//                "cast(tuesday_pay_amount as decimal(10,2)) as \"tuesdayPayAmount\", " +
//                "case when tuesday_pay_count =0 then 0 else cast(tuesday_pay_amount::numeric/tuesday_pay_count::numeric as decimal(10,2)) end as \"tuesdayPerOrderAmount\", " +
//
//                "wednesday_pay_count as \"wednesdayPayCount\", " +
//                "cast(wednesday_pay_amount as decimal(10,2)) as \"wednesdayPayAmount\", " +
//                "case when wednesday_pay_count =0 then 0 else cast(wednesday_pay_amount::numeric/wednesday_pay_count::numeric as decimal(10,2)) end as \"wednesdayPerOrderAmount\", " +
//
//                "thursday_pay_count as \"thursdayPayCount\", " +
//                "cast(thursday_pay_amount as decimal(10,2)) as \"thursdayPayAmount\", " +
//                "case when thursday_pay_count =0 then 0 else cast(thursday_pay_amount::numeric/thursday_pay_count::numeric as decimal(10,2)) end as \"thursdayPerOrderAmount\", " +
//
//                "friday_pay_count as \"fridayPayCount\", " +
//                "cast(friday_pay_amount as decimal(10,2)) as \"fridayPayAmount\", " +
//                "case when friday_pay_count =0 then 0 else cast(friday_pay_amount::numeric/friday_pay_count::numeric as decimal(10,2)) end as \"fridayPerOrderAmount\", " +
//
//                "saturday_pay_count as \"saturdayPayCount\", " +
//                "cast(saturday_pay_amount as decimal(10,2)) as \"saturdayPayAmount\", " +
//                "case when saturday_pay_count =0 then 0 else cast(saturday_pay_amount::numeric/saturday_pay_count::numeric as decimal(10,2)) end as \"saturdayPerOrderAmount\", " +
//
//                "sunday_pay_count as \"sundayPayCount\", " +
//                "cast(sunday_pay_amount as decimal(10,2)) as \"sundayPayAmount\", " +
//                "case when sunday_pay_count =0 then 0 else cast(sunday_pay_amount::numeric/sunday_pay_count::numeric as decimal(10,2)) end as \"sundayPerOrderAmount\", " +
//
//                "cast(pay_amount_0_1 as decimal(10,2)) as \"amount_0_1\", " +
//                "cast(pay_amount_1_2 as decimal(10,2)) as \"amount_1_2\", " +
//                "cast(pay_amount_2_3 as decimal(10,2)) as \"amount_2_3\", " +
//                "cast(pay_amount_3_4 as decimal(10,2)) as \"amount_3_4\", " +
//                "cast(pay_amount_4_5 as decimal(10,2)) as \"amount_4_5\", " +
//                "cast(pay_amount_5_6 as decimal(10,2)) as \"amount_5_6\", " +
//                "cast(pay_amount_6_7 as decimal(10,2)) as \"amount_6_7\", " +
//                "cast(pay_amount_7_8 as decimal(10,2)) as \"amount_7_8\", " +
//                "cast(pay_amount_8_9 as decimal(10,2)) as \"amount_8_9\", " +
//                "cast(pay_amount_9_10 as decimal(10,2)) as \"amount_9_10\", " +
//                "cast(pay_amount_10_11 as decimal(10,2)) as \"amount_10_11\", " +
//                "cast(pay_amount_11_12 as decimal(10,2)) as \"amount_11_12\", " +
//                "cast(pay_amount_12_13 as decimal(10,2)) as \"amount_12_13\", " +
//                "cast(pay_amount_13_14 as decimal(10,2)) as \"amount_13_14\", " +
//                "cast(pay_amount_14_15 as decimal(10,2)) as \"amount_14_15\", " +
//                "cast(pay_amount_15_16 as decimal(10,2)) as \"amount_15_16\", " +
//                "cast(pay_amount_16_17 as decimal(10,2)) as \"amount_16_17\", " +
//                "cast(pay_amount_17_18 as decimal(10,2)) as \"amount_17_18\", " +
//                "cast(pay_amount_18_19 as decimal(10,2)) as \"amount_18_19\", " +
//                "cast(pay_amount_19_20 as decimal(10,2)) as \"amount_19_20\", " +
//                "cast(pay_amount_20_21 as decimal(10,2)) as \"amount_20_21\", " +
//                "cast(pay_amount_21_22 as decimal(10,2)) as \"amount_21_22\", " +
//                "cast(pay_amount_22_23 as decimal(10,2)) as \"amount_22_23\", " +
//                "cast(pay_amount_23_24 as decimal(10,2)) as \"amount_23_24\", " +
//
//                "pay_count_0_1 as \"count_0_1\", " +
//                "pay_count_1_2 as \"count_1_2\", " +
//                "pay_count_2_3 as \"count_2_3\", " +
//                "pay_count_3_4 as \"count_3_4\", " +
//                "pay_count_4_5 as \"count_4_5\", " +
//                "pay_count_5_6 as \"count_5_6\", " +
//                "pay_count_6_7 as \"count_6_7\", " +
//                "pay_count_7_8 as \"count_7_8\", " +
//                "pay_count_8_9 as \"count_8_9\", " +
//                "pay_count_9_10 as \"count_9_10\", " +
//                "pay_count_10_11 as \"count_10_11\", " +
//                "pay_count_11_12 as \"count_11_12\", " +
//                "pay_count_12_13 as \"count_12_13\", " +
//                "pay_count_13_14 as \"count_13_14\", " +
//                "pay_count_14_15 as \"count_14_15\", " +
//                "pay_count_15_16 as \"count_15_16\", " +
//                "pay_count_16_17 as \"count_16_17\", " +
//                "pay_count_17_18 as \"count_17_18\", " +
//                "pay_count_18_19 as \"count_18_19\", " +
//                "pay_count_19_20 as \"count_19_20\", " +
//                "pay_count_20_21 as \"count_20_21\", " +
//                "pay_count_21_22 as \"count_21_22\", " +
//                "pay_count_22_23 as \"count_22_23\", " +
//                "pay_count_23_24 as \"count_23_24\", " +
//
//
//                "case when pay_count_0_1=0 then 0 else cast(pay_amount_0_1::numeric/pay_count_0_1::numeric as decimal(10,2)) end as \"perOrderPrice_0_1\", " +
//                "case when pay_count_1_2=0 then 0 else cast(pay_amount_1_2::numeric/pay_count_1_2::numeric as decimal(10,2)) end as \"perOrderPrice_1_2\", " +
//                "case when pay_count_2_3=0 then 0 else cast(pay_amount_2_3::numeric/pay_count_2_3::numeric as decimal(10,2)) end as \"perOrderPrice_2_3\", " +
//                "case when pay_count_3_4=0 then 0 else cast(pay_amount_3_4::numeric/pay_count_3_4::numeric as decimal(10,2)) end as \"perOrderPrice_3_4\", " +
//                "case when pay_count_4_5=0 then 0 else cast(pay_amount_4_5::numeric/pay_count_4_5::numeric as decimal(10,2)) end as \"perOrderPrice_4_5\", " +
//                "case when pay_count_5_6=0 then 0 else cast(pay_amount_5_6::numeric/pay_count_5_6::numeric as decimal(10,2)) end as \"perOrderPrice_5_6\", " +
//                "case when pay_count_6_7=0 then 0 else cast(pay_amount_6_7::numeric/pay_count_6_7::numeric as decimal(10,2)) end as \"perOrderPrice_6_7\", " +
//                "case when pay_count_7_8=0 then 0 else cast(pay_amount_7_8::numeric/pay_count_7_8::numeric as decimal(10,2)) end as \"perOrderPrice_7_8\", " +
//                "case when pay_count_8_9=0 then 0 else cast(pay_amount_8_9::numeric/pay_count_8_9::numeric as decimal(10,2)) end as \"perOrderPrice_8_9\", " +
//                "case when pay_count_9_10=0 then 0 else cast(pay_amount_9_10::numeric/pay_count_9_10::numeric as decimal(10,2)) end as \"perOrderPrice_9_10\", " +
//                "case when pay_count_10_11=0 then 0 else cast(pay_amount_10_11::numeric/pay_count_10_11::numeric as decimal(10,2)) end as \"perOrderPrice_10_11\", " +
//                "case when pay_count_11_12=0 then 0 else cast(pay_amount_11_12::numeric/pay_count_11_12::numeric as decimal(10,2)) end as \"perOrderPrice_11_12\", " +
//                "case when pay_count_12_13=0 then 0 else cast(pay_amount_12_13::numeric/pay_count_12_13::numeric as decimal(10,2)) end as \"perOrderPrice_12_13\", " +
//                "case when pay_count_13_14=0 then 0 else cast(pay_amount_13_14::numeric/pay_count_13_14::numeric as decimal(10,2)) end as \"perOrderPrice_13_14\", " +
//                "case when pay_count_14_15=0 then 0 else cast(pay_amount_14_15::numeric/pay_count_14_15::numeric as decimal(10,2)) end as \"perOrderPrice_14_15\", " +
//                "case when pay_count_15_16=0 then 0 else cast(pay_amount_15_16::numeric/pay_count_15_16::numeric as decimal(10,2)) end as \"perOrderPrice_15_16\", " +
//                "case when pay_count_16_17=0 then 0 else cast(pay_amount_16_17::numeric/pay_count_16_17::numeric as decimal(10,2)) end as \"perOrderPrice_16_17\", " +
//                "case when pay_count_17_18=0 then 0 else cast(pay_amount_17_18::numeric/pay_count_17_18::numeric as decimal(10,2)) end as \"perOrderPrice_17_18\", " +
//                "case when pay_count_18_19=0 then 0 else cast(pay_amount_18_19::numeric/pay_count_18_19::numeric as decimal(10,2)) end as \"perOrderPrice_18_19\", " +
//                "case when pay_count_19_20=0 then 0 else cast(pay_amount_19_20::numeric/pay_count_19_20::numeric as decimal(10,2)) end as \"perOrderPrice_19_20\", " +
//                "case when pay_count_20_21=0 then 0 else cast(pay_amount_20_21::numeric/pay_count_20_21::numeric as decimal(10,2)) end as \"perOrderPrice_20_21\", " +
//                "case when pay_count_21_22=0 then 0 else cast(pay_amount_21_22::numeric/pay_count_21_22::numeric as decimal(10,2)) end as \"perOrderPrice_21_22\", " +
//                "case when pay_count_22_23=0 then 0 else cast(pay_amount_22_23::numeric/pay_count_22_23::numeric as decimal(10,2)) end as \"perOrderPrice_22_23\", " +
//                "case when pay_count_23_24=0 then 0 else cast(pay_amount_23_24::numeric/pay_count_23_24::numeric as decimal(10,2)) end as \"perOrderPrice_23_24\", " +
//
//                "year_month as \"month\""
//        );
//
//
//        params.getQueryParams().setYear_month(monthParamDto.getMonth());
//        params.getQueryParams().setMerchant_id(monthParamDto.getMerchantId()+"");
//        params.getQueryParams().setCombo_type(monthParamDto.getComboType());
//
//        params.getQueryParams().setProvince_id(monthParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(monthParamDto.getProvinceId()+"");}});
//        params.getQueryParams().setCity_id(monthParamDto.getCityId() == null ? null:new HashSet<String>(){{add(monthParamDto.getCityId()+"");}});
//        params.getQueryParams().setDistrict_id(monthParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(monthParamDto.getAreaId()+"");}});
//        params.getQueryParams().setEquipment_group_id(monthParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(monthParamDto.getGroupId()+"");}});
//        params.getQueryParams().setLyy_equipment_type_id(monthParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(monthParamDto.getEquipmentTypeId()+"");}});
//
//        if(monthParamDto.getGroupIds() != null && monthParamDto.getGroupIds().size()>0){
//            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//            if(monthParamDto.getGroupId() == null || !monthParamDto.getGroupIds().contains(monthParamDto.getGroupId()+"")){
//                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(monthParamDto.getGroupIds())).first()+"");}});
//            }
//            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
//            params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
//        }
//
//        try{
//            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
//            JSONObject result = oneDataServiceFeign.getData(params,headers);
//            log.info("{}",result.toJSONString());
//            return result;
//        }catch (Exception e){
//            log.warn("{}",e);
//        }
//
//
//
//        return null;
//    }
//
//    public JSONObject getYearTimeRangeData(YearParamDto yearParamDto) {
//        MultiValueMap<String, String> headers = new HttpHeaders();
//        headers.add("sk",sk);
//        headers.add("ak",ak);
//
//        ParamDto params = new ParamDto();
//        params.setCode(yearTimeRangeDtaCode);
//
//        params.setColumnArray("merchant_id as \"merchantId\", " +
//                "january_pay_count as \"januaryPayCount\", " +
//                "cast(january_pay_amount as decimal(10,2)) as \"januaryPayAmount\", " +
//                "case when january_pay_count =0 then 0 else cast(january_pay_amount::numeric/january_pay_count::numeric as decimal(10,2)) end as \"januaryPerOrderAmount\", " +
//
//                "february_pay_count as \"februaryPayCount\", " +
//                "cast(february_pay_amount as decimal(10,2)) as \"februaryPayAmount\", " +
//                "case when february_pay_count =0 then 0 else cast(february_pay_amount::numeric/february_pay_count::numeric as decimal(10,2)) end as \"februaryPerOrderAmount\", " +
//
//                "march_pay_count as \"marchPayCount\", " +
//                "cast(march_pay_amount as decimal(10,2)) as \"marchPayAmount\", " +
//                "case when march_pay_count =0 then 0 else cast(march_pay_amount::numeric/march_pay_count::numeric as decimal(10,2)) end as \"marchPerOrderAmount\", " +
//
//                "april_pay_count as \"aprilPayCount\", " +
//                "cast(april_pay_amount as decimal(10,2)) as \"aprilPayAmount\", " +
//                "case when april_pay_count =0 then 0 else cast(april_pay_amount::numeric/april_pay_count::numeric as decimal(10,2)) end as \"aprilPerOrderAmount\", " +
//
//                "may_pay_count as \"mayPayCount\", " +
//                "cast(may_pay_amount as decimal(10,2)) as \"mayPayAmount\", " +
//                "case when may_pay_count =0 then 0 else cast(may_pay_amount::numeric/may_pay_count::numeric as decimal(10,2)) end as \"mayPerOrderAmount\", " +
//
//                "june_pay_count as \"junePayCount\", " +
//                "cast(june_pay_amount as decimal(10,2)) as \"junePayAmount\", " +
//                "case when june_pay_count =0 then 0 else cast(june_pay_amount::numeric/june_pay_count::numeric as decimal(10,2)) end as \"junePerOrderAmount\", " +
//
//                "july_pay_count as \"julyPayCount\", " +
//                "cast(july_pay_amount as decimal(10,2)) as \"julyPayAmount\", " +
//                "case when july_pay_count =0 then 0 else cast(july_pay_amount::numeric/july_pay_count::numeric as decimal(10,2)) end as \"julyPerOrderAmount\", " +
//
//                "august_pay_count as \"augustPayCount\", " +
//                "cast(august_pay_amount as decimal(10,2)) as \"augustPayAmount\", " +
//                "case when august_pay_count =0 then 0 else cast(august_pay_amount::numeric/august_pay_count::numeric as decimal(10,2)) end as \"augustPerOrderAmount\", " +
//
//                "september_pay_count as \"septemberPayCount\", " +
//                "cast(september_pay_amount as decimal(10,2)) as \"septemberPayAmount\", " +
//                "case when september_pay_count =0 then 0 else cast(september_pay_amount::numeric/september_pay_count::numeric as decimal(10,2)) end as \"septemberPerOrderAmount\", " +
//
//                "october_pay_count as \"octoberPayCount\", " +
//                "cast(october_pay_amount as decimal(10,2)) as \"octoberPayAmount\", " +
//                "case when october_pay_count =0 then 0 else cast(october_pay_amount::numeric/october_pay_count::numeric as decimal(10,2)) end as \"octoberPerOrderAmount\", " +
//
//                "november_pay_count as \"novemberPayCount\", " +
//                "cast(november_pay_amount as decimal(10,2)) as \"novemberPayAmount\", " +
//                "case when november_pay_count =0 then 0 else cast(november_pay_amount::numeric/november_pay_count::numeric as decimal(10,2)) end as \"novemberPerOrderAmount\", " +
//
//                "december_pay_count as \"decemberPayCount\", " +
//                "cast(december_pay_amount as decimal(10,2)) as \"decemberPayAmount\", " +
//                "case when december_pay_count =0 then 0 else cast(december_pay_amount::numeric/december_pay_count::numeric as decimal(10,2)) end as \"decemberPerOrderAmount\", " +
//
//                "cast(pay_amount_0_1 as decimal(10,2)) as \"amount_0_1\", " +
//                "cast(pay_amount_1_2 as decimal(10,2)) as \"amount_1_2\", " +
//                "cast(pay_amount_2_3 as decimal(10,2)) as \"amount_2_3\", " +
//                "cast(pay_amount_3_4 as decimal(10,2)) as \"amount_3_4\", " +
//                "cast(pay_amount_4_5 as decimal(10,2)) as \"amount_4_5\", " +
//                "cast(pay_amount_5_6 as decimal(10,2)) as \"amount_5_6\", " +
//                "cast(pay_amount_6_7 as decimal(10,2)) as \"amount_6_7\", " +
//                "cast(pay_amount_7_8 as decimal(10,2)) as \"amount_7_8\", " +
//                "cast(pay_amount_8_9 as decimal(10,2)) as \"amount_8_9\", " +
//                "cast(pay_amount_9_10 as decimal(10,2)) as \"amount_9_10\", " +
//                "cast(pay_amount_10_11 as decimal(10,2)) as \"amount_10_11\", " +
//                "cast(pay_amount_11_12 as decimal(10,2)) as \"amount_11_12\", " +
//                "cast(pay_amount_12_13 as decimal(10,2)) as \"amount_12_13\", " +
//                "cast(pay_amount_13_14 as decimal(10,2)) as \"amount_13_14\", " +
//                "cast(pay_amount_14_15 as decimal(10,2)) as \"amount_14_15\", " +
//                "cast(pay_amount_15_16 as decimal(10,2)) as \"amount_15_16\", " +
//                "cast(pay_amount_16_17 as decimal(10,2)) as \"amount_16_17\", " +
//                "cast(pay_amount_17_18 as decimal(10,2)) as \"amount_17_18\", " +
//                "cast(pay_amount_18_19 as decimal(10,2)) as \"amount_18_19\", " +
//                "cast(pay_amount_19_20 as decimal(10,2)) as \"amount_19_20\", " +
//                "cast(pay_amount_20_21 as decimal(10,2)) as \"amount_20_21\", " +
//                "cast(pay_amount_21_22 as decimal(10,2)) as \"amount_21_22\", " +
//                "cast(pay_amount_22_23 as decimal(10,2)) as \"amount_22_23\", " +
//                "cast(pay_amount_23_24 as decimal(10,2)) as \"amount_23_24\", " +
//
//                "pay_count_0_1 as \"count_0_1\", " +
//                "pay_count_1_2 as \"count_1_2\", " +
//                "pay_count_2_3 as \"count_2_3\", " +
//                "pay_count_3_4 as \"count_3_4\", " +
//                "pay_count_4_5 as \"count_4_5\", " +
//                "pay_count_5_6 as \"count_5_6\", " +
//                "pay_count_6_7 as \"count_6_7\", " +
//                "pay_count_7_8 as \"count_7_8\", " +
//                "pay_count_8_9 as \"count_8_9\", " +
//                "pay_count_9_10 as \"count_9_10\", " +
//                "pay_count_10_11 as \"count_10_11\", " +
//                "pay_count_11_12 as \"count_11_12\", " +
//                "pay_count_12_13 as \"count_12_13\", " +
//                "pay_count_13_14 as \"count_13_14\", " +
//                "pay_count_14_15 as \"count_14_15\", " +
//                "pay_count_15_16 as \"count_15_16\", " +
//                "pay_count_16_17 as \"count_16_17\", " +
//                "pay_count_17_18 as \"count_17_18\", " +
//                "pay_count_18_19 as \"count_18_19\", " +
//                "pay_count_19_20 as \"count_19_20\", " +
//                "pay_count_20_21 as \"count_20_21\", " +
//                "pay_count_21_22 as \"count_21_22\", " +
//                "pay_count_22_23 as \"count_22_23\", " +
//                "pay_count_23_24 as \"count_23_24\", " +
//
//
//                "case when pay_count_0_1=0 then 0 else cast(pay_amount_0_1::numeric/pay_count_0_1::numeric as decimal(10,2)) end as \"perOrderPrice_0_1\", " +
//                "case when pay_count_1_2=0 then 0 else cast(pay_amount_1_2::numeric/pay_count_1_2::numeric as decimal(10,2)) end as \"perOrderPrice_1_2\", " +
//                "case when pay_count_2_3=0 then 0 else cast(pay_amount_2_3::numeric/pay_count_2_3::numeric as decimal(10,2)) end as \"perOrderPrice_2_3\", " +
//                "case when pay_count_3_4=0 then 0 else cast(pay_amount_3_4::numeric/pay_count_3_4::numeric as decimal(10,2)) end as \"perOrderPrice_3_4\", " +
//                "case when pay_count_4_5=0 then 0 else cast(pay_amount_4_5::numeric/pay_count_4_5::numeric as decimal(10,2)) end as \"perOrderPrice_4_5\", " +
//                "case when pay_count_5_6=0 then 0 else cast(pay_amount_5_6::numeric/pay_count_5_6::numeric as decimal(10,2)) end as \"perOrderPrice_5_6\", " +
//                "case when pay_count_6_7=0 then 0 else cast(pay_amount_6_7::numeric/pay_count_6_7::numeric as decimal(10,2)) end as \"perOrderPrice_6_7\", " +
//                "case when pay_count_7_8=0 then 0 else cast(pay_amount_7_8::numeric/pay_count_7_8::numeric as decimal(10,2)) end as \"perOrderPrice_7_8\", " +
//                "case when pay_count_8_9=0 then 0 else cast(pay_amount_8_9::numeric/pay_count_8_9::numeric as decimal(10,2)) end as \"perOrderPrice_8_9\", " +
//                "case when pay_count_9_10=0 then 0 else cast(pay_amount_9_10::numeric/pay_count_9_10::numeric as decimal(10,2)) end as \"perOrderPrice_9_10\", " +
//                "case when pay_count_10_11=0 then 0 else cast(pay_amount_10_11::numeric/pay_count_10_11::numeric as decimal(10,2)) end as \"perOrderPrice_10_11\", " +
//                "case when pay_count_11_12=0 then 0 else cast(pay_amount_11_12::numeric/pay_count_11_12::numeric as decimal(10,2)) end as \"perOrderPrice_11_12\", " +
//                "case when pay_count_12_13=0 then 0 else cast(pay_amount_12_13::numeric/pay_count_12_13::numeric as decimal(10,2)) end as \"perOrderPrice_12_13\", " +
//                "case when pay_count_13_14=0 then 0 else cast(pay_amount_13_14::numeric/pay_count_13_14::numeric as decimal(10,2)) end as \"perOrderPrice_13_14\", " +
//                "case when pay_count_14_15=0 then 0 else cast(pay_amount_14_15::numeric/pay_count_14_15::numeric as decimal(10,2)) end as \"perOrderPrice_14_15\", " +
//                "case when pay_count_15_16=0 then 0 else cast(pay_amount_15_16::numeric/pay_count_15_16::numeric as decimal(10,2)) end as \"perOrderPrice_15_16\", " +
//                "case when pay_count_16_17=0 then 0 else cast(pay_amount_16_17::numeric/pay_count_16_17::numeric as decimal(10,2)) end as \"perOrderPrice_16_17\", " +
//                "case when pay_count_17_18=0 then 0 else cast(pay_amount_17_18::numeric/pay_count_17_18::numeric as decimal(10,2)) end as \"perOrderPrice_17_18\", " +
//                "case when pay_count_18_19=0 then 0 else cast(pay_amount_18_19::numeric/pay_count_18_19::numeric as decimal(10,2)) end as \"perOrderPrice_18_19\", " +
//                "case when pay_count_19_20=0 then 0 else cast(pay_amount_19_20::numeric/pay_count_19_20::numeric as decimal(10,2)) end as \"perOrderPrice_19_20\", " +
//                "case when pay_count_20_21=0 then 0 else cast(pay_amount_20_21::numeric/pay_count_20_21::numeric as decimal(10,2)) end as \"perOrderPrice_20_21\", " +
//                "case when pay_count_21_22=0 then 0 else cast(pay_amount_21_22::numeric/pay_count_21_22::numeric as decimal(10,2)) end as \"perOrderPrice_21_22\", " +
//                "case when pay_count_22_23=0 then 0 else cast(pay_amount_22_23::numeric/pay_count_22_23::numeric as decimal(10,2)) end as \"perOrderPrice_22_23\", " +
//                "case when pay_count_23_24=0 then 0 else cast(pay_amount_23_24::numeric/pay_count_23_24::numeric as decimal(10,2)) end as \"perOrderPrice_23_24\", " +
//
//                "year_str as \"year\""
//        );
//
//
//        params.getQueryParams().setYear_str(yearParamDto.getYear()+"");
//        params.getQueryParams().setMerchant_id(yearParamDto.getMerchantId()+"");
//        params.getQueryParams().setCombo_type(yearParamDto.getComboType());
//
//        params.getQueryParams().setProvince_id(yearParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(yearParamDto.getProvinceId()+"");}});
//        params.getQueryParams().setCity_id(yearParamDto.getCityId() == null ? null:new HashSet<String>(){{add(yearParamDto.getCityId()+"");}});
//        params.getQueryParams().setDistrict_id(yearParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(yearParamDto.getAreaId()+"");}});
//        params.getQueryParams().setEquipment_group_id(yearParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(yearParamDto.getGroupId()+"");}});
//        params.getQueryParams().setLyy_equipment_type_id(yearParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(yearParamDto.getEquipmentTypeId()+"");}});
//
//        if(yearParamDto.getGroupIds() != null && yearParamDto.getGroupIds().size()>0){
//            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//            if(yearParamDto.getGroupId() == null || !yearParamDto.getGroupIds().contains(yearParamDto.getGroupId()+"")){
//                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(yearParamDto.getGroupIds())).first()+"");}});
//            }
//            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
//            params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
//        }
//
//        try{
//            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
//            JSONObject result = oneDataServiceFeign.getData(params,headers);
//            log.info("{}",result.toJSONString());
//            return result;
//        }catch (Exception e){
//            log.warn("{}",e);
//        }
//
//
//
//        return null;
//    }
//
//    @Override
//    public JSONObject getMemberData(DayParamDto dayParamDto) {
//        MultiValueMap<String, String> headers = new HttpHeaders();
//        headers.add("sk",sk);
//        headers.add("ak",ak);
//
//        ParamDto params = new ParamDto();
//        params.setCode(dayMemberDataCode);
//
//        params.setColumnArray("merchant_id as \"merchantId\", " +
//                //会员总数
//                "member_count as \"memberCounts\", " +
//                "cast(case when member_count_yesterday =0 and member_count>0 then 1 when member_count_yesterday=0 and member_count=0 then 0 else (member_count-member_count_yesterday)::numeric/member_count_yesterday::numeric end as decimal(10,2)) as \"memberCountsUp\"," +
//
//                //新会员数
//                "new_member_count as \"newMemberCounts\", " +
//                "cast(case when new_member_count_yesterday =0 and new_member_count>0 then 1 when new_member_count=0 and new_member_count_yesterday=0 then 0 else (new_member_count-new_member_count_yesterday)::numeric/new_member_count_yesterday::numeric end as decimal(10,2)) as \"newMemberCountsUp\"," +
//
//                //老会员数
//                "old_member_count as \"oldMemberCounts\", " +
//                "cast(case when old_member_count_yesterday =0 and old_member_count>0 then 1 when old_member_count_yesterday=0 and old_member_count=0 then 0 else (old_member_count-old_member_count_yesterday)::numeric/old_member_count_yesterday::numeric end as decimal(10,2)) as \"oldMemberCountsUp\"," +
//
//                //新会员订单数
//                "new_pay_count as \"newMemberOrders\", " +
//                "cast(case when new_pay_count_yesterday =0 and new_pay_count>0 then 1 when new_pay_count_yesterday=0 and new_pay_count=0 then 0 else (new_pay_count-new_pay_count_yesterday)::numeric/new_pay_count_yesterday::numeric end as decimal(10,2)) as \"newMemberOrdersUp\"," +
//
//                //老会员订单数
//                "old_pay_count as \"oldMemberOrders\", " +
//                "cast(case when old_pay_count_yesterday =0 and old_pay_count>0 then 1 when old_pay_count_yesterday=0 and old_pay_count=0 then 0 else (old_pay_count-old_pay_count_yesterday)::numeric/old_pay_count_yesterday::numeric end as decimal(10,2)) as \"oldMemberOrdersUp\"," +
//
//                //新会员启动数
//                "new_consume_count as \"newMemberStarts\", " +
//                "cast(case when new_consume_count_yesterday =0 and new_consume_count>0 then 1 when new_consume_count_yesterday=0 and new_consume_count=0 then 0 else (new_consume_count-new_consume_count_yesterday)::numeric/new_consume_count_yesterday::numeric end as decimal(10,2)) as \"newMemberStartsUp\"," +
//
//                //老会员启动数
//                "old_consume_count as \"oldMemberStarts\", " +
//                "cast(case when old_consume_count_yesterday =0 and old_consume_count>0 then 1 when old_consume_count_yesterday=0 and old_consume_count=0 then 0 else (old_consume_count-old_consume_count_yesterday)::numeric/old_consume_count_yesterday::numeric end as decimal(10,2)) as \"oldMemberStartsUp\"," +
//
//                //新会员日均订单数
//                "new_pay_count/1 as \"perNewMemberDayOrders\", " +
//                "case when new_pay_count =0 then 0" +
//                "when (new_pay_count_yesterday = 0) and new_pay_count > 0 then 1" +
//                "else cast( ((new_pay_count/1) - (new_pay_count_yesterday/1))::numeric/(new_pay_count_yesterday/1)::numeric as decimal(10,2)) end  as \"perNewMemberDayOrdersUp\","+
//
//                //老会员日均订单数
//                "old_pay_count/1 as \"perOldMemberDayOrders\", " +
//                "case when old_pay_count =0 then 0" +
//                "when (old_pay_count_yesterday = 0) and old_pay_count > 0 then 1" +
//                "else cast( ((old_pay_count/1) - (old_pay_count_yesterday/1))::numeric/(old_pay_count_yesterday/1)::numeric as decimal(10,2)) end  as \"perOldMemberDayOrdersUp\","+
//
//                //新会员日均启动次
//                "new_consume_count/1 as \"perNewMemberDayStarts\", " +
//                "case when new_consume_count =0 then 0" +
//                "when (new_consume_count_yesterday = 0) and new_consume_count > 0 then 1" +
//                "else cast( ((new_consume_count/1) - (new_consume_count_yesterday/1))::numeric/(new_consume_count_yesterday/1)::numeric as decimal(10,2)) end  as \"perNewMemberDayStartsUp\","+
//
//                //老会员日均启动次
//                "old_consume_count/1 as \"perOldMemberDayStarts\", " +
//                "case when old_consume_count =0 then 0" +
//                "when (old_consume_count_yesterday = 0) and old_consume_count > 0 then 1" +
//                "else cast( ((old_consume_count/1) - (old_consume_count_yesterday/1))::numeric/(old_consume_count_yesterday/1)::numeric as decimal(10,2)) end  as \"perOldMemberDayStartsUp\","+
//
//                //新会员客单价
//                "cast(case when new_pay_count=0 then 0 else new_pay_amount::numeric/new_pay_count::numeric end as decimal(10,2)) as \"perNewMemberOrderAmount\", " +
//                "case when new_pay_count =0 then 0" +
//                "when (new_pay_amount_yesterday = 0 or new_pay_count_yesterday=0) and new_pay_amount > 0 then 1" +
//                "else cast( ((new_pay_amount::numeric/new_pay_count::numeric) - (new_pay_amount_yesterday::numeric/new_pay_count_yesterday::numeric))::numeric/(new_pay_amount_yesterday::numeric/new_pay_count_yesterday::numeric)::numeric as decimal(10,2)) end  as \"perNewMemberOrderAmountUp\","+
//
//                //老会员客单价
//                "cast(case when old_pay_count=0 then 0 else old_pay_amount::numeric/old_pay_count::numeric end as decimal(10,2)) as \"perOldMemberOrderAmount\", " +
//                "case when old_pay_count =0 then 0" +
//                "when (old_pay_amount_yesterday = 0 or old_pay_count_yesterday=0) and old_pay_amount > 0 then 1" +
//                "else cast( ((old_pay_amount::numeric/old_pay_count::numeric) - (old_pay_amount_yesterday::numeric/old_pay_count_yesterday::numeric))::numeric/(old_pay_amount_yesterday::numeric/old_pay_count_yesterday::numeric) as decimal(10,2)) end  as \"perOldMemberOrderAmountUp\""
//        );
//
//
//        params.getQueryParams().setDay(dayParamDto.getDay());
//        params.getQueryParams().setMerchant_id(dayParamDto.getMerchantId()+"");
//        params.getQueryParams().setCombo_type(dayParamDto.getComboType());  //因为会只到商家维度
//
//        params.getQueryParams().setProvince_id(dayParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(dayParamDto.getProvinceId()+"");}});
//        params.getQueryParams().setCity_id(dayParamDto.getCityId() == null ? null:new HashSet<String>(){{add(dayParamDto.getCityId()+"");}});
//        params.getQueryParams().setDistrict_id(dayParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(dayParamDto.getAreaId()+"");}});
//        params.getQueryParams().setEquipment_group_id(dayParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(dayParamDto.getGroupId()+"");}});
//        params.getQueryParams().setLyy_equipment_type_id(dayParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(dayParamDto.getEquipmentTypeId()+"");}});
//
//        if(dayParamDto.getGroupIds() != null && dayParamDto.getGroupIds().size()>0){
//            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//            if(dayParamDto.getGroupId() == null || !dayParamDto.getGroupIds().contains(dayParamDto.getGroupId()+"")){
//                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(dayParamDto.getGroupIds())).first()+"");}});
//            }
//            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
//            params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
//        }
//
//        try{
//            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
//            JSONObject result = oneDataServiceFeign.getData(params,headers);
//            log.info("{}",result.toJSONString());
//            return result;
//        }catch (Exception e){
//            log.warn("{}",e);
//        }
//
//
//
//        return null;
//    }
//
//    @Override
//    public JSONObject getMemberBenefitData(DayParamDto dayParamDto) {
//        MultiValueMap<String, String> headers = new HttpHeaders();
//        headers.add("sk",sk);
//        headers.add("ak",ak);
//
//        ParamDto params = new ParamDto();
//        params.setCode(dayMemberBenefitDataCode);
//
//        params.setColumnArray("merchant_id as \"merchantId\", benefit_kind as \"benefitType\", " +
//                //新增储值额度
//                "increase_benefit_amount as \"addAmount\", " +
//                "cast(case when last_cycle_increase_benefit_amount =0 and increase_benefit_amount>0 then 1 when last_cycle_increase_benefit_amount=0 and increase_benefit_amount=0 then 0 else (increase_benefit_amount-last_cycle_increase_benefit_amount)::numeric/last_cycle_increase_benefit_amount::numeric end as decimal(10,2)) as \"addAmountUp\"," +
//
//                //储值支付金额
//                "benefit_payment_amount  as \"benefitPayAmount\", " +
//                "cast(case when last_cycle_benefit_payment_amount  =0 and benefit_payment_amount>0 then 1 when last_cycle_benefit_payment_amount =0 and benefit_payment_amount=0 then 0 else (benefit_payment_amount-last_cycle_benefit_payment_amount )::numeric/last_cycle_benefit_payment_amount ::numeric end as decimal(10,2)) as \"benefitPayAmountUp\"," +
//
//                //储值订单数
//                "benefit_payment_order_num as \"benefitOrders\", " +
//                "cast(case when last_cycle_benefit_payment_order_num =0 and benefit_payment_order_num>0 then 1 when benefit_payment_order_num=0 and last_cycle_benefit_payment_order_num=0 then 0 else (benefit_payment_order_num-last_cycle_benefit_payment_order_num)::numeric/last_cycle_benefit_payment_order_num::numeric end as decimal(10,2)) as \"benefitOrdersUp\"," +
//
//                //储值会员
//                "benefit_payment_user_num as \"benefitUsers\", " +
//                "cast(case when last_cycle_benefit_payment_user_num =0 and benefit_payment_user_num>0 then 1 when last_cycle_benefit_payment_user_num=0 and benefit_payment_user_num=0 then 0 else (benefit_payment_user_num-last_cycle_benefit_payment_user_num)::numeric/last_cycle_benefit_payment_user_num::numeric end as decimal(10,2)) as \"benefitUsersUp\"," +
//
//                //储值客单价
//                "avg_payment_amount_per_user as \"orderPrice\", " +
//                "cast(case when last_cycle_avg_payment_amount_per_user =0 and avg_payment_amount_per_user>0 then 1 when last_cycle_avg_payment_amount_per_user=0 and avg_payment_amount_per_user=0 then 0 else (avg_payment_amount_per_user-last_cycle_avg_payment_amount_per_user)::numeric/last_cycle_avg_payment_amount_per_user::numeric end as decimal(10,2)) as \"orderPriceUp\"," +
//
//                //储值设备数
//                "benefit_payment_equip_num as \"benefitEquipments\", " +
//                "cast(case when last_cycle_benefit_payment_equip_num =0 and benefit_payment_equip_num>0 then 1 when last_cycle_benefit_payment_equip_num=0 and benefit_payment_equip_num=0 then 0 else (benefit_payment_equip_num-last_cycle_benefit_payment_equip_num)::numeric/last_cycle_benefit_payment_equip_num::numeric end as decimal(10,2)) as \"benefitEquipmentsUp\"," +
//
//                //储值场地数
//                "benefit_payment_group_num as \"benefitGroups\", " +
//                "cast(case when last_cycle_benefit_payment_group_num =0 and benefit_payment_group_num>0 then 1 when last_cycle_benefit_payment_group_num=0 and benefit_payment_group_num=0 then 0 else (benefit_payment_group_num-last_cycle_benefit_payment_group_num)::numeric/last_cycle_benefit_payment_group_num::numeric end as decimal(10,2)) as \"benefitGroupsUp\"," +
//
//                //消耗储值
//                "comsume_benefit_amount as \"consumerBenefit\", " +
//                "cast(case when last_cycle_comsume_benefit_amount =0 and comsume_benefit_amount>0 then 1 when last_cycle_comsume_benefit_amount=0 and comsume_benefit_amount=0 then 0 else (comsume_benefit_amount-last_cycle_comsume_benefit_amount)::numeric/last_cycle_comsume_benefit_amount::numeric end as decimal(10,2)) as \"consumerBenefitUp\"," +
//
//                //消耗储值订单数
//                "benefit_consume_order_num as \"consumerBenefitOrders\", " +
//                "cast(case when last_cycle_benefit_consume_order_num =0 and benefit_consume_order_num>0 then 1 when last_cycle_benefit_consume_order_num=0 and benefit_consume_order_num=0 then 0 else (benefit_consume_order_num-last_cycle_benefit_consume_order_num)::numeric/last_cycle_benefit_consume_order_num::numeric end as decimal(10,2)) as \"consumerBenefitOrdersUp\"," +
//
//
//                //消耗储值会员
//                "benefit_consume_user_num as \"consumerBenefitUsers\", " +
//                "cast(case when last_cycle_benefit_consume_user_num =0 and benefit_consume_user_num>0 then 1 when last_cycle_benefit_consume_user_num=0 and benefit_consume_user_num=0 then 0 else (benefit_consume_user_num-last_cycle_benefit_consume_user_num)::numeric/last_cycle_benefit_consume_user_num::numeric end as decimal(10,2)) as \"consumerBenefitUsersUp\"," +
//
//
//                //消耗储值客单价
//                "avg_consume_amount_per_user as \"consumerBenefitOrderPrice\", " +
//                "cast(case when last_cycle_avg_consume_amount_per_user =0 and avg_consume_amount_per_user>0 then 1 when last_cycle_avg_consume_amount_per_user=0 and avg_consume_amount_per_user=0 then 0 else (avg_consume_amount_per_user-last_cycle_avg_consume_amount_per_user)::numeric/last_cycle_avg_consume_amount_per_user::numeric end as decimal(10,2)) as \"consumerBenefitOrderPriceUp\"," +
//
//                //消耗储值设备数
//                "benefit_consume_equip_num as \"consumerBenefitEquipments\", " +
//                "cast(case when last_cycle_benefit_consume_equip_num =0 and benefit_consume_equip_num>0 then 1 when last_cycle_benefit_consume_equip_num=0 and benefit_consume_equip_num=0 then 0 else (benefit_consume_equip_num-last_cycle_benefit_consume_equip_num)::numeric/last_cycle_benefit_consume_equip_num::numeric end as decimal(10,2)) as \"consumerBenefitEquipmentsUp\"," +
//
//                //消耗储值场地数
//                "benefit_consume_group_num as \"consumerBenefitGroups\", " +
//                "cast(case when last_cycle_benefit_consume_group_num =0 and benefit_consume_group_num>0 then 1 when last_cycle_benefit_consume_group_num=0 and benefit_consume_group_num=0 then 0 else (benefit_consume_group_num-last_cycle_benefit_consume_group_num)::numeric/last_cycle_benefit_consume_group_num::numeric end as decimal(10,2)) as \"consumerBenefitGroupsUp\"," +
//
//
//                //消耗储值启动设备数
//                "consume_benefit_tostart_equip_num as \"consumerBenefitStartEquipments\", " +
//                "cast(case when last_cycle_consume_benefit_tostart_equip_num =0 and consume_benefit_tostart_equip_num>0 then 1 when last_cycle_consume_benefit_tostart_equip_num=0 and consume_benefit_tostart_equip_num=0 then 0 else (consume_benefit_tostart_equip_num-last_cycle_consume_benefit_tostart_equip_num)::numeric/last_cycle_consume_benefit_tostart_equip_num::numeric end as decimal(10,2)) as \"consumerBenefitStartEquipmentsUp\"," +
//
//                //消耗储值启动设备日均数
//                "per_day_consumetostart_equip_num as \"perDayConsumerBenefitStartEquipments\", " +
//                "cast(case when last_cycle_per_day_consumetostart_equip_num =0 and per_day_consumetostart_equip_num>0 then 1 when last_cycle_per_day_consumetostart_equip_num=0 and per_day_consumetostart_equip_num=0 then 0 else (per_day_consumetostart_equip_num-last_cycle_per_day_consumetostart_equip_num)::numeric/last_cycle_per_day_consumetostart_equip_num::numeric end as decimal(10,2)) as \"perDayConsumerBenefitStartEquipmentsUp\""
//
//        );
//
//
//        params.getQueryParams().setDay(dayParamDto.getDay());
//        params.getQueryParams().setMerchant_id(dayParamDto.getMerchantId()+"");
//        params.getQueryParams().setBenefit_kind(dayParamDto.getBenefitType());
//        params.getQueryParams().setCombo_type(dayParamDto.getComboType());  //因为会只到商家维度
//
//        params.getQueryParams().setProvince_id(dayParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(dayParamDto.getProvinceId()+"");}});
//        params.getQueryParams().setCity_id(dayParamDto.getCityId() == null ? null:new HashSet<String>(){{add(dayParamDto.getCityId()+"");}});
//        params.getQueryParams().setDistrict_id(dayParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(dayParamDto.getAreaId()+"");}});
//        params.getQueryParams().setEquipment_group_id(dayParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(dayParamDto.getGroupId()+"");}});
//        params.getQueryParams().setLyy_equipment_type_id(dayParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(dayParamDto.getEquipmentTypeId()+"");}});
//
//        if(dayParamDto.getGroupIds() != null && dayParamDto.getGroupIds().size()>0){
//            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//            if(dayParamDto.getGroupId() == null || !dayParamDto.getGroupIds().contains(dayParamDto.getGroupId()+"")){
//                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(dayParamDto.getGroupIds())).first()+"");}});
//            }
//            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
//            params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
//        }
//
//        try{
//            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
//            JSONObject result = oneDataServiceFeign.getData(params,headers);
//            log.info("{}",result.toJSONString());
//            return result;
//        }catch (Exception e){
//            log.warn("{}",e);
//        }
//
//
//
//        return null;
//    }
//
//    @Override
//    public JSONObject getWeekMemberBenefitData(WeekParamDto weekParamDto) {
//        MultiValueMap<String, String> headers = new HttpHeaders();
//        headers.add("sk",sk);
//        headers.add("ak",ak);
//
//        ParamDto params = new ParamDto();
//        params.setCode(weekMemberBenefitDataCode);
//
//        params.setColumnArray("merchant_id as \"merchantId\", benefit_kind as \"benefitType\", " +
//                //新增储值额度
//                "increase_benefit_amount as \"addAmount\", " +
//                "cast(case when last_cycle_increase_benefit_amount =0 and increase_benefit_amount>0 then 1 when last_cycle_increase_benefit_amount=0 and increase_benefit_amount=0 then 0 else (increase_benefit_amount::numeric-last_cycle_increase_benefit_amount::numeric)::numeric/last_cycle_increase_benefit_amount::numeric end as decimal(10,2)) as \"addAmountUp\"," +
//
//                //储值支付金额
//                "benefit_payment_amount  as \"benefitPayAmount\", " +
//                "cast(case when last_cycle_benefit_payment_amount  =0 and benefit_payment_amount>0 then 1 when last_cycle_benefit_payment_amount =0 and benefit_payment_amount=0 then 0 else (benefit_payment_amount-last_cycle_benefit_payment_amount )::numeric/last_cycle_benefit_payment_amount ::numeric end as decimal(10,2)) as \"benefitPayAmountUp\"," +
//
//
//                //储值订单数
//                "benefit_payment_order_num as \"benefitOrders\", " +
//                "cast(case when last_cycle_benefit_payment_order_num =0 and benefit_payment_order_num>0 then 1 when benefit_payment_order_num=0 and last_cycle_benefit_payment_order_num=0 then 0 else (benefit_payment_order_num::numeric-last_cycle_benefit_payment_order_num::numeric)::numeric/last_cycle_benefit_payment_order_num::numeric end as decimal(10,2)) as \"benefitOrdersUp\"," +
//
//                //储值会员
//                "benefit_payment_user_num as \"benefitUsers\", " +
//                "cast(case when last_cycle_benefit_payment_user_num =0 and benefit_payment_user_num>0 then 1 when last_cycle_benefit_payment_user_num=0 and benefit_payment_user_num=0 then 0 else (benefit_payment_user_num::numeric-last_cycle_benefit_payment_user_num::numeric)::numeric/last_cycle_benefit_payment_user_num::numeric end as decimal(10,2)) as \"benefitUsersUp\"," +
//
//                //储值客单价
//                "avg_payment_amount_per_user as \"orderPrice\", " +
//                "cast(case when last_cycle_avg_payment_amount_per_user =0 and avg_payment_amount_per_user>0 then 1 when last_cycle_avg_payment_amount_per_user=0 and avg_payment_amount_per_user=0 then 0 else (avg_payment_amount_per_user::numeric-last_cycle_avg_payment_amount_per_user::numeric)::numeric/last_cycle_avg_payment_amount_per_user::numeric end as decimal(10,2)) as \"orderPriceUp\"," +
//
//                //储值设备数
//                "benefit_payment_equip_num as \"benefitEquipments\", " +
//                "cast(case when last_cycle_benefit_payment_equip_num =0 and benefit_payment_equip_num>0 then 1 when last_cycle_benefit_payment_equip_num=0 and benefit_payment_equip_num=0 then 0 else (benefit_payment_equip_num::numeric-last_cycle_benefit_payment_equip_num::numeric)::numeric/last_cycle_benefit_payment_equip_num::numeric end as decimal(10,2)) as \"benefitEquipmentsUp\"," +
//
//                //储值场地数
//                "benefit_payment_group_num as \"benefitGroups\", " +
//                "cast(case when last_cycle_benefit_payment_group_num =0 and benefit_payment_group_num>0 then 1 when last_cycle_benefit_payment_group_num=0 and benefit_payment_group_num=0 then 0 else (benefit_payment_group_num::numeric-last_cycle_benefit_payment_group_num::numeric)::numeric/last_cycle_benefit_payment_group_num::numeric end as decimal(10,2)) as \"benefitGroupsUp\"," +
//
//                //消耗储值
//                "comsume_benefit_amount as \"consumerBenefit\", " +
//                "cast(case when last_cycle_comsume_benefit_amount =0 and comsume_benefit_amount>0 then 1 when last_cycle_comsume_benefit_amount=0 and comsume_benefit_amount=0 then 0 else (comsume_benefit_amount::numeric-last_cycle_comsume_benefit_amount::numeric)::numeric/last_cycle_comsume_benefit_amount::numeric end as decimal(10,2)) as \"consumerBenefitUp\"," +
//
//                //消耗储值订单数
//                "benefit_consume_order_num as \"consumerBenefitOrders\", " +
//                "cast(case when last_cycle_benefit_consume_order_num =0 and benefit_consume_order_num>0 then 1 when last_cycle_benefit_consume_order_num=0 and benefit_consume_order_num=0 then 0 else (benefit_consume_order_num::numeric-last_cycle_benefit_consume_order_num::numeric)::numeric/last_cycle_benefit_consume_order_num::numeric end as decimal(10,2)) as \"consumerBenefitOrdersUp\"," +
//
//
//                //消耗储值会员
//                "benefit_consume_user_num as \"consumerBenefitUsers\", " +
//                "cast(case when last_cycle_benefit_consume_user_num =0 and benefit_consume_user_num>0 then 1 when last_cycle_benefit_consume_user_num=0 and benefit_consume_user_num=0 then 0 else (benefit_consume_user_num::numeric-last_cycle_benefit_consume_user_num::numeric)::numeric/last_cycle_benefit_consume_user_num::numeric end as decimal(10,2)) as \"consumerBenefitUsersUp\"," +
//
//
//                //消耗储值客单价
//                "avg_consume_amount_per_user as \"consumerBenefitOrderPrice\", " +
//                "cast(case when last_cycle_avg_consume_amount_per_user =0 and avg_consume_amount_per_user>0 then 1 when last_cycle_avg_consume_amount_per_user=0 and avg_consume_amount_per_user=0 then 0 else (avg_consume_amount_per_user::numeric-last_cycle_avg_consume_amount_per_user::numeric)::numeric/last_cycle_avg_consume_amount_per_user::numeric end as decimal(10,2)) as \"consumerBenefitOrderPriceUp\"," +
//
//                //消耗储值设备数
//                "benefit_consume_equip_num as \"consumerBenefitEquipments\", " +
//                "cast(case when last_cycle_benefit_consume_equip_num =0 and benefit_consume_equip_num>0 then 1 when last_cycle_benefit_consume_equip_num=0 and benefit_consume_equip_num=0 then 0 else (benefit_consume_equip_num::numeric-last_cycle_benefit_consume_equip_num::numeric)::numeric/last_cycle_benefit_consume_equip_num::numeric end as decimal(10,2)) as \"consumerBenefitEquipmentsUp\"," +
//
//                //消耗储值场地数
//                "benefit_consume_group_num as \"consumerBenefitGroups\", " +
//                "cast(case when last_cycle_benefit_consume_group_num =0 and benefit_consume_group_num>0 then 1 when last_cycle_benefit_consume_group_num=0 and benefit_consume_group_num=0 then 0 else (benefit_consume_group_num::numeric-last_cycle_benefit_consume_group_num::numeric)::numeric/last_cycle_benefit_consume_group_num::numeric end as decimal(10,2)) as \"consumerBenefitGroupsUp\"," +
//
//
//                //消耗储值启动设备数
//                "consume_benefit_tostart_equip_num as \"consumerBenefitStartEquipments\", " +
//                "cast(case when last_cycle_consume_benefit_tostart_equip_num =0 and consume_benefit_tostart_equip_num>0 then 1 when last_cycle_consume_benefit_tostart_equip_num=0 and consume_benefit_tostart_equip_num=0 then 0 else (consume_benefit_tostart_equip_num::numeric-last_cycle_consume_benefit_tostart_equip_num::numeric)::numeric/last_cycle_consume_benefit_tostart_equip_num::numeric end as decimal(10,2)) as \"consumerBenefitStartEquipmentsUp\"," +
//
//                //消耗储值启动设备日均数
//                "per_day_consumetostart_equip_num as \"perDayConsumerBenefitStartEquipments\", " +
//                "cast(case when last_cycle_per_day_consumetostart_equip_num =0 and per_day_consumetostart_equip_num>0 then 1 when last_cycle_per_day_consumetostart_equip_num=0 and per_day_consumetostart_equip_num=0 then 0 else (per_day_consumetostart_equip_num::numeric-last_cycle_per_day_consumetostart_equip_num::numeric)::numeric/last_cycle_per_day_consumetostart_equip_num::numeric end as decimal(10,2)) as \"perDayConsumerBenefitStartEquipmentsUp\""
//
//        );
//
//
//        params.getQueryParams().setBenefit_kind(weekParamDto.getBenefitType());
//        params.getQueryParams().setMerchant_id(weekParamDto.getMerchantId()+"");
//        params.getQueryParams().setYear_month(weekParamDto.getMonth());
//        params.getQueryParams().setMonth_week_num(weekParamDto.getWeek()+"");
//        params.getQueryParams().setCombo_type(weekParamDto.getComboType());
//
//        params.getQueryParams().setProvince_id(weekParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(weekParamDto.getProvinceId()+"");}});
//        params.getQueryParams().setCity_id(weekParamDto.getCityId() == null ? null:new HashSet<String>(){{add(weekParamDto.getCityId()+"");}});
//        params.getQueryParams().setDistrict_id(weekParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(weekParamDto.getAreaId()+"");}});
//        params.getQueryParams().setEquipment_group_id(weekParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(weekParamDto.getGroupId()+"");}});
//        params.getQueryParams().setLyy_equipment_type_id(weekParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(weekParamDto.getEquipmentTypeId()+"");}});
//
//        if(weekParamDto.getGroupIds() != null && weekParamDto.getGroupIds().size()>0){
//            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//            if(weekParamDto.getGroupId() == null || !weekParamDto.getGroupIds().contains(weekParamDto.getGroupId()+"")){
//                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(weekParamDto.getGroupIds())).first()+"");}});
//            }
//            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
//            params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
//        }
//
//        try{
//            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
//            JSONObject result = oneDataServiceFeign.getData(params,headers);
//            log.info("{}",result.toJSONString());
//            return result;
//        }catch (Exception e){
//            log.warn("{}",e);
//        }
//
//
//
//        return null;
//    }
//
//
//    @Override
//    public JSONObject getYearMemberBenefitData(YearParamDto yearParamDto) {
//        MultiValueMap<String, String> headers = new HttpHeaders();
//        headers.add("sk",sk);
//        headers.add("ak",ak);
//
//        ParamDto params = new ParamDto();
//        params.setCode(yearMemberBenefitDataCode);
//
//        params.setColumnArray("merchant_id as \"merchantId\", benefit_kind as \"benefitType\", " +
//                //新增储值额度
//                "increase_benefit_amount as \"addAmount\", " +
//                "cast(case when last_cycle_increase_benefit_amount =0 and increase_benefit_amount>0 then 1 when last_cycle_increase_benefit_amount=0 and increase_benefit_amount=0 then 0 else (increase_benefit_amount::numeric-last_cycle_increase_benefit_amount::numeric)::numeric/last_cycle_increase_benefit_amount::numeric end as decimal(10,2)) as \"addAmountUp\"," +
//
//                //储值支付金额
//                "benefit_payment_amount  as \"benefitPayAmount\", " +
//                "cast(case when last_cycle_benefit_payment_amount  =0 and benefit_payment_amount>0 then 1 when last_cycle_benefit_payment_amount =0 and benefit_payment_amount=0 then 0 else (benefit_payment_amount-last_cycle_benefit_payment_amount )::numeric/last_cycle_benefit_payment_amount ::numeric end as decimal(10,2)) as \"benefitPayAmountUp\"," +
//
//                //储值订单数
//                "benefit_payment_order_num as \"benefitOrders\", " +
//                "cast(case when last_cycle_benefit_payment_order_num =0 and benefit_payment_order_num>0 then 1 when benefit_payment_order_num=0 and last_cycle_benefit_payment_order_num=0 then 0 else (benefit_payment_order_num::numeric-last_cycle_benefit_payment_order_num::numeric)::numeric/last_cycle_benefit_payment_order_num::numeric end as decimal(10,2)) as \"benefitOrdersUp\"," +
//
//                //储值会员
//                "benefit_payment_user_num as \"benefitUsers\", " +
//                "cast(case when last_cycle_benefit_payment_user_num =0 and benefit_payment_user_num>0 then 1 when last_cycle_benefit_payment_user_num=0 and benefit_payment_user_num=0 then 0 else (benefit_payment_user_num::numeric-last_cycle_benefit_payment_user_num::numeric)::numeric/last_cycle_benefit_payment_user_num::numeric end as decimal(10,2)) as \"benefitUsersUp\"," +
//
//                //储值客单价
//                "avg_payment_amount_per_user as \"orderPrice\", " +
//                "cast(case when last_cycle_avg_payment_amount_per_user =0 and avg_payment_amount_per_user>0 then 1 when last_cycle_avg_payment_amount_per_user=0 and avg_payment_amount_per_user=0 then 0 else (avg_payment_amount_per_user::numeric-last_cycle_avg_payment_amount_per_user::numeric)::numeric/last_cycle_avg_payment_amount_per_user::numeric end as decimal(10,2)) as \"orderPriceUp\"," +
//
//                //储值设备数
//                "benefit_payment_equip_num as \"benefitEquipments\", " +
//                "cast(case when last_cycle_benefit_payment_equip_num =0 and benefit_payment_equip_num>0 then 1 when last_cycle_benefit_payment_equip_num=0 and benefit_payment_equip_num=0 then 0 else (benefit_payment_equip_num::numeric-last_cycle_benefit_payment_equip_num::numeric)::numeric/last_cycle_benefit_payment_equip_num::numeric end as decimal(10,2)) as \"benefitEquipmentsUp\"," +
//
//                //储值场地数
//                "benefit_payment_group_num as \"benefitGroups\", " +
//                "cast(case when last_cycle_benefit_payment_group_num =0 and benefit_payment_group_num>0 then 1 when last_cycle_benefit_payment_group_num=0 and benefit_payment_group_num=0 then 0 else (benefit_payment_group_num::numeric-last_cycle_benefit_payment_group_num::numeric)::numeric/last_cycle_benefit_payment_group_num::numeric end as decimal(10,2)) as \"benefitGroupsUp\"," +
//
//                //消耗储值
//                "comsume_benefit_amount as \"consumerBenefit\", " +
//                "cast(case when last_cycle_comsume_benefit_amount =0 and comsume_benefit_amount>0 then 1 when last_cycle_comsume_benefit_amount=0 and comsume_benefit_amount=0 then 0 else (comsume_benefit_amount::numeric-last_cycle_comsume_benefit_amount::numeric)::numeric/last_cycle_comsume_benefit_amount::numeric end as decimal(10,2)) as \"consumerBenefitUp\"," +
//
//                //消耗储值订单数
//                "benefit_consume_order_num as \"consumerBenefitOrders\", " +
//                "cast(case when last_cycle_benefit_consume_order_num =0 and benefit_consume_order_num>0 then 1 when last_cycle_benefit_consume_order_num=0 and benefit_consume_order_num=0 then 0 else (benefit_consume_order_num::numeric-last_cycle_benefit_consume_order_num::numeric)::numeric/last_cycle_benefit_consume_order_num::numeric end as decimal(10,2)) as \"consumerBenefitOrdersUp\"," +
//
//
//                //消耗储值会员
//                "benefit_consume_user_num as \"consumerBenefitUsers\", " +
//                "cast(case when last_cycle_benefit_consume_user_num =0 and benefit_consume_user_num>0 then 1 when last_cycle_benefit_consume_user_num=0 and benefit_consume_user_num=0 then 0 else (benefit_consume_user_num::numeric-last_cycle_benefit_consume_user_num::numeric)::numeric/last_cycle_benefit_consume_user_num::numeric end as decimal(10,2)) as \"consumerBenefitUsersUp\"," +
//
//
//                //消耗储值客单价
//                "avg_consume_amount_per_user as \"consumerBenefitOrderPrice\", " +
//                "cast(case when last_cycle_avg_consume_amount_per_user =0 and avg_consume_amount_per_user>0 then 1 when last_cycle_avg_consume_amount_per_user=0 and avg_consume_amount_per_user=0 then 0 else (avg_consume_amount_per_user::numeric-last_cycle_avg_consume_amount_per_user::numeric)::numeric/last_cycle_avg_consume_amount_per_user::numeric end as decimal(10,2)) as \"consumerBenefitOrderPriceUp\"," +
//
//                //消耗储值设备数
//                "benefit_consume_equip_num as \"consumerBenefitEquipments\", " +
//                "cast(case when last_cycle_benefit_consume_equip_num =0 and benefit_consume_equip_num>0 then 1 when last_cycle_benefit_consume_equip_num=0 and benefit_consume_equip_num=0 then 0 else (benefit_consume_equip_num::numeric-last_cycle_benefit_consume_equip_num::numeric)::numeric/last_cycle_benefit_consume_equip_num::numeric end as decimal(10,2)) as \"consumerBenefitEquipmentsUp\"," +
//
//                //消耗储值场地数
//                "benefit_consume_group_num as \"consumerBenefitGroups\", " +
//                "cast(case when last_cycle_benefit_consume_group_num =0 and benefit_consume_group_num>0 then 1 when last_cycle_benefit_consume_group_num=0 and benefit_consume_group_num=0 then 0 else (benefit_consume_group_num::numeric-last_cycle_benefit_consume_group_num::numeric)::numeric/last_cycle_benefit_consume_group_num::numeric end as decimal(10,2)) as \"consumerBenefitGroupsUp\"," +
//
//
//                //消耗储值启动设备数
//                "consume_benefit_tostart_equip_num as \"consumerBenefitStartEquipments\", " +
//                "cast(case when last_cycle_consume_benefit_tostart_equip_num =0 and consume_benefit_tostart_equip_num>0 then 1 when last_cycle_consume_benefit_tostart_equip_num=0 and consume_benefit_tostart_equip_num=0 then 0 else (consume_benefit_tostart_equip_num::numeric-last_cycle_consume_benefit_tostart_equip_num::numeric)::numeric/last_cycle_consume_benefit_tostart_equip_num::numeric end as decimal(10,2)) as \"consumerBenefitStartEquipmentsUp\"," +
//
//                //消耗储值启动设备日均数
//                "per_day_consumetostart_equip_num as \"perDayConsumerBenefitStartEquipments\", " +
//                "cast(case when last_cycle_per_day_consumetostart_equip_num =0 and per_day_consumetostart_equip_num>0 then 1 when last_cycle_per_day_consumetostart_equip_num=0 and per_day_consumetostart_equip_num=0 then 0 else (per_day_consumetostart_equip_num::numeric-last_cycle_per_day_consumetostart_equip_num::numeric)::numeric/last_cycle_per_day_consumetostart_equip_num::numeric end as decimal(10,2)) as \"perDayConsumerBenefitStartEquipmentsUp\""
//
//        );
//
//
//        params.getQueryParams().setBenefit_kind(yearParamDto.getBenefitType());
//        params.getQueryParams().setMerchant_id(yearParamDto.getMerchantId()+"");
//        params.getQueryParams().setYear_str(yearParamDto.getYear()+"");
//        params.getQueryParams().setCombo_type(yearParamDto.getComboType());
//
//        params.getQueryParams().setProvince_id(yearParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(yearParamDto.getProvinceId()+"");}});
//        params.getQueryParams().setCity_id(yearParamDto.getCityId() == null ? null:new HashSet<String>(){{add(yearParamDto.getCityId()+"");}});
//        params.getQueryParams().setDistrict_id(yearParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(yearParamDto.getAreaId()+"");}});
//        params.getQueryParams().setEquipment_group_id(yearParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(yearParamDto.getGroupId()+"");}});
//        params.getQueryParams().setLyy_equipment_type_id(yearParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(yearParamDto.getEquipmentTypeId()+"");}});
//
//        if(yearParamDto.getGroupIds() != null && yearParamDto.getGroupIds().size()>0){
//            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//            if(yearParamDto.getGroupId() == null || !yearParamDto.getGroupIds().contains(yearParamDto.getGroupId()+"")){
//                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(yearParamDto.getGroupIds())).first()+"");}});
//            }
//            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
//            params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
//        }
//
//        try{
//            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
//            JSONObject result = oneDataServiceFeign.getData(params,headers);
//            log.info("{}",result.toJSONString());
//            return result;
//        }catch (Exception e){
//            log.warn("{}",e);
//        }
//
//
//
//        return null;
//    }
//
//
//    @Override
//    public JSONObject getMonthMemberBenefitData(MonthParamDto monthParamDto) {
//        MultiValueMap<String, String> headers = new HttpHeaders();
//        headers.add("sk",sk);
//        headers.add("ak",ak);
//
//        ParamDto params = new ParamDto();
//        params.setCode(monthMemberBenefitDataCode);
//
//        params.setColumnArray("merchant_id as \"merchantId\", benefit_kind as \"benefitType\", " +
//                //新增储值额度
//                "increase_benefit_amount as \"addAmount\", " +
//                "cast(case when last_cycle_increase_benefit_amount =0 and increase_benefit_amount>0 then 1 when last_cycle_increase_benefit_amount=0 and increase_benefit_amount=0 then 0 else (increase_benefit_amount::numeric-last_cycle_increase_benefit_amount::numeric)::numeric/last_cycle_increase_benefit_amount::numeric end as decimal(10,2)) as \"addAmountUp\"," +
//
//                //储值支付金额
//                "benefit_payment_amount  as \"benefitPayAmount\", " +
//                "cast(case when last_cycle_benefit_payment_amount  =0 and benefit_payment_amount>0 then 1 when last_cycle_benefit_payment_amount =0 and benefit_payment_amount=0 then 0 else (benefit_payment_amount-last_cycle_benefit_payment_amount )::numeric/last_cycle_benefit_payment_amount ::numeric end as decimal(10,2)) as \"benefitPayAmountUp\"," +
//
//                //储值订单数
//                "benefit_payment_order_num as \"benefitOrders\", " +
//                "cast(case when last_cycle_benefit_payment_order_num =0 and benefit_payment_order_num>0 then 1 when benefit_payment_order_num=0 and last_cycle_benefit_payment_order_num=0 then 0 else (benefit_payment_order_num::numeric-last_cycle_benefit_payment_order_num::numeric)::numeric/last_cycle_benefit_payment_order_num::numeric end as decimal(10,2)) as \"benefitOrdersUp\"," +
//
//                //储值会员
//                "benefit_payment_user_num as \"benefitUsers\", " +
//                "cast(case when last_cycle_benefit_payment_user_num =0 and benefit_payment_user_num>0 then 1 when last_cycle_benefit_payment_user_num=0 and benefit_payment_user_num=0 then 0 else (benefit_payment_user_num::numeric-last_cycle_benefit_payment_user_num::numeric)::numeric/last_cycle_benefit_payment_user_num::numeric end as decimal(10,2)) as \"benefitUsersUp\"," +
//
//                //储值客单价
//                "avg_payment_amount_per_user as \"orderPrice\", " +
//                "cast(case when last_cycle_avg_payment_amount_per_user =0 and avg_payment_amount_per_user>0 then 1 when last_cycle_avg_payment_amount_per_user=0 and avg_payment_amount_per_user=0 then 0 else (avg_payment_amount_per_user::numeric-last_cycle_avg_payment_amount_per_user::numeric)::numeric/last_cycle_avg_payment_amount_per_user::numeric end as decimal(10,2)) as \"orderPriceUp\"," +
//
//                //储值设备数
//                "benefit_payment_equip_num as \"benefitEquipments\", " +
//                "cast(case when last_cycle_benefit_payment_equip_num =0 and benefit_payment_equip_num>0 then 1 when last_cycle_benefit_payment_equip_num=0 and benefit_payment_equip_num=0 then 0 else (benefit_payment_equip_num::numeric-last_cycle_benefit_payment_equip_num::numeric)::numeric/last_cycle_benefit_payment_equip_num::numeric end as decimal(10,2)) as \"benefitEquipmentsUp\"," +
//
//                //储值场地数
//                "benefit_payment_group_num as \"benefitGroups\", " +
//                "cast(case when last_cycle_benefit_payment_group_num =0 and benefit_payment_group_num>0 then 1 when last_cycle_benefit_payment_group_num=0 and benefit_payment_group_num=0 then 0 else (benefit_payment_group_num::numeric-last_cycle_benefit_payment_group_num::numeric)::numeric/last_cycle_benefit_payment_group_num::numeric end as decimal(10,2)) as \"benefitGroupsUp\"," +
//
//                //消耗储值
//                "comsume_benefit_amount as \"consumerBenefit\", " +
//                "cast(case when last_cycle_comsume_benefit_amount =0 and comsume_benefit_amount>0 then 1 when last_cycle_comsume_benefit_amount=0 and comsume_benefit_amount=0 then 0 else (comsume_benefit_amount::numeric-last_cycle_comsume_benefit_amount::numeric)::numeric/last_cycle_comsume_benefit_amount::numeric end as decimal(10,2)) as \"consumerBenefitUp\"," +
//
//                //消耗储值订单数
//                "benefit_consume_order_num as \"consumerBenefitOrders\", " +
//                "cast(case when last_cycle_benefit_consume_order_num =0 and benefit_consume_order_num>0 then 1 when last_cycle_benefit_consume_order_num=0 and benefit_consume_order_num=0 then 0 else (benefit_consume_order_num::numeric-last_cycle_benefit_consume_order_num::numeric)::numeric/last_cycle_benefit_consume_order_num::numeric end as decimal(10,2)) as \"consumerBenefitOrdersUp\"," +
//
//
//                //消耗储值会员
//                "benefit_consume_user_num as \"consumerBenefitUsers\", " +
//                "cast(case when last_cycle_benefit_consume_user_num =0 and benefit_consume_user_num>0 then 1 when last_cycle_benefit_consume_user_num=0 and benefit_consume_user_num=0 then 0 else (benefit_consume_user_num::numeric-last_cycle_benefit_consume_user_num::numeric)::numeric/last_cycle_benefit_consume_user_num::numeric end as decimal(10,2)) as \"consumerBenefitUsersUp\"," +
//
//
//                //消耗储值客单价
//                "avg_consume_amount_per_user as \"consumerBenefitOrderPrice\", " +
//                "cast(case when last_cycle_avg_consume_amount_per_user =0 and avg_consume_amount_per_user>0 then 1 when last_cycle_avg_consume_amount_per_user=0 and avg_consume_amount_per_user=0 then 0 else (avg_consume_amount_per_user::numeric-last_cycle_avg_consume_amount_per_user::numeric)::numeric/last_cycle_avg_consume_amount_per_user::numeric end as decimal(10,2)) as \"consumerBenefitOrderPriceUp\"," +
//
//                //消耗储值设备数
//                "benefit_consume_equip_num as \"consumerBenefitEquipments\", " +
//                "cast(case when last_cycle_benefit_consume_equip_num =0 and benefit_consume_equip_num>0 then 1 when last_cycle_benefit_consume_equip_num=0 and benefit_consume_equip_num=0 then 0 else (benefit_consume_equip_num::numeric-last_cycle_benefit_consume_equip_num::numeric)::numeric/last_cycle_benefit_consume_equip_num::numeric end as decimal(10,2)) as \"consumerBenefitEquipmentsUp\"," +
//
//                //消耗储值场地数
//                "benefit_consume_group_num as \"consumerBenefitGroups\", " +
//                "cast(case when last_cycle_benefit_consume_group_num =0 and benefit_consume_group_num>0 then 1 when last_cycle_benefit_consume_group_num=0 and benefit_consume_group_num=0 then 0 else (benefit_consume_group_num::numeric-last_cycle_benefit_consume_group_num::numeric)::numeric/last_cycle_benefit_consume_group_num::numeric end as decimal(10,2)) as \"consumerBenefitGroupsUp\"," +
//
//
//                //消耗储值启动设备数
//                "consume_benefit_tostart_equip_num as \"consumerBenefitStartEquipments\", " +
//                "cast(case when last_cycle_consume_benefit_tostart_equip_num =0 and consume_benefit_tostart_equip_num>0 then 1 when last_cycle_consume_benefit_tostart_equip_num=0 and consume_benefit_tostart_equip_num=0 then 0 else (consume_benefit_tostart_equip_num::numeric-last_cycle_consume_benefit_tostart_equip_num::numeric)::numeric/last_cycle_consume_benefit_tostart_equip_num::numeric end as decimal(10,2)) as \"consumerBenefitStartEquipmentsUp\"," +
//
//                //消耗储值启动设备日均数
//                "per_day_consumetostart_equip_num as \"perDayConsumerBenefitStartEquipments\", " +
//                "cast(case when last_cycle_per_day_consumetostart_equip_num =0 and per_day_consumetostart_equip_num>0 then 1 when last_cycle_per_day_consumetostart_equip_num=0 and per_day_consumetostart_equip_num=0 then 0 else (per_day_consumetostart_equip_num::numeric-last_cycle_per_day_consumetostart_equip_num::numeric)::numeric/last_cycle_per_day_consumetostart_equip_num::numeric end as decimal(10,2)) as \"perDayConsumerBenefitStartEquipmentsUp\""
//
//        );
//
//
//        params.getQueryParams().setBenefit_kind(monthParamDto.getBenefitType());
//        params.getQueryParams().setMerchant_id(monthParamDto.getMerchantId()+"");
//        params.getQueryParams().setYear_month(monthParamDto.getMonth());
//        params.getQueryParams().setCombo_type(monthParamDto.getComboType());
//
//        params.getQueryParams().setProvince_id(monthParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(monthParamDto.getProvinceId()+"");}});
//        params.getQueryParams().setCity_id(monthParamDto.getCityId() == null ? null:new HashSet<String>(){{add(monthParamDto.getCityId()+"");}});
//        params.getQueryParams().setDistrict_id(monthParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(monthParamDto.getAreaId()+"");}});
//        params.getQueryParams().setEquipment_group_id(monthParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(monthParamDto.getGroupId()+"");}});
//        params.getQueryParams().setLyy_equipment_type_id(monthParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(monthParamDto.getEquipmentTypeId()+"");}});
//
//        if(monthParamDto.getGroupIds() != null && monthParamDto.getGroupIds().size()>0){
//            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//            if(monthParamDto.getGroupId() == null || !monthParamDto.getGroupIds().contains(monthParamDto.getGroupId()+"")){
//                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(monthParamDto.getGroupIds())).first()+"");}});
//            }
//            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
//            params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
//        }
//
//        try{
//            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
//            JSONObject result = oneDataServiceFeign.getData(params,headers);
//            log.info("{}",result.toJSONString());
//            return result;
//        }catch (Exception e){
//            log.warn("{}",e);
//        }
//
//
//
//        return null;
//    }
//
//    @Override
//    public JSONObject getWeekMemberData(WeekParamDto weekParamDto) {
//        MultiValueMap<String, String> headers = new HttpHeaders();
//        headers.add("sk",sk);
//        headers.add("ak",ak);
//
//        ParamDto params = new ParamDto();
//        params.setCode(weekMemberDataCode);
//
//        params.setColumnArray("merchant_id as \"merchantId\", " +
//                //会员总数
//                "member_count as \"memberCounts\", " +
//                "cast(case when last_week_member_count =0 and member_count>0 then 1 when last_week_member_count=0 and member_count=0 then 0 else (member_count::numeric-last_week_member_count::numeric)::numeric/last_week_member_count::numeric end as decimal(10,2)) as \"memberCountsUp\"," +
//
//                //新会员数
//                "new_member_count as \"newMemberCounts\", " +
//                "cast(case when last_week_new_member_count =0 and new_member_count>0 then 1 when new_member_count=0 and last_week_new_member_count=0 then 0 else (new_member_count::numeric-last_week_new_member_count::numeric)::numeric/last_week_new_member_count::numeric end as decimal(10,2)) as \"newMemberCountsUp\"," +
//
//                //老会员数
//                "old_member_count as \"oldMemberCounts\", " +
//                "cast(case when last_week_old_member_count =0 and old_member_count>0 then 1 when last_week_old_member_count=0 and old_member_count=0 then 0 else (old_member_count::numeric-last_week_old_member_count::numeric)::numeric/last_week_old_member_count::numeric end as decimal(10,2)) as \"oldMemberCountsUp\"," +
//
//                //新会员订单数
//                "new_pay_count as \"newMemberOrders\", " +
//                "cast(case when last_week_new_pay_count =0 and new_pay_count>0 then 1 when last_week_new_pay_count=0 and new_pay_count=0 then 0 else (new_pay_count::numeric-last_week_new_pay_count::numeric)::numeric/last_week_new_pay_count::numeric end as decimal(10,2)) as \"newMemberOrdersUp\"," +
//
//                //老会员订单数
//                "old_pay_count as \"oldMemberOrders\", " +
//                "cast(case when last_week_old_pay_count =0 and old_pay_count>0 then 1 when last_week_old_pay_count=0 and old_pay_count=0 then 0 else (old_pay_count-last_week_old_pay_count)::numeric/last_week_old_pay_count::numeric end as decimal(10,2)) as \"oldMemberOrdersUp\"," +
//
//                //新会员启动数
//                "new_consume_count as \"newMemberStarts\", " +
//                "cast(case when last_week_new_consume_count =0 and new_consume_count>0 then 1 when last_week_new_consume_count=0 and new_consume_count=0 then 0 else (new_consume_count-last_week_new_consume_count)::numeric/last_week_new_consume_count::numeric end as decimal(10,2)) as \"newMemberStartsUp\"," +
//
//                //老会员启动数
//                "old_consume_count as \"oldMemberStarts\", " +
//                "cast(case when last_week_old_consume_count =0 and old_consume_count>0 then 1 when last_week_old_consume_count=0 and old_consume_count=0 then 0 else (old_consume_count-last_week_old_consume_count)::numeric/last_week_old_consume_count::numeric end as decimal(10,2)) as \"oldMemberStartsUp\"," +
//
//                //新会员日均订单数
//                "cast((new_pay_count::numeric/7)::numeric as decimal(10,2)) as \"perNewMemberDayOrders\", " +
//                "case when new_pay_count =0 and last_week_new_pay_count=0 then 0 " +
//                "when (last_week_new_pay_count = 0) and new_pay_count > 0 then 1 " +
//                "else cast( ((new_pay_count::numeric/7)::numeric - (last_week_new_pay_count::numeric/7)::numeric)::numeric/(last_week_new_pay_count::numeric/7)::numeric as decimal(10,2)) end  as \"perNewMemberDayOrdersUp\","+
//
//                //老会员日均订单数
//                "cast((old_pay_count::numeric/7::numeric)::numeric as decimal(10,2)) as \"perOldMemberDayOrders\", " +
//                "case when old_pay_count =0 and last_week_old_pay_count=0 then 0 " +
//                "when (last_week_old_pay_count = 0) and old_pay_count > 0 then 1 " +
//                "else cast( ((old_pay_count::numeric/7::numeric)::numeric - (last_week_old_pay_count::numeric/7::numeric)::numeric)::numeric/(last_week_old_pay_count::numeric/7::numeric)::numeric as decimal(10,2)) end  as \"perOldMemberDayOrdersUp\","+
//
//                //新会员日均启动次
//                "cast((new_consume_count::numeric/7::numeric)::numeric as decimal(10,2)) as \"perNewMemberDayStarts\", " +
//                "case when new_consume_count =0 then 0 " +
//                "when (last_week_new_consume_count = 0) and new_consume_count > 0 then 1 " +
//                "else cast( ((new_consume_count::numeric/7::numeric)::numeric - (last_week_new_consume_count::numeric/7)::numeric)::numeric/(last_week_new_consume_count::numeric/7::numeric)::numeric as decimal(10,2)) end  as \"perNewMemberDayStartsUp\","+
//
//                //老会员日均启动次
//                "cast((old_consume_count::numeric/7::numeric)::numeric as decimal(10,2)) as \"perOldMemberDayStarts\", " +
//                "case when old_consume_count =0 then 0 " +
//                "when (last_week_old_consume_count = 0) and old_consume_count > 0 then 1 " +
//                "else cast( ((old_consume_count::numeric/7::numeric) - (last_week_old_consume_count::numeric/7::numeric))::numeric/(last_week_old_consume_count::numeric/7::numeric)::numeric as decimal(10,2)) end  as \"perOldMemberDayStartsUp\","+
//
//                //新会员客单价
//                "cast(case when new_pay_count=0 then 0 else new_pay_amount::numeric/new_pay_count::numeric end as decimal(10,2)) as \"perNewMemberOrderAmount\", " +
//                "case when (new_pay_count =0 or new_pay_amount=0) and ((last_week_new_pay_amount = 0 or last_week_new_pay_count=0)) then 0 " +
//                "when (last_week_new_pay_amount = 0 or last_week_new_pay_count=0) and new_pay_amount > 0 then 1 " +
//                "else cast( ((new_pay_amount::numeric/new_pay_count::numeric) - (last_week_new_pay_amount::numeric/last_week_new_pay_count::numeric))::numeric/(last_week_new_pay_amount::numeric/last_week_new_pay_count::numeric)::numeric as decimal(10,2)) end  as \"perNewMemberOrderAmountUp\","+
//
//                //老会员客单价
//                "cast(case when old_pay_count=0 then 0 else old_pay_amount::numeric/old_pay_count::numeric end as decimal(10,2)) as \"perOldMemberOrderAmount\", " +
//                "case when (old_pay_count =0 or old_pay_amount =0 ) and (last_week_old_pay_amount = 0 or last_week_old_pay_count=0) then 0 " +
//                "when (last_week_old_pay_amount = 0 or last_week_old_pay_count=0) and old_pay_amount > 0 then 1 " +
//                "else cast( ((old_pay_amount::numeric/old_pay_count::numeric) - (last_week_old_pay_amount::numeric/last_week_old_pay_count::numeric))::numeric/(last_week_old_pay_amount::numeric/last_week_old_pay_count::numeric) as decimal(10,2)) end  as \"perOldMemberOrderAmountUp\""
//        );
//
//
//        params.getQueryParams().setYear_month(weekParamDto.getMonth());
//        params.getQueryParams().setMerchant_id(weekParamDto.getMerchantId()+"");
//        params.getQueryParams().setMonth_week_num(weekParamDto.getWeek()+"");
//        params.getQueryParams().setCombo_type(weekParamDto.getComboType());  //因为会只到商家维度
//
//        params.getQueryParams().setProvince_id(weekParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(weekParamDto.getProvinceId()+"");}});
//        params.getQueryParams().setCity_id(weekParamDto.getCityId() == null ? null:new HashSet<String>(){{add(weekParamDto.getCityId()+"");}});
//        params.getQueryParams().setDistrict_id(weekParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(weekParamDto.getAreaId()+"");}});
//        params.getQueryParams().setEquipment_group_id(weekParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(weekParamDto.getGroupId()+"");}});
//        params.getQueryParams().setLyy_equipment_type_id(weekParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(weekParamDto.getEquipmentTypeId()+"");}});
//
//        if(weekParamDto.getGroupIds() != null && weekParamDto.getGroupIds().size()>0){
//            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//            if(weekParamDto.getGroupId() == null || !weekParamDto.getGroupIds().contains(weekParamDto.getGroupId()+"")){
//                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(weekParamDto.getGroupIds())).first()+"");}});
//            }
//            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
//            params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
//        }
//
//        try{
//            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
//            JSONObject result = oneDataServiceFeign.getData(params,headers);
//            log.info("{}",result.toJSONString());
//            return result;
//        }catch (Exception e){
//            log.warn("{}",e);
//        }
//
//
//
//        return null;
//    }
//
//    @Override
//    public JSONObject getMonthMemberData(MonthParamDto monthParamDto) {
//        MultiValueMap<String, String> headers = new HttpHeaders();
//        headers.add("sk",sk);
//        headers.add("ak",ak);
//
//        ParamDto params = new ParamDto();
//        params.setCode(monthMemberDataCode);
//
//        params.setColumnArray("merchant_id as \"merchantId\", " +
//                //会员总数
//                "member_count as \"memberCounts\", " +
//                "cast(case when last_month_member_count =0 and member_count>0 then 1 when last_month_member_count=0 and member_count=0 then 0 else (member_count::numeric-last_month_member_count::numeric)::numeric/last_month_member_count::numeric end as decimal(10,2)) as \"memberCountsUp\"," +
//
//                //新会员数
//                "new_member_count as \"newMemberCounts\", " +
//                "cast(case when last_month_new_member_count =0 and new_member_count>0 then 1 when new_member_count=0 and last_month_new_member_count=0 then 0 else (new_member_count::numeric-last_month_new_member_count::numeric)::numeric/last_month_new_member_count::numeric end as decimal(10,2)) as \"newMemberCountsUp\"," +
//
//                //老会员数
//                "old_member_count as \"oldMemberCounts\", " +
//                "cast(case when last_month_old_member_count =0 and old_member_count>0 then 1 when last_month_old_member_count=0 and old_member_count=0 then 0 else (old_member_count::numeric-last_month_old_member_count::numeric)::numeric/last_month_old_member_count::numeric end as decimal(10,2)) as \"oldMemberCountsUp\"," +
//
//                //新会员订单数
//                "new_pay_count as \"newMemberOrders\", " +
//                "cast(case when last_month_new_pay_count =0 and new_pay_count>0 then 1 when last_month_new_pay_count=0 and new_pay_count=0 then 0 else (new_pay_count::numeric-last_month_new_pay_count::numeric)::numeric/last_month_new_pay_count::numeric end as decimal(10,2)) as \"newMemberOrdersUp\"," +
//
//                //老会员订单数
//                "old_pay_count as \"oldMemberOrders\", " +
//                "cast(case when last_month_old_pay_count =0 and old_pay_count>0 then 1 when last_month_old_pay_count=0 and old_pay_count=0 then 0 else (old_pay_count-last_month_old_pay_count)::numeric/last_month_old_pay_count::numeric end as decimal(10,2)) as \"oldMemberOrdersUp\"," +
//
//                //新会员启动数
//                "new_consume_count as \"newMemberStarts\", " +
//                "cast(case when last_month_new_consume_count =0 and new_consume_count>0 then 1 when last_month_new_consume_count=0 and new_consume_count=0 then 0 else (new_consume_count-last_month_new_consume_count)::numeric/last_month_new_consume_count::numeric end as decimal(10,2)) as \"newMemberStartsUp\"," +
//
//                //老会员启动数
//                "old_consume_count as \"oldMemberStarts\", " +
//                "cast(case when last_month_old_consume_count =0 and old_consume_count>0 then 1 when last_month_old_consume_count=0 and old_consume_count=0 then 0 else (old_consume_count-last_month_old_consume_count)::numeric/last_month_old_consume_count::numeric end as decimal(10,2)) as \"oldMemberStartsUp\"," +
//
//                //新会员日均订单数
//                "cast((new_pay_count::numeric/30)::numeric as decimal(10,2)) as \"perNewMemberDayOrders\", " +
//                "case when new_pay_count =0 and last_month_new_pay_count=0 then 0 " +
//                "when (last_month_new_pay_count = 0) and new_pay_count > 0 then 1 " +
//                "else cast( ((new_pay_count::numeric/30)::numeric - (last_month_new_pay_count::numeric/30)::numeric)::numeric/(last_month_new_pay_count::numeric/30)::numeric as decimal(10,2)) end  as \"perNewMemberDayOrdersUp\","+
//
//                //老会员日均订单数
//                "cast((old_pay_count::numeric/30::numeric)::numeric as decimal(10,2)) as \"perOldMemberDayOrders\", " +
//                "case when old_pay_count =0 and last_month_old_pay_count=0 then 0 " +
//                "when (last_month_old_pay_count = 0) and old_pay_count > 0 then 1 " +
//                "else cast( ((old_pay_count::numeric/30::numeric)::numeric - (last_month_old_pay_count::numeric/30::numeric)::numeric)::numeric/(last_month_old_pay_count::numeric/30::numeric)::numeric as decimal(10,2)) end  as \"perOldMemberDayOrdersUp\","+
//
//                //新会员日均启动次
//                "cast((new_consume_count::numeric/30::numeric)::numeric as decimal(10,2)) as \"perNewMemberDayStarts\", " +
//                "case when new_consume_count =0 then 0 " +
//                "when (last_month_new_consume_count = 0) and new_consume_count > 0 then 1 " +
//                "else cast( ((new_consume_count::numeric/30::numeric)::numeric - (last_month_new_consume_count::numeric/30)::numeric)::numeric/(last_month_new_consume_count::numeric/30::numeric)::numeric as decimal(10,2)) end  as \"perNewMemberDayStartsUp\","+
//
//                //老会员日均启动次
//                "cast((old_consume_count::numeric/30::numeric)::numeric as decimal(10,2)) as \"perOldMemberDayStarts\", " +
//                "case when old_consume_count =0 then 0 " +
//                "when (last_month_old_consume_count = 0) and old_consume_count > 0 then 1 " +
//                "else cast( ((old_consume_count::numeric/30::numeric) - (last_month_old_consume_count::numeric/30::numeric))::numeric/(last_month_old_consume_count::numeric/30::numeric)::numeric as decimal(10,2)) end  as \"perOldMemberDayStartsUp\","+
//
//                //新会员客单价
//                "cast(case when new_pay_count=0 then 0 else new_pay_amount::numeric/new_pay_count::numeric end as decimal(10,2)) as \"perNewMemberOrderAmount\", " +
//                "case when (new_pay_count =0 or new_pay_amount=0) and ((last_month_new_pay_amount = 0 or last_month_new_pay_count=0)) then 0 " +
//                "when (last_month_new_pay_amount = 0 or last_month_new_pay_count=0) and new_pay_amount > 0 then 1 " +
//                "else cast( ((new_pay_amount::numeric/new_pay_count::numeric) - (last_month_new_pay_amount::numeric/last_month_new_pay_count::numeric))::numeric/(last_month_new_pay_amount::numeric/last_month_new_pay_count::numeric)::numeric as decimal(10,2)) end  as \"perNewMemberOrderAmountUp\","+
//
//                //老会员客单价
//                "cast(case when old_pay_count=0 then 0 else old_pay_amount::numeric/old_pay_count::numeric end as decimal(10,2)) as \"perOldMemberOrderAmount\", " +
//                "case when (old_pay_count =0 or old_pay_amount =0 ) and (last_month_old_pay_amount = 0 or last_month_old_pay_count=0) then 0 " +
//                "when (last_month_old_pay_amount = 0 or last_month_old_pay_count=0) and old_pay_amount > 0 then 1 " +
//                "else cast( ((old_pay_amount::numeric/old_pay_count::numeric) - (last_month_old_pay_amount::numeric/last_month_old_pay_count::numeric))::numeric/(last_month_old_pay_amount::numeric/last_month_old_pay_count::numeric) as decimal(10,2)) end  as \"perOldMemberOrderAmountUp\""
//        );
//
//
//        params.getQueryParams().setYear_month(monthParamDto.getMonth());
//        params.getQueryParams().setMerchant_id(monthParamDto.getMerchantId()+"");
//        params.getQueryParams().setCombo_type(monthParamDto.getComboType());  //因为会只到商家维度
//
//        params.getQueryParams().setProvince_id(monthParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(monthParamDto.getProvinceId()+"");}});
//        params.getQueryParams().setCity_id(monthParamDto.getCityId() == null ? null:new HashSet<String>(){{add(monthParamDto.getCityId()+"");}});
//        params.getQueryParams().setDistrict_id(monthParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(monthParamDto.getAreaId()+"");}});
//        params.getQueryParams().setEquipment_group_id(monthParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(monthParamDto.getGroupId()+"");}});
//        params.getQueryParams().setLyy_equipment_type_id(monthParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(monthParamDto.getEquipmentTypeId()+"");}});
//
//        if(monthParamDto.getGroupIds() != null && monthParamDto.getGroupIds().size()>0){
//            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//            if(monthParamDto.getGroupId() == null || !monthParamDto.getGroupIds().contains(monthParamDto.getGroupId()+"")){
//                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(monthParamDto.getGroupIds())).first()+"");}});
//            }
//            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
//            params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
//        }
//
//        try{
//            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
//            JSONObject result = oneDataServiceFeign.getData(params,headers);
//            log.info("{}",result.toJSONString());
//            return result;
//        }catch (Exception e){
//            log.warn("{}",e);
//        }
//
//
//
//        return null;
//    }
//
//    @Override
//    public JSONObject getYearMemberData(YearParamDto yearParamDto) {
//        MultiValueMap<String, String> headers = new HttpHeaders();
//        headers.add("sk",sk);
//        headers.add("ak",ak);
//
//        ParamDto params = new ParamDto();
//        params.setCode(yearMemberDataCode);
//
//        params.setColumnArray("merchant_id as \"merchantId\", " +
//                //会员总数
//                "member_count as \"memberCounts\", " +
//                "cast(case when last_year_member_count =0 and member_count>0 then 1 when last_year_member_count=0 and member_count=0 then 0 else (member_count::numeric-last_year_member_count::numeric)::numeric/last_year_member_count::numeric end as decimal(10,2)) as \"memberCountsUp\"," +
//
//                //新会员数
//                "new_member_count as \"newMemberCounts\", " +
//                "cast(case when last_year_new_member_count =0 and new_member_count>0 then 1 when new_member_count=0 and last_year_new_member_count=0 then 0 else (new_member_count::numeric-last_year_new_member_count::numeric)::numeric/last_year_new_member_count::numeric end as decimal(10,2)) as \"newMemberCountsUp\"," +
//
//                //老会员数
//                "old_member_count as \"oldMemberCounts\", " +
//                "cast(case when last_year_old_member_count =0 and old_member_count>0 then 1 when last_year_old_member_count=0 and old_member_count=0 then 0 else (old_member_count::numeric-last_year_old_member_count::numeric)::numeric/last_year_old_member_count::numeric end as decimal(10,2)) as \"oldMemberCountsUp\"," +
//
//                //新会员订单数
//                "new_pay_count as \"newMemberOrders\", " +
//                "cast(case when last_year_new_pay_count =0 and new_pay_count>0 then 1 when last_year_new_pay_count=0 and new_pay_count=0 then 0 else (new_pay_count::numeric-last_year_new_pay_count::numeric)::numeric/last_year_new_pay_count::numeric end as decimal(10,2)) as \"newMemberOrdersUp\"," +
//
//                //老会员订单数
//                "old_pay_count as \"oldMemberOrders\", " +
//                "cast(case when last_year_old_pay_count =0 and old_pay_count>0 then 1 when last_year_old_pay_count=0 and old_pay_count=0 then 0 else (old_pay_count-last_year_old_pay_count)::numeric/last_year_old_pay_count::numeric end as decimal(10,2)) as \"oldMemberOrdersUp\"," +
//
//                //新会员启动数
//                "new_consume_count as \"newMemberStarts\", " +
//                "cast(case when last_year_new_consume_count =0 and new_consume_count>0 then 1 when last_year_new_consume_count=0 and new_consume_count=0 then 0 else (new_consume_count-last_year_new_consume_count)::numeric/last_year_new_consume_count::numeric end as decimal(10,2)) as \"newMemberStartsUp\"," +
//
//                //老会员启动数
//                "old_consume_count as \"oldMemberStarts\", " +
//                "cast(case when last_year_old_consume_count =0 and old_consume_count>0 then 1 when last_year_old_consume_count=0 and old_consume_count=0 then 0 else (old_consume_count-last_year_old_consume_count)::numeric/last_year_old_consume_count::numeric end as decimal(10,2)) as \"oldMemberStartsUp\"," +
//
//                //新会员日均订单数
//                "cast((new_pay_count::numeric/365)::numeric as decimal(10,2)) as \"perNewMemberDayOrders\", " +
//                "case when new_pay_count =0 and last_year_new_pay_count=0 then 0 " +
//                "when (last_year_new_pay_count > 0) and new_pay_count = 0 then -1 " +
//                "when (last_year_new_pay_count = 0) and new_pay_count > 0 then 1 " +
//                "else cast( ((new_pay_count::numeric/365)::numeric - (last_year_new_pay_count::numeric/365)::numeric)::numeric/(last_year_new_pay_count::numeric/365)::numeric as decimal(10,2)) end  as \"perNewMemberDayOrdersUp\","+
//
//                //老会员日均订单数
//                "cast((old_pay_count::numeric/365::numeric)::numeric as decimal(10,2)) as \"perOldMemberDayOrders\", " +
//                "case when old_pay_count =0 and last_year_old_pay_count=0 then 0 " +
//                "when (last_year_old_pay_count > 0) and old_pay_count = 0 then -1 " +
//                "when (last_year_old_pay_count = 0) and old_pay_count > 0 then 1 " +
//                "else cast( ((old_pay_count::numeric/365::numeric)::numeric - (last_year_old_pay_count::numeric/365::numeric)::numeric)::numeric/(last_year_old_pay_count::numeric/365::numeric)::numeric as decimal(10,2)) end  as \"perOldMemberDayOrdersUp\","+
//
//                //新会员日均启动次
//                "cast((new_consume_count::numeric/365::numeric)::numeric as decimal(10,2)) as \"perNewMemberDayStarts\", " +
//                "case when new_consume_count =0 then 0 " +
//                "when (last_year_new_consume_count > 0) and new_consume_count = 0 then -1 " +
//                "when (last_year_new_consume_count = 0) and new_consume_count > 0 then 1 " +
//                "else cast( ((new_consume_count::numeric/365::numeric)::numeric - (last_year_new_consume_count::numeric/365)::numeric)::numeric/(last_year_new_consume_count::numeric/365::numeric)::numeric as decimal(10,2)) end  as \"perNewMemberDayStartsUp\","+
//
//                //老会员日均启动次
//                "cast((old_consume_count::numeric/365::numeric)::numeric as decimal(10,2)) as \"perOldMemberDayStarts\", " +
//                "case when old_consume_count =0 then 0 " +
//                "when old_consume_count = 0 and last_year_old_consume_count > 0 then -1 " +
//                "when (last_year_old_consume_count = 0) and old_consume_count > 0 then 1 " +
//                "else cast( ((old_consume_count::numeric/365::numeric) - (last_year_old_consume_count::numeric/365::numeric))::numeric/(last_year_old_consume_count::numeric/365::numeric)::numeric as decimal(10,2)) end  as \"perOldMemberDayStartsUp\","+
//
//                //新会员客单价
//                "cast(case when new_pay_count=0 then 0 else new_pay_amount::numeric/new_pay_count::numeric end as decimal(10,2)) as \"perNewMemberOrderAmount\", " +
//                "case when (new_pay_count =0 or new_pay_amount=0) and ((last_year_new_pay_amount = 0 or last_year_new_pay_count=0)) then 0 " +
//                "when (new_pay_count =0 or new_pay_amount=0) and last_year_new_pay_amount > 0 then -1 " +
//                "when (last_year_new_pay_amount = 0 or last_year_new_pay_count=0) and new_pay_amount > 0 then 1 " +
//                "else cast( ((new_pay_amount::numeric/new_pay_count::numeric) - (last_year_new_pay_amount::numeric/last_year_new_pay_count::numeric))::numeric/(last_year_new_pay_amount::numeric/last_year_new_pay_count::numeric)::numeric as decimal(10,2)) end  as \"perNewMemberOrderAmountUp\","+
//
//                //老会员客单价
//                "cast(case when old_pay_count=0 then 0 else old_pay_amount::numeric/old_pay_count::numeric end as decimal(10,2)) as \"perOldMemberOrderAmount\", " +
//                "case when (old_pay_count =0 or old_pay_amount =0 ) and (last_year_old_pay_amount = 0 or last_year_old_pay_count=0) then 0 " +
//                "when (old_pay_count =0 or old_pay_amount =0 ) and last_year_old_pay_amount > 0 then -1  " +
//                "when (last_year_old_pay_amount = 0 or last_year_old_pay_count=0) and old_pay_amount > 0 then 1 " +
//                "else cast( ((old_pay_amount::numeric/old_pay_count::numeric) - (last_year_old_pay_amount::numeric/last_year_old_pay_count::numeric))::numeric/(last_year_old_pay_amount::numeric/last_year_old_pay_count::numeric) as decimal(10,2)) end  as \"perOldMemberOrderAmountUp\""
//        );
//
//
//        params.getQueryParams().setYear_str(yearParamDto.getYear()+"");
//        params.getQueryParams().setMerchant_id(yearParamDto.getMerchantId()+"");
//        params.getQueryParams().setCombo_type(yearParamDto.getComboType());  //因为会只到商家维度
//
//        params.getQueryParams().setProvince_id(yearParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(yearParamDto.getProvinceId()+"");}});
//        params.getQueryParams().setCity_id(yearParamDto.getCityId() == null ? null:new HashSet<String>(){{add(yearParamDto.getCityId()+"");}});
//        params.getQueryParams().setDistrict_id(yearParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(yearParamDto.getAreaId()+"");}});
//        params.getQueryParams().setEquipment_group_id(yearParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(yearParamDto.getGroupId()+"");}});
//        params.getQueryParams().setLyy_equipment_type_id(yearParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(yearParamDto.getEquipmentTypeId()+"");}});
//
//        if(yearParamDto.getGroupIds() != null && yearParamDto.getGroupIds().size()>0){
//            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//            if(yearParamDto.getGroupId() == null || !yearParamDto.getGroupIds().contains(yearParamDto.getGroupId()+"")){
//                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(yearParamDto.getGroupIds())).first()+"");}});
//            }
//            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
//            params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
//        }
//
//        try{
//            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
//            JSONObject result = oneDataServiceFeign.getData(params,headers);
//            log.info("{}",result.toJSONString());
//            return result;
//        }catch (Exception e){
//            log.warn("{}",e);
//        }
//
//
//
//        return null;
//    }
//
//    @Override
//    public JSONObject getPerOrderPriceCount(DayParamDto dayParamDto) {
//        MultiValueMap<String, String> headers = new HttpHeaders();
//        headers.add("sk",sk);
//        headers.add("ak",ak);
//
//        ParamDto params = new ParamDto();
//        params.setCode(dayOrderDayAnalyseCode);
//
//        params.setColumnArray("merchant_id as \"merchantId\", " +
//                //会员总数
//                "pay_0_1_count , " +
//                "pay_1_3_count , " +
//                "pay_3_5_count , " +
//                "pay_5_10_count , " +
//                "pay_10_15_count , " +
//                "pay_15_30_count , " +
//                "pay_30_50_count , " +
//                "pay_50_100_count , " +
//                "pay_100_200_count , " +
//                "pay_200_500_count , " +
//                "pay_500_1000_count , " +
//                "over_1000_count ,  " +
//                "pay_0_1_amount , " +
//                "pay_1_3_amount , " +
//                "pay_3_5_amount , " +
//                "pay_5_10_amount , " +
//                "pay_10_15_amount , " +
//                "pay_15_30_amount , " +
//                "pay_30_50_amount , " +
//                "pay_50_100_amount , " +
//                "pay_100_200_amount , " +
//                "pay_200_500_amount , " +
//                "pay_500_1000_amount , " +
//                "over_1000_amount ,  " +
//                "cast(case when pay_0_1_count=0 then 0 else pay_0_1_amount::numeric/pay_0_1_count::numeric end  as decimal(10,2)) as \"payOrderPrice0_1\" , " +
//                "cast(case when pay_1_3_count=0 then 0 else pay_1_3_amount::numeric/pay_1_3_count::numeric end  as decimal(10,2)) as \"payOrderPrice1_3\" , " +
//                "cast(case when pay_3_5_count=0 then 0 else pay_3_5_amount::numeric/pay_3_5_count::numeric end  as decimal(10,2)) as \"payOrderPrice3_5\" , " +
//                "cast(case when pay_5_10_count=0 then 0 else pay_5_10_amount::numeric/pay_5_10_count::numeric end  as decimal(10,2)) as \"payOrderPrice5_10\" , " +
//                "cast(case when pay_10_15_count=0 then 0 else pay_10_15_amount::numeric/pay_10_15_count::numeric end  as decimal(10,2)) as \"payOrderPrice10_15\" , " +
//                "cast(case when pay_15_30_count=0 then 0 else pay_15_30_amount::numeric/pay_15_30_count::numeric end  as decimal(10,2)) as \"payOrderPrice15_30\" , " +
//                "cast(case when pay_30_50_count=0 then 0 else pay_30_50_amount::numeric/pay_30_50_count::numeric end  as decimal(10,2)) as \"payOrderPrice30_50\" , " +
//                "cast(case when pay_50_100_count=0 then 0 else pay_50_100_amount::numeric/pay_50_100_count::numeric end  as decimal(10,2)) as \"payOrderPrice50_100\" , " +
//                "cast(case when pay_100_200_count=0 then 0 else pay_100_200_amount::numeric/pay_100_200_count::numeric end  as decimal(10,2)) as \"payOrderPrice100_200\" , " +
//                "cast(case when pay_200_500_count=0 then 0 else pay_200_500_amount::numeric/pay_200_500_count::numeric end  as decimal(10,2)) as \"payOrderPrice200_500\" , " +
//                "cast(case when pay_500_1000_count=0 then 0 else pay_500_1000_amount::numeric/pay_500_1000_count::numeric end  as decimal(10,2)) as \"payOrderPrice500_1000\" , " +
//                "cast(case when over_1000_count=0 then 0 else over_1000_amount::numeric/over_1000_count::numeric end  as decimal(10,2)) as \"payOrderPriceOver1000\"  "
//        );
//
//
//        params.getQueryParams().setDay(dayParamDto.getDay());
//        params.getQueryParams().setMerchant_id(dayParamDto.getMerchantId()+"");
//        params.getQueryParams().setCombo_type(dayParamDto.getComboType());
//
//        params.getQueryParams().setProvince_id(dayParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(dayParamDto.getProvinceId()+"");}});
//        params.getQueryParams().setCity_id(dayParamDto.getCityId() == null ? null:new HashSet<String>(){{add(dayParamDto.getCityId()+"");}});
//        params.getQueryParams().setDistrict_id(dayParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(dayParamDto.getAreaId()+"");}});
//        params.getQueryParams().setEquipment_group_id(dayParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(dayParamDto.getGroupId()+"");}});
//        params.getQueryParams().setLyy_equipment_type_id(dayParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(dayParamDto.getEquipmentTypeId()+"");}});
//
//        if(dayParamDto.getGroupIds() != null && dayParamDto.getGroupIds().size()>0){
//            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//            if(dayParamDto.getGroupId() == null || !dayParamDto.getGroupIds().contains(dayParamDto.getGroupId()+"")){
//                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(dayParamDto.getGroupIds())).first()+"");}});
//            }
//            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
//            params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
//        }
//
//        try{
//            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
//            JSONObject result = oneDataServiceFeign.getData(params,headers);
//            log.info("{}",result.toJSONString());
//
//            //动态返回商家6个区间
//            if(result != null && "200".equals(result.getString("code")) && result.getJSONObject("info") != null){
//                if(result.getJSONObject("info").getJSONArray("list") != null && result.getJSONObject("info").getJSONArray("list").size()>0){
//                    JSONObject obj = result.getJSONObject("info").getJSONArray("list").getJSONObject(0);
//                    JSONObject newObj = new JSONObject();
//                    JSONArray data = new JSONArray();
//                    Map<Integer,String> map = new HashMap<>();
//                    Map<Integer,String> payMap = new HashMap<>();
//                    Map<Integer,String> svgMap = new HashMap<>();
//                    Map<Integer,String> newMap = new HashMap<>();
//                    map.put(1,"pay_0_1_count");
//                    map.put(2,"pay_1_3_count");
//                    map.put(3,"pay_3_5_count");
//                    map.put(4,"pay_5_10_count");
//                    map.put(5,"pay_10_15_count");
//                    map.put(6,"pay_15_30_count");
//                    map.put(7,"pay_30_50_count");
//                    map.put(8,"pay_50_100_count");
//                    map.put(9,"pay_100_200_count");
//                    map.put(10,"pay_200_500_count");
//                    map.put(11,"pay_500_1000_count");
//                    map.put(12,"over_1000_count");
//
//                    payMap.put(1,"pay_0_1_amount");
//                    payMap.put(2,"pay_1_3_amount");
//                    payMap.put(3,"pay_3_5_amount");
//                    payMap.put(4,"pay_5_10_amount");
//                    payMap.put(5,"pay_10_15_amount");
//                    payMap.put(6,"pay_15_30_amount");
//                    payMap.put(7,"pay_30_50_amount");
//                    payMap.put(8,"pay_50_100_amount");
//                    payMap.put(9,"pay_100_200_amount");
//                    payMap.put(10,"pay_200_500_amount");
//                    payMap.put(11,"pay_500_1000_amount");
//                    payMap.put(12,"over_1000_amount");
//
//                    svgMap.put(1,"payOrderPrice0_1");
//                    svgMap.put(2,"payOrderPrice1_3");
//                    svgMap.put(3,"payOrderPrice3_5");
//                    svgMap.put(4,"payOrderPrice5_10");
//                    svgMap.put(5,"payOrderPrice10_15");
//                    svgMap.put(6,"payOrderPrice15_30");
//                    svgMap.put(7,"payOrderPrice30_50");
//                    svgMap.put(8,"payOrderPrice50_100");
//                    svgMap.put(9,"payOrderPrice100_200");
//                    svgMap.put(10,"payOrderPrice200_500");
//                    svgMap.put(11,"payOrderPrice500_1000");
//                    svgMap.put(12,"payOrderPriceOver1000");
//
//
//
//                    newMap.put(1,"0-1");
//                    newMap.put(2,"1-3");
//                    newMap.put(3,"3-5");
//                    newMap.put(4,"5-10");
//                    newMap.put(5,"10-15");
//                    newMap.put(6,"15-30");
//                    newMap.put(7,"30-50");
//                    newMap.put(8,"50-100");
//                    newMap.put(9,"100-200");
//                    newMap.put(10,"200-500");
//                    newMap.put(11,"500-1000");
//                    newMap.put(12,"1000以上");
//                    int start = 0;
//                    int count = 0;
//                    int end = 0;
//                    if(obj.getInteger("pay_0_1_count")>0){
//                        start = 1;
//                        end = 1;
//                        count++;
//                    }
//                    if(obj.getInteger("pay_1_3_count")>0){
//                        if(start <= 0){
//                            start = 2;
//                        }
//                        end = 2;
//                        count++;
//                    }
//                    if(obj.getInteger("pay_3_5_count")>0){
//                        if(start <= 0){
//                            start = 3;
//                        }
//                        end = 3;
//                        count++;
//                    }
//                    if(obj.getInteger("pay_5_10_count")>0){
//                        if(start <= 0){
//                            start = 4;
//                        }
//                        end = 4;
//                        count++;
//                    }
//                    if(obj.getInteger("pay_10_15_count")>0){
//                        if(start <= 0){
//                            start = 5;
//                        }
//                        end = 5;
//                        count++;
//                    }
//                    if(obj.getInteger("pay_15_30_count")>0){
//                        if(start <= 0){
//                            start = 6;
//                        }
//                        end = 6;
//                        count++;
//                    }
//                    if(obj.getInteger("pay_30_50_count")>0){
//                        if(start <= 0){
//                            start = 7;
//                        }
//                        end = 7;
//                        count++;
//                    }
//                    if(obj.getInteger("pay_50_100_count")>0){
//                        if(start <= 0){
//                            start = 8;
//                        }
//                        end = 8;
//                        count++;
//                    }
//                    if(obj.getInteger("pay_100_200_count")>0){
//                        if(start <= 0){
//                            start = 9;
//                        }
//                        end = 9;
//                        count++;
//                    }
//                    if(obj.getInteger("pay_200_500_count")>0){
//                        if(start <= 0){
//                            start = 10;
//                        }
//                        end = 10;
//                        count++;
//                    }
//                    if(obj.getInteger("pay_500_1000_count")>0){
//                        if(start <= 0){
//                            start = 11;
//                        }
//                        end = 11;
//                        count++;
//                    }
//                    if(obj.getInteger("over_1000_count")>0){
//                        if(start <= 0){
//                            start = 12;
//                        }
//                        end = 12;
//                        count++;
//                    }
//                    //刚刚好6个档次区间
//                    if(end - start ==5){
//                        for(int i=start; i<=end; i++){
//                            newObj.put("show",newMap.get(i));
//                            newObj.put("orderCount",obj.getIntValue(map.get(i)));
//                            newObj.put("payAmount",obj.getDoubleValue(payMap.get(i)));
//                            newObj.put("svgPrice",obj.getDoubleValue(svgMap.get(i)));
//                            data.add(newObj);
//                            newObj = new JSONObject();
//                        }
//                    }
//
//                    //如果少于6个，且网上能够补齐6个
//                    if(end - start < 5){
//                        //网上区间够补齐6个
//                        if(start<=7){
//                            for(int i=start; i<=start+5; i++){
//                                newObj.put("show",newMap.get(i));
//                                newObj.put("orderCount",obj.getIntValue(map.get(i)));
//                                newObj.put("payAmount",obj.getDoubleValue(payMap.get(i)));
//                                newObj.put("svgPrice",obj.getDoubleValue(svgMap.get(i)));
//                                data.add(newObj);
//                                newObj = new JSONObject();
//                            }
//                        }
//                        //往上不够补，往下补
//                        if(start>7){
//                            for(int i=7; i<=12; i++){
//                                newObj.put("show",newMap.get(i));
//                                newObj.put("orderCount",obj.getIntValue(map.get(i)));
//                                newObj.put("payAmount",obj.getDoubleValue(payMap.get(i)));
//                                newObj.put("svgPrice",obj.getDoubleValue(svgMap.get(i)));
//                                data.add(newObj);
//                                newObj = new JSONObject();
//                            }
//                        }
//
//                    }
//
//                    //大于6个档次
//                    if(end - start > 5){
//                        for(int i=start; i<start+5; i++){
//                            newObj.put("show",newMap.get(i));
//                            newObj.put("orderCount",obj.getIntValue(map.get(i)));
//                            newObj.put("payAmount",obj.getDoubleValue(payMap.get(i)));
//                            newObj.put("svgPrice",obj.getDoubleValue(svgMap.get(i)));
//                            data.add(newObj);
//                            newObj = new JSONObject();
//                        }
//                        int lastCounts = 0;
//                        double lastPays = 0;
//                        for(int i=start+5; i<=end; i++){
//                            lastCounts += obj.getIntValue(map.get(i));
//                            lastPays += obj.getDoubleValue(payMap.get(i));
//                        }
//                        DecimalFormat decimalFormat = new DecimalFormat("#.00");
//                        newObj.put("show",newMap.get(start+5).split("-")[0]+"以上");
//                        newObj.put("orderCount",lastCounts);
//                        newObj.put("payAmount",lastPays);
//                        newObj.put("svgPrice",decimalFormat.format(lastPays/lastCounts));
//                        data.add(newObj);
//                    }
//                    result.getJSONObject("info").put("totalCount",6);
//                    result.getJSONObject("info").remove("list");
//                    result.getJSONObject("info").put("list",data);
//
//                }
//            }
//            return result;
//        }catch (Exception e){
//            log.warn("{}",e);
//        }
//
//
//
//        return null;
//    }
//
//    @Override
//    public JSONObject getWeekPerOrderPriceCount(WeekParamDto weekParamDto) {
//        MultiValueMap<String, String> headers = new HttpHeaders();
//        headers.add("sk",sk);
//        headers.add("ak",ak);
//
//        ParamDto params = new ParamDto();
//        params.setCode(weekOrderDayAnalyseCode);
//
//        params.setColumnArray("merchant_id as \"merchantId\", " +
//                //会员总数
//                "pay_0_1_count , " +
//                "pay_1_3_count , " +
//                "pay_3_5_count , " +
//                "pay_5_10_count , " +
//                "pay_10_15_count , " +
//                "pay_15_30_count , " +
//                "pay_30_50_count , " +
//                "pay_50_100_count , " +
//                "pay_100_200_count , " +
//                "pay_200_500_count , " +
//                "pay_500_1000_count , " +
//                "over_1000_count ,  " +
//                "pay_0_1_amount , " +
//                "pay_1_3_amount , " +
//                "pay_3_5_amount , " +
//                "pay_5_10_amount , " +
//                "pay_10_15_amount , " +
//                "pay_15_30_amount , " +
//                "pay_30_50_amount , " +
//                "pay_50_100_amount , " +
//                "pay_100_200_amount , " +
//                "pay_200_500_amount , " +
//                "pay_500_1000_amount , " +
//                "over_1000_amount ,  " +
//                "cast(case when pay_0_1_count=0 then 0 else pay_0_1_amount::numeric/pay_0_1_count::numeric end  as decimal(10,2)) as \"payOrderPrice0_1\" , " +
//                "cast(case when pay_1_3_count=0 then 0 else pay_1_3_amount::numeric/pay_1_3_count::numeric end  as decimal(10,2)) as \"payOrderPrice1_3\" , " +
//                "cast(case when pay_3_5_count=0 then 0 else pay_3_5_amount::numeric/pay_3_5_count::numeric end  as decimal(10,2)) as \"payOrderPrice3_5\" , " +
//                "cast(case when pay_5_10_count=0 then 0 else pay_5_10_amount::numeric/pay_5_10_count::numeric end  as decimal(10,2)) as \"payOrderPrice5_10\" , " +
//                "cast(case when pay_10_15_count=0 then 0 else pay_10_15_amount::numeric/pay_10_15_count::numeric end  as decimal(10,2)) as \"payOrderPrice10_15\" , " +
//                "cast(case when pay_15_30_count=0 then 0 else pay_15_30_amount::numeric/pay_15_30_count::numeric end  as decimal(10,2)) as \"payOrderPrice15_30\" , " +
//                "cast(case when pay_30_50_count=0 then 0 else pay_30_50_amount::numeric/pay_30_50_count::numeric end  as decimal(10,2)) as \"payOrderPrice30_50\" , " +
//                "cast(case when pay_50_100_count=0 then 0 else pay_50_100_amount::numeric/pay_50_100_count::numeric end  as decimal(10,2)) as \"payOrderPrice50_100\" , " +
//                "cast(case when pay_100_200_count=0 then 0 else pay_100_200_amount::numeric/pay_100_200_count::numeric end  as decimal(10,2)) as \"payOrderPrice100_200\" , " +
//                "cast(case when pay_200_500_count=0 then 0 else pay_200_500_amount::numeric/pay_200_500_count::numeric end  as decimal(10,2)) as \"payOrderPrice200_500\" , " +
//                "cast(case when pay_500_1000_count=0 then 0 else pay_500_1000_amount::numeric/pay_500_1000_count::numeric end  as decimal(10,2)) as \"payOrderPrice500_1000\" , " +
//                "cast(case when over_1000_count=0 then 0 else over_1000_amount::numeric/over_1000_count::numeric end  as decimal(10,2)) as \"payOrderPriceOver1000\"  "
//        );
//
//
//        params.getQueryParams().setYear_month(weekParamDto.getMonth());
//        params.getQueryParams().setMonth_week_num(weekParamDto.getWeek()+"");
//        params.getQueryParams().setMerchant_id(weekParamDto.getMerchantId()+"");
//        params.getQueryParams().setCombo_type(weekParamDto.getComboType());
//
//        params.getQueryParams().setProvince_id(weekParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(weekParamDto.getProvinceId()+"");}});
//        params.getQueryParams().setCity_id(weekParamDto.getCityId() == null ? null:new HashSet<String>(){{add(weekParamDto.getCityId()+"");}});
//        params.getQueryParams().setDistrict_id(weekParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(weekParamDto.getAreaId()+"");}});
//        params.getQueryParams().setEquipment_group_id(weekParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(weekParamDto.getGroupId()+"");}});
//        params.getQueryParams().setLyy_equipment_type_id(weekParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(weekParamDto.getEquipmentTypeId()+"");}});
//
//        if(weekParamDto.getGroupIds() != null && weekParamDto.getGroupIds().size()>0){
//            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//            if(weekParamDto.getGroupId() == null || !weekParamDto.getGroupIds().contains(weekParamDto.getGroupId()+"")){
//                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(weekParamDto.getGroupIds())).first()+"");}});
//            }
//            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
//            params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
//        }
//
//        try{
//            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
//            JSONObject result = oneDataServiceFeign.getData(params,headers);
//            log.info("{}",result.toJSONString());
//
//            //动态返回商家6个区间
//            if(result != null && "200".equals(result.getString("code")) && result.getJSONObject("info") != null){
//                if(result.getJSONObject("info").getJSONArray("list") != null && result.getJSONObject("info").getJSONArray("list").size()>0){
//                    JSONObject obj = result.getJSONObject("info").getJSONArray("list").getJSONObject(0);
//                    JSONObject newObj = new JSONObject();
//                    JSONArray data = new JSONArray();
//                    Map<Integer,String> map = new HashMap<>();
//                    Map<Integer,String> payMap = new HashMap<>();
//                    Map<Integer,String> svgMap = new HashMap<>();
//                    Map<Integer,String> newMap = new HashMap<>();
//                    map.put(1,"pay_0_1_count");
//                    map.put(2,"pay_1_3_count");
//                    map.put(3,"pay_3_5_count");
//                    map.put(4,"pay_5_10_count");
//                    map.put(5,"pay_10_15_count");
//                    map.put(6,"pay_15_30_count");
//                    map.put(7,"pay_30_50_count");
//                    map.put(8,"pay_50_100_count");
//                    map.put(9,"pay_100_200_count");
//                    map.put(10,"pay_200_500_count");
//                    map.put(11,"pay_500_1000_count");
//                    map.put(12,"over_1000_count");
//
//                    payMap.put(1,"pay_0_1_amount");
//                    payMap.put(2,"pay_1_3_amount");
//                    payMap.put(3,"pay_3_5_amount");
//                    payMap.put(4,"pay_5_10_amount");
//                    payMap.put(5,"pay_10_15_amount");
//                    payMap.put(6,"pay_15_30_amount");
//                    payMap.put(7,"pay_30_50_amount");
//                    payMap.put(8,"pay_50_100_amount");
//                    payMap.put(9,"pay_100_200_amount");
//                    payMap.put(10,"pay_200_500_amount");
//                    payMap.put(11,"pay_500_1000_amount");
//                    payMap.put(12,"over_1000_amount");
//
//                    svgMap.put(1,"payOrderPrice0_1");
//                    svgMap.put(2,"payOrderPrice1_3");
//                    svgMap.put(3,"payOrderPrice3_5");
//                    svgMap.put(4,"payOrderPrice5_10");
//                    svgMap.put(5,"payOrderPrice10_15");
//                    svgMap.put(6,"payOrderPrice15_30");
//                    svgMap.put(7,"payOrderPrice30_50");
//                    svgMap.put(8,"payOrderPrice50_100");
//                    svgMap.put(9,"payOrderPrice100_200");
//                    svgMap.put(10,"payOrderPrice200_500");
//                    svgMap.put(11,"payOrderPrice500_1000");
//                    svgMap.put(12,"payOrderPriceOver1000");
//
//                    newMap.put(1,"0-1");
//                    newMap.put(2,"1-3");
//                    newMap.put(3,"3-5");
//                    newMap.put(4,"5-10");
//                    newMap.put(5,"10-15");
//                    newMap.put(6,"15-30");
//                    newMap.put(7,"30-50");
//                    newMap.put(8,"50-100");
//                    newMap.put(9,"100-200");
//                    newMap.put(10,"200-500");
//                    newMap.put(11,"500-1000");
//                    newMap.put(12,"1000以上");
//                    int start = 0;
//                    int count = 0;
//                    int end = 0;
//                    if(obj.getInteger("pay_0_1_count")>0){
//                        start = 1;
//                        end = 1;
//                        count++;
//                    }
//                    if(obj.getInteger("pay_1_3_count")>0){
//                        if(start <= 0){
//                            start = 2;
//                        }
//                        end = 2;
//                        count++;
//                    }
//                    if(obj.getInteger("pay_3_5_count")>0){
//                        if(start <= 0){
//                            start = 3;
//                        }
//                        end = 3;
//                        count++;
//                    }
//                    if(obj.getInteger("pay_5_10_count")>0){
//                        if(start <= 0){
//                            start = 4;
//                        }
//                        end = 4;
//                        count++;
//                    }
//                    if(obj.getInteger("pay_10_15_count")>0){
//                        if(start <= 0){
//                            start = 5;
//                        }
//                        end = 5;
//                        count++;
//                    }
//                    if(obj.getInteger("pay_15_30_count")>0){
//                        if(start <= 0){
//                            start = 6;
//                        }
//                        end = 6;
//                        count++;
//                    }
//                    if(obj.getInteger("pay_30_50_count")>0){
//                        if(start <= 0){
//                            start = 7;
//                        }
//                        end = 7;
//                        count++;
//                    }
//                    if(obj.getInteger("pay_50_100_count")>0){
//                        if(start <= 0){
//                            start = 8;
//                        }
//                        end = 8;
//                        count++;
//                    }
//                    if(obj.getInteger("pay_100_200_count")>0){
//                        if(start <= 0){
//                            start = 9;
//                        }
//                        end = 9;
//                        count++;
//                    }
//                    if(obj.getInteger("pay_200_500_count")>0){
//                        if(start <= 0){
//                            start = 10;
//                        }
//                        end = 10;
//                        count++;
//                    }
//                    if(obj.getInteger("pay_500_1000_count")>0){
//                        if(start <= 0){
//                            start = 11;
//                        }
//                        end = 11;
//                        count++;
//                    }
//                    if(obj.getInteger("over_1000_count")>0){
//                        if(start <= 0){
//                            start = 12;
//                        }
//                        end = 12;
//                        count++;
//                    }
//                    //刚刚好6个档次区间
//                    if(end - start ==5){
//                        for(int i=start; i<=end; i++){
//                            newObj.put("show",newMap.get(i));
//                            newObj.put("orderCount",obj.getIntValue(map.get(i)));
//                            newObj.put("payAmount",obj.getDoubleValue(payMap.get(i)));
//                            newObj.put("svgPrice",obj.getDoubleValue(svgMap.get(i)));
//                            data.add(newObj);
//                            newObj = new JSONObject();
//                        }
//                    }
//
//                    //如果少于6个，且网上能够补齐6个
//                    if(end - start < 5){
//                        //网上区间够补齐6个
//                        if(start<=7){
//                            for(int i=start; i<=start+5; i++){
//                                newObj.put("show",newMap.get(i));
//                                newObj.put("orderCount",obj.getIntValue(map.get(i)));
//                                newObj.put("payAmount",obj.getDoubleValue(payMap.get(i)));
//                                newObj.put("svgPrice",obj.getDoubleValue(svgMap.get(i)));
//                                data.add(newObj);
//                                newObj = new JSONObject();
//                            }
//                        }
//                        //往上不够补，往下补
//                        if(start>7){
//                            for(int i=7; i<=12; i++){
//                                newObj.put("show",newMap.get(i));
//                                newObj.put("orderCount",obj.getIntValue(map.get(i)));
//                                newObj.put("payAmount",obj.getDoubleValue(payMap.get(i)));
//                                newObj.put("svgPrice",obj.getDoubleValue(svgMap.get(i)));
//                                data.add(newObj);
//                                newObj = new JSONObject();
//                            }
//                        }
//
//                    }
//
//                    //大于6个档次
//                    if(end - start > 5){
//                        for(int i=start; i<start+5; i++){
//                            newObj.put("show",newMap.get(i));
//                            newObj.put("orderCount",obj.getIntValue(map.get(i)));
//                            newObj.put("payAmount",obj.getDoubleValue(payMap.get(i)));
//                            newObj.put("svgPrice",obj.getDoubleValue(svgMap.get(i)));
//                            data.add(newObj);
//                            newObj = new JSONObject();
//                        }
//                        int lastCounts = 0;
//                        double lastPays = 0;
//                        for(int i=start+5; i<=end; i++){
//                            lastCounts += obj.getIntValue(map.get(i));
//                            lastPays += obj.getDoubleValue(payMap.get(i));
//                        }
//                        DecimalFormat decimalFormat = new DecimalFormat("#.00");
//                        newObj.put("show",newMap.get(start+5).split("-")[0]+"以上");
//                        newObj.put("orderCount",lastCounts);
//                        newObj.put("payAmount",lastPays);
//                        newObj.put("svgPrice",decimalFormat.format(lastPays/lastCounts));
//                        data.add(newObj);
//                    }
//                    result.getJSONObject("info").put("totalCount",6);
//                    result.getJSONObject("info").remove("list");
//                    result.getJSONObject("info").put("list",data);
//
//                }
//            }
//            return result;
//        }catch (Exception e){
//            log.warn("{}",e);
//        }
//
//
//
//        return null;
//    }
//
//
//    @Override
//    public JSONObject getMonthPerOrderPriceCount(MonthParamDto monthParamDto) {
//        MultiValueMap<String, String> headers = new HttpHeaders();
//        headers.add("sk",sk);
//        headers.add("ak",ak);
//
//        ParamDto params = new ParamDto();
//        params.setCode(monthOrderDayAnalyseCode);
//
//        params.setColumnArray("merchant_id as \"merchantId\", " +
//                //会员总数
//                "pay_0_1_count , " +
//                "pay_1_3_count , " +
//                "pay_3_5_count , " +
//                "pay_5_10_count , " +
//                "pay_10_15_count , " +
//                "pay_15_30_count , " +
//                "pay_30_50_count , " +
//                "pay_50_100_count , " +
//                "pay_100_200_count , " +
//                "pay_200_500_count , " +
//                "pay_500_1000_count , " +
//                "over_1000_count ,  " +
//                "pay_0_1_amount , " +
//                "pay_1_3_amount , " +
//                "pay_3_5_amount , " +
//                "pay_5_10_amount , " +
//                "pay_10_15_amount , " +
//                "pay_15_30_amount , " +
//                "pay_30_50_amount , " +
//                "pay_50_100_amount , " +
//                "pay_100_200_amount , " +
//                "pay_200_500_amount , " +
//                "pay_500_1000_amount , " +
//                "over_1000_amount ,  " +
//                "cast(case when pay_0_1_count=0 then 0 else pay_0_1_amount::numeric/pay_0_1_count::numeric end  as decimal(10,2)) as \"payOrderPrice0_1\" , " +
//                "cast(case when pay_1_3_count=0 then 0 else pay_1_3_amount::numeric/pay_1_3_count::numeric end  as decimal(10,2)) as \"payOrderPrice1_3\" , " +
//                "cast(case when pay_3_5_count=0 then 0 else pay_3_5_amount::numeric/pay_3_5_count::numeric end  as decimal(10,2)) as \"payOrderPrice3_5\" , " +
//                "cast(case when pay_5_10_count=0 then 0 else pay_5_10_amount::numeric/pay_5_10_count::numeric end  as decimal(10,2)) as \"payOrderPrice5_10\" , " +
//                "cast(case when pay_10_15_count=0 then 0 else pay_10_15_amount::numeric/pay_10_15_count::numeric end  as decimal(10,2)) as \"payOrderPrice10_15\" , " +
//                "cast(case when pay_15_30_count=0 then 0 else pay_15_30_amount::numeric/pay_15_30_count::numeric end  as decimal(10,2)) as \"payOrderPrice15_30\" , " +
//                "cast(case when pay_30_50_count=0 then 0 else pay_30_50_amount::numeric/pay_30_50_count::numeric end  as decimal(10,2)) as \"payOrderPrice30_50\" , " +
//                "cast(case when pay_50_100_count=0 then 0 else pay_50_100_amount::numeric/pay_50_100_count::numeric end  as decimal(10,2)) as \"payOrderPrice50_100\" , " +
//                "cast(case when pay_100_200_count=0 then 0 else pay_100_200_amount::numeric/pay_100_200_count::numeric end  as decimal(10,2)) as \"payOrderPrice100_200\" , " +
//                "cast(case when pay_200_500_count=0 then 0 else pay_200_500_amount::numeric/pay_200_500_count::numeric end  as decimal(10,2)) as \"payOrderPrice200_500\" , " +
//                "cast(case when pay_500_1000_count=0 then 0 else pay_500_1000_amount::numeric/pay_500_1000_count::numeric end  as decimal(10,2)) as \"payOrderPrice500_1000\" , " +
//                "cast(case when over_1000_count=0 then 0 else over_1000_amount::numeric/over_1000_count::numeric end  as decimal(10,2)) as \"payOrderPriceOver1000\"  "
//        );
//
//
//        params.getQueryParams().setYear_month(monthParamDto.getMonth());
//        params.getQueryParams().setMerchant_id(monthParamDto.getMerchantId()+"");
//        params.getQueryParams().setCombo_type(monthParamDto.getComboType());
//
//        params.getQueryParams().setProvince_id(monthParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(monthParamDto.getProvinceId()+"");}});
//        params.getQueryParams().setCity_id(monthParamDto.getCityId() == null ? null:new HashSet<String>(){{add(monthParamDto.getCityId()+"");}});
//        params.getQueryParams().setDistrict_id(monthParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(monthParamDto.getAreaId()+"");}});
//        params.getQueryParams().setEquipment_group_id(monthParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(monthParamDto.getGroupId()+"");}});
//        params.getQueryParams().setLyy_equipment_type_id(monthParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(monthParamDto.getEquipmentTypeId()+"");}});
//
//        if(monthParamDto.getGroupIds() != null && monthParamDto.getGroupIds().size()>0){
//            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//            if(monthParamDto.getGroupId() == null || !monthParamDto.getGroupIds().contains(monthParamDto.getGroupId()+"")){
//                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(monthParamDto.getGroupIds())).first()+"");}});
//            }
//            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
//            params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
//        }
//
//        try{
//            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
//            JSONObject result = oneDataServiceFeign.getData(params,headers);
//            log.info("{}",result.toJSONString());
//
//            //动态返回商家6个区间
//            if(result != null && "200".equals(result.getString("code")) && result.getJSONObject("info") != null){
//                if(result.getJSONObject("info").getJSONArray("list") != null && result.getJSONObject("info").getJSONArray("list").size()>0){
//                    JSONObject obj = result.getJSONObject("info").getJSONArray("list").getJSONObject(0);
//                    JSONObject newObj = new JSONObject();
//                    JSONArray data = new JSONArray();
//                    Map<Integer,String> map = new HashMap<>();
//                    Map<Integer,String> payMap = new HashMap<>();
//                    Map<Integer,String> svgMap = new HashMap<>();
//                    Map<Integer,String> newMap = new HashMap<>();
//                    map.put(1,"pay_0_1_count");
//                    map.put(2,"pay_1_3_count");
//                    map.put(3,"pay_3_5_count");
//                    map.put(4,"pay_5_10_count");
//                    map.put(5,"pay_10_15_count");
//                    map.put(6,"pay_15_30_count");
//                    map.put(7,"pay_30_50_count");
//                    map.put(8,"pay_50_100_count");
//                    map.put(9,"pay_100_200_count");
//                    map.put(10,"pay_200_500_count");
//                    map.put(11,"pay_500_1000_count");
//                    map.put(12,"over_1000_count");
//
//                    payMap.put(1,"pay_0_1_amount");
//                    payMap.put(2,"pay_1_3_amount");
//                    payMap.put(3,"pay_3_5_amount");
//                    payMap.put(4,"pay_5_10_amount");
//                    payMap.put(5,"pay_10_15_amount");
//                    payMap.put(6,"pay_15_30_amount");
//                    payMap.put(7,"pay_30_50_amount");
//                    payMap.put(8,"pay_50_100_amount");
//                    payMap.put(9,"pay_100_200_amount");
//                    payMap.put(10,"pay_200_500_amount");
//                    payMap.put(11,"pay_500_1000_amount");
//                    payMap.put(12,"over_1000_amount");
//
//                    svgMap.put(1,"payOrderPrice0_1");
//                    svgMap.put(2,"payOrderPrice1_3");
//                    svgMap.put(3,"payOrderPrice3_5");
//                    svgMap.put(4,"payOrderPrice5_10");
//                    svgMap.put(5,"payOrderPrice10_15");
//                    svgMap.put(6,"payOrderPrice15_30");
//                    svgMap.put(7,"payOrderPrice30_50");
//                    svgMap.put(8,"payOrderPrice50_100");
//                    svgMap.put(9,"payOrderPrice100_200");
//                    svgMap.put(10,"payOrderPrice200_500");
//                    svgMap.put(11,"payOrderPrice500_1000");
//                    svgMap.put(12,"payOrderPriceOver1000");
//
//                    newMap.put(1,"0-1");
//                    newMap.put(2,"1-3");
//                    newMap.put(3,"3-5");
//                    newMap.put(4,"5-10");
//                    newMap.put(5,"10-15");
//                    newMap.put(6,"15-30");
//                    newMap.put(7,"30-50");
//                    newMap.put(8,"50-100");
//                    newMap.put(9,"100-200");
//                    newMap.put(10,"200-500");
//                    newMap.put(11,"500-1000");
//                    newMap.put(12,"1000以上");
//                    int start = 0;
//                    int count = 0;
//                    int end = 0;
//                    if(obj.getInteger("pay_0_1_count")>0){
//                        start = 1;
//                        end = 1;
//                        count++;
//                    }
//                    if(obj.getInteger("pay_1_3_count")>0){
//                        if(start <= 0){
//                            start = 2;
//                        }
//                        end = 2;
//                        count++;
//                    }
//                    if(obj.getInteger("pay_3_5_count")>0){
//                        if(start <= 0){
//                            start = 3;
//                        }
//                        end = 3;
//                        count++;
//                    }
//                    if(obj.getInteger("pay_5_10_count")>0){
//                        if(start <= 0){
//                            start = 4;
//                        }
//                        end = 4;
//                        count++;
//                    }
//                    if(obj.getInteger("pay_10_15_count")>0){
//                        if(start <= 0){
//                            start = 5;
//                        }
//                        end = 5;
//                        count++;
//                    }
//                    if(obj.getInteger("pay_15_30_count")>0){
//                        if(start <= 0){
//                            start = 6;
//                        }
//                        end = 6;
//                        count++;
//                    }
//                    if(obj.getInteger("pay_30_50_count")>0){
//                        if(start <= 0){
//                            start = 7;
//                        }
//                        end = 7;
//                        count++;
//                    }
//                    if(obj.getInteger("pay_50_100_count")>0){
//                        if(start <= 0){
//                            start = 8;
//                        }
//                        end = 8;
//                        count++;
//                    }
//                    if(obj.getInteger("pay_100_200_count")>0){
//                        if(start <= 0){
//                            start = 9;
//                        }
//                        end = 9;
//                        count++;
//                    }
//                    if(obj.getInteger("pay_200_500_count")>0){
//                        if(start <= 0){
//                            start = 10;
//                        }
//                        end = 10;
//                        count++;
//                    }
//                    if(obj.getInteger("pay_500_1000_count")>0){
//                        if(start <= 0){
//                            start = 11;
//                        }
//                        end = 11;
//                        count++;
//                    }
//                    if(obj.getInteger("over_1000_count")>0){
//                        if(start <= 0){
//                            start = 12;
//                        }
//                        end = 12;
//                        count++;
//                    }
//                    //刚刚好6个档次区间
//                    if(end - start ==5){
//                        for(int i=start; i<=end; i++){
//                            newObj.put("show",newMap.get(i));
//                            newObj.put("orderCount",obj.getIntValue(map.get(i)));
//                            newObj.put("payAmount",obj.getDoubleValue(payMap.get(i)));
//                            newObj.put("svgPrice",obj.getDoubleValue(svgMap.get(i)));
//                            data.add(newObj);
//                            newObj = new JSONObject();
//                        }
//                    }
//
//                    //如果少于6个，且网上能够补齐6个
//                    if(end - start < 5){
//                        //网上区间够补齐6个
//                        if(start<=7){
//                            for(int i=start; i<=start+5; i++){
//                                newObj.put("show",newMap.get(i));
//                                newObj.put("orderCount",obj.getIntValue(map.get(i)));
//                                newObj.put("payAmount",obj.getDoubleValue(payMap.get(i)));
//                                newObj.put("svgPrice",obj.getDoubleValue(svgMap.get(i)));
//                                data.add(newObj);
//                                newObj = new JSONObject();
//                            }
//                        }
//                        //往上不够补，往下补
//                        if(start>7){
//                            for(int i=7; i<=12; i++){
//                                newObj.put("show",newMap.get(i));
//                                newObj.put("orderCount",obj.getIntValue(map.get(i)));
//                                newObj.put("payAmount",obj.getDoubleValue(payMap.get(i)));
//                                newObj.put("svgPrice",obj.getDoubleValue(svgMap.get(i)));
//                                data.add(newObj);
//                                newObj = new JSONObject();
//                            }
//                        }
//
//                    }
//
//                    //大于6个档次
//                    if(end - start > 5){
//                        for(int i=start; i<start+5; i++){
//                            newObj.put("show",newMap.get(i));
//                            newObj.put("orderCount",obj.getIntValue(map.get(i)));
//                            newObj.put("payAmount",obj.getDoubleValue(payMap.get(i)));
//                            newObj.put("svgPrice",obj.getDoubleValue(svgMap.get(i)));
//                            data.add(newObj);
//                            newObj = new JSONObject();
//                        }
//                        int lastCounts = 0;
//                        double lastPays = 0;
//                        for(int i=start+5; i<=end; i++){
//                            lastCounts += obj.getIntValue(map.get(i));
//                            lastPays += obj.getDoubleValue(payMap.get(i));
//                        }
//
//                        DecimalFormat decimalFormat = new DecimalFormat("#.00");
//                        newObj.put("show",newMap.get(start+5).split("-")[0]+"以上");
//                        newObj.put("orderCount",lastCounts);
//                        newObj.put("payAmount",lastPays);
//                        newObj.put("svgPrice",decimalFormat.format(lastPays/lastCounts));
//                        data.add(newObj);
//                    }
//                    result.getJSONObject("info").put("totalCount",6);
//                    result.getJSONObject("info").remove("list");
//                    result.getJSONObject("info").put("list",data);
//
//                }
//            }
//            return result;
//        }catch (Exception e){
//            log.warn("{}",e);
//        }
//
//
//
//        return null;
//    }
//
//    @Override
//    public JSONObject getYearPerOrderPriceCount(YearParamDto yearParamDto) {
//        MultiValueMap<String, String> headers = new HttpHeaders();
//        headers.add("sk",sk);
//        headers.add("ak",ak);
//
//        ParamDto params = new ParamDto();
//        params.setCode(yearOrderDayAnalyseCode);
//
//        params.setColumnArray("merchant_id as \"merchantId\", " +
//                //会员总数
//                "pay_0_1_count , " +
//                "pay_1_3_count , " +
//                "pay_3_5_count , " +
//                "pay_5_10_count , " +
//                "pay_10_15_count , " +
//                "pay_15_30_count , " +
//                "pay_30_50_count , " +
//                "pay_50_100_count , " +
//                "pay_100_200_count , " +
//                "pay_200_500_count , " +
//                "pay_500_1000_count , " +
//                "over_1000_count ,  " +
//                "pay_0_1_amount , " +
//                "pay_1_3_amount , " +
//                "pay_3_5_amount , " +
//                "pay_5_10_amount , " +
//                "pay_10_15_amount , " +
//                "pay_15_30_amount , " +
//                "pay_30_50_amount , " +
//                "pay_50_100_amount , " +
//                "pay_100_200_amount , " +
//                "pay_200_500_amount , " +
//                "pay_500_1000_amount , " +
//                "over_1000_amount ,  " +
//                "cast(case when pay_0_1_count=0 then 0 else pay_0_1_amount::numeric/pay_0_1_count::numeric end  as decimal(10,2)) as \"payOrderPrice0_1\" , " +
//                "cast(case when pay_1_3_count=0 then 0 else pay_1_3_amount::numeric/pay_1_3_count::numeric end  as decimal(10,2)) as \"payOrderPrice1_3\" , " +
//                "cast(case when pay_3_5_count=0 then 0 else pay_3_5_amount::numeric/pay_3_5_count::numeric end  as decimal(10,2)) as \"payOrderPrice3_5\" , " +
//                "cast(case when pay_5_10_count=0 then 0 else pay_5_10_amount::numeric/pay_5_10_count::numeric end  as decimal(10,2)) as \"payOrderPrice5_10\" , " +
//                "cast(case when pay_10_15_count=0 then 0 else pay_10_15_amount::numeric/pay_10_15_count::numeric end  as decimal(10,2)) as \"payOrderPrice10_15\" , " +
//                "cast(case when pay_15_30_count=0 then 0 else pay_15_30_amount::numeric/pay_15_30_count::numeric end  as decimal(10,2)) as \"payOrderPrice15_30\" , " +
//                "cast(case when pay_30_50_count=0 then 0 else pay_30_50_amount::numeric/pay_30_50_count::numeric end  as decimal(10,2)) as \"payOrderPrice30_50\" , " +
//                "cast(case when pay_50_100_count=0 then 0 else pay_50_100_amount::numeric/pay_50_100_count::numeric end  as decimal(10,2)) as \"payOrderPrice50_100\" , " +
//                "cast(case when pay_100_200_count=0 then 0 else pay_100_200_amount::numeric/pay_100_200_count::numeric end  as decimal(10,2)) as \"payOrderPrice100_200\" , " +
//                "cast(case when pay_200_500_count=0 then 0 else pay_200_500_amount::numeric/pay_200_500_count::numeric end  as decimal(10,2)) as \"payOrderPrice200_500\" , " +
//                "cast(case when pay_500_1000_count=0 then 0 else pay_500_1000_amount::numeric/pay_500_1000_count::numeric end  as decimal(10,2)) as \"payOrderPrice500_1000\" , " +
//                "cast(case when over_1000_count=0 then 0 else over_1000_amount::numeric/over_1000_count::numeric end  as decimal(10,2)) as \"payOrderPriceOver1000\"  "
//        );
//
//
//        params.getQueryParams().setYear_str(yearParamDto.getYear()+"");
//        params.getQueryParams().setMerchant_id(yearParamDto.getMerchantId()+"");
//        params.getQueryParams().setCombo_type(yearParamDto.getComboType());
//
//        params.getQueryParams().setProvince_id(yearParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(yearParamDto.getProvinceId()+"");}});
//        params.getQueryParams().setCity_id(yearParamDto.getCityId() == null ? null:new HashSet<String>(){{add(yearParamDto.getCityId()+"");}});
//        params.getQueryParams().setDistrict_id(yearParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(yearParamDto.getAreaId()+"");}});
//        params.getQueryParams().setEquipment_group_id(yearParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(yearParamDto.getGroupId()+"");}});
//        params.getQueryParams().setLyy_equipment_type_id(yearParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(yearParamDto.getEquipmentTypeId()+"");}});
//
//        if(yearParamDto.getGroupIds() != null && yearParamDto.getGroupIds().size()>0){
//            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//            if(yearParamDto.getGroupId() == null || !yearParamDto.getGroupIds().contains(yearParamDto.getGroupId()+"")){
//                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(yearParamDto.getGroupIds())).first()+"");}});
//            }
//            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
//            params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
//        }
//
//        try{
//            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
//            JSONObject result = oneDataServiceFeign.getData(params,headers);
//            log.info("{}",result.toJSONString());
//
//            //动态返回商家6个区间
//            if(result != null && "200".equals(result.getString("code")) && result.getJSONObject("info") != null){
//                if(result.getJSONObject("info").getJSONArray("list") != null && result.getJSONObject("info").getJSONArray("list").size()>0){
//                    JSONObject obj = result.getJSONObject("info").getJSONArray("list").getJSONObject(0);
//                    JSONObject newObj = new JSONObject();
//                    JSONArray data = new JSONArray();
//                    Map<Integer,String> map = new HashMap<>();
//                    Map<Integer,String> payMap = new HashMap<>();
//                    Map<Integer,String> svgMap = new HashMap<>();
//                    Map<Integer,String> newMap = new HashMap<>();
//                    map.put(1,"pay_0_1_count");
//                    map.put(2,"pay_1_3_count");
//                    map.put(3,"pay_3_5_count");
//                    map.put(4,"pay_5_10_count");
//                    map.put(5,"pay_10_15_count");
//                    map.put(6,"pay_15_30_count");
//                    map.put(7,"pay_30_50_count");
//                    map.put(8,"pay_50_100_count");
//                    map.put(9,"pay_100_200_count");
//                    map.put(10,"pay_200_500_count");
//                    map.put(11,"pay_500_1000_count");
//                    map.put(12,"over_1000_count");
//
//                    payMap.put(1,"pay_0_1_amount");
//                    payMap.put(2,"pay_1_3_amount");
//                    payMap.put(3,"pay_3_5_amount");
//                    payMap.put(4,"pay_5_10_amount");
//                    payMap.put(5,"pay_10_15_amount");
//                    payMap.put(6,"pay_15_30_amount");
//                    payMap.put(7,"pay_30_50_amount");
//                    payMap.put(8,"pay_50_100_amount");
//                    payMap.put(9,"pay_100_200_amount");
//                    payMap.put(10,"pay_200_500_amount");
//                    payMap.put(11,"pay_500_1000_amount");
//                    payMap.put(12,"over_1000_amount");
//
//                    svgMap.put(1,"payOrderPrice0_1");
//                    svgMap.put(2,"payOrderPrice1_3");
//                    svgMap.put(3,"payOrderPrice3_5");
//                    svgMap.put(4,"payOrderPrice5_10");
//                    svgMap.put(5,"payOrderPrice10_15");
//                    svgMap.put(6,"payOrderPrice15_30");
//                    svgMap.put(7,"payOrderPrice30_50");
//                    svgMap.put(8,"payOrderPrice50_100");
//                    svgMap.put(9,"payOrderPrice100_200");
//                    svgMap.put(10,"payOrderPrice200_500");
//                    svgMap.put(11,"payOrderPrice500_1000");
//                    svgMap.put(12,"payOrderPriceOver1000");
//
//                    newMap.put(1,"0-1");
//                    newMap.put(2,"1-3");
//                    newMap.put(3,"3-5");
//                    newMap.put(4,"5-10");
//                    newMap.put(5,"10-15");
//                    newMap.put(6,"15-30");
//                    newMap.put(7,"30-50");
//                    newMap.put(8,"50-100");
//                    newMap.put(9,"100-200");
//                    newMap.put(10,"200-500");
//                    newMap.put(11,"500-1000");
//                    newMap.put(12,"1000以上");
//                    int start = 0;
//                    int count = 0;
//                    int end = 0;
//                    if(obj.getInteger("pay_0_1_count")>0){
//                        start = 1;
//                        end = 1;
//                        count++;
//                    }
//                    if(obj.getInteger("pay_1_3_count")>0){
//                        if(start <= 0){
//                            start = 2;
//                        }
//                        end = 2;
//                        count++;
//                    }
//                    if(obj.getInteger("pay_3_5_count")>0){
//                        if(start <= 0){
//                            start = 3;
//                        }
//                        end = 3;
//                        count++;
//                    }
//                    if(obj.getInteger("pay_5_10_count")>0){
//                        if(start <= 0){
//                            start = 4;
//                        }
//                        end = 4;
//                        count++;
//                    }
//                    if(obj.getInteger("pay_10_15_count")>0){
//                        if(start <= 0){
//                            start = 5;
//                        }
//                        end = 5;
//                        count++;
//                    }
//                    if(obj.getInteger("pay_15_30_count")>0){
//                        if(start <= 0){
//                            start = 6;
//                        }
//                        end = 6;
//                        count++;
//                    }
//                    if(obj.getInteger("pay_30_50_count")>0){
//                        if(start <= 0){
//                            start = 7;
//                        }
//                        end = 7;
//                        count++;
//                    }
//                    if(obj.getInteger("pay_50_100_count")>0){
//                        if(start <= 0){
//                            start = 8;
//                        }
//                        end = 8;
//                        count++;
//                    }
//                    if(obj.getInteger("pay_100_200_count")>0){
//                        if(start <= 0){
//                            start = 9;
//                        }
//                        end = 9;
//                        count++;
//                    }
//                    if(obj.getInteger("pay_200_500_count")>0){
//                        if(start <= 0){
//                            start = 10;
//                        }
//                        end = 10;
//                        count++;
//                    }
//                    if(obj.getInteger("pay_500_1000_count")>0){
//                        if(start <= 0){
//                            start = 11;
//                        }
//                        end = 11;
//                        count++;
//                    }
//                    if(obj.getInteger("over_1000_count")>0){
//                        if(start <= 0){
//                            start = 12;
//                        }
//                        end = 12;
//                        count++;
//                    }
//                    //刚刚好6个档次区间
//                    if(end - start ==5){
//                        for(int i=start; i<=end; i++){
//                            newObj.put("show",newMap.get(i));
//                            newObj.put("orderCount",obj.getIntValue(map.get(i)));
//                            newObj.put("payAmount",obj.getDoubleValue(payMap.get(i)));
//                            newObj.put("svgPrice",obj.getDoubleValue(svgMap.get(i)));
//                            data.add(newObj);
//                            newObj = new JSONObject();
//                        }
//                    }
//
//                    //如果少于6个，且网上能够补齐6个
//                    if(end - start < 5){
//                        //网上区间够补齐6个
//                        if(start<=7){
//                            for(int i=start; i<=start+5; i++){
//                                newObj.put("show",newMap.get(i));
//                                newObj.put("orderCount",obj.getIntValue(map.get(i)));
//                                newObj.put("payAmount",obj.getDoubleValue(payMap.get(i)));
//                                newObj.put("svgPrice",obj.getDoubleValue(svgMap.get(i)));
//                                data.add(newObj);
//                                newObj = new JSONObject();
//                            }
//                        }
//                        //往上不够补，往下补
//                        if(start>7){
//                            for(int i=7; i<=12; i++){
//                                newObj.put("show",newMap.get(i));
//                                newObj.put("orderCount",obj.getIntValue(map.get(i)));
//                                newObj.put("payAmount",obj.getDoubleValue(payMap.get(i)));
//                                newObj.put("svgPrice",obj.getDoubleValue(svgMap.get(i)));
//                                data.add(newObj);
//                                newObj = new JSONObject();
//                            }
//                        }
//
//                    }
//
//                    //大于6个档次
//                    if(end - start > 5){
//                        for(int i=start; i<start+5; i++){
//                            newObj.put("show",newMap.get(i));
//                            newObj.put("orderCount",obj.getIntValue(map.get(i)));
//                            newObj.put("payAmount",obj.getDoubleValue(payMap.get(i)));
//                            newObj.put("svgPrice",obj.getDoubleValue(svgMap.get(i)));
//                            data.add(newObj);
//                            newObj = new JSONObject();
//                        }
//                        int lastCounts = 0;
//                        double lastPays = 0;
//                        for(int i=start+5; i<=end; i++){
//                            lastCounts += obj.getIntValue(map.get(i));
//                            lastPays += obj.getDoubleValue(payMap.get(i));
//                        }
//
//                        DecimalFormat decimalFormat = new DecimalFormat("#.00");
//                        newObj.put("show",newMap.get(start+5).split("-")[0]+"以上");
//                        newObj.put("orderCount",lastCounts);
//                        newObj.put("payAmount",lastPays);
//                        newObj.put("svgPrice",decimalFormat.format(lastPays/lastCounts));
//                        data.add(newObj);
//                    }
//                    result.getJSONObject("info").put("totalCount",6);
//                    result.getJSONObject("info").remove("list");
//                    result.getJSONObject("info").put("list",data);
//
//                }
//            }
//            return result;
//        }catch (Exception e){
//            log.warn("{}",e);
//        }
//
//
//
//        return null;
//    }
//
//    @Override
//    public JSONObject getProvince(DayParamDto dayParamDto) {
//        MultiValueMap<String, String> headers = new HttpHeaders();
//        headers.add("sk",sk);
//        headers.add("ak",ak);
//
//        ParamDto params = new ParamDto();
//        params.setCode(groupBaseInfoCode);
//        params.setColumnArray("distinct province_id , province_name "
//        );
//
//        params.getPageParams().setPageRow("100");
//        params.getQueryParams().setDistributor_id(dayParamDto.getMerchantId() == null ? null: dayParamDto.getMerchantId()+"");
//
//        if(dayParamDto.getGroupIds() != null && dayParamDto.getGroupIds().size()>0){
//            params.getQueryParams().setEquipment_group_id(dayParamDto.getGroupIds());
//            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
//            params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
//        }
//        try{
//            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
//            JSONObject result = oneDataServiceFeign.getData(params,headers);
//            log.info("{}",result.toJSONString());
//            if(result != null && "200".equals(result.getString("code")) && result.getJSONObject("info") != null){
//                JSONArray jsonArray = result.getJSONObject("info").getJSONArray("list");
//                Integer removeIndex = null;
//                if(jsonArray != null && jsonArray.size() > 0){
//                    //设置大小
//                    result.getJSONObject("info").remove("totalPage");
//                    result.getJSONObject("info").remove("totalCount");
//                    for(int i=0; i<jsonArray.size(); i++){
//                        JSONObject jsonObject = jsonArray.getJSONObject(i);
//                        if(jsonObject.getString("province_id")==null || jsonObject.getString("province_id").trim().isEmpty()){
//                            removeIndex = i;
//                        }else{
//                            jsonObject.put("provinceId",jsonObject.getIntValue("province_id"));
//                            jsonObject.put("provinceName",jsonObject.getString("province_name"));
//                            jsonObject.remove("province_id");
//                            jsonObject.remove("province_name");
//                        }
//                    }
//                    if(removeIndex !=null){
//                        jsonArray.remove(jsonArray.getJSONObject(removeIndex));
//                    }
//                }
//            }
//            return result;
//        }catch (Exception e){
//            log.warn("{}",e);
//        }
//
//
//
//        return null;
//    }
//
//    @Override
//    public JSONObject getCity(DayParamDto dayParamDto) {
//        MultiValueMap<String, String> headers = new HttpHeaders();
//        headers.add("sk",sk);
//        headers.add("ak",ak);
//
//        ParamDto params = new ParamDto();
//        params.setCode(groupBaseInfoCode);
//        params.setColumnArray("distinct city_id , city_name "
//        );
//
//        params.getPageParams().setPageRow("500");
//
//        params.getQueryParams().setDistributor_id(dayParamDto.getMerchantId() == null ? null: dayParamDto.getMerchantId()+"");
//        params.getQueryParams().setProvince_id(dayParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(dayParamDto.getProvinceId()+"");}});
//        params.getQueryParams().setCity_id(dayParamDto.getCityId() == null ? null:new HashSet<String>(){{add(dayParamDto.getCityId()+"");}});
//        params.getQueryParams().setDistrict_id(dayParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(dayParamDto.getAreaId()+"");}});
//        params.getQueryParams().setEquipment_group_id(dayParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(dayParamDto.getGroupId()+"");}});
//        params.getQueryParams().setLyy_equipment_type_id(dayParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(dayParamDto.getEquipmentTypeId()+"");}});
//
//        if(dayParamDto.getGroupIds() != null && dayParamDto.getGroupIds().size()>0){
//            params.getQueryParams().setEquipment_group_id(dayParamDto.getGroupIds());
//            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
//            params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
//        }
//
//        try{
//            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
//            JSONObject result = oneDataServiceFeign.getData(params,headers);
//            log.info("{}",result.toJSONString());
//            if(result != null && "200".equals(result.getString("code")) && result.getJSONObject("info") != null){
//                JSONArray jsonArray = result.getJSONObject("info").getJSONArray("list");
//                Integer removeIndex = null;
//                if(jsonArray != null && jsonArray.size() > 0){
//                    //设置大小
//                    result.getJSONObject("info").remove("totalPage");
//                    result.getJSONObject("info").remove("totalCount");
//                    for(int i=0; i<jsonArray.size(); i++){
//                        JSONObject jsonObject = jsonArray.getJSONObject(i);
//                        if(jsonObject.getString("city_id")==null || jsonObject.getString("city_id").trim().isEmpty()){
//                            removeIndex = i;
//                        }else{
//                            jsonObject.put("cityId",jsonObject.getIntValue("city_id"));
//                            jsonObject.put("cityName",jsonObject.getString("city_name"));
//                            jsonObject.remove("city_id");
//                            jsonObject.remove("city_name");
//                        }
//                    }
//                    if(removeIndex !=null){
//                        jsonArray.remove(jsonArray.getJSONObject(removeIndex));
//                    }
//                }
//            }
//            return result;
//        }catch (Exception e){
//            log.warn("{}",e);
//        }
//
//
//
//        return null;
//    }
//
//    @Override
//    public JSONObject getArea(DayParamDto dayParamDto) {
//        MultiValueMap<String, String> headers = new HttpHeaders();
//        headers.add("sk",sk);
//        headers.add("ak",ak);
//
//        ParamDto params = new ParamDto();
//        params.setCode(groupBaseInfoCode);
//        params.setColumnArray("distinct district_id , district_name "
//        );
//
//        params.getPageParams().setPageRow("500");
//
//        params.getQueryParams().setDistributor_id(dayParamDto.getMerchantId() == null ? null: dayParamDto.getMerchantId()+"");
//        params.getQueryParams().setProvince_id(dayParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(dayParamDto.getProvinceId()+"");}});
//        params.getQueryParams().setCity_id(dayParamDto.getCityId() == null ? null:new HashSet<String>(){{add(dayParamDto.getCityId()+"");}});
//        params.getQueryParams().setDistrict_id(dayParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(dayParamDto.getAreaId()+"");}});
//        params.getQueryParams().setEquipment_group_id(dayParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(dayParamDto.getGroupId()+"");}});
//        params.getQueryParams().setLyy_equipment_type_id(dayParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(dayParamDto.getEquipmentTypeId()+"");}});
//
//        if(dayParamDto.getGroupIds() != null && dayParamDto.getGroupIds().size()>0){
//            params.getQueryParams().setEquipment_group_id(dayParamDto.getGroupIds());
//            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
//            params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
//        }
//
//        try{
//            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
//            JSONObject result = oneDataServiceFeign.getData(params,headers);
//            log.info("{}",result.toJSONString());
//            if(result != null && "200".equals(result.getString("code")) && result.getJSONObject("info") != null){
//                JSONArray jsonArray = result.getJSONObject("info").getJSONArray("list");
//                Integer removeIndex = null;
//                if(jsonArray != null && jsonArray.size() > 0){
//                    //设置大小
//                    result.getJSONObject("info").remove("totalPage");
//                    result.getJSONObject("info").remove("totalCount");
//                    for(int i=0; i<jsonArray.size(); i++){
//                        JSONObject jsonObject = jsonArray.getJSONObject(i);
//                        if(jsonObject.getString("district_id")==null || jsonObject.getString("district_id").trim().isEmpty()){
//                            removeIndex = i;
//                        }else{
//                            jsonObject.put("areaId",jsonObject.getIntValue("district_id"));
//                            jsonObject.put("areaName",jsonObject.getString("district_name"));
//                            jsonObject.remove("district_id");
//                            jsonObject.remove("district_name");
//                        }
//                    }
//                    if(removeIndex !=null){
//                        jsonArray.remove(jsonArray.getJSONObject(removeIndex));
//                    }
//                }
//            }
//            return result;
//        }catch (Exception e){
//            log.warn("{}",e);
//        }
//
//
//
//        return null;
//    }
//
//    @Override
//    public JSONObject getGroup(DayParamDto dayParamDto) {
//        MultiValueMap<String, String> headers = new HttpHeaders();
//        headers.add("sk",sk);
//        headers.add("ak",ak);
//
//        ParamDto params = new ParamDto();
//        params.setCode(groupBaseInfoCode);
//        params.setColumnArray("distinct equipment_group_id , equipment_group_name "
//        );
//
//        params.getPageParams().setPageRow("500");
//
//        params.getQueryParams().setDistributor_id(dayParamDto.getMerchantId() == null ? null: dayParamDto.getMerchantId()+"");
//        params.getQueryParams().setProvince_id(dayParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(dayParamDto.getProvinceId()+"");}});
//        params.getQueryParams().setCity_id(dayParamDto.getCityId() == null ? null:new HashSet<String>(){{add(dayParamDto.getCityId()+"");}});
//        params.getQueryParams().setDistrict_id(dayParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(dayParamDto.getAreaId()+"");}});
//        params.getQueryParams().setEquipment_group_id(dayParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(dayParamDto.getGroupId()+"");}});
//        params.getQueryParams().setLyy_equipment_type_id(dayParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(dayParamDto.getEquipmentTypeId()+"");}});
//
//        if(dayParamDto.getGroupIds() != null && dayParamDto.getGroupIds().size()>0){
//            params.getQueryParams().setEquipment_group_id(dayParamDto.getGroupIds());
//            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
//            params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
//        }
//
//        if(dayParamDto.getGroupName() != null && !dayParamDto.getGroupName().trim().isEmpty()){
//            params.getLikeParams().put("equipment_group_name",new HashSet<String>(){{add(dayParamDto.getGroupName());}} );
//        }
//
//        try{
//            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
//            JSONObject result = oneDataServiceFeign.getData(params,headers);
//            log.info("{}",result.toJSONString());
//            if(result != null && "200".equals(result.getString("code")) && result.getJSONObject("info") != null){
//                JSONArray jsonArray = result.getJSONObject("info").getJSONArray("list");
//                Integer removeIndex = null;
//                if(jsonArray != null && jsonArray.size() > 0){
//                    //设置大小
//                    result.getJSONObject("info").remove("totalPage");
//                    result.getJSONObject("info").remove("totalCount");
//                    for(int i=0; i<jsonArray.size(); i++){
//                        JSONObject jsonObject = jsonArray.getJSONObject(i);
//                        if(jsonObject.getString("equipment_group_id")==null || jsonObject.getString("equipment_group_id").trim().isEmpty()){
//                            removeIndex = i;
//                        }else{
//                            jsonObject.put("groupId",jsonObject.getIntValue("equipment_group_id"));
//                            jsonObject.put("groupName",jsonObject.getString("equipment_group_name"));
//                            jsonObject.remove("equipment_group_id");
//                            jsonObject.remove("equipment_group_name");
//                        }
//                    }
//                    if(removeIndex !=null){
//                        jsonArray.remove(jsonArray.getJSONObject(removeIndex));
//                    }
//                }
//            }
//            return result;
//        }catch (Exception e){
//            log.warn("{}",e);
//        }
//
//
//
//        return null;
//    }
//
//    @Override
//    public JSONObject getBenifit(DayParamDto dayParamDto) {
//        return null;
//    }
//
//    @Override
//    public JSONObject getMerchantEquipmentType(DayParamDto dayParamDto) {
//        MultiValueMap<String, String> headers = new HttpHeaders();
//        headers.add("sk",sk);
//        headers.add("ak",ak);
//
//        ParamDto params = new ParamDto();
//        params.setCode(merchantEquipmentTypeCode);
//        params.setColumnArray("distinct equipment_type_id as \"equipmentTypeId\" , equipment_type_name as \"equipmentTypeName\" "
//        );
//
//        params.getPageParams().setPageRow("500");
//
//        params.getQueryParams().setDistributor_id(dayParamDto.getMerchantId() == null ? null: dayParamDto.getMerchantId()+"");
//        params.getQueryParams().setProvince_id(dayParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(dayParamDto.getProvinceId()+"");}});
//        params.getQueryParams().setCity_id(dayParamDto.getCityId() == null ? null:new HashSet<String>(){{add(dayParamDto.getCityId()+"");}});
//        params.getQueryParams().setDistrict_id(dayParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(dayParamDto.getAreaId()+"");}});
//        params.getQueryParams().setEquipment_group_id(dayParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(dayParamDto.getGroupId()+"");}});
//        params.getQueryParams().setLyy_equipment_type_id(dayParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(dayParamDto.getEquipmentTypeId()+"");}});
//
//        if(dayParamDto.getGroupIds() != null && dayParamDto.getGroupIds().size()>0){
//            params.getQueryParams().setEquipment_group_id(dayParamDto.getGroupIds());
//        }
//
//        try{
//            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
//            JSONObject result = oneDataServiceFeign.getData(params,headers);
//            log.info("{}",result.toJSONString());
//            if(result != null && "200".equals(result.getString("code")) && result.getJSONObject("info") != null){
//                JSONArray jsonArray = result.getJSONObject("info").getJSONArray("list");
//                Integer removeIndex = null;
//                if(jsonArray != null && jsonArray.size() > 0){
//                    //设置大小
//                    result.getJSONObject("info").remove("totalPage");
//                    result.getJSONObject("info").remove("totalCount");
//                    for(int i=0; i<jsonArray.size(); i++){
//                        JSONObject jsonObject = jsonArray.getJSONObject(i);
//                        if(jsonObject.getString("equipmentTypeId")==null || jsonObject.getString("equipmentTypeId").trim().isEmpty()){
//                            removeIndex = i;
//                        }
//                    }
//                    if(removeIndex !=null){
//                        jsonArray.remove(jsonArray.getJSONObject(removeIndex));
//                    }
//                }
//            }
//            return result;
//        }catch (Exception e){
//            log.warn("{}",e);
//        }
//
//
//
//        return null;
//    }
//
//    /**
//     * 日报获取省份维度统计信息
//     * @param dayParamDto
//     * @return
//     */
//    public JSONObject getDayProvinceData(DayParamDto dayParamDto) {
//    	ParamDto params = new ParamDto();
//    	params.setCode(dayEquipmentAnalyseCode);
//    	params.setColumnArray(QuerySql.PROVINCE_COMMON_COLUMN_ARRAY);
//        params.setGroupByColumnArray("province_name");
//        
//        DayParamDto newDto = new DayParamDto() ;
//    	newDto.setMerchantId(dayParamDto.getMerchantId());
//    	newDto.setDay(dayParamDto.getDay());
//        newDto.setPageNum(dayParamDto.getPageNum());
//    	
//        JSONObject provinceResult = getData(params, newDto);
//        return provinceResult;
//    }
//
//    /**
//     * 周报报获取省份维度统计信息
//     * @param weekParamDto
//     * @return
//     */
//    public JSONObject getWeekProvinceData(WeekParamDto weekParamDto) {
//    	ParamDto params = new ParamDto();
//    	params.setCode(weekEquipmentAnalyseCode);
//    	params.setColumnArray(QuerySql.PROVINCE_COMMON_COLUMN_ARRAY);
//        params.setGroupByColumnArray("province_name");
//        
//        WeekParamDto newDto = new WeekParamDto() ;
//    	newDto.setMerchantId(weekParamDto.getMerchantId());
//    	newDto.setMonth(weekParamDto.getMonth());
//    	newDto.setWeek(weekParamDto.getWeek());
//    	newDto.setPageNum(weekParamDto.getPageNum());
//        
//    	JSONObject provinceResult = getData(params, newDto);
//        return provinceResult;
//    }
//
//    /**
//     * 月报报获取省份维度统计信息
//     * @param monthParamDto
//     * @return
//     */
//    public JSONObject getMonthProvinceData(MonthParamDto monthParamDto) {
//    	ParamDto params = new ParamDto();
//    	params.setCode(monthEquipmentAnalyseCode);
//    	params.setColumnArray(QuerySql.PROVINCE_COMMON_COLUMN_ARRAY);
//        params.setGroupByColumnArray("province_name");
//         
//        MonthParamDto newDto = new MonthParamDto() ;
//    	newDto.setMerchantId(monthParamDto.getMerchantId());
//    	newDto.setYear(monthParamDto.getYear());
//    	newDto.setMonth(monthParamDto.getMonth());
//    	newDto.setPageNum(monthParamDto.getPageNum());
//    	
//    	JSONObject provinceResult = getData(params, newDto);
//        return provinceResult;
//    }
//
//    /**
//     * 年报报获取省份维度统计信息
//     * @param yearParamDto
//     * @return
//     */
//    public JSONObject getYearProvinceData(YearParamDto yearParamDto) {
//    	ParamDto params = new ParamDto();
//    	params.setCode(yearEquipmentAnalyseCode);
//    	params.setColumnArray(QuerySql.PROVINCE_COMMON_COLUMN_ARRAY);
//        params.setGroupByColumnArray("province_name");
//        
//        YearParamDto newDto = new YearParamDto() ;
//    	newDto.setMerchantId(yearParamDto.getMerchantId());
//    	newDto.setYear(yearParamDto.getYear());
//        newDto.setPageNum(yearParamDto.getPageNum());
//        
//    	JSONObject provinceResult = getData(params, newDto);
//        return provinceResult;
//    }
//    
//    /**
//     * 日报获取省份维度统计信息-底部平均值
//     * @param dayParamDto
//     * @return
//     */
//    public JSONObject getDayProvinceAvgData(DayParamDto dayParamDto) {
//    	ParamDto params = new ParamDto();
//    	params.setCode(dayEquipmentAnalyseCode);
//    	params.setColumnArray(QuerySql.PROVINCE_AVG_COLUMN_ARRAY);
//    	
//        DayParamDto newDto = new DayParamDto() ;
//    	newDto.setMerchantId(dayParamDto.getMerchantId());
//    	newDto.setDay(dayParamDto.getDay());
//    	newDto.setPageNum(1);
//        
//        JSONObject provinceAvgResult = getData(params, newDto);
//        return provinceAvgResult;
//    }
//
//    /**
//     * 周报报获取省份维度统计信息-底部平均值
//     * @param weekParamDto
//     * @return
//     */
//    public JSONObject getWeekProvinceAvgData(WeekParamDto weekParamDto) {
//    	ParamDto params = new ParamDto();
//    	params.setCode(weekEquipmentAnalyseCode);
//    	params.setColumnArray(QuerySql.PROVINCE_AVG_COLUMN_ARRAY);
//    	
//        WeekParamDto newDto = new WeekParamDto() ;
//    	newDto.setMerchantId(weekParamDto.getMerchantId());
//    	newDto.setMonth(weekParamDto.getMonth());
//    	newDto.setWeek(weekParamDto.getWeek());
//    	newDto.setPageNum(1);
//        
//        JSONObject provinceAvgResult = getData(params, newDto);
//        return provinceAvgResult;
//    }
//
//    /**
//     * 月报报获取省份维度统计信息-底部平均值
//     * @param monthParamDto
//     * @return
//     */
//    public JSONObject getMonthProvinceAvgData(MonthParamDto monthParamDto) {
//    	ParamDto params = new ParamDto();
//    	params.setCode(monthEquipmentAnalyseCode);
//    	params.setColumnArray(QuerySql.PROVINCE_AVG_COLUMN_ARRAY);
//    	
//        MonthParamDto newDto = new MonthParamDto() ;
//    	newDto.setMerchantId(monthParamDto.getMerchantId());
//    	newDto.setYear(monthParamDto.getYear());
//    	newDto.setMonth(monthParamDto.getMonth());
//    	newDto.setPageNum(1);
//
//        JSONObject provinceAvgResult = getData(params, newDto);
//        return provinceAvgResult;
//    }
//
//    /**
//     * 年报报获取省份维度统计信息-底部平均值
//     * @param yearParamDto
//     * @return
//     */
//    public JSONObject getYearProvinceAvgData(YearParamDto yearParamDto) {
//    	ParamDto params = new ParamDto();
//    	params.setCode(yearEquipmentAnalyseCode);
//    	params.setColumnArray(QuerySql.PROVINCE_AVG_COLUMN_ARRAY);
//    	
//        YearParamDto newDto = new YearParamDto() ;
//    	newDto.setMerchantId(yearParamDto.getMerchantId());
//    	newDto.setYear(yearParamDto.getYear());
//    	newDto.setPageNum(1);
//        
//        JSONObject provinceAvgResult = getData(params, newDto);
//        return provinceAvgResult;
//    }
//    
//    /**
//     * 日报获取城市维度统计信息
//     * @param dayParamDto
//     * @return
//     */
//    public JSONObject getDayCityData(DayParamDto dayParamDto) {
//    	ParamDto params = new ParamDto();
//    	
//    	params.setCode(dayEquipmentAnalyseCode);
//    	params.setColumnArray(QuerySql.CITY_COMMON_COLUMN_ARRAY);
//        params.setGroupByColumnArray("city_name");
//        
//        DayParamDto newDto = new DayParamDto() ;
//        newDto.setMerchantId(dayParamDto.getMerchantId());
//        newDto.setDay(dayParamDto.getDay());
//        newDto.setPageNum(dayParamDto.getPageNum());
//        
//        JSONObject cityResult = getData(params, newDto);
//        return cityResult;
//    }
//
//    /**
//     * 周报报获取城市维度统计信息
//     * @param weekParamDto
//     * @return
//     */
//    public JSONObject getWeekCityData(WeekParamDto weekParamDto){
//    	ParamDto params = new ParamDto();
//        
//    	params.setCode(weekEquipmentAnalyseCode);
//    	params.setColumnArray(QuerySql.CITY_COMMON_COLUMN_ARRAY);
//        params.setGroupByColumnArray("city_name");
//         
//    	WeekParamDto newDto = new WeekParamDto() ;
//    	newDto.setMerchantId(weekParamDto.getMerchantId());
//    	newDto.setMonth(weekParamDto.getMonth());
//    	newDto.setWeek(weekParamDto.getWeek());
//        newDto.setPageNum(weekParamDto.getPageNum());
//        
//    	JSONObject cityResult = getData(params, newDto);
//        return cityResult;
//    }
//
//    /**
//     * 月报报获取城市维度统计信息
//     * @param monthParamDto
//     * @return
//     */
//    public JSONObject getMonthCityData(MonthParamDto monthParamDto){
//    	ParamDto params = new ParamDto();
//    	params.setCode(monthEquipmentAnalyseCode);
//    	params.setColumnArray(QuerySql.CITY_COMMON_COLUMN_ARRAY);
//        params.setGroupByColumnArray("city_name");
//        
//        MonthParamDto newDto = new MonthParamDto() ;
//    	newDto.setMerchantId(monthParamDto.getMerchantId());
//    	newDto.setYear(monthParamDto.getYear());
//    	newDto.setMonth(monthParamDto.getMonth());
//        newDto.setPageNum(monthParamDto.getPageNum() );
//        
//    	JSONObject cityResult = getData(params, newDto);
//        return cityResult;
//    }
//    
//    /**
//     * 年报报获取城市维度统计信息
//     * @param yearParamDto
//     * @return
//     */
//    public JSONObject getYearCityData(YearParamDto yearParamDto){
//    	ParamDto params = new ParamDto();
//    	params.setCode(yearEquipmentAnalyseCode);
//    	params.setColumnArray(QuerySql.CITY_COMMON_COLUMN_ARRAY);
//        params.setGroupByColumnArray("city_name");
//        
//        MonthParamDto newDto = new MonthParamDto() ;
//    	newDto.setMerchantId(yearParamDto.getMerchantId());
//    	newDto.setYear(yearParamDto.getYear());
//        newDto.setPageNum(yearParamDto.getPageNum() );
//        
//    	JSONObject cityResult = getData(params, newDto);
//        return cityResult;
//    }
//    
//    /**
//     * 日报获取城市维度统计信息-底部平均值
//     * @param dayParamDto
//     * @return
//     */
//    public JSONObject getDayCityAvgData(DayParamDto dayParamDto) {
//    	ParamDto params = new ParamDto();
//    	params.setCode(dayEquipmentAnalyseCode);
//    	params.setColumnArray(QuerySql.CITY_AVG_COLUMN_ARRAY);
//    	
//        DayParamDto newDto = new DayParamDto() ;
//        newDto.setMerchantId(dayParamDto.getMerchantId());
//        newDto.setDay(dayParamDto.getDay());
//        newDto.setPageNum(1);
//        
//        JSONObject cityAvgResult = getData(params, newDto);
//        return cityAvgResult;
//    }
//
//    /**
//     * 周报报获取城市维度统计信息-底部平均值
//     * @param weekParamDto
//     * @return
//     */
//    public JSONObject getWeekCityAvgData(WeekParamDto weekParamDto){
//    	ParamDto params = new ParamDto();
//    	params.setCode(weekEquipmentAnalyseCode);
//    	params.setColumnArray(QuerySql.CITY_AVG_COLUMN_ARRAY);
//    	
//    	WeekParamDto newDto = new WeekParamDto() ;
//    	newDto.setMerchantId(weekParamDto.getMerchantId());
//    	newDto.setMonth(weekParamDto.getMonth());
//    	newDto.setWeek(weekParamDto.getWeek());
//    	newDto.setPageNum(1);
//    	
//    	
//        JSONObject cityAvgResult = getData(params, newDto);
//        return cityAvgResult;
//    }
//
//    /**
//     * 月报报获取城市维度统计信息-底部平均值
//     * @param monthParamDto
//     * @return
//     */
//    public JSONObject getMonthCityAvgData(MonthParamDto monthParamDto){
//    	ParamDto params = new ParamDto();
//    	params.setCode(monthEquipmentAnalyseCode);
//    	params.setColumnArray(QuerySql.CITY_AVG_COLUMN_ARRAY);
//    	
//        MonthParamDto newDto = new MonthParamDto() ;
//    	newDto.setMerchantId(monthParamDto.getMerchantId());
//    	newDto.setYear(monthParamDto.getYear());
//    	newDto.setMonth(monthParamDto.getMonth());
//    	newDto.setPageNum(1);
//        
//        JSONObject cityAvgResult = getData(params, newDto);
//        return cityAvgResult;
//    }
//    
//    /**
//     * 年报报获取城市维度统计信息-底部平均值
//     * @param yearParamDto
//     * @return
//     */
//    public JSONObject getYearCityAvgData(YearParamDto yearParamDto){
//    	ParamDto params = new ParamDto();
//    	params.setCode(yearEquipmentAnalyseCode);
//    	params.setColumnArray(QuerySql.CITY_AVG_COLUMN_ARRAY);
//    	
//    	Integer pageNum = yearParamDto.getPageNum() ;
//        MonthParamDto newDto = new MonthParamDto() ;
//    	newDto.setMerchantId(yearParamDto.getMerchantId());
//    	newDto.setYear(yearParamDto.getYear());
//    	newDto.setPageNum(1);
//        
//        JSONObject cityAvgResult = getData(params, newDto);
//        return cityAvgResult;
//    }
//    
//    @Override
//    public JSONObject getDayGroupData(DayParamDto dayParamDto) {
//
//        ParamDto params = new ParamDto();
//        params.setCode(dayOrderDayAnalyseCode);
//        params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//
//        if(dayParamDto.getEquipmentTypeId() != null && dayParamDto.getEquipmentTypeId()>0){
//            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地+设备类型维度");
//        }
//        params.setColumnArray(QuerySql.DayOrderQuerySql);
//        JSONObject orderResult = getData(params, dayParamDto);
//
//        params.setCode(dayGroupEquipmentAnalyseCode);
//        params.setColumnArray(QuerySql.DayGroupEquipmentSql);
//        JSONObject groupResult = getData(params, dayParamDto);
//        //要把排序给排除，会员没有payAmount字段拍讯
//        String orderBy = dayParamDto.getOrderBy();
//        dayParamDto.setOrderBy(null);
//
//        params.setCode(dayMemberDataCode);
//        params.setColumnArray(QuerySql.DayMemberSql);
//        JSONObject memberResult = getData(params, dayParamDto);
//
//
//        DecimalFormat decimalFormat = new DecimalFormat("0.##");
//
//        if(orderResult == null && groupResult == null){
//            return null;
//        }
//
//        Map<String,JSONObject> data = new HashMap<>();
//        List<JSONObject> orders = new ArrayList<>();
//
//        if(memberResult != null && memberResult.getInteger("code") == 200 && memberResult.getJSONObject("info") != null && memberResult.getJSONObject("info").getJSONArray("list") != null){
//            JSONArray memberList = memberResult.getJSONObject("info").getJSONArray("list");
//            if(memberList != null){
//                for(int i=0; i<memberList.size(); i++){
//                    JSONObject member = memberList.getJSONObject(i);
//                    JSONObject obj = new JSONObject();
//                    obj.put("merchantId",member.getString("merchantId"));
//                    obj.put("groupId",member.getString("groupId"));
//                    obj.put("memberCountUp",member.getDoubleValue("memberCountUp"));
//                    obj.put("memberCount",member.getIntValue("memberCount"));
//                    obj.put("lastCycleMemberCount",member.getIntValue("lastCycleMemberCount"));
//                    obj.put("provinceId",null);
//                    obj.put("cityId",null);
//                    obj.put("districtId",null);
//                    obj.put("startCountsUp",0);
//                    obj.put("startCounts",0);
//                    obj.put("equipmentCounts",0);
//                    obj.put("equipmentCountsUp",0);
//                    obj.put("address",null);
//                    obj.put("payAmount",0);
//                    obj.put("payAmountUp",0);
//                    obj.put("payCount",0);
//                    obj.put("payCountUp",0);
//                    obj.put("perOrderAmount",0);
//                    obj.put("perOrderAmountUp",0);
//                    obj.put("perUserAmount",0);
//                    obj.put("perUserAmountUp",0);
//                    data.put(idProcess(obj.getString("merchantId"))+"-"+idProcess(obj.getString("groupId")),obj);
//
//                }
//            }
//        }
//
//        if(groupResult != null && groupResult.getInteger("code") == 200 && groupResult.getJSONObject("info") != null && groupResult.getJSONObject("info").getJSONArray("list") != null){
//            JSONArray groupList = groupResult.getJSONObject("info").getJSONArray("list");
//            if(groupList != null){
//                for(int i=0; i<groupList.size(); i++){
//                    JSONObject group = groupList.getJSONObject(i);
//                    if(data.get(idProcess(group.getString("merchantId"))+"-"+idProcess(group.getString("groupId"))) == null){
//                        JSONObject obj = new JSONObject();
//                        obj.put("merchantId",group.getString("merchantId"));
//                        obj.put("provinceId",group.getString("provinceId"));
//                        obj.put("cityId",group.getString("cityId"));
//                        obj.put("districtId",group.getString("districtId"));
//                        obj.put("groupId",group.getString("groupId"));
//                        obj.put("groupName",group.getString("groupName"));
//                        obj.put("startCountsUp",group.getDoubleValue("startCountsUp"));
//                        obj.put("startCounts",group.getIntValue("startCounts"));
//                        obj.put("equipmentCounts",group.getIntValue("equipmentCounts"));
//                        obj.put("equipmentCountsUp",group.getDoubleValue("equipmentCountsUp"));
//                        obj.put("address",group.getString("address"));
//                        obj.put("memberCountUp",0);
//                        obj.put("memberCount",0);
//                        obj.put("lastCycleMemberCount",0);
//                        obj.put("payAmount",0);
//                        obj.put("payAmountUp",0);
//                        obj.put("payCount",0);
//                        obj.put("payCountUp",0);
//                        obj.put("perOrderAmount",0);
//                        obj.put("perOrderAmountUp",0);
//                        obj.put("perUserAmount",0);
//                        obj.put("perUserAmountUp",0);
//                        data.put(idProcess(group.getString("merchantId"))+"-"+idProcess(group.getString("groupId")),obj);
//                    }else{
//                        JSONObject obj = data.get(idProcess(group.getString("merchantId"))+"-"+idProcess(group.getString("groupId")));
//                        obj.put("merchantId",group.getString("merchantId"));
//                        obj.put("provinceId",group.getString("provinceId"));
//                        obj.put("cityId",group.getString("cityId"));
//                        obj.put("districtId",group.getString("districtId"));
//                        obj.put("groupId",group.getString("groupId"));
//                        obj.put("groupName",group.getString("groupName"));
//                        obj.put("startCountsUp",group.getDoubleValue("startCountsUp"));
//                        obj.put("startCounts",group.getIntValue("startCounts"));
//                        obj.put("equipmentCounts",group.getIntValue("equipmentCounts"));
//                        obj.put("equipmentCountsUp",group.getDoubleValue("equipmentCountsUp"));
//                        obj.put("address",group.getString("address"));
//                        data.put(idProcess(group.getString("merchantId"))+"-"+idProcess(group.getString("groupId")),obj);
//                    }
//                }
//            }
//        }
//        if(orderResult != null && orderResult.getInteger("code") == 200 && orderResult.getJSONObject("info") != null && orderResult.getJSONObject("info").getJSONArray("list") != null){
//            JSONArray orderList = orderResult.getJSONObject("info").getJSONArray("list");
//            if(orderList != null){
//                for(int i=0; i<orderList.size(); i++){
//                    JSONObject order = orderList.getJSONObject(i);
//                    JSONObject obj = data.get(idProcess(order.getString("merchantId"))+"-"+idProcess(order.getString("groupId")));
//                    if(obj == null){
//                        obj = new JSONObject();
//                        obj.put("merchantId",order.getString("merchantId"));
//                        obj.put("provinceId",order.getString("provinceId"));
//                        obj.put("cityId",order.getString("cityId"));
//                        obj.put("districtId",order.getString("districtId"));
//                        obj.put("groupId",order.getString("groupId"));
//                        obj.put("groupName",order.getString("groupName"));
//                        obj.put("startCountsUp",0);
//                        obj.put("startCounts",0);
//                        obj.put("equipmentCounts",0);
//                        obj.put("equipmentCountsUp",0);
//                        obj.put("address",order.getString("address"));
//                        obj.put("memberCountUp",0);
//                        obj.put("memberCount",0);
//                        obj.put("lastCycleMemberCount",0);
//                        obj.put("payAmount",order.getDoubleValue("onlinePayAmount"));
//                        obj.put("payAmountUp",order.getDoubleValue("onlinePayAmountUp"));
//                        obj.put("payCount",order.getDoubleValue("onlinePayAmountUp"));
//                        obj.put("payCountUp",order.getDoubleValue("onlinePayCountsUp"));
//                        obj.put("perOrderAmount",order.getDoubleValue("perOnlineOrderAmount"));
//                        obj.put("perOrderAmountUp",order.getDoubleValue("perOnlineOrderAmountUp"));
//                        obj.put("perUserAmount",0);
//                        obj.put("perUserAmountUp",0);
//                        data.put(idProcess(order.getString("merchantId"))+"-"+idProcess(order.getString("groupId")),obj);
//                    }else{
//                        obj.put("payAmount",order.getDoubleValue("onlinePayAmount"));
//                        obj.put("payAmountUp",order.getDoubleValue("onlinePayAmountUp"));
//                        obj.put("payCount",order.getDoubleValue("onlinePayAmountUp"));
//                        obj.put("payCountUp",order.getDoubleValue("onlinePayCountsUp"));
//                        obj.put("perOrderAmount",order.getDoubleValue("perOnlineOrderAmount"));
//                        obj.put("perOrderAmountUp",order.getDoubleValue("perOnlineOrderAmountUp"));
//                        if(obj.getIntValue("memberCount") >0){
//                            obj.put("perUserAmount",decimalFormat.format(order.getDoubleValue("onlinePayAmount")/obj.getIntValue("memberCount")));
//                            if(obj.getIntValue("lastCycleMemberCount") == 0){
//                                if(order.getDoubleValue("onlinePayAmount") > 0){
//                                    obj.put("perUserAmountUp",1);
//                                }else{
//                                    obj.put("perUserAmountUp",0);
//                                }
//                            }else{
//                                if(order.getDoubleValue("lastCycleOnlinePayAmount") > 0){
//                                    obj.put("perUserAmountUp",decimalFormat.format((order.getDoubleValue("onlinePayAmount")/obj.getIntValue("memberCount")-order.getDoubleValue("lastCycleOnlinePayAmount")/obj.getIntValue("lastCycleMemberCount"))/order.getDoubleValue("lastCycleOnlinePayAmount")/obj.getIntValue("lastCycleMemberCount")));
//                                }else{
//                                    if(order.getDoubleValue("onlinePayAmount") > 0){
//                                        obj.put("perUserAmountUp",1);
//                                    }else{
//                                        obj.put("perUserAmountUp",0);
//                                    }
//                                }
//                            }
//                        }else{
//                            if(obj.getIntValue("lastCycleMemberCount") == 0 ||  order.getDoubleValue("lastCycleOnlinePayAmount") == 0){
//                                obj.put("perUserAmount",0);
//                            }else{
//                                obj.put("perUserAmount",-1);
//                            }
//
//                        }
//                    }
//
//                    data.put(idProcess(order.getString("merchantId"))+"-"+idProcess(order.getString("groupId")),obj);
//                }
//            }
//        }
//
//        if(data != null){
//            Set<String> keys = data.keySet();
//            //倒序
//            Map<Double,List<JSONObject>> treeMap = new TreeMap<>(Comparator.reverseOrder());
//            //正序
//            if("asc".equals(dayParamDto.getOrder())){
//                treeMap = new TreeMap<>();
//            }
//            for(String key : keys){
//                //如何排序,目前只做了按金钱排序
//                JSONObject obj = data.get(key);
//                if(obj.getString("merchantId") == null || "未知".equals(obj.getString("merchantId"))){
//                    continue;
//                }
//                List<JSONObject> list = treeMap.get(obj.getDoubleValue("payAmount"));
//                if(list == null){
//                    list = new ArrayList<JSONObject>();
//                }
//                list.add(obj);
//                treeMap.put(obj.getDoubleValue("payAmount"),list);
//            }
//
//            //开始按照顺序重新拼装list
//            Set<Double> tmKeys = treeMap.keySet();
//            for(Double key : tmKeys){
//                orders.addAll(treeMap.get(key));
//            }
//
//        }
//
//
//        orderResult.getJSONObject("info").remove("list");
//        orderResult.getJSONObject("info").put("list",orders);
//        return orderResult;
//    }
//
//    @Override
//    public JSONObject getWeekGroupData(WeekParamDto weekParamDto) {
//
//        ParamDto params = new ParamDto();
//        params.setCode(weekOrderDayAnalyseCode);
//        params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//        if(weekParamDto.getEquipmentTypeId() != null && weekParamDto.getEquipmentTypeId()>0){
//            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地+设备类型维度");
//        }
//        params.setColumnArray(QuerySql.WeekOrderQuerySql);
//        JSONObject orderResult = getData(params, weekParamDto);
//
//        params.setCode(weekGroupEquipmentAnalyseCode);
//        params.setColumnArray(QuerySql.GroupEquipmentSql);
//        JSONObject groupResult = getData(params, weekParamDto);
//
//        //要把排序给排除，会员没有payAmount字段拍讯
//        String orderBy = weekParamDto.getOrderBy();
//        weekParamDto.setOrderBy(null);
//
//        params.setCode(weekMemberDataCode);
//        params.setColumnArray(QuerySql.WeekMemberSql);
//        JSONObject memberResult = getData(params, weekParamDto);
//
//        DecimalFormat decimalFormat = new DecimalFormat("0.##");
//
//        if(orderResult == null && groupResult == null){
//            return null;
//        }
//
//        Map<String,JSONObject> data = new HashMap<>();
//        List<JSONObject> orders = new ArrayList<>();
//
//        if(memberResult != null && memberResult.getInteger("code") == 200 && memberResult.getJSONObject("info") != null && memberResult.getJSONObject("info").getJSONArray("list") != null){
//            JSONArray memberList = memberResult.getJSONObject("info").getJSONArray("list");
//            if(memberList != null){
//                for(int i=0; i<memberList.size(); i++){
//                    JSONObject member = memberList.getJSONObject(i);
//                    JSONObject obj = new JSONObject();
//                    obj.put("merchantId",member.getString("merchantId"));
//                    obj.put("groupId",member.getString("groupId"));
//                    obj.put("memberCountUp",member.getDoubleValue("memberCountUp"));
//                    obj.put("memberCount",member.getIntValue("memberCount"));
//                    obj.put("lastCycleMemberCount",member.getIntValue("lastCycleMemberCount"));
//                    obj.put("provinceId",null);
//                    obj.put("cityId",null);
//                    obj.put("districtId",null);
//                    obj.put("startCountsUp",0);
//                    obj.put("startCounts",0);
//                    obj.put("equipmentCounts",0);
//                    obj.put("equipmentCountsUp",0);
//                    obj.put("address",null);
//                    obj.put("payAmount",0);
//                    obj.put("payAmountUp",0);
//                    obj.put("payCount",0);
//                    obj.put("payCountUp",0);
//                    obj.put("perOrderAmount",0);
//                    obj.put("perOrderAmountUp",0);
//                    obj.put("perUserAmount",0);
//                    obj.put("perUserAmountUp",0);
//                    data.put(idProcess(obj.getString("merchantId"))+"-"+idProcess(obj.getString("groupId")),obj);
//
//                }
//            }
//        }
//
//        if(groupResult != null && groupResult.getInteger("code") == 200 && groupResult.getJSONObject("info") != null && groupResult.getJSONObject("info").getJSONArray("list") != null){
//            JSONArray groupList = groupResult.getJSONObject("info").getJSONArray("list");
//            if(groupList != null){
//                for(int i=0; i<groupList.size(); i++){
//                    JSONObject group = groupList.getJSONObject(i);
//                    if(data.get(idProcess(group.getString("merchantId"))+"-"+idProcess(group.getString("groupId"))) == null){
//                        JSONObject obj = new JSONObject();
//                        obj.put("merchantId",group.getString("merchantId"));
//                        obj.put("provinceId",group.getString("provinceId"));
//                        obj.put("cityId",group.getString("cityId"));
//                        obj.put("districtId",group.getString("districtId"));
//                        obj.put("groupId",group.getString("groupId"));
//                        obj.put("groupName",group.getString("groupName"));
//                        obj.put("startCountsUp",group.getDoubleValue("startCountsUp"));
//                        obj.put("startCounts",group.getIntValue("startCounts"));
//                        obj.put("equipmentCounts",group.getIntValue("equipmentCounts"));
//                        obj.put("equipmentCountsUp",group.getDoubleValue("equipmentCountsUp"));
//                        obj.put("address",group.getString("address"));
//                        obj.put("memberCountUp",0);
//                        obj.put("memberCount",0);
//                        obj.put("lastCycleMemberCount",0);
//                        obj.put("payAmount",0);
//                        obj.put("payAmountUp",0);
//                        obj.put("payCount",0);
//                        obj.put("payCountUp",0);
//                        obj.put("perOrderAmount",0);
//                        obj.put("perOrderAmountUp",0);
//                        obj.put("perUserAmount",0);
//                        obj.put("perUserAmountUp",0);
//                        data.put(idProcess(group.getString("merchantId"))+"-"+idProcess(group.getString("groupId")),obj);
//                    }else{
//                        JSONObject obj = data.get(idProcess(group.getString("merchantId"))+"-"+idProcess(group.getString("groupId")));
//                        obj.put("merchantId",group.getString("merchantId"));
//                        obj.put("provinceId",group.getString("provinceId"));
//                        obj.put("cityId",group.getString("cityId"));
//                        obj.put("districtId",group.getString("districtId"));
//                        obj.put("groupId",group.getString("groupId"));
//                        obj.put("groupName",group.getString("groupName"));
//                        obj.put("startCountsUp",group.getDoubleValue("startCountsUp"));
//                        obj.put("startCounts",group.getIntValue("startCounts"));
//                        obj.put("equipmentCounts",group.getIntValue("equipmentCounts"));
//                        obj.put("equipmentCountsUp",group.getDoubleValue("equipmentCountsUp"));
//                        obj.put("address",group.getString("address"));
//                        data.put(idProcess(group.getString("merchantId"))+"-"+idProcess(group.getString("groupId")),obj);
//                    }
//                }
//            }
//        }
//        if(orderResult != null && orderResult.getInteger("code") == 200 && orderResult.getJSONObject("info") != null && orderResult.getJSONObject("info").getJSONArray("list") != null){
//            JSONArray orderList = orderResult.getJSONObject("info").getJSONArray("list");
//            if(orderList != null){
//                for(int i=0; i<orderList.size(); i++){
//                    JSONObject order = orderList.getJSONObject(i);
//                    JSONObject obj = data.get(idProcess(order.getString("merchantId"))+"-"+idProcess(order.getString("groupId")));
//                    if(obj == null){
//                        obj = new JSONObject();
//                        obj.put("merchantId",order.getString("merchantId"));
//                        obj.put("provinceId",order.getString("provinceId"));
//                        obj.put("cityId",order.getString("cityId"));
//                        obj.put("districtId",order.getString("districtId"));
//                        obj.put("groupId",order.getString("groupId"));
//                        obj.put("groupName",order.getString("groupName"));
//                        obj.put("startCountsUp",0);
//                        obj.put("startCounts",0);
//                        obj.put("equipmentCounts",0);
//                        obj.put("equipmentCountsUp",0);
//                        obj.put("address",order.getString("address"));
//                        obj.put("memberCountUp",0);
//                        obj.put("memberCount",0);
//                        obj.put("lastCycleMemberCount",0);
//                        obj.put("payAmount",order.getDoubleValue("onlinePayAmount"));
//                        obj.put("payAmountUp",order.getDoubleValue("onlinePayAmountUp"));
//                        obj.put("payCount",order.getDoubleValue("onlinePayAmountUp"));
//                        obj.put("payCountUp",order.getDoubleValue("onlinePayCountsUp"));
//                        obj.put("perOrderAmount",order.getDoubleValue("perOnlineOrderAmount"));
//                        obj.put("perOrderAmountUp",order.getDoubleValue("perOnlineOrderAmountUp"));
//                        obj.put("perUserAmount",0);
//                        obj.put("perUserAmountUp",0);
//                        data.put(idProcess(order.getString("merchantId"))+"-"+idProcess(order.getString("groupId")),obj);
//                    }else{
//                        obj.put("payAmount",order.getDoubleValue("onlinePayAmount"));
//                        obj.put("payAmountUp",order.getDoubleValue("onlinePayAmountUp"));
//                        obj.put("payCount",order.getDoubleValue("onlinePayAmountUp"));
//                        obj.put("payCountUp",order.getDoubleValue("onlinePayCountsUp"));
//                        obj.put("perOrderAmount",order.getDoubleValue("perOnlineOrderAmount"));
//                        obj.put("perOrderAmountUp",order.getDoubleValue("perOnlineOrderAmountUp"));
//                        if(obj.getIntValue("memberCount") >0){
//                            obj.put("perUserAmount",decimalFormat.format(order.getDoubleValue("onlinePayAmount")/obj.getIntValue("memberCount")));
//                            if(obj.getIntValue("lastCycleMemberCount") == 0){
//                                if(order.getDoubleValue("onlinePayAmount") > 0){
//                                    obj.put("perUserAmountUp",1);
//                                }else{
//                                    obj.put("perUserAmountUp",0);
//                                }
//                            }else{
//                                if(order.getDoubleValue("lastCycleOnlinePayAmount") > 0){
//                                    obj.put("perUserAmountUp",decimalFormat.format((order.getDoubleValue("onlinePayAmount")/obj.getIntValue("memberCount")-order.getDoubleValue("lastCycleOnlinePayAmount")/obj.getIntValue("lastCycleMemberCount"))/order.getDoubleValue("lastCycleOnlinePayAmount")/obj.getIntValue("lastCycleMemberCount")));
//                                }else{
//                                    if(order.getDoubleValue("onlinePayAmount") > 0){
//                                        obj.put("perUserAmountUp",1);
//                                    }else{
//                                        obj.put("perUserAmountUp",0);
//                                    }
//                                }
//                            }
//                        }else{
//                            if(obj.getIntValue("lastCycleMemberCount") == 0 ||  order.getDoubleValue("lastCycleOnlinePayAmount") == 0){
//                                obj.put("perUserAmount",0);
//                            }else{
//                                obj.put("perUserAmount",-1);
//                            }
//
//                        }
//                    }
//
//                    data.put(idProcess(order.getString("merchantId"))+"-"+idProcess(order.getString("groupId")),obj);
//                }
//            }
//        }
//
//        if(data != null){
//            Set<String> keys = data.keySet();
//            //倒序
//            Map<Double,List<JSONObject>> treeMap = new TreeMap<>(Comparator.reverseOrder());
//            //正序
//            if("asc".equals(weekParamDto.getOrder())){
//                treeMap = new TreeMap<>();
//            }
//            for(String key : keys){
//                //如何排序,目前只做了按金钱排序
//                JSONObject obj = data.get(key);
//                if(obj.getString("merchantId") == null || "未知".equals(obj.getString("merchantId"))){
//                    continue;
//                }
//                List<JSONObject> list = treeMap.get(obj.getDoubleValue("payAmount"));
//                if(list == null){
//                    list = new ArrayList<JSONObject>();
//                }
//                list.add(obj);
//                treeMap.put(obj.getDoubleValue("payAmount"),list);
//            }
//
//            //开始按照顺序重新拼装list
//            Set<Double> tmKeys = treeMap.keySet();
//            for(Double key : tmKeys){
//                orders.addAll(treeMap.get(key));
//            }
//
//        }
//
//        orderResult.getJSONObject("info").remove("list");
//        orderResult.getJSONObject("info").put("list",orders);
//        return orderResult;
//    }
//
//    @Override
//    public JSONObject getMonthGroupData(MonthParamDto monthParamDto) {
//
//        ParamDto params = new ParamDto();
//        params.setCode(monthOrderDayAnalyseCode);
//        params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//        if(monthParamDto.getEquipmentTypeId() != null && monthParamDto.getEquipmentTypeId()>0){
//            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地+设备类型维度");
//        }
//        params.setColumnArray(QuerySql.MonthOrderQuerySql);
//        JSONObject orderResult = getData(params, monthParamDto);
//
//        params.setCode(monthGroupEquipmentAnalyseCode);
//        params.setColumnArray(QuerySql.GroupEquipmentSql);
//        JSONObject groupResult = getData(params, monthParamDto);
//
//        //要把排序给排除，会员没有payAmount字段拍讯
//        String orderBy = monthParamDto.getOrderBy();
//        monthParamDto.setOrderBy(null);
//
//        params.setCode(monthMemberDataCode);
//        params.setColumnArray(QuerySql.MonthMemberSql);
//        JSONObject memberResult = getData(params, monthParamDto);
//
//        DecimalFormat decimalFormat = new DecimalFormat("0.##");
//
//        if(orderResult == null && groupResult == null){
//            return null;
//        }
//
//        Map<String,JSONObject> data = new HashMap<>();
//        List<JSONObject> orders = new ArrayList<>();
//
//        if(memberResult != null && memberResult.getInteger("code") == 200 && memberResult.getJSONObject("info") != null && memberResult.getJSONObject("info").getJSONArray("list") != null){
//            JSONArray memberList = memberResult.getJSONObject("info").getJSONArray("list");
//            if(memberList != null){
//                for(int i=0; i<memberList.size(); i++){
//                    JSONObject member = memberList.getJSONObject(i);
//                    JSONObject obj = new JSONObject();
//                    obj.put("merchantId",member.getString("merchantId"));
//                    obj.put("groupId",member.getString("groupId"));
//                    obj.put("memberCountUp",member.getDoubleValue("memberCountUp"));
//                    obj.put("memberCount",member.getIntValue("memberCount"));
//                    obj.put("lastCycleMemberCount",member.getIntValue("lastCycleMemberCount"));
//                    obj.put("provinceId",null);
//                    obj.put("cityId",null);
//                    obj.put("districtId",null);
//                    obj.put("startCountsUp",0);
//                    obj.put("startCounts",0);
//                    obj.put("equipmentCounts",0);
//                    obj.put("equipmentCountsUp",0);
//                    obj.put("address",null);
//                    obj.put("payAmount",0);
//                    obj.put("payAmountUp",0);
//                    obj.put("payCount",0);
//                    obj.put("payCountUp",0);
//                    obj.put("perOrderAmount",0);
//                    obj.put("perOrderAmountUp",0);
//                    obj.put("perUserAmount",0);
//                    obj.put("perUserAmountUp",0);
//                    data.put(idProcess(obj.getString("merchantId"))+"-"+idProcess(obj.getString("groupId")),obj);
//
//                }
//            }
//        }
//
//        if(groupResult != null && groupResult.getInteger("code") == 200 && groupResult.getJSONObject("info") != null && groupResult.getJSONObject("info").getJSONArray("list") != null){
//            JSONArray groupList = groupResult.getJSONObject("info").getJSONArray("list");
//            if(groupList != null){
//                for(int i=0; i<groupList.size(); i++){
//                    JSONObject group = groupList.getJSONObject(i);
//                    if(data.get(idProcess(group.getString("merchantId"))+"-"+idProcess(group.getString("groupId"))) == null){
//                        JSONObject obj = new JSONObject();
//                        obj.put("merchantId",group.getString("merchantId"));
//                        obj.put("provinceId",group.getString("provinceId"));
//                        obj.put("cityId",group.getString("cityId"));
//                        obj.put("districtId",group.getString("districtId"));
//                        obj.put("groupId",group.getString("groupId"));
//                        obj.put("groupName",group.getString("groupName"));
//                        obj.put("startCountsUp",group.getDoubleValue("startCountsUp"));
//                        obj.put("startCounts",group.getIntValue("startCounts"));
//                        obj.put("equipmentCounts",group.getIntValue("equipmentCounts"));
//                        obj.put("equipmentCountsUp",group.getDoubleValue("equipmentCountsUp"));
//                        obj.put("address",group.getString("address"));
//                        obj.put("memberCountUp",0);
//                        obj.put("memberCount",0);
//                        obj.put("lastCycleMemberCount",0);
//                        obj.put("payAmount",0);
//                        obj.put("payAmountUp",0);
//                        obj.put("payCount",0);
//                        obj.put("payCountUp",0);
//                        obj.put("perOrderAmount",0);
//                        obj.put("perOrderAmountUp",0);
//                        obj.put("perUserAmount",0);
//                        obj.put("perUserAmountUp",0);
//                        data.put(idProcess(group.getString("merchantId"))+"-"+idProcess(group.getString("groupId")),obj);
//                    }else{
//                        JSONObject obj = data.get(idProcess(group.getString("merchantId"))+"-"+idProcess(group.getString("groupId")));
//                        obj.put("merchantId",group.getString("merchantId"));
//                        obj.put("provinceId",group.getString("provinceId"));
//                        obj.put("cityId",group.getString("cityId"));
//                        obj.put("districtId",group.getString("districtId"));
//                        obj.put("groupId",group.getString("groupId"));
//                        obj.put("groupName",group.getString("groupName"));
//                        obj.put("startCountsUp",group.getDoubleValue("startCountsUp"));
//                        obj.put("startCounts",group.getIntValue("startCounts"));
//                        obj.put("equipmentCounts",group.getIntValue("equipmentCounts"));
//                        obj.put("equipmentCountsUp",group.getDoubleValue("equipmentCountsUp"));
//                        obj.put("address",group.getString("address"));
//                        data.put(idProcess(group.getString("merchantId"))+"-"+idProcess(group.getString("groupId")),obj);
//                    }
//                }
//            }
//        }
//        if(orderResult != null && orderResult.getInteger("code") == 200 && orderResult.getJSONObject("info") != null && orderResult.getJSONObject("info").getJSONArray("list") != null){
//            JSONArray orderList = orderResult.getJSONObject("info").getJSONArray("list");
//            if(orderList != null){
//                for(int i=0; i<orderList.size(); i++){
//                    JSONObject order = orderList.getJSONObject(i);
//                    JSONObject obj = data.get(idProcess(order.getString("merchantId"))+"-"+idProcess(order.getString("groupId")));
//                    if(obj == null){
//                        obj = new JSONObject();
//                        obj.put("merchantId",order.getString("merchantId"));
//                        obj.put("provinceId",order.getString("provinceId"));
//                        obj.put("cityId",order.getString("cityId"));
//                        obj.put("districtId",order.getString("districtId"));
//                        obj.put("groupId",order.getString("groupId"));
//                        obj.put("groupName",order.getString("groupName"));
//                        obj.put("startCountsUp",0);
//                        obj.put("startCounts",0);
//                        obj.put("equipmentCounts",0);
//                        obj.put("equipmentCountsUp",0);
//                        obj.put("address",order.getString("address"));
//                        obj.put("memberCountUp",0);
//                        obj.put("memberCount",0);
//                        obj.put("lastCycleMemberCount",0);
//                        obj.put("payAmount",order.getDoubleValue("onlinePayAmount"));
//                        obj.put("payAmountUp",order.getDoubleValue("onlinePayAmountUp"));
//                        obj.put("payCount",order.getDoubleValue("onlinePayAmountUp"));
//                        obj.put("payCountUp",order.getDoubleValue("onlinePayCountsUp"));
//                        obj.put("perOrderAmount",order.getDoubleValue("perOnlineOrderAmount"));
//                        obj.put("perOrderAmountUp",order.getDoubleValue("perOnlineOrderAmountUp"));
//                        obj.put("perUserAmount",0);
//                        obj.put("perUserAmountUp",0);
//                        data.put(idProcess(order.getString("merchantId"))+"-"+idProcess(order.getString("groupId")),obj);
//                    }else{
//                        obj.put("payAmount",order.getDoubleValue("onlinePayAmount"));
//                        obj.put("payAmountUp",order.getDoubleValue("onlinePayAmountUp"));
//                        obj.put("payCount",order.getDoubleValue("onlinePayAmountUp"));
//                        obj.put("payCountUp",order.getDoubleValue("onlinePayCountsUp"));
//                        obj.put("perOrderAmount",order.getDoubleValue("perOnlineOrderAmount"));
//                        obj.put("perOrderAmountUp",order.getDoubleValue("perOnlineOrderAmountUp"));
//                        if(obj.getIntValue("memberCount") >0){
//                            obj.put("perUserAmount",decimalFormat.format(order.getDoubleValue("onlinePayAmount")/obj.getIntValue("memberCount")));
//                            if(obj.getIntValue("lastCycleMemberCount") == 0){
//                                if(order.getDoubleValue("onlinePayAmount") > 0){
//                                    obj.put("perUserAmountUp",1);
//                                }else{
//                                    obj.put("perUserAmountUp",0);
//                                }
//                            }else{
//                                if(order.getDoubleValue("lastCycleOnlinePayAmount") > 0){
//                                    obj.put("perUserAmountUp",decimalFormat.format((order.getDoubleValue("onlinePayAmount")/obj.getIntValue("memberCount")-order.getDoubleValue("lastCycleOnlinePayAmount")/obj.getIntValue("lastCycleMemberCount"))/order.getDoubleValue("lastCycleOnlinePayAmount")/obj.getIntValue("lastCycleMemberCount")));
//                                }else{
//                                    if(order.getDoubleValue("onlinePayAmount") > 0){
//                                        obj.put("perUserAmountUp",1);
//                                    }else{
//                                        obj.put("perUserAmountUp",0);
//                                    }
//                                }
//                            }
//                        }else{
//                            if(obj.getIntValue("lastCycleMemberCount") == 0 ||  order.getDoubleValue("lastCycleOnlinePayAmount") == 0){
//                                obj.put("perUserAmount",0);
//                            }else{
//                                obj.put("perUserAmount",-1);
//                            }
//
//                        }
//                    }
//
//                    data.put(idProcess(order.getString("merchantId"))+"-"+idProcess(order.getString("groupId")),obj);
//                }
//            }
//        }
//
//        if(data != null){
//            Set<String> keys = data.keySet();
//            //倒序
//            Map<Double,List<JSONObject>> treeMap = new TreeMap<>(Comparator.reverseOrder());
//            //正序
//            if("asc".equals(monthParamDto.getOrder())){
//                treeMap = new TreeMap<>();
//            }
//            for(String key : keys){
//                //如何排序,目前只做了按金钱排序
//                JSONObject obj = data.get(key);
//                if(obj.getString("merchantId") == null || "未知".equals(obj.getString("merchantId"))){
//                    continue;
//                }
//                List<JSONObject> list = treeMap.get(obj.getDoubleValue("payAmount"));
//                if(list == null){
//                    list = new ArrayList<JSONObject>();
//                }
//                list.add(obj);
//                treeMap.put(obj.getDoubleValue("payAmount"),list);
//            }
//
//            //开始按照顺序重新拼装list
//            Set<Double> tmKeys = treeMap.keySet();
//            for(Double key : tmKeys){
//                orders.addAll(treeMap.get(key));
//            }
//
//        }
//
//        orderResult.getJSONObject("info").remove("list");
//        orderResult.getJSONObject("info").put("list",orders);
//        return orderResult;
//    }
//
//    @Override
//    public JSONObject getYearGroupData(YearParamDto yearParamDto) {
//
//        ParamDto params = new ParamDto();
//        params.setCode(yearOrderDayAnalyseCode);
//        params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//        if(yearParamDto.getEquipmentTypeId() != null && yearParamDto.getEquipmentTypeId()>0){
//            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地+设备类型维度");
//        }
//        params.setColumnArray(QuerySql.YearOrderQuerySql);
//        JSONObject orderResult = getData(params, yearParamDto);
//
//        params.setCode(yearGroupEquipmentAnalyseCode);
//        params.setColumnArray(QuerySql.GroupEquipmentSql);
//        JSONObject groupResult = getData(params, yearParamDto);
//
//        //要把排序给排除，会员没有payAmount字段拍讯
//        String orderBy = yearParamDto.getOrderBy();
//        yearParamDto.setOrderBy(null);
//
//        params.setCode(yearMemberDataCode);
//        params.setColumnArray(QuerySql.YearMemberSql);
//        JSONObject memberResult = getData(params, yearParamDto);
//
//        DecimalFormat decimalFormat = new DecimalFormat("0.##");
//
//        if(orderResult == null && groupResult == null){
//            return null;
//        }
//
//        Map<String,JSONObject> data = new HashMap<>();
//        List<JSONObject> orders = new ArrayList<>();
//
//        if(memberResult != null && memberResult.getInteger("code") == 200 && memberResult.getJSONObject("info") != null && memberResult.getJSONObject("info").getJSONArray("list") != null){
//            JSONArray memberList = memberResult.getJSONObject("info").getJSONArray("list");
//            if(memberList != null){
//                for(int i=0; i<memberList.size(); i++){
//                    JSONObject member = memberList.getJSONObject(i);
//                    JSONObject obj = new JSONObject();
//                    obj.put("merchantId",member.getString("merchantId"));
//                    obj.put("groupId",member.getString("groupId"));
//                    obj.put("memberCountUp",member.getDoubleValue("memberCountUp"));
//                    obj.put("memberCount",member.getIntValue("memberCount"));
//                    obj.put("lastCycleMemberCount",member.getIntValue("lastCycleMemberCount"));
//                    obj.put("provinceId",null);
//                    obj.put("cityId",null);
//                    obj.put("districtId",null);
//                    obj.put("startCountsUp",0);
//                    obj.put("startCounts",0);
//                    obj.put("equipmentCounts",0);
//                    obj.put("equipmentCountsUp",0);
//                    obj.put("address",null);
//                    obj.put("payAmount",0);
//                    obj.put("payAmountUp",0);
//                    obj.put("payCount",0);
//                    obj.put("payCountUp",0);
//                    obj.put("perOrderAmount",0);
//                    obj.put("perOrderAmountUp",0);
//                    obj.put("perUserAmount",0);
//                    obj.put("perUserAmountUp",0);
//                    data.put(idProcess(obj.getString("merchantId"))+"-"+idProcess(obj.getString("groupId")),obj);
//
//                }
//            }
//        }
//
//        if(groupResult != null && groupResult.getInteger("code") == 200 && groupResult.getJSONObject("info") != null && groupResult.getJSONObject("info").getJSONArray("list") != null){
//            JSONArray groupList = groupResult.getJSONObject("info").getJSONArray("list");
//            if(groupList != null){
//                for(int i=0; i<groupList.size(); i++){
//                    JSONObject group = groupList.getJSONObject(i);
//                    if(data.get(idProcess(group.getString("merchantId"))+"-"+idProcess(group.getString("groupId"))) == null){
//                        JSONObject obj = new JSONObject();
//                        obj.put("merchantId",group.getString("merchantId"));
//                        obj.put("provinceId",group.getString("provinceId"));
//                        obj.put("cityId",group.getString("cityId"));
//                        obj.put("districtId",group.getString("districtId"));
//                        obj.put("groupId",group.getString("groupId"));
//                        obj.put("groupName",group.getString("groupName"));
//                        obj.put("startCountsUp",group.getDoubleValue("startCountsUp"));
//                        obj.put("startCounts",group.getIntValue("startCounts"));
//                        obj.put("equipmentCounts",group.getIntValue("equipmentCounts"));
//                        obj.put("equipmentCountsUp",group.getDoubleValue("equipmentCountsUp"));
//                        obj.put("address",group.getString("address"));
//                        obj.put("memberCountUp",0);
//                        obj.put("memberCount",0);
//                        obj.put("lastCycleMemberCount",0);
//                        obj.put("payAmount",0);
//                        obj.put("payAmountUp",0);
//                        obj.put("payCount",0);
//                        obj.put("payCountUp",0);
//                        obj.put("perOrderAmount",0);
//                        obj.put("perOrderAmountUp",0);
//                        obj.put("perUserAmount",0);
//                        obj.put("perUserAmountUp",0);
//                        data.put(idProcess(group.getString("merchantId"))+"-"+idProcess(group.getString("groupId")),obj);
//                    }else{
//                        JSONObject obj = data.get(idProcess(group.getString("merchantId"))+"-"+idProcess(group.getString("groupId")));
//                        obj.put("merchantId",group.getString("merchantId"));
//                        obj.put("provinceId",group.getString("provinceId"));
//                        obj.put("cityId",group.getString("cityId"));
//                        obj.put("districtId",group.getString("districtId"));
//                        obj.put("groupId",group.getString("groupId"));
//                        obj.put("groupName",group.getString("groupName"));
//                        obj.put("startCountsUp",group.getDoubleValue("startCountsUp"));
//                        obj.put("startCounts",group.getIntValue("startCounts"));
//                        obj.put("equipmentCounts",group.getIntValue("equipmentCounts"));
//                        obj.put("equipmentCountsUp",group.getDoubleValue("equipmentCountsUp"));
//                        obj.put("address",group.getString("address"));
//                        data.put(idProcess(group.getString("merchantId"))+"-"+idProcess(group.getString("groupId")),obj);
//                    }
//                }
//            }
//        }
//        if(orderResult != null && orderResult.getInteger("code") == 200 && orderResult.getJSONObject("info") != null && orderResult.getJSONObject("info").getJSONArray("list") != null){
//            JSONArray orderList = orderResult.getJSONObject("info").getJSONArray("list");
//            if(orderList != null){
//                for(int i=0; i<orderList.size(); i++){
//                    JSONObject order = orderList.getJSONObject(i);
//                    JSONObject obj = data.get(idProcess(order.getString("merchantId"))+"-"+idProcess(order.getString("groupId")));
//                    if(obj == null){
//                        obj = new JSONObject();
//                        obj.put("merchantId",order.getString("merchantId"));
//                        obj.put("provinceId",order.getString("provinceId"));
//                        obj.put("cityId",order.getString("cityId"));
//                        obj.put("districtId",order.getString("districtId"));
//                        obj.put("groupId",order.getString("groupId"));
//                        obj.put("groupName",order.getString("groupName"));
//                        obj.put("startCountsUp",0);
//                        obj.put("startCounts",0);
//                        obj.put("equipmentCounts",0);
//                        obj.put("equipmentCountsUp",0);
//                        obj.put("address",order.getString("address"));
//                        obj.put("memberCountUp",0);
//                        obj.put("memberCount",0);
//                        obj.put("lastCycleMemberCount",0);
//                        obj.put("payAmount",order.getDoubleValue("onlinePayAmount"));
//                        obj.put("payAmountUp",order.getDoubleValue("onlinePayAmountUp"));
//                        obj.put("payCount",order.getDoubleValue("onlinePayAmountUp"));
//                        obj.put("payCountUp",order.getDoubleValue("onlinePayCountsUp"));
//                        obj.put("perOrderAmount",order.getDoubleValue("perOnlineOrderAmount"));
//                        obj.put("perOrderAmountUp",order.getDoubleValue("perOnlineOrderAmountUp"));
//                        obj.put("perUserAmount",0);
//                        obj.put("perUserAmountUp",0);
//                        data.put(idProcess(order.getString("merchantId"))+"-"+idProcess(order.getString("groupId")),obj);
//                    }else{
//                        obj.put("payAmount",order.getDoubleValue("onlinePayAmount"));
//                        obj.put("payAmountUp",order.getDoubleValue("onlinePayAmountUp"));
//                        obj.put("payCount",order.getDoubleValue("onlinePayAmountUp"));
//                        obj.put("payCountUp",order.getDoubleValue("onlinePayCountsUp"));
//                        obj.put("perOrderAmount",order.getDoubleValue("perOnlineOrderAmount"));
//                        obj.put("perOrderAmountUp",order.getDoubleValue("perOnlineOrderAmountUp"));
//                        if(obj.getIntValue("memberCount") >0){
//                            obj.put("perUserAmount",decimalFormat.format(order.getDoubleValue("onlinePayAmount")/obj.getIntValue("memberCount")));
//                            if(obj.getIntValue("lastCycleMemberCount") == 0){
//                                if(order.getDoubleValue("onlinePayAmount") > 0){
//                                    obj.put("perUserAmountUp",1);
//                                }else{
//                                    obj.put("perUserAmountUp",0);
//                                }
//                            }else{
//                                if(order.getDoubleValue("lastCycleOnlinePayAmount") > 0){
//                                    obj.put("perUserAmountUp",decimalFormat.format((order.getDoubleValue("onlinePayAmount")/obj.getIntValue("memberCount")-order.getDoubleValue("lastCycleOnlinePayAmount")/obj.getIntValue("lastCycleMemberCount"))/order.getDoubleValue("lastCycleOnlinePayAmount")/obj.getIntValue("lastCycleMemberCount")));
//                                }else{
//                                    if(order.getDoubleValue("onlinePayAmount") > 0){
//                                        obj.put("perUserAmountUp",1);
//                                    }else{
//                                        obj.put("perUserAmountUp",0);
//                                    }
//                                }
//                            }
//                        }else{
//                            if(obj.getIntValue("lastCycleMemberCount") == 0 ||  order.getDoubleValue("lastCycleOnlinePayAmount") == 0){
//                                obj.put("perUserAmount",0);
//                            }else{
//                                obj.put("perUserAmount",-1);
//                            }
//
//                        }
//                    }
//
//                    data.put(idProcess(order.getString("merchantId"))+"-"+idProcess(order.getString("groupId")),obj);
//                }
//            }
//        }
//
//        if(data != null){
//            Set<String> keys = data.keySet();
//            //倒序
//            Map<Double,List<JSONObject>> treeMap = new TreeMap<>(Comparator.reverseOrder());
//            //正序
//            if("asc".equals(yearParamDto.getOrder())){
//                treeMap = new TreeMap<>();
//            }
//            for(String key : keys){
//                //如何排序,目前只做了按金钱排序
//                JSONObject obj = data.get(key);
//                if(obj.getString("merchantId") == null || "未知".equals(obj.getString("merchantId"))){
//                    continue;
//                }
//                List<JSONObject> list = treeMap.get(obj.getDoubleValue("payAmount"));
//                if(list == null){
//                    list = new ArrayList<JSONObject>();
//                }
//                list.add(obj);
//                treeMap.put(obj.getDoubleValue("payAmount"),list);
//            }
//
//            //开始按照顺序重新拼装list
//            Set<Double> tmKeys = treeMap.keySet();
//            for(Double key : tmKeys){
//                orders.addAll(treeMap.get(key));
//            }
//
//        }
//
//        orderResult.getJSONObject("info").remove("list");
//        orderResult.getJSONObject("info").put("list",orders);
//        return orderResult;
//    }
//
//    @Override
//    public JSONObject getDayEquipmentData(DayParamDto dayParamDto) {
//
//        ParamDto params = new ParamDto();
//        params.setCode(dayEquipmentAnalyseCode);
//        //params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//        params.setColumnArray(QuerySql.EquipmentSql);
//        JSONObject orderResult = getData(params, dayParamDto);
//
//        return orderResult;
//    }
//
//    @Override
//    public JSONObject getWeekEquipmentData(WeekParamDto weekParamDto) {
//        ParamDto params = new ParamDto();
//        params.setCode(weekEquipmentAnalyseCode);
//        //params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//        params.setColumnArray(QuerySql.EquipmentSql);
//        JSONObject orderResult = getData(params, weekParamDto);
//
//        return orderResult;
//    }
//
//    @Override
//    public JSONObject getMonthEquipmentData(MonthParamDto monthParamDto) {
//        ParamDto params = new ParamDto();
//        params.setCode(monthEquipmentAnalyseCode);
//        //params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//        params.setColumnArray(QuerySql.EquipmentSql);
//        JSONObject orderResult = getData(params, monthParamDto);
//
//        return orderResult;
//    }
//
//    @Override
//    public JSONObject getYearEquipmentData(YearParamDto yearParamDto) {
//        ParamDto params = new ParamDto();
//        params.setCode(yearEquipmentAnalyseCode);
//        //params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//        params.setColumnArray(QuerySql.EquipmentSql);
//        JSONObject orderResult = getData(params, yearParamDto);
//
//        return orderResult;
//    }
//
//    public JSONObject getDaySumEquipmentData(DayParamDto dayParamDto){
//        ParamDto params = new ParamDto();
//        params.setCode(dayEquipmentAnalyseCode);
//        //params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//        params.setColumnArray(QuerySql.EquipmentCountSql);
//        JSONObject orderResult = getData(params, dayParamDto);
//
//        return orderResult;
//    }
//
//    public JSONObject getWeekSumEquipmentData(WeekParamDto weekParamDto){
//        ParamDto params = new ParamDto();
//        params.setCode(weekEquipmentAnalyseCode);
//        //params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//        params.setColumnArray(QuerySql.EquipmentCountSql);
//        JSONObject orderResult = getData(params, weekParamDto);
//
//        return orderResult;
//    }
//
//    public JSONObject getMonthSumEquipmentData(MonthParamDto monthParamDto){
//        ParamDto params = new ParamDto();
//        params.setCode(monthEquipmentAnalyseCode);
//        //params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//        params.setColumnArray(QuerySql.EquipmentCountSql);
//        JSONObject orderResult = getData(params, monthParamDto);
//
//        return orderResult;
//    }
//
//    public JSONObject getYearSumEquipmentData(YearParamDto yearParamDto){
//        ParamDto params = new ParamDto();
//        params.setCode(yearEquipmentAnalyseCode);
//        //params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//        params.setColumnArray(QuerySql.EquipmentCountSql);
//        JSONObject orderResult = getData(params, yearParamDto);
//
//        return orderResult;
//    }
//
//    @Override
//    public JSONObject getDaySumGroupData(DayParamDto dayParamDto) {
//
//
//        ParamDto params = new ParamDto();
//        params.setCode(dayOrderDayAnalyseCode);
//        params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//        if(dayParamDto.getEquipmentTypeId() != null && dayParamDto.getEquipmentTypeId()>0){
//            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地+设备类型维度");
//        }
//        params.setColumnArray(QuerySql.OrderCountSql);
//        JSONObject orderResult = getData(params, dayParamDto);
//
//        params.setCode(dayGroupEquipmentAnalyseCode);
//        params.setColumnArray(QuerySql.OrderGroupCountSql);
//        JSONObject groupResult = getData(params, dayParamDto);
//
//        if(orderResult == null && groupResult == null){
//            return null;
//        }
//        List<JSONObject> orders = new ArrayList<>();
//        JSONObject obj = new JSONObject();
//        JSONObject rel = new JSONObject();
//
//        if(orderResult != null && orderResult.getInteger("code") == 200 && orderResult.getJSONObject("info") != null && orderResult.getJSONObject("info").getJSONArray("list") != null){
//            JSONArray orderList = orderResult.getJSONObject("info").getJSONArray("list");
//            if(orderList != null && orderList.size()>0){
//                obj = orderList.getJSONObject(0);
//            }
//        }
//
//        if(groupResult != null && groupResult.getInteger("code") == 200 && groupResult.getJSONObject("info") != null && groupResult.getJSONObject("info").getJSONArray("list") != null){
//            JSONArray groupList = groupResult.getJSONObject("info").getJSONArray("list");
//            if(groupList != null && groupList.size()>0){
//                DecimalFormat df = new DecimalFormat("#0.00");
//                Integer groups = groupList.getJSONObject(0).getInteger("sumGroup");
//                if(groups == null || groups == 0){
//                    rel.put("sumPerPayAmount",obj.getDoubleValue("sumPayAmount"));
//                    rel.put("sumPerOrderCounts",obj.getIntValue("sumPayCount"));
//                    rel.put("sumPerOrderAmount",obj.getDoubleValue("sumPerOrderAmount"));
//                    rel.put("sumPerGroupStartCounts",groupList.getJSONObject(0).getDoubleValue("sumPerGroupStartCounts"));
//                }else{
//                    rel.put("sumPerPayAmount",df.format(obj.getDoubleValue("sumPayAmount")/groupList.getJSONObject(0).getDoubleValue("sumGroup")));
//                    rel.put("sumPerOrderCounts",df.format(obj.getIntValue("sumPayCount")/groupList.getJSONObject(0).getDoubleValue("sumGroup")));
//                    rel.put("sumPerOrderAmount",obj.getDoubleValue("sumPerOrderAmount"));
//                    rel.put("sumPerGroupStartCounts",groupList.getJSONObject(0).getDoubleValue("sumPerGroupStartCounts"));
//                }
//                orders.add(rel);
//            }
//        }
//
//        orderResult.getJSONObject("info").put("totalPage",1);
//        orderResult.getJSONObject("info").put("totalCount",1);
//        orderResult.getJSONObject("info").remove("list");
//        orderResult.getJSONObject("info").put("list",orders);
//        return orderResult;
//    }
//
//    @Override
//    public JSONObject getWeekSumGroupData(WeekParamDto weekParamDto) {
//
//
//        ParamDto params = new ParamDto();
//        params.setCode(weekOrderDayAnalyseCode);
//        params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//        if(weekParamDto.getEquipmentTypeId() != null && weekParamDto.getEquipmentTypeId()>0){
//            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地+设备类型维度");
//        }
//        params.setColumnArray(QuerySql.OrderCountSql);
//        JSONObject orderResult = getData(params, weekParamDto);
//
//        params.setCode(weekGroupEquipmentAnalyseCode);
//        params.setColumnArray(QuerySql.OrderGroupCountSql);
//        JSONObject groupResult = getData(params, weekParamDto);
//
//        if(orderResult == null && groupResult == null){
//            return null;
//        }
//        List<JSONObject> orders = new ArrayList<>();
//
//        JSONObject obj = new JSONObject();
//        JSONObject rel = new JSONObject();
//
//        if(orderResult != null && orderResult.getInteger("code") == 200 && orderResult.getJSONObject("info") != null && orderResult.getJSONObject("info").getJSONArray("list") != null){
//            JSONArray orderList = orderResult.getJSONObject("info").getJSONArray("list");
//            if(orderList != null && orderList.size()>0){
//                obj = orderList.getJSONObject(0);
//            }
//        }
//
//        if(groupResult != null && groupResult.getInteger("code") == 200 && groupResult.getJSONObject("info") != null && groupResult.getJSONObject("info").getJSONArray("list") != null){
//            JSONArray groupList = groupResult.getJSONObject("info").getJSONArray("list");
//            if(groupList != null && groupList.size()>0){
//                DecimalFormat df = new DecimalFormat("#0.00");
//                Integer groups = groupList.getJSONObject(0).getInteger("sumGroup");
//                if(groups == null || groups == 0){
//                    rel.put("sumPerPayAmount",obj.getDoubleValue("sumPayAmount"));
//                    rel.put("sumPerOrderCounts",obj.getIntValue("sumPayCount"));
//                    rel.put("sumPerOrderAmount",obj.getDoubleValue("sumPerOrderAmount"));
//                    rel.put("sumPerGroupStartCounts",groupList.getJSONObject(0).getDoubleValue("sumPerGroupStartCounts"));
//                }else{
//                    rel.put("sumPerPayAmount",df.format(obj.getDoubleValue("sumPayAmount")/groupList.getJSONObject(0).getDoubleValue("sumGroup")));
//                    rel.put("sumPerOrderCounts",df.format(obj.getIntValue("sumPayCount")/groupList.getJSONObject(0).getDoubleValue("sumGroup")));
//                    rel.put("sumPerOrderAmount",obj.getDoubleValue("sumPerOrderAmount"));
//                    rel.put("sumPerGroupStartCounts",groupList.getJSONObject(0).getDoubleValue("sumPerGroupStartCounts"));
//                }
//                orders.add(rel);
//            }
//        }
//        orderResult.getJSONObject("info").put("totalPage",1);
//        orderResult.getJSONObject("info").put("totalCount",1);
//        orderResult.getJSONObject("info").remove("list");
//        orderResult.getJSONObject("info").put("list",orders);
//        return orderResult;
//    }
//
//    @Override
//    public JSONObject getMonthSumGroupData(MonthParamDto monthParamDto) {
//
//
//        ParamDto params = new ParamDto();
//        params.setCode(monthOrderDayAnalyseCode);
//        params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//        if(monthParamDto.getEquipmentTypeId() != null && monthParamDto.getEquipmentTypeId()>0){
//            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地+设备类型维度");
//        }
//        params.setColumnArray(QuerySql.OrderCountSql);
//        JSONObject orderResult = getData(params, monthParamDto);
//
//        params.setCode(monthGroupEquipmentAnalyseCode);
//        params.setColumnArray(QuerySql.OrderGroupCountSql);
//        JSONObject groupResult = getData(params, monthParamDto);
//
//        if(orderResult == null && groupResult == null){
//            return null;
//        }
//        List<JSONObject> orders = new ArrayList<>();
//
//        JSONObject obj = new JSONObject();
//        JSONObject rel = new JSONObject();
//
//        if(orderResult != null && orderResult.getInteger("code") == 200 && orderResult.getJSONObject("info") != null && orderResult.getJSONObject("info").getJSONArray("list") != null){
//            JSONArray orderList = orderResult.getJSONObject("info").getJSONArray("list");
//            if(orderList != null && orderList.size()>0){
//                obj = orderList.getJSONObject(0);
//            }
//        }
//
//        if(groupResult != null && groupResult.getInteger("code") == 200 && groupResult.getJSONObject("info") != null && groupResult.getJSONObject("info").getJSONArray("list") != null){
//            JSONArray groupList = groupResult.getJSONObject("info").getJSONArray("list");
//            if(groupList != null && groupList.size()>0){
//                DecimalFormat df = new DecimalFormat("#0.00");
//                Integer groups = groupList.getJSONObject(0).getInteger("sumGroup");
//                if(groups == null || groups == 0){
//                    rel.put("sumPerPayAmount",obj.getDoubleValue("sumPayAmount"));
//                    rel.put("sumPerOrderCounts",obj.getIntValue("sumPayCount"));
//                    rel.put("sumPerOrderAmount",obj.getDoubleValue("sumPerOrderAmount"));
//                    rel.put("sumPerGroupStartCounts",groupList.getJSONObject(0).getDoubleValue("sumPerGroupStartCounts"));
//                }else{
//                    rel.put("sumPerPayAmount",df.format(obj.getDoubleValue("sumPayAmount")/groupList.getJSONObject(0).getDoubleValue("sumGroup")));
//                    rel.put("sumPerOrderCounts",df.format(obj.getIntValue("sumPayCount")/groupList.getJSONObject(0).getDoubleValue("sumGroup")));
//                    rel.put("sumPerOrderAmount",obj.getDoubleValue("sumPerOrderAmount"));
//                    rel.put("sumPerGroupStartCounts",groupList.getJSONObject(0).getDoubleValue("sumPerGroupStartCounts"));
//                }
//                orders.add(rel);
//            }
//        }
//        orderResult.getJSONObject("info").put("totalPage",1);
//        orderResult.getJSONObject("info").put("totalCount",1);
//        orderResult.getJSONObject("info").remove("list");
//        orderResult.getJSONObject("info").put("list",orders);
//        return orderResult;
//    }
//
//    @Override
//    public JSONObject getYearSumGroupData(YearParamDto yearParamDto) {
//
//
//        ParamDto params = new ParamDto();
//        params.setCode(yearOrderDayAnalyseCode);
//        params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//        if(yearParamDto.getEquipmentTypeId() != null && yearParamDto.getEquipmentTypeId()>0){
//            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地+设备类型维度");
//        }
//        params.setColumnArray(QuerySql.OrderCountSql);
//        JSONObject orderResult = getData(params, yearParamDto);
//
//        params.setCode(yearGroupEquipmentAnalyseCode);
//        params.setColumnArray(QuerySql.OrderGroupCountSql);
//        JSONObject groupResult = getData(params, yearParamDto);
//
//        if(orderResult == null && groupResult == null){
//            return null;
//        }
//        List<JSONObject> orders = new ArrayList<>();
//
//        JSONObject obj = new JSONObject();
//        JSONObject rel = new JSONObject();
//
//        if(orderResult != null && orderResult.getInteger("code") == 200 && orderResult.getJSONObject("info") != null && orderResult.getJSONObject("info").getJSONArray("list") != null){
//            JSONArray orderList = orderResult.getJSONObject("info").getJSONArray("list");
//            if(orderList != null && orderList.size()>0){
//                obj = orderList.getJSONObject(0);
//            }
//        }
//
//        if(groupResult != null && groupResult.getInteger("code") == 200 && groupResult.getJSONObject("info") != null && groupResult.getJSONObject("info").getJSONArray("list") != null){
//            JSONArray groupList = groupResult.getJSONObject("info").getJSONArray("list");
//            if(groupList != null && groupList.size()>0){
//                DecimalFormat df = new DecimalFormat("#0.00");
//                Integer groups = groupList.getJSONObject(0).getInteger("sumGroup");
//                if(groups == null || groups == 0){
//                    rel.put("sumPerPayAmount",obj.getDoubleValue("sumPayAmount"));
//                    rel.put("sumPerOrderCounts",obj.getIntValue("sumPayCount"));
//                    rel.put("sumPerOrderAmount",obj.getDoubleValue("sumPerOrderAmount"));
//                    rel.put("sumPerGroupStartCounts",groupList.getJSONObject(0).getDoubleValue("sumPerGroupStartCounts"));
//                }else{
//                    rel.put("sumPerPayAmount",df.format(obj.getDoubleValue("sumPayAmount")/groupList.getJSONObject(0).getDoubleValue("sumGroup")));
//                    rel.put("sumPerOrderCounts",df.format(obj.getIntValue("sumPayCount")/groupList.getJSONObject(0).getDoubleValue("sumGroup")));
//                    rel.put("sumPerOrderAmount",obj.getDoubleValue("sumPerOrderAmount"));
//                    rel.put("sumPerGroupStartCounts",groupList.getJSONObject(0).getDoubleValue("sumPerGroupStartCounts"));
//                }
//                orders.add(rel);
//            }
//        }
//        orderResult.getJSONObject("info").put("totalPage",1);
//        orderResult.getJSONObject("info").put("totalCount",1);
//        orderResult.getJSONObject("info").remove("list");
//        orderResult.getJSONObject("info").put("list",orders);
//        return orderResult;
//    }
//
//    @Override
//    public JSONObject
//    getDayEquipmentTypeData(DayParamDto dayParamDto) {
//
//        ParamDto params = new ParamDto();
//        params.setCode(dayOrderDayAnalyseCode);
//        //要把排序给排除
//        String orderBy = dayParamDto.getOrderBy();
//        dayParamDto.setOrderBy(null);
//        //维度加上设备类型
//        String comboType = dayParamDto.getComboType();
//        if(dayParamDto.getEquipmentTypeId() == null){
//            dayParamDto.setEquipmentTypeId(9999999);
//            comboType = dayParamDto.getComboType();
//            dayParamDto.setEquipmentTypeId(null);
//        }
//
//        params.getQueryParams().setCombo_type(comboType);
//        params.setColumnArray(QuerySql.DayEquipmentTypeListSql);
//        JSONObject orderResult = getData(params, dayParamDto);
//
//        params.setCode(dayMemberDataCode);
//        params.setColumnArray(QuerySql.DayMemberListSql);
//        JSONObject memberResult = getData(params, dayParamDto);
//
//        params.setCode(dayGroupEquipmentAnalyseCode);
//        params.setColumnArray(QuerySql.DayGroupListSql);
//        JSONObject groupResult = getData(params, dayParamDto);
//
//        if(orderResult == null && memberResult == null && groupResult == null){
//            return null;
//        }
//
//        Map<String,JSONObject> data = new HashMap<>();
//
//        List<JSONObject> orders = new ArrayList<>();
//
//        if(groupResult != null && groupResult.getInteger("code") == 200 && groupResult.getJSONObject("info") != null && groupResult.getJSONObject("info").getJSONArray("list") != null){
//            JSONArray groupList = groupResult.getJSONObject("info").getJSONArray("list");
//            if(groupList != null && groupList.size()>0){
//                for(int i=0; i<groupList.size(); i++){
//                    JSONObject obj = groupList.getJSONObject(i);
//                    obj.put("memberCount","0");
//                    obj.put("memberCountUp","0");
//                    obj.put("payAmount","0");
//                    obj.put("payAmountUp","0");
//                    obj.put("payCount","0");
//                    obj.put("payCountUp","0");
//                    obj.put("perOrderAmount","0");
//                    obj.put("perOrderAmountUp","0");
//                    data.put(idProcess(obj.getString("provinceId"))+idProcess(obj.getString("cityId"))+idProcess(obj.getString("districtId"))
//                                    +idProcess(obj.getString("groupId"))+idProcess(obj.getString("equipmentTypeId"))
//                            ,obj);
//                }
//            }
//        }
//
//        if(memberResult != null && memberResult.getInteger("code") == 200 && memberResult.getJSONObject("info") != null && memberResult.getJSONObject("info").getJSONArray("list") != null){
//            JSONArray memberList = memberResult.getJSONObject("info").getJSONArray("list");
//            if(memberList != null && memberList.size()>0){
//                for(int i=0; i<memberList.size(); i++){
//                    JSONObject obj = memberList.getJSONObject(i);
//                    JSONObject mapObj = data.get(idProcess(obj.getString("provinceId"))+idProcess(obj.getString("cityId"))+idProcess(obj.getString("districtId"))
//                            +idProcess(obj.getString("groupId"))+idProcess(obj.getString("equipmentTypeId")));
//                    if(mapObj == null){
//                        obj.put("equipmentCounts","0");
//                        obj.put("equipmentCountsUp","0");
//                        data.put(idProcess(obj.getString("provinceId"))+idProcess(obj.getString("cityId"))+idProcess(obj.getString("districtId"))
//                                        +idProcess(obj.getString("groupId"))+idProcess(obj.getString("equipmentTypeId"))
//                                ,obj);
//                    }else{
//                        mapObj.put("memberCount",obj.getIntValue("memberCount"));
//                        mapObj.put("memberCountUp",obj.getDoubleValue("memberCountUp"));
//                    }
//                }
//            }
//        }
//
//        if(orderResult != null && orderResult.getInteger("code") == 200 && orderResult.getJSONObject("info") != null && orderResult.getJSONObject("info").getJSONArray("list") != null){
//            JSONArray orderList = orderResult.getJSONObject("info").getJSONArray("list");
//            if(orderList != null && orderList.size()>0){
//                for(int i=0; i<orderList.size(); i++){
//                    JSONObject obj = orderList.getJSONObject(i);
//                    JSONObject mapObj = data.get(idProcess(obj.getString("provinceId"))+idProcess(obj.getString("cityId"))+idProcess(obj.getString("districtId"))
//                            +idProcess(obj.getString("groupId"))+idProcess(obj.getString("equipmentTypeId")));
//                    if(mapObj == null){
//                        obj.put("memberCount",0);
//                        obj.put("memberCountUp",0);
//                        obj.put("equipmentCounts",0);
//                        obj.put("equipmentCountsUp",0);
//                        data.put(idProcess(obj.getString("provinceId"))+idProcess(obj.getString("cityId"))+idProcess(obj.getString("districtId"))
//                                +idProcess(obj.getString("groupId"))+idProcess(obj.getString("equipmentTypeId")),obj);
//                    }else{
//                        obj.put("memberCount",mapObj.getIntValue("memberCount"));
//                        obj.put("memberCountUp",mapObj.getDoubleValue("memberCountUp"));
//                        obj.put("equipmentCounts",mapObj.getIntValue("equipmentCounts"));
//                        obj.put("equipmentCountsUp",mapObj.getDoubleValue("equipmentCountsUp"));
//                        data.put(idProcess(obj.getString("provinceId"))+idProcess(obj.getString("cityId"))+idProcess(obj.getString("districtId"))
//                                +idProcess(obj.getString("groupId"))+idProcess(obj.getString("equipmentTypeId")),obj);
//                    }
//                }
//            }
//        }
//
//        if(data != null){
//            Set<String> keys = data.keySet();
//            //倒序
//            Map<Double,List<JSONObject>> treeMap = new TreeMap<>(Comparator.reverseOrder());
//            //正序
//            if("asc".equals(dayParamDto.getOrder())){
//                treeMap = new TreeMap<>();
//            }
//            for(String key : keys){
//                //如何排序,目前只做了按金钱排序
//                JSONObject obj = data.get(key);
//                if(obj.getString("equipmentTypeName") == null || "未知".equals(obj.getString("equipmentTypeName"))){
//                    continue;
//                }
//                List<JSONObject> list = treeMap.get(obj.getDoubleValue("payAmount"));
//                if(list == null){
//                    list = new ArrayList<JSONObject>();
//                }
//                list.add(obj);
//                treeMap.put(obj.getDoubleValue("payAmount"),list);
//            }
//
//            //开始按照顺序重新拼装list
//            Set<Double> tmKeys = treeMap.keySet();
//            for(Double key : tmKeys){
//                orders.addAll(treeMap.get(key));
//            }
//
//        }
//
//        orderResult.getJSONObject("info").remove("list");
//        orderResult.getJSONObject("info").put("list",orders);
//
//        orderResult.getJSONObject("info").put("totalCount",orders.size());
//        return orderResult;
//    }
//
//    @Override
//    public JSONObject getWeekEquipmentTypeData(WeekParamDto weekParamDto) {
//        ParamDto params = new ParamDto();
//        params.setCode(weekOrderDayAnalyseCode);
//        //要把排序给排除
//        String orderBy = weekParamDto.getOrderBy();
//        weekParamDto.setOrderBy(null);
//        //维度加上设备类型
//        String comboType = weekParamDto.getComboType();
//        if(weekParamDto.getEquipmentTypeId() == null){
//            weekParamDto.setEquipmentTypeId(9999999);
//            comboType = weekParamDto.getComboType();
//            weekParamDto.setEquipmentTypeId(null);
//        }
//
//        params.getQueryParams().setCombo_type(comboType);
//        params.setColumnArray(QuerySql.WeekEquipmentTypeListSql);
//        JSONObject orderResult = getData(params, weekParamDto);
//
//        params.setCode(weekMemberDataCode);
//        params.setColumnArray(QuerySql.WeekMemberListSql);
//        JSONObject memberResult = getData(params, weekParamDto);
//
//        params.setCode(weekGroupEquipmentAnalyseCode);
//        params.setColumnArray(QuerySql.WeekGroupListSql);
//        JSONObject groupResult = getData(params, weekParamDto);
//
//        if(orderResult == null && memberResult == null && groupResult == null){
//            return null;
//        }
//
//        Map<String,JSONObject> data = new HashMap<>();
//
//        List<JSONObject> orders = new ArrayList<>();
//
//        if(groupResult != null && groupResult.getInteger("code") == 200 && groupResult.getJSONObject("info") != null && groupResult.getJSONObject("info").getJSONArray("list") != null){
//            JSONArray groupList = groupResult.getJSONObject("info").getJSONArray("list");
//            if(groupList != null && groupList.size()>0){
//                for(int i=0; i<groupList.size(); i++){
//                    JSONObject obj = groupList.getJSONObject(i);
//                    obj.put("memberCount","0");
//                    obj.put("memberCountUp","0");
//                    obj.put("payAmount","0");
//                    obj.put("payAmountUp","0");
//                    obj.put("payCount","0");
//                    obj.put("payCountUp","0");
//                    obj.put("perOrderAmount","0");
//                    obj.put("perOrderAmountUp","0");
//                    data.put(idProcess(obj.getString("provinceId"))+idProcess(obj.getString("cityId"))+idProcess(obj.getString("districtId"))
//                                    +idProcess(obj.getString("groupId"))+idProcess(obj.getString("equipmentTypeId"))
//                            ,obj);
//                }
//            }
//        }
//
//        if(memberResult != null && memberResult.getInteger("code") == 200 && memberResult.getJSONObject("info") != null && memberResult.getJSONObject("info").getJSONArray("list") != null){
//            JSONArray memberList = memberResult.getJSONObject("info").getJSONArray("list");
//            if(memberList != null && memberList.size()>0){
//                for(int i=0; i<memberList.size(); i++){
//                    JSONObject obj = memberList.getJSONObject(i);
//                    JSONObject mapObj = data.get(idProcess(obj.getString("provinceId"))+idProcess(obj.getString("cityId"))+idProcess(obj.getString("districtId"))
//                            +idProcess(obj.getString("groupId"))+idProcess(obj.getString("equipmentTypeId")));
//                    if(mapObj == null){
//                        obj.put("equipmentCounts","0");
//                        obj.put("equipmentCountsUp","0");
//                        data.put(idProcess(obj.getString("provinceId"))+idProcess(obj.getString("cityId"))+idProcess(obj.getString("districtId"))
//                                        +idProcess(obj.getString("groupId"))+idProcess(obj.getString("equipmentTypeId"))
//                                ,obj);
//                    }else{
//                        mapObj.put("memberCount",obj.getIntValue("memberCount"));
//                        mapObj.put("memberCountUp",obj.getDoubleValue("memberCountUp"));
//                    }
//                }
//            }
//        }
//
//        if(orderResult != null && orderResult.getInteger("code") == 200 && orderResult.getJSONObject("info") != null && orderResult.getJSONObject("info").getJSONArray("list") != null){
//            JSONArray orderList = orderResult.getJSONObject("info").getJSONArray("list");
//            if(orderList != null && orderList.size()>0){
//                for(int i=0; i<orderList.size(); i++){
//                    JSONObject obj = orderList.getJSONObject(i);
//                    JSONObject mapObj = data.get(idProcess(obj.getString("provinceId"))+idProcess(obj.getString("cityId"))+idProcess(obj.getString("districtId"))
//                            +idProcess(obj.getString("groupId"))+idProcess(obj.getString("equipmentTypeId")));
//                    if(mapObj == null){
//                        obj.put("memberCount",0);
//                        obj.put("memberCountUp",0);
//                        obj.put("equipmentCounts",0);
//                        obj.put("equipmentCountsUp",0);
//                        data.put(idProcess(obj.getString("provinceId"))+idProcess(obj.getString("cityId"))+idProcess(obj.getString("districtId"))
//                                +idProcess(obj.getString("groupId"))+idProcess(obj.getString("equipmentTypeId")),obj);
//                    }else{
//                        obj.put("memberCount",mapObj.getIntValue("memberCount"));
//                        obj.put("memberCountUp",mapObj.getDoubleValue("memberCountUp"));
//                        obj.put("equipmentCounts",mapObj.getIntValue("equipmentCounts"));
//                        obj.put("equipmentCountsUp",mapObj.getDoubleValue("equipmentCountsUp"));
//                        data.put(idProcess(obj.getString("provinceId"))+idProcess(obj.getString("cityId"))+idProcess(obj.getString("districtId"))
//                                +idProcess(obj.getString("groupId"))+idProcess(obj.getString("equipmentTypeId")),obj);
//                    }
//                }
//            }
//        }
//
//        if(data != null){
//            Set<String> keys = data.keySet();
//            //倒序
//            Map<Double,List<JSONObject>> treeMap = new TreeMap<>(Comparator.reverseOrder());
//            //正序
//            if("asc".equals(weekParamDto.getOrder())){
//                treeMap = new TreeMap<>();
//            }
//            for(String key : keys){
//                //如何排序,目前只做了按金钱排序
//                JSONObject obj = data.get(key);
//                if(obj.getString("equipmentTypeName") == null || "未知".equals(obj.getString("equipmentTypeName"))){
//                    continue;
//                }
//                List<JSONObject> list = treeMap.get(obj.getDoubleValue("payAmount"));
//                if(list == null){
//                    list = new ArrayList<JSONObject>();
//                }
//                list.add(obj);
//                treeMap.put(obj.getDoubleValue("payAmount"),list);
//            }
//
//            //开始按照顺序重新拼装list
//            Set<Double> tmKeys = treeMap.keySet();
//            for(Double key : tmKeys){
//                orders.addAll(treeMap.get(key));
//            }
//
//        }
//
//        orderResult.getJSONObject("info").remove("list");
//        orderResult.getJSONObject("info").put("list",orders);
//
//        orderResult.getJSONObject("info").put("totalCount",orders.size());
//        return orderResult;
//    }
//
//    @Override
//    public JSONObject getMonthEquipmentTypeData(MonthParamDto monthParamDto) {
//        ParamDto params = new ParamDto();
//        params.setCode(monthOrderDayAnalyseCode);
//        //要把排序给排除
//        String orderBy = monthParamDto.getOrderBy();
//        monthParamDto.setOrderBy(null);
//        //维度加上设备类型
//        String comboType = monthParamDto.getComboType();
//        if(monthParamDto.getEquipmentTypeId() == null){
//            monthParamDto.setEquipmentTypeId(9999999);
//            comboType = monthParamDto.getComboType();
//            monthParamDto.setEquipmentTypeId(null);
//        }
//
//        params.getQueryParams().setCombo_type(comboType);
//        params.setColumnArray(QuerySql.MonthEquipmentTypeListSql);
//        JSONObject orderResult = getData(params, monthParamDto);
//
//        params.setCode(monthMemberDataCode);
//        params.setColumnArray(QuerySql.MonthMemberListSql);
//        JSONObject memberResult = getData(params, monthParamDto);
//
//        params.setCode(monthGroupEquipmentAnalyseCode);
//        params.setColumnArray(QuerySql.WeekGroupListSql);
//        JSONObject groupResult = getData(params, monthParamDto);
//
//        if(orderResult == null && memberResult == null && groupResult == null){
//            return null;
//        }
//
//        Map<String,JSONObject> data = new HashMap<>();
//
//        List<JSONObject> orders = new ArrayList<>();
//
//        if(groupResult != null && groupResult.getInteger("code") == 200 && groupResult.getJSONObject("info") != null && groupResult.getJSONObject("info").getJSONArray("list") != null){
//            JSONArray groupList = groupResult.getJSONObject("info").getJSONArray("list");
//            if(groupList != null && groupList.size()>0){
//                for(int i=0; i<groupList.size(); i++){
//                    JSONObject obj = groupList.getJSONObject(i);
//                    obj.put("memberCount","0");
//                    obj.put("memberCountUp","0");
//                    obj.put("payAmount","0");
//                    obj.put("payAmountUp","0");
//                    obj.put("payCount","0");
//                    obj.put("payCountUp","0");
//                    obj.put("perOrderAmount","0");
//                    obj.put("perOrderAmountUp","0");
//                    data.put(idProcess(obj.getString("provinceId"))+idProcess(obj.getString("cityId"))+idProcess(obj.getString("districtId"))
//                                    +idProcess(obj.getString("groupId"))+idProcess(obj.getString("equipmentTypeId"))
//                            ,obj);
//                }
//            }
//        }
//
//        if(memberResult != null && memberResult.getInteger("code") == 200 && memberResult.getJSONObject("info") != null && memberResult.getJSONObject("info").getJSONArray("list") != null){
//            JSONArray memberList = memberResult.getJSONObject("info").getJSONArray("list");
//            if(memberList != null && memberList.size()>0){
//                for(int i=0; i<memberList.size(); i++){
//                    JSONObject obj = memberList.getJSONObject(i);
//                    JSONObject mapObj = data.get(idProcess(obj.getString("provinceId"))+idProcess(obj.getString("cityId"))+idProcess(obj.getString("districtId"))
//                            +idProcess(obj.getString("groupId"))+idProcess(obj.getString("equipmentTypeId")));
//                    if(mapObj == null){
//                        obj.put("equipmentCounts","0");
//                        obj.put("equipmentCountsUp","0");
//                        data.put(idProcess(obj.getString("provinceId"))+idProcess(obj.getString("cityId"))+idProcess(obj.getString("districtId"))
//                                        +idProcess(obj.getString("groupId"))+idProcess(obj.getString("equipmentTypeId"))
//                                ,obj);
//                    }else{
//                        mapObj.put("memberCount",obj.getIntValue("memberCount"));
//                        mapObj.put("memberCountUp",obj.getDoubleValue("memberCountUp"));
//                    }
//                }
//            }
//        }
//
//        if(orderResult != null && orderResult.getInteger("code") == 200 && orderResult.getJSONObject("info") != null && orderResult.getJSONObject("info").getJSONArray("list") != null){
//            JSONArray orderList = orderResult.getJSONObject("info").getJSONArray("list");
//            if(orderList != null && orderList.size()>0){
//                for(int i=0; i<orderList.size(); i++){
//                    JSONObject obj = orderList.getJSONObject(i);
//                    JSONObject mapObj = data.get(idProcess(obj.getString("provinceId"))+idProcess(obj.getString("cityId"))+idProcess(obj.getString("districtId"))
//                            +idProcess(obj.getString("groupId"))+idProcess(obj.getString("equipmentTypeId")));
//                    if(mapObj == null){
//                        obj.put("memberCount",0);
//                        obj.put("memberCountUp",0);
//                        obj.put("equipmentCounts",0);
//                        obj.put("equipmentCountsUp",0);
//                        data.put(idProcess(obj.getString("provinceId"))+idProcess(obj.getString("cityId"))+idProcess(obj.getString("districtId"))
//                                +idProcess(obj.getString("groupId"))+idProcess(obj.getString("equipmentTypeId")),obj);
//                    }else{
//                        obj.put("memberCount",mapObj.getIntValue("memberCount"));
//                        obj.put("memberCountUp",mapObj.getDoubleValue("memberCountUp"));
//                        obj.put("equipmentCounts",mapObj.getIntValue("equipmentCounts"));
//                        obj.put("equipmentCountsUp",mapObj.getDoubleValue("equipmentCountsUp"));
//                        data.put(idProcess(obj.getString("provinceId"))+idProcess(obj.getString("cityId"))+idProcess(obj.getString("districtId"))
//                                +idProcess(obj.getString("groupId"))+idProcess(obj.getString("equipmentTypeId")),obj);
//                    }
//                }
//            }
//        }
//
//        if(data != null){
//            Set<String> keys = data.keySet();
//            //倒序
//            Map<Double,List<JSONObject>> treeMap = new TreeMap<>(Comparator.reverseOrder());
//            //正序
//            if("asc".equals(monthParamDto.getOrder())){
//                treeMap = new TreeMap<>();
//            }
//            for(String key : keys){
//                //如何排序,目前只做了按金钱排序
//                JSONObject obj = data.get(key);
//                if(obj.getString("equipmentTypeName") == null || "未知".equals(obj.getString("equipmentTypeName"))){
//                    continue;
//                }
//                List<JSONObject> list = treeMap.get(obj.getDoubleValue("payAmount"));
//                if(list == null){
//                    list = new ArrayList<JSONObject>();
//                }
//                list.add(obj);
//                treeMap.put(obj.getDoubleValue("payAmount"),list);
//            }
//
//            //开始按照顺序重新拼装list
//            Set<Double> tmKeys = treeMap.keySet();
//            for(Double key : tmKeys){
//                orders.addAll(treeMap.get(key));
//            }
//
//        }
//
//        orderResult.getJSONObject("info").remove("list");
//        orderResult.getJSONObject("info").put("list",orders);
//
//        orderResult.getJSONObject("info").put("totalCount",orders.size());
//        return orderResult;
//    }
//
//    @Override
//    public JSONObject getYearEquipmentTypeData(YearParamDto yearParamDto) {
//        ParamDto params = new ParamDto();
//        params.setCode(yearOrderDayAnalyseCode);
//        //要把排序给排除
//        String orderBy = yearParamDto.getOrderBy();
//        yearParamDto.setOrderBy(null);
//        //维度加上设备类型
//        String comboType = yearParamDto.getComboType();
//        if(yearParamDto.getEquipmentTypeId() == null){
//            yearParamDto.setEquipmentTypeId(9999999);
//            comboType = yearParamDto.getComboType();
//            yearParamDto.setEquipmentTypeId(null);
//        }
//
//        params.getQueryParams().setCombo_type(comboType);
//        params.setColumnArray(QuerySql.YearEquipmentTypeListSql);
//        JSONObject orderResult = getData(params, yearParamDto);
//
//        params.setCode(yearMemberDataCode);
//        params.setColumnArray(QuerySql.YearMemberListSql);
//        JSONObject memberResult = getData(params, yearParamDto);
//
//        params.setCode(yearGroupEquipmentAnalyseCode);
//        params.setColumnArray(QuerySql.WeekGroupListSql);
//        JSONObject groupResult = getData(params, yearParamDto);
//
//        if(orderResult == null && memberResult == null && groupResult == null){
//            return null;
//        }
//
//        Map<String,JSONObject> data = new HashMap<>();
//
//        List<JSONObject> orders = new ArrayList<>();
//
//        if(groupResult != null && groupResult.getInteger("code") == 200 && groupResult.getJSONObject("info") != null && groupResult.getJSONObject("info").getJSONArray("list") != null){
//            JSONArray groupList = groupResult.getJSONObject("info").getJSONArray("list");
//            if(groupList != null && groupList.size()>0){
//                for(int i=0; i<groupList.size(); i++){
//                    JSONObject obj = groupList.getJSONObject(i);
//                    obj.put("memberCount","0");
//                    obj.put("memberCountUp","0");
//                    obj.put("payAmount","0");
//                    obj.put("payAmountUp","0");
//                    obj.put("payCount","0");
//                    obj.put("payCountUp","0");
//                    obj.put("perOrderAmount","0");
//                    obj.put("perOrderAmountUp","0");
//                    data.put(idProcess(obj.getString("provinceId"))+idProcess(obj.getString("cityId"))+idProcess(obj.getString("districtId"))
//                                    +idProcess(obj.getString("groupId"))+idProcess(obj.getString("equipmentTypeId"))
//                            ,obj);
//                }
//            }
//        }
//
//        if(memberResult != null && memberResult.getInteger("code") == 200 && memberResult.getJSONObject("info") != null && memberResult.getJSONObject("info").getJSONArray("list") != null){
//            JSONArray memberList = memberResult.getJSONObject("info").getJSONArray("list");
//            if(memberList != null && memberList.size()>0){
//                for(int i=0; i<memberList.size(); i++){
//                    JSONObject obj = memberList.getJSONObject(i);
//                    JSONObject mapObj = data.get(idProcess(obj.getString("provinceId"))+idProcess(obj.getString("cityId"))+idProcess(obj.getString("districtId"))
//                            +idProcess(obj.getString("groupId"))+idProcess(obj.getString("equipmentTypeId")));
//                    if(mapObj == null){
//                        obj.put("equipmentCounts","0");
//                        obj.put("equipmentCountsUp","0");
//                        data.put(idProcess(obj.getString("provinceId"))+idProcess(obj.getString("cityId"))+idProcess(obj.getString("districtId"))
//                                        +idProcess(obj.getString("groupId"))+idProcess(obj.getString("equipmentTypeId"))
//                                ,obj);
//                    }else{
//                        mapObj.put("memberCount",obj.getIntValue("memberCount"));
//                        mapObj.put("memberCountUp",obj.getDoubleValue("memberCountUp"));
//                    }
//                }
//            }
//        }
//
//        if(orderResult != null && orderResult.getInteger("code") == 200 && orderResult.getJSONObject("info") != null && orderResult.getJSONObject("info").getJSONArray("list") != null){
//            JSONArray orderList = orderResult.getJSONObject("info").getJSONArray("list");
//            if(orderList != null && orderList.size()>0){
//                for(int i=0; i<orderList.size(); i++){
//                    JSONObject obj = orderList.getJSONObject(i);
//                    JSONObject mapObj = data.get(idProcess(obj.getString("provinceId"))+idProcess(obj.getString("cityId"))+idProcess(obj.getString("districtId"))
//                            +idProcess(obj.getString("groupId"))+idProcess(obj.getString("equipmentTypeId")));
//                    if(mapObj == null){
//                        obj.put("memberCount",0);
//                        obj.put("memberCountUp",0);
//                        obj.put("equipmentCounts",0);
//                        obj.put("equipmentCountsUp",0);
//                        data.put(idProcess(obj.getString("provinceId"))+idProcess(obj.getString("cityId"))+idProcess(obj.getString("districtId"))
//                                +idProcess(obj.getString("groupId"))+idProcess(obj.getString("equipmentTypeId")),obj);
//                    }else{
//                        obj.put("memberCount",mapObj.getIntValue("memberCount"));
//                        obj.put("memberCountUp",mapObj.getDoubleValue("memberCountUp"));
//                        obj.put("equipmentCounts",mapObj.getIntValue("equipmentCounts"));
//                        obj.put("equipmentCountsUp",mapObj.getDoubleValue("equipmentCountsUp"));
//                        data.put(idProcess(obj.getString("provinceId"))+idProcess(obj.getString("cityId"))+idProcess(obj.getString("districtId"))
//                                +idProcess(obj.getString("groupId"))+idProcess(obj.getString("equipmentTypeId")),obj);
//                    }
//                }
//            }
//        }
//
//        if(data != null){
//            Set<String> keys = data.keySet();
//            //倒序
//            Map<Double,List<JSONObject>> treeMap = new TreeMap<>(Comparator.reverseOrder());
//            //正序
//            if("asc".equals(yearParamDto.getOrder())){
//                treeMap = new TreeMap<>();
//            }
//            for(String key : keys){
//                //如何排序,目前只做了按金钱排序
//                JSONObject obj = data.get(key);
//                if(obj.getString("equipmentTypeName") == null || "未知".equals(obj.getString("equipmentTypeName"))){
//                    continue;
//                }
//                List<JSONObject> list = treeMap.get(obj.getDoubleValue("payAmount"));
//                if(list == null){
//                    list = new ArrayList<JSONObject>();
//                }
//                list.add(obj);
//                treeMap.put(obj.getDoubleValue("payAmount"),list);
//            }
//
//            //开始按照顺序重新拼装list
//            Set<Double> tmKeys = treeMap.keySet();
//            for(Double key : tmKeys){
//                orders.addAll(treeMap.get(key));
//            }
//
//        }
//
//        orderResult.getJSONObject("info").remove("list");
//        orderResult.getJSONObject("info").put("list",orders);
//
//        orderResult.getJSONObject("info").put("totalCount",orders.size());
//        return orderResult;
//    }
//
//    @Override
//    public JSONObject getEveryDayData(DayParamDto dayParamDto) {
//        MultiValueMap<String, String> headers = new HttpHeaders();
//        headers.add("sk",sk);
//        headers.add("ak",ak);
//
//        //去掉排序
//        dayParamDto.setOrderBy(null);
//
//        ParamDto params = new ParamDto();
//        params.setCode(dayOrderDayAnalyseCode);
//        params.getQueryParams().setCombo_type(dayParamDto.getComboType());
//        params.getRangeParams().getDay().setStart(dayParamDto.getMonth()+"-01");
//        params.getRangeParams().getDay().setEnd(dayParamDto.getMonth()+"-32");
//
//        params.setColumnArray(QuerySql.EveryDayOrderQuerySql);
//
//
//        JSONObject orderResult = getData(params, dayParamDto);
//
//        params.setCode(dayMemberDataCode);
//        params.setColumnArray(QuerySql.DayMemberListSql);
//        JSONObject memberResult = getData(params, dayParamDto);
//
//        Map<String,JSONObject> data = new HashMap<>();
//
//        List<JSONObject> orders = new ArrayList<>();
//
//        if(orderResult != null && orderResult.getInteger("code") == 200 && orderResult.getJSONObject("info") != null && orderResult.getJSONObject("info").getJSONArray("list") != null){
//            JSONArray orderList = orderResult.getJSONObject("info").getJSONArray("list");
//            if(orderList != null && orderList.size()>0){
//                for(int i=0; i<orderList.size(); i++){
//                    JSONObject obj = orderList.getJSONObject(i);
//                    obj.put("memberCount",0);
//                    obj.put("memberCountUp",0);
//                    data.put(idProcess(obj.getString("provinceId"))+idProcess(obj.getString("cityId"))+idProcess(obj.getString("districtId"))
//                                    +idProcess(obj.getString("groupId"))+idProcess(obj.getString("equipmentTypeId"))+idProcess(obj.getString("day"))
//                            ,obj);
//                }
//            }
//        }
//
//        if(memberResult != null && memberResult.getInteger("code") == 200 && memberResult.getJSONObject("info") != null && memberResult.getJSONObject("info").getJSONArray("list") != null){
//            JSONArray memberList = memberResult.getJSONObject("info").getJSONArray("list");
//            if(memberList != null && memberList.size()>0){
//                for(int i=0; i<memberList.size(); i++){
//                    JSONObject obj = memberList.getJSONObject(i);
//                    JSONObject mapObj = data.get(idProcess(obj.getString("provinceId"))+idProcess(obj.getString("cityId"))+idProcess(obj.getString("districtId"))
//                            +idProcess(obj.getString("groupId"))+idProcess(obj.getString("equipmentTypeId"))+idProcess(obj.getString("day")));
//                    if(mapObj == null){
//                        obj.put("payAmount","0");
//                        obj.put("payAmountUp","0");
//                        obj.put("payCounts","0");
//                        obj.put("payCountsUp","0");
//                        obj.put("perOrderAmount","0");
//                        obj.put("perOrderAmountUp","0");
//                        data.put(idProcess(obj.getString("provinceId"))+idProcess(obj.getString("cityId"))+idProcess(obj.getString("districtId"))
//                                        +idProcess(obj.getString("groupId"))+idProcess(obj.getString("equipmentTypeId"))+idProcess(obj.getString("day"))
//                                ,obj);
//                    }else{
//                        mapObj.put("memberCount",obj.getIntValue("memberCount"));
//                        mapObj.put("memberCountUp",obj.getDoubleValue("memberCountUp"));
//                    }
//                }
//            }
//        }
//
//        if(data != null){
//            Set<String> keys = data.keySet();
//            //倒序
//            Map<String,List<JSONObject>> treeMap = new TreeMap<>(Comparator.reverseOrder());
//            //正序
//            if("asc".equals(dayParamDto.getOrder())){
//                treeMap = new TreeMap<>();
//            }
//            for(String key : keys){
//                //如何排序,目前只做了按金钱排序
//                JSONObject obj = data.get(key);
//                /*if(obj.getString("equipmentTypeName") == null || "未知".equals(obj.getString("equipmentTypeName"))){
//                    continue;
//                }*/
//                List<JSONObject> list = treeMap.get(obj.getString("day"));
//                if(list == null){
//                    list = new ArrayList<JSONObject>();
//                }
//                list.add(obj);
//                treeMap.put(obj.getString("day"),list);
//            }
//
//            //开始按照顺序重新拼装list
//            Set<String> tmKeys = treeMap.keySet();
//            for(String key : tmKeys){
//                orders.addAll(treeMap.get(key));
//            }
//
//        }
//
//        orderResult.getJSONObject("info").remove("list");
//        orderResult.getJSONObject("info").put("list",orders);
//
//        orderResult.getJSONObject("info").put("totalCount",orders.size());
//        return orderResult;
//    }
//
//    @Override
//    public JSONObject getEveryWeekData(WeekParamDto weekParamDto) {
//        MultiValueMap<String, String> headers = new HttpHeaders();
//        headers.add("sk",sk);
//        headers.add("ak",ak);
//
//        //去掉排序
//        weekParamDto.setOrderBy(null);
//
//
//        ParamDto params = new ParamDto();
//        params.setCode(weekOrderDayAnalyseCode);
//        params.getQueryParams().setCombo_type(weekParamDto.getComboType());
//
//        params.setColumnArray(QuerySql.EveryWeekOrderQuerySql);
//
//
//        JSONObject orderResult = getData(params, weekParamDto);
//
//        params.setCode(weekMemberDataCode);
//        params.setColumnArray(QuerySql.WeekMemberListSql);
//        JSONObject memberResult = getData(params, weekParamDto);
//
//        Map<String,JSONObject> data = new HashMap<>();
//
//        List<JSONObject> orders = new ArrayList<>();
//        if(orderResult == null){
//            return null;
//        }
//
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
//        JSONObject weekRange = new JSONObject();
//        try{
//            Date date = sdf.parse(weekParamDto.getMonth());
//            weekRange = TimeUtil.getMonthWeekRange(date);
//        }catch (Exception e){
//            log.warn("{}",e);
//        }
//
//
//        if(orderResult != null && orderResult.getInteger("code") == 200 && orderResult.getJSONObject("info") != null && orderResult.getJSONObject("info").getJSONArray("list") != null){
//            JSONArray orderList = orderResult.getJSONObject("info").getJSONArray("list");
//            if(orderList != null && orderList.size()>0){
//                for(int i=0; i<orderList.size(); i++){
//                    JSONObject obj = orderList.getJSONObject(i);
//                    obj.put("memberCount",0);
//                    obj.put("memberCountUp",0);
//                    obj.put("weekRange",weekRange == null ? null:weekRange.getString(obj.getString("week")));
//                    data.put(idProcess(obj.getString("provinceId"))+idProcess(obj.getString("cityId"))+idProcess(obj.getString("districtId"))
//                                    +idProcess(obj.getString("groupId"))+idProcess(obj.getString("equipmentTypeId"))+idProcess(obj.getString("week"))
//                            ,obj);
//                }
//            }
//        }
//
//        if(memberResult != null && memberResult.getInteger("code") == 200 && memberResult.getJSONObject("info") != null && memberResult.getJSONObject("info").getJSONArray("list") != null){
//            JSONArray memberList = memberResult.getJSONObject("info").getJSONArray("list");
//            if(memberList != null && memberList.size()>0){
//                for(int i=0; i<memberList.size(); i++){
//                    JSONObject obj = memberList.getJSONObject(i);
//                    JSONObject mapObj = data.get(idProcess(obj.getString("provinceId"))+idProcess(obj.getString("cityId"))+idProcess(obj.getString("districtId"))
//                            +idProcess(obj.getString("groupId"))+idProcess(obj.getString("equipmentTypeId"))+idProcess(obj.getString("week")));
//                    if(mapObj == null){
//                        obj.put("payAmount","0");
//                        obj.put("payAmountUp","0");
//                        obj.put("payCounts","0");
//                        obj.put("payCountsUp","0");
//                        obj.put("perOrderAmount","0");
//                        obj.put("perOrderAmountUp","0");
//                        obj.put("weekRange",weekRange == null ? null:weekRange.getString(obj.getString("week")));
//                        data.put(idProcess(obj.getString("provinceId"))+idProcess(obj.getString("cityId"))+idProcess(obj.getString("districtId"))
//                                        +idProcess(obj.getString("groupId"))+idProcess(obj.getString("equipmentTypeId"))+idProcess(obj.getString("week"))
//                                ,obj);
//                    }else{
//                        mapObj.put("memberCount",obj.getIntValue("memberCount"));
//                        mapObj.put("memberCountUp",obj.getDoubleValue("memberCountUp"));
//                    }
//                }
//            }
//        }
//
//        if(data != null){
//            Set<String> keys = data.keySet();
//            //倒序
//            Map<String,List<JSONObject>> treeMap = new TreeMap<>(Comparator.reverseOrder());
//            //正序
//            if("asc".equals(weekParamDto.getOrder())){
//                treeMap = new TreeMap<>();
//            }
//            for(String key : keys){
//                //如何排序,目前只做了按金钱排序
//                JSONObject obj = data.get(key);
//                /*if(obj.getString("equipmentTypeName") == null || "未知".equals(obj.getString("equipmentTypeName"))){
//                    continue;
//                }*/
//                List<JSONObject> list = treeMap.get(obj.getString("week"));
//                if(list == null){
//                    list = new ArrayList<JSONObject>();
//                }
//                list.add(obj);
//                treeMap.put(obj.getString("week"),list);
//            }
//
//            //开始按照顺序重新拼装list
//            Set<String> tmKeys = treeMap.keySet();
//            for(String key : tmKeys){
//                orders.addAll(treeMap.get(key));
//            }
//
//        }
//
//        orderResult.getJSONObject("info").remove("list");
//        orderResult.getJSONObject("info").put("list",orders);
//
//        orderResult.getJSONObject("info").put("totalCount",orders.size());
//        return orderResult;
//    }
//
//    @Override
//    public JSONObject getEveryMonthData(MonthParamDto monthParamDto) {
//        MultiValueMap<String, String> headers = new HttpHeaders();
//        headers.add("sk",sk);
//        headers.add("ak",ak);
//
//        //去掉排序
//        monthParamDto.setOrderBy(null);
//
//        ParamDto params = new ParamDto();
//        params.setCode(monthOrderDayAnalyseCode);
//        params.getQueryParams().setCombo_type(monthParamDto.getComboType());
//        params.getRangeParams().getYear_month().setStart(monthParamDto.getYear()+"-01");
//        params.getRangeParams().getYear_month().setEnd(monthParamDto.getYear()+"-12");
//
//        params.setColumnArray(QuerySql.EveryMonthOrderQuerySql);
//
//
//        JSONObject orderResult = getData(params, monthParamDto);
//
//        params.setCode(monthMemberDataCode);
//        params.setColumnArray(QuerySql.MonthMemberListSql);
//        JSONObject memberResult = getData(params, monthParamDto);
//
//        Map<String,JSONObject> data = new HashMap<>();
//
//        List<JSONObject> orders = new ArrayList<>();
//
//        if(orderResult != null && orderResult.getInteger("code") == 200 && orderResult.getJSONObject("info") != null && orderResult.getJSONObject("info").getJSONArray("list") != null){
//            JSONArray orderList = orderResult.getJSONObject("info").getJSONArray("list");
//            if(orderList != null && orderList.size()>0){
//                for(int i=0; i<orderList.size(); i++){
//                    JSONObject obj = orderList.getJSONObject(i);
//                    obj.put("memberCount",0);
//                    obj.put("memberCountUp",0);
//                    data.put(idProcess(obj.getString("provinceId"))+idProcess(obj.getString("cityId"))+idProcess(obj.getString("districtId"))
//                                    +idProcess(obj.getString("groupId"))+idProcess(obj.getString("equipmentTypeId"))+idProcess(obj.getString("month"))
//                            ,obj);
//                }
//            }
//        }
//
//        if(memberResult != null && memberResult.getInteger("code") == 200 && memberResult.getJSONObject("info") != null && memberResult.getJSONObject("info").getJSONArray("list") != null){
//            JSONArray memberList = memberResult.getJSONObject("info").getJSONArray("list");
//            if(memberList != null && memberList.size()>0){
//                for(int i=0; i<memberList.size(); i++){
//                    JSONObject obj = memberList.getJSONObject(i);
//                    JSONObject mapObj = data.get(idProcess(obj.getString("provinceId"))+idProcess(obj.getString("cityId"))+idProcess(obj.getString("districtId"))
//                            +idProcess(obj.getString("groupId"))+idProcess(obj.getString("equipmentTypeId"))+idProcess(obj.getString("month")));
//                    if(mapObj == null){
//                        obj.put("payAmount","0");
//                        obj.put("payAmountUp","0");
//                        obj.put("payCounts","0");
//                        obj.put("payCountsUp","0");
//                        obj.put("perOrderAmount","0");
//                        obj.put("perOrderAmountUp","0");
//                        data.put(idProcess(obj.getString("provinceId"))+idProcess(obj.getString("cityId"))+idProcess(obj.getString("districtId"))
//                                        +idProcess(obj.getString("groupId"))+idProcess(obj.getString("equipmentTypeId"))+idProcess(obj.getString("day"))
//                                ,obj);
//                    }else{
//                        mapObj.put("memberCount",obj.getIntValue("memberCount"));
//                        mapObj.put("memberCountUp",obj.getDoubleValue("memberCountUp"));
//                    }
//                }
//            }
//        }
//
//        if(data != null){
//            Set<String> keys = data.keySet();
//            //倒序
//            Map<String,List<JSONObject>> treeMap = new TreeMap<>(Comparator.reverseOrder());
//            //正序
//            if("asc".equals(monthParamDto.getOrder())){
//                treeMap = new TreeMap<>();
//            }
//            for(String key : keys){
//                //如何排序,目前只做了按金钱排序
//                JSONObject obj = data.get(key);
//                /*if(obj.getString("equipmentTypeName") == null || "未知".equals(obj.getString("equipmentTypeName"))){
//                    continue;
//                }*/
//                List<JSONObject> list = treeMap.get(obj.getString("month"));
//                if(list == null){
//                    list = new ArrayList<JSONObject>();
//                }
//                list.add(obj);
//                treeMap.put(obj.getString("month"),list);
//            }
//
//            //开始按照顺序重新拼装list
//            Set<String> tmKeys = treeMap.keySet();
//            for(String key : tmKeys){
//                orders.addAll(treeMap.get(key));
//            }
//
//        }
//
//        orderResult.getJSONObject("info").remove("list");
//        orderResult.getJSONObject("info").put("list",orders);
//
//        orderResult.getJSONObject("info").put("totalCount",orders.size());
//        return orderResult;
//    }
//
//    @Override
//    public JSONObject getEveryYearData(YearParamDto yearParamDto) {
//        MultiValueMap<String, String> headers = new HttpHeaders();
//        headers.add("sk",sk);
//        headers.add("ak",ak);
//
//        //去掉排序
//        yearParamDto.setOrderBy(null);
//
//        ParamDto params = new ParamDto();
//        params.setCode(yearOrderDayAnalyseCode);
//        params.getQueryParams().setCombo_type(yearParamDto.getComboType());
//        params.getRangeParams().getYear_str().setEnd(yearParamDto.getYear()+"");
//
//        params.setColumnArray(QuerySql.EveryYearOrderQuerySql);
//
//
//        JSONObject orderResult = getData(params, yearParamDto);
//
//        params.setCode(yearMemberDataCode);
//        params.setColumnArray(QuerySql.YearMemberListSql);
//        JSONObject memberResult = getData(params, yearParamDto);
//
//        Map<String,JSONObject> data = new HashMap<>();
//
//        List<JSONObject> orders = new ArrayList<>();
//
//        if(orderResult != null && orderResult.getInteger("code") == 200 && orderResult.getJSONObject("info") != null && orderResult.getJSONObject("info").getJSONArray("list") != null){
//            JSONArray orderList = orderResult.getJSONObject("info").getJSONArray("list");
//            if(orderList != null && orderList.size()>0){
//                for(int i=0; i<orderList.size(); i++){
//                    JSONObject obj = orderList.getJSONObject(i);
//                    obj.put("memberCount",0);
//                    obj.put("memberCountUp",0);
//                    data.put(idProcess(obj.getString("provinceId"))+idProcess(obj.getString("cityId"))+idProcess(obj.getString("districtId"))
//                                    +idProcess(obj.getString("groupId"))+idProcess(obj.getString("equipmentTypeId"))+idProcess(obj.getString("year"))
//                            ,obj);
//                }
//            }
//        }
//
//        if(memberResult != null && memberResult.getInteger("code") == 200 && memberResult.getJSONObject("info") != null && memberResult.getJSONObject("info").getJSONArray("list") != null){
//            JSONArray memberList = memberResult.getJSONObject("info").getJSONArray("list");
//            if(memberList != null && memberList.size()>0){
//                for(int i=0; i<memberList.size(); i++){
//                    JSONObject obj = memberList.getJSONObject(i);
//                    JSONObject mapObj = data.get(idProcess(obj.getString("provinceId"))+idProcess(obj.getString("cityId"))+idProcess(obj.getString("districtId"))
//                            +idProcess(obj.getString("groupId"))+idProcess(obj.getString("equipmentTypeId"))+idProcess(obj.getString("year")));
//                    if(mapObj == null){
//                        obj.put("payAmount","0");
//                        obj.put("payAmountUp","0");
//                        obj.put("payCounts","0");
//                        obj.put("payCountsUp","0");
//                        obj.put("perOrderAmount","0");
//                        obj.put("perOrderAmountUp","0");
//                        data.put(idProcess(obj.getString("provinceId"))+idProcess(obj.getString("cityId"))+idProcess(obj.getString("districtId"))
//                                        +idProcess(obj.getString("groupId"))+idProcess(obj.getString("equipmentTypeId"))+idProcess(obj.getString("day"))
//                                ,obj);
//                    }else{
//                        mapObj.put("memberCount",obj.getIntValue("memberCount"));
//                        mapObj.put("memberCountUp",obj.getDoubleValue("memberCountUp"));
//                    }
//                }
//            }
//        }
//
//        if(data != null){
//            Set<String> keys = data.keySet();
//            //倒序
//            Map<String,List<JSONObject>> treeMap = new TreeMap<>(Comparator.reverseOrder());
//            //正序
//            if("asc".equals(yearParamDto.getOrder())){
//                treeMap = new TreeMap<>();
//            }
//            for(String key : keys){
//                //如何排序,目前只做了按金钱排序
//                JSONObject obj = data.get(key);
//                /*if(obj.getString("equipmentTypeName") == null || "未知".equals(obj.getString("equipmentTypeName"))){
//                    continue;
//                }*/
//                List<JSONObject> list = treeMap.get(obj.getString("year"));
//                if(list == null){
//                    list = new ArrayList<JSONObject>();
//                }
//                list.add(obj);
//                treeMap.put(obj.getString("year"),list);
//            }
//
//            //开始按照顺序重新拼装list
//            Set<String> tmKeys = treeMap.keySet();
//            for(String key : tmKeys){
//                orders.addAll(treeMap.get(key));
//            }
//
//        }
//
//        orderResult.getJSONObject("info").remove("list");
//        orderResult.getJSONObject("info").put("list",orders);
//
//        orderResult.getJSONObject("info").put("totalCount",orders.size());
//        return orderResult;
//    }
//
//    private JSONObject getData(ParamDto params, Object obj){
//        MultiValueMap<String, String> headers = new HttpHeaders();
//        headers.add("sk",sk);
//        headers.add("ak",ak);
//
//        if(obj instanceof DayParamDto){
//            params.getQueryParams().setDay(((DayParamDto) obj).getDay());
//            params.getQueryParams().setMerchant_id(((DayParamDto) obj).getMerchantId()+"");
//            params.getQueryParams().setDay(((DayParamDto) obj).getDay());
//
//            params.getQueryParams().setProvince_id(((DayParamDto) obj).getProvinceId() == null ? null: new HashSet<String>(){{add(((DayParamDto) obj).getProvinceId()+"");}});
//            params.getQueryParams().setCity_id(((DayParamDto) obj).getCityId() == null ? null:new HashSet<String>(){{add(((DayParamDto) obj).getCityId()+"");}});
//            params.getQueryParams().setDistrict_id(((DayParamDto) obj).getAreaId() == null ? null: new HashSet<String>(){{add(((DayParamDto) obj).getAreaId()+"");}});
//            params.getQueryParams().setEquipment_group_id(((DayParamDto) obj).getGroupId() == null ? null: new HashSet<String>(){{add(((DayParamDto) obj).getGroupId()+"");}});
//            params.getQueryParams().setLyy_equipment_type_id(((DayParamDto) obj).getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(((DayParamDto) obj).getEquipmentTypeId()+"");}});
//
//            String pageNum = ((DayParamDto) obj).getPageNum() == null ? null : (((DayParamDto) obj).getPageNum()-1)+"" ;
//            params.getPageParams().setPageNum(pageNum);
//            
//            if(((DayParamDto) obj).getOrderBy() != null){
//                params.getOrderParams().put(FieldMapping.fieldMap.get(((DayParamDto) obj).getOrderBy()),params.buildOrderFile(((DayParamDto) obj).getOrder() == null ?  "asc":((DayParamDto) obj).getOrder()));
//            }
//
//            if(((DayParamDto) obj).getGroupIds() != null && ((DayParamDto) obj).getGroupIds().size()>0){
//                //params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//                if(((DayParamDto) obj).getGroupId() == null || !((DayParamDto) obj).getGroupIds().contains(((DayParamDto) obj).getGroupId()+"")){
//                    params.getQueryParams().setEquipment_group_id(((DayParamDto) obj).getGroupIds());
//                }
//            }
//        }
//
//        if(obj instanceof WeekParamDto){
//            params.getQueryParams().setYear_month(((WeekParamDto) obj).getMonth());
//            params.getQueryParams().setMerchant_id(((WeekParamDto) obj).getMerchantId()+"");
//            if(((WeekParamDto) obj).getWeek() != null){
//                params.getQueryParams().setMonth_week_num(((WeekParamDto) obj).getWeek()+"");
//            }
//
//
//            params.getQueryParams().setProvince_id(((WeekParamDto) obj).getProvinceId() == null ? null: new HashSet<String>(){{add(((WeekParamDto) obj).getProvinceId()+"");}});
//            params.getQueryParams().setCity_id(((WeekParamDto) obj).getCityId() == null ? null:new HashSet<String>(){{add(((WeekParamDto) obj).getCityId()+"");}});
//            params.getQueryParams().setDistrict_id(((WeekParamDto) obj).getAreaId() == null ? null: new HashSet<String>(){{add(((WeekParamDto) obj).getAreaId()+"");}});
//            params.getQueryParams().setEquipment_group_id(((WeekParamDto) obj).getGroupId() == null ? null: new HashSet<String>(){{add(((WeekParamDto) obj).getGroupId()+"");}});
//            params.getQueryParams().setLyy_equipment_type_id(((WeekParamDto) obj).getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(((WeekParamDto) obj).getEquipmentTypeId()+"");}});
//
//            params.getPageParams().setPageNum(((WeekParamDto) obj).getPageNum() == null ? null : (((WeekParamDto) obj).getPageNum()-1)+"");
//
//            if(((WeekParamDto) obj).getOrderBy() != null){
//                params.getOrderParams().put(FieldMapping.fieldMap.get(((WeekParamDto) obj).getOrderBy()),params.buildOrderFile(((WeekParamDto) obj).getOrder() == null ?  "asc":((WeekParamDto) obj).getOrder()));
//            }
//
//            if(((WeekParamDto) obj).getGroupIds() != null && ((WeekParamDto) obj).getGroupIds().size()>0){
//                //params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//                if(((WeekParamDto) obj).getGroupId() == null || !((WeekParamDto) obj).getGroupIds().contains(((WeekParamDto) obj).getGroupId()+"")){
//                    params.getQueryParams().setEquipment_group_id(((WeekParamDto) obj).getGroupIds());
//                }
//            }
//
//        }
//
//        if(obj instanceof MonthParamDto){
//            if(((MonthParamDto) obj).getMonth() != null){
//                params.getQueryParams().setYear_month(((MonthParamDto) obj).getMonth());
//            }
//
//            params.getQueryParams().setMerchant_id(((MonthParamDto) obj).getMerchantId()+"");
//            //params.getQueryParams().setMonth_week_num(((MonthParamDto) obj).getWeek()+"");
//
//            params.getQueryParams().setProvince_id(((MonthParamDto) obj).getProvinceId() == null ? null: new HashSet<String>(){{add(((MonthParamDto) obj).getProvinceId()+"");}});
//            params.getQueryParams().setCity_id(((MonthParamDto) obj).getCityId() == null ? null:new HashSet<String>(){{add(((MonthParamDto) obj).getCityId()+"");}});
//            params.getQueryParams().setDistrict_id(((MonthParamDto) obj).getAreaId() == null ? null: new HashSet<String>(){{add(((MonthParamDto) obj).getAreaId()+"");}});
//            params.getQueryParams().setEquipment_group_id(((MonthParamDto) obj).getGroupId() == null ? null: new HashSet<String>(){{add(((MonthParamDto) obj).getGroupId()+"");}});
//            params.getQueryParams().setLyy_equipment_type_id(((MonthParamDto) obj).getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(((MonthParamDto) obj).getEquipmentTypeId()+"");}});
//
//            params.getPageParams().setPageNum(((MonthParamDto) obj).getPageNum() == null ? null : (((MonthParamDto) obj).getPageNum()-1)+"");
//
//            if(((MonthParamDto) obj).getOrderBy() != null){
//                params.getOrderParams().put(FieldMapping.fieldMap.get(((MonthParamDto) obj).getOrderBy()),params.buildOrderFile(((MonthParamDto) obj).getOrder() == null ?  "asc":((MonthParamDto) obj).getOrder()));
//            }
//
//
//            if(((MonthParamDto) obj).getGroupIds() != null && ((MonthParamDto) obj).getGroupIds().size()>0){
//                //params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//                if(((MonthParamDto) obj).getGroupId() == null || !((MonthParamDto) obj).getGroupIds().contains(((MonthParamDto) obj).getGroupId()+"")){
//                    params.getQueryParams().setEquipment_group_id(((MonthParamDto) obj).getGroupIds());
//                }
//            }
//        }
//
//        if(obj instanceof YearParamDto){
//            if(((YearParamDto) obj).getYear() != null){
//                params.getQueryParams().setYear_str(((YearParamDto) obj).getYear()+"");
//            }
//
//            params.getQueryParams().setMerchant_id(((YearParamDto) obj).getMerchantId()+"");
//            //params.getQueryParams().setMonth_week_num(((MonthParamDto) obj).getWeek()+"");
//
//            params.getQueryParams().setProvince_id(((YearParamDto) obj).getProvinceId() == null ? null: new HashSet<String>(){{add(((YearParamDto) obj).getProvinceId()+"");}});
//            params.getQueryParams().setCity_id(((YearParamDto) obj).getCityId() == null ? null:new HashSet<String>(){{add(((YearParamDto) obj).getCityId()+"");}});
//            params.getQueryParams().setDistrict_id(((YearParamDto) obj).getAreaId() == null ? null: new HashSet<String>(){{add(((YearParamDto) obj).getAreaId()+"");}});
//            params.getQueryParams().setEquipment_group_id(((YearParamDto) obj).getGroupId() == null ? null: new HashSet<String>(){{add(((YearParamDto) obj).getGroupId()+"");}});
//            params.getQueryParams().setLyy_equipment_type_id(((YearParamDto) obj).getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(((YearParamDto) obj).getEquipmentTypeId()+"");}});
//
//            params.getPageParams().setPageNum(((YearParamDto) obj).getPageNum() == null ? null : (((YearParamDto) obj).getPageNum()-1)+"");
//            if(((YearParamDto) obj).getOrderBy() != null){
//                params.getOrderParams().put(FieldMapping.fieldMap.get(((YearParamDto) obj).getOrderBy()),params.buildOrderFile(((YearParamDto) obj).getOrder() == null ?  "asc":((YearParamDto) obj).getOrder()));
//            }
//
//
//            if(((YearParamDto) obj).getGroupIds() != null && ((YearParamDto) obj).getGroupIds().size()>0){
//                //params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
//                if(((YearParamDto) obj).getGroupId() == null || !((YearParamDto) obj).getGroupIds().contains(((YearParamDto) obj).getGroupId()+"")){
//                    params.getQueryParams().setEquipment_group_id(((YearParamDto) obj).getGroupIds());
//                }
//            }
//        }
//
//
//        try{
//            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
//            JSONObject result = oneDataServiceFeign.getData(params,headers);
//            log.info("{}",result.toJSONString());
//            return result;
//        }catch (Exception e){
//            log.warn("{}",e);
//        }
//        return null;
//    }
//
//    private String idProcess(String id){
//        if(id == null || "未知".equals(id) || "0".equals(id)){
//            return "";
//        }
//        return id;
//    }
}
