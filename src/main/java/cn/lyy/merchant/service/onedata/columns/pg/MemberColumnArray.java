package cn.lyy.merchant.service.onedata.columns.pg;

public class MemberColumnArray {
	
	public static String DAY_MEMBER_DATA_COLUMN_ARRAY =
			"merchant_id as \"merchantId\", " +
            //会员总数
            "member_count as \"memberCounts\", " +
            "cast(case when member_count_yesterday =0 and member_count>0 then 1 when member_count_yesterday=0 and member_count=0 then 0 else (member_count-member_count_yesterday)::numeric/member_count_yesterday::numeric end as decimal(15,2)) as \"memberCountsUp\"," +

            //新会员数
            "new_member_count as \"newMemberCounts\", " +
            "cast(case when new_member_count_yesterday =0 and new_member_count>0 then 1 when new_member_count=0 and new_member_count_yesterday=0 then 0 else (new_member_count-new_member_count_yesterday)::numeric/new_member_count_yesterday::numeric end as decimal(15,2)) as \"newMemberCountsUp\"," +

            //老会员数
            "old_member_count as \"oldMemberCounts\", " +
            "cast(case when old_member_count_yesterday =0 and old_member_count>0 then 1 when old_member_count_yesterday=0 and old_member_count=0 then 0 else (old_member_count-old_member_count_yesterday)::numeric/old_member_count_yesterday::numeric end as decimal(15,2)) as \"oldMemberCountsUp\"," +

            //新会员订单数
            "new_pay_count as \"newMemberOrders\", " +
            "cast(case when new_pay_count_yesterday =0 and new_pay_count>0 then 1 when new_pay_count_yesterday=0 and new_pay_count=0 then 0 else (new_pay_count-new_pay_count_yesterday)::numeric/new_pay_count_yesterday::numeric end as decimal(15,2)) as \"newMemberOrdersUp\"," +

            //老会员订单数
            "old_pay_count as \"oldMemberOrders\", " +
            "cast(case when old_pay_count_yesterday =0 and old_pay_count>0 then 1 when old_pay_count_yesterday=0 and old_pay_count=0 then 0 else (old_pay_count-old_pay_count_yesterday)::numeric/old_pay_count_yesterday::numeric end as decimal(15,2)) as \"oldMemberOrdersUp\"," +

            //新会员启动数
            "new_consume_count as \"newMemberStarts\", " +
            "cast(case when new_consume_count_yesterday =0 and new_consume_count>0 then 1 when new_consume_count_yesterday=0 and new_consume_count=0 then 0 else (new_consume_count-new_consume_count_yesterday)::numeric/new_consume_count_yesterday::numeric end as decimal(15,2)) as \"newMemberStartsUp\"," +

            //老会员启动数
            "old_consume_count as \"oldMemberStarts\", " +
            "cast(case when old_consume_count_yesterday =0 and old_consume_count>0 then 1 when old_consume_count_yesterday=0 and old_consume_count=0 then 0 else (old_consume_count-old_consume_count_yesterday)::numeric/old_consume_count_yesterday::numeric end as decimal(15,2)) as \"oldMemberStartsUp\"," +

            //新会员日均订单数
            "new_pay_count/1 as \"perNewMemberDayOrders\", " +
            "case when new_pay_count =0 then 0 " +
            "when (new_pay_count_yesterday = 0) and new_pay_count > 0 then 1 " +
            "else cast( ((new_pay_count/1) - (new_pay_count_yesterday/1))::numeric/(new_pay_count_yesterday/1)::numeric as decimal(15,2)) end  as \"perNewMemberDayOrdersUp\","+

            //老会员日均订单数
            "old_pay_count/1 as \"perOldMemberDayOrders\", " +
            "case when old_pay_count =0 then 0 " +
            "when (old_pay_count_yesterday = 0) and old_pay_count > 0 then 1 " +
            "else cast( ((old_pay_count/1) - (old_pay_count_yesterday/1))::numeric/(old_pay_count_yesterday/1)::numeric as decimal(15,2)) end  as \"perOldMemberDayOrdersUp\","+

            //新会员日均启动次
            "new_consume_count/1 as \"perNewMemberDayStarts\", " +
            "case when new_consume_count =0 then 0 " +
            "when (new_consume_count_yesterday = 0) and new_consume_count > 0 then 1 " +
            "else cast( ((new_consume_count/1) - (new_consume_count_yesterday/1))::numeric/(new_consume_count_yesterday/1)::numeric as decimal(15,2)) end  as \"perNewMemberDayStartsUp\","+

            //老会员日均启动次
            "old_consume_count/1 as \"perOldMemberDayStarts\", " +
            "case when old_consume_count =0 then 0 " +
            "when (old_consume_count_yesterday = 0) and old_consume_count > 0 then 1 " +
            "else cast( ((old_consume_count/1) - (old_consume_count_yesterday/1))::numeric/(old_consume_count_yesterday/1)::numeric as decimal(15,2)) end  as \"perOldMemberDayStartsUp\","+

            //新会员客单价
            "cast(case when new_pay_count=0 then 0 else new_pay_amount::numeric/new_pay_count::numeric end as decimal(15,2)) as \"perNewMemberOrderAmount\", " +
            "case when new_pay_count =0 then 0 " +
            "when (new_pay_amount_yesterday = 0 or new_pay_count_yesterday=0) and new_pay_amount > 0 then 1 " +
            "when (new_pay_count > 0 and new_pay_count_yesterday > 0) then cast( ((new_pay_amount::numeric/new_pay_count::numeric) - (new_pay_amount_yesterday::numeric/new_pay_count_yesterday::numeric))::numeric/(new_pay_amount_yesterday::numeric/new_pay_count_yesterday::numeric)::numeric as decimal(15,2)) " +
            "else 0 end  as \"perNewMemberOrderAmountUp\","+

            //老会员客单价
            "cast(case when old_pay_count=0 then 0 else old_pay_amount::numeric/old_pay_count::numeric end as decimal(15,2)) as \"perOldMemberOrderAmount\", " +
            "case when old_pay_count =0 then 0 " +
            "when (old_pay_amount_yesterday = 0 or old_pay_count_yesterday=0) and old_pay_amount > 0 then 1 " +
            "when (old_pay_count_yesterday > 0 and old_pay_amount > 0) then cast( ((old_pay_amount::numeric/old_pay_count::numeric) - (old_pay_amount_yesterday::numeric/old_pay_count_yesterday::numeric))::numeric/(old_pay_amount_yesterday::numeric/old_pay_count_yesterday::numeric) as decimal(15,2)) " +
            "else 0 end  as \"perOldMemberOrderAmountUp\"" ;
	
	public static String WEEK_MEMBER_DATA_COLUMN_ARRAY = "merchant_id as \"merchantId\", " +
            //会员总数
            "member_count as \"memberCounts\", " +
            "cast(case when last_week_member_count =0 and member_count>0 then 1 when last_week_member_count=0 and member_count=0 then 0 else (member_count::numeric-last_week_member_count::numeric)::numeric/last_week_member_count::numeric end as decimal(15,2)) as \"memberCountsUp\"," +

            //新会员数
            "new_member_count as \"newMemberCounts\", " +
            "cast(case when last_week_new_member_count =0 and new_member_count>0 then 1 when new_member_count=0 and last_week_new_member_count=0 then 0 else (new_member_count::numeric-last_week_new_member_count::numeric)::numeric/last_week_new_member_count::numeric end as decimal(15,2)) as \"newMemberCountsUp\"," +

            //老会员数
            "old_member_count as \"oldMemberCounts\", " +
            "cast(case when last_week_old_member_count =0 and old_member_count>0 then 1 when last_week_old_member_count=0 and old_member_count=0 then 0 else (old_member_count::numeric-last_week_old_member_count::numeric)::numeric/last_week_old_member_count::numeric end as decimal(15,2)) as \"oldMemberCountsUp\"," +

            //新会员订单数
            "new_pay_count as \"newMemberOrders\", " +
            "cast(case when last_week_new_pay_count =0 and new_pay_count>0 then 1 when last_week_new_pay_count=0 and new_pay_count=0 then 0 else (new_pay_count::numeric-last_week_new_pay_count::numeric)::numeric/last_week_new_pay_count::numeric end as decimal(15,2)) as \"newMemberOrdersUp\"," +

            //老会员订单数
            "old_pay_count as \"oldMemberOrders\", " +
            "cast(case when last_week_old_pay_count =0 and old_pay_count>0 then 1 when last_week_old_pay_count=0 and old_pay_count=0 then 0 else (old_pay_count-last_week_old_pay_count)::numeric/last_week_old_pay_count::numeric end as decimal(15,2)) as \"oldMemberOrdersUp\"," +

            //新会员启动数
            "new_consume_count as \"newMemberStarts\", " +
            "cast(case when last_week_new_consume_count =0 and new_consume_count>0 then 1 when last_week_new_consume_count=0 and new_consume_count=0 then 0 else (new_consume_count-last_week_new_consume_count)::numeric/last_week_new_consume_count::numeric end as decimal(15,2)) as \"newMemberStartsUp\"," +

            //老会员启动数
            "old_consume_count as \"oldMemberStarts\", " +
            "cast(case when last_week_old_consume_count =0 and old_consume_count>0 then 1 when last_week_old_consume_count=0 and old_consume_count=0 then 0 else (old_consume_count-last_week_old_consume_count)::numeric/last_week_old_consume_count::numeric end as decimal(15,2)) as \"oldMemberStartsUp\"," +

            //新会员日均订单数
            "cast((new_pay_count::numeric/7)::numeric as decimal(15,2)) as \"perNewMemberDayOrders\", " +
            "case when new_pay_count =0 and last_week_new_pay_count=0 then 0 " +
            "when (last_week_new_pay_count = 0) and new_pay_count > 0 then 1 " +
            "else cast( ((new_pay_count::numeric/7)::numeric - (last_week_new_pay_count::numeric/7)::numeric)::numeric/(last_week_new_pay_count::numeric/7)::numeric as decimal(15,2)) end  as \"perNewMemberDayOrdersUp\","+

            //老会员日均订单数
            "cast((old_pay_count::numeric/7::numeric)::numeric as decimal(15,2)) as \"perOldMemberDayOrders\", " +
            "case when old_pay_count =0 and last_week_old_pay_count=0 then 0 " +
            "when (last_week_old_pay_count = 0) and old_pay_count > 0 then 1 " +
            "else cast( ((old_pay_count::numeric/7::numeric)::numeric - (last_week_old_pay_count::numeric/7::numeric)::numeric)::numeric/(last_week_old_pay_count::numeric/7::numeric)::numeric as decimal(15,2)) end  as \"perOldMemberDayOrdersUp\","+

            //新会员日均启动次
            "cast((new_consume_count::numeric/7::numeric)::numeric as decimal(15,2)) as \"perNewMemberDayStarts\", " +
            "case when new_consume_count =0 then 0 " +
            "when (last_week_new_consume_count = 0) and new_consume_count > 0 then 1 " +
            "else cast( ((new_consume_count::numeric/7::numeric)::numeric - (last_week_new_consume_count::numeric/7)::numeric)::numeric/(last_week_new_consume_count::numeric/7::numeric)::numeric as decimal(15,2)) end  as \"perNewMemberDayStartsUp\","+

            //老会员日均启动次
            "cast((old_consume_count::numeric/7::numeric)::numeric as decimal(15,2)) as \"perOldMemberDayStarts\", " +
            "case when old_consume_count =0 then 0 " +
            "when (last_week_old_consume_count = 0) and old_consume_count > 0 then 1 " +
            "else cast( ((old_consume_count::numeric/7::numeric) - (last_week_old_consume_count::numeric/7::numeric))::numeric/(last_week_old_consume_count::numeric/7::numeric)::numeric as decimal(15,2)) end  as \"perOldMemberDayStartsUp\","+

            //新会员客单价
            "cast(case when new_pay_count=0 then 0 else new_pay_amount::numeric/new_pay_count::numeric end as decimal(15,2)) as \"perNewMemberOrderAmount\", " +
            "case when (new_pay_count =0 or new_pay_amount=0) and ((last_week_new_pay_amount = 0 or last_week_new_pay_count=0)) then 0 " +
            "when (last_week_new_pay_amount = 0 or last_week_new_pay_count=0) and new_pay_amount > 0 then 1 " +
            "when (new_pay_count > 0 and last_week_new_pay_count > 0) then cast( ((new_pay_amount::numeric/new_pay_count::numeric) - (last_week_new_pay_amount::numeric/last_week_new_pay_count::numeric))::numeric/(last_week_new_pay_amount::numeric/last_week_new_pay_count::numeric)::numeric as decimal(15,2)) " + 
            "else 0 end  as \"perNewMemberOrderAmountUp\","+

            //老会员客单价
            "cast(case when old_pay_count=0 then 0 else old_pay_amount::numeric/old_pay_count::numeric end as decimal(15,2)) as \"perOldMemberOrderAmount\", " +
            "case when (old_pay_count =0 or old_pay_amount =0 ) and (last_week_old_pay_amount = 0 or last_week_old_pay_count=0) then 0 " +
            "when (last_week_old_pay_amount = 0 or last_week_old_pay_count=0) and old_pay_amount > 0 then 1 " +
            "when (old_pay_count > 0 and last_week_old_pay_count > 0) then cast( ((old_pay_amount::numeric/old_pay_count::numeric) - (last_week_old_pay_amount::numeric/last_week_old_pay_count::numeric))::numeric/(last_week_old_pay_amount::numeric/last_week_old_pay_count::numeric) as decimal(15,2)) " +
            "else 0 end  as \"perOldMemberOrderAmountUp\"";
	
	public static String MONTH_MEMBER_DATA_COLUMN_ARRAY = "merchant_id as \"merchantId\", " +
            //会员总数
            "member_count as \"memberCounts\", " +
            "cast(case when last_month_member_count =0 and member_count>0 then 1 when last_month_member_count=0 and member_count=0 then 0 else (member_count::numeric-last_month_member_count::numeric)::numeric/last_month_member_count::numeric end as decimal(15,2)) as \"memberCountsUp\"," +

            //新会员数
            "new_member_count as \"newMemberCounts\", " +
            "cast(case when last_month_new_member_count =0 and new_member_count>0 then 1 when new_member_count=0 and last_month_new_member_count=0 then 0 else (new_member_count::numeric-last_month_new_member_count::numeric)::numeric/last_month_new_member_count::numeric end as decimal(15,2)) as \"newMemberCountsUp\"," +

            //老会员数
            "old_member_count as \"oldMemberCounts\", " +
            "cast(case when last_month_old_member_count =0 and old_member_count>0 then 1 when last_month_old_member_count=0 and old_member_count=0 then 0 else (old_member_count::numeric-last_month_old_member_count::numeric)::numeric/last_month_old_member_count::numeric end as decimal(15,2)) as \"oldMemberCountsUp\"," +

            //新会员订单数
            "new_pay_count as \"newMemberOrders\", " +
            "cast(case when last_month_new_pay_count =0 and new_pay_count>0 then 1 when last_month_new_pay_count=0 and new_pay_count=0 then 0 else (new_pay_count::numeric-last_month_new_pay_count::numeric)::numeric/last_month_new_pay_count::numeric end as decimal(15,2)) as \"newMemberOrdersUp\"," +

            //老会员订单数
            "old_pay_count as \"oldMemberOrders\", " +
            "cast(case when last_month_old_pay_count =0 and old_pay_count>0 then 1 when last_month_old_pay_count=0 and old_pay_count=0 then 0 else (old_pay_count-last_month_old_pay_count)::numeric/last_month_old_pay_count::numeric end as decimal(15,2)) as \"oldMemberOrdersUp\"," +

            //新会员启动数
            "new_consume_count as \"newMemberStarts\", " +
            "cast(case when last_month_new_consume_count =0 and new_consume_count>0 then 1 when last_month_new_consume_count=0 and new_consume_count=0 then 0 else (new_consume_count-last_month_new_consume_count)::numeric/last_month_new_consume_count::numeric end as decimal(15,2)) as \"newMemberStartsUp\"," +

            //老会员启动数
            "old_consume_count as \"oldMemberStarts\", " +
            "cast(case when last_month_old_consume_count =0 and old_consume_count>0 then 1 when last_month_old_consume_count=0 and old_consume_count=0 then 0 else (old_consume_count-last_month_old_consume_count)::numeric/last_month_old_consume_count::numeric end as decimal(15,2)) as \"oldMemberStartsUp\"," +

            //新会员日均订单数
            "cast((new_pay_count::numeric/30)::numeric as decimal(15,2)) as \"perNewMemberDayOrders\", " +
            "case when new_pay_count =0 and last_month_new_pay_count=0 then 0 " +
            "when (last_month_new_pay_count = 0) and new_pay_count > 0 then 1 " +
            "else cast( ((new_pay_count::numeric/30)::numeric - (last_month_new_pay_count::numeric/30)::numeric)::numeric/(last_month_new_pay_count::numeric/30)::numeric as decimal(15,2)) end  as \"perNewMemberDayOrdersUp\","+

            //老会员日均订单数
            "cast((old_pay_count::numeric/30::numeric)::numeric as decimal(15,2)) as \"perOldMemberDayOrders\", " +
            "case when old_pay_count =0 and last_month_old_pay_count=0 then 0 " +
            "when (last_month_old_pay_count = 0) and old_pay_count > 0 then 1 " +
            "else cast( ((old_pay_count::numeric/30::numeric)::numeric - (last_month_old_pay_count::numeric/30::numeric)::numeric)::numeric/(last_month_old_pay_count::numeric/30::numeric)::numeric as decimal(15,2)) end  as \"perOldMemberDayOrdersUp\","+

            //新会员日均启动次
            "cast((new_consume_count::numeric/30::numeric)::numeric as decimal(15,2)) as \"perNewMemberDayStarts\", " +
            "case when new_consume_count =0 then 0 " +
            "when (last_month_new_consume_count = 0) and new_consume_count > 0 then 1 " +
            "else cast( ((new_consume_count::numeric/30::numeric)::numeric - (last_month_new_consume_count::numeric/30)::numeric)::numeric/(last_month_new_consume_count::numeric/30::numeric)::numeric as decimal(15,2)) end  as \"perNewMemberDayStartsUp\","+

            //老会员日均启动次
            "cast((old_consume_count::numeric/30::numeric)::numeric as decimal(15,2)) as \"perOldMemberDayStarts\", " +
            "case when old_consume_count =0 then 0 " +
            "when (last_month_old_consume_count = 0) and old_consume_count > 0 then 1 " +
            "else cast( ((old_consume_count::numeric/30::numeric) - (last_month_old_consume_count::numeric/30::numeric))::numeric/(last_month_old_consume_count::numeric/30::numeric)::numeric as decimal(15,2)) end  as \"perOldMemberDayStartsUp\","+

            //新会员客单价
            "cast(case when new_pay_count=0 then 0 else new_pay_amount::numeric/new_pay_count::numeric end as decimal(15,2)) as \"perNewMemberOrderAmount\", " +
            "case when (new_pay_count =0 or new_pay_amount=0) and ((last_month_new_pay_amount = 0 or last_month_new_pay_count=0)) then 0 " +
            "when (last_month_new_pay_amount = 0 or last_month_new_pay_count=0) and new_pay_amount > 0 then 1 " +
            "when (new_pay_count > 0 and last_month_new_pay_count > 0) then cast( ((new_pay_amount::numeric/new_pay_count::numeric) - (last_month_new_pay_amount::numeric/last_month_new_pay_count::numeric))::numeric/(last_month_new_pay_amount::numeric/last_month_new_pay_count::numeric)::numeric as decimal(15,2)) " + 
            "else 0 end  as \"perNewMemberOrderAmountUp\","+

            //老会员客单价
            "cast(case when old_pay_count=0 then 0 else old_pay_amount::numeric/old_pay_count::numeric end as decimal(15,2)) as \"perOldMemberOrderAmount\", " +
            "case when (old_pay_count =0 or old_pay_amount =0 ) and (last_month_old_pay_amount = 0 or last_month_old_pay_count=0) then 0 " +
            "when (last_month_old_pay_amount = 0 or last_month_old_pay_count=0) and old_pay_amount > 0 then 1 " +
            "when (old_pay_count > 0 and last_month_old_pay_count > 0) then  cast( ((old_pay_amount::numeric/old_pay_count::numeric) - (last_month_old_pay_amount::numeric/last_month_old_pay_count::numeric))::numeric/(last_month_old_pay_amount::numeric/last_month_old_pay_count::numeric) as decimal(15,2)) " + 
            "else 0 end  as \"perOldMemberOrderAmountUp\"";
	
	public static String YEAR_MEMBER_DATA_COLUMN_ARRAY = 
			"merchant_id as \"merchantId\", " +
            //会员总数
            "member_count as \"memberCounts\", " +
            "cast(case when last_year_member_count =0 and member_count>0 then 1 when last_year_member_count=0 and member_count=0 then 0 else (member_count::numeric-last_year_member_count::numeric)::numeric/last_year_member_count::numeric end as decimal(15,2)) as \"memberCountsUp\"," +

            //新会员数
            "new_member_count as \"newMemberCounts\", " +
            "cast(case when last_year_new_member_count =0 and new_member_count>0 then 1 when new_member_count=0 and last_year_new_member_count=0 then 0 else (new_member_count::numeric-last_year_new_member_count::numeric)::numeric/last_year_new_member_count::numeric end as decimal(15,2)) as \"newMemberCountsUp\"," +

            //老会员数
            "old_member_count as \"oldMemberCounts\", " +
            "cast(case when last_year_old_member_count =0 and old_member_count>0 then 1 when last_year_old_member_count=0 and old_member_count=0 then 0 else (old_member_count::numeric-last_year_old_member_count::numeric)::numeric/last_year_old_member_count::numeric end as decimal(15,2)) as \"oldMemberCountsUp\"," +

            //新会员订单数
            "new_pay_count as \"newMemberOrders\", " +
            "cast(case when last_year_new_pay_count =0 and new_pay_count>0 then 1 when last_year_new_pay_count=0 and new_pay_count=0 then 0 else (new_pay_count::numeric-last_year_new_pay_count::numeric)::numeric/last_year_new_pay_count::numeric end as decimal(15,2)) as \"newMemberOrdersUp\"," +

            //老会员订单数
            "old_pay_count as \"oldMemberOrders\", " +
            "cast(case when last_year_old_pay_count =0 and old_pay_count>0 then 1 when last_year_old_pay_count=0 and old_pay_count=0 then 0 else (old_pay_count-last_year_old_pay_count)::numeric/last_year_old_pay_count::numeric end as decimal(15,2)) as \"oldMemberOrdersUp\"," +

            //新会员启动数
            "new_consume_count as \"newMemberStarts\", " +
            "cast(case when last_year_new_consume_count =0 and new_consume_count>0 then 1 when last_year_new_consume_count=0 and new_consume_count=0 then 0 else (new_consume_count-last_year_new_consume_count)::numeric/last_year_new_consume_count::numeric end as decimal(15,2)) as \"newMemberStartsUp\"," +

            //老会员启动数
            "old_consume_count as \"oldMemberStarts\", " +
            "cast(case when last_year_old_consume_count =0 and old_consume_count>0 then 1 when last_year_old_consume_count=0 and old_consume_count=0 then 0 else (old_consume_count-last_year_old_consume_count)::numeric/last_year_old_consume_count::numeric end as decimal(15,2)) as \"oldMemberStartsUp\"," +

            //新会员日均订单数
            "cast((new_pay_count::numeric/365)::numeric as decimal(15,2)) as \"perNewMemberDayOrders\", " +
            "case when new_pay_count =0 and last_year_new_pay_count=0 then 0 " +
            "when (last_year_new_pay_count > 0) and new_pay_count = 0 then -1 " +
            "when (last_year_new_pay_count = 0) and new_pay_count > 0 then 1 " +
            "else cast( ((new_pay_count::numeric/365)::numeric - (last_year_new_pay_count::numeric/365)::numeric)::numeric/(last_year_new_pay_count::numeric/365)::numeric as decimal(15,2)) end  as \"perNewMemberDayOrdersUp\","+

            //老会员日均订单数
            "cast((old_pay_count::numeric/365::numeric)::numeric as decimal(15,2)) as \"perOldMemberDayOrders\", " +
            "case when old_pay_count =0 and last_year_old_pay_count=0 then 0 " +
            "when (last_year_old_pay_count > 0) and old_pay_count = 0 then -1 " +
            "when (last_year_old_pay_count = 0) and old_pay_count > 0 then 1 " +
            "else cast( ((old_pay_count::numeric/365::numeric)::numeric - (last_year_old_pay_count::numeric/365::numeric)::numeric)::numeric/(last_year_old_pay_count::numeric/365::numeric)::numeric as decimal(15,2)) end  as \"perOldMemberDayOrdersUp\","+

            //新会员日均启动次
            "cast((new_consume_count::numeric/365::numeric)::numeric as decimal(15,2)) as \"perNewMemberDayStarts\", " +
            "case when new_consume_count =0 then 0 " +
            "when (last_year_new_consume_count > 0) and new_consume_count = 0 then -1 " +
            "when (last_year_new_consume_count = 0) and new_consume_count > 0 then 1 " +
            "else cast( ((new_consume_count::numeric/365::numeric)::numeric - (last_year_new_consume_count::numeric/365)::numeric)::numeric/(last_year_new_consume_count::numeric/365::numeric)::numeric as decimal(15,2)) end  as \"perNewMemberDayStartsUp\","+

            //老会员日均启动次
            "cast((old_consume_count::numeric/365::numeric)::numeric as decimal(15,2)) as \"perOldMemberDayStarts\", " +
            "case when old_consume_count =0 then 0 " +
            "when old_consume_count = 0 and last_year_old_consume_count > 0 then -1 " +
            "when (last_year_old_consume_count = 0) and old_consume_count > 0 then 1 " +
            "else cast( ((old_consume_count::numeric/365::numeric) - (last_year_old_consume_count::numeric/365::numeric))::numeric/(last_year_old_consume_count::numeric/365::numeric)::numeric as decimal(15,2)) end  as \"perOldMemberDayStartsUp\","+

            //新会员客单价
            "cast(case when new_pay_count=0 then 0 else new_pay_amount::numeric/new_pay_count::numeric end as decimal(15,2)) as \"perNewMemberOrderAmount\", " +
            "case when (new_pay_count =0 or new_pay_amount=0) and ((last_year_new_pay_amount = 0 or last_year_new_pay_count=0)) then 0 " +
            "when (new_pay_count =0 or new_pay_amount=0) and last_year_new_pay_amount > 0 then -1 " +
            "when (last_year_new_pay_amount = 0 or last_year_new_pay_count=0) and new_pay_amount > 0 then 1 " +
            "when (new_pay_count > 0 and last_year_new_pay_count > 0) then cast( ((new_pay_amount::numeric/new_pay_count::numeric) - (last_year_new_pay_amount::numeric/last_year_new_pay_count::numeric))::numeric/(last_year_new_pay_amount::numeric/last_year_new_pay_count::numeric)::numeric as decimal(15,2)) " +
            "else 0 end  as \"perNewMemberOrderAmountUp\","+

            //老会员客单价
            "cast(case when old_pay_count=0 then 0 else old_pay_amount::numeric/old_pay_count::numeric end as decimal(15,2)) as \"perOldMemberOrderAmount\", " +
            "case when (old_pay_count =0 or old_pay_amount =0 ) and (last_year_old_pay_amount = 0 or last_year_old_pay_count=0) then 0 " +
            "when (old_pay_count =0 or old_pay_amount =0 ) and last_year_old_pay_amount > 0 then -1  " +
            "when (last_year_old_pay_amount = 0 or last_year_old_pay_count=0) and old_pay_amount > 0 then 1 " +
            "when (old_pay_count > 0 and last_year_old_pay_count > 0) then  cast( ((old_pay_amount::numeric/old_pay_count::numeric) - (last_year_old_pay_amount::numeric/last_year_old_pay_count::numeric))::numeric/(last_year_old_pay_amount::numeric/last_year_old_pay_count::numeric) as decimal(15,2)) " + 
            "else 0 end  as \"perOldMemberOrderAmountUp\"";

}
