package cn.lyy.merchant.service.onedata;

import cn.lyy.merchant.controller.onedata.dto.DayParamDto;
import cn.lyy.merchant.service.onedata.columns.ColumnArrayMap;
import cn.lyy.merchant.service.onedata.columns.ColumnArrayMapKey;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import java.util.*;

@Service
@Slf4j
public class OneDataServiceImpl implements OneDataService{

    @Autowired
    private FeignUtil feignUtil;
    @Value("${oneData.groupBaseInfoCode}")
    private String groupBaseInfoCode;
    @Value("${oneData.merchantEquipmentTypeCode}")
    private String merchantEquipmentTypeCode;
//    @Value("${oneData.databaseType:pg}")
//    private String databaseType ;//pg、selectdb
    
    @Override
    public JSONObject getProvince(DayParamDto dayParamDto) {
        ParamDto params = new ParamDto();
        params.setCode(groupBaseInfoCode);
        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.DROP_DOWN_PROVINCE_COLUMN_ARRAY) ;
        params.setColumnArray(columnArray);

        params.getPageParams().setPageRow("100");
        params.getQueryParams().setDistributor_id(dayParamDto.getMerchantId() == null ? null: dayParamDto.getMerchantId()+"");

        if(dayParamDto.getGroupIds() != null && dayParamDto.getGroupIds().size()>0){
            params.getQueryParams().setEquipment_group_id(dayParamDto.getGroupIds());
            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
            params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
        }
        try{
            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
            JSONObject result = feignUtil.getData(params);
            log.info("{}",result.toJSONString());
            if(result != null && "200".equals(result.getString("code")) && result.getJSONObject("info") != null){
                JSONArray jsonArray = result.getJSONObject("info").getJSONArray("list");
                Integer removeIndex = null;
                if(jsonArray != null && jsonArray.size() > 0){
                    //设置大小
                    result.getJSONObject("info").remove("totalPage");
                    result.getJSONObject("info").remove("totalCount");
                    for(int i=0; i<jsonArray.size(); i++){
                        JSONObject jsonObject = jsonArray.getJSONObject(i);
                        if(jsonObject.getString("province_id")==null || jsonObject.getString("province_id").trim().isEmpty()){
                            removeIndex = i;
                        }else{
                            jsonObject.put("provinceId",jsonObject.getIntValue("province_id"));
                            jsonObject.put("provinceName",jsonObject.getString("province_name"));
                            jsonObject.remove("province_id");
                            jsonObject.remove("province_name");
                        }
                    }
                    if(removeIndex !=null){
                        jsonArray.remove(jsonArray.getJSONObject(removeIndex));
                    }
                }
            }
            return result;
        }catch (Exception e){
            log.warn("{}",e);
        }

        return null;
    }

    @SuppressWarnings("serial")
	@Override
    public JSONObject getCity(DayParamDto dayParamDto) {
        ParamDto params = new ParamDto();
        params.setCode(groupBaseInfoCode);
        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.DROP_DOWN_CITY_COLUMN_ARRAY) ;
        params.setColumnArray(columnArray);
        params.getPageParams().setPageRow("500");

        params.getQueryParams().setDistributor_id(dayParamDto.getMerchantId() == null ? null: dayParamDto.getMerchantId()+"");
        params.getQueryParams().setProvince_id(dayParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(dayParamDto.getProvinceId()+"");}});
        params.getQueryParams().setCity_id(dayParamDto.getCityId() == null ? null:new HashSet<String>(){{add(dayParamDto.getCityId()+"");}});
        params.getQueryParams().setDistrict_id(dayParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(dayParamDto.getAreaId()+"");}});
        params.getQueryParams().setEquipment_group_id(dayParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(dayParamDto.getGroupId()+"");}});
        params.getQueryParams().setLyy_equipment_type_id(dayParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(dayParamDto.getEquipmentTypeId()+"");}});

        if(dayParamDto.getGroupIds() != null && dayParamDto.getGroupIds().size()>0){
            params.getQueryParams().setEquipment_group_id(dayParamDto.getGroupIds());
            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
            params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
        }

        try{
            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
            JSONObject result = feignUtil.getData(params);
            log.info("{}",result.toJSONString());
            if(result != null && "200".equals(result.getString("code")) && result.getJSONObject("info") != null){
                JSONArray jsonArray = result.getJSONObject("info").getJSONArray("list");
                Integer removeIndex = null;
                if(jsonArray != null && jsonArray.size() > 0){
                    //设置大小
                    result.getJSONObject("info").remove("totalPage");
                    result.getJSONObject("info").remove("totalCount");
                    for(int i=0; i<jsonArray.size(); i++){
                        JSONObject jsonObject = jsonArray.getJSONObject(i);
                        if(jsonObject.getString("city_id")==null || jsonObject.getString("city_id").trim().isEmpty()){
                            removeIndex = i;
                        }else{
                            jsonObject.put("cityId",jsonObject.getIntValue("city_id"));
                            jsonObject.put("cityName",jsonObject.getString("city_name"));
                            jsonObject.remove("city_id");
                            jsonObject.remove("city_name");
                        }
                    }
                    if(removeIndex !=null){
                        jsonArray.remove(jsonArray.getJSONObject(removeIndex));
                    }
                }
            }
            return result;
        }catch (Exception e){
            log.warn("{}",e);
        }

        return null;
    }

    @SuppressWarnings("serial")
	@Override
    public JSONObject getArea(DayParamDto dayParamDto) {
        ParamDto params = new ParamDto();
        params.setCode(groupBaseInfoCode);
        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.DROP_DOWN_DISTRICT_COLUMN_ARRAY) ;
        params.setColumnArray(columnArray);

        params.getPageParams().setPageRow("500");

        params.getQueryParams().setDistributor_id(dayParamDto.getMerchantId() == null ? null: dayParamDto.getMerchantId()+"");
        params.getQueryParams().setProvince_id(dayParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(dayParamDto.getProvinceId()+"");}});
        params.getQueryParams().setCity_id(dayParamDto.getCityId() == null ? null:new HashSet<String>(){{add(dayParamDto.getCityId()+"");}});
        params.getQueryParams().setDistrict_id(dayParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(dayParamDto.getAreaId()+"");}});
        params.getQueryParams().setEquipment_group_id(dayParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(dayParamDto.getGroupId()+"");}});
        params.getQueryParams().setLyy_equipment_type_id(dayParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(dayParamDto.getEquipmentTypeId()+"");}});

        if(dayParamDto.getGroupIds() != null && dayParamDto.getGroupIds().size()>0){
            params.getQueryParams().setEquipment_group_id(dayParamDto.getGroupIds());
            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
            params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
        }

        try{
            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
            JSONObject result = feignUtil.getData(params);
            log.info("{}",result.toJSONString());
            if(result != null && "200".equals(result.getString("code")) && result.getJSONObject("info") != null){
                JSONArray jsonArray = result.getJSONObject("info").getJSONArray("list");
                Integer removeIndex = null;
                if(jsonArray != null && jsonArray.size() > 0){
                    //设置大小
                    result.getJSONObject("info").remove("totalPage");
                    result.getJSONObject("info").remove("totalCount");
                    for(int i=0; i<jsonArray.size(); i++){
                        JSONObject jsonObject = jsonArray.getJSONObject(i);
                        if(jsonObject.getString("district_id")==null || jsonObject.getString("district_id").trim().isEmpty()){
                            removeIndex = i;
                        }else{
                            jsonObject.put("areaId",jsonObject.getIntValue("district_id"));
                            jsonObject.put("areaName",jsonObject.getString("district_name"));
                            jsonObject.remove("district_id");
                            jsonObject.remove("district_name");
                        }
                    }
                    if(removeIndex !=null){
                        jsonArray.remove(jsonArray.getJSONObject(removeIndex));
                    }
                }
            }
            return result;
        }catch (Exception e){
            log.warn("{}",e);
        }

        return null;
    }

    @SuppressWarnings("serial")
	@Override
    public JSONObject getGroup(DayParamDto dayParamDto) {
        ParamDto params = new ParamDto();
        params.setCode(groupBaseInfoCode);
        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.DROP_DOWN_EQUIPMENT_GROUP_COLUMN_ARRAY) ;
        params.setColumnArray(columnArray);
        params.getPageParams().setPageRow("500");

        params.getQueryParams().setDistributor_id(dayParamDto.getMerchantId() == null ? null: dayParamDto.getMerchantId()+"");
        params.getQueryParams().setProvince_id(dayParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(dayParamDto.getProvinceId()+"");}});
        params.getQueryParams().setCity_id(dayParamDto.getCityId() == null ? null:new HashSet<String>(){{add(dayParamDto.getCityId()+"");}});
        params.getQueryParams().setDistrict_id(dayParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(dayParamDto.getAreaId()+"");}});
        params.getQueryParams().setEquipment_group_id(dayParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(dayParamDto.getGroupId()+"");}});
        params.getQueryParams().setLyy_equipment_type_id(dayParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(dayParamDto.getEquipmentTypeId()+"");}});

        if(dayParamDto.getGroupIds() != null && dayParamDto.getGroupIds().size()>0){
            params.getQueryParams().setEquipment_group_id(dayParamDto.getGroupIds());
            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
            params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
        }

        if(dayParamDto.getGroupName() != null && !dayParamDto.getGroupName().trim().isEmpty()){
            params.getLikeParams().put("equipment_group_name",new HashSet<String>(){{add(dayParamDto.getGroupName());}} );
        }

        try{
            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
            JSONObject result = feignUtil.getData(params);
            log.info("{}",result.toJSONString());
            if(result != null && "200".equals(result.getString("code")) && result.getJSONObject("info") != null){
                JSONArray jsonArray = result.getJSONObject("info").getJSONArray("list");
                Integer removeIndex = null;
                if(jsonArray != null && jsonArray.size() > 0){
                    //设置大小
                    result.getJSONObject("info").remove("totalPage");
                    result.getJSONObject("info").remove("totalCount");
                    for(int i=0; i<jsonArray.size(); i++){
                        JSONObject jsonObject = jsonArray.getJSONObject(i);
                        if(jsonObject.getString("equipment_group_id")==null || jsonObject.getString("equipment_group_id").trim().isEmpty()){
                            removeIndex = i;
                        }else{
                            jsonObject.put("groupId",jsonObject.getIntValue("equipment_group_id"));
                            jsonObject.put("groupName",jsonObject.getString("equipment_group_name"));
                            jsonObject.remove("equipment_group_id");
                            jsonObject.remove("equipment_group_name");
                        }
                    }
                    if(removeIndex !=null){
                        jsonArray.remove(jsonArray.getJSONObject(removeIndex));
                    }
                }
            }
            return result;
        }catch (Exception e){
            log.warn("{}",e);
        }

        return null;
    }

    @SuppressWarnings("serial")
	@Override
    public JSONObject getMerchantEquipmentType(DayParamDto dayParamDto) {
        ParamDto params = new ParamDto();
        params.setCode(merchantEquipmentTypeCode);
        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(),ColumnArrayMapKey.DROP_DOWN_EQUIPMENT_TYPE_COLUMN_ARRAY) ;
        params.setColumnArray(columnArray);

        params.getPageParams().setPageRow("500");

        params.getQueryParams().setDistributor_id(dayParamDto.getMerchantId() == null ? null: dayParamDto.getMerchantId()+"");
        params.getQueryParams().setProvince_id(dayParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(dayParamDto.getProvinceId()+"");}});
        params.getQueryParams().setCity_id(dayParamDto.getCityId() == null ? null:new HashSet<String>(){{add(dayParamDto.getCityId()+"");}});
        params.getQueryParams().setDistrict_id(dayParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(dayParamDto.getAreaId()+"");}});
        params.getQueryParams().setEquipment_group_id(dayParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(dayParamDto.getGroupId()+"");}});
        params.getQueryParams().setLyy_equipment_type_id(dayParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(dayParamDto.getEquipmentTypeId()+"");}});

        if(dayParamDto.getGroupIds() != null && dayParamDto.getGroupIds().size()>0){
            params.getQueryParams().setEquipment_group_id(dayParamDto.getGroupIds());
        }

        try{
            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
            JSONObject result = feignUtil.getData(params);
            log.info("{}",result.toJSONString());
            if(result != null && "200".equals(result.getString("code")) && result.getJSONObject("info") != null){
                JSONArray jsonArray = result.getJSONObject("info").getJSONArray("list");
                Integer removeIndex = null;
                if(jsonArray != null && jsonArray.size() > 0){
                    //设置大小
                    result.getJSONObject("info").remove("totalPage");
                    result.getJSONObject("info").remove("totalCount");
                    for(int i=0; i<jsonArray.size(); i++){
                        JSONObject jsonObject = jsonArray.getJSONObject(i);
                        if(jsonObject.getString("equipmentTypeId")==null || jsonObject.getString("equipmentTypeId").trim().isEmpty()){
                            removeIndex = i;
                        }
                    }
                    if(removeIndex !=null){
                        jsonArray.remove(jsonArray.getJSONObject(removeIndex));
                    }
                }
            }
            return result;
        }catch (Exception e){
            log.warn("{}",e);
        }

        return null;
    }

}
