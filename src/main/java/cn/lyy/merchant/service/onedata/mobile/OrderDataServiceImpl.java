package cn.lyy.merchant.service.onedata.mobile;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;
import java.util.TreeSet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import cn.lyy.base.util.StringUtil;
import cn.lyy.merchant.controller.onedata.dto.DayParamDto;
import cn.lyy.merchant.controller.onedata.dto.MonthParamDto;
import cn.lyy.merchant.controller.onedata.dto.WeekParamDto;
import cn.lyy.merchant.controller.onedata.dto.YearParamDto;
import cn.lyy.merchant.controller.onedata.util.TimeUtil;
import cn.lyy.merchant.service.onedata.FeignUtil;
import cn.lyy.merchant.service.onedata.ParamDto;
import cn.lyy.merchant.service.onedata.columns.ColumnArrayMap;
import cn.lyy.merchant.service.onedata.columns.ColumnArrayMapKey;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class OrderDataServiceImpl implements OrderDataService {

	@Autowired
	private FeignUtil feignUtil ;
    @Value("${oneData.dayOrderDayAnalyseCode}")
    private String dayOrderDayAnalyseCode;
    @Value("${oneData.weekOrderDayAnalyseCode}")
    private String weekOrderDayAnalyseCode;
    @Value("${oneData.monthOrderDayAnalyseCode}")
    private String monthOrderDayAnalyseCode;
    @Value("${oneData.yearOrderDayAnalyseCode}")
    private String yearOrderDayAnalyseCode;
    
    @Value("${oneData.dayMemberDataCode}")
    private String dayMemberDataCode;
    @Value("${oneData.weekMemberDataCode}")
    private String weekMemberDataCode;
    @Value("${oneData.monthMemberDataCode}")
    private String monthMemberDataCode;
    @Value("${oneData.yearMemberDataCode}")
    private String yearMemberDataCode;
    
    /**
     * 获取日报订单支付数据
     * @param dayParamDto
     * @return
     */
    @SuppressWarnings({ "serial", "rawtypes" })
	@Override
    public JSONObject getOrderData(DayParamDto dayParamDto) {
        ParamDto params = new ParamDto();
        params.setCode(dayOrderDayAnalyseCode);
        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.DAY_ORDER_COLUMN_ARRAY) ;
        params.setColumnArray(columnArray);
        params.getQueryParams().setDay(dayParamDto.getDay());
        params.getQueryParams().setMerchant_id(dayParamDto.getMerchantId()+"");
        params.getQueryParams().setCombo_type(dayParamDto.getComboType());
        params.getQueryParams().setDay(dayParamDto.getDay());

        params.getQueryParams().setProvince_id(dayParamDto.getProvinceId() == null ? null: new HashSet<String>(){/**
			 * 
			 */
			private static final long serialVersionUID = 4602613413250945046L;

		{add(dayParamDto.getProvinceId()+"");}});
        params.getQueryParams().setCity_id(dayParamDto.getCityId() == null ? null:new HashSet<String>(){{add(dayParamDto.getCityId()+"");}});
        params.getQueryParams().setDistrict_id(dayParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(dayParamDto.getAreaId()+"");}});
        params.getQueryParams().setEquipment_group_id(dayParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(dayParamDto.getGroupId()+"");}});
        params.getQueryParams().setLyy_equipment_type_id(dayParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(dayParamDto.getEquipmentTypeId()+"");}});

        if(dayParamDto.getGroupIds() != null && dayParamDto.getGroupIds().size()>0){
            if(dayParamDto.getGroupId() == null || !dayParamDto.getGroupIds().contains(dayParamDto.getGroupId()+"")){
                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(dayParamDto.getGroupIds())).first()+"");}});
            }
            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
            //params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
        }
        params.resetComboType();

        try{
            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
            JSONObject result = feignUtil.getData(params);
            log.info("{}",result.toJSONString());
            return result;
        }catch (Exception e){
            log.warn("{}",e);
        }

        return null;
    }

    /**
     * 获取周报订单支付数据
     * @param weekParamDto
     * @return
     */
    @SuppressWarnings({ "serial", "rawtypes" })
    @Override
    public JSONObject getWeekOrderData(WeekParamDto weekParamDto) {
        ParamDto params = new ParamDto();
        params.setCode(weekOrderDayAnalyseCode);
        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.WEEK_ORDER_COLUMN_ARRAY) ;
        params.setColumnArray(columnArray);
        params.getQueryParams().setYear_month(weekParamDto.getMonth());
        params.getQueryParams().setMerchant_id(weekParamDto.getMerchantId()+"");
        params.getQueryParams().setCombo_type(weekParamDto.getComboType());
        params.getQueryParams().setMonth_week_num(weekParamDto.getWeek()+"");
        params.getQueryParams().setProvince_id(weekParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(weekParamDto.getProvinceId()+"");}});
        params.getQueryParams().setCity_id(weekParamDto.getCityId() == null ? null:new HashSet<String>(){{add(weekParamDto.getCityId()+"");}});
        params.getQueryParams().setDistrict_id(weekParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(weekParamDto.getAreaId()+"");}});
        params.getQueryParams().setEquipment_group_id(weekParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(weekParamDto.getGroupId()+"");}});
        params.getQueryParams().setLyy_equipment_type_id(weekParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(weekParamDto.getEquipmentTypeId()+"");}});

        if(weekParamDto.getGroupIds() != null && weekParamDto.getGroupIds().size()>0){
            if(weekParamDto.getGroupId() == null || !weekParamDto.getGroupIds().contains(weekParamDto.getGroupId()+"")){
                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(weekParamDto.getGroupIds())).first()+"");}});
            }
            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
            //params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
        }
        params.resetComboType();
        
        try{
            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
            JSONObject result = feignUtil.getData(params);
            log.info("{}",result.toJSONString());
            return result;
        }catch (Exception e){
            log.warn("{}",e);
        }

        return null;
    }

    /**
     * 获取月报订单支付数据
     * @param monthParamDto
     * @return
     */
    @SuppressWarnings({ "serial", "rawtypes" })
    @Override
    public JSONObject getMonthOrderData(MonthParamDto monthParamDto) {
        ParamDto params = new ParamDto();
        params.setCode(monthOrderDayAnalyseCode);
        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.MONTH_ORDER_COLUMN_ARRAY) ;
        params.setColumnArray(columnArray);
        params.getQueryParams().setYear_month(monthParamDto.getMonth());
        params.getQueryParams().setMerchant_id(monthParamDto.getMerchantId()+"");
        params.getQueryParams().setCombo_type(monthParamDto.getComboType());
        params.getQueryParams().setProvince_id(monthParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(monthParamDto.getProvinceId()+"");}});
        params.getQueryParams().setCity_id(monthParamDto.getCityId() == null ? null:new HashSet<String>(){{add(monthParamDto.getCityId()+"");}});
        params.getQueryParams().setDistrict_id(monthParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(monthParamDto.getAreaId()+"");}});
        params.getQueryParams().setEquipment_group_id(monthParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(monthParamDto.getGroupId()+"");}});
        params.getQueryParams().setLyy_equipment_type_id(monthParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(monthParamDto.getEquipmentTypeId()+"");}});

        if(monthParamDto.getGroupIds() != null && monthParamDto.getGroupIds().size()>0){
            if(monthParamDto.getGroupId() == null || !monthParamDto.getGroupIds().contains(monthParamDto.getGroupId()+"")){
                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(monthParamDto.getGroupIds())).first()+"");}});
            }
            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
            //params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
        }
        params.resetComboType();

        try{
            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
            JSONObject result = feignUtil.getData(params);
            log.info("{}",result.toJSONString());
            return result;
        }catch (Exception e){
            log.warn("{}",e);
        }



        return null;
    }

    /**
     * 获取年报订单支付数据
     * @param yearParamDto
     * @return
     */
    @SuppressWarnings({ "serial", "rawtypes" })
    @Override
    public JSONObject getYearOrderData(YearParamDto yearParamDto) {
        ParamDto params = new ParamDto();
        params.setCode(yearOrderDayAnalyseCode);

        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.YEAR_ORDER_COLUMN_ARRAY) ;
        params.setColumnArray(columnArray);
        params.getQueryParams().setYear_str(yearParamDto.getYear()+"");
        params.getQueryParams().setMerchant_id(yearParamDto.getMerchantId()+"");
        params.getQueryParams().setCombo_type(yearParamDto.getComboType());
        params.getQueryParams().setProvince_id(yearParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(yearParamDto.getProvinceId()+"");}});
        params.getQueryParams().setCity_id(yearParamDto.getCityId() == null ? null:new HashSet<String>(){{add(yearParamDto.getCityId()+"");}});
        params.getQueryParams().setDistrict_id(yearParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(yearParamDto.getAreaId()+"");}});
        params.getQueryParams().setEquipment_group_id(yearParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(yearParamDto.getGroupId()+"");}});
        params.getQueryParams().setLyy_equipment_type_id(yearParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(yearParamDto.getEquipmentTypeId()+"");}});

        if(yearParamDto.getGroupIds() != null && yearParamDto.getGroupIds().size()>0){
            if(yearParamDto.getGroupId() == null || !yearParamDto.getGroupIds().contains(yearParamDto.getGroupId()+"")){
                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(yearParamDto.getGroupIds())).first()+"");}});
            }
            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
           // params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
        }
        params.resetComboType();

        try{
            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
            JSONObject result = feignUtil.getData(params);
            log.info("{}",result.toJSONString());
            return result;
        }catch (Exception e){
            log.warn("{}",e);
        }

        return null;
    }

    @SuppressWarnings({ "serial", "rawtypes" })
    @Override
    public JSONObject getDayOrderData(DayParamDto dayParamDto) {
        ParamDto params = new ParamDto();
        params.setCode(dayOrderDayAnalyseCode);
        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.DAY_PER_ORDER_COLUMN_ARRAY) ;
        params.setColumnArray(columnArray);
        params.getPageParams().setPageRow("31");
        params.getQueryParams().setMerchant_id(dayParamDto.getMerchantId()+"");
        params.getQueryParams().setCombo_type(dayParamDto.getComboType());
        params.getQueryParams().setDay(dayParamDto.getDay());

        params.getQueryParams().setProvince_id(dayParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(dayParamDto.getProvinceId()+"");}});
        params.getQueryParams().setCity_id(dayParamDto.getCityId() == null ? null:new HashSet<String>(){{add(dayParamDto.getCityId()+"");}});
        params.getQueryParams().setDistrict_id(dayParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(dayParamDto.getAreaId()+"");}});
        params.getQueryParams().setEquipment_group_id(dayParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(dayParamDto.getGroupId()+"");}});
        params.getQueryParams().setLyy_equipment_type_id(dayParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(dayParamDto.getEquipmentTypeId()+"");}});
        params.getRangeParams().getDay().setStart(dayParamDto.getMonth()+"-01");
        
        //params.getRangeParams().getDay().setEnd(dayParamDto.getMonth()+"-32");
        try {
        	String dateStr = dayParamDto.getMonth()+"-01"; // 要转换的日期字符串
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd"); // 定义日期格式
            Date dateObj = sdf.parse(dateStr); // 将字符串转换为日期对象
            Calendar calendar = new GregorianCalendar() ;
            calendar.setTime(dateObj);
            
//            //本月第一天
//            calendar.add(Calendar.MONTH, 0);
//            calendar.set(Calendar.DAY_OF_MONTH, 1);
//            String firstDate = sdf.format(calendar.getTime()) ;
//            System.out.println("firstDate>>"+firstDate);
            
            //本月最后一天
            calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
            String lastDate = sdf.format(calendar.getTime()) ;
            params.getRangeParams().getDay().setEnd(lastDate) ;
            //System.out.println("lastDate"+lastDate);
		} catch (Exception e) {
			log.info("设置本月最后一天异常...");
			params.getRangeParams().getDay().setEnd(dayParamDto.getMonth()+"-32");
		}

        if(dayParamDto.getGroupIds() != null && dayParamDto.getGroupIds().size()>0){
            if(dayParamDto.getGroupId() == null || !dayParamDto.getGroupIds().contains(dayParamDto.getGroupId()+"")){
                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(dayParamDto.getGroupIds())).first()+"");}});
            }
            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
           // params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
        }
        params.resetComboType();
        
        try{
            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
            JSONObject result = feignUtil.getData(params);
            log.info("{}",result.toJSONString());
            return result;
        }catch (Exception e){
            log.warn("{}",e);
        }

        return null;
    }

    /**
     * 每周营收数据
     * @param weekParamDto
     * @return
     */
    @SuppressWarnings({ "serial", "rawtypes" })
    @Override
    public JSONObject getPerWeekOrderData(WeekParamDto weekParamDto) {
        ParamDto params = new ParamDto();
        params.setCode(weekOrderDayAnalyseCode);

        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.WEEK_PER_ORDER_COLUMN_ARRAY) ;
        params.setColumnArray(columnArray);

        params.getPageParams().setPageRow("10");
        params.getQueryParams().setMerchant_id(weekParamDto.getMerchantId()+"");
        params.getQueryParams().setCombo_type(weekParamDto.getComboType());
        params.getQueryParams().setYear_month(weekParamDto.getMonth());

        params.getQueryParams().setProvince_id(weekParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(weekParamDto.getProvinceId()+"");}});
        params.getQueryParams().setCity_id(weekParamDto.getCityId() == null ? null:new HashSet<String>(){{add(weekParamDto.getCityId()+"");}});
        params.getQueryParams().setDistrict_id(weekParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(weekParamDto.getAreaId()+"");}});
        params.getQueryParams().setEquipment_group_id(weekParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(weekParamDto.getGroupId()+"");}});
        params.getQueryParams().setLyy_equipment_type_id(weekParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(weekParamDto.getEquipmentTypeId()+"");}});
        //params.getRangeParams().getDay().setStart(dayParamDto.getMonth()+"-01");
        //params.getRangeParams().getDay().setEnd(dayParamDto.getMonth()+"-32");

        if(weekParamDto.getGroupIds() != null && weekParamDto.getGroupIds().size()>0){
            if(weekParamDto.getGroupId() == null || !weekParamDto.getGroupIds().contains(weekParamDto.getGroupId()+"")){
                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(weekParamDto.getGroupIds())).first()+"");}});
            }
            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
            //params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
        }
        params.resetComboType();

        try{
            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
            JSONObject result = feignUtil.getData(params);
            log.info("{}",result.toJSONString());
            return result;
        }catch (Exception e){
            log.warn("{}",e);
        }

        return null;
    }

    /**
     * 每月营收数据
     * @param monthParamDto
     * @return
     */
    @SuppressWarnings({ "serial", "rawtypes" })
    @Override
    public JSONObject getPerMonthOrderData(MonthParamDto monthParamDto) {
        ParamDto params = new ParamDto();
        params.setCode(monthOrderDayAnalyseCode);
        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.MONTH_PER_ORDER_COLUMN_ARRAY) ;
        params.setColumnArray(columnArray);
        params.getPageParams().setPageRow("20");
        params.getQueryParams().setMerchant_id(monthParamDto.getMerchantId()+"");
        params.getQueryParams().setCombo_type(monthParamDto.getComboType());
        params.getQueryParams().setYear_month(monthParamDto.getMonth());

        params.getQueryParams().setProvince_id(monthParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(monthParamDto.getProvinceId()+"");}});
        params.getQueryParams().setCity_id(monthParamDto.getCityId() == null ? null:new HashSet<String>(){{add(monthParamDto.getCityId()+"");}});
        params.getQueryParams().setDistrict_id(monthParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(monthParamDto.getAreaId()+"");}});
        params.getQueryParams().setEquipment_group_id(monthParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(monthParamDto.getGroupId()+"");}});
        params.getQueryParams().setLyy_equipment_type_id(monthParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(monthParamDto.getEquipmentTypeId()+"");}});
        params.getRangeParams().getYear_month().setStart(monthParamDto.getYear()+"-01");
        params.getRangeParams().getYear_month().setEnd(monthParamDto.getYear()+"-12");

        if(monthParamDto.getGroupIds() != null && monthParamDto.getGroupIds().size()>0){
            if(monthParamDto.getGroupId() == null || !monthParamDto.getGroupIds().contains(monthParamDto.getGroupId()+"")){
                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(monthParamDto.getGroupIds())).first()+"");}});
            }
            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
            //params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
        }

        try{
            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
            JSONObject result = feignUtil.getData(params);
            log.info("{}",result.toJSONString());
            return result;
        }catch (Exception e){
            log.warn("{}",e);
        }

        return null;
    }

    /**
     * 每年营收数据
     * @param yearParamDto
     * @return
     */
    @SuppressWarnings({ "serial", "rawtypes" })
    @Override
    public JSONObject getPerYearOrderData(YearParamDto yearParamDto) {
        ParamDto params = new ParamDto();
        params.setCode(yearOrderDayAnalyseCode);

        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.YEAR_PER_ORDER_COLUMN_ARRAY) ;
        params.setColumnArray(columnArray);
        params.getPageParams().setPageRow("10");
        params.getQueryParams().setMerchant_id(yearParamDto.getMerchantId()+"");
        params.getQueryParams().setCombo_type(yearParamDto.getComboType());
        params.getQueryParams().setYear_str(yearParamDto.getYear()+"");

        params.getQueryParams().setProvince_id(yearParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(yearParamDto.getProvinceId()+"");}});
        params.getQueryParams().setCity_id(yearParamDto.getCityId() == null ? null:new HashSet<String>(){{add(yearParamDto.getCityId()+"");}});
        params.getQueryParams().setDistrict_id(yearParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(yearParamDto.getAreaId()+"");}});
        params.getQueryParams().setEquipment_group_id(yearParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(yearParamDto.getGroupId()+"");}});
        params.getQueryParams().setLyy_equipment_type_id(yearParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(yearParamDto.getEquipmentTypeId()+"");}});
        //params.getRangeParams().getYear_month().setStart(monthParamDto.getYear()+"-01");
        //params.getRangeParams().getYear_month().setEnd(monthParamDto.getYear()+"-12");

        if(yearParamDto.getGroupIds() != null && yearParamDto.getGroupIds().size()>0){
            if(yearParamDto.getGroupId() == null || !yearParamDto.getGroupIds().contains(yearParamDto.getGroupId()+"")){
                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(yearParamDto.getGroupIds())).first()+"");}});
            }
            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
            //params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
        }
        params.resetComboType();

        try{
            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
            JSONObject result = feignUtil.getData(params);
            log.info("{}",result.toJSONString());
            return result;
        }catch (Exception e){
            log.warn("{}",e);
        }

        return null;
    }
    
    @Override
    public JSONObject getEveryDayData(DayParamDto dayParamDto) {
        //去掉排序
        dayParamDto.setOrderBy(null);

        ParamDto params = new ParamDto();
        params.setCode(dayOrderDayAnalyseCode);
        params.getQueryParams().setCombo_type(dayParamDto.getComboType());
        params.getRangeParams().getDay().setStart(dayParamDto.getMonth()+"-01");
        
        //params.getRangeParams().getDay().setEnd(dayParamDto.getMonth()+"-32");
        try {
        	String dateStr = dayParamDto.getMonth()+"-01"; // 要转换的日期字符串
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd"); // 定义日期格式
            Date dateObj = sdf.parse(dateStr); // 将字符串转换为日期对象
            Calendar calendar = new GregorianCalendar() ;
            calendar.setTime(dateObj);
            
//            //本月第一天
//            calendar.add(Calendar.MONTH, 0);
//            calendar.set(Calendar.DAY_OF_MONTH, 1);
//            String firstDate = sdf.format(calendar.getTime()) ;
//            System.out.println("firstDate>>"+firstDate);
            
            //本月最后一天
            calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
            String lastDate = sdf.format(calendar.getTime()) ;
            params.getRangeParams().getDay().setEnd(lastDate) ;
		} catch (Exception e) {
			log.info("设置本月最后一天异常...");
			params.getRangeParams().getDay().setEnd(dayParamDto.getMonth()+"-32");
		}

        //params.setColumnArray(QuerySql.EveryDayOrderQuerySql);
        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.EveryDayOrderQuerySql) ;
        params.setColumnArray(columnArray);

        JSONObject orderResult = feignUtil.getData(params, dayParamDto);

        params.setCode(dayMemberDataCode);
        //params.setColumnArray(QuerySql.DayMemberListSql);
        columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.DayMemberEquipmentListSql) ;
        params.setColumnArray(columnArray);
        JSONObject memberResult = feignUtil.getData(params, dayParamDto);

        Map<String,JSONObject> data = new HashMap<>();

        List<JSONObject> orders = new ArrayList<>();

        if(orderResult != null && orderResult.getInteger("code") == 200 && orderResult.getJSONObject("info") != null && orderResult.getJSONObject("info").getJSONArray("list") != null){
            JSONArray orderList = orderResult.getJSONObject("info").getJSONArray("list");
            if(orderList != null && orderList.size()>0){
                for(int i=0; i<orderList.size(); i++){
                    JSONObject obj = orderList.getJSONObject(i);
                    obj.put("memberCount",0);
                    obj.put("memberCountUp",0);
                    data.put(feignUtil.idProcess(obj.getString("provinceId"))+feignUtil.idProcess(obj.getString("cityId"))+feignUtil.idProcess(obj.getString("districtId"))
                                    +feignUtil.idProcess(obj.getString("groupId"))+feignUtil.idProcess(obj.getString("equipmentTypeId"))+feignUtil.idProcess(obj.getString("day"))
                            ,obj);
                }
            }
        }

        if(memberResult != null && memberResult.getInteger("code") == 200 && memberResult.getJSONObject("info") != null && memberResult.getJSONObject("info").getJSONArray("list") != null){
            JSONArray memberList = memberResult.getJSONObject("info").getJSONArray("list");
            if(memberList != null && memberList.size()>0){
                for(int i=0; i<memberList.size(); i++){
                    JSONObject obj = memberList.getJSONObject(i);
                    JSONObject mapObj = data.get(feignUtil.idProcess(obj.getString("provinceId"))+feignUtil.idProcess(obj.getString("cityId"))+feignUtil.idProcess(obj.getString("districtId"))
                            +feignUtil.idProcess(obj.getString("groupId"))+feignUtil.idProcess(obj.getString("equipmentTypeId"))+feignUtil.idProcess(obj.getString("day")));
                    if(mapObj == null){
                        obj.put("payAmount","0");
                        obj.put("payAmountUp","0");
                        obj.put("payCounts","0");
                        obj.put("payCountsUp","0");
                        obj.put("perOrderAmount","0");
                        obj.put("perOrderAmountUp","0");
                        data.put(feignUtil.idProcess(obj.getString("provinceId"))+feignUtil.idProcess(obj.getString("cityId"))+feignUtil.idProcess(obj.getString("districtId"))
                                        +feignUtil.idProcess(obj.getString("groupId"))+feignUtil.idProcess(obj.getString("equipmentTypeId"))+feignUtil.idProcess(obj.getString("day"))
                                ,obj);
                    }else{
                        mapObj.put("memberCount",obj.getIntValue("memberCount"));
                        mapObj.put("memberCountUp",obj.getDoubleValue("memberCountUp"));
                    }
                }
            }
        }

        if(data != null){
            Set<String> keys = data.keySet();
            //倒序
            Map<String,List<JSONObject>> treeMap = new TreeMap<>(Comparator.reverseOrder());
            //正序
            if("asc".equals(dayParamDto.getOrder())){
                treeMap = new TreeMap<>();
            }
            for(String key : keys){
                //如何排序,目前只做了按金钱排序
                JSONObject obj = data.get(key);
                /*if(obj.getString("equipmentTypeName") == null || "未知".equals(obj.getString("equipmentTypeName"))){
                    continue;
                }*/
                List<JSONObject> list = treeMap.get(obj.getString("day"));
                if(list == null){
                    list = new ArrayList<JSONObject>();
                }
                list.add(obj);
                treeMap.put(obj.getString("day"),list);
            }

            //开始按照顺序重新拼装list
            Set<String> tmKeys = treeMap.keySet();
            for(String key : tmKeys){
                orders.addAll(treeMap.get(key));
            }

        }

        orderResult.getJSONObject("info").remove("list");
        orderResult.getJSONObject("info").put("list",orders);

        orderResult.getJSONObject("info").put("totalCount",orders.size());
        return orderResult;
    }

    @Override
    public JSONObject getEveryWeekData(WeekParamDto weekParamDto) {
        //去掉排序
        weekParamDto.setOrderBy(null);

        ParamDto params = new ParamDto();
        params.setCode(weekOrderDayAnalyseCode);
        params.getQueryParams().setCombo_type(weekParamDto.getComboType());

       // params.setColumnArray(QuerySql.EveryWeekOrderQuerySql);
        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.EveryWeekOrderQuerySql) ;
        params.setColumnArray(columnArray);

        JSONObject orderResult = feignUtil.getData(params, weekParamDto);

        params.setCode(weekMemberDataCode);
        //params.setColumnArray(QuerySql.WeekMemberListSql);
        columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.WeekMemberEquipmentListSql) ;
        params.setColumnArray(columnArray);
        JSONObject memberResult = feignUtil.getData(params, weekParamDto);

        Map<String,JSONObject> data = new HashMap<>();

        List<JSONObject> orders = new ArrayList<>();
        if(orderResult == null){
            return null;
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        JSONObject weekRange = new JSONObject();
        try{
            Date date = sdf.parse(weekParamDto.getMonth());
            weekRange = TimeUtil.getMonthWeekRange(date);
        }catch (Exception e){
            log.warn("{}",e);
        }


        if(orderResult != null && orderResult.getInteger("code") == 200 && orderResult.getJSONObject("info") != null && orderResult.getJSONObject("info").getJSONArray("list") != null){
            JSONArray orderList = orderResult.getJSONObject("info").getJSONArray("list");
            if(orderList != null && orderList.size()>0){
                for(int i=0; i<orderList.size(); i++){
                    JSONObject obj = orderList.getJSONObject(i);
                    obj.put("memberCount",0);
                    obj.put("memberCountUp",0);
                    obj.put("weekRange",weekRange == null ? null:weekRange.getString(obj.getString("week")));
                    data.put(feignUtil.idProcess(obj.getString("provinceId"))+feignUtil.idProcess(obj.getString("cityId"))+feignUtil.idProcess(obj.getString("districtId"))
                                    +feignUtil.idProcess(obj.getString("groupId"))+feignUtil.idProcess(obj.getString("equipmentTypeId"))+feignUtil.idProcess(obj.getString("week"))
                            ,obj);
                }
            }
        }

        if(memberResult != null && memberResult.getInteger("code") == 200 && memberResult.getJSONObject("info") != null && memberResult.getJSONObject("info").getJSONArray("list") != null){
            JSONArray memberList = memberResult.getJSONObject("info").getJSONArray("list");
            if(memberList != null && memberList.size()>0){
                for(int i=0; i<memberList.size(); i++){
                    JSONObject obj = memberList.getJSONObject(i);
                    JSONObject mapObj = data.get(feignUtil.idProcess(obj.getString("provinceId"))+feignUtil.idProcess(obj.getString("cityId"))+feignUtil.idProcess(obj.getString("districtId"))
                            +feignUtil.idProcess(obj.getString("groupId"))+feignUtil.idProcess(obj.getString("equipmentTypeId"))+feignUtil.idProcess(obj.getString("week")));
                    if(mapObj == null){
                        obj.put("payAmount","0");
                        obj.put("payAmountUp","0");
                        obj.put("payCounts","0");
                        obj.put("payCountsUp","0");
                        obj.put("perOrderAmount","0");
                        obj.put("perOrderAmountUp","0");
                        obj.put("weekRange",weekRange == null ? null:weekRange.getString(obj.getString("week")));
                        data.put(feignUtil.idProcess(obj.getString("provinceId"))+feignUtil.idProcess(obj.getString("cityId"))+feignUtil.idProcess(obj.getString("districtId"))
                                        +feignUtil.idProcess(obj.getString("groupId"))+feignUtil.idProcess(obj.getString("equipmentTypeId"))+feignUtil.idProcess(obj.getString("week"))
                                ,obj);
                    }else{
                        mapObj.put("memberCount",obj.getIntValue("memberCount"));
                        mapObj.put("memberCountUp",obj.getDoubleValue("memberCountUp"));
                    }
                }
            }
        }

        if(data != null){
            Set<String> keys = data.keySet();
            //倒序
            Map<String,List<JSONObject>> treeMap = new TreeMap<>(Comparator.reverseOrder());
            //正序
            if("asc".equals(weekParamDto.getOrder())){
                treeMap = new TreeMap<>();
            }
            for(String key : keys){
                //如何排序,目前只做了按金钱排序
                JSONObject obj = data.get(key);
                /*if(obj.getString("equipmentTypeName") == null || "未知".equals(obj.getString("equipmentTypeName"))){
                    continue;
                }*/
                List<JSONObject> list = treeMap.get(obj.getString("week"));
                if(list == null){
                    list = new ArrayList<JSONObject>();
                }
                list.add(obj);
                treeMap.put(obj.getString("week"),list);
            }

            //开始按照顺序重新拼装list
            Set<String> tmKeys = treeMap.keySet();
            for(String key : tmKeys){
                orders.addAll(treeMap.get(key));
            }

        }

        orderResult.getJSONObject("info").remove("list");
        orderResult.getJSONObject("info").put("list",orders);

        orderResult.getJSONObject("info").put("totalCount",orders.size());
        return orderResult;
    }

    @Override
    public JSONObject getEveryMonthData(MonthParamDto monthParamDto) {
        //去掉排序
        monthParamDto.setOrderBy(null);

        ParamDto params = new ParamDto();
        params.setCode(monthOrderDayAnalyseCode);
        params.getQueryParams().setCombo_type(monthParamDto.getComboType());
        params.getRangeParams().getYear_month().setStart(monthParamDto.getYear()+"-01");
        params.getRangeParams().getYear_month().setEnd(monthParamDto.getYear()+"-12");

        //params.setColumnArray(QuerySql.EveryMonthOrderQuerySql);
        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.EveryMonthOrderQuerySql) ;
        params.setColumnArray(columnArray);
        JSONObject orderResult = feignUtil.getData(params, monthParamDto);

        params.setCode(monthMemberDataCode);
        //params.setColumnArray(QuerySql.MonthMemberListSql);
        columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.MonthMemberEquipmentListSql) ;
        params.setColumnArray(columnArray);
        JSONObject memberResult = feignUtil.getData(params, monthParamDto);

        Map<String,JSONObject> data = new HashMap<>();

        List<JSONObject> orders = new ArrayList<>();

        if(orderResult != null && orderResult.getInteger("code") == 200 && orderResult.getJSONObject("info") != null && orderResult.getJSONObject("info").getJSONArray("list") != null){
            JSONArray orderList = orderResult.getJSONObject("info").getJSONArray("list");
            if(orderList != null && orderList.size()>0){
                for(int i=0; i<orderList.size(); i++){
                    JSONObject obj = orderList.getJSONObject(i);
                    obj.put("memberCount",0);
                    obj.put("memberCountUp",0);
                    data.put(feignUtil.idProcess(obj.getString("provinceId"))+feignUtil.idProcess(obj.getString("cityId"))+feignUtil.idProcess(obj.getString("districtId"))
                                    +feignUtil.idProcess(obj.getString("groupId"))+feignUtil.idProcess(obj.getString("equipmentTypeId"))+feignUtil.idProcess(obj.getString("month"))
                            ,obj);
                }
            }
        }

        if(memberResult != null && memberResult.getInteger("code") == 200 && memberResult.getJSONObject("info") != null && memberResult.getJSONObject("info").getJSONArray("list") != null){
            JSONArray memberList = memberResult.getJSONObject("info").getJSONArray("list");
            if(memberList != null && memberList.size()>0){
                for(int i=0; i<memberList.size(); i++){
                    JSONObject obj = memberList.getJSONObject(i);
                    JSONObject mapObj = data.get(feignUtil.idProcess(obj.getString("provinceId"))+feignUtil.idProcess(obj.getString("cityId"))+feignUtil.idProcess(obj.getString("districtId"))
                            +feignUtil.idProcess(obj.getString("groupId"))+feignUtil.idProcess(obj.getString("equipmentTypeId"))+feignUtil.idProcess(obj.getString("month")));
                    if(mapObj == null){
                        obj.put("payAmount","0");
                        obj.put("payAmountUp","0");
                        obj.put("payCounts","0");
                        obj.put("payCountsUp","0");
                        obj.put("perOrderAmount","0");
                        obj.put("perOrderAmountUp","0");
                        data.put(feignUtil.idProcess(obj.getString("provinceId"))+feignUtil.idProcess(obj.getString("cityId"))+feignUtil.idProcess(obj.getString("districtId"))
                                        +feignUtil.idProcess(obj.getString("groupId"))+feignUtil.idProcess(obj.getString("equipmentTypeId"))+feignUtil.idProcess(obj.getString("day"))
                                ,obj);
                    }else{
                        mapObj.put("memberCount",obj.getIntValue("memberCount"));
                        mapObj.put("memberCountUp",obj.getDoubleValue("memberCountUp"));
                    }
                }
            }
        }

        if(data != null){
            Set<String> keys = data.keySet();
            //倒序
            Map<String,List<JSONObject>> treeMap = new TreeMap<>(Comparator.reverseOrder());
            //正序
            if("asc".equals(monthParamDto.getOrder())){
                treeMap = new TreeMap<>();
            }
            for(String key : keys){
                //如何排序,目前只做了按金钱排序
                JSONObject obj = data.get(key);
                /*if(obj.getString("equipmentTypeName") == null || "未知".equals(obj.getString("equipmentTypeName"))){
                    continue;
                }*/
                List<JSONObject> list = treeMap.get(obj.getString("month"));
                if(list == null){
                    list = new ArrayList<JSONObject>();
                }
                list.add(obj);
                treeMap.put(obj.getString("month"),list);
            }

            //开始按照顺序重新拼装list
            Set<String> tmKeys = treeMap.keySet();
            for(String key : tmKeys){
                orders.addAll(treeMap.get(key));
            }

        }

        orderResult.getJSONObject("info").remove("list");
        orderResult.getJSONObject("info").put("list",orders);

        orderResult.getJSONObject("info").put("totalCount",orders.size());
        return orderResult;
    }

    @Override
    public JSONObject getEveryYearData(YearParamDto yearParamDto) {
        //去掉排序
        yearParamDto.setOrderBy(null);

        ParamDto params = new ParamDto();
        params.setCode(yearOrderDayAnalyseCode);
        params.getQueryParams().setCombo_type(yearParamDto.getComboType());
        params.getRangeParams().getYear_str().setEnd(yearParamDto.getYear()+"");

        //params.setColumnArray(QuerySql.EveryYearOrderQuerySql);
        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.EveryYearOrderQuerySql) ;
        params.setColumnArray(columnArray);

        JSONObject orderResult = feignUtil.getData(params, yearParamDto);

        params.setCode(yearMemberDataCode);
        //params.setColumnArray(QuerySql.YearMemberListSql);
        columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.YearMemberEquipmentListSql) ;
        params.setColumnArray(columnArray);
        JSONObject memberResult = feignUtil.getData(params, yearParamDto);

        Map<String,JSONObject> data = new HashMap<>();

        List<JSONObject> orders = new ArrayList<>();

        if(orderResult != null && orderResult.getInteger("code") == 200 && orderResult.getJSONObject("info") != null && orderResult.getJSONObject("info").getJSONArray("list") != null){
            JSONArray orderList = orderResult.getJSONObject("info").getJSONArray("list");
            if(orderList != null && orderList.size()>0){
                for(int i=0; i<orderList.size(); i++){
                    JSONObject obj = orderList.getJSONObject(i);
                    obj.put("memberCount",0);
                    obj.put("memberCountUp",0);
                    data.put(feignUtil.idProcess(obj.getString("provinceId"))+feignUtil.idProcess(obj.getString("cityId"))+feignUtil.idProcess(obj.getString("districtId"))
                                    +feignUtil.idProcess(obj.getString("groupId"))+feignUtil.idProcess(obj.getString("equipmentTypeId"))+feignUtil.idProcess(obj.getString("year"))
                            ,obj);
                }
            }
        }

        if(memberResult != null && memberResult.getInteger("code") == 200 && memberResult.getJSONObject("info") != null && memberResult.getJSONObject("info").getJSONArray("list") != null){
            JSONArray memberList = memberResult.getJSONObject("info").getJSONArray("list");
            if(memberList != null && memberList.size()>0){
                for(int i=0; i<memberList.size(); i++){
                    JSONObject obj = memberList.getJSONObject(i);
                    JSONObject mapObj = data.get(feignUtil.idProcess(obj.getString("provinceId"))+feignUtil.idProcess(obj.getString("cityId"))+feignUtil.idProcess(obj.getString("districtId"))
                            +feignUtil.idProcess(obj.getString("groupId"))+feignUtil.idProcess(obj.getString("equipmentTypeId"))+feignUtil.idProcess(obj.getString("year")));
                    if(mapObj == null){
                        obj.put("payAmount","0");
                        obj.put("payAmountUp","0");
                        obj.put("payCounts","0");
                        obj.put("payCountsUp","0");
                        obj.put("perOrderAmount","0");
                        obj.put("perOrderAmountUp","0");
                        data.put(feignUtil.idProcess(obj.getString("provinceId"))+feignUtil.idProcess(obj.getString("cityId"))+feignUtil.idProcess(obj.getString("districtId"))
                                        +feignUtil.idProcess(obj.getString("groupId"))+feignUtil.idProcess(obj.getString("equipmentTypeId"))+feignUtil.idProcess(obj.getString("day"))
                                ,obj);
                    }else{
                        mapObj.put("memberCount",obj.getIntValue("memberCount"));
                        mapObj.put("memberCountUp",obj.getDoubleValue("memberCountUp"));
                    }
                }
            }
        }

        if(data != null){
            Set<String> keys = data.keySet();
            //倒序
            Map<String,List<JSONObject>> treeMap = new TreeMap<>(Comparator.reverseOrder());
            //正序
            if("asc".equals(yearParamDto.getOrder())){
                treeMap = new TreeMap<>();
            }
            for(String key : keys){
                //如何排序,目前只做了按金钱排序
                JSONObject obj = data.get(key);
                /*if(obj.getString("equipmentTypeName") == null || "未知".equals(obj.getString("equipmentTypeName"))){
                    continue;
                }*/
                List<JSONObject> list = treeMap.get(obj.getString("year"));
                if(list == null){
                    list = new ArrayList<JSONObject>();
                }
                list.add(obj);
                treeMap.put(obj.getString("year"),list);
            }

            //开始按照顺序重新拼装list
            Set<String> tmKeys = treeMap.keySet();
            for(String key : tmKeys){
                orders.addAll(treeMap.get(key));
            }

        }

        orderResult.getJSONObject("info").remove("list");
        orderResult.getJSONObject("info").put("list",orders);

        orderResult.getJSONObject("info").put("totalCount",orders.size());
        return orderResult;
    }
    
    @SuppressWarnings({ "serial", "rawtypes", "unused" })
    @Override
    public JSONObject getPerOrderPriceCount(DayParamDto dayParamDto) {
        ParamDto params = new ParamDto();
        params.setCode(dayOrderDayAnalyseCode);
        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.DAY_PER_ORDER_PRICE_COUNT) ;
        params.setColumnArray(columnArray);
        params.getQueryParams().setDay(dayParamDto.getDay());
        params.getQueryParams().setMerchant_id(dayParamDto.getMerchantId()+"");
        params.getQueryParams().setCombo_type(dayParamDto.getComboType());

        params.getQueryParams().setProvince_id(dayParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(dayParamDto.getProvinceId()+"");}});
        params.getQueryParams().setCity_id(dayParamDto.getCityId() == null ? null:new HashSet<String>(){{add(dayParamDto.getCityId()+"");}});
        params.getQueryParams().setDistrict_id(dayParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(dayParamDto.getAreaId()+"");}});
        params.getQueryParams().setEquipment_group_id(dayParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(dayParamDto.getGroupId()+"");}});
        params.getQueryParams().setLyy_equipment_type_id(dayParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(dayParamDto.getEquipmentTypeId()+"");}});

        if(dayParamDto.getGroupIds() != null && dayParamDto.getGroupIds().size()>0){
            if(dayParamDto.getGroupId() == null || !dayParamDto.getGroupIds().contains(dayParamDto.getGroupId()+"")){
                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(dayParamDto.getGroupIds())).first()+"");}});
            }
            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
            //params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
        }
        params.resetComboType();

        try{
            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
            JSONObject result = feignUtil.getData(params);
            log.info("{}",result.toJSONString());

            //动态返回商家6个区间
            if(result != null && "200".equals(result.getString("code")) && result.getJSONObject("info") != null){
                if(result.getJSONObject("info").getJSONArray("list") != null && result.getJSONObject("info").getJSONArray("list").size()>0){
                    JSONObject obj = result.getJSONObject("info").getJSONArray("list").getJSONObject(0);
                    JSONObject newObj = new JSONObject();
                    JSONArray data = new JSONArray();
                    Map<Integer,String> map = new HashMap<>();
                    Map<Integer,String> payMap = new HashMap<>();
                    Map<Integer,String> svgMap = new HashMap<>();
                    Map<Integer,String> newMap = new HashMap<>();
                    map.put(1,"pay_0_1_count");
                    map.put(2,"pay_1_3_count");
                    map.put(3,"pay_3_5_count");
                    map.put(4,"pay_5_10_count");
                    map.put(5,"pay_10_15_count");
                    map.put(6,"pay_15_30_count");
                    map.put(7,"pay_30_50_count");
                    map.put(8,"pay_50_100_count");
                    map.put(9,"pay_100_200_count");
                    map.put(10,"pay_200_500_count");
                    map.put(11,"pay_500_1000_count");
                    map.put(12,"over_1000_count");

                    payMap.put(1,"pay_0_1_amount");
                    payMap.put(2,"pay_1_3_amount");
                    payMap.put(3,"pay_3_5_amount");
                    payMap.put(4,"pay_5_10_amount");
                    payMap.put(5,"pay_10_15_amount");
                    payMap.put(6,"pay_15_30_amount");
                    payMap.put(7,"pay_30_50_amount");
                    payMap.put(8,"pay_50_100_amount");
                    payMap.put(9,"pay_100_200_amount");
                    payMap.put(10,"pay_200_500_amount");
                    payMap.put(11,"pay_500_1000_amount");
                    payMap.put(12,"over_1000_amount");

                    svgMap.put(1,"payOrderPrice0_1");
                    svgMap.put(2,"payOrderPrice1_3");
                    svgMap.put(3,"payOrderPrice3_5");
                    svgMap.put(4,"payOrderPrice5_10");
                    svgMap.put(5,"payOrderPrice10_15");
                    svgMap.put(6,"payOrderPrice15_30");
                    svgMap.put(7,"payOrderPrice30_50");
                    svgMap.put(8,"payOrderPrice50_100");
                    svgMap.put(9,"payOrderPrice100_200");
                    svgMap.put(10,"payOrderPrice200_500");
                    svgMap.put(11,"payOrderPrice500_1000");
                    svgMap.put(12,"payOrderPriceOver1000");



                    newMap.put(1,"0-1");
                    newMap.put(2,"1-3");
                    newMap.put(3,"3-5");
                    newMap.put(4,"5-10");
                    newMap.put(5,"10-15");
                    newMap.put(6,"15-30");
                    newMap.put(7,"30-50");
                    newMap.put(8,"50-100");
                    newMap.put(9,"100-200");
                    newMap.put(10,"200-500");
                    newMap.put(11,"500-1000");
                    newMap.put(12,"1000以上");
                    int start = 0;
                    int count = 0;
                    int end = 0;
                    if(obj.getInteger("pay_0_1_count")>0){
                        start = 1;
                        end = 1;
                        count++;
                    }
                    if(obj.getInteger("pay_1_3_count")>0){
                        if(start <= 0){
                            start = 2;
                        }
                        end = 2;
                        count++;
                    }
                    if(obj.getInteger("pay_3_5_count")>0){
                        if(start <= 0){
                            start = 3;
                        }
                        end = 3;
                        count++;
                    }
                    if(obj.getInteger("pay_5_10_count")>0){
                        if(start <= 0){
                            start = 4;
                        }
                        end = 4;
                        count++;
                    }
                    if(obj.getInteger("pay_10_15_count")>0){
                        if(start <= 0){
                            start = 5;
                        }
                        end = 5;
                        count++;
                    }
                    if(obj.getInteger("pay_15_30_count")>0){
                        if(start <= 0){
                            start = 6;
                        }
                        end = 6;
                        count++;
                    }
                    if(obj.getInteger("pay_30_50_count")>0){
                        if(start <= 0){
                            start = 7;
                        }
                        end = 7;
                        count++;
                    }
                    if(obj.getInteger("pay_50_100_count")>0){
                        if(start <= 0){
                            start = 8;
                        }
                        end = 8;
                        count++;
                    }
                    if(obj.getInteger("pay_100_200_count")>0){
                        if(start <= 0){
                            start = 9;
                        }
                        end = 9;
                        count++;
                    }
                    if(obj.getInteger("pay_200_500_count")>0){
                        if(start <= 0){
                            start = 10;
                        }
                        end = 10;
                        count++;
                    }
                    if(obj.getInteger("pay_500_1000_count")>0){
                        if(start <= 0){
                            start = 11;
                        }
                        end = 11;
                        count++;
                    }
                    if(obj.getInteger("over_1000_count")>0){
                        if(start <= 0){
                            start = 12;
                        }
                        end = 12;
                        count++;
                    }
                    //刚刚好6个档次区间
                    if(end - start ==5){
                        for(int i=start; i<=end; i++){
                            newObj.put("show",newMap.get(i));
                            newObj.put("orderCount",obj.getIntValue(map.get(i)));
                            newObj.put("payAmount",obj.getDoubleValue(payMap.get(i)));
                            newObj.put("svgPrice",obj.getDoubleValue(svgMap.get(i)));
                            data.add(newObj);
                            newObj = new JSONObject();
                        }
                    }

                    //如果少于6个，且网上能够补齐6个
                    if(end - start < 5){
                        //网上区间够补齐6个
                        if(start<=7){
                            for(int i=start; i<=start+5; i++){
                                newObj.put("show",newMap.get(i));
                                newObj.put("orderCount",obj.getIntValue(map.get(i)));
                                newObj.put("payAmount",obj.getDoubleValue(payMap.get(i)));
                                newObj.put("svgPrice",obj.getDoubleValue(svgMap.get(i)));
                                data.add(newObj);
                                newObj = new JSONObject();
                            }
                        }
                        //往上不够补，往下补
                        if(start>7){
                            for(int i=7; i<=12; i++){
                                newObj.put("show",newMap.get(i));
                                newObj.put("orderCount",obj.getIntValue(map.get(i)));
                                newObj.put("payAmount",obj.getDoubleValue(payMap.get(i)));
                                newObj.put("svgPrice",obj.getDoubleValue(svgMap.get(i)));
                                data.add(newObj);
                                newObj = new JSONObject();
                            }
                        }

                    }

                    //大于6个档次
                    if(end - start > 5){
                        for(int i=start; i<start+5; i++){
                            newObj.put("show",newMap.get(i));
                            newObj.put("orderCount",obj.getIntValue(map.get(i)));
                            newObj.put("payAmount",obj.getDoubleValue(payMap.get(i)));
                            newObj.put("svgPrice",obj.getDoubleValue(svgMap.get(i)));
                            data.add(newObj);
                            newObj = new JSONObject();
                        }
                        int lastCounts = 0;
                        double lastPays = 0;
                        for(int i=start+5; i<=end; i++){
                            lastCounts += obj.getIntValue(map.get(i));
                            lastPays += obj.getDoubleValue(payMap.get(i));
                        }
                        DecimalFormat decimalFormat = new DecimalFormat("#.00");
                        newObj.put("show",newMap.get(start+5).split("-")[0]+"以上");
                        newObj.put("orderCount",lastCounts);
                        newObj.put("payAmount",lastPays);
                        newObj.put("svgPrice",decimalFormat.format(lastPays/lastCounts));
                        data.add(newObj);
                    }
                    result.getJSONObject("info").put("totalCount",6);
                    result.getJSONObject("info").remove("list");
                    result.getJSONObject("info").put("list",data);

                }
            }
            return result;
        }catch (Exception e){
            log.warn("{}",e);
        }



        return null;
    }

    @SuppressWarnings({ "serial", "rawtypes", "unused" })
    @Override
    public JSONObject getWeekPerOrderPriceCount(WeekParamDto weekParamDto) {
        ParamDto params = new ParamDto();
        params.setCode(weekOrderDayAnalyseCode);
        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.WEEK_PER_ORDER_PRICE_COUNT) ;
        params.setColumnArray(columnArray);
        params.getQueryParams().setYear_month(weekParamDto.getMonth());
        params.getQueryParams().setMonth_week_num(weekParamDto.getWeek()+"");
        params.getQueryParams().setMerchant_id(weekParamDto.getMerchantId()+"");
        params.getQueryParams().setCombo_type(weekParamDto.getComboType());

        params.getQueryParams().setProvince_id(weekParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(weekParamDto.getProvinceId()+"");}});
        params.getQueryParams().setCity_id(weekParamDto.getCityId() == null ? null:new HashSet<String>(){{add(weekParamDto.getCityId()+"");}});
        params.getQueryParams().setDistrict_id(weekParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(weekParamDto.getAreaId()+"");}});
        params.getQueryParams().setEquipment_group_id(weekParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(weekParamDto.getGroupId()+"");}});
        params.getQueryParams().setLyy_equipment_type_id(weekParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(weekParamDto.getEquipmentTypeId()+"");}});

        if(weekParamDto.getGroupIds() != null && weekParamDto.getGroupIds().size()>0){
            if(weekParamDto.getGroupId() == null || !weekParamDto.getGroupIds().contains(weekParamDto.getGroupId()+"")){
                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(weekParamDto.getGroupIds())).first()+"");}});
            }
            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
           // params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
        }
        params.resetComboType();
        
        try{
            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
            JSONObject result = feignUtil.getData(params);
            log.info("{}",result.toJSONString());

            //动态返回商家6个区间
            if(result != null && "200".equals(result.getString("code")) && result.getJSONObject("info") != null){
                if(result.getJSONObject("info").getJSONArray("list") != null && result.getJSONObject("info").getJSONArray("list").size()>0){
                    JSONObject obj = result.getJSONObject("info").getJSONArray("list").getJSONObject(0);
                    JSONObject newObj = new JSONObject();
                    JSONArray data = new JSONArray();
                    Map<Integer,String> map = new HashMap<>();
                    Map<Integer,String> payMap = new HashMap<>();
                    Map<Integer,String> svgMap = new HashMap<>();
                    Map<Integer,String> newMap = new HashMap<>();
                    map.put(1,"pay_0_1_count");
                    map.put(2,"pay_1_3_count");
                    map.put(3,"pay_3_5_count");
                    map.put(4,"pay_5_10_count");
                    map.put(5,"pay_10_15_count");
                    map.put(6,"pay_15_30_count");
                    map.put(7,"pay_30_50_count");
                    map.put(8,"pay_50_100_count");
                    map.put(9,"pay_100_200_count");
                    map.put(10,"pay_200_500_count");
                    map.put(11,"pay_500_1000_count");
                    map.put(12,"over_1000_count");

                    payMap.put(1,"pay_0_1_amount");
                    payMap.put(2,"pay_1_3_amount");
                    payMap.put(3,"pay_3_5_amount");
                    payMap.put(4,"pay_5_10_amount");
                    payMap.put(5,"pay_10_15_amount");
                    payMap.put(6,"pay_15_30_amount");
                    payMap.put(7,"pay_30_50_amount");
                    payMap.put(8,"pay_50_100_amount");
                    payMap.put(9,"pay_100_200_amount");
                    payMap.put(10,"pay_200_500_amount");
                    payMap.put(11,"pay_500_1000_amount");
                    payMap.put(12,"over_1000_amount");

                    svgMap.put(1,"payOrderPrice0_1");
                    svgMap.put(2,"payOrderPrice1_3");
                    svgMap.put(3,"payOrderPrice3_5");
                    svgMap.put(4,"payOrderPrice5_10");
                    svgMap.put(5,"payOrderPrice10_15");
                    svgMap.put(6,"payOrderPrice15_30");
                    svgMap.put(7,"payOrderPrice30_50");
                    svgMap.put(8,"payOrderPrice50_100");
                    svgMap.put(9,"payOrderPrice100_200");
                    svgMap.put(10,"payOrderPrice200_500");
                    svgMap.put(11,"payOrderPrice500_1000");
                    svgMap.put(12,"payOrderPriceOver1000");

                    newMap.put(1,"0-1");
                    newMap.put(2,"1-3");
                    newMap.put(3,"3-5");
                    newMap.put(4,"5-10");
                    newMap.put(5,"10-15");
                    newMap.put(6,"15-30");
                    newMap.put(7,"30-50");
                    newMap.put(8,"50-100");
                    newMap.put(9,"100-200");
                    newMap.put(10,"200-500");
                    newMap.put(11,"500-1000");
                    newMap.put(12,"1000以上");
                    int start = 0;
                    int count = 0;
                    int end = 0;
                    if(obj.getInteger("pay_0_1_count")>0){
                        start = 1;
                        end = 1;
                        count++;
                    }
                    if(obj.getInteger("pay_1_3_count")>0){
                        if(start <= 0){
                            start = 2;
                        }
                        end = 2;
                        count++;
                    }
                    if(obj.getInteger("pay_3_5_count")>0){
                        if(start <= 0){
                            start = 3;
                        }
                        end = 3;
                        count++;
                    }
                    if(obj.getInteger("pay_5_10_count")>0){
                        if(start <= 0){
                            start = 4;
                        }
                        end = 4;
                        count++;
                    }
                    if(obj.getInteger("pay_10_15_count")>0){
                        if(start <= 0){
                            start = 5;
                        }
                        end = 5;
                        count++;
                    }
                    if(obj.getInteger("pay_15_30_count")>0){
                        if(start <= 0){
                            start = 6;
                        }
                        end = 6;
                        count++;
                    }
                    if(obj.getInteger("pay_30_50_count")>0){
                        if(start <= 0){
                            start = 7;
                        }
                        end = 7;
                        count++;
                    }
                    if(obj.getInteger("pay_50_100_count")>0){
                        if(start <= 0){
                            start = 8;
                        }
                        end = 8;
                        count++;
                    }
                    if(obj.getInteger("pay_100_200_count")>0){
                        if(start <= 0){
                            start = 9;
                        }
                        end = 9;
                        count++;
                    }
                    if(obj.getInteger("pay_200_500_count")>0){
                        if(start <= 0){
                            start = 10;
                        }
                        end = 10;
                        count++;
                    }
                    if(obj.getInteger("pay_500_1000_count")>0){
                        if(start <= 0){
                            start = 11;
                        }
                        end = 11;
                        count++;
                    }
                    if(obj.getInteger("over_1000_count")>0){
                        if(start <= 0){
                            start = 12;
                        }
                        end = 12;
                        count++;
                    }
                    //刚刚好6个档次区间
                    if(end - start ==5){
                        for(int i=start; i<=end; i++){
                            newObj.put("show",newMap.get(i));
                            newObj.put("orderCount",obj.getIntValue(map.get(i)));
                            newObj.put("payAmount",obj.getDoubleValue(payMap.get(i)));
                            newObj.put("svgPrice",obj.getDoubleValue(svgMap.get(i)));
                            data.add(newObj);
                            newObj = new JSONObject();
                        }
                    }

                    //如果少于6个，且网上能够补齐6个
                    if(end - start < 5){
                        //网上区间够补齐6个
                        if(start<=7){
                            for(int i=start; i<=start+5; i++){
                                newObj.put("show",newMap.get(i));
                                newObj.put("orderCount",obj.getIntValue(map.get(i)));
                                newObj.put("payAmount",obj.getDoubleValue(payMap.get(i)));
                                newObj.put("svgPrice",obj.getDoubleValue(svgMap.get(i)));
                                data.add(newObj);
                                newObj = new JSONObject();
                            }
                        }
                        //往上不够补，往下补
                        if(start>7){
                            for(int i=7; i<=12; i++){
                                newObj.put("show",newMap.get(i));
                                newObj.put("orderCount",obj.getIntValue(map.get(i)));
                                newObj.put("payAmount",obj.getDoubleValue(payMap.get(i)));
                                newObj.put("svgPrice",obj.getDoubleValue(svgMap.get(i)));
                                data.add(newObj);
                                newObj = new JSONObject();
                            }
                        }

                    }

                    //大于6个档次
                    if(end - start > 5){
                        for(int i=start; i<start+5; i++){
                            newObj.put("show",newMap.get(i));
                            newObj.put("orderCount",obj.getIntValue(map.get(i)));
                            newObj.put("payAmount",obj.getDoubleValue(payMap.get(i)));
                            newObj.put("svgPrice",obj.getDoubleValue(svgMap.get(i)));
                            data.add(newObj);
                            newObj = new JSONObject();
                        }
                        int lastCounts = 0;
                        double lastPays = 0;
                        for(int i=start+5; i<=end; i++){
                            lastCounts += obj.getIntValue(map.get(i));
                            lastPays += obj.getDoubleValue(payMap.get(i));
                        }
                        DecimalFormat decimalFormat = new DecimalFormat("#.00");
                        newObj.put("show",newMap.get(start+5).split("-")[0]+"以上");
                        newObj.put("orderCount",lastCounts);
                        newObj.put("payAmount",lastPays);
                        newObj.put("svgPrice",decimalFormat.format(lastPays/lastCounts));
                        data.add(newObj);
                    }
                    result.getJSONObject("info").put("totalCount",6);
                    result.getJSONObject("info").remove("list");
                    result.getJSONObject("info").put("list",data);

                }
            }
            return result;
        }catch (Exception e){
            log.warn("{}",e);
        }

        return null;
    }

    @SuppressWarnings({ "serial", "rawtypes", "unused" })
    @Override
    public JSONObject getMonthPerOrderPriceCount(MonthParamDto monthParamDto) {
        ParamDto params = new ParamDto();
        params.setCode(monthOrderDayAnalyseCode);
        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.MONTH_PER_ORDER_PRICE_COUNT) ;
        params.setColumnArray(columnArray);
        params.getQueryParams().setYear_month(monthParamDto.getMonth());
        params.getQueryParams().setMerchant_id(monthParamDto.getMerchantId()+"");
        params.getQueryParams().setCombo_type(monthParamDto.getComboType());
        params.getQueryParams().setProvince_id(monthParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(monthParamDto.getProvinceId()+"");}});
        params.getQueryParams().setCity_id(monthParamDto.getCityId() == null ? null:new HashSet<String>(){{add(monthParamDto.getCityId()+"");}});
        params.getQueryParams().setDistrict_id(monthParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(monthParamDto.getAreaId()+"");}});
        params.getQueryParams().setEquipment_group_id(monthParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(monthParamDto.getGroupId()+"");}});
        params.getQueryParams().setLyy_equipment_type_id(monthParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(monthParamDto.getEquipmentTypeId()+"");}});

        if(monthParamDto.getGroupIds() != null && monthParamDto.getGroupIds().size()>0){
            if(monthParamDto.getGroupId() == null || !monthParamDto.getGroupIds().contains(monthParamDto.getGroupId()+"")){
                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(monthParamDto.getGroupIds())).first()+"");}});
            }
            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
            //params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
        }
        params.resetComboType();

        try{
            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
            JSONObject result = feignUtil.getData(params);
            log.info("{}",result.toJSONString());

            //动态返回商家6个区间
            if(result != null && "200".equals(result.getString("code")) && result.getJSONObject("info") != null){
                if(result.getJSONObject("info").getJSONArray("list") != null && result.getJSONObject("info").getJSONArray("list").size()>0){
                    JSONObject obj = result.getJSONObject("info").getJSONArray("list").getJSONObject(0);
                    JSONObject newObj = new JSONObject();
                    JSONArray data = new JSONArray();
                    Map<Integer,String> map = new HashMap<>();
                    Map<Integer,String> payMap = new HashMap<>();
                    Map<Integer,String> svgMap = new HashMap<>();
                    Map<Integer,String> newMap = new HashMap<>();
                    map.put(1,"pay_0_1_count");
                    map.put(2,"pay_1_3_count");
                    map.put(3,"pay_3_5_count");
                    map.put(4,"pay_5_10_count");
                    map.put(5,"pay_10_15_count");
                    map.put(6,"pay_15_30_count");
                    map.put(7,"pay_30_50_count");
                    map.put(8,"pay_50_100_count");
                    map.put(9,"pay_100_200_count");
                    map.put(10,"pay_200_500_count");
                    map.put(11,"pay_500_1000_count");
                    map.put(12,"over_1000_count");

                    payMap.put(1,"pay_0_1_amount");
                    payMap.put(2,"pay_1_3_amount");
                    payMap.put(3,"pay_3_5_amount");
                    payMap.put(4,"pay_5_10_amount");
                    payMap.put(5,"pay_10_15_amount");
                    payMap.put(6,"pay_15_30_amount");
                    payMap.put(7,"pay_30_50_amount");
                    payMap.put(8,"pay_50_100_amount");
                    payMap.put(9,"pay_100_200_amount");
                    payMap.put(10,"pay_200_500_amount");
                    payMap.put(11,"pay_500_1000_amount");
                    payMap.put(12,"over_1000_amount");

                    svgMap.put(1,"payOrderPrice0_1");
                    svgMap.put(2,"payOrderPrice1_3");
                    svgMap.put(3,"payOrderPrice3_5");
                    svgMap.put(4,"payOrderPrice5_10");
                    svgMap.put(5,"payOrderPrice10_15");
                    svgMap.put(6,"payOrderPrice15_30");
                    svgMap.put(7,"payOrderPrice30_50");
                    svgMap.put(8,"payOrderPrice50_100");
                    svgMap.put(9,"payOrderPrice100_200");
                    svgMap.put(10,"payOrderPrice200_500");
                    svgMap.put(11,"payOrderPrice500_1000");
                    svgMap.put(12,"payOrderPriceOver1000");

                    newMap.put(1,"0-1");
                    newMap.put(2,"1-3");
                    newMap.put(3,"3-5");
                    newMap.put(4,"5-10");
                    newMap.put(5,"10-15");
                    newMap.put(6,"15-30");
                    newMap.put(7,"30-50");
                    newMap.put(8,"50-100");
                    newMap.put(9,"100-200");
                    newMap.put(10,"200-500");
                    newMap.put(11,"500-1000");
                    newMap.put(12,"1000以上");
                    int start = 0;
                    int count = 0;
                    int end = 0;
                    if(obj.getInteger("pay_0_1_count")>0){
                        start = 1;
                        end = 1;
                        count++;
                    }
                    if(obj.getInteger("pay_1_3_count")>0){
                        if(start <= 0){
                            start = 2;
                        }
                        end = 2;
                        count++;
                    }
                    if(obj.getInteger("pay_3_5_count")>0){
                        if(start <= 0){
                            start = 3;
                        }
                        end = 3;
                        count++;
                    }
                    if(obj.getInteger("pay_5_10_count")>0){
                        if(start <= 0){
                            start = 4;
                        }
                        end = 4;
                        count++;
                    }
                    if(obj.getInteger("pay_10_15_count")>0){
                        if(start <= 0){
                            start = 5;
                        }
                        end = 5;
                        count++;
                    }
                    if(obj.getInteger("pay_15_30_count")>0){
                        if(start <= 0){
                            start = 6;
                        }
                        end = 6;
                        count++;
                    }
                    if(obj.getInteger("pay_30_50_count")>0){
                        if(start <= 0){
                            start = 7;
                        }
                        end = 7;
                        count++;
                    }
                    if(obj.getInteger("pay_50_100_count")>0){
                        if(start <= 0){
                            start = 8;
                        }
                        end = 8;
                        count++;
                    }
                    if(obj.getInteger("pay_100_200_count")>0){
                        if(start <= 0){
                            start = 9;
                        }
                        end = 9;
                        count++;
                    }
                    if(obj.getInteger("pay_200_500_count")>0){
                        if(start <= 0){
                            start = 10;
                        }
                        end = 10;
                        count++;
                    }
                    if(obj.getInteger("pay_500_1000_count")>0){
                        if(start <= 0){
                            start = 11;
                        }
                        end = 11;
                        count++;
                    }
                    if(obj.getInteger("over_1000_count")>0){
                        if(start <= 0){
                            start = 12;
                        }
                        end = 12;
                        count++;
                    }
                    //刚刚好6个档次区间
                    if(end - start ==5){
                        for(int i=start; i<=end; i++){
                            newObj.put("show",newMap.get(i));
                            newObj.put("orderCount",obj.getIntValue(map.get(i)));
                            newObj.put("payAmount",obj.getDoubleValue(payMap.get(i)));
                            newObj.put("svgPrice",obj.getDoubleValue(svgMap.get(i)));
                            data.add(newObj);
                            newObj = new JSONObject();
                        }
                    }

                    //如果少于6个，且网上能够补齐6个
                    if(end - start < 5){
                        //网上区间够补齐6个
                        if(start<=7){
                            for(int i=start; i<=start+5; i++){
                                newObj.put("show",newMap.get(i));
                                newObj.put("orderCount",obj.getIntValue(map.get(i)));
                                newObj.put("payAmount",obj.getDoubleValue(payMap.get(i)));
                                newObj.put("svgPrice",obj.getDoubleValue(svgMap.get(i)));
                                data.add(newObj);
                                newObj = new JSONObject();
                            }
                        }
                        //往上不够补，往下补
                        if(start>7){
                            for(int i=7; i<=12; i++){
                                newObj.put("show",newMap.get(i));
                                newObj.put("orderCount",obj.getIntValue(map.get(i)));
                                newObj.put("payAmount",obj.getDoubleValue(payMap.get(i)));
                                newObj.put("svgPrice",obj.getDoubleValue(svgMap.get(i)));
                                data.add(newObj);
                                newObj = new JSONObject();
                            }
                        }

                    }

                    //大于6个档次
                    if(end - start > 5){
                        for(int i=start; i<start+5; i++){
                            newObj.put("show",newMap.get(i));
                            newObj.put("orderCount",obj.getIntValue(map.get(i)));
                            newObj.put("payAmount",obj.getDoubleValue(payMap.get(i)));
                            newObj.put("svgPrice",obj.getDoubleValue(svgMap.get(i)));
                            data.add(newObj);
                            newObj = new JSONObject();
                        }
                        int lastCounts = 0;
                        double lastPays = 0;
                        for(int i=start+5; i<=end; i++){
                            lastCounts += obj.getIntValue(map.get(i));
                            lastPays += obj.getDoubleValue(payMap.get(i));
                        }

                        DecimalFormat decimalFormat = new DecimalFormat("#.00");
                        newObj.put("show",newMap.get(start+5).split("-")[0]+"以上");
                        newObj.put("orderCount",lastCounts);
                        newObj.put("payAmount",lastPays);
                        newObj.put("svgPrice",decimalFormat.format(lastPays/lastCounts));
                        data.add(newObj);
                    }
                    result.getJSONObject("info").put("totalCount",6);
                    result.getJSONObject("info").remove("list");
                    result.getJSONObject("info").put("list",data);

                }
            }
            return result;
        }catch (Exception e){
            log.warn("{}",e);
        }



        return null;
    }

    @SuppressWarnings({ "serial", "rawtypes", "unused" })
    @Override
    public JSONObject getYearPerOrderPriceCount(YearParamDto yearParamDto) {
        ParamDto params = new ParamDto();
        params.setCode(yearOrderDayAnalyseCode);
        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.YEAR_PER_ORDER_PRICE_COUNT) ;
        params.setColumnArray(columnArray);
        params.getQueryParams().setYear_str(yearParamDto.getYear()+"");
        params.getQueryParams().setMerchant_id(yearParamDto.getMerchantId()+"");
        params.getQueryParams().setCombo_type(yearParamDto.getComboType());
        params.getQueryParams().setProvince_id(yearParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(yearParamDto.getProvinceId()+"");}});
        params.getQueryParams().setCity_id(yearParamDto.getCityId() == null ? null:new HashSet<String>(){{add(yearParamDto.getCityId()+"");}});
        params.getQueryParams().setDistrict_id(yearParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(yearParamDto.getAreaId()+"");}});
        params.getQueryParams().setEquipment_group_id(yearParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(yearParamDto.getGroupId()+"");}});
        params.getQueryParams().setLyy_equipment_type_id(yearParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(yearParamDto.getEquipmentTypeId()+"");}});

        if(yearParamDto.getGroupIds() != null && yearParamDto.getGroupIds().size()>0){
            if(yearParamDto.getGroupId() == null || !yearParamDto.getGroupIds().contains(yearParamDto.getGroupId()+"")){
                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(yearParamDto.getGroupIds())).first()+"");}});
            }
            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
            //params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
        }
        params.resetComboType();
        
        try{
            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
            JSONObject result = feignUtil.getData(params);
            log.info("{}",result.toJSONString());

            //动态返回商家6个区间
            if(result != null && "200".equals(result.getString("code")) && result.getJSONObject("info") != null){
                if(result.getJSONObject("info").getJSONArray("list") != null && result.getJSONObject("info").getJSONArray("list").size()>0){
                    JSONObject obj = result.getJSONObject("info").getJSONArray("list").getJSONObject(0);
                    JSONObject newObj = new JSONObject();
                    JSONArray data = new JSONArray();
                    Map<Integer,String> map = new HashMap<>();
                    Map<Integer,String> payMap = new HashMap<>();
                    Map<Integer,String> svgMap = new HashMap<>();
                    Map<Integer,String> newMap = new HashMap<>();
                    map.put(1,"pay_0_1_count");
                    map.put(2,"pay_1_3_count");
                    map.put(3,"pay_3_5_count");
                    map.put(4,"pay_5_10_count");
                    map.put(5,"pay_10_15_count");
                    map.put(6,"pay_15_30_count");
                    map.put(7,"pay_30_50_count");
                    map.put(8,"pay_50_100_count");
                    map.put(9,"pay_100_200_count");
                    map.put(10,"pay_200_500_count");
                    map.put(11,"pay_500_1000_count");
                    map.put(12,"over_1000_count");

                    payMap.put(1,"pay_0_1_amount");
                    payMap.put(2,"pay_1_3_amount");
                    payMap.put(3,"pay_3_5_amount");
                    payMap.put(4,"pay_5_10_amount");
                    payMap.put(5,"pay_10_15_amount");
                    payMap.put(6,"pay_15_30_amount");
                    payMap.put(7,"pay_30_50_amount");
                    payMap.put(8,"pay_50_100_amount");
                    payMap.put(9,"pay_100_200_amount");
                    payMap.put(10,"pay_200_500_amount");
                    payMap.put(11,"pay_500_1000_amount");
                    payMap.put(12,"over_1000_amount");

                    svgMap.put(1,"payOrderPrice0_1");
                    svgMap.put(2,"payOrderPrice1_3");
                    svgMap.put(3,"payOrderPrice3_5");
                    svgMap.put(4,"payOrderPrice5_10");
                    svgMap.put(5,"payOrderPrice10_15");
                    svgMap.put(6,"payOrderPrice15_30");
                    svgMap.put(7,"payOrderPrice30_50");
                    svgMap.put(8,"payOrderPrice50_100");
                    svgMap.put(9,"payOrderPrice100_200");
                    svgMap.put(10,"payOrderPrice200_500");
                    svgMap.put(11,"payOrderPrice500_1000");
                    svgMap.put(12,"payOrderPriceOver1000");

                    newMap.put(1,"0-1");
                    newMap.put(2,"1-3");
                    newMap.put(3,"3-5");
                    newMap.put(4,"5-10");
                    newMap.put(5,"10-15");
                    newMap.put(6,"15-30");
                    newMap.put(7,"30-50");
                    newMap.put(8,"50-100");
                    newMap.put(9,"100-200");
                    newMap.put(10,"200-500");
                    newMap.put(11,"500-1000");
                    newMap.put(12,"1000以上");
                    int start = 0;
                    int count = 0;
                    int end = 0;
                    if(obj.getInteger("pay_0_1_count")>0){
                        start = 1;
                        end = 1;
                        count++;
                    }
                    if(obj.getInteger("pay_1_3_count")>0){
                        if(start <= 0){
                            start = 2;
                        }
                        end = 2;
                        count++;
                    }
                    if(obj.getInteger("pay_3_5_count")>0){
                        if(start <= 0){
                            start = 3;
                        }
                        end = 3;
                        count++;
                    }
                    if(obj.getInteger("pay_5_10_count")>0){
                        if(start <= 0){
                            start = 4;
                        }
                        end = 4;
                        count++;
                    }
                    if(obj.getInteger("pay_10_15_count")>0){
                        if(start <= 0){
                            start = 5;
                        }
                        end = 5;
                        count++;
                    }
                    if(obj.getInteger("pay_15_30_count")>0){
                        if(start <= 0){
                            start = 6;
                        }
                        end = 6;
                        count++;
                    }
                    if(obj.getInteger("pay_30_50_count")>0){
                        if(start <= 0){
                            start = 7;
                        }
                        end = 7;
                        count++;
                    }
                    if(obj.getInteger("pay_50_100_count")>0){
                        if(start <= 0){
                            start = 8;
                        }
                        end = 8;
                        count++;
                    }
                    if(obj.getInteger("pay_100_200_count")>0){
                        if(start <= 0){
                            start = 9;
                        }
                        end = 9;
                        count++;
                    }
                    if(obj.getInteger("pay_200_500_count")>0){
                        if(start <= 0){
                            start = 10;
                        }
                        end = 10;
                        count++;
                    }
                    if(obj.getInteger("pay_500_1000_count")>0){
                        if(start <= 0){
                            start = 11;
                        }
                        end = 11;
                        count++;
                    }
                    if(obj.getInteger("over_1000_count")>0){
                        if(start <= 0){
                            start = 12;
                        }
                        end = 12;
                        count++;
                    }
                    //刚刚好6个档次区间
                    if(end - start ==5){
                        for(int i=start; i<=end; i++){
                            newObj.put("show",newMap.get(i));
                            newObj.put("orderCount",obj.getIntValue(map.get(i)));
                            newObj.put("payAmount",obj.getDoubleValue(payMap.get(i)));
                            newObj.put("svgPrice",obj.getDoubleValue(svgMap.get(i)));
                            data.add(newObj);
                            newObj = new JSONObject();
                        }
                    }

                    //如果少于6个，且网上能够补齐6个
                    if(end - start < 5){
                        //网上区间够补齐6个
                        if(start<=7){
                            for(int i=start; i<=start+5; i++){
                                newObj.put("show",newMap.get(i));
                                newObj.put("orderCount",obj.getIntValue(map.get(i)));
                                newObj.put("payAmount",obj.getDoubleValue(payMap.get(i)));
                                newObj.put("svgPrice",obj.getDoubleValue(svgMap.get(i)));
                                data.add(newObj);
                                newObj = new JSONObject();
                            }
                        }
                        //往上不够补，往下补
                        if(start>7){
                            for(int i=7; i<=12; i++){
                                newObj.put("show",newMap.get(i));
                                newObj.put("orderCount",obj.getIntValue(map.get(i)));
                                newObj.put("payAmount",obj.getDoubleValue(payMap.get(i)));
                                newObj.put("svgPrice",obj.getDoubleValue(svgMap.get(i)));
                                data.add(newObj);
                                newObj = new JSONObject();
                            }
                        }

                    }

                    //大于6个档次
                    if(end - start > 5){
                        for(int i=start; i<start+5; i++){
                            newObj.put("show",newMap.get(i));
                            newObj.put("orderCount",obj.getIntValue(map.get(i)));
                            newObj.put("payAmount",obj.getDoubleValue(payMap.get(i)));
                            newObj.put("svgPrice",obj.getDoubleValue(svgMap.get(i)));
                            data.add(newObj);
                            newObj = new JSONObject();
                        }
                        int lastCounts = 0;
                        double lastPays = 0;
                        for(int i=start+5; i<=end; i++){
                            lastCounts += obj.getIntValue(map.get(i));
                            lastPays += obj.getDoubleValue(payMap.get(i));
                        }

                        DecimalFormat decimalFormat = new DecimalFormat("#.00");
                        newObj.put("show",newMap.get(start+5).split("-")[0]+"以上");
                        newObj.put("orderCount",lastCounts);
                        newObj.put("payAmount",lastPays);
                        newObj.put("svgPrice",decimalFormat.format(lastPays/lastCounts));
                        data.add(newObj);
                    }
                    result.getJSONObject("info").put("totalCount",6);
                    result.getJSONObject("info").remove("list");
                    result.getJSONObject("info").put("list",data);

                }
            }
            return result;
        }catch (Exception e){
            log.warn("{}",e);
        }

        return null;
    }
    
}
