package cn.lyy.merchant.service.onedata.columns.pg;

public class EquipmentTypeColumnArray {

	public static final String DayEquipmentTypeListSql =
            "merchant_id as \"merchantId\", " +
            "province_id as \"provinceId\", " +
            "city_id as \"cityId\", " +
            "district_id as \"districtId\", " +
            "equipment_group_id as \"groupId\", " +
            "lyy_equipment_type_id as \"equipmentTypeId\", " +
            "equipment_type_name as \"equipmentTypeName\", " +
            "cast(pay_amount as decimal(15,2)) as \"payAmount\"," +        //支付金额
            "cast(case when pay_amount_yesterday =0 and pay_amount>0 then 1 " +
            "when pay_amount_yesterday=0 and pay_amount=0 then 0 " +
            "when pay_amount_yesterday>0 and pay_amount=0 then -1 " +
            "else (pay_amount-pay_amount_yesterday)::numeric/pay_amount_yesterday::numeric end as decimal(15,2)) as \"payAmountUp\", " +

            "cast(pay_count as decimal(15,0)) as \"payCount\"," +        //订单数
            "cast(case when pay_count_yesterday =0 and pay_count>0 then 1 " +
            "when pay_count_yesterday=0 and pay_count=0 then 0 " +
            "when pay_count_yesterday>0 and pay_count=0 then -1 " +
            "else (pay_count-pay_count_yesterday)::numeric/pay_count_yesterday::numeric end as decimal(15,2)) as \"payCountUp\", " +

            "cast(case when pay_count=0 then 0 else pay_amount::numeric/pay_count::numeric end as decimal(15,2)) as \"perOrderAmount\"," +        //订单均价
            "case when (pay_count =0 or pay_amount=0) and (pay_count_yesterday = 0 or pay_amount_yesterday=0) then 0 " +
            "when (pay_count =0 or pay_amount=0) and pay_amount_yesterday>0 then -1 " +
            "when (pay_count_yesterday = 0 or pay_amount_yesterday=0) and pay_amount > 0 then 1 " +
            "else cast( ((pay_amount::numeric/pay_count::numeric) - (pay_amount_yesterday::numeric/pay_count_yesterday::numeric))::numeric/(pay_amount_yesterday::numeric/pay_count_yesterday::numeric)::numeric as decimal(15,2)) end  as \"perOrderAmountUp\" "+

            "";

    public static final String WeekEquipmentTypeListSql =
            "merchant_id as \"merchantId\", " +
            "province_id as \"provinceId\", " +
            "city_id as \"cityId\", " +
            "district_id as \"districtId\", " +
            "equipment_group_id as \"groupId\", " +
            "lyy_equipment_type_id as \"equipmentTypeId\", " +
            "equipment_type_name as \"equipmentTypeName\", " +
            "cast(pay_amount as decimal(15,2)) as \"payAmount\"," +        //支付金额
            "cast(case when pay_amount_last_week =0 and pay_amount>0 then 1 " +
            "when pay_amount_last_week=0 and pay_amount=0 then 0 " +
            "when pay_amount_last_week>0 and pay_amount=0 then -1 " +
            "else (pay_amount-pay_amount_last_week)::numeric/pay_amount_last_week::numeric end as decimal(15,2)) as \"payAmountUp\", " +

                    "cast(pay_count as decimal(15,0)) as \"payCount\"," +        //订单数
                    "cast(case when pay_count_last_week =0 and pay_count>0 then 1 " +
                    "when pay_count_last_week=0 and pay_count=0 then 0 " +
                    "when pay_count_last_week>0 and pay_count=0 then -1 " +
                    "else (pay_count-pay_count_last_week)::numeric/pay_count_last_week::numeric end as decimal(15,2)) as \"payCountUp\", " +

                    "cast(case when pay_count=0 then 0 else pay_amount::numeric/pay_count::numeric end as decimal(15,2)) as \"perOrderAmount\"," +        //订单均价
                    "case when (pay_count =0 or pay_amount=0) and (pay_count_last_week = 0 or pay_amount_last_week=0) then 0 " +
                    "when (pay_count =0 or pay_amount=0) and pay_amount_last_week>0 then -1 " +
                    "when (pay_count_last_week = 0 or pay_amount_last_week=0) and pay_amount > 0 then 1 " +
                    "else cast( ((pay_amount::numeric/pay_count::numeric) - (pay_amount_last_week::numeric/pay_count_last_week::numeric))::numeric/(pay_amount_last_week::numeric/pay_count_last_week::numeric)::numeric as decimal(15,2)) end  as \"perOrderAmountUp\" "+

                    "";
    public static final String MonthEquipmentTypeListSql =
            "merchant_id as \"merchantId\", " +
                    "province_id as \"provinceId\", " +
                    "city_id as \"cityId\", " +
                    "district_id as \"districtId\", " +
                    "equipment_group_id as \"groupId\", " +
                    "lyy_equipment_type_id as \"equipmentTypeId\", " +
                    "equipment_type_name as \"equipmentTypeName\", " +
                    "cast(pay_amount as decimal(15,2)) as \"payAmount\"," +        //支付金额
                    "cast(case when pay_amount_last_month =0 and pay_amount>0 then 1 " +
                    "when pay_amount_last_month=0 and pay_amount=0 then 0 " +
                    "when pay_amount_last_month>0 and pay_amount=0 then -1 " +
                    "else (pay_amount-pay_amount_last_month)::numeric/pay_amount_last_month::numeric end as decimal(15,2)) as \"payAmountUp\", " +

                    "cast(pay_count as decimal(15,0)) as \"payCount\"," +        //订单数
                    "cast(case when pay_count_last_month =0 and pay_count>0 then 1 " +
                    "when pay_count_last_month=0 and pay_count=0 then 0 " +
                    "when pay_count_last_month>0 and pay_count=0 then -1 " +
                    "else (pay_count-pay_count_last_month)::numeric/pay_count_last_month::numeric end as decimal(15,2)) as \"payCountUp\", " +

                    "cast(case when pay_count=0 then 0 else pay_amount::numeric/pay_count::numeric end as decimal(15,2)) as \"perOrderAmount\"," +        //订单均价
                    "case when (pay_count =0 or pay_amount=0) and (pay_count_last_month = 0 or pay_amount_last_month=0) then 0 " +
                    "when (pay_count =0 or pay_amount=0) and pay_amount_last_month>0 then -1 " +
                    "when (pay_count_last_month = 0 or pay_amount_last_month=0) and pay_amount > 0 then 1 " +
                    "else cast( ((pay_amount::numeric/pay_count::numeric) - (pay_amount_last_month::numeric/pay_count_last_month::numeric))::numeric/(pay_amount_last_month::numeric/pay_count_last_month::numeric)::numeric as decimal(15,2)) end  as \"perOrderAmountUp\" "+

                    "";

    public static final String YearEquipmentTypeListSql =
            "merchant_id as \"merchantId\", " +
                    "province_id as \"provinceId\", " +
                    "city_id as \"cityId\", " +
                    "district_id as \"districtId\", " +
                    "equipment_group_id as \"groupId\", " +
                    "lyy_equipment_type_id as \"equipmentTypeId\", " +
                    "equipment_type_name as \"equipmentTypeName\", " +
                    "cast(pay_amount as decimal(15,2)) as \"payAmount\"," +        //支付金额
                    "cast(case when pay_amount_last_year =0 and pay_amount>0 then 1 " +
                    "when pay_amount_last_year=0 and pay_amount=0 then 0 " +
                    "when pay_amount_last_year>0 and pay_amount=0 then -1 " +
                    "else (pay_amount-pay_amount_last_year)::numeric/pay_amount_last_year::numeric end as decimal(15,2)) as \"payAmountUp\", " +

                    "cast(pay_count as decimal(15,0)) as \"payCount\"," +        //订单数
                    "cast(case when pay_count_last_year =0 and pay_count>0 then 1 " +
                    "when pay_count_last_year=0 and pay_count=0 then 0 " +
                    "when pay_count_last_year>0 and pay_count=0 then -1 " +
                    "else (pay_count-pay_count_last_year)::numeric/pay_count_last_year::numeric end as decimal(15,2)) as \"payCountUp\", " +

                    "cast(case when pay_count=0 then 0 else pay_amount::numeric/pay_count::numeric end as decimal(15,2)) as \"perOrderAmount\"," +        //订单均价
                    "case when (pay_count =0 or pay_amount=0) and (pay_count_last_year = 0 or pay_amount_last_year=0) then 0 " +
                    "when (pay_count =0 or pay_amount=0) and pay_amount_last_year>0 then -1 " +
                    "when (pay_count_last_year = 0 or pay_amount_last_year=0) and pay_amount > 0 then 1 " +
                    "else cast( ((pay_amount::numeric/pay_count::numeric) - (pay_amount_last_year::numeric/pay_count_last_year::numeric))::numeric/(pay_amount_last_year::numeric/pay_count_last_year::numeric)::numeric as decimal(15,2)) end  as \"perOrderAmountUp\" "+

                    "";

}
