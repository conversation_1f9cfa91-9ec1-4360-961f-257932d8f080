package cn.lyy.merchant.service.onedata.mobile;

import java.util.HashSet;
import java.util.TreeSet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.alibaba.fastjson.JSONObject;

import cn.lyy.base.util.StringUtil;
import cn.lyy.merchant.controller.onedata.dto.DayParamDto;
import cn.lyy.merchant.controller.onedata.dto.MonthParamDto;
import cn.lyy.merchant.controller.onedata.dto.WeekParamDto;
import cn.lyy.merchant.controller.onedata.dto.YearParamDto;
import cn.lyy.merchant.service.onedata.FeignUtil;
import cn.lyy.merchant.service.onedata.ParamDto;
import cn.lyy.merchant.service.onedata.columns.ColumnArrayMap;
import cn.lyy.merchant.service.onedata.columns.ColumnArrayMapKey;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class EquipmentGroupDataServiceImpl implements EquipmentGroupDataService {

	@Autowired
	private FeignUtil feignUtil ;
	
    @Value("${oneData.dayOrderDayAnalyseCode}")
    private String dayOrderDayAnalyseCode;
    @Value("${oneData.weekOrderDayAnalyseCode}")
    private String weekOrderDayAnalyseCode;
    @Value("${oneData.monthOrderDayAnalyseCode}")
    private String monthOrderDayAnalyseCode;
    @Value("${oneData.yearOrderDayAnalyseCode}")
    private String yearOrderDayAnalyseCode;
    
    @Value("${oneData.dayGroupEquipmentAnalyseCode}")
    private String dayGroupEquipmentAnalyseCode;
    @Value("${oneData.weekGroupEquipmentAnalyseCode}")
    private String weekGroupEquipmentAnalyseCode;
    @Value("${oneData.monthGroupEquipmentAnalyseCode}")
    private String monthGroupEquipmentAnalyseCode;
    @Value("${oneData.yearGroupEquipmentAnalyseCode}")
    private String yearGroupEquipmentAnalyseCode;
    
    @Value("${oneData.dayMemberDataCode}")
    private String dayMemberDataCode;
    @Value("${oneData.weekMemberDataCode}")
    private String weekMemberDataCode;
    @Value("${oneData.monthMemberDataCode}")
    private String monthMemberDataCode;
    @Value("${oneData.yearMemberDataCode}")
    private String yearMemberDataCode;

	/**
     * 日报场地，设备数据
     * @param dayParamDto
     * @return
     */
    @SuppressWarnings({ "serial", "rawtypes" })
    @Override
    public JSONObject getGroupEquipmentData(DayParamDto dayParamDto) {
        ParamDto params = new ParamDto();
        params.setCode(dayGroupEquipmentAnalyseCode);
        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.DAY_EQUIPMENT_GROUP_COLUMN_ARRAY) ;
        params.setColumnArray(columnArray);
        params.getQueryParams().setDay(dayParamDto.getDay());
        params.getQueryParams().setMerchant_id(dayParamDto.getMerchantId()+"");
        params.getQueryParams().setCombo_type(dayParamDto.getComboType());

        params.getQueryParams().setProvince_id(dayParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(dayParamDto.getProvinceId()+"");}});
        params.getQueryParams().setCity_id(dayParamDto.getCityId() == null ? null:new HashSet<String>(){{add(dayParamDto.getCityId()+"");}});
        params.getQueryParams().setDistrict_id(dayParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(dayParamDto.getAreaId()+"");}});
        params.getQueryParams().setEquipment_group_id(dayParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(dayParamDto.getGroupId()+"");}});
        params.getQueryParams().setLyy_equipment_type_id(dayParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(dayParamDto.getEquipmentTypeId()+"");}});

        if(dayParamDto.getGroupIds() != null && dayParamDto.getGroupIds().size()>0){
            if(dayParamDto.getGroupId() == null || !dayParamDto.getGroupIds().contains(dayParamDto.getGroupId()+"")){
                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(dayParamDto.getGroupIds())).first()+"");}});
            }
            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
           // params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
        }
        params.resetComboType();

        try{
            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
            JSONObject result = feignUtil.getData(params);
            log.info("{}",result.toJSONString());
            return result;
        }catch (Exception e){
            log.warn("{}",e);
        }

        return null;
    }
    
    /**
     * 周报场地，设备数据
     * @param weekParamDto
     * @return
     */
    @SuppressWarnings({ "serial", "rawtypes" })
    @Override
    public JSONObject getWeekGroupEquipmentData(WeekParamDto weekParamDto) {
        ParamDto params = new ParamDto();
        params.setCode(weekGroupEquipmentAnalyseCode);
        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.WEEK_EQUIPMENT_GROUP_COLUMN_ARRAY) ;
        params.setColumnArray(columnArray);

        params.getQueryParams().setYear_month(weekParamDto.getMonth());
        params.getQueryParams().setMonth_week_num(weekParamDto.getWeek()+"");
        params.getQueryParams().setMerchant_id(weekParamDto.getMerchantId()+"");
        params.getQueryParams().setCombo_type(weekParamDto.getComboType());

        params.getQueryParams().setProvince_id(weekParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(weekParamDto.getProvinceId()+"");}});
        params.getQueryParams().setCity_id(weekParamDto.getCityId() == null ? null:new HashSet<String>(){{add(weekParamDto.getCityId()+"");}});
        params.getQueryParams().setDistrict_id(weekParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(weekParamDto.getAreaId()+"");}});
        params.getQueryParams().setEquipment_group_id(weekParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(weekParamDto.getGroupId()+"");}});
        params.getQueryParams().setLyy_equipment_type_id(weekParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(weekParamDto.getEquipmentTypeId()+"");}});

        if(weekParamDto.getGroupIds() != null && weekParamDto.getGroupIds().size()>0){
            if(weekParamDto.getGroupId() == null || !weekParamDto.getGroupIds().contains(weekParamDto.getGroupId()+"")){
                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(weekParamDto.getGroupIds())).first()+"");}});
            }
            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
           // params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
        }
        params.resetComboType();

        try{
            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
            JSONObject result = feignUtil.getData(params);
            log.info("{}",result.toJSONString());
            return result;
        }catch (Exception e){
            log.warn("{}",e);
        }



        return null;
    }

    /**
     * 月报场地，设备数据
     * @param weekParamDto
     * @return
     */
    @SuppressWarnings({ "serial", "rawtypes" })
	@Override
    public JSONObject getMonthGroupEquipmentData(MonthParamDto monthParamDto) {
        ParamDto params = new ParamDto();
        params.setCode(monthGroupEquipmentAnalyseCode);

        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.MONTH_EQUIPMENT_GROUP_COLUMN_ARRAY) ;
        params.setColumnArray(columnArray);

        params.getQueryParams().setYear_month(monthParamDto.getMonth());
        params.getQueryParams().setMerchant_id(monthParamDto.getMerchantId()+"");
        params.getQueryParams().setCombo_type(monthParamDto.getComboType());

        params.getQueryParams().setProvince_id(monthParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(monthParamDto.getProvinceId()+"");}});
        params.getQueryParams().setCity_id(monthParamDto.getCityId() == null ? null:new HashSet<String>(){{add(monthParamDto.getCityId()+"");}});
        params.getQueryParams().setDistrict_id(monthParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(monthParamDto.getAreaId()+"");}});
        params.getQueryParams().setEquipment_group_id(monthParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(monthParamDto.getGroupId()+"");}});
        params.getQueryParams().setLyy_equipment_type_id(monthParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(monthParamDto.getEquipmentTypeId()+"");}});

        if(monthParamDto.getGroupIds() != null && monthParamDto.getGroupIds().size()>0){
            if(monthParamDto.getGroupId() == null || !monthParamDto.getGroupIds().contains(monthParamDto.getGroupId()+"")){
                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(monthParamDto.getGroupIds())).first()+"");}});
            }
            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
            //params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
        }
        params.resetComboType();

        try{
            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
            JSONObject result = feignUtil.getData(params);
            log.info("{}",result.toJSONString());
            return result;
        }catch (Exception e){
            log.warn("{}",e);
        }



        return null;
    }

    /**
     * 年报场地，设备数据
     * @param weekParamDto
     * @return
     */
    @SuppressWarnings({ "serial", "rawtypes" })
	@Override
    public JSONObject getYearGroupEquipmentData(YearParamDto yearParamDto) {
        ParamDto params = new ParamDto();
        params.setCode(yearGroupEquipmentAnalyseCode);
        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.YEAR_EQUIPMENT_GROUP_COLUMN_ARRAY) ;
        params.setColumnArray(columnArray);
        params.getQueryParams().setYear_str(yearParamDto.getYear()+"");
        params.getQueryParams().setMerchant_id(yearParamDto.getMerchantId()+"");
        params.getQueryParams().setCombo_type(yearParamDto.getComboType());

        params.getQueryParams().setProvince_id(yearParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(yearParamDto.getProvinceId()+"");}});
        params.getQueryParams().setCity_id(yearParamDto.getCityId() == null ? null:new HashSet<String>(){{add(yearParamDto.getCityId()+"");}});
        params.getQueryParams().setDistrict_id(yearParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(yearParamDto.getAreaId()+"");}});
        params.getQueryParams().setEquipment_group_id(yearParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(yearParamDto.getGroupId()+"");}});
        params.getQueryParams().setLyy_equipment_type_id(yearParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(yearParamDto.getEquipmentTypeId()+"");}});

        if(yearParamDto.getGroupIds() != null && yearParamDto.getGroupIds().size()>0){
            if(yearParamDto.getGroupId() == null || !yearParamDto.getGroupIds().contains(yearParamDto.getGroupId()+"")){
                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(yearParamDto.getGroupIds())).first()+"");}});
            }
            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
            //params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
        }
        params.resetComboType();

        try{
            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
            JSONObject result = feignUtil.getData(params);
            log.info("{}",result.toJSONString());
            return result;
        }catch (Exception e){
            log.warn("{}",e);
        }

        return null;
    }

    /**
     * 日报获取场地维度统计信息
     * @param dayParamDto
     * @return
     */
    @Override
    public JSONObject getDayGroupData(DayParamDto dayParamDto) {
        ParamDto params = new ParamDto();
        params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");

        if(dayParamDto.getEquipmentTypeId() != null && dayParamDto.getEquipmentTypeId()>0){
            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地+设备类型维度");
        }

        params.setCode(dayGroupEquipmentAnalyseCode);
        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.DAY_SUB_EQUIPMENT_GROUP_LIST_COLUMN_ARRAY) ;
        params.setColumnArray(columnArray);   
        JSONObject groupResult = feignUtil.getData(params, dayParamDto);

        return groupResult;
    }

    /**
     * 周报获取场地维度统计信息
     * @param dayParamDto
     * @return
     */
    @Override
    public JSONObject getWeekGroupData(WeekParamDto weekParamDto) {
        ParamDto params = new ParamDto();
        params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
        if(weekParamDto.getEquipmentTypeId() != null && weekParamDto.getEquipmentTypeId()>0){
            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地+设备类型维度");
        }

        params.setCode(weekGroupEquipmentAnalyseCode);
        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.COMMON_SUB_EQUIPMENT_GROUP_LIST_COLUMN_ARRAY) ;
        params.setColumnArray(columnArray);   
        JSONObject groupResult = feignUtil.getData(params, weekParamDto);

        return groupResult ;
    }

    /**
     * 月报获取场地维度统计信息
     * @param dayParamDto
     * @return
     */
    @Override
    public JSONObject getMonthGroupData(MonthParamDto monthParamDto) {
        ParamDto params = new ParamDto();
        params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
        if(monthParamDto.getEquipmentTypeId() != null && monthParamDto.getEquipmentTypeId()>0){
            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地+设备类型维度");
        }

        params.setCode(monthGroupEquipmentAnalyseCode);
        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.COMMON_SUB_EQUIPMENT_GROUP_LIST_COLUMN_ARRAY) ;
        params.setColumnArray(columnArray);   
        JSONObject groupResult = feignUtil.getData(params, monthParamDto);

        return groupResult ;
    }

    /**
     * 年报获取场地维度统计信息
     * @param dayParamDto
     * @return
     */
    @Override
    public JSONObject getYearGroupData(YearParamDto yearParamDto) {
        ParamDto params = new ParamDto();
        params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
        if(yearParamDto.getEquipmentTypeId() != null && yearParamDto.getEquipmentTypeId()>0){
            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地+设备类型维度");
        }
        params.setCode(yearGroupEquipmentAnalyseCode);
        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.COMMON_SUB_EQUIPMENT_GROUP_LIST_COLUMN_ARRAY) ;
        params.setColumnArray(columnArray);        
        JSONObject groupResult = feignUtil.getData(params, yearParamDto);

        return groupResult ;
    }
    
    /**
     * 日报获取场地维度底部均值统计信息
     * @param dayParamDto
     * @return
     */
    @Override
    public JSONObject getDaySumGroupData(DayParamDto dayParamDto) {

        ParamDto params = new ParamDto();
        params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
        if(dayParamDto.getEquipmentTypeId() != null && dayParamDto.getEquipmentTypeId()>0){
            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地+设备类型维度");
        }
        

        params.setCode(dayGroupEquipmentAnalyseCode);
        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.COMMON_AVG_SUB_EQUIPMENT_GROUP_LIST_COLUMN_ARRAY) ;
        params.setColumnArray(columnArray);
        JSONObject groupResult = feignUtil.getData(params, dayParamDto);

        groupResult.getJSONObject("info").put("totalPage",1);
        groupResult.getJSONObject("info").put("totalCount",1);
        return groupResult;
    }

    /**
     * 周报获取场地维度统计信息
     * @param dayParamDto
     * @return
     */
    @Override
    public JSONObject getWeekSumGroupData(WeekParamDto weekParamDto) {
        ParamDto params = new ParamDto();
        params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
        if(weekParamDto.getEquipmentTypeId() != null && weekParamDto.getEquipmentTypeId()>0){
            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地+设备类型维度");
        }
       

        params.setCode(weekGroupEquipmentAnalyseCode);
        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.COMMON_AVG_SUB_EQUIPMENT_GROUP_LIST_COLUMN_ARRAY) ;
        params.setColumnArray(columnArray);
        JSONObject groupResult = feignUtil.getData(params, weekParamDto);

        
        groupResult.getJSONObject("info").put("totalPage",1);
        groupResult.getJSONObject("info").put("totalCount",1);
        return groupResult;
    }

    /**
     * 月报获取场地维度统计信息
     * @param dayParamDto
     * @return
     */
    @Override
    public JSONObject getMonthSumGroupData(MonthParamDto monthParamDto) {
        ParamDto params = new ParamDto();
        params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
        if(monthParamDto.getEquipmentTypeId() != null && monthParamDto.getEquipmentTypeId()>0){
            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地+设备类型维度");
        }
        params.setCode(monthGroupEquipmentAnalyseCode);
        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.COMMON_AVG_SUB_EQUIPMENT_GROUP_LIST_COLUMN_ARRAY) ;
        params.setColumnArray(columnArray);
        JSONObject groupResult = feignUtil.getData(params, monthParamDto);
        
        groupResult.getJSONObject("info").put("totalPage",1);
        groupResult.getJSONObject("info").put("totalCount",1);
        return groupResult;
    }

    /**
     * 年报获取场地维度统计信息
     * @param dayParamDto
     * @return
     */
    @Override
    public JSONObject getYearSumGroupData(YearParamDto yearParamDto) {
        ParamDto params = new ParamDto();
        params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地维度");
        if(yearParamDto.getEquipmentTypeId() != null && yearParamDto.getEquipmentTypeId()>0){
            params.getQueryParams().setCombo_type("商家+省份+城市+区域+场地+设备类型维度");
        }
        params.setCode(yearGroupEquipmentAnalyseCode);
        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.COMMON_AVG_SUB_EQUIPMENT_GROUP_LIST_COLUMN_ARRAY) ;
        params.setColumnArray(columnArray);
        JSONObject groupResult = feignUtil.getData(params, yearParamDto);
        
        groupResult.getJSONObject("info").put("totalPage",1);
        groupResult.getJSONObject("info").put("totalCount",1);
        return groupResult;
    }
    
}
