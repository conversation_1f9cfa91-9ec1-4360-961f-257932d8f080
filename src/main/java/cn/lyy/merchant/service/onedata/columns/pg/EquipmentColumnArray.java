package cn.lyy.merchant.service.onedata.columns.pg;

public class EquipmentColumnArray {


  public static final String EquipmentSql = "merchant_id as \"merchantId\", " +
          "province_id as \"provinceId\", " +
          "city_id as \"cityId\", " +
          "district_id as \"districtId\", " +
          "equipment_group_id as \"groupId\", " +
          "equipment_group_name as \"groupName\", " +
          "equipment_group_address as \"address\", " +
          "equipment_id as \"equipmentId\", " +
          "equipment_type_name || equipment_value as \"equipmentName\", "+
          "lyy_equipment_type_id as \"equipmentTypeId\", " +
          "equipment_type_name as \"equipmentTypeName\", " +

          "cast(pay_amount as decimal(15,2)) as \"payAmount\"," +
          "cast(case when last_cycle_pay_amount =0 and pay_amount>0 then 1 " +
          "when last_cycle_pay_amount=0 and pay_amount=0 then 0 " +
          "when last_cycle_pay_amount>0 and pay_amount=0 then -1 " +
          "else (pay_amount-last_cycle_pay_amount)::numeric/last_cycle_pay_amount::numeric end as decimal(15,2)) as \"payAmountUp\", " +

          "total_start_counts as \"startCounts\"," +
          "cast(case when last_cycle_total_start_counts =0 and total_start_counts>0 then 1 " +
          "when last_cycle_total_start_counts=0 and total_start_counts=0 then 0 " +
          "when last_cycle_total_start_counts>0 and total_start_counts=0 then -1 " +
          "else (total_start_counts-last_cycle_total_start_counts)::numeric/last_cycle_total_start_counts::numeric end as decimal(15,2)) as \"startCountsUp\", " +

          "cast(pay_user_num as decimal(15,2)) as \"payUsers\"," +        //支付会员数
          "cast(case when last_cycle_pay_user_num =0 and pay_user_num>0 then 1 " +
          "when last_cycle_pay_user_num=0 and pay_user_num=0 then 0 " +
          "when last_cycle_pay_user_num>0 and pay_user_num=0 then -1 " +
          "else (pay_user_num-last_cycle_pay_user_num)::numeric/last_cycle_pay_user_num::numeric end as decimal(15,2)) as \"payUsersUp\", " +

          "cast(order_counts as decimal(15,2)) as \"payCounts\"," +        //订单总数
          "cast(case when last_cycle_order_counts =0 and order_counts>0 then 1 " +
          "when last_cycle_order_counts=0 and order_counts=0 then 0 " +
          "when last_cycle_order_counts>0 and order_counts=0 then -1 " +
          "else (order_counts-last_cycle_order_counts)::numeric/last_cycle_order_counts::numeric end as decimal(15,2)) as \"payCountsUp\", " +

          "cast(online_pay_amount as decimal(15,2)) as \"onlinePayAmount\", " +   //线上支付金额
          "cast(case when last_cycle_online_pay_amount =0 and online_pay_amount>0 then 1 " +
          "when last_cycle_online_pay_amount=0 and online_pay_amount=0 then 0 " +
          "when last_cycle_online_pay_amount>0 and online_pay_amount=0 then -1 " +
          "else (online_pay_amount-last_cycle_online_pay_amount)::numeric/last_cycle_online_pay_amount::numeric end as decimal(15,2)) as \"onlinePayAmountUp\"," +

          "online_pay_count as \"onlinePayCounts\", " +       //线上支付订单数
          "cast(case when last_cycle_online_pay_count =0 and online_pay_count>0 then 1 " +
          "when online_pay_count=0 and last_cycle_online_pay_count=0 then 0 " +
          "when online_pay_count=0 and last_cycle_online_pay_count>0 then -1 " +
          "else (online_pay_count-last_cycle_online_pay_count)::numeric/last_cycle_online_pay_count::numeric end as decimal(15,2)) as \"onlinePayCountsUp\"," +

          "cast(online_refund_amount as decimal(15,2)) as \"refundAmount\"," +
          "cast(case when last_cycle_online_refund_amount =0 and online_refund_amount>0 then 1 when last_cycle_online_refund_amount=0 and online_refund_amount=0 then 0 else (online_refund_amount-last_cycle_online_refund_amount)::numeric/last_cycle_online_refund_amount::numeric end as decimal(15,2)) as \"refundAmountUp\"," +
          "cast(online_refund_count as decimal(15,2)) as \"refundCounts\"," +
          "cast(case when last_cycle_online_refund_count =0 and online_refund_count>0 then 1 when last_cycle_online_refund_count=0 and online_refund_count=0 then 0 else (online_refund_count-last_cycle_online_refund_count)::numeric/last_cycle_online_refund_count::numeric end as decimal(15,2)) as \"refundCountsUp\"," +
          "cast(refund_user_num as decimal(15,2)) as \"refundUsers\"," +      //退款会员数
          "cast(case when last_cycle_refund_user_num =0 and refund_user_num>0 then 1 when last_cycle_refund_user_num=0 and refund_user_num=0 then 0 else (refund_user_num-last_cycle_refund_user_num)::numeric/last_cycle_refund_user_num::numeric end as decimal(15,2)) as \"refundUsersUp\"," +

          "cast(case when online_refund_count=0 then 0 else online_refund_amount::numeric/online_refund_count::numeric end as decimal(15,2)) as \"refundPerOrderAmount\"," +        //退款订单均价
          "case when (online_refund_count =0 or online_refund_amount=0) and (last_cycle_online_refund_count = 0 or last_cycle_online_refund_amount=0) then 0 " +
          "when (online_refund_count =0 or online_refund_amount=0) and last_cycle_online_refund_amount>0 then -1 "+
          "when (last_cycle_online_refund_count = 0 or last_cycle_online_refund_amount=0) and online_refund_amount > 0 then 1 " +
          "else cast( ((online_refund_amount::numeric/online_refund_count::numeric) - (last_cycle_online_refund_amount::numeric/last_cycle_online_refund_count::numeric))::numeric/(last_cycle_online_refund_amount::numeric/last_cycle_online_refund_count::numeric)::numeric as decimal(15,2)) end  as \"refundPerOrderAmountUp\", "+


          "cast(case when order_counts=0 then 0 else pay_amount::numeric/order_counts::numeric end as decimal(15,2)) as \"perOrderAmount\"," +        //订单均价
          "case when (order_counts =0 or pay_amount=0) and (last_cycle_order_counts = 0 or last_cycle_pay_amount=0) then 0 " +
          "when (order_counts =0 or pay_amount=0) and last_cycle_pay_amount>0 then -1 " +
          "when (last_cycle_order_counts = 0 or last_cycle_pay_amount=0) and pay_amount > 0 then 1 " +
          "else cast( ((pay_amount::numeric/order_counts::numeric) - (last_cycle_pay_amount::numeric/last_cycle_order_counts::numeric))::numeric/(last_cycle_pay_amount::numeric/last_cycle_order_counts::numeric)::numeric as decimal(15,2)) end  as \"perOrderAmountUp\", "+

          "cast(case when pay_user_num=0 then 0 else pay_amount::numeric/pay_user_num::numeric end as decimal(15,2)) as \"perUserAmount\"," +        //客单均价
          "case when (pay_user_num =0 or pay_amount=0) and (last_cycle_pay_user_num = 0 or last_cycle_pay_amount=0) then 0 " +
          "when (pay_user_num =0 or pay_amount=0) and last_cycle_pay_amount>0 then -1 " +
          "when (last_cycle_pay_user_num = 0 or last_cycle_pay_amount=0) and pay_amount > 0 then 1 " +
          "else cast( ((pay_amount::numeric/pay_user_num::numeric) - (last_cycle_pay_amount::numeric/last_cycle_pay_user_num::numeric))::numeric/(last_cycle_pay_amount::numeric/last_cycle_pay_user_num::numeric)::numeric as decimal(15,2)) end  as \"perUserAmountUp\", "+

          "cast(case when online_pay_count=0 then 0 else online_pay_amount::numeric/online_pay_count::numeric end as decimal(15,2)) as \"onlinePerOrderAmount\"," +        //在线订单均价
          "case when (online_pay_count =0 or online_pay_amount=0) and (last_cycle_online_pay_count = 0 or last_cycle_online_pay_amount=0) then 0 " +
          "when (online_pay_count =0 or online_pay_amount=0) and last_cycle_online_pay_amount>0 then -1 " +
          "when (last_cycle_online_pay_count = 0 or last_cycle_online_pay_amount=0) and online_pay_amount > 0 then 1 " +
          "else cast( ((online_pay_amount::numeric/online_pay_count::numeric) - (last_cycle_online_pay_amount::numeric/last_cycle_online_pay_count::numeric))::numeric/(last_cycle_online_pay_amount::numeric/last_cycle_online_pay_count::numeric)::numeric as decimal(15,2)) end  as \"onlinePerOrderAmountUp\" "+

          "";


  public static final String EquipmentCountSql =
          "sum(coalesce(pay_amount::numeric,0)) as \"sumPayAmount\", "+
          "sum(coalesce(pay_user_num::numeric,0)) as \"sumPayUsers\", "+
          "sum(coalesce(order_counts::numeric,0)) as \"sumOrderCounts\", "+
          "sum(coalesce(online_start_counts::numeric,0)) as \"sumStartCounts\", "+
          "count(distinct equipment_id) as \"sumEquipments\", "+
          "cast(case when count(distinct equipment_id) =0 then sum(pay_amount)  else (sum(pay_amount))::numeric/count(distinct equipment_id)::numeric end as decimal(15,2)) as \"sumPerPayAmount\", " +
          "cast(case when count(distinct equipment_id) =0 then sum(pay_user_num)  else (sum(pay_user_num))::numeric/count(distinct equipment_id)::numeric end as decimal(15,2)) as \"sumPerPayUsers\", " +
          "cast(case when count(distinct equipment_id) =0 then sum(order_counts)  else (sum(order_counts))::numeric/count(distinct equipment_id)::numeric end as decimal(15,2)) as \"sumPerPayCounts\", " +
          "cast(case when sum(order_counts) =0 then sum(pay_amount)  else (sum(pay_amount))::numeric/sum(order_counts)::numeric end as decimal(15,2)) as \"sumPerOrderAmount\", " +
          "cast(case when count(distinct equipment_id) =0 then sum(coalesce(online_start_counts::numeric,0))  else (sum(coalesce(online_start_counts::numeric,0)))::numeric/count(distinct equipment_id)::numeric end as decimal(15,2)) as \"sumPerStartCounts\", " +
          "cast(case when count(distinct equipment_id) =0 then sum(coalesce(pay_amount::numeric,0))  else (sum(coalesce(pay_amount::numeric,0)))::numeric/count(distinct equipment_id)::numeric end as decimal(15,2)) as \"sumPerUserOrderAmount\", " +


          "cast(case when sum(coalesce(last_cycle_pay_amount::numeric,0)) =0 and sum(coalesce(pay_amount::numeric,0)) =0 then 0  " +
          "when sum(coalesce(last_cycle_pay_amount::numeric,0)) =0 and sum(coalesce(pay_amount::numeric,0)) > 0 then  1 "+
          "else (sum(coalesce(pay_amount::numeric,0))-sum(coalesce(last_cycle_pay_amount::numeric,0)))::numeric/sum(coalesce(last_cycle_pay_amount::numeric,0))::numeric end as decimal(15,2)) as \"sumPayAmountUp\", " +

          "cast(case when sum(coalesce(last_cycle_pay_user_num::numeric,0)) =0 and sum(coalesce(pay_user_num::numeric,0)) =0 then 0  " +
          "when sum(coalesce(last_cycle_pay_user_num::numeric,0)) =0 and sum(coalesce(pay_user_num::numeric,0)) > 0 then 1 "+
          "else (sum(coalesce(pay_user_num::numeric,0))-sum(coalesce(last_cycle_pay_user_num::numeric,0)))::numeric/sum(coalesce(last_cycle_pay_user_num::numeric,0))::numeric end as decimal(15,2)) as \"sumPayUsersUp\", " +

          "cast(case when sum(coalesce(last_cycle_order_counts::numeric,0)) =0 and sum(coalesce(order_counts::numeric,0)) =0 then 0  " +
          "when sum(coalesce(last_cycle_order_counts::numeric,0)) =0 and sum(coalesce(order_counts::numeric,0)) > 0 then 1 "+
          "else (sum(coalesce(order_counts::numeric,0))-sum(coalesce(last_cycle_order_counts::numeric,0)))::numeric/sum(coalesce(last_cycle_order_counts::numeric,0))::numeric end as decimal(15,2)) as \"sumOrderCountsUp\", " +

          "cast(case when sum(coalesce(last_cycle_online_start_counts::numeric,0)) =0 and sum(coalesce(online_start_counts::numeric,0)) =0 then 0  " +
          "when sum(coalesce(last_cycle_online_start_counts::numeric,0)) =0 and sum(coalesce(online_start_counts::numeric,0)) > 0 then 1 "+
          "else (sum(coalesce(online_start_counts::numeric,0))-sum(coalesce(last_cycle_online_start_counts::numeric,0)))::numeric/sum(coalesce(last_cycle_online_start_counts::numeric,0))::numeric end as decimal(15,2)) as \"sumStartCountsUp\" " +


          //"case when (online_pay_count =0 or online_pay_amount=0) and (last_cycle_online_pay_count = 0 or last_cycle_online_pay_amount=0) then 0 " +
          //"when (online_pay_count =0 or online_pay_amount=0) and last_cycle_online_pay_amount>0 then -1 " +
          //"when (last_cycle_online_pay_count = 0 or last_cycle_online_pay_amount=0) and online_pay_amount > 0 then 1 " +
          //"else cast( ((online_pay_amount::numeric/online_pay_count::numeric) - (last_cycle_online_pay_amount::numeric/last_cycle_online_pay_count::numeric))::numeric/(last_cycle_online_pay_amount::numeric/last_cycle_online_pay_count::numeric)::numeric as decimal(15,2)) end  as \"onlinePerOrderAmountUp\" "+


          "";
}
