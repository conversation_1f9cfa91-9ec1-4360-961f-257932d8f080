package cn.lyy.merchant.service.onedata.mobile;

import com.alibaba.fastjson.JSONObject;

import cn.lyy.merchant.controller.onedata.dto.DayParamDto;
import cn.lyy.merchant.controller.onedata.dto.MonthParamDto;
import cn.lyy.merchant.controller.onedata.dto.WeekParamDto;
import cn.lyy.merchant.controller.onedata.dto.YearParamDto;

public interface OrderDataService {

	

    /**
     * 每日营收数据
     * @param dayParamDto
     * @return
     */
    public JSONObject getDayOrderData(DayParamDto dayParamDto);
    
    /**
     * 获取周报订单支付数据
     * @param weekParamDto
     * @return
     */
    public JSONObject getWeekOrderData(WeekParamDto weekParamDto);

    /**
     * 获取月报订单支付数据
     * @param monthParamDto
     * @return
     */
    public JSONObject getMonthOrderData(MonthParamDto monthParamDto);

    /**
     * 获取年报订单支付数据
     * @param yearParamDto
     * @return
     */
    public JSONObject getYearOrderData(YearParamDto yearParamDto);

    /**
     * 获取日报订单支付数据
     * @param dayParamDto
     * @return
     */
    public JSONObject getOrderData(DayParamDto dayParamDto);
    
    /**
     * 每周营收数据
     * @param weekParamDto
     * @return
     */
    public JSONObject getPerWeekOrderData(WeekParamDto weekParamDto);

    /**
     * 每月营收数据
     * @param monthParamDto
     * @return
     */
    public JSONObject getPerMonthOrderData(MonthParamDto monthParamDto);

    /**
     * 每年营收数据
     * @param yearParamDto
     * @return
     */
    public JSONObject getPerYearOrderData(YearParamDto yearParamDto);
    
    /**
     * 二级列表每日统计
     * @param dayParamDto
     * @return
     */
    public JSONObject getEveryDayData(DayParamDto dayParamDto);

    /**
     * 二级列表每周统计
     * @param weekParamDto
     * @return
     */
    public JSONObject getEveryWeekData(WeekParamDto weekParamDto);

    /**
     * 二级列表每周统计
     * @param monthParamDto
     * @return
     */
    public JSONObject getEveryMonthData(MonthParamDto monthParamDto);

    /**
     * 二级列表每周统计
     * @param yearParamDto
     * @return
     */
    public JSONObject getEveryYearData(YearParamDto yearParamDto);
    
    /**
     * 日报-获取客单价区间支付笔数
     * @param dayParamDto
     * @return
     */
    public JSONObject getPerOrderPriceCount(DayParamDto dayParamDto);

    /**
     * 周报-获取客单价区间支付笔数
     * @param weekParamDto
     * @return
     */
    public JSONObject getWeekPerOrderPriceCount(WeekParamDto weekParamDto);

    /**
     * 月报-获取客单价区间支付笔数
     * @param monthParamDto
     * @return
     */
    public JSONObject getMonthPerOrderPriceCount(MonthParamDto monthParamDto);


    /**
     * 年报-获取客单价区间支付笔数
     * @param yearParamDto
     * @return
     */
    public JSONObject getYearPerOrderPriceCount(YearParamDto yearParamDto);

    
}
