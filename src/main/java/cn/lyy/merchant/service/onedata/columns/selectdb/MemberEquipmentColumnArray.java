package cn.lyy.merchant.service.onedata.columns.selectdb;

public class MemberEquipmentColumnArray {

	public static final String DayMemberSql =
            "merchant_id as `merchantId`, " +
            "equipment_group_id as `groupId`, " +
            "coalesce(member_count,0) as `memberCount`, " +
            "coalesce(member_count_yesterday,0) as `lastCycleMemberCount`, " +
            "cast(case when coalesce(member_count_yesterday,0) =0 and coalesce(member_count,0) =0 then 0  " +
            "when coalesce(member_count_yesterday,0) =0 and coalesce(member_count,0) > 0 then 1 "+
            "else (coalesce(member_count,0)-coalesce(member_count_yesterday,0))/coalesce(member_count_yesterday,0) end as decimal(15,2)) as `memberCountUp` " ;

    public static final String WeekMemberSql =
            "merchant_id as `merchantId`, " +
            "equipment_group_id as `groupId`, " +
            "coalesce(member_count,0) as `memberCount`, " +
            "coalesce(last_week_member_count,0) as `lastCycleMemberCount`, " +
            "cast(case when coalesce(last_week_member_count,0) =0 and coalesce(member_count,0) =0 then 0  " +
            "when coalesce(last_week_member_count,0) =0 and coalesce(member_count,0) > 0 then 1 "+
            "else (coalesce(member_count,0)-coalesce(last_week_member_count,0))/coalesce(last_week_member_count,0) end as decimal(15,2)) as `memberCountUp` " ;
    
    public static final String MonthMemberSql =
            "merchant_id as `merchantId`, " +
            "equipment_group_id as `groupId`, " +
            "coalesce(member_count,0) as `memberCount`, " +
            "coalesce(last_month_member_count,0) as `lastCycleMemberCount`, " +
            "cast(case when coalesce(last_month_member_count,0) =0 and coalesce(member_count,0) =0 then 0  " +
            "when coalesce(last_month_member_count,0) =0 and coalesce(member_count,0) > 0 then 1 "+
            "else (coalesce(member_count,0)-coalesce(last_month_member_count,0))/coalesce(last_month_member_count,0) end as decimal(15,2)) as `memberCountUp` " ;

    public static final String YearMemberSql =
            "merchant_id as `merchantId`, " +
            "equipment_group_id as `groupId`, " +
            "coalesce(member_count,0) as `memberCount`, " +
            "coalesce(last_year_member_count,0) as `lastCycleMemberCount`, " +
            "cast(case when coalesce(last_year_member_count,0) =0 and coalesce(member_count,0) =0 then 0  " +
            "when coalesce(last_year_member_count,0) =0 and coalesce(member_count,0) > 0 then 1 "+
            "else (coalesce(member_count,0)-coalesce(last_year_member_count,0))/coalesce(last_year_member_count,0) end as decimal(15,2)) as `memberCountUp` " ;

    public static final String DayMemberListSql =
            "day, "+
            "merchant_id as `merchantId`, " +
            "province_id as `provinceId`, " +
            "city_id as `cityId`, " +
            "district_id as `districtId`, " +
            "equipment_type_id as `equipmentTypeId`, " +
            "equipment_group_id as `groupId`, " +
            "equipment_type_name as `equipmentTypeName`, " +
            "coalesce(member_count,0) as `memberCount`, " +
            "coalesce(member_count_yesterday,0) as `lastCycleMemberCount`, " +
            "cast(case when coalesce(member_count_yesterday,0) =0 and coalesce(member_count,0) =0 then 0  " +
            "when coalesce(member_count_yesterday,0) =0 and coalesce(member_count,0) > 0 then 1 "+
            "else (coalesce(member_count,0)-coalesce(member_count_yesterday,0))/coalesce(member_count_yesterday,0) end as decimal(15,2)) as `memberCountUp` " ;
    
    public static final String WeekMemberListSql =
            "merchant_id as `merchantId`, " +
            "month_week_num as `week`, " +
            "province_id as `provinceId`, " +
            "city_id as `cityId`, " +
            "district_id as `districtId`, " +
            "equipment_type_id as `equipmentTypeId`, " +
            "equipment_group_id as `groupId`, " +
            "equipment_type_name as `equipmentTypeName`, " +
            "coalesce(member_count,0) as `memberCount`, " +
            "coalesce(last_week_member_count,0) as `lastCycleMemberCount`, " +
            "cast(case when coalesce(last_week_member_count,0) =0 and coalesce(member_count,0) =0 then 0  " +
            "when coalesce(last_week_member_count,0) =0 and coalesce(member_count,0) > 0 then 1 "+
            "else (coalesce(member_count,0)-coalesce(last_week_member_count,0))/coalesce(last_week_member_count,0) end as decimal(15,2)) as `memberCountUp` " ;
    
    public static final String MonthMemberListSql =
            "merchant_id as `merchantId`, " +
            "year_month as `month`, " +
            "province_id as `provinceId`, " +
            "city_id as `cityId`, " +
            "district_id as `districtId`, " +
            "equipment_type_id as `equipmentTypeId`, " +
            "equipment_group_id as `groupId`, " +
            "equipment_type_name as `equipmentTypeName`, " +
            "coalesce(member_count,0) as `memberCount`, " +
            "coalesce(last_month_member_count,0) as `lastCycleMemberCount`, " +
            "cast(case when coalesce(last_month_member_count,0) =0 and coalesce(member_count,0) =0 then 0  " +
            "when coalesce(last_month_member_count,0) =0 and coalesce(member_count,0) > 0 then 1 "+
            "else (coalesce(member_count,0)-coalesce(last_month_member_count,0))/coalesce(last_month_member_count,0) end as decimal(15,2)) as `memberCountUp` " ;
    
    public static final String YearMemberListSql =
            "merchant_id as `merchantId`, " +
            "year_str as `year`, " +
            "province_id as `provinceId`, " +
            "city_id as `cityId`, " +
            "district_id as `districtId`, " +
            "equipment_type_id as `equipmentTypeId`, " +
            "equipment_group_id as `groupId`, " +
            "equipment_type_name as `equipmentTypeName`, " +
            "coalesce(member_count,0) as `memberCount`, " +
            "coalesce(last_year_member_count,0) as `lastCycleMemberCount`, " +
            "cast(case when coalesce(last_year_member_count,0) =0 and coalesce(member_count,0) =0 then 0  " +
            "when coalesce(last_year_member_count,0) =0 and coalesce(member_count,0) > 0 then 1 "+
            "else (coalesce(member_count,0)-coalesce(last_year_member_count,0))/coalesce(last_year_member_count,0) end as decimal(15,2)) as `memberCountUp` " ;
    
}
