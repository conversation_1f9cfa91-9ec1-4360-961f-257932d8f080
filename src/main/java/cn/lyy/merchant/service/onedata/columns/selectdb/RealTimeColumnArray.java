package cn.lyy.merchant.service.onedata.columns.selectdb;

public class RealTimeColumnArray {
	
	public static String RT_ORDER_DATA_COLUMN_ARRAY =
			"ad_org_id as `merchantId`," +
			//在线收入(元)
            //"cast(sum(coalesce(total_fee,0)/100 - coalesce(refund_fee,0)/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0)) as decimal(15,2)) as `onlineAmount`," +
            // 1：代理商分账，2：场地分账，3：还款分账，4：商户分账，5：商业化收费分账，6：增值服务收费分账，7：预充值分账，8：红包卡片升单分账，9：幸运购币分账，10：兑币机游戏出币分账，11：智能引流分账，12：找好抓分账，13：免单抽奖分账，14：新客宝分账，15：充电桩保险加购分账，16：支付服务费，17：综合服务签约分账，18：会员卡分账，19：充值抵扣金分账，20：拉客宝分账，21：复购易分账，22：平台收费强制分账，23：智能导购分账，24：跨品类引流分账，25：业务服务费强制分账，26：超净洗日卡分账，27：获易客分账，28：洗衣预充值活动，29：1元服务包，30：VIP福利活动，31：充电桩保险，32：升单复购抽佣，33：洗水一元服务包，34：洗水杀菌洗抽佣
            //business type=29是充电桩一元服务包。类似的商品还有按摩vip、超净洗、洗水一元服务包。都是B端订单中心、经营统计剔除了商品金额的
            //2024-03-28赵晓瑜、戴立斌提的优化
            "cast(sum(coalesce(total_fee,0)/100 - coalesce(refund_fee,0)/100 -if(business_type in(26,29,30,33),0,coalesce(json_extract(payment_detail,'$.lyyAmount'),0))-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0)) as decimal(15,2)) as `onlineAmount`," +
            "0 as  `onlineAmountUp`, "+

            //收款金额(元),不能扣减退款费用!
            "cast(sum(coalesce(total_fee,0)/100- if(business_type in(26,29,30,33),0,coalesce(json_extract(payment_detail,'$.lyyAmount'),0)) -coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0)) as decimal(15,2)) as `onlinePayAmount`," +

            "0 as  `onlinePayAmountUp`, "+

            "count(distinct case when type ='Pay' or type='Refund' then out_trade_no else null end) as `onlinePayCounts`," +
            "0 as  `onlinePayCountsUp`, "+

            "cast(sum(cast(coalesce(refund_fee,0)/100 as decimal(15,2))) as decimal(15,2)) as `onlineRefundAmount`, " +
            "0 as  `onlineRefundAmountUp`, "+

            "count(distinct case when type='Refund' and status='SUCCESS' then out_refund_no else null end) as `onlineRefundCounts`, " +
            "0 as  `onlineRefundCountsUp`, "+

            //现金收入先为0
            "0 as  `cashAmount`, "+
            "0 as  `cashAmountUp`, "+
            "0 as  `cashPayAmount`, "+
            "0 as  `cashPayAmountUp`, "+
            "0 as  `cashPayCounts`, "+
            "0 as  `cashPayCountsUp`, "+
            "0 as `cashRefundAmount`, 0 as `cashRefundAmountUp`, 0 as `cashRefundCounts`, 0 as `cashRefundCountsUp`," +       //现金没有退款

            "cast(sum(total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0)) as decimal(15,2)) as `payAmount`," +
            "0 as  `payAmountUp`, "+

            "count(distinct case when type ='Pay' or type='Refund' then out_trade_no else null end) as `payCounts`," +
            "0 as  `payCountsUp`, "+

            "cast(sum(cast(coalesce(refund_fee,0)/100 as decimal(15,2))) as decimal(15,2)) as `refundAmount`, " +
            "0 as  `refundAmountUp`, "+

            "count(distinct case when type='Refund' and status='SUCCESS' then out_refund_no else null end) as `refundCounts`, " +
            "0 as  `refundCounts` " ;
	
	public static String RT_TIME_RANGE_DATA_COLUMN_ARRAY =
			"ad_org_id as `merchantId`, " +
			"cast(sum(case when (type ='Pay' or type='Refund') and date_format(created,'%H')='00' then (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0)) else 0 end) as decimal(15,2)) as `amount_0_1`, " +
			"cast(sum(case when (type ='Pay' or type='Refund') and date_format(created,'%H')='01' then (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0)) else 0 end) as decimal(15,2)) as `amount_1_2`, " +
			"cast(sum(case when (type ='Pay' or type='Refund') and date_format(created,'%H')='02' then (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0)) else 0 end) as decimal(15,2)) as `amount_2_3`, " +
			"cast(sum(case when (type ='Pay' or type='Refund') and date_format(created,'%H')='03' then (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0)) else 0 end) as decimal(15,2)) as `amount_3_4`, " +
			"cast(sum(case when (type ='Pay' or type='Refund') and date_format(created,'%H')='04' then (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0)) else 0 end) as decimal(15,2)) as `amount_4_5`, " +
			"cast(sum(case when (type ='Pay' or type='Refund') and date_format(created,'%H')='05' then (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0)) else 0 end) as decimal(15,2)) as `amount_5_6`, " +
			"cast(sum(case when (type ='Pay' or type='Refund') and date_format(created,'%H')='06' then (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0)) else 0 end) as decimal(15,2)) as `amount_6_7`, " +
			"cast(sum(case when (type ='Pay' or type='Refund') and date_format(created,'%H')='07' then (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0)) else 0 end) as decimal(15,2)) as `amount_7_8`, " +
			"cast(sum(case when (type ='Pay' or type='Refund') and date_format(created,'%H')='08' then (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0)) else 0 end) as decimal(15,2)) as `amount_8_9`, " +
			"cast(sum(case when (type ='Pay' or type='Refund') and date_format(created,'%H')='09' then (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0)) else 0 end) as decimal(15,2)) as `amount_9_10`, " +
			"cast(sum(case when (type ='Pay' or type='Refund') and date_format(created,'%H')='10' then (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0)) else 0 end) as decimal(15,2)) as `amount_10_11`, " +
			"cast(sum(case when (type ='Pay' or type='Refund') and date_format(created,'%H')='11' then (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0)) else 0 end) as decimal(15,2)) as `amount_11_12`, " +
			"cast(sum(case when (type ='Pay' or type='Refund') and date_format(created,'%H')='12' then (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0)) else 0 end) as decimal(15,2)) as `amount_12_13`, " +
			"cast(sum(case when (type ='Pay' or type='Refund') and date_format(created,'%H')='13' then (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0)) else 0 end) as decimal(15,2)) as `amount_13_14`, " +
			"cast(sum(case when (type ='Pay' or type='Refund') and date_format(created,'%H')='14' then (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0)) else 0 end) as decimal(15,2)) as `amount_14_15`, " +
			"cast(sum(case when (type ='Pay' or type='Refund') and date_format(created,'%H')='15' then (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0)) else 0 end) as decimal(15,2)) as `amount_15_16`, " +
			"cast(sum(case when (type ='Pay' or type='Refund') and date_format(created,'%H')='16' then (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0)) else 0 end) as decimal(15,2)) as `amount_16_17`, " +
			"cast(sum(case when (type ='Pay' or type='Refund') and date_format(created,'%H')='17' then (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0)) else 0 end) as decimal(15,2)) as `amount_17_18`, " +
			"cast(sum(case when (type ='Pay' or type='Refund') and date_format(created,'%H')='18' then (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0)) else 0 end) as decimal(15,2)) as `amount_18_19`, " +
			"cast(sum(case when (type ='Pay' or type='Refund') and date_format(created,'%H')='19' then (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0)) else 0 end) as decimal(15,2)) as `amount_19_20`, " +
			"cast(sum(case when (type ='Pay' or type='Refund') and date_format(created,'%H')='20' then (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0)) else 0 end) as decimal(15,2)) as `amount_20_21`, " +
			"cast(sum(case when (type ='Pay' or type='Refund') and date_format(created,'%H')='21' then (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0)) else 0 end) as decimal(15,2)) as `amount_21_22`, " +
			"cast(sum(case when (type ='Pay' or type='Refund') and date_format(created,'%H')='22' then (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0)) else 0 end) as decimal(15,2)) as `amount_22_23`, " +
			"cast(sum(case when (type ='Pay' or type='Refund') and date_format(created,'%H')='23' then (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0)) else 0 end) as decimal(15,2)) as `amount_23_24`, " +
    
			"count(distinct case when (type ='Pay' or type='Refund') and date_format(created,'%H')='00' then out_trade_no else null end) as `count_0_1`, " +
            "count(distinct case when (type ='Pay' or type='Refund') and date_format(created,'%H')='01' then out_trade_no else null end) as `count_1_2`, " +
            "count(distinct case when (type ='Pay' or type='Refund') and date_format(created,'%H')='02' then out_trade_no else null end) as `count_2_3`, " +
            "count(distinct case when (type ='Pay' or type='Refund') and date_format(created,'%H')='03' then out_trade_no else null end) as `count_3_4`, " +
            "count(distinct case when (type ='Pay' or type='Refund') and date_format(created,'%H')='04' then out_trade_no else null end) as `count_4_5`, " +
            "count(distinct case when (type ='Pay' or type='Refund') and date_format(created,'%H')='05' then out_trade_no else null end) as `count_5_6`, " +
            "count(distinct case when (type ='Pay' or type='Refund') and date_format(created,'%H')='06' then out_trade_no else null end) as `count_6_7`, " +
            "count(distinct case when (type ='Pay' or type='Refund') and date_format(created,'%H')='07' then out_trade_no else null end) as `count_7_8`, " +
            "count(distinct case when (type ='Pay' or type='Refund') and date_format(created,'%H')='08' then out_trade_no else null end) as `count_8_9`, " +
            "count(distinct case when (type ='Pay' or type='Refund') and date_format(created,'%H')='09' then out_trade_no else null end) as `count_9_10`, " +
            "count(distinct case when (type ='Pay' or type='Refund') and date_format(created,'%H')='10' then out_trade_no else null end) as `count_10_11`, " +
            "count(distinct case when (type ='Pay' or type='Refund') and date_format(created,'%H')='11' then out_trade_no else null end) as `count_11_12`, " +
            "count(distinct case when (type ='Pay' or type='Refund') and date_format(created,'%H')='12' then out_trade_no else null end) as `count_12_13`, " +
            "count(distinct case when (type ='Pay' or type='Refund') and date_format(created,'%H')='13' then out_trade_no else null end) as `count_13_14`, " +
            "count(distinct case when (type ='Pay' or type='Refund') and date_format(created,'%H')='14' then out_trade_no else null end) as `count_14_15`, " +
            "count(distinct case when (type ='Pay' or type='Refund') and date_format(created,'%H')='15' then out_trade_no else null end) as `count_15_16`, " +
            "count(distinct case when (type ='Pay' or type='Refund') and date_format(created,'%H')='16' then out_trade_no else null end) as `count_16_17`, " +
            "count(distinct case when (type ='Pay' or type='Refund') and date_format(created,'%H')='17' then out_trade_no else null end) as `count_17_18`, " +
            "count(distinct case when (type ='Pay' or type='Refund') and date_format(created,'%H')='18' then out_trade_no else null end) as `count_18_19`, " +
            "count(distinct case when (type ='Pay' or type='Refund') and date_format(created,'%H')='19' then out_trade_no else null end) as `count_19_20`, " +
            "count(distinct case when (type ='Pay' or type='Refund') and date_format(created,'%H')='20' then out_trade_no else null end) as `count_20_21`, " +
            "count(distinct case when (type ='Pay' or type='Refund') and date_format(created,'%H')='21' then out_trade_no else null end) as `count_21_22`, " +
            "count(distinct case when (type ='Pay' or type='Refund') and date_format(created,'%H')='22' then out_trade_no else null end) as `count_22_23`, " +
            "count(distinct case when (type ='Pay' or type='Refund') and date_format(created,'%H')='23' then out_trade_no else null end) as `count_23_24` " ;

	public static String RT_PER_ORDER_PRICE_COUNT_COLUMN_ARRAY = 
			"ad_org_id as `merchantId`, " +
			
			"cast(sum(case when (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0))*100 >0 and (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0))*100 <=100 then (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0)) else 0 end) as decimal(15,2)) as `pay_0_1_amount` , " +
			"cast(sum(case when (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0))*100 >100 and (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0))*100 <=300 then (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0)) else 0 end) as decimal(15,2)) as `pay_1_3_amount` , " +
			"cast(sum(case when (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0))*100 >300 and (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0))*100 <=500 then (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0)) else 0 end) as decimal(15,2)) as `pay_3_5_amount` , " +
			"cast(sum(case when (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0))*100 >500 and (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0))*100 <=1000 then (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0)) else 0 end) as decimal(15,2)) as `pay_5_10_amount` , " +
			"cast(sum(case when (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0))*100 >1000 and (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0))*100 <=1500 then (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0)) else 0 end)  as decimal(15,2)) as pay_10_15_amount , " +
			"cast(sum(case when (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0))*100 >1500 and (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0))*100 <=3000 then (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0)) else 0 end)  as decimal(15,2)) as pay_15_30_amount , " +
			"cast(sum(case when (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0))*100 >3000 and (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0))*100 <=5000 then (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0)) else 0 end)  as decimal(15,2)) as pay_30_50_amount , " +
			"cast(sum(case when (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0))*100 >5000 and (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0))*100 <=10000 then (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0)) else 0 end)  as decimal(15,2)) as pay_50_100_amount , " +
			"cast(sum(case when (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0))*100 >10000 and (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0))*100 <=20000 then (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0)) else 0 end)  as decimal(15,2)) as pay_100_200_amount , " +
			"cast(sum(case when (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0))*100 >20000 and (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0))*100 <=50000 then (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0)) else 0 end)  as decimal(15,2)) as pay_200_500_amount , " +
			"cast(sum(case when (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0))*100 >50000 and (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0))*100 <=100000 then (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0)) else 0 end)  as decimal(15,2)) as pay_500_1000_amount , " +
			"cast(sum(case when (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0))*100 >100000 then (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0)) else 0 end) as decimal(15,2)) as over_1000_amount ,  " +

            "count(distinct case when (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0))*100 >0 and (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0))*100 <=100 then out_trade_no else null end) as pay_0_1_count , " +
            "count(distinct case when (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0))*100 >100 and (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0))*100 <=300 then out_trade_no else null end) as pay_1_3_count , " +
            "count(distinct case when (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0))*100 >300 and (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0))*100 <=500 then out_trade_no else null end) as pay_3_5_count , " +
            "count(distinct case when (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0))*100 >500 and (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0))*100 <=1000 then out_trade_no else null end) as pay_5_10_count , " +
            "count(distinct case when (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0))*100 >1000 and (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0))*100 <=1500 then out_trade_no else null end) as pay_10_15_count , " +
            "count(distinct case when (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0))*100 >1500 and (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0))*100 <=3000 then out_trade_no else null end) as pay_15_30_count , " +
            "count(distinct case when (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0))*100 >3000 and (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0))*100 <=5000 then out_trade_no else null end) as pay_30_50_count , " +
            "count(distinct case when (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0))*100 >5000 and (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0))*100 <=10000 then out_trade_no else null end) as pay_50_100_count , " +
            "count(distinct case when (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0))*100 >10000 and (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0))*100 <=20000 then out_trade_no else null end) as pay_100_200_count , " +
            "count(distinct case when (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0))*100 >20000 and (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0))*100 <=50000 then out_trade_no else null end) as pay_200_500_count , " +
            "count(distinct case when (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0))*100 >50000 and (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0))*100 <=100000 then out_trade_no else null end) as pay_500_1000_count , " +
            "count(distinct case when (total_fee/100 -coalesce(json_extract(payment_detail,'$.lyyAmount'),0)-coalesce(json_extract(payment_detail,'$.platformServiceFee'),0)-coalesce(json_extract(payment_detail,'$.businessServiceFee'),0))*100 >100000 then out_trade_no else null end) as over_1000_count  " ;

	public static String RT_MEMBER_DATA_COLUMN_ARRAY = 
			"ad_org_id as `merchantId`, " +
            "count(distinct lyy_user_id) as `memberCounts` , " +
            "0 as `memberCountsUp` , " +
            "count(distinct out_trade_no) as `orderCounts` , " +
            "0 as `orderCountsUp` , " +
            "0 as `activeRate` , " +
            "0 as `totalMember` , " +
            "case when count(distinct out_trade_no) = 0 then 0 " +
            "else cast(sum(cast(COALESCE(total_fee,0) as bigint))/(count(distinct out_trade_no)*100) as decimal(15,2)) end as `perMemberOrderAmount` " ;

	public static String RT_GROUP_DATA_COLUMN_ARRAY =
			"T0.ad_org_id as `merchantId`, " +
            "T1.equipment_group_id as `groupId`, " +
            "max(T1.equipment_group_name) as `groupName`, " +
            "max(T1.address) as `address`, " +
            "cast(sum((coalesce(total_fee,0) - (COALESCE(json_extract(payment_detail,'$.lyyAmount'),0) + COALESCE(json_extract(payment_detail,'$.platformServiceFee'),0) + COALESCE(json_extract(payment_detail,'$.businessServiceFee'),0))))/100 as decimal(15,2)) as `payAmount`, "+
            "count(distinct lyy_user_id) as `memberCounts` , " +
            "0 as `memberCountsUp` , " +
            "count(distinct out_trade_no) as `orderCounts` , " +
            "0 as `orderCountsUp` , " +
            "case when count(distinct out_trade_no) = 0 then 0 " +
            "else cast(sum((coalesce(total_fee,0) - (COALESCE(json_extract(payment_detail,'$.lyyAmount'),0) + COALESCE(json_extract(payment_detail,'$.platformServiceFee'),0) + COALESCE(json_extract(payment_detail,'$.businessServiceFee'),0))))/(count(distinct out_trade_no)*100) as decimal(15,2)) end as `perOrderAmount` " ;

}
