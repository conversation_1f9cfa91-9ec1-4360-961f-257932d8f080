package cn.lyy.merchant.service.onedata.mobile;

import com.alibaba.fastjson.JSONObject;

import cn.lyy.merchant.controller.onedata.dto.DayParamDto;
import cn.lyy.merchant.controller.onedata.dto.MonthParamDto;
import cn.lyy.merchant.controller.onedata.dto.WeekParamDto;
import cn.lyy.merchant.controller.onedata.dto.YearParamDto;

public interface MemberDataService {

	/**
     * 日报会员数据
     * @param dayParamDto
     * @return
     */
    public JSONObject getMemberData(DayParamDto dayParamDto);
    
    /**
     * 周报会员数据
     * @param weekParamDto
     * @return
     */
    public JSONObject getWeekMemberData(WeekParamDto weekParamDto);

    /**
     * 月报会员数据
     * @param monthParamDto
     * @return
     */
    public JSONObject getMonthMemberData(MonthParamDto monthParamDto);

    /**
     * 年报会员数据
     * @param yearParamDto
     * @return
     */
    public JSONObject getYearMemberData(YearParamDto yearParamDto);
    
}
