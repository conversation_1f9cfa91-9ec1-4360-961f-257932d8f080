package cn.lyy.merchant.service.onedata.mobile;

import com.alibaba.fastjson.JSONObject;

import cn.lyy.merchant.controller.onedata.dto.DayParamDto;
import cn.lyy.merchant.controller.onedata.dto.MonthParamDto;
import cn.lyy.merchant.controller.onedata.dto.WeekParamDto;
import cn.lyy.merchant.controller.onedata.dto.YearParamDto;

public interface EquipmentGroupDataService {

	/**
     * 日报场地，设备数据
     * @param dayParamDto
     * @return
     */
    public JSONObject getGroupEquipmentData(DayParamDto dayParamDto);

    /**
     * 周报场地，设备数据
     * @param weekParamDto
     * @return
     */
    public JSONObject getWeekGroupEquipmentData(WeekParamDto weekParamDto);

    /**
     * 月报场地，设备数据
     * @param monthParamDto
     * @return
     */
    public JSONObject getMonthGroupEquipmentData(MonthParamDto monthParamDto);

    /**
     * 年报场地，设备数据
     * @param yearParamDto
     * @return
     */
    public JSONObject getYearGroupEquipmentData(YearParamDto yearParamDto);
    
    /**
     * 日报获取场地维度统计信息
     * @param dayParamDto
     * @return
     */
    public JSONObject getDayGroupData(DayParamDto dayParamDto);

    /**
     * 周报报获取场地维度统计信息
     * @param weekParamDto
     * @return
     */
    public JSONObject getWeekGroupData(WeekParamDto weekParamDto);

    /**
     * 月报报获取场地维度统计信息
     * @param monthParamDto
     * @return
     */
    public JSONObject getMonthGroupData(MonthParamDto monthParamDto);

    /**
     * 年报报获取场地维度统计信息
     * @param yearParamDto
     * @return
     */
    public JSONObject getYearGroupData(YearParamDto yearParamDto);

    /**
     * 日报获取场地维度统计信息
     * @param dayParamDto
     * @return
     */
    public JSONObject getDaySumGroupData(DayParamDto dayParamDto);

    /**
     * 周报获取场地维度统计信息
     * @param weekParamDto
     * @return
     */
    public JSONObject getWeekSumGroupData(WeekParamDto weekParamDto);

    /**
     * 月报获取场地维度统计信息
     * @param monthParamDto
     * @return
     */
    public JSONObject getMonthSumGroupData(MonthParamDto monthParamDto);

    /**
     * 年报获取场地维度统计信息
     * @param yearParamDto
     * @return
     */
    public JSONObject getYearSumGroupData(YearParamDto yearParamDto);
    
}
