package cn.lyy.merchant.service.onedata.columns.pg;

public class RealTimeColumnArray {
	
	public static String RT_ORDER_DATA_COLUMN_ARRAY =
			"ad_org_id as \"merchantId\"," +
            "sum(((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))::decimal(15,2)) as \"onlineAmount\"," +
            "0 as  \"onlineAmountUp\", "+

            "cast(sum((coalesce(total_fee::bigint,0)-coalesce(refund_fee::numeric,0))::decimal(15,2)/100::decimal(15,2)-coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))as decimal(15,2)) as \"onlinePayAmount\"," +

            "0 as  \"onlinePayAmountUp\", "+

            "count(distinct case when type ='Pay' or type='Refund' then out_trade_no else null end) as \"onlinePayCounts\"," +
            "0 as  \"onlinePayCountsUp\", "+

            "sum(cast(coalesce(refund_fee::numeric,0)/100 as decimal(15,2))) as \"onlineRefundAmount\", " +
            "0 as  \"onlineRefundAmountUp\", "+

            "count(distinct case when type='Refund' and status='SUCCESS' then out_refund_no else null end) as \"onlineRefundCounts\", " +
            "0 as  \"onlineRefundCountsUp\", "+

            //现金收入先为0
            "0 as  \"cashAmount\", "+
            "0 as  \"cashAmountUp\", "+
            "0 as  \"cashPayAmount\", "+
            "0 as  \"cashPayAmountUp\", "+
            "0 as  \"cashPayCounts\", "+
            "0 as  \"cashPayCountsUp\", "+
            "0 as \"cashRefundAmount\", 0 as \"cashRefundAmountUp\", 0 as \"cashRefundCounts\", 0 as \"cashRefundCountsUp\"," +       //现金没有退款

            "sum(((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))::decimal(15,2)) as \"payAmount\"," +
            "0 as  \"payAmountUp\", "+

            "count(distinct case when type ='Pay' or type='Refund' then out_trade_no else null end) as \"payCounts\"," +
            "0 as  \"payCountsUp\", "+

            "sum(cast(coalesce(refund_fee::numeric,0)/100 as decimal(15,2))) as \"refundAmount\", " +
            "0 as  \"refundAmountUp\", "+

            "count(distinct case when type='Refund' and status='SUCCESS' then out_refund_no else null end) as \"refundCounts\", " +
            "0 as  \"refundCounts\" " ;
	
	public static String RT_TIME_RANGE_DATA_COLUMN_ARRAY =
			"ad_org_id as \"merchantId\", " +
            "sum(case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='00' then ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))::decimal(15,2) else 0 end) as \"amount_0_1\", " +
            "sum(case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='01' then ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))::decimal(15,2) else 0 end) as \"amount_1_2\", " +
            "sum(case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='02' then ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))::decimal(15,2) else 0 end) as \"amount_2_3\", " +
            "sum(case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='03' then ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))::decimal(15,2) else 0 end) as \"amount_3_4\", " +
            "sum(case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='04' then ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))::decimal(15,2) else 0 end) as \"amount_4_5\", " +
            "sum(case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='05' then ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))::decimal(15,2) else 0 end) as \"amount_5_6\", " +
            "sum(case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='06' then ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))::decimal(15,2) else 0 end) as \"amount_6_7\", " +
            "sum(case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='07' then ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))::decimal(15,2) else 0 end) as \"amount_7_8\", " +
            "sum(case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='08' then ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))::decimal(15,2) else 0 end) as \"amount_8_9\", " +
            "sum(case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='09' then ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))::decimal(15,2) else 0 end) as \"amount_9_10\", " +
            "sum(case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='10' then ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))::decimal(15,2) else 0 end) as \"amount_10_11\", " +
            "sum(case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='11' then ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))::decimal(15,2) else 0 end) as \"amount_11_12\", " +
            "sum(case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='12' then ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))::decimal(15,2) else 0 end) as \"amount_12_13\", " +
            "sum(case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='13' then ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))::decimal(15,2) else 0 end) as \"amount_13_14\", " +
            "sum(case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='14' then ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))::decimal(15,2) else 0 end) as \"amount_14_15\", " +
            "sum(case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='15' then ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))::decimal(15,2) else 0 end) as \"amount_15_16\", " +
            "sum(case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='16' then ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))::decimal(15,2) else 0 end) as \"amount_16_17\", " +
            "sum(case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='17' then ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))::decimal(15,2) else 0 end) as \"amount_17_18\", " +
            "sum(case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='18' then ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))::decimal(15,2) else 0 end) as \"amount_18_19\", " +
            "sum(case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='19' then ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))::decimal(15,2) else 0 end) as \"amount_19_20\", " +
            "sum(case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='20' then ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))::decimal(15,2) else 0 end) as \"amount_20_21\", " +
            "sum(case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='21' then ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))::decimal(15,2) else 0 end) as \"amount_21_22\", " +
            "sum(case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='22' then ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))::decimal(15,2) else 0 end) as \"amount_22_23\", " +
            "sum(case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='23' then ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))::decimal(15,2) else 0 end) as \"amount_23_24\", " +

            "count(distinct case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='00' then out_trade_no else null end) as \"count_0_1\", " +
            "count(distinct case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='01' then out_trade_no else null end) as \"count_1_2\", " +
            "count(distinct case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='02' then out_trade_no else null end) as \"count_2_3\", " +
            "count(distinct case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='03' then out_trade_no else null end) as \"count_3_4\", " +
            "count(distinct case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='04' then out_trade_no else null end) as \"count_4_5\", " +
            "count(distinct case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='05' then out_trade_no else null end) as \"count_5_6\", " +
            "count(distinct case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='06' then out_trade_no else null end) as \"count_6_7\", " +
            "count(distinct case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='07' then out_trade_no else null end) as \"count_7_8\", " +
            "count(distinct case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='08' then out_trade_no else null end) as \"count_8_9\", " +
            "count(distinct case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='09' then out_trade_no else null end) as \"count_9_10\", " +
            "count(distinct case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='10' then out_trade_no else null end) as \"count_10_11\", " +
            "count(distinct case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='11' then out_trade_no else null end) as \"count_11_12\", " +
            "count(distinct case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='12' then out_trade_no else null end) as \"count_12_13\", " +
            "count(distinct case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='13' then out_trade_no else null end) as \"count_13_14\", " +
            "count(distinct case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='14' then out_trade_no else null end) as \"count_14_15\", " +
            "count(distinct case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='15' then out_trade_no else null end) as \"count_15_16\", " +
            "count(distinct case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='16' then out_trade_no else null end) as \"count_16_17\", " +
            "count(distinct case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='17' then out_trade_no else null end) as \"count_17_18\", " +
            "count(distinct case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='18' then out_trade_no else null end) as \"count_18_19\", " +
            "count(distinct case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='19' then out_trade_no else null end) as \"count_19_20\", " +
            "count(distinct case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='20' then out_trade_no else null end) as \"count_20_21\", " +
            "count(distinct case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='21' then out_trade_no else null end) as \"count_21_22\", " +
            "count(distinct case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='22' then out_trade_no else null end) as \"count_22_23\", " +
            "count(distinct case when (type ='Pay' or type='Refund') and to_char(created,'HH24')='23' then out_trade_no else null end) as \"count_23_24\" " ;

	public static String RT_PER_ORDER_PRICE_COUNT_COLUMN_ARRAY = 
			"ad_org_id as \"merchantId\", " +
            "sum(case when ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))*100::decimal(15,2) >0 and ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))*100::decimal(15,2) <=100 then ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))::decimal(15,2) else 0 end) as \"pay_0_1_amount\" , " +
            "sum(case when ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))*100::decimal(15,2) >100 and ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))*100::decimal(15,2) <=300 then ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))::decimal(15,2) else 0 end) as \"pay_1_3_amount\" , " +
            "sum(case when ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))*100::decimal(15,2) >300 and ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))*100::decimal(15,2) <=500 then ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))::decimal(15,2) else 0 end) as \"pay_3_5_amount\" , " +
            "sum(case when ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))*100::decimal(15,2) >500 and ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))*100::decimal(15,2) <=1000 then ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))::decimal(15,2) else 0 end) as \"pay_5_10_amount\" , " +
            "sum(case when ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))*100::decimal(15,2) >1000 and ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))*100::decimal(15,2) <=1500 then ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))::decimal(15,2) else 0 end) as pay_10_15_amount , " +
            "sum(case when ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))*100::decimal(15,2) >1500 and ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))*100::decimal(15,2) <=3000 then ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))::decimal(15,2) else 0 end) as pay_15_30_amount , " +
            "sum(case when ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))*100::decimal(15,2) >3000 and ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))*100::decimal(15,2) <=5000 then ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))::decimal(15,2) else 0 end) as pay_30_50_amount , " +
            "sum(case when ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))*100::decimal(15,2) >5000 and ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))*100::decimal(15,2) <=10000 then ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))::decimal(15,2) else 0 end) as pay_50_100_amount , " +
            "sum(case when ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))*100::decimal(15,2) >10000 and ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))*100::decimal(15,2) <=20000 then ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))::decimal(15,2) else 0 end) as pay_100_200_amount , " +
            "sum(case when ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))*100::decimal(15,2) >20000 and ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))*100::decimal(15,2) <=50000 then ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))::decimal(15,2) else 0 end) as pay_200_500_amount , " +
            "sum(case when ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))*100::decimal(15,2) >50000 and ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))*100::decimal(15,2) <=100000 then ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))::decimal(15,2) else 0 end) as pay_500_1000_amount , " +
            "sum(case when ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))*100::decimal(15,2) >100000 then ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))::decimal(15,2) else 0 end) as over_1000_amount ,  " +
            "count(distinct case when ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))*100::decimal(15,2) >0 and ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))*100::decimal(15,2) <=100 then out_trade_no else null end) as pay_0_1_count , " +
            "count(distinct case when ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))*100::decimal(15,2) >100 and ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))*100::decimal(15,2) <=300 then out_trade_no else null end) as pay_1_3_count , " +
            "count(distinct case when ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))*100::decimal(15,2) >300 and ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))*100::decimal(15,2) <=500 then out_trade_no else null end) as pay_3_5_count , " +
            "count(distinct case when ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))*100::decimal(15,2) >500 and ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))*100::decimal(15,2) <=1000 then out_trade_no else null end) as pay_5_10_count , " +
            "count(distinct case when ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))*100::decimal(15,2) >1000 and ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))*100::decimal(15,2) <=1500 then out_trade_no else null end) as pay_10_15_count , " +
            "count(distinct case when ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))*100::decimal(15,2) >1500 and ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))*100::decimal(15,2) <=3000 then out_trade_no else null end) as pay_15_30_count , " +
            "count(distinct case when ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))*100::decimal(15,2) >3000 and ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))*100::decimal(15,2) <=5000 then out_trade_no else null end) as pay_30_50_count , " +
            "count(distinct case when ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))*100::decimal(15,2) >5000 and ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))*100::decimal(15,2) <=10000 then out_trade_no else null end) as pay_50_100_count , " +
            "count(distinct case when ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))*100::decimal(15,2) >10000 and ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))*100::decimal(15,2) <=20000 then out_trade_no else null end) as pay_100_200_count , " +
            "count(distinct case when ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))*100::decimal(15,2) >20000 and ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))*100::decimal(15,2) <=50000 then out_trade_no else null end) as pay_200_500_count , " +
            "count(distinct case when ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))*100::decimal(15,2) >50000 and ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))*100::decimal(15,2) <=100000 then out_trade_no else null end) as pay_500_1000_count , " +
            "count(distinct case when ((total_fee::decimal(15,2))/100::decimal(15,2) -coalesce((payment_detail->>'lyyAmount')::decimal(15,2),0)-coalesce((payment_detail->>'platformServiceFee')::decimal(15,2),0)-coalesce((payment_detail->>'businessServiceFee')::decimal(15,2),0))*100::decimal(15,2) >100000 then out_trade_no else null end) as over_1000_count  " ;

	public static String RT_MEMBER_DATA_COLUMN_ARRAY = 
			"ad_org_id as \"merchantId\", " +
            "count(distinct lyy_user_id) as \"memberCounts\" , " +
            "0 as \"memberCountsUp\" , " +
            "count(distinct out_trade_no) as \"orderCounts\" , " +
            "0 as \"orderCountsUp\" , " +
            "0 as \"activeRate\" , " +
            "0 as \"totalMember\" , " +
            "case when count(distinct out_trade_no) = 0 then 0 " +
            "else cast(sum(COALESCE(total_fee::numeric,0))::numeric/(count(distinct out_trade_no)*100) as decimal(15,2)) end as \"perMemberOrderAmount\" " ;

	public static String RT_GROUP_DATA_COLUMN_ARRAY =
			"T0.ad_org_id as \"merchantId\", " +
            "T1.equipment_group_id as \"groupId\", " +
            "max(T1.equipment_group_name) as \"groupName\", " +
            "max(T1.address) as \"address\", " +
            "cast(sum((coalesce(total_fee::bigint,0) - (COALESCE((payment_detail->>'lyyAmount')::decimal(15,2),0) + COALESCE((payment_detail->>'platformServiceFee')::decimal(15,2),0) + COALESCE((payment_detail->>'businessServiceFee')::decimal(15,2),0))))::numeric/100 as decimal(15,2)) as \"payAmount\", "+
            "count(distinct lyy_user_id) as \"memberCounts\" , " +
            "0 as \"memberCountsUp\" , " +
            "count(distinct out_trade_no) as \"orderCounts\" , " +
            "0 as \"orderCountsUp\" , " +
            "case when count(distinct out_trade_no) = 0 then 0 " +
            "else cast(sum((coalesce(total_fee::bigint,0) - (COALESCE((payment_detail->>'lyyAmount')::decimal(15,2),0) + COALESCE((payment_detail->>'platformServiceFee')::decimal(15,2),0) + COALESCE((payment_detail->>'businessServiceFee')::decimal(15,2),0))))::numeric/(count(distinct out_trade_no)*100) as decimal(15,2)) end as \"perOrderAmount\" " ;

}
