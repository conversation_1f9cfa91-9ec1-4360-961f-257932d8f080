package cn.lyy.merchant.service.onedata.mobile;

import cn.lyy.merchant.controller.onedata.dto.DayParamDto;
import cn.lyy.merchant.controller.onedata.dto.MonthParamDto;
import cn.lyy.merchant.controller.onedata.dto.WeekParamDto;
import cn.lyy.merchant.controller.onedata.dto.YearParamDto;
import com.alibaba.fastjson.JSONObject;

public interface ProvinceDataService {

    
    //public JSONObject getProvince(DayParamDto dayParamDto);

    /**
     * 日报获取省份维度统计信息
     * @param dayParamDto
     * @return
     */
    public JSONObject getDayProvinceData(DayParamDto dayParamDto);

    /**
     * 周报报获取省份维度统计信息
     * @param weekParamDto
     * @return
     */
    public JSONObject getWeekProvinceData(WeekParamDto weekParamDto);

    /**
     * 月报报获取省份维度统计信息
     * @param monthParamDto
     * @return
     */
    public JSONObject getMonthProvinceData(MonthParamDto monthParamDto);

    /**
     * 年报报获取省份维度统计信息
     * @param yearParamDto
     * @return
     */
    public JSONObject getYearProvinceData(YearParamDto yearParamDto);
    
    /**
     * 日报获取省份维度统计信息-底部平均值
     * @param dayParamDto
     * @return
     */
    public JSONObject getDayProvinceAvgData(DayParamDto dayParamDto);

    /**
     * 周报报获取省份维度统计信息-底部平均值
     * @param weekParamDto
     * @return
     */
    public JSONObject getWeekProvinceAvgData(WeekParamDto weekParamDto);

    /**
     * 月报报获取省份维度统计信息-底部平均值
     * @param monthParamDto
     * @return
     */
    public JSONObject getMonthProvinceAvgData(MonthParamDto monthParamDto);

    /**
     * 年报报获取省份维度统计信息-底部平均值
     * @param yearParamDto
     * @return
     */
    public JSONObject getYearProvinceAvgData(YearParamDto yearParamDto);

}
