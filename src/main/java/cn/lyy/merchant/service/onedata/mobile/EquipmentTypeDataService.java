package cn.lyy.merchant.service.onedata.mobile;

import com.alibaba.fastjson.JSONObject;
import cn.lyy.merchant.controller.onedata.dto.DayParamDto;
import cn.lyy.merchant.controller.onedata.dto.MonthParamDto;
import cn.lyy.merchant.controller.onedata.dto.WeekParamDto;
import cn.lyy.merchant.controller.onedata.dto.YearParamDto;

public interface EquipmentTypeDataService {

	 /**
     * 日报获取设备类型列表数据
     * @param dayParamDto
     * @return
     */
    public JSONObject getDayEquipmentTypeData(DayParamDto dayParamDto);

    /**
     * 周报获取设备类型列表数据
     * @param weekParamDto
     * @return
     */
    public JSONObject getWeekEquipmentTypeData(WeekParamDto weekParamDto);

    /**
     * 月报获取设备类型列表数据
     * @param monthParamDto
     * @return
     */
    public JSONObject getMonthEquipmentTypeData(MonthParamDto monthParamDto);

    /**
     * 年报获取设备类型列表数据
     * @param yearParamDto
     * @return
     */
    public JSONObject getYearEquipmentTypeData(YearParamDto yearParamDto);

}
