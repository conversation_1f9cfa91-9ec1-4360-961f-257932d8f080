package cn.lyy.merchant.service.onedata.columns.pg;

public class EquipmentGroupColumnArray {

	public static final String DAY_EQUIPMENT_GROUP_COLUMN_ARRAY = 
		"merchant_id as \"merchantId\", " +
		
		"pay_equipment as \"payEquipments\"," +     //支付设备
		"cast(case when pay_equipment_yesterday =0 and pay_equipment>0 then 1 when pay_equipment_yesterday=0 and pay_equipment=0 then 0 else (pay_equipment-pay_equipment_yesterday)::numeric/pay_equipment_yesterday::numeric end as decimal(15,2)) as \"payEquipmentsUp\"," +
		
		"equipment_count as \"equipmentCounts\", " +        //设备总数
		"cast(case when equipment_count_yesterday =0 and equipment_count>0 then 1 when equipment_count_yesterday=0 and equipment_count=0 then 0 else (equipment_count-equipment_count_yesterday)::numeric/equipment_count_yesterday::numeric end as decimal(15,2)) as \"equipmentCountsUp\"," +
		
		"online_00_position_num as \"onlineEquipmentCounts\", " +       //在线设备数
		"cast(case when online_00_position_num_yesterday =0 and online_00_position_num>0 then 1 when online_00_position_num_yesterday=0 and online_00_position_num=0 then 0 else (online_00_position_num-online_00_position_num_yesterday)::numeric/online_00_position_num_yesterday::numeric end as decimal(15,2)) as \"onlineEquipmentCountsUp\"," +
		
		"total_start_counts as \"startCounts\", " +       //启动次数
		"cast(case when total_start_counts_yesterday =0 and total_start_counts>0 then 1 when total_start_counts_yesterday=0 and total_start_counts=0 then 0 else (total_start_counts-total_start_counts_yesterday)::numeric/total_start_counts_yesterday::numeric end as decimal(15,2)) as \"startCountsUp\"," +
		//每台设备平均启动次数
		"cast(start_per_equipment as decimal(15,2)) as \"perEquipmentStarts\"," +   //设备启动均次
		"cast(case when start_per_equipment_yesterday =0 and start_per_equipment>0 then 1 when start_per_equipment_yesterday=0 and start_per_equipment=0 then 0 else (start_per_equipment-start_per_equipment_yesterday)::numeric/start_per_equipment_yesterday::numeric end as decimal(15,2)) as \"perEquipmentStartsUp\"," +
		
		"total_start_counts/1 as \"perEquipmentDayStarts\"," +   //设备日均启动均次 设备启动次数/账期日
		"cast(case when total_start_counts_yesterday =0 and total_start_counts>0 then 1 when total_start_counts_yesterday=0 and total_start_counts=0 then 0 else (total_start_counts-total_start_counts_yesterday)::numeric/total_start_counts_yesterday::numeric end as decimal(15,2)) as \"perEquipmentDayStartsUp\"," +
		
		"equipment_group_count as \"groupCounts\"," +        //场地总数
		"cast(case when equipment_group_count_yesterday =0 and equipment_group_count>0 then 1 when equipment_group_count_yesterday=0 and equipment_group_count=0 then 0 else (equipment_group_count-equipment_group_count_yesterday)::numeric/equipment_group_count_yesterday::numeric end as decimal(15,2)) as \"groupCountsUp\"," +
		
		"cast(case when equipment_group_count=0 then 0 else pay_amount::numeric/equipment_group_count::numeric end as decimal(15,2)) as \"perGroupAmount\"," +        //场地均收
		"case when (equipment_group_count =0 or pay_amount=0) and (equipment_group_count_yesterday = 0 or pay_amount_yesterday=0) then 0 " +
		"when (equipment_group_count_yesterday = 0 or pay_amount_yesterday=0) and pay_amount > 0 then 1 " +
		//解决除0 问题
		"when (equipment_group_count = 0 and equipment_group_count_yesterday = 0) then 0 " +
		"when (equipment_group_count = 0 and equipment_group_count_yesterday <> 0) then -1 " +
		"when (equipment_group_count <> 0 and equipment_group_count_yesterday = 0) then 1 " +
		//解决除0 问题
		"else cast( ((pay_amount::numeric/equipment_group_count::numeric) - (pay_amount_yesterday::numeric/equipment_group_count_yesterday::numeric))::numeric/(pay_amount_yesterday::numeric/equipment_group_count_yesterday::numeric)::numeric as decimal(15,2)) end  as \"perGroupAmountUp\","+
		
		"cast(case when equipment_count=0 then 0 else pay_amount::numeric/equipment_count::numeric end as decimal(15,2)) as \"perEquipmentAmount\"," +        //设备均收
		"case when (equipment_count =0 or pay_amount=0) and (equipment_count_yesterday = 0 or pay_amount_yesterday=0) then 0 " +
		"when (equipment_count_yesterday = 0 or pay_amount_yesterday=0) and pay_amount > 0 then 1 " +
		//解决除0 问题
		"when (equipment_group_count = 0 and equipment_group_count_yesterday = 0) then 0 " +
		"when (equipment_group_count = 0 and equipment_group_count_yesterday <> 0) then -1 " +
		"when (equipment_group_count <> 0 and equipment_group_count_yesterday = 0) then 1 " +
		//解决除0 问题
		"else cast( ((pay_amount::numeric/equipment_count::numeric) - (pay_amount_yesterday::numeric/equipment_count_yesterday::numeric))::numeric/(pay_amount_yesterday::numeric/equipment_count_yesterday::numeric)::numeric as decimal(15,2)) end  as \"perEquipmentAmountUp\","+
		
		"cast(case when equipment_group_count=0 then 0 else order_count::numeric/equipment_group_count::numeric end as decimal(15,2)) as \"perGroupOrder\"," +        //场地订单均数
		"case when (equipment_group_count =0 or order_count=0) and (equipment_group_count_yesterday = 0 or order_count_yesterday=0) then 0 " +
		"when (equipment_group_count_yesterday = 0 or order_count_yesterday=0) and order_count > 0 then 1 " +
		//解决除0 问题
		"when (equipment_group_count = 0 and equipment_group_count_yesterday = 0) then 0 " +
		"when (equipment_group_count = 0 and equipment_group_count_yesterday <> 0) then -1 " +
		"when (equipment_group_count <> 0 and equipment_group_count_yesterday = 0) then 1 " +
		//解决除0 问题
		"else cast( ((order_count::numeric/equipment_group_count::numeric) - (order_count_yesterday::numeric/equipment_group_count_yesterday::numeric))::numeric/(order_count_yesterday::numeric/equipment_group_count_yesterday::numeric)::numeric as decimal(15,2)) end  as \"perGroupOrderUp\","+
		
		
		"cast(case when equipment_count=0 then 0 else order_count::numeric/equipment_count::numeric end as decimal(15,2)) as \"perEquipmentOrder\"," +        //设备订单均数
		"case when (equipment_count =0 or order_count=0) and (equipment_count_yesterday = 0 or order_count_yesterday=0) then 0 " +
		"when (equipment_count_yesterday = 0 or order_count_yesterday=0) and order_count > 0 then 1 " +
		"when (equipment_group_count = 0 and equipment_group_count_yesterday=0) then 0 " +
		"when (equipment_group_count = 0 and equipment_group_count_yesterday<>0) then -1 " +
		"when (equipment_group_count <> 0 and equipment_group_count_yesterday=0) then 1 " +
		"else cast( ((order_count::numeric/equipment_count::numeric) - (order_count_yesterday::numeric/equipment_count_yesterday::numeric))::numeric/(order_count_yesterday::numeric/equipment_count_yesterday::numeric)::numeric as decimal(15,2)) end  as \"perEquipmentOrderUp\","+
		
		
		
		"cast(case when equipment_group_count=0 then 0 else total_start_counts::numeric/equipment_group_count::numeric end as decimal(15,2)) as \"perGroupStart\"," +        //场地启动均次
		"case when (equipment_group_count_yesterday = 0 or total_start_counts_yesterday=0) and (total_start_counts = 0 or equipment_group_count =0) then 0 when (equipment_group_count_yesterday = 0 or total_start_counts_yesterday=0) and total_start_counts > 0 then 1 " +
		//解决除0 问题
		"when (equipment_group_count = 0 and equipment_group_count_yesterday = 0) then 0 " +
		"when (equipment_group_count = 0 and equipment_group_count_yesterday <> 0) then -1 " +
		"when (equipment_group_count <> 0 and equipment_group_count_yesterday = 0) then 1 " +
		//解决除0 问题
		"else cast( ((total_start_counts::numeric/equipment_group_count::numeric) - (total_start_counts_yesterday::numeric/equipment_group_count_yesterday::numeric))/(total_start_counts_yesterday::numeric/equipment_group_count_yesterday::numeric) as decimal(15,2)) end  as \"perGroupStartUp\","+
		
		
		"cast(case when equipment_group_count=0 then 0 else equipment_count::numeric/equipment_group_count::numeric end as decimal(15,2)) as \"perGroupEquipment\","+        //场地设备均数
		"case when (equipment_group_count_yesterday =0 or equipment_count_yesterday =0) and (equipment_count=0 or equipment_group_count=0) then 0 "+
		"when (equipment_group_count_yesterday = 0 or equipment_count_yesterday=0) and equipment_count > 0 then 1 " +
		//解决除0 问题
		"when (equipment_group_count = 0 and equipment_group_count_yesterday = 0) then 0 " +
		"when (equipment_group_count = 0 and equipment_group_count_yesterday <> 0) then -1 " +
		"when (equipment_group_count <> 0 and equipment_group_count_yesterday = 0) then 1 " +
		//解决除0 问题
		"else cast( ((equipment_count::numeric/equipment_group_count::numeric) - (equipment_count_yesterday::numeric/equipment_group_count_yesterday::numeric))/(equipment_count_yesterday::numeric/equipment_group_count_yesterday::numeric) as decimal(15,2)) end as \"perGroupEquipmentUp\"" ;
	
	
	public static final String WEEK_EQUIPMENT_GROUP_COLUMN_ARRAY = 
		"merchant_id as \"merchantId\", " +
		
		"pay_equipment as \"payEquipments\"," +     //支付设备
		"cast(case when last_cycle_pay_equipment =0 and pay_equipment>0 then 1 when last_cycle_pay_equipment=0 and pay_equipment=0 then 0 else (pay_equipment-last_cycle_pay_equipment)::numeric/last_cycle_pay_equipment::numeric end as decimal(15,2)) as \"payEquipmentsUp\"," +
		
		"equipment_count as \"equipmentCounts\", " +        //设备总数
		"cast(case when last_cycle_equipment_count =0 and equipment_count>0 then 1 when last_cycle_equipment_count=0 and equipment_count=0 then 0 else (equipment_count-last_cycle_equipment_count)::numeric/last_cycle_equipment_count::numeric end as decimal(15,2)) as \"equipmentCountsUp\"," +
		
		"online_00_position_num as \"onlineEquipmentCounts\", " +       //在线设备数
		"cast(case when last_cycle_online_00_position_num =0 and online_00_position_num>0 then 1 when last_cycle_online_00_position_num=0 and online_00_position_num=0 then 0 else (online_00_position_num-last_cycle_online_00_position_num)::numeric/last_cycle_online_00_position_num::numeric end as decimal(15,2)) as \"onlineEquipmentCountsUp\"," +
		
		"total_start_counts as \"startCounts\", " +       //启动次数
		"cast(case when last_cycle_total_start_counts =0 and total_start_counts>0 then 1 when last_cycle_total_start_counts=0 and total_start_counts=0 then 0 else (total_start_counts-last_cycle_total_start_counts)::numeric/last_cycle_total_start_counts::numeric end as decimal(15,2)) as \"startCountsUp\"," +
		//每台设备平均启动次数
		"cast(start_per_equipment as decimal(15,2)) as \"perEquipmentStarts\"," +   //设备启动均次
		"cast(case when last_cycle_start_per_equipment =0 and start_per_equipment>0 then 1 when last_cycle_start_per_equipment=0 and start_per_equipment=0 then 0 else (start_per_equipment-last_cycle_start_per_equipment)::numeric/last_cycle_start_per_equipment::numeric end as decimal(15,2)) as \"perEquipmentStartsUp\"," +
		
		"cast(total_start_counts::numeric/7 as decimal(15,2)) as \"perEquipmentDayStarts\"," +   //设备日均启动均次 设备启动次数/账期日
		"cast(case when last_cycle_total_start_counts =0 and total_start_counts>0 then 1 when last_cycle_total_start_counts=0 and total_start_counts=0 then 0 else (total_start_counts-last_cycle_total_start_counts)::numeric/last_cycle_total_start_counts::numeric end as decimal(15,2)) as \"perEquipmentDayStartsUp\"," +
		
		"equipment_group_count as \"groupCounts\"," +        //场地总数
		"cast(case when last_cycle_equipment_group_count =0 and equipment_group_count>0 then 1 when last_cycle_equipment_group_count=0 and equipment_group_count=0 then 0 else (equipment_group_count-last_cycle_equipment_group_count)::numeric/last_cycle_equipment_group_count::numeric end as decimal(15,2)) as \"groupCountsUp\"," +
		
		"cast(case when equipment_group_count=0 then 0 else pay_amount::numeric/equipment_group_count::numeric end as decimal(15,2)) as \"perGroupAmount\"," +        //场地均收
		"case when (equipment_group_count =0 or pay_amount=0) and (last_cycle_equipment_group_count = 0 or last_cycle_pay_amount=0) then 0 " +
		"when (last_cycle_equipment_group_count = 0 or last_cycle_pay_amount=0) and pay_amount > 0 then 1 " +
		//解决除0 问题
  		"when (equipment_group_count = 0 and last_cycle_equipment_group_count = 0) then 0 " +
  		"when (equipment_group_count = 0 and last_cycle_equipment_group_count <> 0) then -1 " +
  		"when (equipment_group_count <> 0 and last_cycle_equipment_group_count = 0) then 1 " +
  		//解决除0 问题
		"else cast( ((pay_amount::numeric/equipment_group_count::numeric) - (last_cycle_pay_amount::numeric/last_cycle_equipment_group_count::numeric))::numeric/(last_cycle_pay_amount::numeric/last_cycle_equipment_group_count::numeric)::numeric as decimal(15,2)) end  as \"perGroupAmountUp\","+
		
		
		"cast(case when equipment_count=0 then 0 else pay_amount::numeric/equipment_count::numeric end as decimal(15,2)) as \"perEquipmentAmount\"," +        //设备均收
		"case when (equipment_count =0 or pay_amount=0) and (last_cycle_equipment_count = 0 or last_cycle_pay_amount=0) then 0 " +
		"when (last_cycle_equipment_count = 0 or last_cycle_pay_amount=0) and pay_amount > 0 then 1 " +
		//解决除0 问题
  		"when (equipment_group_count = 0 and last_cycle_equipment_group_count = 0) then 0 " +
  		"when (equipment_group_count = 0 and last_cycle_equipment_group_count <> 0) then -1 " +
  		"when (equipment_group_count <> 0 and last_cycle_equipment_group_count = 0) then 1 " +
  		//解决除0 问题
		"else cast( ((pay_amount::numeric/equipment_count::numeric) - (last_cycle_pay_amount::numeric/last_cycle_equipment_count::numeric))::numeric/(last_cycle_pay_amount::numeric/last_cycle_equipment_count::numeric)::numeric as decimal(15,2)) end  as \"perEquipmentAmountUp\","+
		
		
		
		
		"cast(case when equipment_count=0 then 0 else order_count::numeric/equipment_count::numeric end as decimal(15,2)) as \"perEquipmentOrder\"," +        //设备订单均数
		"case when (equipment_count =0 or order_count=0) and (last_cycle_equipment_count = 0 or last_cycle_order_count=0) then 0 " +
		"when (last_cycle_equipment_count = 0 or last_cycle_order_count=0) and order_count > 0 then 1 " +
		//解决除0 问题
  		"when (equipment_group_count = 0 and last_cycle_equipment_group_count = 0) then 0 " +
  		"when (equipment_group_count = 0 and last_cycle_equipment_group_count <> 0) then -1 " +
  		"when (equipment_group_count <> 0 and last_cycle_equipment_group_count = 0) then 1 " +
  		//解决除0 问题
		"else cast( ((order_count::numeric/equipment_count::numeric) - (last_cycle_order_count::numeric/last_cycle_equipment_count::numeric))::numeric/(last_cycle_order_count::numeric/last_cycle_equipment_count::numeric)::numeric as decimal(15,2)) end  as \"perEquipmentOrderUp\","+
		
		
		
		"cast(case when equipment_group_count=0 then 0 else order_count::numeric/equipment_group_count::numeric end as decimal(15,2)) as \"perGroupOrder\"," +        //场地订单均数
		"case when (equipment_group_count =0 or order_count=0) and (last_cycle_equipment_group_count = 0 or last_cycle_order_count=0) then 0 " +
		"when (last_cycle_equipment_group_count = 0 or last_cycle_order_count=0) and order_count > 0 then 1 " +
		//解决除0 问题
  		"when (equipment_group_count = 0 and last_cycle_equipment_group_count = 0) then 0 " +
  		"when (equipment_group_count = 0 and last_cycle_equipment_group_count <> 0) then -1 " +
  		"when (equipment_group_count <> 0 and last_cycle_equipment_group_count = 0) then 1 " +
  		//解决除0 问题
		"else cast( ((order_count::numeric/equipment_group_count::numeric) - (last_cycle_order_count::numeric/last_cycle_equipment_group_count::numeric))::numeric/(last_cycle_order_count::numeric/last_cycle_equipment_group_count::numeric)::numeric as decimal(15,2)) end  as \"perGroupOrderUp\","+
		
		
		"cast(case when equipment_group_count=0 then 0 else total_start_counts::numeric/equipment_group_count::numeric end as decimal(15,2)) as \"perGroupStart\"," +        //场地启动均次
		"case when (last_cycle_equipment_group_count = 0 or last_cycle_total_start_counts=0) and (total_start_counts = 0 or equipment_group_count =0) then 0 when (last_cycle_equipment_group_count = 0 or last_cycle_total_start_counts=0) and total_start_counts > 0 then 1 " +
		//解决除0 问题
  		"when (equipment_group_count = 0 and last_cycle_equipment_group_count = 0) then 0 " +
  		"when (equipment_group_count = 0 and last_cycle_equipment_group_count <> 0) then -1 " +
  		"when (equipment_group_count <> 0 and last_cycle_equipment_group_count = 0) then 1 " +
  		//解决除0 问题
		"else cast( ((total_start_counts::numeric/equipment_group_count::numeric) - (last_cycle_total_start_counts::numeric/last_cycle_equipment_group_count::numeric))/(last_cycle_total_start_counts::numeric/last_cycle_equipment_group_count::numeric) as decimal(15,2)) end  as \"perGroupStartUp\","+
		
		
		"cast(case when equipment_group_count=0 then 0 else equipment_count::numeric/equipment_group_count::numeric end as decimal(15,2)) as \"perGroupEquipment\","+        //场地设备均数
		"case when (last_cycle_equipment_group_count =0 or last_cycle_equipment_count =0) and (equipment_count=0 or equipment_group_count=0) then 0 "+
		"when (last_cycle_equipment_group_count = 0 or last_cycle_equipment_count=0) and equipment_count > 0 then 1 " +
		//解决除0 问题
  		"when (equipment_group_count = 0 and last_cycle_equipment_group_count = 0) then 0 " +
  		"when (equipment_group_count = 0 and last_cycle_equipment_group_count <> 0) then -1 " +
  		"when (equipment_group_count <> 0 and last_cycle_equipment_group_count = 0) then 1 " +
  		//解决除0 问题
		"else cast( ((equipment_count::numeric/equipment_group_count::numeric) - (last_cycle_equipment_count::numeric/last_cycle_equipment_group_count::numeric))/(last_cycle_equipment_count::numeric/last_cycle_equipment_group_count::numeric) as decimal(15,2)) end as \"perGroupEquipmentUp\"" ;
		

	public static final String MONTH_EQUIPMENT_GROUP_COLUMN_ARRAY = 
		"merchant_id as \"merchantId\", " +
		
		"pay_equipment as \"payEquipments\"," +     //支付设备
		"cast(case when last_cycle_pay_equipment =0 and pay_equipment>0 then 1 when last_cycle_pay_equipment=0 and pay_equipment=0 then 0 else (pay_equipment-last_cycle_pay_equipment)::numeric/last_cycle_pay_equipment::numeric end as decimal(15,2)) as \"payEquipmentsUp\"," +
		
		"equipment_count as \"equipmentCounts\", " +        //设备总数
		"cast(case when last_cycle_equipment_count =0 and equipment_count>0 then 1 when last_cycle_equipment_count=0 and equipment_count=0 then 0 else (equipment_count-last_cycle_equipment_count)::numeric/last_cycle_equipment_count::numeric end as decimal(15,2)) as \"equipmentCountsUp\"," +
		
		"online_00_position_num as \"onlineEquipmentCounts\", " +       //在线设备数
		"cast(case when last_cycle_online_00_position_num =0 and online_00_position_num>0 then 1 when last_cycle_online_00_position_num=0 and online_00_position_num=0 then 0 else (online_00_position_num-last_cycle_online_00_position_num)::numeric/last_cycle_online_00_position_num::numeric end as decimal(15,2)) as \"onlineEquipmentCountsUp\"," +
		
		"total_start_counts as \"startCounts\", " +       //启动次数
		"cast(case when last_cycle_total_start_counts =0 and total_start_counts>0 then 1 when last_cycle_total_start_counts=0 and total_start_counts=0 then 0 else (total_start_counts-last_cycle_total_start_counts)::numeric/last_cycle_total_start_counts::numeric end as decimal(15,2)) as \"startCountsUp\"," +
		//每台设备平均启动次数
		"cast(start_per_equipment as decimal(15,2)) as \"perEquipmentStarts\"," +   //设备启动均次
		"cast(case when last_cycle_start_per_equipment =0 and start_per_equipment>0 then 1 when last_cycle_start_per_equipment=0 and start_per_equipment=0 then 0 else (start_per_equipment-last_cycle_start_per_equipment)::numeric/last_cycle_start_per_equipment::numeric end as decimal(15,2)) as \"perEquipmentStartsUp\"," +
		
		"cast(total_start_counts::numeric/30 as decimal(15,2)) as \"perEquipmentDayStarts\"," +   //设备日均启动均次 设备启动次数/账期日
		"cast(case when last_cycle_total_start_counts =0 and total_start_counts>0 then 1 when last_cycle_total_start_counts=0 and total_start_counts=0 then 0 else (total_start_counts-last_cycle_total_start_counts)::numeric/last_cycle_total_start_counts::numeric end as decimal(15,2)) as \"perEquipmentDayStartsUp\"," +
		
		"equipment_group_count as \"groupCounts\"," +        //场地总数
		"cast(case when last_cycle_equipment_group_count =0 and equipment_group_count>0 then 1 when last_cycle_equipment_group_count=0 and equipment_group_count=0 then 0 else (equipment_group_count-last_cycle_equipment_group_count)::numeric/last_cycle_equipment_group_count::numeric end as decimal(15,2)) as \"groupCountsUp\"," +
		
		"cast(case when equipment_group_count=0 then 0 else pay_amount::numeric/equipment_group_count::numeric end as decimal(15,2)) as \"perGroupAmount\"," +        //场地均收
		"case when (equipment_group_count =0 or pay_amount=0) and (last_cycle_equipment_group_count = 0 or last_cycle_pay_amount=0) then 0 " +
		"when (last_cycle_equipment_group_count = 0 or last_cycle_pay_amount=0) and pay_amount > 0 then 1 " +
		//解决除0 问题
		"when (equipment_group_count = 0 and last_cycle_equipment_group_count = 0) then 0 " +
		"when (equipment_group_count = 0 and last_cycle_equipment_group_count <> 0) then -1 " +
		"when (equipment_group_count <> 0 and last_cycle_equipment_group_count = 0) then 1 " +
		//解决除0 问题
		"else cast( ((pay_amount::numeric/equipment_group_count::numeric) - (last_cycle_pay_amount::numeric/last_cycle_equipment_group_count::numeric))::numeric/(last_cycle_pay_amount::numeric/last_cycle_equipment_group_count::numeric)::numeric as decimal(15,2)) end  as \"perGroupAmountUp\","+
		
		"cast(case when equipment_group_count=0 then 0 else order_count::numeric/equipment_group_count::numeric end as decimal(15,2)) as \"perGroupOrder\"," +        //场地订单均数
		"case when (equipment_group_count =0 or order_count=0) and (last_cycle_equipment_group_count = 0 or last_cycle_order_count=0) then 0 " +
		"when (last_cycle_equipment_group_count = 0 or last_cycle_order_count=0) and order_count > 0 then 1 " +
		//解决除0 问题
  		"when (equipment_group_count = 0 and last_cycle_equipment_group_count = 0) then 0 " +
  		"when (equipment_group_count = 0 and last_cycle_equipment_group_count <> 0) then -1 " +
  		"when (equipment_group_count <> 0 and last_cycle_equipment_group_count = 0) then 1 " +
  		//解决除0 问题
		"else cast( ((order_count::numeric/equipment_group_count::numeric) - (last_cycle_order_count::numeric/last_cycle_equipment_group_count::numeric))::numeric/(last_cycle_order_count::numeric/last_cycle_equipment_group_count::numeric)::numeric as decimal(15,2)) end  as \"perGroupOrderUp\","+
		
		
		"cast(case when equipment_count=0 then 0 else pay_amount::numeric/equipment_count::numeric end as decimal(15,2)) as \"perEquipmentAmount\"," +        //设备均收
		"case when (equipment_count =0 or pay_amount=0) and (last_cycle_equipment_count = 0 or last_cycle_pay_amount=0) then 0 " +
		"when (last_cycle_equipment_count = 0 or last_cycle_pay_amount=0) and pay_amount > 0 then 1 " +
		//解决除0 问题
  		"when (equipment_group_count = 0 and last_cycle_equipment_group_count = 0) then 0 " +
  		"when (equipment_group_count = 0 and last_cycle_equipment_group_count <> 0) then -1 " +
  		"when (equipment_group_count <> 0 and last_cycle_equipment_group_count = 0) then 1 " +
  		//解决除0 问题
		"else cast( ((pay_amount::numeric/equipment_count::numeric) - (last_cycle_pay_amount::numeric/last_cycle_equipment_count::numeric))::numeric/(last_cycle_pay_amount::numeric/last_cycle_equipment_count::numeric)::numeric as decimal(15,2)) end  as \"perEquipmentAmountUp\","+
		
		
		
		
		"cast(case when equipment_count=0 then 0 else order_count::numeric/equipment_count::numeric end as decimal(15,2)) as \"perEquipmentOrder\"," +        //设备订单均数
		"case when (equipment_count =0 or order_count=0) and (last_cycle_equipment_count = 0 or last_cycle_order_count=0) then 0 " +
		"when (last_cycle_equipment_count = 0 or last_cycle_order_count=0) and order_count > 0 then 1 " +
		//解决除0 问题
  		"when (equipment_group_count = 0 and last_cycle_equipment_group_count = 0) then 0 " +
  		"when (equipment_group_count = 0 and last_cycle_equipment_group_count <> 0) then -1 " +
  		"when (equipment_group_count <> 0 and last_cycle_equipment_group_count = 0) then 1 " +
  		//解决除0 问题
		"else cast( ((order_count::numeric/equipment_count::numeric) - (last_cycle_order_count::numeric/last_cycle_equipment_count::numeric))::numeric/(last_cycle_order_count::numeric/last_cycle_equipment_count::numeric)::numeric as decimal(15,2)) end  as \"perEquipmentOrderUp\","+
		
		
		
		"cast(case when equipment_group_count=0 then 0 else total_start_counts::numeric/equipment_group_count::numeric end as decimal(15,2)) as \"perGroupStart\"," +        //场地启动均次
		"case when (last_cycle_equipment_group_count = 0 or last_cycle_total_start_counts=0) and (total_start_counts = 0 or equipment_group_count =0) then 0 when (last_cycle_equipment_group_count = 0 or last_cycle_total_start_counts=0) and total_start_counts > 0 then 1 " +
		//解决除0 问题
  		"when (equipment_group_count = 0 and last_cycle_equipment_group_count = 0) then 0 " +
  		"when (equipment_group_count = 0 and last_cycle_equipment_group_count <> 0) then -1 " +
  		"when (equipment_group_count <> 0 and last_cycle_equipment_group_count = 0) then 1 " +
  		//解决除0 问题
		"else cast( ((total_start_counts::numeric/equipment_group_count::numeric) - (last_cycle_total_start_counts::numeric/last_cycle_equipment_group_count::numeric))/(last_cycle_total_start_counts::numeric/last_cycle_equipment_group_count::numeric) as decimal(15,2)) end  as \"perGroupStartUp\","+
		
		
		"cast(case when equipment_group_count=0 then 0 else equipment_count::numeric/equipment_group_count::numeric end as decimal(15,2)) as \"perGroupEquipment\","+        //场地设备均数
		"case when (last_cycle_equipment_group_count =0 or last_cycle_equipment_count =0) and (equipment_count=0 or equipment_group_count=0) then 0 "+
		"when (last_cycle_equipment_group_count = 0 or last_cycle_equipment_count=0) and equipment_count > 0 then 1 " +
		//解决除0 问题
  		"when (equipment_group_count = 0 and last_cycle_equipment_group_count = 0) then 0 " +
  		"when (equipment_group_count = 0 and last_cycle_equipment_group_count <> 0) then -1 " +
  		"when (equipment_group_count <> 0 and last_cycle_equipment_group_count = 0) then 1 " +
  		//解决除0 问题
		"else cast( ((equipment_count::numeric/equipment_group_count::numeric) - (last_cycle_equipment_count::numeric/last_cycle_equipment_group_count::numeric))/(last_cycle_equipment_count::numeric/last_cycle_equipment_group_count::numeric) as decimal(15,2)) end as \"perGroupEquipmentUp\"" ;

	public static final String YEAR_EQUIPMENT_GROUP_COLUMN_ARRAY = 
		"merchant_id as \"merchantId\", " +
		
		"pay_equipment as \"payEquipments\"," +     //支付设备
		"cast(case when last_cycle_pay_equipment =0 and pay_equipment>0 then 1 when last_cycle_pay_equipment=0 and pay_equipment=0 then 0 else (pay_equipment-last_cycle_pay_equipment)::numeric/last_cycle_pay_equipment::numeric end as decimal(15,2)) as \"payEquipmentsUp\"," +
		
		"equipment_count as \"equipmentCounts\", " +        //设备总数
		"cast(case when last_cycle_equipment_count =0 and equipment_count>0 then 1 when last_cycle_equipment_count=0 and equipment_count=0 then 0 else (equipment_count-last_cycle_equipment_count)::numeric/last_cycle_equipment_count::numeric end as decimal(15,2)) as \"equipmentCountsUp\"," +
		
		"online_00_position_num as \"onlineEquipmentCounts\", " +       //在线设备数
		"cast(case when last_cycle_online_00_position_num =0 and online_00_position_num>0 then 1 when last_cycle_online_00_position_num=0 and online_00_position_num=0 then 0 else (online_00_position_num-last_cycle_online_00_position_num)::numeric/last_cycle_online_00_position_num::numeric end as decimal(15,2)) as \"onlineEquipmentCountsUp\"," +
		
		"total_start_counts as \"startCounts\", " +       //启动次数
		"cast(case when last_cycle_total_start_counts =0 and total_start_counts>0 then 1 when last_cycle_total_start_counts=0 and total_start_counts=0 then 0 else (total_start_counts-last_cycle_total_start_counts)::numeric/last_cycle_total_start_counts::numeric end as decimal(15,2)) as \"startCountsUp\"," +
		//每台设备平均启动次数
		"cast(start_per_equipment as decimal(15,2)) as \"perEquipmentStarts\"," +   //设备启动均次
		"cast(case when last_cycle_start_per_equipment =0 and start_per_equipment>0 then 1 when last_cycle_start_per_equipment=0 and start_per_equipment=0 then 0 else (start_per_equipment-last_cycle_start_per_equipment)::numeric/last_cycle_start_per_equipment::numeric end as decimal(15,2)) as \"perEquipmentStartsUp\"," +
		
		"cast(total_start_counts::numeric/365 as decimal(15,2)) as \"perEquipmentDayStarts\"," +   //设备日均启动均次 设备启动次数/账期日
		"cast(case when last_cycle_total_start_counts =0 and total_start_counts>0 then 1 when last_cycle_total_start_counts=0 and total_start_counts=0 then 0 else (total_start_counts-last_cycle_total_start_counts)::numeric/last_cycle_total_start_counts::numeric end as decimal(15,2)) as \"perEquipmentDayStartsUp\"," +
		
		"equipment_group_count as \"groupCounts\"," +        //场地总数
		"cast(case when last_cycle_equipment_group_count =0 and equipment_group_count>0 then 1 when last_cycle_equipment_group_count=0 and equipment_group_count=0 then 0 else (equipment_group_count-last_cycle_equipment_group_count)::numeric/last_cycle_equipment_group_count::numeric end as decimal(15,2)) as \"groupCountsUp\"," +
		
		"cast(case when equipment_group_count=0 then 0 else pay_amount::numeric/equipment_group_count::numeric end as decimal(15,2)) as \"perGroupAmount\"," +        //场地均收
		"case when (equipment_group_count =0 or pay_amount=0) and (last_cycle_equipment_group_count = 0 or last_cycle_pay_amount=0) then 0 " +
		"when (last_cycle_equipment_group_count = 0 or last_cycle_pay_amount=0) and pay_amount > 0 then 1 " +
		//解决除0 问题
		"when (equipment_group_count = 0 and last_cycle_equipment_group_count = 0) then 0 " +
		"when (equipment_group_count = 0 and last_cycle_equipment_group_count <> 0) then -1 " +
		"when (equipment_group_count <> 0 and last_cycle_equipment_group_count = 0) then 1 " +
		//解决除0 问题
		"else cast( ((pay_amount::numeric/equipment_group_count::numeric) - (last_cycle_pay_amount::numeric/last_cycle_equipment_group_count::numeric))::numeric/(last_cycle_pay_amount::numeric/last_cycle_equipment_group_count::numeric)::numeric as decimal(15,2)) end  as \"perGroupAmountUp\","+
		
		
		"cast(case when equipment_group_count=0 then 0 else order_count::numeric/equipment_group_count::numeric end as decimal(15,2)) as \"perGroupOrder\"," +        //场地订单均数
		"case when (equipment_group_count =0 or order_count=0) and (last_cycle_equipment_group_count = 0 or last_cycle_order_count=0) then 0 " +
		"when (last_cycle_equipment_group_count = 0 or last_cycle_order_count=0) and order_count > 0 then 1 " +
		//解决除0 问题
  		"when (equipment_group_count = 0 and last_cycle_equipment_group_count = 0) then 0 " +
  		"when (equipment_group_count = 0 and last_cycle_equipment_group_count <> 0) then -1 " +
  		"when (equipment_group_count <> 0 and last_cycle_equipment_group_count = 0) then 1 " +
  		//解决除0 问题
		"else cast( ((order_count::numeric/equipment_group_count::numeric) - (last_cycle_order_count::numeric/last_cycle_equipment_group_count::numeric))::numeric/(last_cycle_order_count::numeric/last_cycle_equipment_group_count::numeric)::numeric as decimal(15,2)) end  as \"perGroupOrderUp\","+
		
		
		"cast(case when equipment_count=0 then 0 else pay_amount::numeric/equipment_count::numeric end as decimal(15,2)) as \"perEquipmentAmount\"," +        //设备均收
		"case when (equipment_count =0 or pay_amount=0) and (last_cycle_equipment_count = 0 or last_cycle_pay_amount=0) then 0 " +
		"when (last_cycle_equipment_count = 0 or last_cycle_pay_amount=0) and pay_amount > 0 then 1 " +
		//解决除0 问题
  		"when (equipment_group_count = 0 and last_cycle_equipment_group_count = 0) then 0 " +
  		"when (equipment_group_count = 0 and last_cycle_equipment_group_count <> 0) then -1 " +
  		"when (equipment_group_count <> 0 and last_cycle_equipment_group_count = 0) then 1 " +
  		//解决除0 问题
		"else cast( ((pay_amount::numeric/equipment_count::numeric) - (last_cycle_pay_amount::numeric/last_cycle_equipment_count::numeric))::numeric/(last_cycle_pay_amount::numeric/last_cycle_equipment_count::numeric)::numeric as decimal(15,2)) end  as \"perEquipmentAmountUp\","+
		
		
		
		
		"cast(case when equipment_count=0 then 0 else order_count::numeric/equipment_count::numeric end as decimal(15,2)) as \"perEquipmentOrder\"," +        //设备订单均数
		"case when (equipment_count =0 or order_count=0) and (last_cycle_equipment_count = 0 or last_cycle_order_count=0) then 0 " +
		"when (last_cycle_equipment_count = 0 or last_cycle_order_count=0) and order_count > 0 then 1 " +
		//解决除0 问题
  		"when (equipment_group_count = 0 and last_cycle_equipment_group_count = 0) then 0 " +
  		"when (equipment_group_count = 0 and last_cycle_equipment_group_count <> 0) then -1 " +
  		"when (equipment_group_count <> 0 and last_cycle_equipment_group_count = 0) then 1 " +
  		//解决除0 问题
		"else cast( ((order_count::numeric/equipment_count::numeric) - (last_cycle_order_count::numeric/last_cycle_equipment_count::numeric))::numeric/(last_cycle_order_count::numeric/last_cycle_equipment_count::numeric)::numeric as decimal(15,2)) end  as \"perEquipmentOrderUp\","+
		
		
		
		"cast(case when equipment_group_count=0 then 0 else total_start_counts::numeric/equipment_group_count::numeric end as decimal(15,2)) as \"perGroupStart\"," +        //场地启动均次
		"case when (last_cycle_equipment_group_count = 0 or last_cycle_total_start_counts=0) and (total_start_counts = 0 or equipment_group_count =0) then 0 when (last_cycle_equipment_group_count = 0 or last_cycle_total_start_counts=0) and total_start_counts > 0 then 1 " +
		//解决除0 问题
  		"when (equipment_group_count = 0 and last_cycle_equipment_group_count = 0) then 0 " +
  		"when (equipment_group_count = 0 and last_cycle_equipment_group_count <> 0) then -1 " +
  		"when (equipment_group_count <> 0 and last_cycle_equipment_group_count = 0) then 1 " +
  		//解决除0 问题
		"else cast( ((total_start_counts::numeric/equipment_group_count::numeric) - (last_cycle_total_start_counts::numeric/last_cycle_equipment_group_count::numeric))/(last_cycle_total_start_counts::numeric/last_cycle_equipment_group_count::numeric) as decimal(15,2)) end  as \"perGroupStartUp\","+
		
		
		"cast(case when equipment_group_count=0 then 0 else equipment_count::numeric/equipment_group_count::numeric end as decimal(15,2)) as \"perGroupEquipment\","+        //场地设备均数
		"case when (last_cycle_equipment_group_count =0 or last_cycle_equipment_count =0) and (equipment_count=0 or equipment_group_count=0) then 0 "+
		"when (last_cycle_equipment_group_count = 0 or last_cycle_equipment_count=0) and equipment_count > 0 then 1 " +
		//解决除0 问题
  		"when (equipment_group_count = 0 and last_cycle_equipment_group_count = 0) then 0 " +
  		"when (equipment_group_count = 0 and last_cycle_equipment_group_count <> 0) then -1 " +
  		"when (equipment_group_count <> 0 and last_cycle_equipment_group_count = 0) then 1 " +
  		//解决除0 问题
		"else cast( ((equipment_count::numeric/equipment_group_count::numeric) - (last_cycle_equipment_count::numeric/last_cycle_equipment_group_count::numeric))/(last_cycle_equipment_count::numeric/last_cycle_equipment_group_count::numeric) as decimal(15,2)) end as \"perGroupEquipmentUp\"" ;

	
	public static final String DayGroupEquipmentSql = "merchant_id as \"merchantId\", " +
        "province_id as \"provinceId\", " +
        "city_id as \"cityId\", " +
        "district_id as \"districtId\", " +
        "equipment_group_id as \"groupId\", " +
        "equipment_group_name as \"groupName\", " +
        "equipment_group_address as \"address\", " +
        "pay_equipment as \"payEquipments\"," +     //支付设备
        "cast(case when pay_equipment_yesterday =0 and pay_equipment>0 then 1 " +
        "when pay_equipment_yesterday=0 and pay_equipment=0 then 0 " +
        "when pay_equipment_yesterday>0 and pay_equipment=0 then -1 " +
        "else (pay_equipment-pay_equipment_yesterday)::numeric/pay_equipment_yesterday::numeric end as decimal(15,2)) as \"payEquipmentsUp\"," +

        "equipment_count as \"equipmentCounts\", " +        //设备总数
        "cast(case when equipment_count_yesterday =0 and equipment_count>0 then 1 " +
        "when equipment_count_yesterday=0 and equipment_count=0 then 0 " +
        "when equipment_count_yesterday>0 and equipment_count=0 then -1 " +
        "else (equipment_count-equipment_count_yesterday)::numeric/equipment_count_yesterday::numeric end as decimal(15,2)) as \"equipmentCountsUp\"," +

        "online_00_position_num as \"onlineEquipmentCounts\", " +       //在线设备数
        "cast(case when online_00_position_num_yesterday =0 and online_00_position_num>0 then 1 " +
        "when online_00_position_num_yesterday=0 and online_00_position_num=0 then 0 " +
        "when online_00_position_num_yesterday>0 and online_00_position_num=0 then -1 " +
        "else (online_00_position_num-online_00_position_num_yesterday)::numeric/online_00_position_num_yesterday::numeric end as decimal(15,2)) as \"onlineEquipmentCountsUp\"," +

        "total_start_counts as \"startCounts\", " +       //启动次数
        "cast(case when total_start_counts_yesterday =0 and total_start_counts>0 then 1 " +
        "when total_start_counts_yesterday=0 and total_start_counts=0 then 0 " +
        "when total_start_counts_yesterday>0 and total_start_counts=0 then -1 " +
        "else (total_start_counts-total_start_counts_yesterday)::numeric/total_start_counts_yesterday::numeric end as decimal(15,2)) as \"startCountsUp\"," +
        //每台设备平均启动次数
        "cast(start_per_equipment as decimal(15,2)) as \"perEquipmentStarts\"," +   //设备启动均次
        "cast(case when start_per_equipment_yesterday =0 and start_per_equipment>0 then 1 " +
        "when start_per_equipment_yesterday=0 and start_per_equipment=0 then 0 " +
        "when start_per_equipment_yesterday>0 and start_per_equipment=0 then -1 " +
        "else (start_per_equipment-start_per_equipment_yesterday)::numeric/start_per_equipment_yesterday::numeric end as decimal(15,2)) as \"perEquipmentStartsUp\"," +

        "total_start_counts/1 as \"perEquipmentDayStarts\"," +   //设备日均启动均次 设备启动次数/账期日
        "cast(case when total_start_counts_yesterday =0 and total_start_counts>0 then 1 " +
        "when total_start_counts_yesterday=0 and total_start_counts=0 then 0 " +
        "when total_start_counts_yesterday>0 and total_start_counts=0 then -1 " +
        "else (total_start_counts-total_start_counts_yesterday)::numeric/total_start_counts_yesterday::numeric end as decimal(15,2)) as \"perEquipmentDayStartsUp\"," +

        "equipment_group_count as \"groupCounts\"," +        //场地总数
        "cast(case when equipment_group_count_yesterday =0 and equipment_group_count>0 then 1 " +
        "when equipment_group_count_yesterday=0 and equipment_group_count=0 then 0 " +
        "when equipment_group_count_yesterday>0 and equipment_group_count=0 then -1 " +
        "else (equipment_group_count-equipment_group_count_yesterday)::numeric/equipment_group_count_yesterday::numeric end as decimal(15,2)) as \"groupCountsUp\"," +

        "cast(case when equipment_group_count=0 then 0 else pay_amount::numeric/equipment_group_count::numeric end as decimal(15,2)) as \"perGroupAmount\"," +        //场地均收
        "case when (equipment_group_count =0 or pay_amount=0) and (equipment_group_count_yesterday = 0 or pay_amount_yesterday=0) then 0 " +
        "when (equipment_group_count =0 or pay_amount=0) and pay_amount_yesterday > 0 then -1 " +
        "when (equipment_group_count_yesterday = 0 or pay_amount_yesterday=0) and pay_amount > 0 then 1 " +
        //解决除0 问题
  		"when (equipment_group_count = 0 and equipment_group_count_yesterday = 0) then 0 " +
  		"when (equipment_group_count = 0 and equipment_group_count_yesterday <> 0) then -1 " +
  		"when (equipment_group_count <> 0 and equipment_group_count_yesterday = 0) then 1 " +
  		//解决除0 问题
        "else cast( ((pay_amount::numeric/equipment_group_count::numeric) - (pay_amount_yesterday::numeric/equipment_group_count_yesterday::numeric))::numeric/(pay_amount_yesterday::numeric/equipment_group_count_yesterday::numeric)::numeric as decimal(15,2)) end  as \"perGroupAmountUp\","+

        "cast(case when equipment_count=0 then 0 else pay_amount::numeric/equipment_count::numeric end as decimal(15,2)) as \"perEquipmentAmount\"," +        //设备均收
        "case when (equipment_count =0 or pay_amount=0) and (equipment_count_yesterday = 0 or pay_amount_yesterday=0) then 0 " +
        "when (equipment_count =0 or pay_amount=0) and pay_amount_yesterday > 0 then -1 "+
        "when (equipment_count_yesterday = 0 or pay_amount_yesterday=0) and pay_amount > 0 then 1 " +
        
        //解决除0问题
		"when (equipment_count = 0 and equipment_count_yesterday=0) then 0 " +
		"when (equipment_count = 0 and equipment_count_yesterday<>0) then -1 " +
		"when (equipment_count <> 0 and equipment_count_yesterday=0) then 1 " +
		//解决除0问题
        
        "else cast( ((pay_amount::numeric/equipment_count::numeric) - (pay_amount_yesterday::numeric/equipment_count_yesterday::numeric))::numeric/(pay_amount_yesterday::numeric/equipment_count_yesterday::numeric)::numeric as decimal(15,2)) end  as \"perEquipmentAmountUp\","+

        "cast(case when equipment_group_count=0 then 0 else order_count::numeric/equipment_group_count::numeric end as decimal(15,2)) as \"perGroupOrder\"," +        //场地订单均数
        "case when (equipment_group_count =0 or order_count=0) and (equipment_group_count_yesterday = 0 or order_count_yesterday=0) then 0 " +
        "when (equipment_group_count =0 or order_count=0) and order_count_yesterday>0 then -1 " +
        "when (equipment_group_count_yesterday = 0 or order_count_yesterday=0) and order_count > 0 then 1 " +
        //解决除0问题
		"when (equipment_count = 0 and equipment_count_yesterday=0) then 0 " +
		"when (equipment_count = 0 and equipment_count_yesterday<>0) then -1 " +
		"when (equipment_count <> 0 and equipment_count_yesterday=0) then 1 " +
		//解决除0问题
        "else cast( ((order_count::numeric/equipment_group_count::numeric) - (order_count_yesterday::numeric/equipment_group_count_yesterday::numeric))::numeric/(order_count_yesterday::numeric/equipment_group_count_yesterday::numeric)::numeric as decimal(15,2)) end  as \"perGroupOrderUp\","+


        "cast(case when equipment_count=0 then 0 else order_count::numeric/equipment_count::numeric end as decimal(15,2)) as \"perEquipmentOrder\"," +        //设备订单均数
        "case when (equipment_count =0 or order_count=0) and (equipment_count_yesterday = 0 or order_count_yesterday=0) then 0 " +
        "when (equipment_count =0 or order_count=0) and order_count_yesterday >0 then -1 " +
        "when (equipment_count_yesterday = 0 or order_count_yesterday=0) and order_count > 0 then 1 " +
        //解决除0问题
		"when (equipment_count = 0 and equipment_count_yesterday=0) then 0 " +
		"when (equipment_count = 0 and equipment_count_yesterday<>0) then -1 " +
		"when (equipment_count <> 0 and equipment_count_yesterday=0) then 1 " +
		//解决除0问题
        "else cast( ((order_count::numeric/equipment_count::numeric) - (order_count_yesterday::numeric/equipment_count_yesterday::numeric))::numeric/(order_count_yesterday::numeric/equipment_count_yesterday::numeric)::numeric as decimal(15,2)) end  as \"perEquipmentOrderUp\", "+



        "cast(case when equipment_group_count=0 then 0 else total_start_counts::numeric/equipment_group_count::numeric end as decimal(15,2)) as \"perGroupStart\"," +        //场地启动均次
        "case when (equipment_group_count_yesterday = 0 or total_start_counts_yesterday=0) and (total_start_counts = 0 or equipment_group_count =0) then 0 " +
        "when total_start_counts_yesterday >0 and (total_start_counts = 0 or equipment_group_count =0) then -1 " +
        "when (equipment_group_count_yesterday = 0 or total_start_counts_yesterday=0) and total_start_counts > 0 then 1 " +
        //解决除0问题
		"when (equipment_count = 0 and equipment_count_yesterday=0) then 0 " +
		"when (equipment_count = 0 and equipment_count_yesterday<>0) then -1 " +
		"when (equipment_count <> 0 and equipment_count_yesterday=0) then 1 " +
		//解决除0问题
        "else cast( ((total_start_counts::numeric/equipment_group_count::numeric) - (total_start_counts_yesterday::numeric/equipment_group_count_yesterday::numeric))/(total_start_counts_yesterday::numeric/equipment_group_count_yesterday::numeric) as decimal(15,2)) end  as \"perGroupStartUp\", "+


        "cast(case when equipment_group_count=0 then 0 else equipment_count::numeric/equipment_group_count::numeric end as decimal(15,2)) as \"perGroupEquipment\","+        //场地设备均数
        "case when (equipment_group_count_yesterday =0 or equipment_count_yesterday =0) and (equipment_count=0 or equipment_group_count=0) then 0 "+
        "when equipment_count_yesterday >0 and (equipment_count=0 or equipment_group_count=0) then -1 "+
        "when (equipment_group_count_yesterday = 0 or equipment_count_yesterday=0) and equipment_count > 0 then 1 " +
        //解决除0问题
		"when (equipment_count = 0 and equipment_count_yesterday=0) then 0 " +
		"when (equipment_count = 0 and equipment_count_yesterday<>0) then -1 " +
		"when (equipment_count <> 0 and equipment_count_yesterday=0) then 1 " +
		//解决除0问题
        "else cast( ((equipment_count::numeric/equipment_group_count::numeric) - (equipment_count_yesterday::numeric/equipment_group_count_yesterday::numeric))/(equipment_count_yesterday::numeric/equipment_group_count_yesterday::numeric) as decimal(15,2)) end as \"perGroupEquipmentUp\"";

	public static final String GroupEquipmentSql = 
		"merchant_id as \"merchantId\", " +
        "province_id as \"provinceId\", " +
        "city_id as \"cityId\", " +
        "district_id as \"districtId\", " +
        "equipment_group_id as \"groupId\", " +
        "equipment_group_name as \"groupName\", " +
        "equipment_group_address as \"address\", " +

        "pay_equipment as \"payEquipments\"," +     //支付设备
        "cast(case when last_cycle_pay_equipment =0 and pay_equipment>0 then 1 " +
        "when last_cycle_pay_equipment=0 and pay_equipment=0 then 0 " +
        "when last_cycle_pay_equipment>0 and pay_equipment=0 then -1 " +
        "else (pay_equipment-last_cycle_pay_equipment)::numeric/last_cycle_pay_equipment::numeric end as decimal(15,2)) as \"payEquipmentsUp\"," +

        "equipment_count as \"equipmentCounts\", " +        //设备总数
        "cast(case when last_cycle_equipment_count =0 and equipment_count>0 then 1 " +
        "when last_cycle_equipment_count=0 and equipment_count=0 then 0 " +
        "when last_cycle_equipment_count>0 and equipment_count=0 then -1 " +
        "else (equipment_count-last_cycle_equipment_count)::numeric/last_cycle_equipment_count::numeric end as decimal(15,2)) as \"equipmentCountsUp\"," +

        "online_00_position_num as \"onlineEquipmentCounts\", " +       //在线设备数
        "cast(case when last_cycle_online_00_position_num =0 and online_00_position_num>0 then 1 " +
        "when last_cycle_online_00_position_num=0 and online_00_position_num=0 then 0 " +
        "when last_cycle_online_00_position_num>0 and online_00_position_num=0 then -1 " +
        "else (online_00_position_num-last_cycle_online_00_position_num)::numeric/last_cycle_online_00_position_num::numeric end as decimal(15,2)) as \"onlineEquipmentCountsUp\"," +

        "total_start_counts as \"startCounts\", " +       //启动次数
        "cast(case when last_cycle_total_start_counts =0 and total_start_counts>0 then 1 " +
        "when last_cycle_total_start_counts=0 and total_start_counts=0 then 0 " +
        "when last_cycle_total_start_counts>0 and total_start_counts=0 then -1 " +
        "else (total_start_counts-last_cycle_total_start_counts)::numeric/last_cycle_total_start_counts::numeric end as decimal(15,2)) as \"startCountsUp\"," +
        //每台设备平均启动次数
        "cast(start_per_equipment as decimal(15,2)) as \"perEquipmentStarts\"," +   //设备启动均次
        "cast(case when last_cycle_start_per_equipment =0 and start_per_equipment>0 then 1 " +
        "when last_cycle_start_per_equipment=0 and start_per_equipment=0 then 0 " +
        "when last_cycle_start_per_equipment>0 and start_per_equipment=0 then -1 " +
        "else (start_per_equipment-last_cycle_start_per_equipment)::numeric/last_cycle_start_per_equipment::numeric end as decimal(15,2)) as \"perEquipmentStartsUp\"," +

        "cast(total_start_counts::numeric/7 as decimal(15,2)) as \"perEquipmentDayStarts\"," +   //设备日均启动均次 设备启动次数/账期日
        "cast(case when last_cycle_total_start_counts =0 and total_start_counts>0 then 1 " +
        "when last_cycle_total_start_counts=0 and total_start_counts=0 then 0 " +
        "when last_cycle_total_start_counts>0 and total_start_counts=0 then -1 " +
        "else (total_start_counts-last_cycle_total_start_counts)::numeric/last_cycle_total_start_counts::numeric end as decimal(15,2)) as \"perEquipmentDayStartsUp\"," +

        "equipment_group_count as \"groupCounts\"," +        //场地总数
        "cast(case when last_cycle_equipment_group_count =0 and equipment_group_count>0 then 1 " +
        "when last_cycle_equipment_group_count=0 and equipment_group_count=0 then 0 " +
        "when last_cycle_equipment_group_count>0 and equipment_group_count=0 then -1 " +
        "else (equipment_group_count-last_cycle_equipment_group_count)::numeric/last_cycle_equipment_group_count::numeric end as decimal(15,2)) as \"groupCountsUp\"," +

        "cast(case when equipment_group_count=0 then 0 else pay_amount::numeric/equipment_group_count::numeric end as decimal(15,2)) as \"perGroupAmount\"," +        //场地均收
        "case when (equipment_group_count =0 or pay_amount=0) and (last_cycle_equipment_group_count = 0 or last_cycle_pay_amount=0) then 0 " +
        "when (equipment_group_count =0 or pay_amount=0) and last_cycle_pay_amount>0 then -1 " +
        "when (last_cycle_equipment_group_count = 0 or last_cycle_pay_amount=0) and pay_amount > 0 then 1 " +
        //解决除0 问题
  		"when (equipment_group_count = 0 and last_cycle_equipment_group_count = 0) then 0 " +
  		"when (equipment_group_count = 0 and last_cycle_equipment_group_count <> 0) then -1 " +
  		"when (equipment_group_count <> 0 and last_cycle_equipment_group_count = 0) then 1 " +
  		//解决除0 问题
        "else cast( ((pay_amount::numeric/equipment_group_count::numeric) - (last_cycle_pay_amount::numeric/last_cycle_equipment_group_count::numeric))::numeric/(last_cycle_pay_amount::numeric/last_cycle_equipment_group_count::numeric)::numeric as decimal(15,2)) end  as \"perGroupAmountUp\","+


        "cast(case when equipment_count=0 then 0 else pay_amount::numeric/equipment_count::numeric end as decimal(15,2)) as \"perEquipmentAmount\"," +        //设备均收
        "case when (equipment_count =0 or pay_amount=0) and (last_cycle_equipment_count = 0 or last_cycle_pay_amount=0) then 0 " +
        "when (equipment_count =0 or pay_amount=0) and last_cycle_pay_amount > 0 then -1 " +
        "when (last_cycle_equipment_count = 0 or last_cycle_pay_amount=0) and pay_amount > 0 then 1 " +
        //解决除0 问题
		"when (equipment_group_count = 0 and last_cycle_equipment_group_count = 0) then 0 " +
		"when (equipment_group_count = 0 and last_cycle_equipment_group_count <> 0) then -1 " +
		"when (equipment_group_count <> 0 and last_cycle_equipment_group_count = 0) then 1 " +
		//解决除0 问题
        "else cast( ((pay_amount::numeric/equipment_count::numeric) - (last_cycle_pay_amount::numeric/last_cycle_equipment_count::numeric))::numeric/(last_cycle_pay_amount::numeric/last_cycle_equipment_count::numeric)::numeric as decimal(15,2)) end  as \"perEquipmentAmountUp\","+




        "cast(case when equipment_count=0 then 0 else order_count::numeric/equipment_count::numeric end as decimal(15,2)) as \"perEquipmentOrder\"," +        //设备订单均数
        "case when (equipment_count =0 or order_count=0) and (last_cycle_equipment_count = 0 or last_cycle_order_count=0) then 0 " +
        "when (equipment_count =0 or order_count=0) and last_cycle_order_count>0 then -1 " +
        "when (last_cycle_equipment_count = 0 or last_cycle_order_count=0) and order_count > 0 then 1 " +
        //解决除0 问题
		"when (equipment_group_count = 0 and last_cycle_equipment_group_count = 0) then 0 " +
		"when (equipment_group_count = 0 and last_cycle_equipment_group_count <> 0) then -1 " +
		"when (equipment_group_count <> 0 and last_cycle_equipment_group_count = 0) then 1 " +
		//解决除0 问题
        "else cast( ((order_count::numeric/equipment_count::numeric) - (last_cycle_order_count::numeric/last_cycle_equipment_count::numeric))::numeric/(last_cycle_order_count::numeric/last_cycle_equipment_count::numeric)::numeric as decimal(15,2)) end  as \"perEquipmentOrderUp\","+



        "cast(case when equipment_group_count=0 then 0 else order_count::numeric/equipment_group_count::numeric end as decimal(15,2)) as \"perGroupOrder\"," +        //场地订单均数
        "case when (equipment_group_count =0 or order_count=0) and (last_cycle_equipment_group_count = 0 or last_cycle_order_count=0) then 0 " +
        "when (equipment_group_count =0 or order_count=0) and last_cycle_order_count>0 then -1 " +
        "when (last_cycle_equipment_group_count = 0 or last_cycle_order_count=0) and order_count > 0 then 1 " +
        //解决除0 问题
		"when (equipment_group_count = 0 and last_cycle_equipment_group_count = 0) then 0 " +
		"when (equipment_group_count = 0 and last_cycle_equipment_group_count <> 0) then -1 " +
		"when (equipment_group_count <> 0 and last_cycle_equipment_group_count = 0) then 1 " +
		//解决除0 问题
        "else cast( ((order_count::numeric/equipment_group_count::numeric) - (last_cycle_order_count::numeric/last_cycle_equipment_group_count::numeric))::numeric/(last_cycle_order_count::numeric/last_cycle_equipment_group_count::numeric)::numeric as decimal(15,2)) end  as \"perGroupOrderUp\","+


        "cast(case when equipment_group_count=0 then 0 else total_start_counts::numeric/equipment_group_count::numeric end as decimal(15,2)) as \"perGroupStart\"," +        //场地启动均次
        "case when (last_cycle_equipment_group_count = 0 or last_cycle_total_start_counts=0) and (total_start_counts = 0 or equipment_group_count =0) then 0 " +
        "when last_cycle_total_start_counts>0 and (total_start_counts = 0 or equipment_group_count =0) then -1 " +
        "when (last_cycle_equipment_group_count = 0 or last_cycle_total_start_counts=0) and total_start_counts > 0 then 1 " +
        "else cast( ((total_start_counts::numeric/equipment_group_count::numeric) - (last_cycle_total_start_counts::numeric/last_cycle_equipment_group_count::numeric))/(last_cycle_total_start_counts::numeric/last_cycle_equipment_group_count::numeric) as decimal(15,2)) end  as \"perGroupStartUp\","+


        "cast(case when equipment_group_count=0 then 0 else equipment_count::numeric/equipment_group_count::numeric end as decimal(15,2)) as \"perGroupEquipment\","+        //场地设备均数
        "case when (last_cycle_equipment_group_count =0 or last_cycle_equipment_count =0) and (equipment_count=0 or equipment_group_count=0) then 0 "+
        "when last_cycle_equipment_count>0 and (equipment_count=0 or equipment_group_count=0) then -1 "+
        "when (last_cycle_equipment_group_count = 0 or last_cycle_equipment_count=0) and equipment_count > 0 then 1 " +
        //解决除0 问题
		"when (equipment_group_count = 0 and last_cycle_equipment_group_count = 0) then 0 " +
		"when (equipment_group_count = 0 and last_cycle_equipment_group_count <> 0) then -1 " +
		"when (equipment_group_count <> 0 and last_cycle_equipment_group_count = 0) then 1 " +
		//解决除0 问题
        "else cast( ((equipment_count::numeric/equipment_group_count::numeric) - (last_cycle_equipment_count::numeric/last_cycle_equipment_group_count::numeric))/(last_cycle_equipment_count::numeric/last_cycle_equipment_group_count::numeric) as decimal(15,2)) end as \"perGroupEquipmentUp\""
        ;

	public static final String DayGroupListSql =
        "merchant_id as \"merchantId\", " +
        "province_id as \"provinceId\", " +
        "city_id as \"cityId\", " +
        "district_id as \"districtId\", " +
        "lyy_equipment_type_id as \"equipmentTypeId\", " +
        "equipment_group_id as \"groupId\", " +
        "equipment_type_name as \"equipmentTypeName\", " +
        "coalesce(equipment_count::numeric,0) as \"equipmentCounts\", " +
        "cast(case when coalesce(equipment_count_yesterday::numeric,0) =0 and coalesce(equipment_count::numeric,0) =0 then 0  " +
        "when coalesce(equipment_count_yesterday::numeric,0) =0 and coalesce(equipment_count::numeric,0) > 0 then 1"+
        "when (equipment_count_yesterday=0 then 1 " +        
        "else (coalesce(equipment_count::numeric,0)-coalesce(equipment_count_yesterday::numeric,0))::numeric/coalesce(equipment_count_yesterday::numeric,0)::numeric end as decimal(15,2)) as \"equipmentCountsUp\" " +

        "";

    public static final String WeekGroupListSql =
        "merchant_id as \"merchantId\", " +
        "province_id as \"provinceId\", " +
        "city_id as \"cityId\", " +
        "district_id as \"districtId\", " +
        "lyy_equipment_type_id as \"equipmentTypeId\", " +
        "equipment_group_id as \"groupId\", " +
        "equipment_type_name as \"equipmentTypeName\", " +
        "coalesce(equipment_count::numeric,0) as \"equipmentCounts\", " +
        "cast(case when coalesce(last_cycle_equipment_count::numeric,0) =0 and coalesce(equipment_count::numeric,0) =0 then 0  " +
        "when coalesce(last_cycle_equipment_count::numeric,0) =0 and coalesce(equipment_count::numeric,0) > 0 then 1"+
        "when last_cycle_equipment_count=0 then 1 " +
        "else (coalesce(equipment_count::numeric,0)-coalesce(last_cycle_equipment_count::numeric,0))::numeric/coalesce(last_cycle_equipment_count::numeric,0)::numeric end as decimal(15,2)) as \"equipmentCountsUp\" " +

        "";
    
    public static final String DAY_SUB_EQUIPMENT_GROUP_LIST_COLUMN_ARRAY = 
    		"merchant_id as \"provinceId\","
    		+ "    city_id as \"cityId\","
    		+ "    district_id as \"districtId\","
    		+ "    equipment_group_id as \"groupId\","
    		+ "    equipment_group_name as \"groupName\","
    		+ "    equipment_group_address as \"address\","
    		+"     equipment_count as \"equipmentCounts\","
    		
    		+ "    order_counts as \"payCount\","
    		+ "    case when order_count=0 then 0 else cast(pay_amount/order_count as decimal(15,2)) end as \"perOrderAmount\","
    		
    		
    		//笔单价增长率
    		+ "    case "
    		+ "        when (order_count=0 and order_counts_yesterday=0) then 0"
    		+ "        when (order_count=0 and order_counts_yesterday<>0) then -1"
    		+ "        when (order_count<>0 and order_counts_yesterday=0) then 1"
    		+ "        when (pay_amount_yesterday = 0) then 1"
    		+ "        else cast((pay_amount/order_count - pay_amount_yesterday/order_counts_yesterday)/(pay_amount_yesterday/order_counts_yesterday) as decimal(15,2)) "
    		+ "    end as \"perOrderAmountUp\"," 
    		
    		// 订单增长率
    		+ "    case "
    		+ "        when (order_counts_yesterday=0 and order_counts=0) then 0"
    		+ "        when (order_counts_yesterday=0 and order_counts<>0) then 1"
    		+ "        when (order_counts_yesterday<>0 and order_counts=0) then -1"
    		+ "        when order_counts_yesterday=order_counts then 0"
    		+ "        else cast((order_counts-order_counts_yesterday)/order_counts_yesterday as decimal(15,2)) "
    		+ "    end as \"payCountUp\"," 
    		
    		+ "    total_start_counts as \"startCounts\","
    		// 设备启动增长率
    		+ "    case "
    		+ "        when (total_start_counts=0 and total_start_counts_yesterday=0) then 0"
    		+ "        when (total_start_counts=0 and total_start_counts_yesterday<>0) then -1"
    		+ "        when (total_start_counts<>0 and total_start_counts_yesterday=0) then 1"
    		+ "        else cast((total_start_counts-total_start_counts_yesterday)/total_start_counts_yesterday as decimal(15,2)) "
    		+ "    end as \"startCountsUp\"" ;
    
    public static final String COMMON_SUB_EQUIPMENT_GROUP_LIST_COLUMN_ARRAY =
  		  "	merchant_id as \"provinceId\","
  		+ "    city_id as \"cityId\","
  		+ "    district_id as \"districtId\","
  		+ "    equipment_group_id as \"groupId\","
  		+ "    equipment_group_name as \"groupName\","
  		+ "    equipment_group_address as \"address\","
  		+"     equipment_count as \"equipmentCounts\","
  		+ "    order_counts as \"payCount\","
  		
  		+ "    case when order_count=0 then 0 else cast(pay_amount/order_count as decimal(15,2)) end as \"perOrderAmount\","
  		
  		//笔单价增长率
  		+ "    case "
  		+ "        when (order_count=0 and last_cycle_order_counts=0) then 0"
  		+ "        when (order_count=0 and last_cycle_order_counts<>0) then -1"
  		+ "        when (order_count<>0 and last_cycle_order_counts=0) then 1"
  		+ "        when (last_cycle_pay_amount = 0) then 1"
  		+ "        else cast((pay_amount/order_count - last_cycle_pay_amount/last_cycle_order_counts)/(last_cycle_pay_amount/last_cycle_order_counts) as decimal(15,2)) "
  		+ "    end as \"perOrderAmountUp\","

  		// 订单增长率
  		+ "    case "
  		+ "        when (last_cycle_order_counts=0 and order_counts=0) then 0"
  		+ "        when (last_cycle_order_counts=0 and order_counts<>0) then 1"
  		+ "        when (last_cycle_order_counts<>0 and order_counts=0) then -1"
  		+ "        when last_cycle_order_counts=order_counts then 0"
  		+ "        else cast((order_counts-last_cycle_order_counts)/last_cycle_order_counts as decimal(15,2)) "
  		+ "    end as \"payCountUp\","

  		+ "    total_start_counts as \"startCounts\","
  		//设备启动增长率
  		+ "    case "
  		+ "        when (total_start_counts=0 and last_cycle_total_start_counts=0) then 0"
  		+ "        when (total_start_counts=0 and last_cycle_total_start_counts<>0) then -1"
  		+ "        when (total_start_counts<>0 and last_cycle_total_start_counts=0) then 1"
  		+ "        else cast((total_start_counts-last_cycle_total_start_counts)/last_cycle_total_start_counts as decimal(15,2)) "
  		+ "    end as \"startCountsUp\"" ;
    
    public static final String COMMON_AVG_SUB_EQUIPMENT_GROUP_LIST_COLUMN_ARRAY =
    		"case "
			+ "    when count(equipment_group_id)=0 then 0 "
			+ "    else cast(sum(order_count)/count(equipment_group_id) as decimal(15,2)) "
			+ "end as \"sumPerOrderCounts\","
			
			+ "case "
			+ "    when sum(order_count)=0 then 0 "
			+ "    else cast(sum(pay_amount)/sum(order_count) as decimal(15,2)) "
			+ "end as \"sumPerOrderAmount\","
			
			+ "case "
			+ "    when count(equipment_group_id)=0 then 0 "
			+ "    else cast(sum(pay_amount)/count(equipment_group_id) as decimal(15,2)) "
			+ "end as \"sumPerPayAmount\","

			+ "case "
			+ "    when count(equipment_group_id)=0 then 0 "
			+ "    else cast(sum(total_start_counts)/count(equipment_group_id) as decimal(15,2)) "
			+ "end as \"sumPerGroupStartCounts\"" ;
    
    public static final String DROP_DOWN_PROVINCE_COLUMN_ARRAY = 
    	"distinct province_id , province_name";
    
    public static final String DROP_DOWN_CITY_COLUMN_ARRAY = 
    	"distinct city_id , city_name";
    
    public static final String DROP_DOWN_DISTRICT_COLUMN_ARRAY = 
    	"distinct district_id , district_name ";
    
    public static final String DROP_DOWN_EQUIPMENT_GROUP_COLUMN_ARRAY = 
    	"distinct equipment_group_id , equipment_group_name" ;
    
    public static final String DROP_DOWN_EQUIPMENT_TYPE_COLUMN_ARRAY = 
    	"distinct equipment_type_id as \"equipmentTypeId\" , equipment_type_name as \"equipmentTypeName\"" ;

}
