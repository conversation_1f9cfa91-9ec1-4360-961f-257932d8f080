package cn.lyy.merchant.service.onedata;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import java.util.Set;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class ParamDto {
    public String code;
    public String databaseType ;
    public String tableName;
    public String columnArray;
    public String groupByColumnArray ;
    public PageParams pageParams = new PageParams();
    public QueryParams queryParams=new QueryParams();
    public RangeParams rangeParams = new RangeParams();
    public JSONObject orderParams = new JSONObject();
    public JSONObject likeParams = new JSONObject();
    
    //:切分库类型
    public void setCode(String code) {
    	String[] array = code.split(":") ;
    	this.code = array[0] ;
    	if(array.length > 1) {
    		databaseType = array[1] ;
    	} else {
    		databaseType = "selectdb" ;//pg/selectdb
    	}
    }
    
    public void resetComboType() {
    	Set<String> equipmentGroupIds = queryParams.getEquipment_group_id() ;
    	Set<String> lyyEquipmentTypeId = queryParams.getLyy_equipment_type_id() ;
    	if(equipmentGroupIds !=null && equipmentGroupIds.size()>0) {
    		if(lyyEquipmentTypeId == null || lyyEquipmentTypeId.size() == 0) {
    			this.queryParams.setCombo_type("商家+省份+城市+区域+场地维度");
    		} else {
    			this.queryParams.setCombo_type("商家+省份+城市+区域+场地+设备类型维度");
    		}
    	} 
    }

    @Data
    public class PageParams{
        public String pageRow = "100";
        public String pageNum = "0";
    }
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Data
	public class QueryParams{
        public String day;
        public String merchant_id;
        public String distributor_id;
        public String combo_type;
        public String year_month;
        public String month_week_num;
        public String benefit_kind;
        public String year_str;
        public Set<String> city_id;
        public Set<String> province_id;
        public Set<String> district_id;
        public Set<String> equipment_group_id;
        public Set<String> lyy_equipment_type_id;
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Data
    public class RangeParams{
        public Range day = new Range();
        public Range year_month = new Range();
        public Range year_str = new Range();
    }
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Data
    public class Range{
        public String start;
        public String end;
    }

    @Data
    public class OrderFile{
        public String direction;

        public OrderFile(){}
        public OrderFile(String direction){
            this.direction = direction;
        }
    }

    public OrderFile buildOrderFile(String direction){
        return new OrderFile(direction);
    }

}
