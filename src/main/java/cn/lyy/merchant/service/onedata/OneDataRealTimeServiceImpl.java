package cn.lyy.merchant.service.onedata;

import cn.lyy.merchant.controller.onedata.dto.DayParamDto;
import cn.lyy.merchant.controller.onedata.util.TimeUtil;
import cn.lyy.merchant.service.onedata.columns.ColumnArrayMap;
import cn.lyy.merchant.service.onedata.columns.ColumnArrayMapKey;
import cn.lyy.merchant.service.onedata.mobile.MemberDataService;
import cn.lyy.merchant.service.onedata.mobile.OrderDataService;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.util.MultiValueMap;

import java.text.DecimalFormat;
import java.util.*;

@Service
@Slf4j
public class OneDataRealTimeServiceImpl implements OneDataRealTimeService{
    @Autowired
    private OneDataServiceFeign oneDataServiceFeign;

    @Autowired
    private MemberDataService memberDataService ;
    
    @Autowired
    private OrderDataService orderDataService ;

    @Value("${oneData.sk}")
    private String sk;
    @Value("${oneData.ak}")
    private String ak;

    @Value("${oneData.realTimeOrderAnalyseCode}")
    private String realTimeOrderAnalyseCode;

    @Value("${oneData.provinceInfoCode}")
    private String provinceInfoCode;

    @Override
    public JSONObject getOrderData(DayParamDto dayParamDto) {
        //设置查昨天数据的维度和天数
        MultiValueMap<String, String> headers = new HttpHeaders();
        headers.add("sk",sk);
        headers.add("ak",ak);

        RealTimeParamDto params = new RealTimeParamDto();
        params.setCode(realTimeOrderAnalyseCode);
        params.setGroupByColumnArray("ad_org_id");
        
        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.RT_ORDER_DATA_COLUMN_ARRAY) ;
        params.setColumnArray(columnArray);

        params.getQueryParams().setAd_org_id(dayParamDto.getMerchantId()+"");
        params.getRangeParams().getCreated().setStart(dayParamDto.getDay()+" 00:00:00");
        params.getRangeParams().getCreated().setEnd(dayParamDto.getDay()+" 23:59:59");

        params.getQueryParams().setLyy_equipment_group_id(dayParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(dayParamDto.getGroupId()+"");}});
        params.getQueryParams().setLyy_equipment_type_id(dayParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(dayParamDto.getEquipmentTypeId()+"");}});

        boolean filter = false;
        Set<String> groupSet = new HashSet<>();

        if((dayParamDto.getProvinceId() != null && dayParamDto.getProvinceId()>0) || (dayParamDto.getCityId() != null && dayParamDto.getCityId()>0) || (dayParamDto.getAreaId() != null && dayParamDto.getAreaId()>0)){
            if((dayParamDto.getGroupId() == null || dayParamDto.getGroupId() <=0) && (dayParamDto.getGroupIds() == null || dayParamDto.getGroupIds().isEmpty())){
                filter = true;
                //有选择省市区，先通过省市区筛选出场地
                groupSet = getGroupId(dayParamDto,headers);
            }

        }
        if(dayParamDto.getGroupIds() != null && dayParamDto.getGroupIds().size()>0){
            //证明是子账户
            if(filter){
                //证明没有选择场地，而是通过省市区过来的场地
                if(groupSet.size() > 0){
                    Iterator<String> iterator = groupSet.iterator();
                    while (iterator.hasNext()){
                        String gid = iterator.next();
                        if(!dayParamDto.getGroupIds().contains(gid)){
                            iterator.remove();
                        }

                    }
                    if(groupSet.size()>0){
                        params.getQueryParams().setLyy_equipment_group_id(groupSet);
                    }else{
                        return null;
                    }

                }else{
                    //证明筛选了省份城市等，但是没有一个场地
                    return null;
                }

            }else{
                //证明是子账户且没有省份城市等筛选条件
                if(dayParamDto.getGroupId() == null){
                    //params.getQueryParams().setLyy_equipment_group_id(new HashSet<String>(){{add(((TreeSet)(dayParamDto.getGroupIds())).first()+"");}});
                	params.getQueryParams().setLyy_equipment_group_id(dayParamDto.getGroupIds());
                	params.getOrderParams().put("ad_org_id",params.buildOrderFile("asc"));
                    //params.getOrderParams().put("lyy_equipment_group_id",params.buildOrderFile("asc"));
                }else{
                    //证明前端有选择一个场地，如果在权限范围内则允许。否则退出
                    if(dayParamDto.getGroupIds().contains(dayParamDto.getGroupId())){
                        params.getQueryParams().setLyy_equipment_group_id(new HashSet<String>(){{add(dayParamDto.getGroupId()+"");}});
                    }else{
                        //params.getQueryParams().setLyy_equipment_group_id(new HashSet<String>(){{add("-11111");}});
                        return null;
                    }
                }

            }


        }else{
            //主账户且选择了省份城市等
            if(filter){
                if(groupSet.size() > 0){
                    params.getQueryParams().setLyy_equipment_group_id(groupSet);
                }else{
                    //证明筛选了省份城市等，但是没有一个场地
                    return null;
                }
            }
        }

        try{
            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
            JSONObject result = oneDataServiceFeign.getRealTimeData(params,headers);
            log.info("{}",result.toJSONString());
            //实时数据获取成功后处理环比
            if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
                JSONObject current = new JSONObject();
                if(result.containsKey("info") && result.getJSONObject("info") !=null){
                    if(result.getJSONObject("info").getJSONArray("list") != null && result.getJSONObject("info").getJSONArray("list").size() > 0){
                        current = result.getJSONObject("info").getJSONArray("list").getJSONObject(0);
                    }
                }

                //前一天
                //dayParamDto.setDay(TimeUtil.yesterday());
                //JSONObject lastTime = orderDataService.getOrderData(dayParamDto);
                String yesterday = TimeUtil.yesterday() ;
                params.getRangeParams().getCreated().setStart(yesterday+" 00:00:00");
                params.getRangeParams().getCreated().setEnd(yesterday+" 23:59:59");
                log.info("请求参数：{}",JSONObject.toJSON(params).toString());
                JSONObject lastTime = oneDataServiceFeign.getRealTimeData(params,headers);
                log.info("昨天数据：{}",lastTime.toJSONString());
                if(lastTime !=null && lastTime.containsKey("code") && "200".equals(lastTime.getString("code"))){
                    if(lastTime.containsKey("info") && lastTime.getJSONObject("info") !=null){
                        JSONObject lastData = lastTime.getJSONObject("info");
                        if(lastData.getJSONArray("list") != null && lastData.getJSONArray("list").size() > 0){
                            DecimalFormat decimalFormat = new DecimalFormat("0.00");
                            JSONObject last = lastData.getJSONArray("list").getJSONObject(0);

                            if(last.getDouble("onlineAmount") != null && last.getDouble("onlineAmount").doubleValue() ==0){
                                if(current.getDouble("onlineAmount") != null && current.getDouble("onlineAmount").doubleValue() > 0){
                                    current.put("onlineAmountUp",1);
                                }

                            }else if(last.getDouble("onlineAmount") != null && last.getDouble("onlineAmount").doubleValue() > 0){
                                current.put("onlineAmountUp",decimalFormat.format((current.getDoubleValue("onlineAmount")-last.getDoubleValue("onlineAmount"))/last.getDoubleValue("onlineAmount")));
                            }

                            if(last.getDouble("cashAmount") != null && last.getDouble("cashAmount").doubleValue() ==0){
                                if(current.getDouble("cashAmount") != null && current.getDouble("cashAmount").doubleValue() > 0){
                                    current.put("cashAmountUp",1);
                                }

                            }else if(last.getDouble("cashAmount") != null && last.getDouble("cashAmount").doubleValue() > 0){
                                current.put("cashAmountUp",decimalFormat.format((current.getDoubleValue("cashAmount")-last.getDoubleValue("cashAmount"))/last.getDoubleValue("cashAmount")));
                            }

                            if(last.getDouble("cashRefundCounts") != null && last.getDouble("cashRefundCounts").doubleValue() ==0){
                                if(current.getDouble("cashRefundCounts") != null && current.getDouble("cashRefundCounts").doubleValue() > 0){
                                    current.put("cashRefundCountsUp",1);
                                }

                            }else if(last.getDouble("cashRefundCounts") != null && last.getDouble("cashRefundCounts").doubleValue() > 0){
                                current.put("cashRefundCountsUp",decimalFormat.format((current.getDoubleValue("cashRefundCounts")-last.getDoubleValue("cashRefundCounts"))/last.getDoubleValue("cashRefundCounts")));
                            }

                            if(last.getDouble("onlineRefundCounts") != null && last.getDouble("onlineRefundCounts").doubleValue() ==0){
                                if(current.getDouble("onlineRefundCounts") != null && current.getDouble("onlineRefundCounts").doubleValue() > 0){
                                    current.put("onlineRefundCountsUp",1);
                                }

                            }else if(last.getDouble("onlineRefundCounts") != null && last.getDouble("onlineRefundCounts").doubleValue() > 0){
                                current.put("onlineRefundCountsUp",decimalFormat.format((current.getDoubleValue("onlineRefundCounts")-last.getDoubleValue("onlineRefundCounts"))/last.getDoubleValue("onlineRefundCounts")));
                            }
                            
                            if(last.getDouble("onlinePayCounts") != null && last.getDouble("onlinePayCounts").doubleValue() ==0){
                                if(current.getDouble("onlinePayCounts") != null && current.getDouble("onlinePayCounts").doubleValue() > 0){
                                    current.put("onlinePayCountsUp",1);
                                }

                            }else if(last.getDouble("onlinePayCounts") != null && last.getDouble("onlinePayCounts").doubleValue() > 0){
                                current.put("onlinePayCountsUp",decimalFormat.format((current.getDoubleValue("onlinePayCounts")-last.getDoubleValue("onlinePayCounts"))/last.getDoubleValue("onlinePayCounts")));
                            }

                            if(last.getDouble("payAmount") != null && last.getDouble("payAmount").doubleValue() ==0){
                                if(current.getDouble("payAmount") != null && current.getDouble("payAmount").doubleValue() > 0){
                                    current.put("payAmountUp",1);
                                }

                            }else if(last.getDouble("payAmount") != null && last.getDouble("payAmount").doubleValue() > 0){
                                current.put("payAmountUp",decimalFormat.format((current.getDoubleValue("payAmount")-last.getDoubleValue("payAmount"))/last.getDoubleValue("payAmount")));
                            }

                            if(last.getDouble("onlineRefundAmount") != null && last.getDouble("onlineRefundAmount").doubleValue() ==0){
                                if(current.getDouble("onlineRefundAmount") != null && current.getDouble("onlineRefundAmount").doubleValue() > 0){
                                    current.put("onlineRefundAmountUp",1);
                                }

                            }else if(last.getDouble("onlineRefundAmount") != null && last.getDouble("onlineRefundAmount").doubleValue() > 0){
                                current.put("onlineRefundAmountUp",decimalFormat.format((current.getDoubleValue("onlineRefundAmount")-last.getDoubleValue("onlineRefundAmount"))/last.getDoubleValue("onlineRefundAmount")));
                            }

                            if(last.getDouble("cashPayAmount") != null && last.getDouble("cashPayAmount").doubleValue() ==0){
                                if(current.getDouble("cashPayAmount") != null && current.getDouble("cashPayAmount").doubleValue() > 0){
                                    current.put("cashPayAmountUp",1);
                                }

                            }else if(last.getDouble("cashPayAmount") != null && last.getDouble("cashPayAmount").doubleValue() > 0){
                                current.put("cashPayAmountUp",decimalFormat.format((current.getDoubleValue("cashPayAmount")-last.getDoubleValue("cashPayAmount"))/last.getDoubleValue("cashPayAmount")));
                            }

                            if(last.getDouble("refundAmount") != null && last.getDouble("refundAmount").doubleValue() ==0){
                                if(current.getDouble("refundAmount") != null && current.getDouble("refundAmount").doubleValue() > 0){
                                    current.put("refundAmountUp",1);
                                }

                            }else if(last.getDouble("refundAmount") != null && last.getDouble("refundAmount").doubleValue() > 0){
                                current.put("refundAmountUp",decimalFormat.format((current.getDoubleValue("refundAmount")-last.getDoubleValue("refundAmount"))/last.getDoubleValue("refundAmount")));
                            }

                            if(last.getDouble("cashRefundAmount") != null && last.getDouble("cashRefundAmount").doubleValue() ==0){
                                if(current.getDouble("cashRefundAmount") != null && current.getDouble("cashRefundAmount").doubleValue() > 0){
                                    current.put("cashRefundAmountUp",1);
                                }

                            }else if(last.getDouble("cashRefundAmount") != null && last.getDouble("cashRefundAmount").doubleValue() > 0){
                                current.put("cashRefundAmountUp",decimalFormat.format((current.getDoubleValue("cashRefundAmount")-last.getDoubleValue("cashRefundAmount"))/last.getDoubleValue("cashRefundAmount")));
                            }

                            if(last.getDouble("cashPayCounts") != null && last.getDouble("cashPayCounts").doubleValue() ==0){
                                if(current.getDouble("cashPayCounts") != null && current.getDouble("cashPayCounts").doubleValue() > 0){
                                    current.put("cashPayCountsUp",1);
                                }

                            }else if(last.getDouble("cashPayCounts") != null && last.getDouble("cashPayCounts").doubleValue() > 0){
                                current.put("cashPayCountsUp",decimalFormat.format((current.getDoubleValue("cashPayCounts")-last.getDoubleValue("cashPayCounts"))/last.getDoubleValue("cashPayCounts")));
                            }

                            if(last.getDouble("refundCounts") != null && last.getDouble("refundCounts").doubleValue() ==0){
                                if(current.getDouble("refundCounts") != null && current.getDouble("refundCounts").doubleValue() > 0){
                                    current.put("refundCountsUp",1);
                                }

                            }else if(last.getDouble("refundCounts") != null && last.getDouble("refundCounts").doubleValue() > 0){
                                current.put("refundCountsUp",decimalFormat.format((current.getDoubleValue("refundCounts")-last.getDoubleValue("refundCounts"))/last.getDoubleValue("refundCounts")));
                            }

                            if(last.getDouble("onlinePayAmount") != null && last.getDouble("onlinePayAmount").doubleValue() ==0){
                                if(current.getDouble("onlinePayAmount") != null && current.getDouble("onlinePayAmount").doubleValue() > 0){
                                    current.put("onlinePayAmountUp",1);
                                }

                            }else if(last.getDouble("onlinePayAmount") != null && last.getDouble("onlinePayAmount").doubleValue() > 0){
                                current.put("onlinePayAmountUp",decimalFormat.format((current.getDoubleValue("onlinePayAmount")-last.getDoubleValue("onlinePayAmount"))/last.getDoubleValue("onlinePayAmount")));
                            }

                            if(last.getDouble("payCounts") != null && last.getDouble("payCounts").doubleValue() ==0){
                                if(current.getDouble("payCounts") != null && current.getDouble("payCounts").doubleValue() > 0){
                                    current.put("payCountsUp",1);
                                }

                            }else if(last.getDouble("payCounts") != null && last.getDouble("payCounts").doubleValue() > 0){
                                current.put("payCountsUp",decimalFormat.format((current.getDoubleValue("payCounts")-last.getDoubleValue("payCounts"))/last.getDoubleValue("payCounts")));
                            }

                            if(last.getDouble("payCounts") != null && last.getDouble("payCounts").doubleValue() ==0){
                                if(current.getDouble("payCounts") != null && current.getDouble("payCounts").doubleValue() > 0){
                                    current.put("payCountsUp",1);
                                }

                            }else if(last.getDouble("payCounts") != null && last.getDouble("payCounts").doubleValue() > 0){
                                current.put("payCountsUp",decimalFormat.format((current.getDoubleValue("payCounts")-last.getDoubleValue("payCounts"))/last.getDoubleValue("payCounts")));
                            }
                        }
                    }
                }
            }

            return result;
        }catch (Exception e){
            log.warn("{}",e);
        }

        return null;
    }

    @Override
    public JSONObject getTimeRangeData(DayParamDto dayParamDto) {
        MultiValueMap<String, String> headers = new HttpHeaders();
        headers.add("sk",sk);
        headers.add("ak",ak);

        RealTimeParamDto params = new RealTimeParamDto();
        params.setCode(realTimeOrderAnalyseCode);
        params.setGroupByColumnArray("ad_org_id");
        params.getRangeParams().getCreated().setStart(dayParamDto.getDay()+" 00:00:00");
        params.getRangeParams().getCreated().setEnd(dayParamDto.getDay()+" 23:59:59");

        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.RT_TIME_RANGE_DATA_COLUMN_ARRAY) ;
        params.setColumnArray(columnArray);

        params.getQueryParams().setAd_org_id(dayParamDto.getMerchantId()+"");

        params.getQueryParams().setLyy_equipment_group_id(dayParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(dayParamDto.getGroupId()+"");}});
        params.getQueryParams().setLyy_equipment_type_id(dayParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(dayParamDto.getEquipmentTypeId()+"");}});

        boolean filter = false;
        Set<String> groupSet = new HashSet<>();

        if((dayParamDto.getProvinceId() != null && dayParamDto.getProvinceId()>0) || (dayParamDto.getCityId() != null && dayParamDto.getCityId()>0) || (dayParamDto.getAreaId() != null && dayParamDto.getAreaId()>0)){
            if((dayParamDto.getGroupId() == null || dayParamDto.getGroupId() <=0) && (dayParamDto.getGroupIds() == null || dayParamDto.getGroupIds().isEmpty())){
                filter = true;
                //有选择省市区，先通过省市区筛选出场地
                groupSet = getGroupId(dayParamDto,headers);
            }

        }
        if(dayParamDto.getGroupIds() != null && dayParamDto.getGroupIds().size()>0){
            //证明是子账户
            if(filter){
                //证明没有选择场地，而是通过省市区过来的场地
                if(groupSet.size() > 0){
                    Iterator<String> iterator = groupSet.iterator();
                    while (iterator.hasNext()){
                        String gid = iterator.next();
                        if(!dayParamDto.getGroupIds().contains(gid)){
                            iterator.remove();
                        }

                    }
                    if(groupSet.size()>0){
                        params.getQueryParams().setLyy_equipment_group_id(groupSet);
                    }else{
                        return null;
                    }

                }else{
                    //证明筛选了省份城市等，但是没有一个场地
                    return null;
                }

            }else{
                //证明是子账户且没有省份城市等筛选条件
                if(dayParamDto.getGroupId() == null){
                   // params.getQueryParams().setLyy_equipment_group_id(new HashSet<String>(){{add(((TreeSet)(dayParamDto.getGroupIds())).first()+"");}});
                	params.getQueryParams().setLyy_equipment_group_id(dayParamDto.getGroupIds()) ;
                	params.getOrderParams().put("ad_org_id",params.buildOrderFile("asc"));
                   // params.getOrderParams().put("lyy_equipment_group_id",params.buildOrderFile("asc"));
                }else{
                    //证明前端有选择一个场地，如果在权限范围内则允许。否则退出
                    if(dayParamDto.getGroupIds().contains(dayParamDto.getGroupId())){
                        params.getQueryParams().setLyy_equipment_group_id(new HashSet<String>(){{add(dayParamDto.getGroupId()+"");}});
                    }else{
                        //params.getQueryParams().setLyy_equipment_group_id(new HashSet<String>(){{add("-11111");}});
                        return null;
                    }
                }

            }


        }else{
            //主账户且选择了省份城市等
            if(filter){
                if(groupSet.size() > 0){
                    params.getQueryParams().setLyy_equipment_group_id(groupSet);
                }else{
                    //证明筛选了省份城市等，但是没有一个场地
                    return null;
                }
            }
        }

        try{
            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
            JSONObject result = oneDataServiceFeign.getRealTimeData(params,headers);
            log.info("{}",result.toJSONString());
            //处理客单价
            if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){{
                JSONObject obj = result.getJSONObject("info");
                if(obj.getJSONArray("list") != null && obj.getJSONArray("list").size()>0){
                    DecimalFormat decimalFormat = new DecimalFormat("0.##");
                    JSONObject data = obj.getJSONArray("list").getJSONObject(0);

                    if(data.getInteger("count_0_1") == 0){
                        data.put("perOrderPrice_0_1",0);
                    }else{
                        data.put("perOrderPrice_0_1",decimalFormat.format(data.getDoubleValue("amount_0_1")/data.getInteger("count_0_1")));
                    }

                    if(data.getInteger("count_1_2") == 0){
                        data.put("perOrderPrice_1_2",0);
                    }else{
                        data.put("perOrderPrice_1_2",decimalFormat.format(data.getDoubleValue("amount_1_2")/data.getInteger("count_1_2")));
                    }

                    if(data.getInteger("count_2_3") == 0){
                        data.put("perOrderPrice_2_3",0);
                    }else{
                        data.put("perOrderPrice_2_3",decimalFormat.format(data.getDoubleValue("amount_2_3")/data.getInteger("count_2_3")));
                    }

                    if(data.getInteger("count_3_4") == 0){
                        data.put("perOrderPrice_3_4",0);
                    }else{
                        data.put("perOrderPrice_3_4",decimalFormat.format(data.getDoubleValue("amount_3_4")/data.getInteger("count_3_4")));
                    }

                    if(data.getInteger("count_4_5") == 0){
                        data.put("perOrderPrice_4_5",0);
                    }else{
                        data.put("perOrderPrice_4_5",decimalFormat.format(data.getDoubleValue("amount_4_5")/data.getInteger("count_4_5")));
                    }

                    if(data.getInteger("count_5_6") == 0){
                        data.put("perOrderPrice_5_6",0);
                    }else{
                        data.put("perOrderPrice_5_6",decimalFormat.format(data.getDoubleValue("amount_5_6")/data.getInteger("count_5_6")));
                    }

                    if(data.getInteger("count_6_7") == 0){
                        data.put("perOrderPrice_6_7",0);
                    }else{
                        data.put("perOrderPrice_6_7",decimalFormat.format(data.getDoubleValue("amount_6_7")/data.getInteger("count_6_7")));
                    }

                    if(data.getInteger("count_7_8") == 0){
                        data.put("perOrderPrice_7_8",0);
                    }else{
                        data.put("perOrderPrice_7_8",decimalFormat.format(data.getDoubleValue("amount_7_8")/data.getInteger("count_7_8")));
                    }

                    if(data.getInteger("count_8_9") == 0){
                        data.put("perOrderPrice_8_9",0);
                    }else{
                        data.put("perOrderPrice_8_9",decimalFormat.format(data.getDoubleValue("amount_8_9")/data.getInteger("count_8_9")));
                    }

                    if(data.getInteger("count_9_10") == 0){
                        data.put("perOrderPrice_9_10",0);
                    }else{
                        data.put("perOrderPrice_9_10",decimalFormat.format(data.getDoubleValue("amount_9_10")/data.getInteger("count_9_10")));
                    }

                    if(data.getInteger("count_10_11") == 0){
                        data.put("perOrderPrice_10_11",0);
                    }else{
                        data.put("perOrderPrice_10_11",decimalFormat.format(data.getDoubleValue("amount_10_11")/data.getInteger("count_10_11")));
                    }

                    if(data.getInteger("count_11_12") == 0){
                        data.put("perOrderPrice_11_12",0);
                    }else{
                        data.put("perOrderPrice_11_12",decimalFormat.format(data.getDoubleValue("amount_11_12")/data.getInteger("count_11_12")));
                    }

                    if(data.getInteger("count_12_13") == 0){
                        data.put("perOrderPrice_12_13",0);
                    }else{
                        data.put("perOrderPrice_12_13",decimalFormat.format(data.getDoubleValue("amount_12_13")/data.getInteger("count_12_13")));
                    }

                    if(data.getInteger("count_13_14") == 0){
                        data.put("perOrderPrice_13_14",0);
                    }else{
                        data.put("perOrderPrice_13_14",decimalFormat.format(data.getDoubleValue("amount_13_14")/data.getInteger("count_13_14")));
                    }

                    if(data.getInteger("count_14_15") == 0){
                        data.put("perOrderPrice_14_15",0);
                    }else{
                        data.put("perOrderPrice_14_15",decimalFormat.format(data.getDoubleValue("amount_14_15")/data.getInteger("count_14_15")));
                    }

                    if(data.getInteger("count_15_16") == 0){
                        data.put("perOrderPrice_15_16",0);
                    }else{
                        data.put("perOrderPrice_15_16",decimalFormat.format(data.getDoubleValue("amount_15_16")/data.getInteger("count_15_16")));
                    }

                    if(data.getInteger("count_16_17") == 0){
                        data.put("perOrderPrice_16_17",0);
                    }else{
                        data.put("perOrderPrice_16_17",decimalFormat.format(data.getDoubleValue("amount_16_17")/data.getInteger("count_16_17")));
                    }

                    if(data.getInteger("count_17_18") == 0){
                        data.put("perOrderPrice_17_18",0);
                    }else{
                        data.put("perOrderPrice_17_18",decimalFormat.format(data.getDoubleValue("amount_17_18")/data.getInteger("count_17_18")));
                    }

                    if(data.getInteger("count_18_19") == 0){
                        data.put("perOrderPrice_18_19",0);
                    }else{
                        data.put("perOrderPrice_18_19",decimalFormat.format(data.getDoubleValue("amount_18_19")/data.getInteger("count_18_19")));
                    }

                    if(data.getInteger("count_19_20") == 0){
                        data.put("perOrderPrice_19_20",0);
                    }else{
                        data.put("perOrderPrice_19_20",decimalFormat.format(data.getDoubleValue("amount_19_20")/data.getInteger("count_19_20")));
                    }

                    if(data.getInteger("count_20_21") == 0){
                        data.put("perOrderPrice_20_21",0);
                    }else{
                        data.put("perOrderPrice_20_21",decimalFormat.format(data.getDoubleValue("amount_20_21")/data.getInteger("count_20_21")));
                    }

                    if(data.getInteger("count_21_22") == 0){
                        data.put("perOrderPrice_21_22",0);
                    }else{
                        data.put("perOrderPrice_21_22",decimalFormat.format(data.getDoubleValue("amount_21_22")/data.getInteger("count_21_22")));
                    }

                    if(data.getInteger("count_22_23") == 0){
                        data.put("perOrderPrice_22_23",0);
                    }else{
                        data.put("perOrderPrice_22_23",decimalFormat.format(data.getDoubleValue("amount_22_23")/data.getInteger("count_22_23")));
                    }

                    if(data.getInteger("count_23_24") == 0){
                        data.put("perOrderPrice_23_24",0);
                    }else{
                        data.put("perOrderPrice_23_24",decimalFormat.format(data.getDoubleValue("amount_23_24")/data.getInteger("count_23_24")));
                    }

                }
            }
            }
            return result;
        }catch (Exception e){
            log.warn("{}",e);
        }



        return null;
    }

    @Override
    public JSONObject getPerOrderPriceCount(DayParamDto dayParamDto) {
        MultiValueMap<String, String> headers = new HttpHeaders();
        headers.add("sk",sk);
        headers.add("ak",ak);

        RealTimeParamDto params = new RealTimeParamDto();
        params.setCode(realTimeOrderAnalyseCode);
        params.setGroupByColumnArray("ad_org_id");
        params.getRangeParams().getCreated().setStart(dayParamDto.getDay()+" 00:00:00");
        params.getRangeParams().getCreated().setEnd(dayParamDto.getDay()+" 23:59:59");

        params.getQueryParams().setAd_org_id(dayParamDto.getMerchantId()+"");

        params.getQueryParams().setLyy_equipment_group_id(dayParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(dayParamDto.getGroupId()+"");}});
        params.getQueryParams().setLyy_equipment_type_id(dayParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(dayParamDto.getEquipmentTypeId()+"");}});

        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.RT_PER_ORDER_PRICE_COUNT_COLUMN_ARRAY) ;
        params.setColumnArray(columnArray);

        boolean filter = false;
        Set<String> groupSet = new HashSet<>();

        if((dayParamDto.getProvinceId() != null && dayParamDto.getProvinceId()>0) || (dayParamDto.getCityId() != null && dayParamDto.getCityId()>0) || (dayParamDto.getAreaId() != null && dayParamDto.getAreaId()>0)){
            if((dayParamDto.getGroupId() == null || dayParamDto.getGroupId() <=0) && (dayParamDto.getGroupIds() == null || dayParamDto.getGroupIds().isEmpty())){
                filter = true;
                //有选择省市区，先通过省市区筛选出场地
                groupSet = getGroupId(dayParamDto,headers);
            }

        }
        if(dayParamDto.getGroupIds() != null && dayParamDto.getGroupIds().size()>0){
            //证明是子账户
            if(filter){
                //证明没有选择场地，而是通过省市区过来的场地
                if(groupSet.size() > 0){
                    Iterator<String> iterator = groupSet.iterator();
                    while (iterator.hasNext()){
                        String gid = iterator.next();
                        if(!dayParamDto.getGroupIds().contains(gid)){
                            iterator.remove();
                        }

                    }
                    if(groupSet.size()>0){
                        params.getQueryParams().setLyy_equipment_group_id(groupSet);
                    }else{
                        return null;
                    }

                }else{
                    //证明筛选了省份城市等，但是没有一个场地
                    return null;
                }

            }else{
                //证明是子账户且没有省份城市等筛选条件
                if(dayParamDto.getGroupId() == null){
                    //params.getQueryParams().setLyy_equipment_group_id(new HashSet<String>(){{add(((TreeSet)(dayParamDto.getGroupIds())).first()+"");}});
                    params.getQueryParams().setLyy_equipment_group_id(dayParamDto.getGroupIds()) ;
                	params.getOrderParams().put("ad_org_id",params.buildOrderFile("asc"));
                    //params.getOrderParams().put("lyy_equipment_group_id",params.buildOrderFile("asc"));
                }else{
                    //证明前端有选择一个场地，如果在权限范围内则允许。否则退出
                    if(dayParamDto.getGroupIds().contains(dayParamDto.getGroupId())){
                        params.getQueryParams().setLyy_equipment_group_id(new HashSet<String>(){{add(dayParamDto.getGroupId()+"");}});
                    }else{
                        //params.getQueryParams().setLyy_equipment_group_id(new HashSet<String>(){{add("-11111");}});
                        return null;
                    }
                }

            }


        }else{
            //主账户且选择了省份城市等
            if(filter){
                if(groupSet.size() > 0){
                    params.getQueryParams().setLyy_equipment_group_id(groupSet);
                }else{
                    //证明筛选了省份城市等，但是没有一个场地
                    return null;
                }
            }
        }

        try{
            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
            JSONObject result = oneDataServiceFeign.getRealTimeData(params,headers);
            log.info("{}",result.toJSONString());
            //处理客单价
            if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){{
                JSONObject obj = result.getJSONObject("info");
                if(obj.getJSONArray("list") != null && obj.getJSONArray("list").size()>0){
                    DecimalFormat decimalFormat = new DecimalFormat("#.00");
                    JSONObject data = obj.getJSONArray("list").getJSONObject(0);

                    if(data.getInteger("pay_0_1_count") == 0){
                        data.put("payOrderPrice0_1",0);
                    }else{
                        data.put("payOrderPrice0_1",decimalFormat.format(data.getDoubleValue("pay_0_1_amount")/data.getInteger("pay_0_1_count")));
                    }

                    if(data.getInteger("pay_1_3_count") == 0){
                        data.put("payOrderPrice1_3",0);
                    }else{
                        data.put("payOrderPrice1_3",decimalFormat.format(data.getDoubleValue("pay_1_3_amount")/data.getInteger("pay_1_3_count")));
                    }

                    if(data.getInteger("pay_3_5_count") == 0){
                        data.put("payOrderPrice3_5",0);
                    }else{
                        data.put("payOrderPrice3_5",decimalFormat.format(data.getDoubleValue("pay_3_5_amount")/data.getInteger("pay_3_5_count")));
                    }

                    if(data.getInteger("pay_5_10_count") == 0){
                        data.put("payOrderPrice5_10",0);
                    }else{
                        data.put("payOrderPrice5_10",decimalFormat.format(data.getDoubleValue("pay_5_10_amount")/data.getInteger("pay_5_10_count")));
                    }

                    if(data.getInteger("pay_10_15_count") == 0){
                        data.put("payOrderPrice10_15",0);
                    }else{
                        data.put("payOrderPrice10_15",decimalFormat.format(data.getDoubleValue("pay_10_15_amount")/data.getInteger("pay_10_15_count")));
                    }

                    if(data.getInteger("pay_15_30_count") == 0){
                        data.put("payOrderPrice15_30",0);
                    }else{
                        data.put("payOrderPrice15_30",decimalFormat.format(data.getDoubleValue("pay_15_30_amount")/data.getInteger("pay_15_30_count")));
                    }

                    if(data.getInteger("pay_30_50_count") == 0){
                        data.put("payOrderPrice30_50",0);
                    }else{
                        data.put("payOrderPrice30_50",decimalFormat.format(data.getDoubleValue("pay_30_50_amount")/data.getInteger("pay_30_50_count")));
                    }

                    if(data.getInteger("pay_50_100_count") == 0){
                        data.put("payOrderPrice50_100",0);
                    }else{
                        data.put("payOrderPrice50_100",decimalFormat.format(data.getDoubleValue("pay_50_100_amount")/data.getInteger("pay_50_100_count")));
                    }

                    if(data.getInteger("pay_100_200_count") == 0){
                        data.put("payOrderPrice100_200",0);
                    }else{
                        data.put("payOrderPrice100_200",decimalFormat.format(data.getDoubleValue("pay_100_200_amount")/data.getInteger("pay_100_200_count")));
                    }

                    if(data.getInteger("pay_200_500_count") == 0){
                        data.put("payOrderPrice200_500",0);
                    }else{
                        data.put("payOrderPrice200_500",decimalFormat.format(data.getDoubleValue("pay_200_500_amount")/data.getInteger("pay_200_500_count")));
                    }

                    if(data.getInteger("pay_500_1000_count") == 0){
                        data.put("payOrderPrice500_1000",0);
                    }else{
                        data.put("payOrderPrice500_1000",decimalFormat.format(data.getDoubleValue("pay_500_1000_amount")/data.getInteger("pay_500_1000_count")));
                    }

                    if(data.getInteger("over_1000_count") == 0){
                        data.put("payOrderPriceOver1000",0);
                    }else{
                        data.put("payOrderPriceOver1000",decimalFormat.format(data.getDoubleValue("over_1000_amount")/data.getInteger("over_1000_count")));
                    }


                    JSONObject newObj = new JSONObject();
                    JSONArray reData = new JSONArray();
                    Map<Integer,String> map = new HashMap<>();
                    Map<Integer,String> payMap = new HashMap<>();
                    Map<Integer,String> svgMap = new HashMap<>();
                    Map<Integer,String> newMap = new HashMap<>();
                    map.put(1,"pay_0_1_count");
                    map.put(2,"pay_1_3_count");
                    map.put(3,"pay_3_5_count");
                    map.put(4,"pay_5_10_count");
                    map.put(5,"pay_10_15_count");
                    map.put(6,"pay_15_30_count");
                    map.put(7,"pay_30_50_count");
                    map.put(8,"pay_50_100_count");
                    map.put(9,"pay_100_200_count");
                    map.put(10,"pay_200_500_count");
                    map.put(11,"pay_500_1000_count");
                    map.put(12,"over_1000_count");

                    payMap.put(1,"pay_0_1_amount");
                    payMap.put(2,"pay_1_3_amount");
                    payMap.put(3,"pay_3_5_amount");
                    payMap.put(4,"pay_5_10_amount");
                    payMap.put(5,"pay_10_15_amount");
                    payMap.put(6,"pay_15_30_amount");
                    payMap.put(7,"pay_30_50_amount");
                    payMap.put(8,"pay_50_100_amount");
                    payMap.put(9,"pay_100_200_amount");
                    payMap.put(10,"pay_200_500_amount");
                    payMap.put(11,"pay_500_1000_amount");
                    payMap.put(12,"over_1000_amount");

                    svgMap.put(1,"payOrderPrice0_1");
                    svgMap.put(2,"payOrderPrice1_3");
                    svgMap.put(3,"payOrderPrice3_5");
                    svgMap.put(4,"payOrderPrice5_10");
                    svgMap.put(5,"payOrderPrice10_15");
                    svgMap.put(6,"payOrderPrice15_30");
                    svgMap.put(7,"payOrderPrice30_50");
                    svgMap.put(8,"payOrderPrice50_100");
                    svgMap.put(9,"payOrderPrice100_200");
                    svgMap.put(10,"payOrderPrice200_500");
                    svgMap.put(11,"payOrderPrice500_1000");
                    svgMap.put(12,"payOrderPriceOver1000");



                    newMap.put(1,"0-1");
                    newMap.put(2,"1-3");
                    newMap.put(3,"3-5");
                    newMap.put(4,"5-10");
                    newMap.put(5,"10-15");
                    newMap.put(6,"15-30");
                    newMap.put(7,"30-50");
                    newMap.put(8,"50-100");
                    newMap.put(9,"100-200");
                    newMap.put(10,"200-500");
                    newMap.put(11,"500-1000");
                    newMap.put(12,"1000以上");
                    int start = 0;
                    int count = 0;
                    int end = 0;
                    if(data.getInteger("pay_0_1_count")>0){
                        start = 1;
                        end = 1;
                        count++;
                    }
                    if(data.getInteger("pay_1_3_count")>0){
                        if(start <= 0){
                            start = 2;
                        }
                        end = 2;
                        count++;
                    }
                    if(data.getInteger("pay_3_5_count")>0){
                        if(start <= 0){
                            start = 3;
                        }
                        end = 3;
                        count++;
                    }
                    if(data.getInteger("pay_5_10_count")>0){
                        if(start <= 0){
                            start = 4;
                        }
                        end = 4;
                        count++;
                    }
                    if(data.getInteger("pay_10_15_count")>0){
                        if(start <= 0){
                            start = 5;
                        }
                        end = 5;
                        count++;
                    }
                    if(data.getInteger("pay_15_30_count")>0){
                        if(start <= 0){
                            start = 6;
                        }
                        end = 6;
                        count++;
                    }
                    if(data.getInteger("pay_30_50_count")>0){
                        if(start <= 0){
                            start = 7;
                        }
                        end = 7;
                        count++;
                    }
                    if(data.getInteger("pay_50_100_count")>0){
                        if(start <= 0){
                            start = 8;
                        }
                        end = 8;
                        count++;
                    }
                    if(data.getInteger("pay_100_200_count")>0){
                        if(start <= 0){
                            start = 9;
                        }
                        end = 9;
                        count++;
                    }
                    if(data.getInteger("pay_200_500_count")>0){
                        if(start <= 0){
                            start = 10;
                        }
                        end = 10;
                        count++;
                    }
                    if(data.getInteger("pay_500_1000_count")>0){
                        if(start <= 0){
                            start = 11;
                        }
                        end = 11;
                        count++;
                    }
                    if(data.getInteger("over_1000_count")>0){
                        if(start <= 0){
                            start = 12;
                        }
                        end = 12;
                        count++;
                    }

                    //刚刚好6个档次区间
                    if(end - start ==5){
                        for(int i=start; i<=end; i++){
                            newObj.put("show",newMap.get(i));
                            newObj.put("orderCount",data.getIntValue(map.get(i)));
                            newObj.put("payAmount",data.getDoubleValue(payMap.get(i)));
                            newObj.put("svgPrice",data.getDoubleValue(svgMap.get(i)));
                            reData.add(newObj);
                            newObj = new JSONObject();
                        }
                    }

                    //如果少于6个，且网上能够补齐6个
                    if(end - start < 5){
                        //网上区间够补齐6个
                        if(start<=7){
                            for(int i=start; i<=start+5; i++){
                                newObj.put("show",newMap.get(i));
                                newObj.put("orderCount",data.getIntValue(map.get(i)));
                                newObj.put("payAmount",data.getDoubleValue(payMap.get(i)));
                                newObj.put("svgPrice",data.getDoubleValue(svgMap.get(i)));
                                reData.add(newObj);
                                newObj = new JSONObject();
                            }
                        }
                        //往上不够补，往下补
                        if(start>7){
                            for(int i=7; i<=12; i++){
                                newObj.put("show",newMap.get(i));
                                newObj.put("orderCount",data.getIntValue(map.get(i)));
                                newObj.put("payAmount",data.getDoubleValue(payMap.get(i)));
                                newObj.put("svgPrice",data.getDoubleValue(svgMap.get(i)));
                                reData.add(newObj);
                                newObj = new JSONObject();
                            }
                        }

                    }


                    //大于6个档次
                    if(end - start > 5){
                        for(int i=start; i<start+5; i++){
                            newObj.put("show",newMap.get(i));
                            newObj.put("orderCount",data.getIntValue(map.get(i)));
                            newObj.put("payAmount",data.getDoubleValue(payMap.get(i)));
                            newObj.put("svgPrice",data.getDoubleValue(svgMap.get(i)));
                            reData.add(newObj);
                            newObj = new JSONObject();
                        }
                        int lastCounts = 0;
                        double lastPays = 0;
                        for(int i=start+5; i<=end; i++){
                            lastCounts += data.getIntValue(map.get(i));
                            lastPays += data.getDoubleValue(payMap.get(i));
                        }
                        //DecimalFormat decimalFormat = new DecimalFormat("#.00");
                        newObj.put("show",newMap.get(start+5).split("-")[0]+"以上");
                        newObj.put("orderCount",lastCounts);
                        newObj.put("payAmount",lastPays);
                        newObj.put("svgPrice",decimalFormat.format(lastPays/lastCounts));
                        reData.add(newObj);
                    }
                    result.getJSONObject("info").put("totalCount",6);
                    result.getJSONObject("info").remove("list");
                    result.getJSONObject("info").put("list",reData);

                }
            }
            }
            return result;
        }catch (Exception e){
            log.warn("{}",e);
        }
        return null;
    }

    @Override
    public JSONObject getGroupEquipmentData(DayParamDto dayParamDto) {

        return null;
    }

    @Override
    public JSONObject getMemberData(DayParamDto dayParamDto) {
        MultiValueMap<String, String> headers = new HttpHeaders();
        headers.add("sk",sk);
        headers.add("ak",ak);

        RealTimeParamDto params = new RealTimeParamDto();
        params.setCode(realTimeOrderAnalyseCode);
        params.setGroupByColumnArray("ad_org_id");
        params.getRangeParams().getCreated().setStart(dayParamDto.getDay()+" 00:00:00");
        params.getRangeParams().getCreated().setEnd(dayParamDto.getDay()+" 23:59:59");

        params.getQueryParams().setAd_org_id(dayParamDto.getMerchantId()+"");
        params.getQueryParams().setLyy_equipment_group_id(dayParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(dayParamDto.getGroupId()+"");}});
        params.getQueryParams().setLyy_equipment_type_id(dayParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(dayParamDto.getEquipmentTypeId()+"");}});

        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.RT_MEMBER_DATA_COLUMN_ARRAY) ;
        params.setColumnArray(columnArray);

        boolean filter = false;
        Set<String> groupSet = new HashSet<>();

        if((dayParamDto.getProvinceId() != null && dayParamDto.getProvinceId()>0) || (dayParamDto.getCityId() != null && dayParamDto.getCityId()>0) || (dayParamDto.getAreaId() != null && dayParamDto.getAreaId()>0)){
            if((dayParamDto.getGroupId() == null || dayParamDto.getGroupId() <=0) && (dayParamDto.getGroupIds() == null || dayParamDto.getGroupIds().isEmpty())){
                filter = true;
                //有选择省市区，先通过省市区筛选出场地
                groupSet = getGroupId(dayParamDto,headers);
            }

        }
        if(dayParamDto.getGroupIds() != null && dayParamDto.getGroupIds().size()>0){
            //证明是子账户
            if(filter){
                //证明没有选择场地，而是通过省市区过来的场地
                if(groupSet.size() > 0){
                    Iterator<String> iterator = groupSet.iterator();
                    while (iterator.hasNext()){
                        String gid = iterator.next();
                        if(!dayParamDto.getGroupIds().contains(gid)){
                            iterator.remove();
                        }

                    }
                    if(groupSet.size()>0){
                        params.getQueryParams().setLyy_equipment_group_id(groupSet);
                    }else{
                        return null;
                    }

                }else{
                    //证明筛选了省份城市等，但是没有一个场地
                    return null;
                }

            }else{
                //证明是子账户且没有省份城市等筛选条件
                if(dayParamDto.getGroupId() == null){
                    //params.getQueryParams().setLyy_equipment_group_id(new HashSet<String>(){{add(((TreeSet)(dayParamDto.getGroupIds())).first()+"");}});
                	params.getQueryParams().setLyy_equipment_group_id(dayParamDto.getGroupIds());
                	params.getOrderParams().put("ad_org_id",params.buildOrderFile("asc"));
                   // params.getOrderParams().put("lyy_equipment_group_id",params.buildOrderFile("asc"));
                }else{
                    //证明前端有选择一个场地，如果在权限范围内则允许。否则退出
                    if(dayParamDto.getGroupIds().contains(dayParamDto.getGroupId())){
                        params.getQueryParams().setLyy_equipment_group_id(new HashSet<String>(){{add(dayParamDto.getGroupId()+"");}});
                    }else{
                        //params.getQueryParams().setLyy_equipment_group_id(new HashSet<String>(){{add("-11111");}});
                        return null;
                    }
                }

            }


        }else{
            //主账户且选择了省份城市等
            if(filter){
                if(groupSet.size() > 0){
                    params.getQueryParams().setLyy_equipment_group_id(groupSet);
                }else{
                    //证明筛选了省份城市等，但是没有一个场地
                    return null;
                }
            }
        }

        try{
            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
            JSONObject result = oneDataServiceFeign.getRealTimeData(params,headers);
            log.info("{}",result.toJSONString());
            //获取昨天的会议情况
            if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
                JSONObject current = new JSONObject();
                if(result.containsKey("info") && result.getJSONObject("info") !=null){
                    if(result.getJSONObject("info").getJSONArray("list") != null && result.getJSONObject("info").getJSONArray("list").size() > 0){
                        current = result.getJSONObject("info").getJSONArray("list").getJSONObject(0);
                    }
                }

                //前一天
                try{
                    dayParamDto.setDay(TimeUtil.yesterday());
                    JSONObject lastTime = memberDataService.getMemberData(dayParamDto);
                    log.info("昨天数据：{}",lastTime.toJSONString());
                    if(lastTime !=null && lastTime.containsKey("code") && "200".equals(lastTime.getString("code"))){
                        if(lastTime.containsKey("info") && lastTime.getJSONObject("info") !=null){
                            JSONObject lastData = lastTime.getJSONObject("info");
                            if(lastData.getJSONArray("list") != null && lastData.getJSONArray("list").size() > 0){
                                JSONObject last = lastData.getJSONArray("list").getJSONObject(0);
                                if(last != null){
                                    current.put("totalMember",current.getIntValue("memberCounts")+last.getIntValue("memberCounts"));
                                    if(last.getIntValue("memberCounts")>0){
                                        DecimalFormat decimalFormat = new DecimalFormat("0.##");
                                        current.put("activeRate",decimalFormat.format(current.getDoubleValue("memberCounts")/last.getDoubleValue("memberCounts")));
                                    }
                                }
                            }
                        }
                    }
                }catch (Exception e){
                    log.warn("{}",e);
                }
            }
            return result;
        }catch (Exception e){
            log.warn("{}",e);
        }
        return null;
    }

    @Override
    public JSONObject getGroupData(DayParamDto dayParamDto) {
        MultiValueMap<String, String> headers = new HttpHeaders();
        headers.add("sk",sk);
        headers.add("ak",ak);

        RealTimeParamDto params = new RealTimeParamDto();
        params.setCode(realTimeOrderAnalyseCode);
        //params.setCode("d1si77f9");
        params.setGroupByColumnArray("T0.ad_org_id,T1.equipment_group_id");
        params.getQueryParams().setAd_org_id(dayParamDto.getMerchantId()+"");
        params.getRangeParams().getCreated().setStart(dayParamDto.getDay()+" 00:00:00");
        params.getRangeParams().getCreated().setEnd(dayParamDto.getDay()+" 23:59:59");

        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.RT_GROUP_DATA_COLUMN_ARRAY) ;
        params.setColumnArray(columnArray);

        params.getQueryParams().setProvinceId(dayParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(dayParamDto.getProvinceId()+"");}});
        params.getQueryParams().setCityId(dayParamDto.getCityId() == null ? null:new HashSet<String>(){{add(dayParamDto.getCityId()+"");}});
        params.getQueryParams().setDistrictId(dayParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(dayParamDto.getAreaId()+"");}});
        params.getQueryParams().setEquipment_group_id(dayParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(dayParamDto.getGroupId()+"");}});
        params.getQueryParams().setLyy_equipment_type_id(dayParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(dayParamDto.getEquipmentTypeId()+"");}});

        params.getPageParams().setPageNum(dayParamDto.getPageNum() == null ? null : (dayParamDto.getPageNum()-1)+"");

        try{
            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
            JSONObject result = oneDataServiceFeign.getRealTimeData(params,headers);
            log.info("{}",result.toJSONString());
            return result;
        }catch (Exception e){
            log.warn("{}",e);
        }

        return null;
    }

    private Set<String> getGroupId(DayParamDto dayParamDto,MultiValueMap<String, String> headers){
        RealTimeParamDto provinceParams = new RealTimeParamDto();
        Set<String> groupSet = new HashSet<>();

        provinceParams.setCode(provinceInfoCode);
        provinceParams.setColumnArray("distinct equipment_group_id");
        provinceParams.getQueryParams().setDistributor_id(dayParamDto.getMerchantId()+"");
        provinceParams.setFixedCondition(null);
        provinceParams.getQueryParams().setProvinceId(dayParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(dayParamDto.getProvinceId()+"");}});
        provinceParams.getQueryParams().setCityId(dayParamDto.getCityId() == null ? null: new HashSet<String>(){{add(dayParamDto.getCityId()+"");}});
        provinceParams.getQueryParams().setDistrictId(dayParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(dayParamDto.getAreaId()+"");}});
        if(dayParamDto.getGroupIds() != null && dayParamDto.getGroupIds().size()>0){
            provinceParams.getQueryParams().setEquipment_group_id(dayParamDto.getGroupIds());
        }
        log.info("特殊请求参数：{}",JSONObject.toJSON(provinceParams).toString());
        JSONObject result = oneDataServiceFeign.getRealTimeData(provinceParams,headers);
        log.info("特殊结果：{}",result.toJSONString());
        //如果获取到场地id赋值
        if(result != null && result.getInteger("code") == 200 && result.getJSONObject("info") != null && result.getJSONObject("info").getJSONArray("list") != null){
            JSONArray groupIds = result.getJSONObject("info").getJSONArray("list");
            if(groupIds != null && groupIds.size()>0){
                for(int i=0; i<groupIds.size(); i++){
                    groupSet.add(groupIds.getJSONObject(i).getString("equipment_group_id"));
                }
            }
        }
        return groupSet;
    }
}
