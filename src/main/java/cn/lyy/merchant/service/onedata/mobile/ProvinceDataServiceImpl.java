package cn.lyy.merchant.service.onedata.mobile;

import cn.lyy.merchant.controller.onedata.dto.DayParamDto;
import cn.lyy.merchant.controller.onedata.dto.MonthParamDto;
import cn.lyy.merchant.controller.onedata.dto.WeekParamDto;
import cn.lyy.merchant.controller.onedata.dto.YearParamDto;
import cn.lyy.merchant.service.onedata.FeignUtil;
import cn.lyy.merchant.service.onedata.ParamDto;
import cn.lyy.merchant.service.onedata.columns.ColumnArrayMap;
import cn.lyy.merchant.service.onedata.columns.ColumnArrayMapKey;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class ProvinceDataServiceImpl implements ProvinceDataService{

    @Autowired
    private FeignUtil feignUtil ;
    
    @Value("${oneData.dayEquipmentAnalyseCode}")
    private String dayEquipmentAnalyseCode;
    
    @Value("${oneData.weekEquipmentAnalyseCode}")
    private String weekEquipmentAnalyseCode;
    
    @Value("${oneData.monthEquipmentAnalyseCode}")
    private String monthEquipmentAnalyseCode;
    
    @Value("${oneData.yearEquipmentAnalyseCode}")
    private String yearEquipmentAnalyseCode;

    /**
     * 日报获取省份维度统计信息
     * @param dayParamDto
     * @return
     */
    public JSONObject getDayProvinceData(DayParamDto dayParamDto) {
    	ParamDto params = new ParamDto();
    	params.setCode(dayEquipmentAnalyseCode);
    	//params.setColumnArray(QuerySql.PROVINCE_COMMON_COLUMN_ARRAY);
    	String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.PROVINCE_COMMON_COLUMN_ARRAY) ;
    	params.setColumnArray(columnArray) ;
        params.setGroupByColumnArray("province_name");
        
        DayParamDto newDto = new DayParamDto() ;
    	newDto.setMerchantId(dayParamDto.getMerchantId());
    	newDto.setDay(dayParamDto.getDay());
        newDto.setPageNum(dayParamDto.getPageNum());
    	
        JSONObject provinceResult = feignUtil.getData(params, newDto);
        return provinceResult;
    }

    /**
     * 周报报获取省份维度统计信息
     * @param weekParamDto
     * @return
     */
    public JSONObject getWeekProvinceData(WeekParamDto weekParamDto) {
    	ParamDto params = new ParamDto();
    	params.setCode(weekEquipmentAnalyseCode);
    	//params.setColumnArray(QuerySql.PROVINCE_COMMON_COLUMN_ARRAY);
    	String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.PROVINCE_COMMON_COLUMN_ARRAY) ;
    	params.setColumnArray(columnArray) ;
        params.setGroupByColumnArray("province_name");
        
        WeekParamDto newDto = new WeekParamDto() ;
    	newDto.setMerchantId(weekParamDto.getMerchantId());
    	newDto.setMonth(weekParamDto.getMonth());
    	newDto.setWeek(weekParamDto.getWeek());
    	newDto.setPageNum(weekParamDto.getPageNum());
        
    	JSONObject provinceResult = feignUtil.getData(params, newDto);
        return provinceResult;
    }

    /**
     * 月报报获取省份维度统计信息
     * @param monthParamDto
     * @return
     */
    public JSONObject getMonthProvinceData(MonthParamDto monthParamDto) {
    	ParamDto params = new ParamDto();
    	params.setCode(monthEquipmentAnalyseCode);
    	//params.setColumnArray(QuerySql.PROVINCE_COMMON_COLUMN_ARRAY);
    	String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.PROVINCE_COMMON_COLUMN_ARRAY) ;
    	params.setColumnArray(columnArray) ;
        params.setGroupByColumnArray("province_name");
         
        MonthParamDto newDto = new MonthParamDto() ;
    	newDto.setMerchantId(monthParamDto.getMerchantId());
    	newDto.setYear(monthParamDto.getYear());
    	newDto.setMonth(monthParamDto.getMonth());
    	newDto.setPageNum(monthParamDto.getPageNum());
    	
    	JSONObject provinceResult = feignUtil.getData(params, newDto);
        return provinceResult;
    }

    /**
     * 年报报获取省份维度统计信息
     * @param yearParamDto
     * @return
     */
    public JSONObject getYearProvinceData(YearParamDto yearParamDto) {
    	ParamDto params = new ParamDto();
    	params.setCode(yearEquipmentAnalyseCode);
    	//params.setColumnArray(QuerySql.PROVINCE_COMMON_COLUMN_ARRAY);
    	String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.PROVINCE_COMMON_COLUMN_ARRAY) ;
    	params.setColumnArray(columnArray) ;
        params.setGroupByColumnArray("province_name");
        
        YearParamDto newDto = new YearParamDto() ;
    	newDto.setMerchantId(yearParamDto.getMerchantId());
    	newDto.setYear(yearParamDto.getYear());
        newDto.setPageNum(yearParamDto.getPageNum());
        
    	JSONObject provinceResult = feignUtil.getData(params, newDto);
        return provinceResult;
    }
    
    /**
     * 日报获取省份维度统计信息-底部平均值
     * @param dayParamDto
     * @return
     */
    public JSONObject getDayProvinceAvgData(DayParamDto dayParamDto) {
    	ParamDto params = new ParamDto();
    	params.setCode(dayEquipmentAnalyseCode);
    	//params.setColumnArray(QuerySql.PROVINCE_AVG_COLUMN_ARRAY);
    	String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.PROVINCE_AVG_COLUMN_ARRAY) ;
    	params.setColumnArray(columnArray) ;
    	
        DayParamDto newDto = new DayParamDto() ;
    	newDto.setMerchantId(dayParamDto.getMerchantId());
    	newDto.setDay(dayParamDto.getDay());
    	newDto.setPageNum(1);
        
        JSONObject provinceAvgResult = feignUtil.getData(params, newDto);
        return provinceAvgResult;
    }

    /**
     * 周报报获取省份维度统计信息-底部平均值
     * @param weekParamDto
     * @return
     */
    public JSONObject getWeekProvinceAvgData(WeekParamDto weekParamDto) {
    	ParamDto params = new ParamDto();
    	params.setCode(weekEquipmentAnalyseCode);
    	//params.setColumnArray(QuerySql.PROVINCE_AVG_COLUMN_ARRAY);
    	String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.PROVINCE_AVG_COLUMN_ARRAY) ;
    	params.setColumnArray(columnArray) ;
    	
        WeekParamDto newDto = new WeekParamDto() ;
    	newDto.setMerchantId(weekParamDto.getMerchantId());
    	newDto.setMonth(weekParamDto.getMonth());
    	newDto.setWeek(weekParamDto.getWeek());
    	newDto.setPageNum(1);
        
        JSONObject provinceAvgResult = feignUtil.getData(params, newDto);
        return provinceAvgResult;
    }

    /**
     * 月报报获取省份维度统计信息-底部平均值
     * @param monthParamDto
     * @return
     */
    public JSONObject getMonthProvinceAvgData(MonthParamDto monthParamDto) {
    	ParamDto params = new ParamDto();
    	params.setCode(monthEquipmentAnalyseCode);
    	//params.setColumnArray(QuerySql.PROVINCE_AVG_COLUMN_ARRAY);
    	String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.PROVINCE_AVG_COLUMN_ARRAY) ;
    	params.setColumnArray(columnArray) ;
    	
        MonthParamDto newDto = new MonthParamDto() ;
    	newDto.setMerchantId(monthParamDto.getMerchantId());
    	newDto.setYear(monthParamDto.getYear());
    	newDto.setMonth(monthParamDto.getMonth());
    	newDto.setPageNum(1);

        JSONObject provinceAvgResult = feignUtil.getData(params, newDto);
        return provinceAvgResult;
    }

    /**
     * 年报报获取省份维度统计信息-底部平均值
     * @param yearParamDto
     * @return
     */
    public JSONObject getYearProvinceAvgData(YearParamDto yearParamDto) {
    	ParamDto params = new ParamDto();
    	params.setCode(yearEquipmentAnalyseCode);
    	//params.setColumnArray(QuerySql.PROVINCE_AVG_COLUMN_ARRAY);
    	String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.PROVINCE_AVG_COLUMN_ARRAY) ;
    	params.setColumnArray(columnArray) ;
    	
        YearParamDto newDto = new YearParamDto() ;
    	newDto.setMerchantId(yearParamDto.getMerchantId());
    	newDto.setYear(yearParamDto.getYear());
    	newDto.setPageNum(1);
        
        JSONObject provinceAvgResult = feignUtil.getData(params, newDto);
        return provinceAvgResult;
    }

//    @Override
//    public JSONObject getProvince(DayParamDto dayParamDto) {
//        ParamDto params = new ParamDto();
//        params.setCode(groupBaseInfoCode);
//        params.setColumnArray("distinct province_id , province_name ");
//
//        params.getPageParams().setPageRow("100");
//        params.getQueryParams().setDistributor_id(dayParamDto.getMerchantId() == null ? null: dayParamDto.getMerchantId()+"");
//
//        if(dayParamDto.getGroupIds() != null && dayParamDto.getGroupIds().size()>0){
//            params.getQueryParams().setEquipment_group_id(dayParamDto.getGroupIds());
//            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
//            params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
//        }
//        try{
//            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
//            JSONObject result = oneDataServiceFeign.getData(params,headers);
//            log.info("{}",result.toJSONString());
//            if(result != null && "200".equals(result.getString("code")) && result.getJSONObject("info") != null){
//                JSONArray jsonArray = result.getJSONObject("info").getJSONArray("list");
//                Integer removeIndex = null;
//                if(jsonArray != null && jsonArray.size() > 0){
//                    //设置大小
//                    result.getJSONObject("info").remove("totalPage");
//                    result.getJSONObject("info").remove("totalCount");
//                    for(int i=0; i<jsonArray.size(); i++){
//                        JSONObject jsonObject = jsonArray.getJSONObject(i);
//                        if(jsonObject.getString("province_id")==null || jsonObject.getString("province_id").trim().isEmpty()){
//                            removeIndex = i;
//                        }else{
//                            jsonObject.put("provinceId",jsonObject.getIntValue("province_id"));
//                            jsonObject.put("provinceName",jsonObject.getString("province_name"));
//                            jsonObject.remove("province_id");
//                            jsonObject.remove("province_name");
//                        }
//                    }
//                    if(removeIndex !=null){
//                        jsonArray.remove(jsonArray.getJSONObject(removeIndex));
//                    }
//                }
//            }
//            return result;
//        }catch (Exception e){
//            log.warn("{}",e);
//        }
//
//        return null;
//    }
   
}
