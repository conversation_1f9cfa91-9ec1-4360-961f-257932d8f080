package cn.lyy.merchant.service.onedata.columns.pg;

public class PayTypeColumnArray {
	
	public static String DAY_PAY_TYPE_COLUMN_ARRAY = 
			" cast(pay_amount_yesterday as decimal(15,2)) as \"preRangePayAmount\"," +
            "cast(pay_amount as decimal(15,2)) as \"payAmount\", " +
            "pay_count as \"payCount\"," +
            "cast(per_order_amount as decimal(15,2)) as \"perOrderAmount\", " +
            "pay_type as \"payType\", " +
            "day, " +
            "cast(case when pay_amount_yesterday =0 and pay_amount>0 then 1 when pay_amount_yesterday=0 and pay_amount=0 then 0 else (pay_amount-pay_amount_yesterday)::numeric/pay_amount_yesterday::numeric end as decimal(15,2)) as up" ;
    
	
	public static String WEEK_PAY_TYPE_COLUMN_ARRAY = 
			" distinct cast(pay_amount_last_week as decimal(15,2)) as \"preRangePayAmount\"," +
            "cast(pay_amount as decimal(15,2)) as \"payAmount\", " +
            "pay_count as \"payCount\"," +
            "cast(per_order_amount as decimal(15,2)) as \"perOrderAmount\", " +
            "pay_type as \"payType\", " +
            "year_month as \"month\", " +
            "month_week_num as \"week\", " +
            "cast(case when pay_amount_last_week =0 and pay_amount>0 then 1 when pay_amount_last_week=0 and pay_amount=0 then 0 else (pay_amount-pay_amount_last_week)::numeric/pay_amount_last_week::numeric end as decimal(15,2)) as up" ;
    
	
	public static String MONTH_PAY_TYPE_COLUMN_ARRAY = "distinct cast(pay_amount_last_month as decimal(15,2)) as \"preRangePayAmount\"," +
            " cast(pay_amount as decimal(15,2)) as \"payAmount\", " +
            "pay_count as \"payCount\"," +
            "cast(per_order_amount as decimal(15,2)) as \"perOrderAmount\", " +
            "pay_type as \"payType\", " +
            "year_month as \"month\", " +
            "cast(case when pay_amount_last_month =0 and pay_amount>0 then 1 when pay_amount_last_month=0 and pay_amount=0 then 0 else (pay_amount-pay_amount_last_month)::numeric/pay_amount_last_month::numeric end as decimal(15,2)) as up" ;
	
	public static String YEAR_PAY_TYPE_COLUMN_ARRAY = 
			" distinct cast(pay_amount_last_year as decimal(15,2)) as \"preRangePayAmount\"," +
            "cast(pay_amount as decimal(15,2)) as \"payAmount\", " +
            "pay_count as \"payCount\"," +
            "cast(per_order_amount as decimal(15,2)) as \"perOrderAmount\", " +
            "pay_type as \"payType\", " +
            "year_str as \"year\", " +
            "cast(case when pay_amount_last_year =0 and pay_amount>0 then 1 when pay_amount_last_year=0 and pay_amount=0 then 0 else (pay_amount-pay_amount_last_year)::numeric/pay_amount_last_year::numeric end as decimal(15,2)) as up" ;

}
