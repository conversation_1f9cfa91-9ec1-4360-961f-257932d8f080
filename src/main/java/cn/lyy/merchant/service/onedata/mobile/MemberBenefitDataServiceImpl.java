package cn.lyy.merchant.service.onedata.mobile;

import java.util.HashSet;
import java.util.TreeSet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.alibaba.fastjson.JSONObject;

import cn.lyy.base.util.StringUtil;
import cn.lyy.merchant.controller.onedata.dto.DayParamDto;
import cn.lyy.merchant.controller.onedata.dto.MonthParamDto;
import cn.lyy.merchant.controller.onedata.dto.WeekParamDto;
import cn.lyy.merchant.controller.onedata.dto.YearParamDto;
import cn.lyy.merchant.service.onedata.FeignUtil;
import cn.lyy.merchant.service.onedata.ParamDto;
import cn.lyy.merchant.service.onedata.columns.ColumnArrayMap;
import cn.lyy.merchant.service.onedata.columns.ColumnArrayMapKey;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class MemberBenefitDataServiceImpl implements MemberBenefitDataService{

	@Autowired
	private FeignUtil feignUtil ;
	
    @Value("${oneData.dayMemberBenefitDataCode}")
    private String dayMemberBenefitDataCode;
    @Value("${oneData.weekMemberBenefitDataCode}")
    private String weekMemberBenefitDataCode;
    @Value("${oneData.monthMemberBenefitDataCode}")
    private String monthMemberBenefitDataCode;
    @Value("${oneData.yearMemberBenefitDataCode}")
    private String yearMemberBenefitDataCode;

	
	/**
     * 日报会员储值数据
     * @param dayParamDto
     * @return
     */
    @SuppressWarnings({ "serial", "rawtypes" })
    @Override
    public JSONObject getMemberBenefitData(DayParamDto dayParamDto) {
        ParamDto params = new ParamDto();
        params.setCode(dayMemberBenefitDataCode);

        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.DAY_MEMBER_BENEFIT_COLUMN_ARRAY) ;
	    params.setColumnArray(columnArray) ;


        params.getQueryParams().setDay(dayParamDto.getDay());
        params.getQueryParams().setMerchant_id(dayParamDto.getMerchantId()+"");
        params.getQueryParams().setBenefit_kind(dayParamDto.getBenefitType());
        params.getQueryParams().setCombo_type(dayParamDto.getComboType());  //因为会只到商家维度

        params.getQueryParams().setProvince_id(dayParamDto.getProvinceId() == null ? null: new HashSet<String>(){/**
			 * 
			 */
			private static final long serialVersionUID = -8566159728611866048L;

		{add(dayParamDto.getProvinceId()+"");}});
        params.getQueryParams().setCity_id(dayParamDto.getCityId() == null ? null:new HashSet<String>(){{add(dayParamDto.getCityId()+"");}});
        params.getQueryParams().setDistrict_id(dayParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(dayParamDto.getAreaId()+"");}});
        params.getQueryParams().setEquipment_group_id(dayParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(dayParamDto.getGroupId()+"");}});
        params.getQueryParams().setLyy_equipment_type_id(dayParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){/**
			 * 
			 */
			private static final long serialVersionUID = -957780210003031353L;

		{add(dayParamDto.getEquipmentTypeId()+"");}});

        if(dayParamDto.getGroupIds() != null && dayParamDto.getGroupIds().size()>0){
            if(dayParamDto.getGroupId() == null || !dayParamDto.getGroupIds().contains(dayParamDto.getGroupId()+"")){
                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(dayParamDto.getGroupIds())).first()+"");}});
            }
            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
            //params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
        }
        params.resetComboType();

        try{
            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
            JSONObject result = feignUtil.getData(params);
            log.info("{}",result.toJSONString());
            return result;
        }catch (Exception e){
            log.warn("{}",e);
        }

        return null;
    }


    /**
     * 周报会员储值数据
     * @param weekParamDto
     * @return
     */
    @SuppressWarnings({ "serial", "rawtypes" })
    @Override
    public JSONObject getWeekMemberBenefitData(WeekParamDto weekParamDto) {
	       ParamDto params = new ParamDto();
	       params.setCode(weekMemberBenefitDataCode);
	
	       String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.WEEK_MEMBER_BENEFIT_COLUMN_ARRAY) ;
	       params.setColumnArray(columnArray) ;
	
	       params.getQueryParams().setBenefit_kind(weekParamDto.getBenefitType());
	       params.getQueryParams().setMerchant_id(weekParamDto.getMerchantId()+"");
	       params.getQueryParams().setYear_month(weekParamDto.getMonth());
	       params.getQueryParams().setMonth_week_num(weekParamDto.getWeek()+"");
	       params.getQueryParams().setCombo_type(weekParamDto.getComboType());
	
	       params.getQueryParams().setProvince_id(weekParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(weekParamDto.getProvinceId()+"");}});
	       params.getQueryParams().setCity_id(weekParamDto.getCityId() == null ? null:new HashSet<String>(){{add(weekParamDto.getCityId()+"");}});
	       params.getQueryParams().setDistrict_id(weekParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(weekParamDto.getAreaId()+"");}});
	       params.getQueryParams().setEquipment_group_id(weekParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(weekParamDto.getGroupId()+"");}});
	       params.getQueryParams().setLyy_equipment_type_id(weekParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(weekParamDto.getEquipmentTypeId()+"");}});
	
	       if(weekParamDto.getGroupIds() != null && weekParamDto.getGroupIds().size()>0){
	           if(weekParamDto.getGroupId() == null || !weekParamDto.getGroupIds().contains(weekParamDto.getGroupId()+"")){
	               params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(weekParamDto.getGroupIds())).first()+"");}});
	           }
	           params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
	           //params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
	       }
	       params.resetComboType();
	
	       try{
	           log.info("请求参数：{}",JSONObject.toJSON(params).toString());
	           JSONObject result = feignUtil.getData(params);
	           log.info("{}",result.toJSONString());
	           return result;
	       }catch (Exception e){
	           log.warn("{}",e);
	       }
	
	       return null;
    }

    /**
     * 月报会员储值数据
     * @param monthParamDto
     * @return
     */
    @SuppressWarnings({ "serial", "rawtypes" })
    @Override
	public JSONObject getMonthMemberBenefitData(MonthParamDto monthParamDto) {
	      ParamDto params = new ParamDto();
	      params.setCode(monthMemberBenefitDataCode);
	
	      String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.MONTH_MEMBER_BENEFIT_COLUMN_ARRAY) ;
	      params.setColumnArray(columnArray) ;
	
	      params.getQueryParams().setBenefit_kind(monthParamDto.getBenefitType());
	      params.getQueryParams().setMerchant_id(monthParamDto.getMerchantId()+"");
	      params.getQueryParams().setYear_month(monthParamDto.getMonth());
	      params.getQueryParams().setCombo_type(monthParamDto.getComboType());
	
	      params.getQueryParams().setProvince_id(monthParamDto.getProvinceId() == null ? null: new HashSet<String>(){{add(monthParamDto.getProvinceId()+"");}});
	      params.getQueryParams().setCity_id(monthParamDto.getCityId() == null ? null:new HashSet<String>(){{add(monthParamDto.getCityId()+"");}});
	      params.getQueryParams().setDistrict_id(monthParamDto.getAreaId() == null ? null: new HashSet<String>(){{add(monthParamDto.getAreaId()+"");}});
	      params.getQueryParams().setEquipment_group_id(monthParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(monthParamDto.getGroupId()+"");}});
	      params.getQueryParams().setLyy_equipment_type_id(monthParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(monthParamDto.getEquipmentTypeId()+"");}});
	
	      if(monthParamDto.getGroupIds() != null && monthParamDto.getGroupIds().size()>0){
	          if(monthParamDto.getGroupId() == null || !monthParamDto.getGroupIds().contains(monthParamDto.getGroupId()+"")){
	              params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(monthParamDto.getGroupIds())).first()+"");}});
	          }
	          params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
	          //params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
	      }
	      params.resetComboType();
	
	      try{
	          log.info("请求参数：{}",JSONObject.toJSON(params).toString());
	          JSONObject result = feignUtil.getData(params);
	          log.info("{}",result.toJSONString());
	          return result;
	      }catch (Exception e){
	          log.warn("{}",e);
	      }
	
	      return null;
    }

    
    /**
     * 年报会员储值数据
     * @param yearParamDto
     * @return
     */
    @SuppressWarnings({ "serial", "rawtypes" })
    @Override
	public JSONObject getYearMemberBenefitData(YearParamDto yearParamDto) {
        ParamDto params = new ParamDto();
        params.setCode(yearMemberBenefitDataCode);

        String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.YEAR_MEMBER_BENEFIT_COLUMN_ARRAY) ;
	    params.setColumnArray(columnArray) ;

        params.getQueryParams().setBenefit_kind(yearParamDto.getBenefitType());
        params.getQueryParams().setMerchant_id(yearParamDto.getMerchantId()+"");
        params.getQueryParams().setYear_str(yearParamDto.getYear()+"");
        params.getQueryParams().setCombo_type(yearParamDto.getComboType());

        params.getQueryParams().setProvince_id(yearParamDto.getProvinceId() == null ? null: new HashSet<String>(){/**
			 * 
			 */
			private static final long serialVersionUID = -1030765234289503591L;

		{add(yearParamDto.getProvinceId()+"");}});
        params.getQueryParams().setCity_id(yearParamDto.getCityId() == null ? null:new HashSet<String>(){{add(yearParamDto.getCityId()+"");}});
        params.getQueryParams().setDistrict_id(yearParamDto.getAreaId() == null ? null: new HashSet<String>(){/**
			 * 
			 */
			private static final long serialVersionUID = 9048062886070748321L;

		{add(yearParamDto.getAreaId()+"");}});
        params.getQueryParams().setEquipment_group_id(yearParamDto.getGroupId() == null ? null: new HashSet<String>(){{add(yearParamDto.getGroupId()+"");}});
        params.getQueryParams().setLyy_equipment_type_id(yearParamDto.getEquipmentTypeId() == null ? null: new HashSet<String>(){{add(yearParamDto.getEquipmentTypeId()+"");}});

        if(yearParamDto.getGroupIds() != null && yearParamDto.getGroupIds().size()>0){
            if(yearParamDto.getGroupId() == null || !yearParamDto.getGroupIds().contains(yearParamDto.getGroupId()+"")){
                params.getQueryParams().setEquipment_group_id(new HashSet<String>(){{add(((TreeSet)(yearParamDto.getGroupIds())).first()+"");}});
            }
            params.getOrderParams().put("merchant_id",params.buildOrderFile("asc"));
            //params.getOrderParams().put("equipment_group_id",params.buildOrderFile("asc"));
        }
        params.resetComboType();

        try{
            log.info("请求参数：{}",JSONObject.toJSON(params).toString());
            JSONObject result = feignUtil.getData(params);
            log.info("{}",result.toJSONString());
            return result;
        }catch (Exception e){
            log.warn("{}",e);
        }

        return null;
    }
    
}
