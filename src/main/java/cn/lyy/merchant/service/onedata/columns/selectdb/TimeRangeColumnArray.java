package cn.lyy.merchant.service.onedata.columns.selectdb;

public class TimeRangeColumnArray {
	
	public static final String DAY_TIME_RANGE_COLUMN_ARRAY = "merchant_id as `merchantId`, " +
            "cast(pay_amount_0_1 as decimal(15,2)) as `amount_0_1`, " +
            "cast(pay_amount_1_2 as decimal(15,2)) as `amount_1_2`, " +
            "cast(pay_amount_2_3 as decimal(15,2)) as `amount_2_3`, " +
            "cast(pay_amount_3_4 as decimal(15,2)) as `amount_3_4`, " +
            "cast(pay_amount_4_5 as decimal(15,2)) as `amount_4_5`, " +
            "cast(pay_amount_5_6 as decimal(15,2)) as `amount_5_6`, " +
            "cast(pay_amount_6_7 as decimal(15,2)) as `amount_6_7`, " +
            "cast(pay_amount_7_8 as decimal(15,2)) as `amount_7_8`, " +
            "cast(pay_amount_8_9 as decimal(15,2)) as `amount_8_9`, " +
            "cast(pay_amount_9_10 as decimal(15,2)) as `amount_9_10`, " +
            "cast(pay_amount_10_11 as decimal(15,2)) as `amount_10_11`, " +
            "cast(pay_amount_11_12 as decimal(15,2)) as `amount_11_12`, " +
            "cast(pay_amount_12_13 as decimal(15,2)) as `amount_12_13`, " +
            "cast(pay_amount_13_14 as decimal(15,2)) as `amount_13_14`, " +
            "cast(pay_amount_14_15 as decimal(15,2)) as `amount_14_15`, " +
            "cast(pay_amount_15_16 as decimal(15,2)) as `amount_15_16`, " +
            "cast(pay_amount_16_17 as decimal(15,2)) as `amount_16_17`, " +
            "cast(pay_amount_17_18 as decimal(15,2)) as `amount_17_18`, " +
            "cast(pay_amount_18_19 as decimal(15,2)) as `amount_18_19`, " +
            "cast(pay_amount_19_20 as decimal(15,2)) as `amount_19_20`, " +
            "cast(pay_amount_20_21 as decimal(15,2)) as `amount_20_21`, " +
            "cast(pay_amount_21_22 as decimal(15,2)) as `amount_21_22`, " +
            "cast(pay_amount_22_23 as decimal(15,2)) as `amount_22_23`, " +
            "cast(pay_amount_23_24 as decimal(15,2)) as `amount_23_24`, " +

            "pay_count_0_1 as `count_0_1`, " +
            "pay_count_1_2 as `count_1_2`, " +
            "pay_count_2_3 as `count_2_3`, " +
            "pay_count_3_4 as `count_3_4`, " +
            "pay_count_4_5 as `count_4_5`, " +
            "pay_count_5_6 as `count_5_6`, " +
            "pay_count_6_7 as `count_6_7`, " +
            "pay_count_7_8 as `count_7_8`, " +
            "pay_count_8_9 as `count_8_9`, " +
            "pay_count_9_10 as `count_9_10`, " +
            "pay_count_10_11 as `count_10_11`, " +
            "pay_count_11_12 as `count_11_12`, " +
            "pay_count_12_13 as `count_12_13`, " +
            "pay_count_13_14 as `count_13_14`, " +
            "pay_count_14_15 as `count_14_15`, " +
            "pay_count_15_16 as `count_15_16`, " +
            "pay_count_16_17 as `count_16_17`, " +
            "pay_count_17_18 as `count_17_18`, " +
            "pay_count_18_19 as `count_18_19`, " +
            "pay_count_19_20 as `count_19_20`, " +
            "pay_count_20_21 as `count_20_21`, " +
            "pay_count_21_22 as `count_21_22`, " +
            "pay_count_22_23 as `count_22_23`, " +
            "pay_count_23_24 as `count_23_24`, " +


            "case when pay_count_0_1=0 then 0 else cast(pay_amount_0_1/pay_count_0_1 as decimal(15,2)) end as `perOrderPrice_0_1`, " +
            "case when pay_count_1_2=0 then 0 else cast(pay_amount_1_2/pay_count_1_2 as decimal(15,2)) end as `perOrderPrice_1_2`, " +
            "case when pay_count_2_3=0 then 0 else cast(pay_amount_2_3/pay_count_2_3 as decimal(15,2)) end as `perOrderPrice_2_3`, " +
            "case when pay_count_3_4=0 then 0 else cast(pay_amount_3_4/pay_count_3_4 as decimal(15,2)) end as `perOrderPrice_3_4`, " +
            "case when pay_count_4_5=0 then 0 else cast(pay_amount_4_5/pay_count_4_5 as decimal(15,2)) end as `perOrderPrice_4_5`, " +
            "case when pay_count_5_6=0 then 0 else cast(pay_amount_5_6/pay_count_5_6 as decimal(15,2)) end as `perOrderPrice_5_6`, " +
            "case when pay_count_6_7=0 then 0 else cast(pay_amount_6_7/pay_count_6_7 as decimal(15,2)) end as `perOrderPrice_6_7`, " +
            "case when pay_count_7_8=0 then 0 else cast(pay_amount_7_8/pay_count_7_8 as decimal(15,2)) end as `perOrderPrice_7_8`, " +
            "case when pay_count_8_9=0 then 0 else cast(pay_amount_8_9/pay_count_8_9 as decimal(15,2)) end as `perOrderPrice_8_9`, " +
            "case when pay_count_9_10=0 then 0 else cast(pay_amount_9_10/pay_count_9_10 as decimal(15,2)) end as `perOrderPrice_9_10`, " +
            "case when pay_count_10_11=0 then 0 else cast(pay_amount_10_11/pay_count_10_11 as decimal(15,2)) end as `perOrderPrice_10_11`, " +
            "case when pay_count_11_12=0 then 0 else cast(pay_amount_11_12/pay_count_11_12 as decimal(15,2)) end as `perOrderPrice_11_12`, " +
            "case when pay_count_12_13=0 then 0 else cast(pay_amount_12_13/pay_count_12_13 as decimal(15,2)) end as `perOrderPrice_12_13`, " +
            "case when pay_count_13_14=0 then 0 else cast(pay_amount_13_14/pay_count_13_14 as decimal(15,2)) end as `perOrderPrice_13_14`, " +
            "case when pay_count_14_15=0 then 0 else cast(pay_amount_14_15/pay_count_14_15 as decimal(15,2)) end as `perOrderPrice_14_15`, " +
            "case when pay_count_15_16=0 then 0 else cast(pay_amount_15_16/pay_count_15_16 as decimal(15,2)) end as `perOrderPrice_15_16`, " +
            "case when pay_count_16_17=0 then 0 else cast(pay_amount_16_17/pay_count_16_17 as decimal(15,2)) end as `perOrderPrice_16_17`, " +
            "case when pay_count_17_18=0 then 0 else cast(pay_amount_17_18/pay_count_17_18 as decimal(15,2)) end as `perOrderPrice_17_18`, " +
            "case when pay_count_18_19=0 then 0 else cast(pay_amount_18_19/pay_count_18_19 as decimal(15,2)) end as `perOrderPrice_18_19`, " +
            "case when pay_count_19_20=0 then 0 else cast(pay_amount_19_20/pay_count_19_20 as decimal(15,2)) end as `perOrderPrice_19_20`, " +
            "case when pay_count_20_21=0 then 0 else cast(pay_amount_20_21/pay_count_20_21 as decimal(15,2)) end as `perOrderPrice_20_21`, " +
            "case when pay_count_21_22=0 then 0 else cast(pay_amount_21_22/pay_count_21_22 as decimal(15,2)) end as `perOrderPrice_21_22`, " +
            "case when pay_count_22_23=0 then 0 else cast(pay_amount_22_23/pay_count_22_23 as decimal(15,2)) end as `perOrderPrice_22_23`, " +
            "case when pay_count_23_24=0 then 0 else cast(pay_amount_23_24/pay_count_23_24 as decimal(15,2)) end as `perOrderPrice_23_24`, " +

            "day as `day` " ;
	
	public static final String WEEK_TIME_RANGE_COLUMN_ARRAY = "merchant_id as `merchantId`, " +
            "monday_pay_count as `mondayPayCount`, " +
            "cast(monday_pay_amount as decimal(15,2)) as `mondayPayAmount`, " +
            "case when monday_pay_count =0 then 0 else cast(monday_pay_amount/monday_pay_count as decimal(15,2)) end as `mondayPerOrderAmount`, " +

            "tuesday_pay_count as `tuesdayPayCount`, " +
            "cast(tuesday_pay_amount as decimal(15,2)) as `tuesdayPayAmount`, " +
            "case when tuesday_pay_count =0 then 0 else cast(tuesday_pay_amount/tuesday_pay_count as decimal(15,2)) end as `tuesdayPerOrderAmount`, " +

            "wednesday_pay_count as `wednesdayPayCount`, " +
            "cast(wednesday_pay_amount as decimal(15,2)) as `wednesdayPayAmount`, " +
            "case when wednesday_pay_count =0 then 0 else cast(wednesday_pay_amount/wednesday_pay_count as decimal(15,2)) end as `wednesdayPerOrderAmount`, " +

            "thursday_pay_count as `thursdayPayCount`, " +
            "cast(thursday_pay_amount as decimal(15,2)) as `thursdayPayAmount`, " +
            "case when thursday_pay_count =0 then 0 else cast(thursday_pay_amount/thursday_pay_count as decimal(15,2)) end as `thursdayPerOrderAmount`, " +

            "friday_pay_count as `fridayPayCount`, " +
            "cast(friday_pay_amount as decimal(15,2)) as `fridayPayAmount`, " +
            "case when friday_pay_count =0 then 0 else cast(friday_pay_amount/friday_pay_count as decimal(15,2)) end as `fridayPerOrderAmount`, " +

            "saturday_pay_count as `saturdayPayCount`, " +
            "cast(saturday_pay_amount as decimal(15,2)) as `saturdayPayAmount`, " +
            "case when saturday_pay_count =0 then 0 else cast(saturday_pay_amount/saturday_pay_count as decimal(15,2)) end as `saturdayPerOrderAmount`, " +

            "sunday_pay_count as `sundayPayCount`, " +
            "cast(sunday_pay_amount as decimal(15,2)) as `sundayPayAmount`, " +
            "case when sunday_pay_count =0 then 0 else cast(sunday_pay_amount/sunday_pay_count as decimal(15,2)) end as `sundayPerOrderAmount`, " +

            "cast(pay_amount_0_1 as decimal(15,2)) as `amount_0_1`, " +
            "cast(pay_amount_1_2 as decimal(15,2)) as `amount_1_2`, " +
            "cast(pay_amount_2_3 as decimal(15,2)) as `amount_2_3`, " +
            "cast(pay_amount_3_4 as decimal(15,2)) as `amount_3_4`, " +
            "cast(pay_amount_4_5 as decimal(15,2)) as `amount_4_5`, " +
            "cast(pay_amount_5_6 as decimal(15,2)) as `amount_5_6`, " +
            "cast(pay_amount_6_7 as decimal(15,2)) as `amount_6_7`, " +
            "cast(pay_amount_7_8 as decimal(15,2)) as `amount_7_8`, " +
            "cast(pay_amount_8_9 as decimal(15,2)) as `amount_8_9`, " +
            "cast(pay_amount_9_10 as decimal(15,2)) as `amount_9_10`, " +
            "cast(pay_amount_10_11 as decimal(15,2)) as `amount_10_11`, " +
            "cast(pay_amount_11_12 as decimal(15,2)) as `amount_11_12`, " +
            "cast(pay_amount_12_13 as decimal(15,2)) as `amount_12_13`, " +
            "cast(pay_amount_13_14 as decimal(15,2)) as `amount_13_14`, " +
            "cast(pay_amount_14_15 as decimal(15,2)) as `amount_14_15`, " +
            "cast(pay_amount_15_16 as decimal(15,2)) as `amount_15_16`, " +
            "cast(pay_amount_16_17 as decimal(15,2)) as `amount_16_17`, " +
            "cast(pay_amount_17_18 as decimal(15,2)) as `amount_17_18`, " +
            "cast(pay_amount_18_19 as decimal(15,2)) as `amount_18_19`, " +
            "cast(pay_amount_19_20 as decimal(15,2)) as `amount_19_20`, " +
            "cast(pay_amount_20_21 as decimal(15,2)) as `amount_20_21`, " +
            "cast(pay_amount_21_22 as decimal(15,2)) as `amount_21_22`, " +
            "cast(pay_amount_22_23 as decimal(15,2)) as `amount_22_23`, " +
            "cast(pay_amount_23_24 as decimal(15,2)) as `amount_23_24`, " +

            "pay_count_0_1 as `count_0_1`, " +
            "pay_count_1_2 as `count_1_2`, " +
            "pay_count_2_3 as `count_2_3`, " +
            "pay_count_3_4 as `count_3_4`, " +
            "pay_count_4_5 as `count_4_5`, " +
            "pay_count_5_6 as `count_5_6`, " +
            "pay_count_6_7 as `count_6_7`, " +
            "pay_count_7_8 as `count_7_8`, " +
            "pay_count_8_9 as `count_8_9`, " +
            "pay_count_9_10 as `count_9_10`, " +
            "pay_count_10_11 as `count_10_11`, " +
            "pay_count_11_12 as `count_11_12`, " +
            "pay_count_12_13 as `count_12_13`, " +
            "pay_count_13_14 as `count_13_14`, " +
            "pay_count_14_15 as `count_14_15`, " +
            "pay_count_15_16 as `count_15_16`, " +
            "pay_count_16_17 as `count_16_17`, " +
            "pay_count_17_18 as `count_17_18`, " +
            "pay_count_18_19 as `count_18_19`, " +
            "pay_count_19_20 as `count_19_20`, " +
            "pay_count_20_21 as `count_20_21`, " +
            "pay_count_21_22 as `count_21_22`, " +
            "pay_count_22_23 as `count_22_23`, " +
            "pay_count_23_24 as `count_23_24`, " +


            "case when pay_count_0_1=0 then 0 else cast(pay_amount_0_1/pay_count_0_1 as decimal(15,2)) end as `perOrderPrice_0_1`, " +
            "case when pay_count_1_2=0 then 0 else cast(pay_amount_1_2/pay_count_1_2 as decimal(15,2)) end as `perOrderPrice_1_2`, " +
            "case when pay_count_2_3=0 then 0 else cast(pay_amount_2_3/pay_count_2_3 as decimal(15,2)) end as `perOrderPrice_2_3`, " +
            "case when pay_count_3_4=0 then 0 else cast(pay_amount_3_4/pay_count_3_4 as decimal(15,2)) end as `perOrderPrice_3_4`, " +
            "case when pay_count_4_5=0 then 0 else cast(pay_amount_4_5/pay_count_4_5 as decimal(15,2)) end as `perOrderPrice_4_5`, " +
            "case when pay_count_5_6=0 then 0 else cast(pay_amount_5_6/pay_count_5_6 as decimal(15,2)) end as `perOrderPrice_5_6`, " +
            "case when pay_count_6_7=0 then 0 else cast(pay_amount_6_7/pay_count_6_7 as decimal(15,2)) end as `perOrderPrice_6_7`, " +
            "case when pay_count_7_8=0 then 0 else cast(pay_amount_7_8/pay_count_7_8 as decimal(15,2)) end as `perOrderPrice_7_8`, " +
            "case when pay_count_8_9=0 then 0 else cast(pay_amount_8_9/pay_count_8_9 as decimal(15,2)) end as `perOrderPrice_8_9`, " +
            "case when pay_count_9_10=0 then 0 else cast(pay_amount_9_10/pay_count_9_10 as decimal(15,2)) end as `perOrderPrice_9_10`, " +
            "case when pay_count_10_11=0 then 0 else cast(pay_amount_10_11/pay_count_10_11 as decimal(15,2)) end as `perOrderPrice_10_11`, " +
            "case when pay_count_11_12=0 then 0 else cast(pay_amount_11_12/pay_count_11_12 as decimal(15,2)) end as `perOrderPrice_11_12`, " +
            "case when pay_count_12_13=0 then 0 else cast(pay_amount_12_13/pay_count_12_13 as decimal(15,2)) end as `perOrderPrice_12_13`, " +
            "case when pay_count_13_14=0 then 0 else cast(pay_amount_13_14/pay_count_13_14 as decimal(15,2)) end as `perOrderPrice_13_14`, " +
            "case when pay_count_14_15=0 then 0 else cast(pay_amount_14_15/pay_count_14_15 as decimal(15,2)) end as `perOrderPrice_14_15`, " +
            "case when pay_count_15_16=0 then 0 else cast(pay_amount_15_16/pay_count_15_16 as decimal(15,2)) end as `perOrderPrice_15_16`, " +
            "case when pay_count_16_17=0 then 0 else cast(pay_amount_16_17/pay_count_16_17 as decimal(15,2)) end as `perOrderPrice_16_17`, " +
            "case when pay_count_17_18=0 then 0 else cast(pay_amount_17_18/pay_count_17_18 as decimal(15,2)) end as `perOrderPrice_17_18`, " +
            "case when pay_count_18_19=0 then 0 else cast(pay_amount_18_19/pay_count_18_19 as decimal(15,2)) end as `perOrderPrice_18_19`, " +
            "case when pay_count_19_20=0 then 0 else cast(pay_amount_19_20/pay_count_19_20 as decimal(15,2)) end as `perOrderPrice_19_20`, " +
            "case when pay_count_20_21=0 then 0 else cast(pay_amount_20_21/pay_count_20_21 as decimal(15,2)) end as `perOrderPrice_20_21`, " +
            "case when pay_count_21_22=0 then 0 else cast(pay_amount_21_22/pay_count_21_22 as decimal(15,2)) end as `perOrderPrice_21_22`, " +
            "case when pay_count_22_23=0 then 0 else cast(pay_amount_22_23/pay_count_22_23 as decimal(15,2)) end as `perOrderPrice_22_23`, " +
            "case when pay_count_23_24=0 then 0 else cast(pay_amount_23_24/pay_count_23_24 as decimal(15,2)) end as `perOrderPrice_23_24`, " +

            "year_month as `month`,"+
            "month_week_num as `week`" ;
	
	public static final String MONTH_TIME_RANGE_COLUMN_ARRAY = "merchant_id as `merchantId`, " +
            "monday_pay_count as `mondayPayCount`, " +
            "cast(monday_pay_amount as decimal(15,2)) as `mondayPayAmount`, " +
            "case when monday_pay_count =0 then 0 else cast(monday_pay_amount/monday_pay_count as decimal(15,2)) end as `mondayPerOrderAmount`, " +

            "tuesday_pay_count as `tuesdayPayCount`, " +
            "cast(tuesday_pay_amount as decimal(15,2)) as `tuesdayPayAmount`, " +
            "case when tuesday_pay_count =0 then 0 else cast(tuesday_pay_amount/tuesday_pay_count as decimal(15,2)) end as `tuesdayPerOrderAmount`, " +

            "wednesday_pay_count as `wednesdayPayCount`, " +
            "cast(wednesday_pay_amount as decimal(15,2)) as `wednesdayPayAmount`, " +
            "case when wednesday_pay_count =0 then 0 else cast(wednesday_pay_amount/wednesday_pay_count as decimal(15,2)) end as `wednesdayPerOrderAmount`, " +

            "thursday_pay_count as `thursdayPayCount`, " +
            "cast(thursday_pay_amount as decimal(15,2)) as `thursdayPayAmount`, " +
            "case when thursday_pay_count =0 then 0 else cast(thursday_pay_amount/thursday_pay_count as decimal(15,2)) end as `thursdayPerOrderAmount`, " +

            "friday_pay_count as `fridayPayCount`, " +
            "cast(friday_pay_amount as decimal(15,2)) as `fridayPayAmount`, " +
            "case when friday_pay_count =0 then 0 else cast(friday_pay_amount/friday_pay_count as decimal(15,2)) end as `fridayPerOrderAmount`, " +

            "saturday_pay_count as `saturdayPayCount`, " +
            "cast(saturday_pay_amount as decimal(15,2)) as `saturdayPayAmount`, " +
            "case when saturday_pay_count =0 then 0 else cast(saturday_pay_amount/saturday_pay_count as decimal(15,2)) end as `saturdayPerOrderAmount`, " +

            "sunday_pay_count as `sundayPayCount`, " +
            "cast(sunday_pay_amount as decimal(15,2)) as `sundayPayAmount`, " +
            "case when sunday_pay_count =0 then 0 else cast(sunday_pay_amount/sunday_pay_count as decimal(15,2)) end as `sundayPerOrderAmount`, " +

            "cast(pay_amount_0_1 as decimal(15,2)) as `amount_0_1`, " +
            "cast(pay_amount_1_2 as decimal(15,2)) as `amount_1_2`, " +
            "cast(pay_amount_2_3 as decimal(15,2)) as `amount_2_3`, " +
            "cast(pay_amount_3_4 as decimal(15,2)) as `amount_3_4`, " +
            "cast(pay_amount_4_5 as decimal(15,2)) as `amount_4_5`, " +
            "cast(pay_amount_5_6 as decimal(15,2)) as `amount_5_6`, " +
            "cast(pay_amount_6_7 as decimal(15,2)) as `amount_6_7`, " +
            "cast(pay_amount_7_8 as decimal(15,2)) as `amount_7_8`, " +
            "cast(pay_amount_8_9 as decimal(15,2)) as `amount_8_9`, " +
            "cast(pay_amount_9_10 as decimal(15,2)) as `amount_9_10`, " +
            "cast(pay_amount_10_11 as decimal(15,2)) as `amount_10_11`, " +
            "cast(pay_amount_11_12 as decimal(15,2)) as `amount_11_12`, " +
            "cast(pay_amount_12_13 as decimal(15,2)) as `amount_12_13`, " +
            "cast(pay_amount_13_14 as decimal(15,2)) as `amount_13_14`, " +
            "cast(pay_amount_14_15 as decimal(15,2)) as `amount_14_15`, " +
            "cast(pay_amount_15_16 as decimal(15,2)) as `amount_15_16`, " +
            "cast(pay_amount_16_17 as decimal(15,2)) as `amount_16_17`, " +
            "cast(pay_amount_17_18 as decimal(15,2)) as `amount_17_18`, " +
            "cast(pay_amount_18_19 as decimal(15,2)) as `amount_18_19`, " +
            "cast(pay_amount_19_20 as decimal(15,2)) as `amount_19_20`, " +
            "cast(pay_amount_20_21 as decimal(15,2)) as `amount_20_21`, " +
            "cast(pay_amount_21_22 as decimal(15,2)) as `amount_21_22`, " +
            "cast(pay_amount_22_23 as decimal(15,2)) as `amount_22_23`, " +
            "cast(pay_amount_23_24 as decimal(15,2)) as `amount_23_24`, " +

            "pay_count_0_1 as `count_0_1`, " +
            "pay_count_1_2 as `count_1_2`, " +
            "pay_count_2_3 as `count_2_3`, " +
            "pay_count_3_4 as `count_3_4`, " +
            "pay_count_4_5 as `count_4_5`, " +
            "pay_count_5_6 as `count_5_6`, " +
            "pay_count_6_7 as `count_6_7`, " +
            "pay_count_7_8 as `count_7_8`, " +
            "pay_count_8_9 as `count_8_9`, " +
            "pay_count_9_10 as `count_9_10`, " +
            "pay_count_10_11 as `count_10_11`, " +
            "pay_count_11_12 as `count_11_12`, " +
            "pay_count_12_13 as `count_12_13`, " +
            "pay_count_13_14 as `count_13_14`, " +
            "pay_count_14_15 as `count_14_15`, " +
            "pay_count_15_16 as `count_15_16`, " +
            "pay_count_16_17 as `count_16_17`, " +
            "pay_count_17_18 as `count_17_18`, " +
            "pay_count_18_19 as `count_18_19`, " +
            "pay_count_19_20 as `count_19_20`, " +
            "pay_count_20_21 as `count_20_21`, " +
            "pay_count_21_22 as `count_21_22`, " +
            "pay_count_22_23 as `count_22_23`, " +
            "pay_count_23_24 as `count_23_24`, " +


            "case when pay_count_0_1=0 then 0 else cast(pay_amount_0_1/pay_count_0_1 as decimal(15,2)) end as `perOrderPrice_0_1`, " +
            "case when pay_count_1_2=0 then 0 else cast(pay_amount_1_2/pay_count_1_2 as decimal(15,2)) end as `perOrderPrice_1_2`, " +
            "case when pay_count_2_3=0 then 0 else cast(pay_amount_2_3/pay_count_2_3 as decimal(15,2)) end as `perOrderPrice_2_3`, " +
            "case when pay_count_3_4=0 then 0 else cast(pay_amount_3_4/pay_count_3_4 as decimal(15,2)) end as `perOrderPrice_3_4`, " +
            "case when pay_count_4_5=0 then 0 else cast(pay_amount_4_5/pay_count_4_5 as decimal(15,2)) end as `perOrderPrice_4_5`, " +
            "case when pay_count_5_6=0 then 0 else cast(pay_amount_5_6/pay_count_5_6 as decimal(15,2)) end as `perOrderPrice_5_6`, " +
            "case when pay_count_6_7=0 then 0 else cast(pay_amount_6_7/pay_count_6_7 as decimal(15,2)) end as `perOrderPrice_6_7`, " +
            "case when pay_count_7_8=0 then 0 else cast(pay_amount_7_8/pay_count_7_8 as decimal(15,2)) end as `perOrderPrice_7_8`, " +
            "case when pay_count_8_9=0 then 0 else cast(pay_amount_8_9/pay_count_8_9 as decimal(15,2)) end as `perOrderPrice_8_9`, " +
            "case when pay_count_9_10=0 then 0 else cast(pay_amount_9_10/pay_count_9_10 as decimal(15,2)) end as `perOrderPrice_9_10`, " +
            "case when pay_count_10_11=0 then 0 else cast(pay_amount_10_11/pay_count_10_11 as decimal(15,2)) end as `perOrderPrice_10_11`, " +
            "case when pay_count_11_12=0 then 0 else cast(pay_amount_11_12/pay_count_11_12 as decimal(15,2)) end as `perOrderPrice_11_12`, " +
            "case when pay_count_12_13=0 then 0 else cast(pay_amount_12_13/pay_count_12_13 as decimal(15,2)) end as `perOrderPrice_12_13`, " +
            "case when pay_count_13_14=0 then 0 else cast(pay_amount_13_14/pay_count_13_14 as decimal(15,2)) end as `perOrderPrice_13_14`, " +
            "case when pay_count_14_15=0 then 0 else cast(pay_amount_14_15/pay_count_14_15 as decimal(15,2)) end as `perOrderPrice_14_15`, " +
            "case when pay_count_15_16=0 then 0 else cast(pay_amount_15_16/pay_count_15_16 as decimal(15,2)) end as `perOrderPrice_15_16`, " +
            "case when pay_count_16_17=0 then 0 else cast(pay_amount_16_17/pay_count_16_17 as decimal(15,2)) end as `perOrderPrice_16_17`, " +
            "case when pay_count_17_18=0 then 0 else cast(pay_amount_17_18/pay_count_17_18 as decimal(15,2)) end as `perOrderPrice_17_18`, " +
            "case when pay_count_18_19=0 then 0 else cast(pay_amount_18_19/pay_count_18_19 as decimal(15,2)) end as `perOrderPrice_18_19`, " +
            "case when pay_count_19_20=0 then 0 else cast(pay_amount_19_20/pay_count_19_20 as decimal(15,2)) end as `perOrderPrice_19_20`, " +
            "case when pay_count_20_21=0 then 0 else cast(pay_amount_20_21/pay_count_20_21 as decimal(15,2)) end as `perOrderPrice_20_21`, " +
            "case when pay_count_21_22=0 then 0 else cast(pay_amount_21_22/pay_count_21_22 as decimal(15,2)) end as `perOrderPrice_21_22`, " +
            "case when pay_count_22_23=0 then 0 else cast(pay_amount_22_23/pay_count_22_23 as decimal(15,2)) end as `perOrderPrice_22_23`, " +
            "case when pay_count_23_24=0 then 0 else cast(pay_amount_23_24/pay_count_23_24 as decimal(15,2)) end as `perOrderPrice_23_24`, " +

            "year_month as `month`" ;
	
	public static final String YEAR_TIME_RANGE_COLUMN_ARRAY = "merchant_id as `merchantId`, " +
            "january_pay_count as `januaryPayCount`, " +
            "cast(january_pay_amount as decimal(15,2)) as `januaryPayAmount`, " +
            "case when january_pay_count =0 then 0 else cast(january_pay_amount/january_pay_count as decimal(15,2)) end as `januaryPerOrderAmount`, " +

            "february_pay_count as `februaryPayCount`, " +
            "cast(february_pay_amount as decimal(15,2)) as `februaryPayAmount`, " +
            "case when february_pay_count =0 then 0 else cast(february_pay_amount/february_pay_count as decimal(15,2)) end as `februaryPerOrderAmount`, " +

            "march_pay_count as `marchPayCount`, " +
            "cast(march_pay_amount as decimal(15,2)) as `marchPayAmount`, " +
            "case when march_pay_count =0 then 0 else cast(march_pay_amount/march_pay_count as decimal(15,2)) end as `marchPerOrderAmount`, " +

            "april_pay_count as `aprilPayCount`, " +
            "cast(april_pay_amount as decimal(15,2)) as `aprilPayAmount`, " +
            "case when april_pay_count =0 then 0 else cast(april_pay_amount/april_pay_count as decimal(15,2)) end as `aprilPerOrderAmount`, " +

            "may_pay_count as `mayPayCount`, " +
            "cast(may_pay_amount as decimal(15,2)) as `mayPayAmount`, " +
            "case when may_pay_count =0 then 0 else cast(may_pay_amount/may_pay_count as decimal(15,2)) end as `mayPerOrderAmount`, " +

            "june_pay_count as `junePayCount`, " +
            "cast(june_pay_amount as decimal(15,2)) as `junePayAmount`, " +
            "case when june_pay_count =0 then 0 else cast(june_pay_amount/june_pay_count as decimal(15,2)) end as `junePerOrderAmount`, " +

            "july_pay_count as `julyPayCount`, " +
            "cast(july_pay_amount as decimal(15,2)) as `julyPayAmount`, " +
            "case when july_pay_count =0 then 0 else cast(july_pay_amount/july_pay_count as decimal(15,2)) end as `julyPerOrderAmount`, " +

            "august_pay_count as `augustPayCount`, " +
            "cast(august_pay_amount as decimal(15,2)) as `augustPayAmount`, " +
            "case when august_pay_count =0 then 0 else cast(august_pay_amount/august_pay_count as decimal(15,2)) end as `augustPerOrderAmount`, " +

            "september_pay_count as `septemberPayCount`, " +
            "cast(september_pay_amount as decimal(15,2)) as `septemberPayAmount`, " +
            "case when september_pay_count =0 then 0 else cast(september_pay_amount/september_pay_count as decimal(15,2)) end as `septemberPerOrderAmount`, " +

            "october_pay_count as `octoberPayCount`, " +
            "cast(october_pay_amount as decimal(15,2)) as `octoberPayAmount`, " +
            "case when october_pay_count =0 then 0 else cast(october_pay_amount/october_pay_count as decimal(15,2)) end as `octoberPerOrderAmount`, " +

            "november_pay_count as `novemberPayCount`, " +
            "cast(november_pay_amount as decimal(15,2)) as `novemberPayAmount`, " +
            "case when november_pay_count =0 then 0 else cast(november_pay_amount/november_pay_count as decimal(15,2)) end as `novemberPerOrderAmount`, " +

            "december_pay_count as `decemberPayCount`, " +
            "cast(december_pay_amount as decimal(15,2)) as `decemberPayAmount`, " +
            "case when december_pay_count =0 then 0 else cast(december_pay_amount/december_pay_count as decimal(15,2)) end as `decemberPerOrderAmount`, " +

            "cast(pay_amount_0_1 as decimal(15,2)) as `amount_0_1`, " +
            "cast(pay_amount_1_2 as decimal(15,2)) as `amount_1_2`, " +
            "cast(pay_amount_2_3 as decimal(15,2)) as `amount_2_3`, " +
            "cast(pay_amount_3_4 as decimal(15,2)) as `amount_3_4`, " +
            "cast(pay_amount_4_5 as decimal(15,2)) as `amount_4_5`, " +
            "cast(pay_amount_5_6 as decimal(15,2)) as `amount_5_6`, " +
            "cast(pay_amount_6_7 as decimal(15,2)) as `amount_6_7`, " +
            "cast(pay_amount_7_8 as decimal(15,2)) as `amount_7_8`, " +
            "cast(pay_amount_8_9 as decimal(15,2)) as `amount_8_9`, " +
            "cast(pay_amount_9_10 as decimal(15,2)) as `amount_9_10`, " +
            "cast(pay_amount_10_11 as decimal(15,2)) as `amount_10_11`, " +
            "cast(pay_amount_11_12 as decimal(15,2)) as `amount_11_12`, " +
            "cast(pay_amount_12_13 as decimal(15,2)) as `amount_12_13`, " +
            "cast(pay_amount_13_14 as decimal(15,2)) as `amount_13_14`, " +
            "cast(pay_amount_14_15 as decimal(15,2)) as `amount_14_15`, " +
            "cast(pay_amount_15_16 as decimal(15,2)) as `amount_15_16`, " +
            "cast(pay_amount_16_17 as decimal(15,2)) as `amount_16_17`, " +
            "cast(pay_amount_17_18 as decimal(15,2)) as `amount_17_18`, " +
            "cast(pay_amount_18_19 as decimal(15,2)) as `amount_18_19`, " +
            "cast(pay_amount_19_20 as decimal(15,2)) as `amount_19_20`, " +
            "cast(pay_amount_20_21 as decimal(15,2)) as `amount_20_21`, " +
            "cast(pay_amount_21_22 as decimal(15,2)) as `amount_21_22`, " +
            "cast(pay_amount_22_23 as decimal(15,2)) as `amount_22_23`, " +
            "cast(pay_amount_23_24 as decimal(15,2)) as `amount_23_24`, " +

            "pay_count_0_1 as `count_0_1`, " +
            "pay_count_1_2 as `count_1_2`, " +
            "pay_count_2_3 as `count_2_3`, " +
            "pay_count_3_4 as `count_3_4`, " +
            "pay_count_4_5 as `count_4_5`, " +
            "pay_count_5_6 as `count_5_6`, " +
            "pay_count_6_7 as `count_6_7`, " +
            "pay_count_7_8 as `count_7_8`, " +
            "pay_count_8_9 as `count_8_9`, " +
            "pay_count_9_10 as `count_9_10`, " +
            "pay_count_10_11 as `count_10_11`, " +
            "pay_count_11_12 as `count_11_12`, " +
            "pay_count_12_13 as `count_12_13`, " +
            "pay_count_13_14 as `count_13_14`, " +
            "pay_count_14_15 as `count_14_15`, " +
            "pay_count_15_16 as `count_15_16`, " +
            "pay_count_16_17 as `count_16_17`, " +
            "pay_count_17_18 as `count_17_18`, " +
            "pay_count_18_19 as `count_18_19`, " +
            "pay_count_19_20 as `count_19_20`, " +
            "pay_count_20_21 as `count_20_21`, " +
            "pay_count_21_22 as `count_21_22`, " +
            "pay_count_22_23 as `count_22_23`, " +
            "pay_count_23_24 as `count_23_24`, " +


            "case when pay_count_0_1=0 then 0 else cast(pay_amount_0_1/pay_count_0_1 as decimal(15,2)) end as `perOrderPrice_0_1`, " +
            "case when pay_count_1_2=0 then 0 else cast(pay_amount_1_2/pay_count_1_2 as decimal(15,2)) end as `perOrderPrice_1_2`, " +
            "case when pay_count_2_3=0 then 0 else cast(pay_amount_2_3/pay_count_2_3 as decimal(15,2)) end as `perOrderPrice_2_3`, " +
            "case when pay_count_3_4=0 then 0 else cast(pay_amount_3_4/pay_count_3_4 as decimal(15,2)) end as `perOrderPrice_3_4`, " +
            "case when pay_count_4_5=0 then 0 else cast(pay_amount_4_5/pay_count_4_5 as decimal(15,2)) end as `perOrderPrice_4_5`, " +
            "case when pay_count_5_6=0 then 0 else cast(pay_amount_5_6/pay_count_5_6 as decimal(15,2)) end as `perOrderPrice_5_6`, " +
            "case when pay_count_6_7=0 then 0 else cast(pay_amount_6_7/pay_count_6_7 as decimal(15,2)) end as `perOrderPrice_6_7`, " +
            "case when pay_count_7_8=0 then 0 else cast(pay_amount_7_8/pay_count_7_8 as decimal(15,2)) end as `perOrderPrice_7_8`, " +
            "case when pay_count_8_9=0 then 0 else cast(pay_amount_8_9/pay_count_8_9 as decimal(15,2)) end as `perOrderPrice_8_9`, " +
            "case when pay_count_9_10=0 then 0 else cast(pay_amount_9_10/pay_count_9_10 as decimal(15,2)) end as `perOrderPrice_9_10`, " +
            "case when pay_count_10_11=0 then 0 else cast(pay_amount_10_11/pay_count_10_11 as decimal(15,2)) end as `perOrderPrice_10_11`, " +
            "case when pay_count_11_12=0 then 0 else cast(pay_amount_11_12/pay_count_11_12 as decimal(15,2)) end as `perOrderPrice_11_12`, " +
            "case when pay_count_12_13=0 then 0 else cast(pay_amount_12_13/pay_count_12_13 as decimal(15,2)) end as `perOrderPrice_12_13`, " +
            "case when pay_count_13_14=0 then 0 else cast(pay_amount_13_14/pay_count_13_14 as decimal(15,2)) end as `perOrderPrice_13_14`, " +
            "case when pay_count_14_15=0 then 0 else cast(pay_amount_14_15/pay_count_14_15 as decimal(15,2)) end as `perOrderPrice_14_15`, " +
            "case when pay_count_15_16=0 then 0 else cast(pay_amount_15_16/pay_count_15_16 as decimal(15,2)) end as `perOrderPrice_15_16`, " +
            "case when pay_count_16_17=0 then 0 else cast(pay_amount_16_17/pay_count_16_17 as decimal(15,2)) end as `perOrderPrice_16_17`, " +
            "case when pay_count_17_18=0 then 0 else cast(pay_amount_17_18/pay_count_17_18 as decimal(15,2)) end as `perOrderPrice_17_18`, " +
            "case when pay_count_18_19=0 then 0 else cast(pay_amount_18_19/pay_count_18_19 as decimal(15,2)) end as `perOrderPrice_18_19`, " +
            "case when pay_count_19_20=0 then 0 else cast(pay_amount_19_20/pay_count_19_20 as decimal(15,2)) end as `perOrderPrice_19_20`, " +
            "case when pay_count_20_21=0 then 0 else cast(pay_amount_20_21/pay_count_20_21 as decimal(15,2)) end as `perOrderPrice_20_21`, " +
            "case when pay_count_21_22=0 then 0 else cast(pay_amount_21_22/pay_count_21_22 as decimal(15,2)) end as `perOrderPrice_21_22`, " +
            "case when pay_count_22_23=0 then 0 else cast(pay_amount_22_23/pay_count_22_23 as decimal(15,2)) end as `perOrderPrice_22_23`, " +
            "case when pay_count_23_24=0 then 0 else cast(pay_amount_23_24/pay_count_23_24 as decimal(15,2)) end as `perOrderPrice_23_24`, " +

            "year_str as `year`" ;

}
