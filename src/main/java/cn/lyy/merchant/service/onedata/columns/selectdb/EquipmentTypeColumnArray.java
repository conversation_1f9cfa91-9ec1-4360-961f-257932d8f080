package cn.lyy.merchant.service.onedata.columns.selectdb;

public class EquipmentTypeColumnArray {

	public static final String DayEquipmentTypeListSql =
            " merchant_id as `merchantId`,"
            + " province_id as `provinceId`,"
            + " city_id as `cityId`,"
            + " district_id as `districtId`,"
            + " equipment_group_id as `groupId`,"
            + " lyy_equipment_type_id as `equipmentTypeId`,"
            + " equipment_type_name as `equipmentTypeName`,"
            + " cast(pay_amount as decimal(15,2)) as `payAmount`,"         //支付金额
            + " cast(case when pay_amount_yesterday =0 and pay_amount>0 then 1 " 
            + " when pay_amount_yesterday=0 and pay_amount=0 then 0 " 
            + " when pay_amount_yesterday>0 and pay_amount=0 then -1 " 
            + " else (pay_amount-pay_amount_yesterday)/pay_amount_yesterday end as decimal(15,2)) as `payAmountUp`,"

			+ " cast(pay_count as decimal(15,0)) as `payCount`,"         //订单数
			+ " cast(case when pay_count_yesterday =0 and pay_count>0 then 1 " 
			+ " when pay_count_yesterday=0 and pay_count=0 then 0 " 
			+ " when pay_count_yesterday>0 and pay_count=0 then -1 " 
			+ " else (pay_count-pay_count_yesterday)/pay_count_yesterday end as decimal(15,2)) as `payCountUp`,"

			+ " cast(case when pay_count=0 then 0 else pay_amount/pay_count end as decimal(15,2)) as `perOrderAmount`,"         //订单均价
			+ " case when (pay_count =0 or pay_amount=0) and (pay_count_yesterday = 0 or pay_amount_yesterday=0) then 0 " 
			+ " when (pay_count =0 or pay_amount=0) and pay_amount_yesterday>0 then -1 " 
			+ " when (pay_count_yesterday = 0 or pay_amount_yesterday=0) and pay_amount > 0 then 1 " 
			+ " else cast( ((pay_amount/pay_count) - (pay_amount_yesterday/pay_count_yesterday))/(pay_amount_yesterday/pay_count_yesterday) as decimal(15,2)) end  as `perOrderAmountUp`" ;

    public static final String WeekEquipmentTypeListSql =
    		 " merchant_id as `merchantId`,"
			+ " province_id as `provinceId`,"
			+ " city_id as `cityId`,"
			+ " district_id as `districtId`,"
			+ " equipment_group_id as `groupId`,"
			+ " lyy_equipment_type_id as `equipmentTypeId`,"
			+ " equipment_type_name as `equipmentTypeName`,"
			+ " cast(pay_amount as decimal(15,2)) as `payAmount`,"        //支付金额
			+ " cast(case when pay_amount_last_week =0 and pay_amount>0 then 1 " 
			+ " when pay_amount_last_week=0 and pay_amount=0 then 0 " 
			+ " when pay_amount_last_week>0 and pay_amount=0 then -1 " 
			+ " else (pay_amount-pay_amount_last_week)/pay_amount_last_week end as decimal(15,2)) as `payAmountUp`,"
			 
			+ " cast(pay_count as decimal(15,0)) as `payCount`,"        //订单数
			+ " cast(case when pay_count_last_week =0 and pay_count>0 then 1 " 
			+ " when pay_count_last_week=0 and pay_count=0 then 0 " 
			+ " when pay_count_last_week>0 and pay_count=0 then -1 " 
			+ " else (pay_count-pay_count_last_week)/pay_count_last_week end as decimal(15,2)) as `payCountUp`,"
			
			+ " cast(case when pay_count=0 then 0 else pay_amount/pay_count end as decimal(15,2)) as `perOrderAmount`,"         //订单均价
			+ " case when (pay_count =0 or pay_amount=0) and (pay_count_last_week = 0 or pay_amount_last_week=0) then 0 " 
			+ " when (pay_count =0 or pay_amount=0) and pay_amount_last_week>0 then -1 " 
			+ " when (pay_count_last_week = 0 or pay_amount_last_week=0) and pay_amount > 0 then 1 " 
			+ " else cast( ((pay_amount/pay_count) - (pay_amount_last_week/pay_count_last_week))/(pay_amount_last_week/pay_count_last_week) as decimal(15,2)) end  as `perOrderAmountUp` " ;

                   
    public static final String MonthEquipmentTypeListSql =
    		" merchant_id as `merchantId`,"
			+ " province_id as `provinceId`,"
			+ " city_id as `cityId`,"
			+ " district_id as `districtId`,"
			+ " equipment_group_id as `groupId`,"
			+ " lyy_equipment_type_id as `equipmentTypeId`,"
			+ " equipment_type_name as `equipmentTypeName`,"
			+ " cast(pay_amount as decimal(15,2)) as `payAmount`,"       //支付金额
			+ " cast(case when pay_amount_last_month =0 and pay_amount>0 then 1 "
			+ " when pay_amount_last_month=0 and pay_amount=0 then 0 "
			+ " when pay_amount_last_month>0 and pay_amount=0 then -1 "
			+ " else (pay_amount-pay_amount_last_month)/pay_amount_last_month end as decimal(15,2)) as `payAmountUp`,"
			  
			+ " cast(pay_count as decimal(15,0)) as `payCount`,"       //订单数
			+ " cast(case when pay_count_last_month =0 and pay_count>0 then 1 "
			+ " when pay_count_last_month=0 and pay_count=0 then 0 "
			+ " when pay_count_last_month>0 and pay_count=0 then -1 "
			+ " else (pay_count-pay_count_last_month)/pay_count_last_month end as decimal(15,2)) as `payCountUp`,"
			 
			+ " cast(case when pay_count=0 then 0 else pay_amount/pay_count end as decimal(15,2)) as `perOrderAmount`,"       //订单均价
			+ " case when (pay_count =0 or pay_amount=0) and (pay_count_last_month = 0 or pay_amount_last_month=0) then 0 "
			+ " when (pay_count =0 or pay_amount=0) and pay_amount_last_month>0 then -1 "
			+ " when (pay_count_last_month = 0 or pay_amount_last_month=0) and pay_amount > 0 then 1 "
			+ " else cast( ((pay_amount/pay_count) - (pay_amount_last_month/pay_count_last_month))/(pay_amount_last_month/pay_count_last_month) as decimal(15,2)) end  as `perOrderAmountUp` " ;

    public static final String YearEquipmentTypeListSql =
    		" merchant_id as `merchantId`,"
			+ " province_id as `provinceId`,"
			+ " city_id as `cityId`,"
			+ " district_id as `districtId`,"
			+ " equipment_group_id as `groupId`,"
			+ " lyy_equipment_type_id as `equipmentTypeId`,"
			+ " equipment_type_name as `equipmentTypeName`,"
			+ " cast(pay_amount as decimal(15,2)) as `payAmount`,"        //支付金额
			+ " cast(case when pay_amount_last_year =0 and pay_amount>0 then 1 "
			+ " when pay_amount_last_year=0 and pay_amount=0 then 0 "
			+ " when pay_amount_last_year>0 and pay_amount=0 then -1 "
			+ " else (pay_amount-pay_amount_last_year)/pay_amount_last_year end as decimal(15,2)) as `payAmountUp`,"
			    
			+ " cast(pay_count as decimal(15,0)) as `payCount`,"        //订单数
			+ " cast(case when pay_count_last_year =0 and pay_count>0 then 1 "
			+ " when pay_count_last_year=0 and pay_count=0 then 0 "
			+ " when pay_count_last_year>0 and pay_count=0 then -1 "
			+ " else (pay_count-pay_count_last_year)/pay_count_last_year end as decimal(15,2)) as `payCountUp`,"
			     
			+ " cast(case when pay_count=0 then 0 else pay_amount/pay_count end as decimal(15,2)) as `perOrderAmount`,"        //订单均价
			+ " case when (pay_count =0 or pay_amount=0) and (pay_count_last_year = 0 or pay_amount_last_year=0) then 0 "
			+ " when (pay_count =0 or pay_amount=0) and pay_amount_last_year>0 then -1 "
			+ " when (pay_count_last_year = 0 or pay_amount_last_year=0) and pay_amount > 0 then 1 "
			+ " else cast( ((pay_amount/pay_count) - (pay_amount_last_year/pay_count_last_year))/(pay_amount_last_year/pay_count_last_year) as decimal(15,2)) end  as `perOrderAmountUp` " ;

}
