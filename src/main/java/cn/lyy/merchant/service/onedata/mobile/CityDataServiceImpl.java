package cn.lyy.merchant.service.onedata.mobile;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.alibaba.fastjson.JSONObject;
import cn.lyy.merchant.controller.onedata.dto.DayParamDto;
import cn.lyy.merchant.controller.onedata.dto.MonthParamDto;
import cn.lyy.merchant.controller.onedata.dto.WeekParamDto;
import cn.lyy.merchant.controller.onedata.dto.YearParamDto;
import cn.lyy.merchant.service.onedata.FeignUtil;
import cn.lyy.merchant.service.onedata.ParamDto;
import cn.lyy.merchant.service.onedata.columns.ColumnArrayMap;
import cn.lyy.merchant.service.onedata.columns.ColumnArrayMapKey;

@Service
public class CityDataServiceImpl implements CityDataService{

	@Autowired
    private FeignUtil feignUtil ;
    
    @Value("${oneData.dayEquipmentAnalyseCode}")
    private String dayEquipmentAnalyseCode;
    
    @Value("${oneData.weekEquipmentAnalyseCode}")
    private String weekEquipmentAnalyseCode;
    
    @Value("${oneData.monthEquipmentAnalyseCode}")
    private String monthEquipmentAnalyseCode;
    
    @Value("${oneData.yearEquipmentAnalyseCode}")
    private String yearEquipmentAnalyseCode;
    
	/**
     * 日报获取城市维度统计信息
     * @param dayParamDto
     * @return
     */
    public JSONObject getDayCityData(DayParamDto dayParamDto) {
    	ParamDto params = new ParamDto();
    	
    	params.setCode(dayEquipmentAnalyseCode);
    	//params.setColumnArray(QuerySql.CITY_COMMON_COLUMN_ARRAY);
    	String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.CITY_COMMON_COLUMN_ARRAY) ;
    	params.setColumnArray(columnArray) ;
        params.setGroupByColumnArray("city_name");
        
        DayParamDto newDto = new DayParamDto() ;
        newDto.setMerchantId(dayParamDto.getMerchantId());
        newDto.setDay(dayParamDto.getDay());
        newDto.setPageNum(dayParamDto.getPageNum());
        
        JSONObject cityResult = feignUtil.getData(params, newDto);
        return cityResult;
    }

    /**
     * 周报报获取城市维度统计信息
     * @param weekParamDto
     * @return
     */
    public JSONObject getWeekCityData(WeekParamDto weekParamDto){
    	ParamDto params = new ParamDto();
    	params.setCode(weekEquipmentAnalyseCode);
    	//params.setColumnArray(QuerySql.CITY_COMMON_COLUMN_ARRAY);
    	String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.CITY_COMMON_COLUMN_ARRAY) ;
    	params.setColumnArray(columnArray) ;
        params.setGroupByColumnArray("city_name");
         
    	WeekParamDto newDto = new WeekParamDto() ;
    	newDto.setMerchantId(weekParamDto.getMerchantId());
    	newDto.setMonth(weekParamDto.getMonth());
    	newDto.setWeek(weekParamDto.getWeek());
        newDto.setPageNum(weekParamDto.getPageNum());
        
    	JSONObject cityResult = feignUtil.getData(params, newDto);
        return cityResult;
    }

    /**
     * 月报报获取城市维度统计信息
     * @param monthParamDto
     * @return
     */
    public JSONObject getMonthCityData(MonthParamDto monthParamDto){
    	ParamDto params = new ParamDto();
    	params.setCode(monthEquipmentAnalyseCode);
    	//params.setColumnArray(QuerySql.CITY_COMMON_COLUMN_ARRAY);
    	String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.CITY_COMMON_COLUMN_ARRAY) ;
    	params.setColumnArray(columnArray) ;
        params.setGroupByColumnArray("city_name");
        
        MonthParamDto newDto = new MonthParamDto() ;
    	newDto.setMerchantId(monthParamDto.getMerchantId());
    	newDto.setYear(monthParamDto.getYear());
    	newDto.setMonth(monthParamDto.getMonth());
        newDto.setPageNum(monthParamDto.getPageNum() );
        
    	JSONObject cityResult = feignUtil.getData(params, newDto);
        return cityResult;
    }
    
    /**
     * 年报报获取城市维度统计信息
     * @param yearParamDto
     * @return
     */
    public JSONObject getYearCityData(YearParamDto yearParamDto){
    	ParamDto params = new ParamDto();
    	params.setCode(yearEquipmentAnalyseCode);
    	//params.setColumnArray(QuerySql.CITY_COMMON_COLUMN_ARRAY);
    	String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.CITY_COMMON_COLUMN_ARRAY) ;
    	params.setColumnArray(columnArray) ;
        params.setGroupByColumnArray("city_name");
        
        YearParamDto newDto = new YearParamDto() ;
    	newDto.setMerchantId(yearParamDto.getMerchantId());
    	newDto.setYear(yearParamDto.getYear());
        newDto.setPageNum(yearParamDto.getPageNum() );
        
    	JSONObject cityResult = feignUtil.getData(params, newDto);
        return cityResult;
    }
    
    /**
     * 日报获取城市维度统计信息-底部平均值
     * @param dayParamDto
     * @return
     */
    public JSONObject getDayCityAvgData(DayParamDto dayParamDto) {
    	ParamDto params = new ParamDto();
    	params.setCode(dayEquipmentAnalyseCode);
    	
    	//params.setColumnArray(QuerySql.CITY_AVG_COLUMN_ARRAY);
    	String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.CITY_AVG_COLUMN_ARRAY) ;
    	params.setColumnArray(columnArray);
    	
        DayParamDto newDto = new DayParamDto() ;
        newDto.setMerchantId(dayParamDto.getMerchantId());
        newDto.setDay(dayParamDto.getDay());
        newDto.setPageNum(1);
        
        JSONObject cityAvgResult = feignUtil.getData(params, newDto);
        return cityAvgResult;
    }

    /**
     * 周报报获取城市维度统计信息-底部平均值
     * @param weekParamDto
     * @return
     */
    public JSONObject getWeekCityAvgData(WeekParamDto weekParamDto){
    	ParamDto params = new ParamDto();
    	params.setCode(weekEquipmentAnalyseCode);
    	//params.setColumnArray(QuerySql.CITY_AVG_COLUMN_ARRAY);
    	String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.CITY_AVG_COLUMN_ARRAY) ;
    	params.setColumnArray(columnArray);
    	
    	WeekParamDto newDto = new WeekParamDto() ;
    	newDto.setMerchantId(weekParamDto.getMerchantId());
    	newDto.setMonth(weekParamDto.getMonth());
    	newDto.setWeek(weekParamDto.getWeek());
    	newDto.setPageNum(1);
    	
        JSONObject cityAvgResult = feignUtil.getData(params, newDto);
        return cityAvgResult;
    }

    /**
     * 月报报获取城市维度统计信息-底部平均值
     * @param monthParamDto
     * @return
     */
    public JSONObject getMonthCityAvgData(MonthParamDto monthParamDto){
    	ParamDto params = new ParamDto();
    	params.setCode(monthEquipmentAnalyseCode);
    	//params.setColumnArray(QuerySql.CITY_AVG_COLUMN_ARRAY);
    	String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.CITY_AVG_COLUMN_ARRAY) ;
    	params.setColumnArray(columnArray);
    	
        MonthParamDto newDto = new MonthParamDto() ;
    	newDto.setMerchantId(monthParamDto.getMerchantId());
    	newDto.setYear(monthParamDto.getYear());
    	newDto.setMonth(monthParamDto.getMonth());
    	newDto.setPageNum(1);
        
        JSONObject cityAvgResult = feignUtil.getData(params, newDto);
        return cityAvgResult;
    }
    
    /**
     * 年报报获取城市维度统计信息-底部平均值
     * @param yearParamDto
     * @return
     */
    public JSONObject getYearCityAvgData(YearParamDto yearParamDto){
    	ParamDto params = new ParamDto();
    	params.setCode(yearEquipmentAnalyseCode);
    	//params.setColumnArray(QuerySql.CITY_AVG_COLUMN_ARRAY);
    	String columnArray = ColumnArrayMap.columnArray(params.getDatabaseType(), ColumnArrayMapKey.CITY_AVG_COLUMN_ARRAY) ;
    	params.setColumnArray(columnArray);
    	
        YearParamDto newDto = new YearParamDto() ;
    	newDto.setMerchantId(yearParamDto.getMerchantId());
    	newDto.setYear(yearParamDto.getYear());
    	newDto.setPageNum(1);
        
        JSONObject cityAvgResult = feignUtil.getData(params, newDto);
        return cityAvgResult;
    }
    
}
