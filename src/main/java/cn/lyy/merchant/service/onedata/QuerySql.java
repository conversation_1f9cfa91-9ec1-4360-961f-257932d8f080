package cn.lyy.merchant.service.onedata;

import lombok.Data;

@Data
public class QuerySql {
//
//	 //订单日表查询数据
//    public static final String DayOrderQuerySql = "merchant_id as \"merchantId\", " +
//            "province_id as \"provinceId\", " +
//            "city_id as \"cityId\", " +
//            "district_id as \"districtId\", " +
//            "equipment_group_id as \"groupId\", " +
//            "equipment_group_name as \"groupName\", " +
//            "equipment_group_address as \"address\", " +
//            "cast(online_actual_amount as decimal(10,2)) as \"onlineAmount\"," +
//            "cast(case when online_actual_amount_yesterday =0 and online_actual_amount>0 then 1 " +
//            "when online_actual_amount_yesterday=0 and online_actual_amount=0 then 0 " +
//            "when online_actual_amount_yesterday>0 and online_actual_amount=0 then -1 " +
//            "else (online_actual_amount-online_actual_amount_yesterday)::numeric/online_actual_amount_yesterday::numeric end as decimal(10,2)) as \"onlineAmountUp\"," +
//
//            "cast(online_pay_amount as decimal(10,2)) as \"onlinePayAmount\", " +
//            "cast(online_pay_amount_yesterday as decimal(10,2)) as \"lastCycleOnlinePayAmount\", " +
//            "cast(case when online_pay_amount_yesterday =0 and online_pay_amount>0 then 1 " +
//            "when online_pay_amount_yesterday=0 and online_pay_amount=0 then 0 " +
//            "when online_pay_amount_yesterday>0 and online_pay_amount=0 then -1 " +
//            "else (online_pay_amount-online_pay_amount_yesterday)::numeric/online_pay_amount_yesterday::numeric end as decimal(10,2)) as \"onlinePayAmountUp\"," +
//
//            "online_pay_count as \"onlinePayCounts\", " +
//            "cast(case when online_pay_count_yesterday =0 and online_pay_count>0 then 1 " +
//            "when online_pay_count=0 and online_pay_count_yesterday=0 then 0 " +
//            "when online_pay_count=0 and online_pay_count_yesterday>0 then -1 " +
//            "else (online_pay_count-online_pay_count_yesterday)::numeric/online_pay_count_yesterday::numeric end as decimal(10,2)) as \"onlinePayCountsUp\"," +
//
//            "cast(online_refund_amount as decimal(10,2)) as \"onlineRefundAmount\", " +
//            "cast(case when online_refund_amount_yesterday =0 and online_refund_amount>0 then 1 " +
//            "when online_refund_amount_yesterday=0 and online_refund_amount=0 then 0 " +
//            "when online_refund_amount_yesterday>0 and online_refund_amount=0 then -1 " +
//            "else (online_refund_amount-online_refund_amount_yesterday)::numeric/online_refund_amount_yesterday::numeric end as decimal(10,2)) as \"onlineRefundAmountUp\"," +
//
//            "online_refund_count as \"onlineRefundCounts\"," +
//            "cast(case when online_refund_count_yesterday =0 and online_refund_count>0 then 1 " +
//            "when online_refund_count_yesterday=0 and online_refund_count=0 then 0 " +
//            "when online_refund_count_yesterday>0 and online_refund_count=0 then -1 " +
//            "else (online_refund_count-online_refund_count_yesterday)::numeric/online_refund_count_yesterday::numeric end as decimal(10,2)) as \"onlineRefundCountsUp\"," +
//
//            "cast(offline_actual_amount as decimal(10,2)) as \"cashAmount\"," +        //现金总收入
//            "cast(case when offline_actual_amount_yesterday =0 and offline_actual_amount>0 then 1 " +
//            "when offline_actual_amount_yesterday=0 and offline_actual_amount=0 then 0 " +
//            "when offline_actual_amount_yesterday>0 and offline_actual_amount=0 then -1 " +
//            "else (offline_actual_amount-offline_actual_amount_yesterday)::numeric/offline_actual_amount_yesterday::numeric end as decimal(10,2)) as \"cashAmountUp\"," +
//
//            "cast(offline_pay_amount as decimal(10,2)) as \"cashPayAmount\"," +        //现金收款
//            "cast(case when offline_pay_amount_yesterday =0 and offline_pay_amount>0 then 1 " +
//            "when offline_pay_amount_yesterday=0 and offline_pay_amount=0 then 0 " +
//            "when offline_pay_amount_yesterday>0 and offline_pay_amount=0 then -1 " +
//            "else (offline_pay_amount-offline_pay_amount_yesterday)::numeric/offline_pay_amount_yesterday::numeric end as decimal(10,2)) as \"cashPayAmountUp\"," +
//
//            "cast(offline_pay_count as decimal(10,2)) as \"cashPayCounts\"," +        //现金收款笔数
//            "cast(case when offline_pay_count_yesterday =0 and offline_pay_count>0 then 1 " +
//            "when offline_pay_count_yesterday=0 and offline_pay_count=0 then 0 " +
//            "when offline_pay_count_yesterday>0 and offline_pay_count=0 then -1 " +
//            "else (offline_pay_count-offline_pay_count_yesterday)::numeric/offline_pay_count_yesterday::numeric end as decimal(10,2)) as \"cashPayCountsUp\"," +
//
//            "0 as \"cashRefundAmount\", 0 as \"cashRefundAmountUp\", 0 as \"cashRefundCounts\", 0 as \"cashRefundCountsUp\"," +       //现金没有退款
//
//            "cast(pay_amount as decimal(10,2)) as \"payAmount\"," +
//            "cast(case when pay_amount_yesterday =0 and pay_amount>0 then 1 " +
//            "when pay_amount_yesterday=0 and pay_amount=0 then 0 " +
//            "when pay_amount_yesterday>0 and pay_amount=0 then -1 " +
//            "else (pay_amount-pay_amount_yesterday)::numeric/pay_amount_yesterday::numeric end as decimal(10,2)) as \"payAmountUp\"," +
//
//            "pay_count as \"payCounts\"," +
//            "cast(case when pay_count_yesterday =0 and pay_count>0 then 1 " +
//            "when pay_count_yesterday=0 and pay_count=0 then 0 " +
//            "when pay_count_yesterday>0 and pay_count=0 then -1 " +
//            "else (pay_count-pay_count_yesterday)::numeric/pay_count_yesterday::numeric end as decimal(10,2)) as \"payCountsUp\"," +
//
//            "cast(case when pay_count=0 then 0 else pay_amount::numeric/pay_count::numeric end as decimal(10,2)) as \"perOrderAmount\"," +        //订单均价
//            "case when (pay_count =0 or pay_amount=0) and (pay_count_yesterday = 0 or pay_amount_yesterday=0) then 0 " +
//            "when (pay_count =0 or pay_amount=0) and  pay_amount_yesterday > 0 then -1 " +
//            "when (pay_count_yesterday = 0 or pay_amount_yesterday=0) and pay_amount > 0 then 1 " +
//            "else cast( ((pay_amount::numeric/pay_count::numeric) - (pay_amount_yesterday::numeric/pay_count_yesterday::numeric))::numeric/(pay_amount_yesterday::numeric/pay_count_yesterday::numeric)::numeric as decimal(10,2)) end  as \"perOrderAmountUp\","+
//
//
//            "cast(case when online_pay_count=0 then 0 else online_pay_amount::numeric/online_pay_count::numeric end as decimal(10,2)) as \"perOnlineOrderAmount\"," +        //订单均价
//            "case when (online_pay_count =0 or online_pay_amount=0) and (online_pay_count_yesterday = 0 or online_pay_amount_yesterday=0) then 0 " +
//            "when (online_pay_count =0 or online_pay_amount=0) and  online_pay_amount_yesterday > 0 then -1 " +
//            "when (online_pay_count_yesterday = 0 or online_pay_amount_yesterday=0) and online_pay_amount > 0 then 1 " +
//            "else cast( ((online_pay_amount::numeric/online_pay_count::numeric) - (online_pay_amount_yesterday::numeric/online_pay_count_yesterday::numeric))::numeric/(online_pay_amount_yesterday::numeric/online_pay_count_yesterday::numeric)::numeric as decimal(10,2)) end  as \"perOnlineOrderAmountUp\","+
//
//
//            "cast(refund_amount as decimal(10,2)) as \"refundAmount\"," +
//            "cast(case when refund_amount_yesterday =0 and refund_amount>0 then 1 " +
//            "when refund_amount_yesterday=0 and refund_amount=0 then 0 " +
//            "when refund_amount_yesterday>0 and refund_amount=0 then -1 " +
//            "else (refund_amount-refund_amount_yesterday)::numeric/refund_amount_yesterday::numeric end as decimal(10,2)) as \"refundAmountUp\"," +
//
//            "refund_count as \"refundCounts\"," +
//            "cast(case when refund_count_yesterday =0 and refund_count>0 then 1 " +
//            "when refund_count_yesterday=0 and refund_count=0 then 0 " +
//            "when refund_count_yesterday>0 and refund_count=0 then -1 " +
//            "else (refund_count-refund_count_yesterday)::numeric/refund_count_yesterday::numeric end as decimal(10,2)) as \"refundCountsUp\", " +
//
//            "day " ;
//
//
//    //订单日表查询数据
//    public static final String WeekOrderQuerySql = "merchant_id as \"merchantId\", " +
//            "province_id as \"provinceId\", " +
//            "city_id as \"cityId\", " +
//            "district_id as \"districtId\", " +
//            "equipment_group_id as \"groupId\", " +
//            "equipment_group_name as \"groupName\", " +
//            "equipment_group_address as \"address\", " +
//            "cast(online_actual_amount as decimal(10,2)) as \"onlineAmount\"," +
//            "cast(case when online_actual_amount_last_week =0 and online_actual_amount>0 then 1 " +
//            "when online_actual_amount_last_week=0 and online_actual_amount=0 then 0 " +
//            "when online_actual_amount_last_week>0 and online_actual_amount=0 then -1 " +
//            "else (online_actual_amount-online_actual_amount_last_week)::numeric/online_actual_amount_last_week::numeric end as decimal(10,2)) as \"onlineAmountUp\"," +
//
//            "cast(online_pay_amount as decimal(10,2)) as \"onlinePayAmount\", " +
//            "cast(online_pay_amount_last_week as decimal(10,2)) as \"lastCycleOnlinePayAmount\", " +
//            "cast(case when online_pay_amount_last_week =0 and online_pay_amount>0 then 1 " +
//            "when online_pay_amount_last_week=0 and online_pay_amount=0 then 0 " +
//            "when online_pay_amount_last_week>0 and online_pay_amount=0 then -1 " +
//            "else (online_pay_amount-online_pay_amount_last_week)::numeric/online_pay_amount_last_week::numeric end as decimal(10,2)) as \"onlinePayAmountUp\"," +
//
//            "online_pay_count as \"onlinePayCounts\", " +
//            "cast(case when online_pay_count_last_week =0 and online_pay_count>0 then 1 " +
//            "when online_pay_count=0 and online_pay_count_last_week=0 then 0 " +
//            "when online_pay_count=0 and online_pay_count_last_week>0 then -1 " +
//            "else (online_pay_count-online_pay_count_last_week)::numeric/online_pay_count_last_week::numeric end as decimal(10,2)) as \"onlinePayCountsUp\"," +
//
//            "cast(online_refund_amount as decimal(10,2)) as \"onlineRefundAmount\", " +
//            "cast(case when online_refund_amount_last_week =0 and online_refund_amount>0 then 1 " +
//            "when online_refund_amount_last_week=0 and online_refund_amount=0 then 0 " +
//            "when online_refund_amount_last_week>0 and online_refund_amount=0 then -1 " +
//            "else (online_refund_amount-online_refund_amount_last_week)::numeric/online_refund_amount_last_week::numeric end as decimal(10,2)) as \"onlineRefundAmountUp\"," +
//
//            "online_refund_count as \"onlineRefundCounts\"," +
//            "cast(case when online_refund_count_last_week =0 and online_refund_count>0 then 1 " +
//            "when online_refund_count_last_week=0 and online_refund_count=0 then 0 " +
//            "when online_refund_count_last_week>0 and online_refund_count=0 then -1 " +
//            "else (online_refund_count-online_refund_count_last_week)::numeric/online_refund_count_last_week::numeric end as decimal(10,2)) as \"onlineRefundCountsUp\"," +
//
//            "cast(offline_actual_amount as decimal(10,2)) as \"cashAmount\"," +        //现金总收入
//            "cast(case when offline_actual_amount_last_week =0 and offline_actual_amount>0 then 1 " +
//            "when offline_actual_amount_last_week=0 and offline_actual_amount=0 then 0 " +
//            "when offline_actual_amount_last_week>0 and offline_actual_amount=0 then -1 " +
//            "else (offline_actual_amount-offline_actual_amount_last_week)::numeric/offline_actual_amount_last_week::numeric end as decimal(10,2)) as \"cashAmountUp\"," +
//
//            "cast(offline_pay_amount as decimal(10,2)) as \"cashPayAmount\"," +        //现金收款
//            "cast(case when offline_pay_amount_last_week =0 and offline_pay_amount>0 then 1 " +
//            "when offline_pay_amount_last_week=0 and offline_pay_amount=0 then 0 " +
//            "when offline_pay_amount_last_week>0 and offline_pay_amount=0 then -1 " +
//            "else (offline_pay_amount-offline_pay_amount_last_week)::numeric/offline_pay_amount_last_week::numeric end as decimal(10,2)) as \"cashPayAmountUp\"," +
//
//            "cast(offline_pay_count as decimal(10,2)) as \"cashPayCounts\"," +        //现金收款笔数
//            "cast(case when offline_pay_count_last_week =0 and offline_pay_count>0 then 1 " +
//            "when offline_pay_count_last_week=0 and offline_pay_count=0 then 0 " +
//            "when offline_pay_count_last_week>0 and offline_pay_count=0 then -1 " +
//            "else (offline_pay_count-offline_pay_count_last_week)::numeric/offline_pay_count_last_week::numeric end as decimal(10,2)) as \"cashPayCountsUp\"," +
//
//            "0 as \"cashRefundAmount\", 0 as \"cashRefundAmountUp\", 0 as \"cashRefundCounts\", 0 as \"cashRefundCountsUp\"," +       //现金没有退款
//
//            "cast(pay_amount as decimal(10,2)) as \"payAmount\"," +
//            "cast(case when pay_amount_last_week =0 and pay_amount>0 then 1 " +
//            "when pay_amount_last_week=0 and pay_amount=0 then 0 " +
//            "when pay_amount_last_week>0 and pay_amount=0 then -1 " +
//            "else (pay_amount-pay_amount_last_week)::numeric/pay_amount_last_week::numeric end as decimal(10,2)) as \"payAmountUp\"," +
//
//            "pay_count as \"payCounts\"," +
//            "cast(case when pay_count_last_week =0 and pay_count>0 then 1 " +
//            "when pay_count_last_week=0 and pay_count=0 then 0 " +
//            "when pay_count_last_week>0 and pay_count=0 then -1 " +
//            "else (pay_count-pay_count_last_week)::numeric/pay_count_last_week::numeric end as decimal(10,2)) as \"payCountsUp\"," +
//
//            "cast(case when pay_count=0 then 0 else pay_amount::numeric/pay_count::numeric end as decimal(10,2)) as \"perOrderAmount\"," +        //订单均价
//            "case when (pay_count =0 or pay_amount=0) and (pay_count_last_week = 0 or pay_amount_last_week=0) then 0 " +
//            "when (pay_count =0 or pay_amount=0) and  pay_amount_last_week > 0 then -1 " +
//            "when (pay_count_last_week = 0 or pay_amount_last_week=0) and pay_amount > 0 then 1 " +
//            "else cast( ((pay_amount::numeric/pay_count::numeric) - (pay_amount_last_week::numeric/pay_count_last_week::numeric))::numeric/(pay_amount_last_week::numeric/pay_count_last_week::numeric)::numeric as decimal(10,2)) end  as \"perOrderAmountUp\","+
//
//            "cast(case when online_pay_count=0 then 0 else online_pay_amount::numeric/online_pay_count::numeric end as decimal(10,2)) as \"perOnlineOrderAmount\"," +        //订单均价
//            "case when (online_pay_count =0 or online_pay_amount=0) and (online_pay_count_last_week = 0 or online_pay_amount_last_week=0) then 0 " +
//            "when (online_pay_count =0 or online_pay_amount=0) and  online_pay_amount_last_week > 0 then -1 " +
//            "when (online_pay_count_last_week = 0 or online_pay_amount_last_week=0) and online_pay_amount > 0 then 1 " +
//            "else cast( ((online_pay_amount::numeric/online_pay_count::numeric) - (online_pay_amount_last_week::numeric/online_pay_count_last_week::numeric))::numeric/(online_pay_amount_last_week::numeric/online_pay_count_last_week::numeric)::numeric as decimal(10,2)) end  as \"perOnlineOrderAmountUp\","+
//
//
//
//            "cast(refund_amount as decimal(10,2)) as \"refundAmount\"," +
//            "cast(case when refund_amount_last_week =0 and refund_amount>0 then 1 " +
//            "when refund_amount_last_week=0 and refund_amount=0 then 0 " +
//            "when refund_amount_last_week>0 and refund_amount=0 then -1 " +
//            "else (refund_amount-refund_amount_last_week)::numeric/refund_amount_last_week::numeric end as decimal(10,2)) as \"refundAmountUp\"," +
//
//            "refund_count as \"refundCounts\"," +
//            "cast(case when refund_count_last_week =0 and refund_count>0 then 1 " +
//            "when refund_count_last_week=0 and refund_count=0 then 0 " +
//            "when refund_count_last_week>0 and refund_count=0 then -1 " +
//            "else (refund_count-refund_count_last_week)::numeric/refund_count_last_week::numeric end as decimal(10,2)) as \"refundCountsUp\""
//            ;
//
//    //订单日表查询数据
//    public static final String MonthOrderQuerySql = "merchant_id as \"merchantId\", " +
//            "province_id as \"provinceId\", " +
//            "city_id as \"cityId\", " +
//            "district_id as \"districtId\", " +
//            "equipment_group_id as \"groupId\", " +
//            "equipment_group_name as \"groupName\", " +
//            "equipment_group_address as \"address\", " +
//            "cast(online_actual_amount as decimal(10,2)) as \"onlineAmount\"," +
//            "cast(case when online_actual_amount_last_month =0 and online_actual_amount>0 then 1 " +
//            "when online_actual_amount_last_month=0 and online_actual_amount=0 then 0 " +
//            "when online_actual_amount_last_month>0 and online_actual_amount=0 then -1 " +
//            "else (online_actual_amount-online_actual_amount_last_month)::numeric/online_actual_amount_last_month::numeric end as decimal(10,2)) as \"onlineAmountUp\"," +
//
//            "cast(online_pay_amount as decimal(10,2)) as \"onlinePayAmount\", " +
//            "cast(online_pay_amount_last_month as decimal(10,2)) as \"lastCycleOnlinePayAmount\", " +
//            "cast(case when online_pay_amount_last_month =0 and online_pay_amount>0 then 1 " +
//            "when online_pay_amount_last_month=0 and online_pay_amount=0 then 0 " +
//            "when online_pay_amount_last_month>0 and online_pay_amount=0 then -1 " +
//            "else (online_pay_amount-online_pay_amount_last_month)::numeric/online_pay_amount_last_month::numeric end as decimal(10,2)) as \"onlinePayAmountUp\"," +
//
//            "online_pay_count as \"onlinePayCounts\", " +
//            "cast(case when online_pay_count_last_month =0 and online_pay_count>0 then 1 " +
//            "when online_pay_count=0 and online_pay_count_last_month=0 then 0 " +
//            "when online_pay_count=0 and online_pay_count_last_month>0 then -1 " +
//            "else (online_pay_count-online_pay_count_last_month)::numeric/online_pay_count_last_month::numeric end as decimal(10,2)) as \"onlinePayCountsUp\"," +
//
//            "cast(online_refund_amount as decimal(10,2)) as \"onlineRefundAmount\", " +
//            "cast(case when online_refund_amount_last_month =0 and online_refund_amount>0 then 1 " +
//            "when online_refund_amount_last_month=0 and online_refund_amount=0 then 0 " +
//            "when online_refund_amount_last_month>0 and online_refund_amount=0 then -1 " +
//            "else (online_refund_amount-online_refund_amount_last_month)::numeric/online_refund_amount_last_month::numeric end as decimal(10,2)) as \"onlineRefundAmountUp\"," +
//
//            "online_refund_count as \"onlineRefundCounts\"," +
//            "cast(case when online_refund_count_last_month =0 and online_refund_count>0 then 1 " +
//            "when online_refund_count_last_month=0 and online_refund_count=0 then 0 " +
//            "when online_refund_count_last_month>0 and online_refund_count=0 then -1 " +
//            "else (online_refund_count-online_refund_count_last_month)::numeric/online_refund_count_last_month::numeric end as decimal(10,2)) as \"onlineRefundCountsUp\"," +
//
//            "cast(offline_actual_amount as decimal(10,2)) as \"cashAmount\"," +        //现金总收入
//            "cast(case when offline_actual_amount_last_month =0 and offline_actual_amount>0 then 1 " +
//            "when offline_actual_amount_last_month=0 and offline_actual_amount=0 then 0 " +
//            "when offline_actual_amount_last_month>0 and offline_actual_amount=0 then -1 " +
//            "else (offline_actual_amount-offline_actual_amount_last_month)::numeric/offline_actual_amount_last_month::numeric end as decimal(10,2)) as \"cashAmountUp\"," +
//
//            "cast(offline_pay_amount as decimal(10,2)) as \"cashPayAmount\"," +        //现金收款
//            "cast(case when offline_pay_amount_last_month =0 and offline_pay_amount>0 then 1 " +
//            "when offline_pay_amount_last_month=0 and offline_pay_amount=0 then 0 " +
//            "when offline_pay_amount_last_month>0 and offline_pay_amount=0 then -1 " +
//            "else (offline_pay_amount-offline_pay_amount_last_month)::numeric/offline_pay_amount_last_month::numeric end as decimal(10,2)) as \"cashPayAmountUp\"," +
//
//            "cast(offline_pay_count as decimal(10,2)) as \"cashPayCounts\"," +        //现金收款笔数
//            "cast(case when offline_pay_count_last_month =0 and offline_pay_count>0 then 1 " +
//            "when offline_pay_count_last_month=0 and offline_pay_count=0 then 0 " +
//            "when offline_pay_count_last_month>0 and offline_pay_count=0 then -1 " +
//            "else (offline_pay_count-offline_pay_count_last_month)::numeric/offline_pay_count_last_month::numeric end as decimal(10,2)) as \"cashPayCountsUp\"," +
//
//            "0 as \"cashRefundAmount\", 0 as \"cashRefundAmountUp\", 0 as \"cashRefundCounts\", 0 as \"cashRefundCountsUp\"," +       //现金没有退款
//
//            "cast(pay_amount as decimal(10,2)) as \"payAmount\"," +
//            "cast(case when pay_amount_last_month =0 and pay_amount>0 then 1 " +
//            "when pay_amount_last_month=0 and pay_amount=0 then 0 " +
//            "when pay_amount_last_month>0 and pay_amount=0 then -1 " +
//            "else (pay_amount-pay_amount_last_month)::numeric/pay_amount_last_month::numeric end as decimal(10,2)) as \"payAmountUp\"," +
//
//            "pay_count as \"payCounts\"," +
//            "cast(case when pay_count_last_month =0 and pay_count>0 then 1 " +
//            "when pay_count_last_month=0 and pay_count=0 then 0 " +
//            "when pay_count_last_month>0 and pay_count=0 then -1 " +
//            "else (pay_count-pay_count_last_month)::numeric/pay_count_last_month::numeric end as decimal(10,2)) as \"payCountsUp\"," +
//
//            "cast(case when pay_count=0 then 0 else pay_amount::numeric/pay_count::numeric end as decimal(10,2)) as \"perOrderAmount\"," +        //订单均价
//            "case when (pay_count =0 or pay_amount=0) and (pay_count_last_month = 0 or pay_amount_last_month=0) then 0 " +
//            "when (pay_count =0 or pay_amount=0) and  pay_amount_last_month > 0 then -1 " +
//            "when (pay_count_last_month = 0 or pay_amount_last_month=0) and pay_amount > 0 then 1 " +
//            "else cast( ((pay_amount::numeric/pay_count::numeric) - (pay_amount_last_month::numeric/pay_count_last_month::numeric))::numeric/(pay_amount_last_month::numeric/pay_count_last_month::numeric)::numeric as decimal(10,2)) end  as \"perOrderAmountUp\","+
//
//            "cast(case when online_pay_count=0 then 0 else online_pay_amount::numeric/online_pay_count::numeric end as decimal(10,2)) as \"perOnlineOrderAmount\"," +        //订单均价
//            "case when (online_pay_count =0 or online_pay_amount=0) and (online_pay_count_last_month = 0 or online_pay_amount_last_month=0) then 0 " +
//            "when (online_pay_count =0 or online_pay_amount=0) and  online_pay_amount_last_month > 0 then -1 " +
//            "when (online_pay_count_last_month = 0 or online_pay_amount_last_month=0) and online_pay_amount > 0 then 1 " +
//            "else cast( ((online_pay_amount::numeric/online_pay_count::numeric) - (online_pay_amount_last_month::numeric/online_pay_count_last_month::numeric))::numeric/(online_pay_amount_last_month::numeric/online_pay_count_last_month::numeric)::numeric as decimal(10,2)) end  as \"perOnlineOrderAmountUp\","+
//
//
//            "cast(refund_amount as decimal(10,2)) as \"refundAmount\"," +
//            "cast(case when refund_amount_last_month =0 and refund_amount>0 then 1 " +
//            "when refund_amount_last_month=0 and refund_amount=0 then 0 " +
//            "when refund_amount_last_month>0 and refund_amount=0 then -1 " +
//            "else (refund_amount-refund_amount_last_month)::numeric/refund_amount_last_month::numeric end as decimal(10,2)) as \"refundAmountUp\"," +
//
//            "refund_count as \"refundCounts\"," +
//            "cast(case when refund_count_last_month =0 and refund_count>0 then 1 " +
//            "when refund_count_last_month=0 and refund_count=0 then 0 " +
//            "when refund_count_last_month>0 and refund_count=0 then -1 " +
//            "else (refund_count-refund_count_last_month)::numeric/refund_count_last_month::numeric end as decimal(10,2)) as \"refundCountsUp\""
//            ;
//
//    //订单日表查询数据
//    public static final String YearOrderQuerySql = "merchant_id as \"merchantId\", " +
//            "province_id as \"provinceId\", " +
//            "city_id as \"cityId\", " +
//            "district_id as \"districtId\", " +
//            "equipment_group_id as \"groupId\", " +
//            "equipment_group_name as \"groupName\", " +
//            "equipment_group_address as \"address\", " +
//            "cast(online_actual_amount as decimal(10,2)) as \"onlineAmount\"," +
//            "cast(case when online_actual_amount_last_year =0 and online_actual_amount>0 then 1 " +
//            "when online_actual_amount_last_year=0 and online_actual_amount=0 then 0 " +
//            "when online_actual_amount_last_year>0 and online_actual_amount=0 then -1 " +
//            "else (online_actual_amount-online_actual_amount_last_year)::numeric/online_actual_amount_last_year::numeric end as decimal(10,2)) as \"onlineAmountUp\"," +
//
//            "cast(online_pay_amount as decimal(10,2)) as \"onlinePayAmount\", " +
//            "cast(online_pay_amount_last_year as decimal(10,2)) as \"lastCycleOnlinePayAmount\", " +
//            "cast(case when online_pay_amount_last_year =0 and online_pay_amount>0 then 1 " +
//            "when online_pay_amount_last_year=0 and online_pay_amount=0 then 0 " +
//            "when online_pay_amount_last_year>0 and online_pay_amount=0 then -1 " +
//            "else (online_pay_amount-online_pay_amount_last_year)::numeric/online_pay_amount_last_year::numeric end as decimal(10,2)) as \"onlinePayAmountUp\"," +
//
//            "online_pay_count as \"onlinePayCounts\", " +
//            "cast(case when online_pay_count_last_year =0 and online_pay_count>0 then 1 " +
//            "when online_pay_count=0 and online_pay_count_last_year=0 then 0 " +
//            "when online_pay_count=0 and online_pay_count_last_year>0 then -1 " +
//            "else (online_pay_count-online_pay_count_last_year)::numeric/online_pay_count_last_year::numeric end as decimal(10,2)) as \"onlinePayCountsUp\"," +
//
//            "cast(online_refund_amount as decimal(10,2)) as \"onlineRefundAmount\", " +
//            "cast(case when online_refund_amount_last_year =0 and online_refund_amount>0 then 1 " +
//            "when online_refund_amount_last_year=0 and online_refund_amount=0 then 0 " +
//            "when online_refund_amount_last_year>0 and online_refund_amount=0 then -1 " +
//            "else (online_refund_amount-online_refund_amount_last_year)::numeric/online_refund_amount_last_year::numeric end as decimal(10,2)) as \"onlineRefundAmountUp\"," +
//
//            "online_refund_count as \"onlineRefundCounts\"," +
//            "cast(case when online_refund_count_last_year =0 and online_refund_count>0 then 1 " +
//            "when online_refund_count_last_year=0 and online_refund_count=0 then 0 " +
//            "when online_refund_count_last_year>0 and online_refund_count=0 then -1 " +
//            "else (online_refund_count-online_refund_count_last_year)::numeric/online_refund_count_last_year::numeric end as decimal(10,2)) as \"onlineRefundCountsUp\"," +
//
//            "cast(offline_actual_amount as decimal(10,2)) as \"cashAmount\"," +        //现金总收入
//            "cast(case when offline_actual_amount_last_year =0 and offline_actual_amount>0 then 1 " +
//            "when offline_actual_amount_last_year=0 and offline_actual_amount=0 then 0 " +
//            "when offline_actual_amount_last_year>0 and offline_actual_amount=0 then -1 " +
//            "else (offline_actual_amount-offline_actual_amount_last_year)::numeric/offline_actual_amount_last_year::numeric end as decimal(10,2)) as \"cashAmountUp\"," +
//
//            "cast(offline_pay_amount as decimal(10,2)) as \"cashPayAmount\"," +        //现金收款
//            "cast(case when offline_pay_amount_last_year =0 and offline_pay_amount>0 then 1 " +
//            "when offline_pay_amount_last_year=0 and offline_pay_amount=0 then 0 " +
//            "when offline_pay_amount_last_year>0 and offline_pay_amount=0 then -1 " +
//            "else (offline_pay_amount-offline_pay_amount_last_year)::numeric/offline_pay_amount_last_year::numeric end as decimal(10,2)) as \"cashPayAmountUp\"," +
//
//            "cast(offline_pay_count as decimal(10,2)) as \"cashPayCounts\"," +        //现金收款笔数
//            "cast(case when offline_pay_count_last_year =0 and offline_pay_count>0 then 1 " +
//            "when offline_pay_count_last_year=0 and offline_pay_count=0 then 0 " +
//            "when offline_pay_count_last_year>0 and offline_pay_count=0 then -1 " +
//            "else (offline_pay_count-offline_pay_count_last_year)::numeric/offline_pay_count_last_year::numeric end as decimal(10,2)) as \"cashPayCountsUp\"," +
//
//            "0 as \"cashRefundAmount\", 0 as \"cashRefundAmountUp\", 0 as \"cashRefundCounts\", 0 as \"cashRefundCountsUp\"," +       //现金没有退款
//
//            "cast(pay_amount as decimal(10,2)) as \"payAmount\"," +
//            "cast(case when pay_amount_last_year =0 and pay_amount>0 then 1 " +
//            "when pay_amount_last_year=0 and pay_amount=0 then 0 " +
//            "when pay_amount_last_year>0 and pay_amount=0 then -1 " +
//            "else (pay_amount-pay_amount_last_year)::numeric/pay_amount_last_year::numeric end as decimal(10,2)) as \"payAmountUp\"," +
//
//            "pay_count as \"payCounts\"," +
//            "cast(case when pay_count_last_year =0 and pay_count>0 then 1 " +
//            "when pay_count_last_year=0 and pay_count=0 then 0 " +
//            "when pay_count_last_year>0 and pay_count=0 then -1 " +
//            "else (pay_count-pay_count_last_year)::numeric/pay_count_last_year::numeric end as decimal(10,2)) as \"payCountsUp\"," +
//
//            "cast(case when pay_count=0 then 0 else pay_amount::numeric/pay_count::numeric end as decimal(10,2)) as \"perOrderAmount\"," +        //订单均价
//            "case when (pay_count =0 or pay_amount=0) and (pay_count_last_year = 0 or pay_amount_last_year=0) then 0 " +
//            "when (pay_count =0 or pay_amount=0) and  pay_count_last_year > 0 then -1 " +
//            "when (pay_count_last_year = 0 or pay_amount_last_year=0) and pay_amount > 0 then 1 " +
//            "else cast( ((pay_amount::numeric/pay_count::numeric) - (pay_amount_last_year::numeric/pay_count_last_year::numeric))::numeric/(pay_amount_last_year::numeric/pay_count_last_year::numeric)::numeric as decimal(10,2)) end  as \"perOrderAmountUp\","+
//
//            "cast(case when online_pay_count=0 then 0 else online_pay_amount::numeric/online_pay_count::numeric end as decimal(10,2)) as \"perOnlineOrderAmount\"," +        //订单均价
//            "case when (online_pay_count =0 or online_pay_amount=0) and (online_pay_count_last_year = 0 or online_pay_amount_last_year=0) then 0 " +
//            "when (online_pay_count =0 or online_pay_amount=0) and  online_pay_amount_last_year > 0 then -1 " +
//            "when (online_pay_count_last_year = 0 or online_pay_amount_last_year=0) and online_pay_amount > 0 then 1 " +
//            "else cast( ((online_pay_amount::numeric/online_pay_count::numeric) - (online_pay_amount_last_year::numeric/online_pay_count_last_year::numeric))::numeric/(online_pay_amount_last_year::numeric/online_pay_count_last_year::numeric)::numeric as decimal(10,2)) end  as \"perOnlineOrderAmountUp\","+
//
//            "cast(refund_amount as decimal(10,2)) as \"refundAmount\"," +
//            "cast(case when refund_amount_last_year =0 and refund_amount>0 then 1 " +
//            "when refund_amount_last_year=0 and refund_amount=0 then 0 " +
//            "when refund_amount_last_year>0 and refund_amount=0 then -1 " +
//            "else (refund_amount-refund_amount_last_year)::numeric/refund_amount_last_year::numeric end as decimal(10,2)) as \"refundAmountUp\"," +
//
//            "refund_count as \"refundCounts\"," +
//            "cast(case when refund_count_last_year =0 and refund_count>0 then 1 " +
//            "when refund_count_last_year=0 and refund_count=0 then 0 " +
//            "when refund_count_last_year>0 and refund_count=0 then -1 " +
//            "else (refund_count-refund_count_last_year)::numeric/refund_count_last_year::numeric end as decimal(10,2)) as \"refundCountsUp\""
//            ;
//
//
//    public static final String DayGroupEquipmentSql = "merchant_id as \"merchantId\", " +
//            "province_id as \"provinceId\", " +
//            "city_id as \"cityId\", " +
//            "district_id as \"districtId\", " +
//            "equipment_group_id as \"groupId\", " +
//            "equipment_group_name as \"groupName\", " +
//            "equipment_group_address as \"address\", " +
//            "pay_equipment as \"payEquipments\"," +     //支付设备
//            "cast(case when pay_equipment_yesterday =0 and pay_equipment>0 then 1 " +
//            "when pay_equipment_yesterday=0 and pay_equipment=0 then 0 " +
//            "when pay_equipment_yesterday>0 and pay_equipment=0 then -1 " +
//            "else (pay_equipment-pay_equipment_yesterday)::numeric/pay_equipment_yesterday::numeric end as decimal(10,2)) as \"payEquipmentsUp\"," +
//
//            "equipment_count as \"equipmentCounts\", " +        //设备总数
//            "cast(case when equipment_count_yesterday =0 and equipment_count>0 then 1 " +
//            "when equipment_count_yesterday=0 and equipment_count=0 then 0 " +
//            "when equipment_count_yesterday>0 and equipment_count=0 then -1 " +
//            "else (equipment_count-equipment_count_yesterday)::numeric/equipment_count_yesterday::numeric end as decimal(10,2)) as \"equipmentCountsUp\"," +
//
//            "online_00_position_num as \"onlineEquipmentCounts\", " +       //在线设备数
//            "cast(case when online_00_position_num_yesterday =0 and online_00_position_num>0 then 1 " +
//            "when online_00_position_num_yesterday=0 and online_00_position_num=0 then 0 " +
//            "when online_00_position_num_yesterday>0 and online_00_position_num=0 then -1 " +
//            "else (online_00_position_num-online_00_position_num_yesterday)::numeric/online_00_position_num_yesterday::numeric end as decimal(10,2)) as \"onlineEquipmentCountsUp\"," +
//
//            "total_start_counts as \"startCounts\", " +       //启动次数
//            "cast(case when total_start_counts_yesterday =0 and total_start_counts>0 then 1 " +
//            "when total_start_counts_yesterday=0 and total_start_counts=0 then 0 " +
//            "when total_start_counts_yesterday>0 and total_start_counts=0 then -1 " +
//            "else (total_start_counts-total_start_counts_yesterday)::numeric/total_start_counts_yesterday::numeric end as decimal(10,2)) as \"startCountsUp\"," +
//            //每台设备平均启动次数
//            "cast(start_per_equipment as decimal(10,2)) as \"perEquipmentStarts\"," +   //设备启动均次
//            "cast(case when start_per_equipment_yesterday =0 and start_per_equipment>0 then 1 " +
//            "when start_per_equipment_yesterday=0 and start_per_equipment=0 then 0 " +
//            "when start_per_equipment_yesterday>0 and start_per_equipment=0 then -1 " +
//            "else (start_per_equipment-start_per_equipment_yesterday)::numeric/start_per_equipment_yesterday::numeric end as decimal(10,2)) as \"perEquipmentStartsUp\"," +
//
//            "total_start_counts/1 as \"perEquipmentDayStarts\"," +   //设备日均启动均次 设备启动次数/账期日
//            "cast(case when total_start_counts_yesterday =0 and total_start_counts>0 then 1 " +
//            "when total_start_counts_yesterday=0 and total_start_counts=0 then 0 " +
//            "when total_start_counts_yesterday>0 and total_start_counts=0 then -1 " +
//            "else (total_start_counts-total_start_counts_yesterday)::numeric/total_start_counts_yesterday::numeric end as decimal(10,2)) as \"perEquipmentDayStartsUp\"," +
//
//            "equipment_group_count as \"groupCounts\"," +        //场地总数
//            "cast(case when equipment_group_count_yesterday =0 and equipment_group_count>0 then 1 " +
//            "when equipment_group_count_yesterday=0 and equipment_group_count=0 then 0 " +
//            "when equipment_group_count_yesterday>0 and equipment_group_count=0 then -1 " +
//            "else (equipment_group_count-equipment_group_count_yesterday)::numeric/equipment_group_count_yesterday::numeric end as decimal(10,2)) as \"groupCountsUp\"," +
//
//            "cast(case when equipment_group_count=0 then 0 else pay_amount::numeric/equipment_group_count::numeric end as decimal(10,2)) as \"perGroupAmount\"," +        //场地均收
//            "case when (equipment_group_count =0 or pay_amount=0) and (equipment_group_count_yesterday = 0 or pay_amount_yesterday=0) then 0 " +
//            "when (equipment_group_count =0 or pay_amount=0) and pay_amount_yesterday > 0 then -1 " +
//            "when (equipment_group_count_yesterday = 0 or pay_amount_yesterday=0) and pay_amount > 0 then 1 " +
//            "else cast( ((pay_amount::numeric/equipment_group_count::numeric) - (pay_amount_yesterday::numeric/equipment_group_count_yesterday::numeric))::numeric/(pay_amount_yesterday::numeric/equipment_group_count_yesterday::numeric)::numeric as decimal(10,2)) end  as \"perGroupAmountUp\","+
//
//            "cast(case when equipment_count=0 then 0 else pay_amount::numeric/equipment_count::numeric end as decimal(10,2)) as \"perEquipmentAmount\"," +        //设备均收
//            "case when (equipment_count =0 or pay_amount=0) and (equipment_count_yesterday = 0 or pay_amount_yesterday=0) then 0 " +
//            "when (equipment_count =0 or pay_amount=0) and pay_amount_yesterday > 0 then -1 "+
//            "when (equipment_count_yesterday = 0 or pay_amount_yesterday=0) and pay_amount > 0 then 1 " +
//            "else cast( ((pay_amount::numeric/equipment_count::numeric) - (pay_amount_yesterday::numeric/equipment_count_yesterday::numeric))::numeric/(pay_amount_yesterday::numeric/equipment_count_yesterday::numeric)::numeric as decimal(10,2)) end  as \"perEquipmentAmountUp\","+
//
//            "cast(case when equipment_group_count=0 then 0 else order_count::numeric/equipment_group_count::numeric end as decimal(10,2)) as \"perGroupOrder\"," +        //场地订单均数
//            "case when (equipment_group_count =0 or order_count=0) and (equipment_group_count_yesterday = 0 or order_count_yesterday=0) then 0 " +
//            "when (equipment_group_count =0 or order_count=0) and order_count_yesterday>0 then -1 " +
//            "when (equipment_group_count_yesterday = 0 or order_count_yesterday=0) and order_count > 0 then 1 " +
//            "else cast( ((order_count::numeric/equipment_group_count::numeric) - (order_count_yesterday::numeric/equipment_group_count_yesterday::numeric))::numeric/(order_count_yesterday::numeric/equipment_group_count_yesterday::numeric)::numeric as decimal(10,2)) end  as \"perGroupOrderUp\","+
//
//
//            "cast(case when equipment_count=0 then 0 else order_count::numeric/equipment_count::numeric end as decimal(10,2)) as \"perEquipmentOrder\"," +        //设备订单均数
//            "case when (equipment_count =0 or order_count=0) and (equipment_count_yesterday = 0 or order_count_yesterday=0) then 0 " +
//            "when (equipment_count =0 or order_count=0) and order_count_yesterday >0 then -1 " +
//            "when (equipment_count_yesterday = 0 or order_count_yesterday=0) and order_count > 0 then 1 " +
//            "else cast( ((order_count::numeric/equipment_count::numeric) - (order_count_yesterday::numeric/equipment_count_yesterday::numeric))::numeric/(order_count_yesterday::numeric/equipment_count_yesterday::numeric)::numeric as decimal(10,2)) end  as \"perEquipmentOrderUp\", "+
//
//
//
//            "cast(case when equipment_group_count=0 then 0 else total_start_counts::numeric/equipment_group_count::numeric end as decimal(10,2)) as \"perGroupStart\"," +        //场地启动均次
//            "case when (equipment_group_count_yesterday = 0 or total_start_counts_yesterday=0) and (total_start_counts = 0 or equipment_group_count =0) then 0 " +
//            "when total_start_counts_yesterday >0 and (total_start_counts = 0 or equipment_group_count =0) then -1 " +
//            "when (equipment_group_count_yesterday = 0 or total_start_counts_yesterday=0) and total_start_counts > 0 then 1 " +
//            "else cast( ((total_start_counts::numeric/equipment_group_count::numeric) - (total_start_counts_yesterday::numeric/equipment_group_count_yesterday::numeric))/(total_start_counts_yesterday::numeric/equipment_group_count_yesterday::numeric) as decimal(10,2)) end  as \"perGroupStartUp\", "+
//
//
//            "cast(case when equipment_group_count=0 then 0 else equipment_count::numeric/equipment_group_count::numeric end as decimal(10,2)) as \"perGroupEquipment\","+        //场地设备均数
//            "case when (equipment_group_count_yesterday =0 or equipment_count_yesterday =0) and (equipment_count=0 or equipment_group_count=0) then 0 "+
//            "when equipment_count_yesterday >0 and (equipment_count=0 or equipment_group_count=0) then -1 "+
//            "when (equipment_group_count_yesterday = 0 or equipment_count_yesterday=0) and equipment_count > 0 then 1 " +
//            "else cast( ((equipment_count::numeric/equipment_group_count::numeric) - (equipment_count_yesterday::numeric/equipment_group_count_yesterday::numeric))/(equipment_count_yesterday::numeric/equipment_group_count_yesterday::numeric) as decimal(10,2)) end as \"perGroupEquipmentUp\"";
//
//
//    public static final String GroupEquipmentSql = "merchant_id as \"merchantId\", " +
//            "province_id as \"provinceId\", " +
//            "city_id as \"cityId\", " +
//            "district_id as \"districtId\", " +
//            "equipment_group_id as \"groupId\", " +
//            "equipment_group_name as \"groupName\", " +
//            "equipment_group_address as \"address\", " +
//
//            "pay_equipment as \"payEquipments\"," +     //支付设备
//            "cast(case when last_cycle_pay_equipment =0 and pay_equipment>0 then 1 " +
//            "when last_cycle_pay_equipment=0 and pay_equipment=0 then 0 " +
//            "when last_cycle_pay_equipment>0 and pay_equipment=0 then -1 " +
//            "else (pay_equipment-last_cycle_pay_equipment)::numeric/last_cycle_pay_equipment::numeric end as decimal(10,2)) as \"payEquipmentsUp\"," +
//
//            "equipment_count as \"equipmentCounts\", " +        //设备总数
//            "cast(case when last_cycle_equipment_count =0 and equipment_count>0 then 1 " +
//            "when last_cycle_equipment_count=0 and equipment_count=0 then 0 " +
//            "when last_cycle_equipment_count>0 and equipment_count=0 then -1 " +
//            "else (equipment_count-last_cycle_equipment_count)::numeric/last_cycle_equipment_count::numeric end as decimal(10,2)) as \"equipmentCountsUp\"," +
//
//            "online_00_position_num as \"onlineEquipmentCounts\", " +       //在线设备数
//            "cast(case when last_cycle_online_00_position_num =0 and online_00_position_num>0 then 1 " +
//            "when last_cycle_online_00_position_num=0 and online_00_position_num=0 then 0 " +
//            "when last_cycle_online_00_position_num>0 and online_00_position_num=0 then -1 " +
//            "else (online_00_position_num-last_cycle_online_00_position_num)::numeric/last_cycle_online_00_position_num::numeric end as decimal(10,2)) as \"onlineEquipmentCountsUp\"," +
//
//            "total_start_counts as \"startCounts\", " +       //启动次数
//            "cast(case when last_cycle_total_start_counts =0 and total_start_counts>0 then 1 " +
//            "when last_cycle_total_start_counts=0 and total_start_counts=0 then 0 " +
//            "when last_cycle_total_start_counts>0 and total_start_counts=0 then -1 " +
//            "else (total_start_counts-last_cycle_total_start_counts)::numeric/last_cycle_total_start_counts::numeric end as decimal(10,2)) as \"startCountsUp\"," +
//            //每台设备平均启动次数
//            "cast(start_per_equipment as decimal(10,2)) as \"perEquipmentStarts\"," +   //设备启动均次
//            "cast(case when last_cycle_start_per_equipment =0 and start_per_equipment>0 then 1 " +
//            "when last_cycle_start_per_equipment=0 and start_per_equipment=0 then 0 " +
//            "when last_cycle_start_per_equipment>0 and start_per_equipment=0 then -1 " +
//            "else (start_per_equipment-last_cycle_start_per_equipment)::numeric/last_cycle_start_per_equipment::numeric end as decimal(10,2)) as \"perEquipmentStartsUp\"," +
//
//            "cast(total_start_counts::numeric/7 as decimal(10,2)) as \"perEquipmentDayStarts\"," +   //设备日均启动均次 设备启动次数/账期日
//            "cast(case when last_cycle_total_start_counts =0 and total_start_counts>0 then 1 " +
//            "when last_cycle_total_start_counts=0 and total_start_counts=0 then 0 " +
//            "when last_cycle_total_start_counts>0 and total_start_counts=0 then -1 " +
//            "else (total_start_counts-last_cycle_total_start_counts)::numeric/last_cycle_total_start_counts::numeric end as decimal(10,2)) as \"perEquipmentDayStartsUp\"," +
//
//            "equipment_group_count as \"groupCounts\"," +        //场地总数
//            "cast(case when last_cycle_equipment_group_count =0 and equipment_group_count>0 then 1 " +
//            "when last_cycle_equipment_group_count=0 and equipment_group_count=0 then 0 " +
//            "when last_cycle_equipment_group_count>0 and equipment_group_count=0 then -1 " +
//            "else (equipment_group_count-last_cycle_equipment_group_count)::numeric/last_cycle_equipment_group_count::numeric end as decimal(10,2)) as \"groupCountsUp\"," +
//
//            "cast(case when equipment_group_count=0 then 0 else pay_amount::numeric/equipment_group_count::numeric end as decimal(10,2)) as \"perGroupAmount\"," +        //场地均收
//            "case when (equipment_group_count =0 or pay_amount=0) and (last_cycle_equipment_group_count = 0 or last_cycle_pay_amount=0) then 0 " +
//            "when (equipment_group_count =0 or pay_amount=0) and last_cycle_pay_amount>0 then -1 " +
//            "when (last_cycle_equipment_group_count = 0 or last_cycle_pay_amount=0) and pay_amount > 0 then 1 " +
//            "else cast( ((pay_amount::numeric/equipment_group_count::numeric) - (last_cycle_pay_amount::numeric/last_cycle_equipment_group_count::numeric))::numeric/(last_cycle_pay_amount::numeric/last_cycle_equipment_group_count::numeric)::numeric as decimal(10,2)) end  as \"perGroupAmountUp\","+
//
//
//            "cast(case when equipment_count=0 then 0 else pay_amount::numeric/equipment_count::numeric end as decimal(10,2)) as \"perEquipmentAmount\"," +        //设备均收
//            "case when (equipment_count =0 or pay_amount=0) and (last_cycle_equipment_count = 0 or last_cycle_pay_amount=0) then 0 " +
//            "when (equipment_count =0 or pay_amount=0) and last_cycle_pay_amount > 0 then -1 " +
//            "when (last_cycle_equipment_count = 0 or last_cycle_pay_amount=0) and pay_amount > 0 then 1 " +
//            "else cast( ((pay_amount::numeric/equipment_count::numeric) - (last_cycle_pay_amount::numeric/last_cycle_equipment_count::numeric))::numeric/(last_cycle_pay_amount::numeric/last_cycle_equipment_count::numeric)::numeric as decimal(10,2)) end  as \"perEquipmentAmountUp\","+
//
//
//
//
//            "cast(case when equipment_count=0 then 0 else order_count::numeric/equipment_count::numeric end as decimal(10,2)) as \"perEquipmentOrder\"," +        //设备订单均数
//            "case when (equipment_count =0 or order_count=0) and (last_cycle_equipment_count = 0 or last_cycle_order_count=0) then 0 " +
//            "when (equipment_count =0 or order_count=0) and last_cycle_order_count>0 then -1 " +
//            "when (last_cycle_equipment_count = 0 or last_cycle_order_count=0) and order_count > 0 then 1 " +
//            "else cast( ((order_count::numeric/equipment_count::numeric) - (last_cycle_order_count::numeric/last_cycle_equipment_count::numeric))::numeric/(last_cycle_order_count::numeric/last_cycle_equipment_count::numeric)::numeric as decimal(10,2)) end  as \"perEquipmentOrderUp\","+
//
//
//
//            "cast(case when equipment_group_count=0 then 0 else order_count::numeric/equipment_group_count::numeric end as decimal(10,2)) as \"perGroupOrder\"," +        //场地订单均数
//            "case when (equipment_group_count =0 or order_count=0) and (last_cycle_equipment_group_count = 0 or last_cycle_order_count=0) then 0 " +
//            "when (equipment_group_count =0 or order_count=0) and last_cycle_order_count>0 then -1 " +
//            "when (last_cycle_equipment_group_count = 0 or last_cycle_order_count=0) and order_count > 0 then 1 " +
//            "else cast( ((order_count::numeric/equipment_group_count::numeric) - (last_cycle_order_count::numeric/last_cycle_equipment_group_count::numeric))::numeric/(last_cycle_order_count::numeric/last_cycle_equipment_group_count::numeric)::numeric as decimal(10,2)) end  as \"perGroupOrderUp\","+
//
//
//            "cast(case when equipment_group_count=0 then 0 else total_start_counts::numeric/equipment_group_count::numeric end as decimal(10,2)) as \"perGroupStart\"," +        //场地启动均次
//            "case when (last_cycle_equipment_group_count = 0 or last_cycle_total_start_counts=0) and (total_start_counts = 0 or equipment_group_count =0) then 0 " +
//            "when last_cycle_total_start_counts>0 and (total_start_counts = 0 or equipment_group_count =0) then -1 " +
//            "when (last_cycle_equipment_group_count = 0 or last_cycle_total_start_counts=0) and total_start_counts > 0 then 1 " +
//            "else cast( ((total_start_counts::numeric/equipment_group_count::numeric) - (last_cycle_total_start_counts::numeric/last_cycle_equipment_group_count::numeric))/(last_cycle_total_start_counts::numeric/last_cycle_equipment_group_count::numeric) as decimal(10,2)) end  as \"perGroupStartUp\","+
//
//
//            "cast(case when equipment_group_count=0 then 0 else equipment_count::numeric/equipment_group_count::numeric end as decimal(10,2)) as \"perGroupEquipment\","+        //场地设备均数
//            "case when (last_cycle_equipment_group_count =0 or last_cycle_equipment_count =0) and (equipment_count=0 or equipment_group_count=0) then 0 "+
//            "when last_cycle_equipment_count>0 and (equipment_count=0 or equipment_group_count=0) then -1 "+
//            "when (last_cycle_equipment_group_count = 0 or last_cycle_equipment_count=0) and equipment_count > 0 then 1 " +
//            "else cast( ((equipment_count::numeric/equipment_group_count::numeric) - (last_cycle_equipment_count::numeric/last_cycle_equipment_group_count::numeric))/(last_cycle_equipment_count::numeric/last_cycle_equipment_group_count::numeric) as decimal(10,2)) end as \"perGroupEquipmentUp\""
//            ;
//
//
//    public static final String EquipmentSql = "merchant_id as \"merchantId\", " +
//            "province_id as \"provinceId\", " +
//            "city_id as \"cityId\", " +
//            "district_id as \"districtId\", " +
//            "equipment_group_id as \"groupId\", " +
//            "equipment_group_name as \"groupName\", " +
//            "equipment_group_address as \"address\", " +
//            "equipment_id as \"equipmentId\", " +
//            "equipment_type_name || equipment_value as \"equipmentName\", "+
//            "lyy_equipment_type_id as \"equipmentTypeId\", " +
//            "equipment_type_name as \"equipmentTypeName\", " +
//
//            "cast(pay_amount as decimal(10,2)) as \"payAmount\"," +
//            "cast(case when last_cycle_pay_amount =0 and pay_amount>0 then 1 " +
//            "when last_cycle_pay_amount=0 and pay_amount=0 then 0 " +
//            "when last_cycle_pay_amount>0 and pay_amount=0 then -1 " +
//            "else (pay_amount-last_cycle_pay_amount)::numeric/last_cycle_pay_amount::numeric end as decimal(10,2)) as \"payAmountUp\", " +
//
//            "total_start_counts as \"startCounts\"," +
//            "cast(case when last_cycle_total_start_counts =0 and total_start_counts>0 then 1 " +
//            "when last_cycle_total_start_counts=0 and total_start_counts=0 then 0 " +
//            "when last_cycle_total_start_counts>0 and total_start_counts=0 then -1 " +
//            "else (total_start_counts-last_cycle_total_start_counts)::numeric/last_cycle_total_start_counts::numeric end as decimal(10,2)) as \"startCountsUp\", " +
//
//            "cast(pay_user_num as decimal(10,2)) as \"payUsers\"," +        //支付会员数
//            "cast(case when last_cycle_pay_user_num =0 and pay_user_num>0 then 1 " +
//            "when last_cycle_pay_user_num=0 and pay_user_num=0 then 0 " +
//            "when last_cycle_pay_user_num>0 and pay_user_num=0 then -1 " +
//            "else (pay_user_num-last_cycle_pay_user_num)::numeric/last_cycle_pay_user_num::numeric end as decimal(10,2)) as \"payUsersUp\", " +
//
//            "cast(order_counts as decimal(10,2)) as \"payCounts\"," +        //订单总数
//            "cast(case when last_cycle_order_counts =0 and order_counts>0 then 1 " +
//            "when last_cycle_order_counts=0 and order_counts=0 then 0 " +
//            "when last_cycle_order_counts>0 and order_counts=0 then -1 " +
//            "else (order_counts-last_cycle_order_counts)::numeric/last_cycle_order_counts::numeric end as decimal(10,2)) as \"payCountsUp\", " +
//
//            "cast(online_pay_amount as decimal(10,2)) as \"onlinePayAmount\", " +   //线上支付金额
//            "cast(case when last_cycle_online_pay_amount =0 and online_pay_amount>0 then 1 " +
//            "when last_cycle_online_pay_amount=0 and online_pay_amount=0 then 0 " +
//            "when last_cycle_online_pay_amount>0 and online_pay_amount=0 then -1 " +
//            "else (online_pay_amount-last_cycle_online_pay_amount)::numeric/last_cycle_online_pay_amount::numeric end as decimal(10,2)) as \"onlinePayAmountUp\"," +
//
//            "online_pay_count as \"onlinePayCounts\", " +       //线上支付订单数
//            "cast(case when last_cycle_online_pay_count =0 and online_pay_count>0 then 1 " +
//            "when online_pay_count=0 and last_cycle_online_pay_count=0 then 0 " +
//            "when online_pay_count=0 and last_cycle_online_pay_count>0 then -1 " +
//            "else (online_pay_count-last_cycle_online_pay_count)::numeric/last_cycle_online_pay_count::numeric end as decimal(10,2)) as \"onlinePayCountsUp\"," +
//
//            "cast(online_refund_amount as decimal(10,2)) as \"refundAmount\"," +
//            "cast(case when last_cycle_online_refund_amount =0 and online_refund_amount>0 then 1 when last_cycle_online_refund_amount=0 and online_refund_amount=0 then 0 else (online_refund_amount-last_cycle_online_refund_amount)::numeric/last_cycle_online_refund_amount::numeric end as decimal(10,2)) as \"refundAmountUp\"," +
//            "cast(online_refund_count as decimal(10,2)) as \"refundCounts\"," +
//            "cast(case when last_cycle_online_refund_count =0 and online_refund_count>0 then 1 when last_cycle_online_refund_count=0 and online_refund_count=0 then 0 else (online_refund_count-last_cycle_online_refund_count)::numeric/last_cycle_online_refund_count::numeric end as decimal(10,2)) as \"refundCountsUp\"," +
//            "cast(refund_user_num as decimal(10,2)) as \"refundUsers\"," +      //退款会员数
//            "cast(case when last_cycle_refund_user_num =0 and refund_user_num>0 then 1 when last_cycle_refund_user_num=0 and refund_user_num=0 then 0 else (refund_user_num-last_cycle_refund_user_num)::numeric/last_cycle_refund_user_num::numeric end as decimal(10,2)) as \"refundUsersUp\"," +
//
//            "cast(case when online_refund_count=0 then 0 else online_refund_amount::numeric/online_refund_count::numeric end as decimal(10,2)) as \"refundPerOrderAmount\"," +        //退款订单均价
//            "case when (online_refund_count =0 or online_refund_amount=0) and (last_cycle_online_refund_count = 0 or last_cycle_online_refund_amount=0) then 0 " +
//            "when (online_refund_count =0 or online_refund_amount=0) and last_cycle_online_refund_amount>0 then -1 "+
//            "when (last_cycle_online_refund_count = 0 or last_cycle_online_refund_amount=0) and online_refund_amount > 0 then 1 " +
//            "else cast( ((online_refund_amount::numeric/online_refund_count::numeric) - (last_cycle_online_refund_amount::numeric/last_cycle_online_refund_count::numeric))::numeric/(last_cycle_online_refund_amount::numeric/last_cycle_online_refund_count::numeric)::numeric as decimal(10,2)) end  as \"refundPerOrderAmountUp\", "+
//
//
//            "cast(case when order_counts=0 then 0 else pay_amount::numeric/order_counts::numeric end as decimal(10,2)) as \"perOrderAmount\"," +        //订单均价
//            "case when (order_counts =0 or pay_amount=0) and (last_cycle_order_counts = 0 or last_cycle_pay_amount=0) then 0 " +
//            "when (order_counts =0 or pay_amount=0) and last_cycle_pay_amount>0 then -1 " +
//            "when (last_cycle_order_counts = 0 or last_cycle_pay_amount=0) and pay_amount > 0 then 1 " +
//            "else cast( ((pay_amount::numeric/order_counts::numeric) - (last_cycle_pay_amount::numeric/last_cycle_order_counts::numeric))::numeric/(last_cycle_pay_amount::numeric/last_cycle_order_counts::numeric)::numeric as decimal(10,2)) end  as \"perOrderAmountUp\", "+
//
//            "cast(case when pay_user_num=0 then 0 else pay_amount::numeric/pay_user_num::numeric end as decimal(10,2)) as \"perUserAmount\"," +        //客单均价
//            "case when (pay_user_num =0 or pay_amount=0) and (last_cycle_pay_user_num = 0 or last_cycle_pay_amount=0) then 0 " +
//            "when (pay_user_num =0 or pay_amount=0) and last_cycle_pay_amount>0 then -1 " +
//            "when (last_cycle_pay_user_num = 0 or last_cycle_pay_amount=0) and pay_amount > 0 then 1 " +
//            "else cast( ((pay_amount::numeric/pay_user_num::numeric) - (last_cycle_pay_amount::numeric/last_cycle_pay_user_num::numeric))::numeric/(last_cycle_pay_amount::numeric/last_cycle_pay_user_num::numeric)::numeric as decimal(10,2)) end  as \"perUserAmountUp\", "+
//
//            "cast(case when online_pay_count=0 then 0 else online_pay_amount::numeric/online_pay_count::numeric end as decimal(10,2)) as \"onlinePerOrderAmount\"," +        //在线订单均价
//            "case when (online_pay_count =0 or online_pay_amount=0) and (last_cycle_online_pay_count = 0 or last_cycle_online_pay_amount=0) then 0 " +
//            "when (online_pay_count =0 or online_pay_amount=0) and last_cycle_online_pay_amount>0 then -1 " +
//            "when (last_cycle_online_pay_count = 0 or last_cycle_online_pay_amount=0) and online_pay_amount > 0 then 1 " +
//            "else cast( ((online_pay_amount::numeric/online_pay_count::numeric) - (last_cycle_online_pay_amount::numeric/last_cycle_online_pay_count::numeric))::numeric/(last_cycle_online_pay_amount::numeric/last_cycle_online_pay_count::numeric)::numeric as decimal(10,2)) end  as \"onlinePerOrderAmountUp\" "+
//
//            "";
//
//
//    public static final String EquipmentCountSql =
//            "sum(coalesce(pay_amount::numeric,0)) as \"sumPayAmount\", "+
//            "sum(coalesce(pay_user_num::numeric,0)) as \"sumPayUsers\", "+
//            "sum(coalesce(order_counts::numeric,0)) as \"sumOrderCounts\", "+
//            "sum(coalesce(online_start_counts::numeric,0)) as \"sumStartCounts\", "+
//            "count(distinct equipment_id) as \"sumEquipments\", "+
//            "cast(case when count(distinct equipment_id) =0 then sum(pay_amount)  else (sum(pay_amount))::numeric/count(distinct equipment_id)::numeric end as decimal(10,2)) as \"sumPerPayAmount\", " +
//            "cast(case when count(distinct equipment_id) =0 then sum(pay_user_num)  else (sum(pay_user_num))::numeric/count(distinct equipment_id)::numeric end as decimal(10,2)) as \"sumPerPayUsers\", " +
//            "cast(case when count(distinct equipment_id) =0 then sum(order_counts)  else (sum(order_counts))::numeric/count(distinct equipment_id)::numeric end as decimal(10,2)) as \"sumPerPayCounts\", " +
//            "cast(case when sum(order_counts) =0 then sum(pay_amount)  else (sum(pay_amount))::numeric/sum(order_counts)::numeric end as decimal(10,2)) as \"sumPerOrderAmount\", " +
//            "cast(case when count(distinct equipment_id) =0 then sum(coalesce(online_start_counts::numeric,0))  else (sum(coalesce(online_start_counts::numeric,0)))::numeric/count(distinct equipment_id)::numeric end as decimal(10,2)) as \"sumPerStartCounts\", " +
//            "cast(case when count(distinct equipment_id) =0 then sum(coalesce(pay_amount::numeric,0))  else (sum(coalesce(pay_amount::numeric,0)))::numeric/count(distinct equipment_id)::numeric end as decimal(10,2)) as \"sumPerUserOrderAmount\", " +
//
//
//            "cast(case when sum(coalesce(last_cycle_pay_amount::numeric,0)) =0 and sum(coalesce(pay_amount::numeric,0)) =0 then 0  " +
//            "when sum(coalesce(last_cycle_pay_amount::numeric,0)) =0 and sum(coalesce(pay_amount::numeric,0)) > 0 then 1"+
//            "else (sum(coalesce(pay_amount::numeric,0))-sum(coalesce(last_cycle_pay_amount::numeric,0)))::numeric/sum(coalesce(last_cycle_pay_amount::numeric,0))::numeric end as decimal(10,2)) as \"sumPayAmountUp\", " +
//
//            "cast(case when sum(coalesce(last_cycle_pay_user_num::numeric,0)) =0 and sum(coalesce(pay_user_num::numeric,0)) =0 then 0  " +
//            "when sum(coalesce(last_cycle_pay_user_num::numeric,0)) =0 and sum(coalesce(pay_user_num::numeric,0)) > 0 then 1"+
//            "else (sum(coalesce(pay_user_num::numeric,0))-sum(coalesce(last_cycle_pay_user_num::numeric,0)))::numeric/sum(coalesce(last_cycle_pay_user_num::numeric,0))::numeric end as decimal(10,2)) as \"sumPayUsersUp\", " +
//
//            "cast(case when sum(coalesce(last_cycle_order_counts::numeric,0)) =0 and sum(coalesce(order_counts::numeric,0)) =0 then 0  " +
//            "when sum(coalesce(last_cycle_order_counts::numeric,0)) =0 and sum(coalesce(order_counts::numeric,0)) > 0 then 1"+
//            "else (sum(coalesce(order_counts::numeric,0))-sum(coalesce(last_cycle_order_counts::numeric,0)))::numeric/sum(coalesce(last_cycle_order_counts::numeric,0))::numeric end as decimal(10,2)) as \"sumOrderCountsUp\", " +
//
//            "cast(case when sum(coalesce(last_cycle_online_start_counts::numeric,0)) =0 and sum(coalesce(online_start_counts::numeric,0)) =0 then 0  " +
//            "when sum(coalesce(last_cycle_online_start_counts::numeric,0)) =0 and sum(coalesce(online_start_counts::numeric,0)) > 0 then 1"+
//            "else (sum(coalesce(online_start_counts::numeric,0))-sum(coalesce(last_cycle_online_start_counts::numeric,0)))::numeric/sum(coalesce(last_cycle_online_start_counts::numeric,0))::numeric end as decimal(10,2)) as \"sumStartCountsUp\" " +
//
//
//            //"case when (online_pay_count =0 or online_pay_amount=0) and (last_cycle_online_pay_count = 0 or last_cycle_online_pay_amount=0) then 0 " +
//            //"when (online_pay_count =0 or online_pay_amount=0) and last_cycle_online_pay_amount>0 then -1 " +
//            //"when (last_cycle_online_pay_count = 0 or last_cycle_online_pay_amount=0) and online_pay_amount > 0 then 1 " +
//            //"else cast( ((online_pay_amount::numeric/online_pay_count::numeric) - (last_cycle_online_pay_amount::numeric/last_cycle_online_pay_count::numeric))::numeric/(last_cycle_online_pay_amount::numeric/last_cycle_online_pay_count::numeric)::numeric as decimal(10,2)) end  as \"onlinePerOrderAmountUp\" "+
//
//
//            "";
//
//    /*public static final String OrderCountSql =
//            "cast(case when count(distinct equipment_group_id) =0 then sum(pay_amount)  else (sum(pay_amount))::numeric/count(distinct equipment_group_id)::numeric end as decimal(10,2)) as \"sumPerPayAmount\", " +
//                    "cast(case when count(distinct equipment_group_id) =0 then sum(pay_count)  else (sum(pay_count))::numeric/count(distinct equipment_group_id)::numeric end as decimal(10,2)) as \"sumPerOrderCounts\", " +
//                    "cast(case when sum(pay_count) =0 then sum(pay_amount)  else (sum(pay_amount))::numeric/sum(pay_count)::numeric end as decimal(10,2)) as \"sumPerOrderAmount\" " +
//
//                    "";*/
//    public static final String OrderCountSql =
//            "sum(coalesce(online_pay_amount,0)) as \"sumPayAmount\", "+
//            "sum(coalesce(online_pay_count,0)) as \"sumPayCount\", "+
//                    "cast(case when sum(online_pay_count) =0 then sum(online_pay_amount)  else (sum(online_pay_amount))::numeric/sum(online_pay_count)::numeric end as decimal(10,2)) as \"sumPerOrderAmount\" " +
//
//                    "";
//    public static final String OrderGroupCountSql =
//            "cast(case when count(distinct equipment_group_id) =0 then sum(total_start_counts)  else (sum(total_start_counts))::numeric/count(distinct equipment_group_id)::numeric end as decimal(10,2)) as \"sumPerGroupStartCounts\", " +
//            "count(distinct equipment_group_id) as \"sumGroup\" " +
//                    "";
//
//    public static final String DayMemberSql =
//            "merchant_id as \"merchantId\", " +
//            "equipment_group_id as \"groupId\", " +
//            "coalesce(member_count::numeric,0) as \"memberCount\", " +
//            "coalesce(member_count_yesterday::numeric,0) as \"lastCycleMemberCount\", " +
//            "cast(case when coalesce(member_count_yesterday::numeric,0) =0 and coalesce(member_count::numeric,0) =0 then 0  " +
//            "when coalesce(member_count_yesterday::numeric,0) =0 and coalesce(member_count::numeric,0) > 0 then 1"+
//            "else (coalesce(member_count::numeric,0)-coalesce(member_count_yesterday::numeric,0))::numeric/coalesce(member_count_yesterday::numeric,0)::numeric end as decimal(10,2)) as \"memberCountUp\" " +
//
//            "";
//
//
//    public static final String WeekMemberSql =
//            "merchant_id as \"merchantId\", " +
//                    "equipment_group_id as \"groupId\", " +
//            "coalesce(member_count::numeric,0) as \"memberCount\", " +
//            "coalesce(last_week_member_count::numeric,0) as \"lastCycleMemberCount\", " +
//            "cast(case when coalesce(last_week_member_count::numeric,0) =0 and coalesce(member_count::numeric,0) =0 then 0  " +
//            "when coalesce(last_week_member_count::numeric,0) =0 and coalesce(member_count::numeric,0) > 0 then 1"+
//            "else (coalesce(member_count::numeric,0)-coalesce(last_week_member_count::numeric,0))::numeric/coalesce(last_week_member_count::numeric,0)::numeric end as decimal(10,2)) as \"memberCountUp\" " +
//
//            "";
//    public static final String MonthMemberSql =
//            "merchant_id as \"merchantId\", " +
//                    "equipment_group_id as \"groupId\", " +
//            "coalesce(member_count::numeric,0) as \"memberCount\", " +
//            "coalesce(last_month_member_count::numeric,0) as \"lastCycleMemberCount\", " +
//                    "cast(case when coalesce(last_month_member_count::numeric,0) =0 and coalesce(member_count::numeric,0) =0 then 0  " +
//                    "when coalesce(last_month_member_count::numeric,0) =0 and coalesce(member_count::numeric,0) > 0 then 1"+
//                    "else (coalesce(member_count::numeric,0)-coalesce(last_month_member_count::numeric,0))::numeric/coalesce(last_month_member_count::numeric,0)::numeric end as decimal(10,2)) as \"memberCountUp\" " +
//
//                    "";
//
//    public static final String YearMemberSql =
//            "merchant_id as \"merchantId\", " +
//                    "equipment_group_id as \"groupId\", " +
//            "coalesce(member_count::numeric,0) as \"memberCount\", " +
//            "coalesce(last_year_member_count::numeric,0) as \"lastCycleMemberCount\", " +
//                    "cast(case when coalesce(last_year_member_count::numeric,0) =0 and coalesce(member_count::numeric,0) =0 then 0  " +
//                    "when coalesce(last_year_member_count::numeric,0) =0 and coalesce(member_count::numeric,0) > 0 then 1"+
//                    "else (coalesce(member_count::numeric,0)-coalesce(last_year_member_count::numeric,0))::numeric/coalesce(last_year_member_count::numeric,0)::numeric end as decimal(10,2)) as \"memberCountUp\" " +
//
//                    "";
//
//
//    public static final String DayEquipmentTypeListSql =
//            "merchant_id as \"merchantId\", " +
//            "province_id as \"provinceId\", " +
//            "city_id as \"cityId\", " +
//            "district_id as \"districtId\", " +
//            "equipment_group_id as \"groupId\", " +
//            "lyy_equipment_type_id as \"equipmentTypeId\", " +
//            "equipment_type_name as \"equipmentTypeName\", " +
//            "cast(pay_amount as decimal(15,2)) as \"payAmount\"," +        //支付金额
//            "cast(case when pay_amount_yesterday =0 and pay_amount>0 then 1 " +
//            "when pay_amount_yesterday=0 and pay_amount=0 then 0 " +
//            "when pay_amount_yesterday>0 and pay_amount=0 then -1 " +
//            "else (pay_amount-pay_amount_yesterday)::numeric/pay_amount_yesterday::numeric end as decimal(15,2)) as \"payAmountUp\", " +
//
//            "cast(pay_count as decimal(15,0)) as \"payCount\"," +        //订单数
//            "cast(case when pay_count_yesterday =0 and pay_count>0 then 1 " +
//            "when pay_count_yesterday=0 and pay_count=0 then 0 " +
//            "when pay_count_yesterday>0 and pay_count=0 then -1 " +
//            "else (pay_count-pay_count_yesterday)::numeric/pay_count_yesterday::numeric end as decimal(15,2)) as \"payCountUp\", " +
//
//            "cast(case when pay_count=0 then 0 else pay_amount::numeric/pay_count::numeric end as decimal(10,2)) as \"perOrderAmount\"," +        //订单均价
//            "case when (pay_count =0 or pay_amount=0) and (pay_count_yesterday = 0 or pay_amount_yesterday=0) then 0 " +
//            "when (pay_count =0 or pay_amount=0) and pay_amount_yesterday>0 then -1 " +
//            "when (pay_count_yesterday = 0 or pay_amount_yesterday=0) and pay_amount > 0 then 1 " +
//            "else cast( ((pay_amount::numeric/pay_count::numeric) - (pay_amount_yesterday::numeric/pay_count_yesterday::numeric))::numeric/(pay_amount_yesterday::numeric/pay_count_yesterday::numeric)::numeric as decimal(10,2)) end  as \"perOrderAmountUp\" "+
//
//            "";
//
//    public static final String WeekEquipmentTypeListSql =
//            "merchant_id as \"merchantId\", " +
//            "province_id as \"provinceId\", " +
//            "city_id as \"cityId\", " +
//            "district_id as \"districtId\", " +
//            "equipment_group_id as \"groupId\", " +
//            "lyy_equipment_type_id as \"equipmentTypeId\", " +
//            "equipment_type_name as \"equipmentTypeName\", " +
//            "cast(pay_amount as decimal(15,2)) as \"payAmount\"," +        //支付金额
//            "cast(case when pay_amount_last_week =0 and pay_amount>0 then 1 " +
//            "when pay_amount_last_week=0 and pay_amount=0 then 0 " +
//            "when pay_amount_last_week>0 and pay_amount=0 then -1 " +
//            "else (pay_amount-pay_amount_last_week)::numeric/pay_amount_last_week::numeric end as decimal(15,2)) as \"payAmountUp\", " +
//
//                    "cast(pay_count as decimal(15,0)) as \"payCount\"," +        //订单数
//                    "cast(case when pay_count_last_week =0 and pay_count>0 then 1 " +
//                    "when pay_count_last_week=0 and pay_count=0 then 0 " +
//                    "when pay_count_last_week>0 and pay_count=0 then -1 " +
//                    "else (pay_count-pay_count_last_week)::numeric/pay_count_last_week::numeric end as decimal(15,2)) as \"payCountUp\", " +
//
//                    "cast(case when pay_count=0 then 0 else pay_amount::numeric/pay_count::numeric end as decimal(10,2)) as \"perOrderAmount\"," +        //订单均价
//                    "case when (pay_count =0 or pay_amount=0) and (pay_count_last_week = 0 or pay_amount_last_week=0) then 0 " +
//                    "when (pay_count =0 or pay_amount=0) and pay_amount_last_week>0 then -1 " +
//                    "when (pay_count_last_week = 0 or pay_amount_last_week=0) and pay_amount > 0 then 1 " +
//                    "else cast( ((pay_amount::numeric/pay_count::numeric) - (pay_amount_last_week::numeric/pay_count_last_week::numeric))::numeric/(pay_amount_last_week::numeric/pay_count_last_week::numeric)::numeric as decimal(10,2)) end  as \"perOrderAmountUp\" "+
//
//                    "";
//    public static final String MonthEquipmentTypeListSql =
//            "merchant_id as \"merchantId\", " +
//                    "province_id as \"provinceId\", " +
//                    "city_id as \"cityId\", " +
//                    "district_id as \"districtId\", " +
//                    "equipment_group_id as \"groupId\", " +
//                    "lyy_equipment_type_id as \"equipmentTypeId\", " +
//                    "equipment_type_name as \"equipmentTypeName\", " +
//                    "cast(pay_amount as decimal(15,2)) as \"payAmount\"," +        //支付金额
//                    "cast(case when pay_amount_last_month =0 and pay_amount>0 then 1 " +
//                    "when pay_amount_last_month=0 and pay_amount=0 then 0 " +
//                    "when pay_amount_last_month>0 and pay_amount=0 then -1 " +
//                    "else (pay_amount-pay_amount_last_month)::numeric/pay_amount_last_month::numeric end as decimal(15,2)) as \"payAmountUp\", " +
//
//                    "cast(pay_count as decimal(15,0)) as \"payCount\"," +        //订单数
//                    "cast(case when pay_count_last_month =0 and pay_count>0 then 1 " +
//                    "when pay_count_last_month=0 and pay_count=0 then 0 " +
//                    "when pay_count_last_month>0 and pay_count=0 then -1 " +
//                    "else (pay_count-pay_count_last_month)::numeric/pay_count_last_month::numeric end as decimal(15,2)) as \"payCountUp\", " +
//
//                    "cast(case when pay_count=0 then 0 else pay_amount::numeric/pay_count::numeric end as decimal(10,2)) as \"perOrderAmount\"," +        //订单均价
//                    "case when (pay_count =0 or pay_amount=0) and (pay_count_last_month = 0 or pay_amount_last_month=0) then 0 " +
//                    "when (pay_count =0 or pay_amount=0) and pay_amount_last_month>0 then -1 " +
//                    "when (pay_count_last_month = 0 or pay_amount_last_month=0) and pay_amount > 0 then 1 " +
//                    "else cast( ((pay_amount::numeric/pay_count::numeric) - (pay_amount_last_month::numeric/pay_count_last_month::numeric))::numeric/(pay_amount_last_month::numeric/pay_count_last_month::numeric)::numeric as decimal(10,2)) end  as \"perOrderAmountUp\" "+
//
//                    "";
//
//    public static final String YearEquipmentTypeListSql =
//            "merchant_id as \"merchantId\", " +
//                    "province_id as \"provinceId\", " +
//                    "city_id as \"cityId\", " +
//                    "district_id as \"districtId\", " +
//                    "equipment_group_id as \"groupId\", " +
//                    "lyy_equipment_type_id as \"equipmentTypeId\", " +
//                    "equipment_type_name as \"equipmentTypeName\", " +
//                    "cast(pay_amount as decimal(15,2)) as \"payAmount\"," +        //支付金额
//                    "cast(case when pay_amount_last_year =0 and pay_amount>0 then 1 " +
//                    "when pay_amount_last_year=0 and pay_amount=0 then 0 " +
//                    "when pay_amount_last_year>0 and pay_amount=0 then -1 " +
//                    "else (pay_amount-pay_amount_last_year)::numeric/pay_amount_last_year::numeric end as decimal(15,2)) as \"payAmountUp\", " +
//
//                    "cast(pay_count as decimal(15,0)) as \"payCount\"," +        //订单数
//                    "cast(case when pay_count_last_year =0 and pay_count>0 then 1 " +
//                    "when pay_count_last_year=0 and pay_count=0 then 0 " +
//                    "when pay_count_last_year>0 and pay_count=0 then -1 " +
//                    "else (pay_count-pay_count_last_year)::numeric/pay_count_last_year::numeric end as decimal(15,2)) as \"payCountUp\", " +
//
//                    "cast(case when pay_count=0 then 0 else pay_amount::numeric/pay_count::numeric end as decimal(10,2)) as \"perOrderAmount\"," +        //订单均价
//                    "case when (pay_count =0 or pay_amount=0) and (pay_count_last_year = 0 or pay_amount_last_year=0) then 0 " +
//                    "when (pay_count =0 or pay_amount=0) and pay_amount_last_year>0 then -1 " +
//                    "when (pay_count_last_year = 0 or pay_amount_last_year=0) and pay_amount > 0 then 1 " +
//                    "else cast( ((pay_amount::numeric/pay_count::numeric) - (pay_amount_last_year::numeric/pay_count_last_year::numeric))::numeric/(pay_amount_last_year::numeric/pay_count_last_year::numeric)::numeric as decimal(10,2)) end  as \"perOrderAmountUp\" "+
//
//                    "";
//
//
//    public static final String DayGroupListSql =
//            "merchant_id as \"merchantId\", " +
//            "province_id as \"provinceId\", " +
//            "city_id as \"cityId\", " +
//            "district_id as \"districtId\", " +
//            "lyy_equipment_type_id as \"equipmentTypeId\", " +
//            "equipment_group_id as \"groupId\", " +
//            "equipment_type_name as \"equipmentTypeName\", " +
//            "coalesce(equipment_count::numeric,0) as \"equipmentCounts\", " +
//            "cast(case when coalesce(equipment_count_yesterday::numeric,0) =0 and coalesce(equipment_count::numeric,0) =0 then 0  " +
//            "when coalesce(equipment_count_yesterday::numeric,0) =0 and coalesce(equipment_count::numeric,0) > 0 then 1"+
//            "else (coalesce(equipment_count::numeric,0)-coalesce(equipment_count_yesterday::numeric,0))::numeric/coalesce(equipment_count_yesterday::numeric,0)::numeric end as decimal(10,2)) as \"equipmentCountsUp\" " +
//
//            "";
//
//    public static final String WeekGroupListSql =
//            "merchant_id as \"merchantId\", " +
//                    "province_id as \"provinceId\", " +
//                    "city_id as \"cityId\", " +
//                    "district_id as \"districtId\", " +
//                    "lyy_equipment_type_id as \"equipmentTypeId\", " +
//                    "equipment_group_id as \"groupId\", " +
//                    "equipment_type_name as \"equipmentTypeName\", " +
//                    "coalesce(equipment_count::numeric,0) as \"equipmentCounts\", " +
//                    "cast(case when coalesce(last_cycle_equipment_count::numeric,0) =0 and coalesce(equipment_count::numeric,0) =0 then 0  " +
//                    "when coalesce(last_cycle_equipment_count::numeric,0) =0 and coalesce(equipment_count::numeric,0) > 0 then 1"+
//                    "else (coalesce(equipment_count::numeric,0)-coalesce(last_cycle_equipment_count::numeric,0))::numeric/coalesce(last_cycle_equipment_count::numeric,0)::numeric end as decimal(10,2)) as \"equipmentCountsUp\" " +
//
//                    "";
//
//    public static final String DayMemberListSql =
//            "day, "+
//            "merchant_id as \"merchantId\", " +
//            "province_id as \"provinceId\", " +
//            "city_id as \"cityId\", " +
//            "district_id as \"districtId\", " +
//            "equipment_type_id as \"equipmentTypeId\", " +
//            "equipment_group_id as \"groupId\", " +
//            "equipment_type_name as \"equipmentTypeName\", " +
//            "coalesce(member_count::numeric,0) as \"memberCount\", " +
//            "coalesce(member_count_yesterday::numeric,0) as \"lastCycleMemberCount\", " +
//            "cast(case when coalesce(member_count_yesterday::numeric,0) =0 and coalesce(member_count::numeric,0) =0 then 0  " +
//            "when coalesce(member_count_yesterday::numeric,0) =0 and coalesce(member_count::numeric,0) > 0 then 1"+
//            "else (coalesce(member_count::numeric,0)-coalesce(member_count_yesterday::numeric,0))::numeric/coalesce(member_count_yesterday::numeric,0)::numeric end as decimal(10,2)) as \"memberCountUp\" " +
//
//            "";
//    public static final String WeekMemberListSql =
//            "merchant_id as \"merchantId\", " +
//                    "month_week_num as \"week\", " +
//                    "province_id as \"provinceId\", " +
//                    "city_id as \"cityId\", " +
//                    "district_id as \"districtId\", " +
//                    "equipment_type_id as \"equipmentTypeId\", " +
//                    "equipment_group_id as \"groupId\", " +
//                    "equipment_type_name as \"equipmentTypeName\", " +
//                    "coalesce(member_count::numeric,0) as \"memberCount\", " +
//                    "coalesce(last_week_member_count::numeric,0) as \"lastCycleMemberCount\", " +
//                    "cast(case when coalesce(last_week_member_count::numeric,0) =0 and coalesce(member_count::numeric,0) =0 then 0  " +
//                    "when coalesce(last_week_member_count::numeric,0) =0 and coalesce(member_count::numeric,0) > 0 then 1"+
//                    "else (coalesce(member_count::numeric,0)-coalesce(last_week_member_count::numeric,0))::numeric/coalesce(last_week_member_count::numeric,0)::numeric end as decimal(10,2)) as \"memberCountUp\" " +
//
//                    "";
//    public static final String MonthMemberListSql =
//            "merchant_id as \"merchantId\", " +
//                    "year_month as \"month\", " +
//                    "province_id as \"provinceId\", " +
//                    "city_id as \"cityId\", " +
//                    "district_id as \"districtId\", " +
//                    "equipment_type_id as \"equipmentTypeId\", " +
//                    "equipment_group_id as \"groupId\", " +
//                    "equipment_type_name as \"equipmentTypeName\", " +
//                    "coalesce(member_count::numeric,0) as \"memberCount\", " +
//                    "coalesce(last_month_member_count::numeric,0) as \"lastCycleMemberCount\", " +
//                    "cast(case when coalesce(last_month_member_count::numeric,0) =0 and coalesce(member_count::numeric,0) =0 then 0  " +
//                    "when coalesce(last_month_member_count::numeric,0) =0 and coalesce(member_count::numeric,0) > 0 then 1"+
//                    "else (coalesce(member_count::numeric,0)-coalesce(last_month_member_count::numeric,0))::numeric/coalesce(last_month_member_count::numeric,0)::numeric end as decimal(10,2)) as \"memberCountUp\" " +
//
//                    "";
//    public static final String YearMemberListSql =
//            "merchant_id as \"merchantId\", " +
//                    "year_str as \"year\", " +
//                    "province_id as \"provinceId\", " +
//                    "city_id as \"cityId\", " +
//                    "district_id as \"districtId\", " +
//                    "equipment_type_id as \"equipmentTypeId\", " +
//                    "equipment_group_id as \"groupId\", " +
//                    "equipment_type_name as \"equipmentTypeName\", " +
//                    "coalesce(member_count::numeric,0) as \"memberCount\", " +
//                    "coalesce(last_year_member_count::numeric,0) as \"lastCycleMemberCount\", " +
//                    "cast(case when coalesce(last_year_member_count::numeric,0) =0 and coalesce(member_count::numeric,0) =0 then 0  " +
//                    "when coalesce(last_year_member_count::numeric,0) =0 and coalesce(member_count::numeric,0) > 0 then 1"+
//                    "else (coalesce(member_count::numeric,0)-coalesce(last_year_member_count::numeric,0))::numeric/coalesce(last_year_member_count::numeric,0)::numeric end as decimal(10,2)) as \"memberCountUp\" " +
//
//                    "";
//
//    //订单日表查询数据
//    public static final String EveryDayOrderQuerySql = "merchant_id as \"merchantId\", " +
//            "province_id as \"provinceId\", " +
//            "city_id as \"cityId\", " +
//            "district_id as \"districtId\", " +
//            "lyy_equipment_type_id as \"equipmentTypeId\", " +
//            "equipment_type_name as \"equipmentTypeName\", " +
//            "equipment_group_id as \"groupId\", " +
//            "equipment_group_name as \"groupName\", " +
//            "equipment_group_address as \"address\", " +
//
//            "cast(pay_amount as decimal(10,2)) as \"payAmount\"," +
//            "cast(case when pay_amount_yesterday =0 and pay_amount>0 then 1 " +
//            "when pay_amount_yesterday=0 and pay_amount=0 then 0 " +
//            "when pay_amount_yesterday>0 and pay_amount=0 then -1 " +
//            "else (pay_amount-pay_amount_yesterday)::numeric/pay_amount_yesterday::numeric end as decimal(10,2)) as \"payAmountUp\"," +
//
//            "pay_count as \"payCounts\"," +
//            "cast(case when pay_count_yesterday =0 and pay_count>0 then 1 " +
//            "when pay_count_yesterday=0 and pay_count=0 then 0 " +
//            "when pay_count_yesterday>0 and pay_count=0 then -1 " +
//            "else (pay_count-pay_count_yesterday)::numeric/pay_count_yesterday::numeric end as decimal(10,2)) as \"payCountsUp\"," +
//
//            "cast(case when pay_count=0 then 0 else pay_amount::numeric/pay_count::numeric end as decimal(10,2)) as \"perOrderAmount\"," +        //订单均价
//            "case when (pay_count =0 or pay_amount=0) and (pay_count_yesterday = 0 or pay_amount_yesterday=0) then 0 " +
//            "when (pay_count =0 or pay_amount=0) and  pay_amount_yesterday > 0 then -1 " +
//            "when (pay_count_yesterday = 0 or pay_amount_yesterday=0) and pay_amount > 0 then 1 " +
//            "else cast( ((pay_amount::numeric/pay_count::numeric) - (pay_amount_yesterday::numeric/pay_count_yesterday::numeric))::numeric/(pay_amount_yesterday::numeric/pay_count_yesterday::numeric)::numeric as decimal(10,2)) end  as \"perOrderAmountUp\","+
//
//            "day " ;
//
//
//    //订单日表查询数据
//    public static final String EveryWeekOrderQuerySql = "merchant_id as \"merchantId\", " +
//            "province_id as \"provinceId\", " +
//            "city_id as \"cityId\", " +
//            "district_id as \"districtId\", " +
//            "lyy_equipment_type_id as \"equipmentTypeId\", " +
//            "equipment_type_name as \"equipmentTypeName\", " +
//            "equipment_group_id as \"groupId\", " +
//            "equipment_group_name as \"groupName\", " +
//            "equipment_group_address as \"address\", " +
//
//
//            "cast(pay_amount as decimal(10,2)) as \"payAmount\"," +
//            "cast(case when pay_amount_last_week =0 and pay_amount>0 then 1 " +
//            "when pay_amount_last_week=0 and pay_amount=0 then 0 " +
//            "when pay_amount_last_week>0 and pay_amount=0 then -1 " +
//            "else (pay_amount-pay_amount_last_week)::numeric/pay_amount_last_week::numeric end as decimal(10,2)) as \"payAmountUp\"," +
//
//            "pay_count as \"payCounts\"," +
//            "cast(case when pay_count_last_week =0 and pay_count>0 then 1 " +
//            "when pay_count_last_week=0 and pay_count=0 then 0 " +
//            "when pay_count_last_week>0 and pay_count=0 then -1 " +
//            "else (pay_count-pay_count_last_week)::numeric/pay_count_last_week::numeric end as decimal(10,2)) as \"payCountsUp\"," +
//
//            "cast(case when pay_count=0 then 0 else pay_amount::numeric/pay_count::numeric end as decimal(10,2)) as \"perOrderAmount\"," +        //订单均价
//            "case when (pay_count =0 or pay_amount=0) and (pay_count_last_week = 0 or pay_amount_last_week=0) then 0 " +
//            "when (pay_count =0 or pay_amount=0) and  pay_amount_last_week > 0 then -1 " +
//            "when (pay_count_last_week = 0 or pay_amount_last_week=0) and pay_amount > 0 then 1 " +
//            "else cast( ((pay_amount::numeric/pay_count::numeric) - (pay_amount_last_week::numeric/pay_count_last_week::numeric))::numeric/(pay_amount_last_week::numeric/pay_count_last_week::numeric)::numeric as decimal(10,2)) end  as \"perOrderAmountUp\","+
//            " month_week_num as week "
//            ;
//
//    //订单日表查询数据
//    public static final String EveryMonthOrderQuerySql = "merchant_id as \"merchantId\", " +
//            "province_id as \"provinceId\", " +
//            "city_id as \"cityId\", " +
//            "district_id as \"districtId\", " +
//            "lyy_equipment_type_id as \"equipmentTypeId\", " +
//            "equipment_type_name as \"equipmentTypeName\", " +
//            "equipment_group_id as \"groupId\", " +
//            "equipment_group_name as \"groupName\", " +
//            "equipment_group_address as \"address\", " +
//
//            "cast(pay_amount as decimal(10,2)) as \"payAmount\"," +
//            "cast(case when pay_amount_last_month =0 and pay_amount>0 then 1 " +
//            "when pay_amount_last_month=0 and pay_amount=0 then 0 " +
//            "when pay_amount_last_month>0 and pay_amount=0 then -1 " +
//            "else (pay_amount-pay_amount_last_month)::numeric/pay_amount_last_month::numeric end as decimal(10,2)) as \"payAmountUp\"," +
//
//            "pay_count as \"payCounts\"," +
//            "cast(case when pay_count_last_month =0 and pay_count>0 then 1 " +
//            "when pay_count_last_month=0 and pay_count=0 then 0 " +
//            "when pay_count_last_month>0 and pay_count=0 then -1 " +
//            "else (pay_count-pay_count_last_month)::numeric/pay_count_last_month::numeric end as decimal(10,2)) as \"payCountsUp\"," +
//
//            "cast(case when pay_count=0 then 0 else pay_amount::numeric/pay_count::numeric end as decimal(10,2)) as \"perOrderAmount\"," +        //订单均价
//            "case when (pay_count =0 or pay_amount=0) and (pay_count_last_month = 0 or pay_amount_last_month=0) then 0 " +
//            "when (pay_count =0 or pay_amount=0) and  pay_amount_last_month > 0 then -1 " +
//            "when (pay_count_last_month = 0 or pay_amount_last_month=0) and pay_amount > 0 then 1 " +
//            "else cast( ((pay_amount::numeric/pay_count::numeric) - (pay_amount_last_month::numeric/pay_count_last_month::numeric))::numeric/(pay_amount_last_month::numeric/pay_count_last_month::numeric)::numeric as decimal(10,2)) end  as \"perOrderAmountUp\","+
//
//            "year_month as month"
//            ;
//
//    //订单日表查询数据
//    public static final String EveryYearOrderQuerySql = "merchant_id as \"merchantId\", " +
//            "province_id as \"provinceId\", " +
//            "city_id as \"cityId\", " +
//            "district_id as \"districtId\", " +
//            "lyy_equipment_type_id as \"equipmentTypeId\", " +
//            "equipment_type_name as \"equipmentTypeName\", " +
//            "equipment_group_id as \"groupId\", " +
//            "equipment_group_name as \"groupName\", " +
//            "equipment_group_address as \"address\", " +
//
//            "cast(pay_amount as decimal(10,2)) as \"payAmount\"," +
//            "cast(case when pay_amount_last_year =0 and pay_amount>0 then 1 " +
//            "when pay_amount_last_year=0 and pay_amount=0 then 0 " +
//            "when pay_amount_last_year>0 and pay_amount=0 then -1 " +
//            "else (pay_amount-pay_amount_last_year)::numeric/pay_amount_last_year::numeric end as decimal(10,2)) as \"payAmountUp\"," +
//
//            "pay_count as \"payCounts\"," +
//            "cast(case when pay_count_last_year =0 and pay_count>0 then 1 " +
//            "when pay_count_last_year=0 and pay_count=0 then 0 " +
//            "when pay_count_last_year>0 and pay_count=0 then -1 " +
//            "else (pay_count-pay_count_last_year)::numeric/pay_count_last_year::numeric end as decimal(10,2)) as \"payCountsUp\"," +
//
//            "cast(case when pay_count=0 then 0 else pay_amount::numeric/pay_count::numeric end as decimal(10,2)) as \"perOrderAmount\"," +        //订单均价
//            "case when (pay_count =0 or pay_amount=0) and (pay_count_last_year = 0 or pay_amount_last_year=0) then 0 " +
//            "when (pay_count =0 or pay_amount=0) and  pay_count_last_year > 0 then -1 " +
//            "when (pay_count_last_year = 0 or pay_amount_last_year=0) and pay_amount > 0 then 1 " +
//            "else cast( ((pay_amount::numeric/pay_count::numeric) - (pay_amount_last_year::numeric/pay_count_last_year::numeric))::numeric/(pay_amount_last_year::numeric/pay_count_last_year::numeric)::numeric as decimal(10,2)) end  as \"perOrderAmountUp\","+
//
//            "year_str as year"
//            ;
//    
//    
//    public static final String CITY_COMMON_COLUMN_ARRAY =  "city_name as \"cityName\""
//    		+" ,count(distinct equipment_id) as \"equipmentCount\""
//    		+" ,count(distinct equipment_group_id) as \"equipmentGroupCount\""
//    		+" ,sum(pay_amount) - sum(online_service_amount) as \"totalAmount\""
//    		+" ,sum(order_counts) as \"orderCount\""
//    		+" ,sum(total_start_counts) as \"totalStartCount\""
//    		+" ,cast(case when sum(order_counts) = 0 then 0 else (sum(pay_amount) - sum(online_service_amount)) / sum(order_counts) end as decimal(15,2)) as \"perOrderAmount\""
//    		+" ,cast(case when (sum(last_cycle_pay_amount) - sum(last_cycle_online_service_amount)) = 0 then 1 else (sum(pay_amount) - sum(online_service_amount) - sum(last_cycle_pay_amount) + sum(last_cycle_online_service_amount)) / (sum(last_cycle_pay_amount) - sum(last_cycle_online_service_amount)) end as decimal(15,2)) as \"amountUp\""
//    		+" ,cast(case when sum(last_cycle_order_counts) = 0 then 1 else (sum(order_counts) - sum(last_cycle_order_counts)) / sum(last_cycle_order_counts) end as decimal(15,2)) as \"orderCountUp\""
//    		+" ,cast(case when sum(last_cycle_total_start_counts) = 0 then 1 else (sum(total_start_counts) - sum(last_cycle_total_start_counts)) /sum(last_cycle_total_start_counts) end as decimal(15,2)) as \"startCountUp\"" ;
//
//    public static final String CITY_AVG_COLUMN_ARRAY = "cast(case when count(distinct city_name) =0 then 0 else sum(order_counts) / count(distinct city_name) end as decimal(15,2)) as \"avgOrderCount\""
//    		+ "	,cast(case when sum(order_counts) = 0 then 0 else (sum(pay_amount) - sum(online_service_amount)) / sum(order_counts) end as decimal(15,2)) as \"perOrderAmount\""
//    		+ "	,cast(case when count(distinct city_name) = 0 then 0 else sum(total_start_counts) /  count(distinct city_name) end as decimal(15,2)) as \"avgStartCount\""
//    		+ "	,cast(case when count(distinct city_name) = 0 then 0 else (sum(pay_amount) - sum(online_service_amount)) / count(distinct city_name) end as decimal(15,2)) as \"avgAmount\"" ;
//    
//    public static final String PROVINCE_COMMON_COLUMN_ARRAY = "province_name as \"provinceName\""
//    		+" ,count(distinct equipment_id) as \"equipmentCount\""
//    		+" ,count(distinct equipment_group_id) as \"equipmentGroupCount\""
//    		+" ,sum(pay_amount) - sum(online_service_amount) as \"totalAmount\""
//    		+" ,sum(order_counts) as \"orderCount\""
//    		+" ,sum(total_start_counts) as \"totalStartCount\""
//    		+" ,cast(case when sum(order_counts) = 0 then 0 else (sum(pay_amount) - sum(online_service_amount)) / sum(order_counts) end as decimal(15,2)) as \"perOrderAmount\""
//    		+" ,cast(case when (sum(last_cycle_pay_amount) - sum(last_cycle_online_service_amount)) = 0 then 1 else (sum(pay_amount) - sum(online_service_amount) - sum(last_cycle_pay_amount) + sum(last_cycle_online_service_amount)) / (sum(last_cycle_pay_amount) - sum(last_cycle_online_service_amount)) end as decimal(15,2)) as \"amountUp\""
//    		+" ,cast(case when sum(last_cycle_order_counts) = 0 then 1 else (sum(order_counts) - sum(last_cycle_order_counts)) / sum(last_cycle_order_counts) end as decimal(15,2)) as \"orderCountUp\""
//    		+" ,cast(case when sum(last_cycle_total_start_counts) = 0 then 1 else (sum(total_start_counts) - sum(last_cycle_total_start_counts)) /sum(last_cycle_total_start_counts) end as decimal(15,2)) as \"startCountUp\"" ;
//
//    public static final String PROVINCE_AVG_COLUMN_ARRAY = "cast(case when count(distinct province_name) =0 then 0 else sum(order_counts) / count(distinct province_name) end as decimal(15,2)) as \"avgOrderCount\""
//    		+ "	,cast(case when sum(order_counts) = 0 then 0 else (sum(pay_amount) - sum(online_service_amount)) / sum(order_counts) end as decimal(15,2)) as \"perOrderAmount\""
//    		+ "	,cast(case when count(distinct province_name) = 0 then 0 else sum(total_start_counts) /  count(distinct province_name) end as decimal(15,2)) as \"avgStartCount\""
//    		+ "	,cast(case when count(distinct province_name) = 0 then 0 else (sum(pay_amount) - sum(online_service_amount)) / count(distinct province_name) end as decimal(15,2)) as \"avgAmount\"" ;
//    
}
