package cn.lyy.merchant.service.multistore;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.merchant.dto.multistore.MultiStoreListBizReqDTO;
import cn.lyy_dto.wash.multistore.MultiStorePointDetailDTO;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/22 星期六 10:17
 * @description:
 */
public interface MultiStoreManageService {


    BaseResponse<List<MultiStorePointDetailDTO>> queryStoreLabel(MultiStoreListBizReqDTO request);


    List<Long> listSubStoreId(Long merchantId, Long storeId);

    List<Long> listSubStoreIdAndCurrentStoreId(Integer adOrgId, Integer groupId, Integer dataType);

    List<Long> getSubStoreIdAndCurrentStoreId(Long merchantId, Long storeId, Integer dataType);

    List<Long> listSubStoreIdAManyAndParamStoreId(Long merchantId, List<Long> storeIds, Integer dataType);

    List<Long> listSubStoreIdMany(Long merchantId, List<Long> storeIdList);
}
