package cn.lyy.merchant.service.multistore.impl;

import static java.util.Optional.ofNullable;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.base.utils.exception.BizException;
import cn.lyy.lyy_api.multistore.MultiStoreClient;
import cn.lyy.merchant.api.service.MerchantWhiteClient;
import cn.lyy.merchant.constant.LyyDistributorConstants.WhiteListType;
import cn.lyy.merchant.constants.MultiStoreBizTypeEnum;
import cn.lyy.merchant.dto.multistore.MultiStoreListBizReqDTO;
import cn.lyy.merchant.service.multistore.MultiStoreManageService;
import cn.lyy.merchant.service.multistore.strategy.AbstractMultiStoreBizHandler;
import cn.lyy.merchant.service.multistore.strategy.MultiStoreBizHandlerStrategyContext;
import cn.lyy_dto.constants.BusinessDataTypeEnum;
import cn.lyy_dto.wash.multistore.MultiStorePointDetailDTO;
import cn.lyy_dto.wash.multistore.request.MultiStoreStoreListQueryDTO;
import cn.lyy_dto.wash.multistore.request.MultiStoreStoreQueryDTO;
import com.google.common.collect.Lists;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/2/22 星期六 10:18
 * @description:
 */
@Slf4j
@Service
public class MultiStoreManageServiceImpl implements MultiStoreManageService {


    @Autowired
    private MultiStoreClient multiStoreClient;

    @Autowired
    private MerchantWhiteClient merchantWhiteClient;

    @Autowired
    private MultiStoreBizHandlerStrategyContext multiStoreBizHandlerStrategyContext;

    private void checkMultiStoreWhiteType(Long merchantId) {
        // 1. 判断商家是否在白名单内
        Boolean isWhiteMerchant = ofNullable(merchantWhiteClient.isWhiteDistributor(merchantId, WhiteListType.MULTI_STORE_MANAGE_WHITE.getValue())).map(
                BaseResponse::getData).orElse(false);

        if (!isWhiteMerchant) {
            throw new BizException("当前商户不在白名单内");
        }
    }


    @Override
    public BaseResponse<List<MultiStorePointDetailDTO>> queryStoreLabel(MultiStoreListBizReqDTO request) {
        checkMultiStoreWhiteType(request.getMerchantId());
        if (StringUtils.isBlank(request.getBizType())) {
            log.info("没有传bizType,默认查询");
            request.setBizType(MultiStoreBizTypeEnum.NORMAL_QUERY.getCode());
        }
        AbstractMultiStoreBizHandler abstractBusinessHandler = multiStoreBizHandlerStrategyContext.loadStrategy(request.getBizType());
        if (Objects.isNull(abstractBusinessHandler)) {
            throw new BizException("没有对应处理类: " + request.getBizType());
        }
        return new BaseResponse<>(abstractBusinessHandler.handle(request));
    }



    @Override
    public List<Long> listSubStoreId(Long merchantId, Long storeId) {
        if (storeId == null || merchantId == null) {
            log.warn("商家或场地ID为空");
            return Lists.newArrayList();
        }
        MultiStoreStoreQueryDTO dto = new MultiStoreStoreQueryDTO();
        dto.setMerchantId(merchantId);
        dto.setStoreId(storeId);
        BaseResponse<List<Long>> response = multiStoreClient.listSubStoreId(dto);
        return ResponseUtils.checkResponse(response);
    }

    @Override
    public List<Long> listSubStoreIdAndCurrentStoreId(Integer adOrgId, Integer groupId, Integer dataType) {
        Long merchantId = Optional.ofNullable(adOrgId).map(Integer::longValue).orElse(null);
        Long storeId = Optional.ofNullable(groupId).map(Integer::longValue).orElse(null);
        return getSubStoreIdAndCurrentStoreId(merchantId, storeId, dataType);
    }

    @Override
    public List<Long> getSubStoreIdAndCurrentStoreId(Long merchantId, Long storeId, Integer dataType) {
        return listSubStoreIdAManyAndParamStoreId(merchantId, Optional.ofNullable(storeId).map(Lists::newArrayList).orElse(new ArrayList<>()), dataType);
    }


    /**
     * 子场地及当前节点
     *
     * @param merchantId
     * @param storeIds
     * @param dataType
     * @return
     */
    @Override
    public List<Long> listSubStoreIdAManyAndParamStoreId(Long merchantId, List<Long> storeIds, Integer dataType) {
        log.debug("listSubStoreIdAManyAndParamStoreId:{},{},{}", merchantId, storeIds, dataType);
        if (!Objects.equals(BusinessDataTypeEnum.MULTI_STORE.getType(), dataType)) {
            return storeIds;
        }
        log.info("listSubStoreIdAManyAndParamStoreId:{},{},{}", merchantId, storeIds, dataType);
        if (Objects.isNull(merchantId)) {
            log.error("merchantId is null");
            return storeIds;
        }
        Set<Long> storeIdList = Optional.ofNullable(storeIds).orElse(new ArrayList<>())
                .stream().filter(Objects::nonNull)
                .collect(Collectors.toSet());
        if (org.apache.commons.collections.CollectionUtils.isEmpty(storeIdList)) {
            log.error("storeIdList is null");
            return Lists.newArrayList();
        }
        storeIds = new ArrayList<>(storeIdList);
        List<Long> subStoreIdList = listSubStoreIdMany(merchantId, storeIds);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(subStoreIdList)) {
            storeIds.addAll(subStoreIdList);
            log.info("根据多个场地ID查询子场地数据:{},{},  subStoreIdList:{}", merchantId, storeIds, subStoreIdList);
            return storeIds;
        }
        return Lists.newArrayList(new HashSet<>(storeIds));
    }

    @Override
    public List<Long> listSubStoreIdMany(Long merchantId, List<Long> storeIdList) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(storeIdList) || merchantId == null) {
            log.error("商家ID为空或场地ID为空,merchantId:{} storeIdList:{}", merchantId, storeIdList);
            return Lists.newArrayList();
        }
        MultiStoreStoreListQueryDTO dto = new MultiStoreStoreListQueryDTO();
        dto.setMerchantId(merchantId);
        dto.setStoreIdList(storeIdList);
        BaseResponse<List<Long>> response = multiStoreClient.listSubStoreIdMany(dto);
        log.debug("根据多个场地ID查询子场地数据:{}", response);
        return ResponseUtils.checkResponse((response));
    }
}
