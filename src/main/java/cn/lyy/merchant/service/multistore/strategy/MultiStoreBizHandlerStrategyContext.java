package cn.lyy.merchant.service.multistore.strategy;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 多级场地业务处理策略上下文
 * <AUTHOR>
 */
@Slf4j
@Component
public class MultiStoreBizHandlerStrategyContext {

    private final Map<String, AbstractMultiStoreBizHandler> strategyMap = new HashMap<>();

    @Autowired
    public MultiStoreBizHandlerStrategyContext(List<AbstractMultiStoreBizHandler> strategies) {
        for (AbstractMultiStoreBizHandler strategy : strategies) {
            this.strategyMap.put(strategy.getBizType().getCode(), strategy);
        }
    }

    public AbstractMultiStoreBizHandler loadStrategy(String fnOrMsgType) {
        return this.strategyMap.get(fnOrMsgType);
    }
}
