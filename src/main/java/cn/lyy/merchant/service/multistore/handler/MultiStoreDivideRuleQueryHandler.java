package cn.lyy.merchant.service.multistore.handler;

import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.base.utils.exception.BizException;
import cn.lyy.lyy_api.util.MultiStoreTreeFilter;
import cn.lyy.merchant.api.service.rule.DivideRuleGroupEquipmentClient;
import cn.lyy.merchant.constants.MultiStoreBizTypeEnum;
import cn.lyy.merchant.dto.equipment.rule.DivideRuleGroupEquipmentInfoRequestDTO;
import cn.lyy.merchant.dto.equipment.rule.DivideRuleGroupEquipmentInfoResponseDTO;
import cn.lyy.merchant.dto.multistore.MultiStoreListBizReqDTO;
import cn.lyy.merchant.service.multistore.strategy.AbstractMultiStoreBizHandler;
import cn.lyy_dto.wash.multistore.MultiStorePointDetailDTO;
import com.github.pagehelper.PageInfo;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * 分成规则查询处理类
 */
@Component("multiStoreDivideRuleQueryHandler")
@Slf4j
@RequiredArgsConstructor
public class MultiStoreDivideRuleQueryHandler extends AbstractMultiStoreBizHandler {




    private final DivideRuleGroupEquipmentClient divideRuleGroupEquipmentClient;

    @Override
    public MultiStoreBizTypeEnum getBizType() {
        log.debug("bizType {}",MultiStoreBizTypeEnum.DIVIDE_STORE.getDescription());
        return MultiStoreBizTypeEnum.DIVIDE_STORE;
    }

    @Override
    public List<MultiStorePointDetailDTO> handle(MultiStoreListBizReqDTO request) {
        DivideRuleGroupEquipmentInfoRequestDTO param = request.getDivideRuleStoreReqDTO();
        param.setMerchantId(request.getMerchantId());
        param.setAdUserId(request.getAdUserId());
        param.setPageSize(10000);
        PageInfo<DivideRuleGroupEquipmentInfoResponseDTO> pageResponse =   ResponseUtils.checkResponse(divideRuleGroupEquipmentClient.getAdUserGroupEquipmentCount(param));
        List<DivideRuleGroupEquipmentInfoResponseDTO> divideRuleList = pageResponse.getList();
        if(Objects.isNull(divideRuleList)){
            divideRuleList = new ArrayList<>();
        }
        Map<Long, DivideRuleGroupEquipmentInfoResponseDTO> bizDataMap = divideRuleList.stream()
                .collect(Collectors.toMap(DivideRuleGroupEquipmentInfoResponseDTO::getStoreId, Function.identity()));
        log.debug("bizDataMap.size: {}", bizDataMap.size());

        //3. 查询树结构 && 过滤返回结果
        List<MultiStorePointDetailDTO> filterResultList = filterValidNodes(bizDataMap, MultiStorePointDetailDTO::getStoreId,
                MultiStorePointDetailDTO::setBizTypeResultDTO, request
        );
        // 4. 累加ecount到父级场地
        Map<String, Function<Object, Integer>> extractorMap = MultiStoreTreeFilter.buildFieldExtractorMap(
                Arrays.asList("equipmentCount")
        );
        return MultiStoreTreeFilter.computeBizData(filterResultList, extractorMap);
    }





    @Override
    protected void validateRequest(MultiStoreListBizReqDTO request) {
        super.commonValidateRequest(request);
        DivideRuleGroupEquipmentInfoRequestDTO bizReqDTO = request.getDivideRuleStoreReqDTO();
        if (Objects.isNull(bizReqDTO)
        ) {
            log.debug("bizReqDTO参数错误");
            throw new BizException("参数错误");
        }
    }
}
