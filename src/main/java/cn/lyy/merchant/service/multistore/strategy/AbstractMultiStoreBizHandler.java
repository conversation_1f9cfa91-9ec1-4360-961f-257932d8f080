package cn.lyy.merchant.service.multistore.strategy;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.base.utils.exception.BizException;
import cn.lyy.base.utils.text.JSONObjectUtils;
import cn.lyy.lyy_api.multistore.MultiStoreClient;
import cn.lyy.lyy_api.util.MultiStoreTreeFilter;
import cn.lyy.merchant.constants.MultiStoreBizTypeEnum;
import cn.lyy.merchant.dto.multistore.MultiStoreListBizReqDTO;
import cn.lyy_dto.wash.multistore.MultiStorePointDetailDTO;
import com.google.common.collect.Lists;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @date 2025/6/11 星期三 14:20
 * @description:
 */
@Slf4j
public abstract class AbstractMultiStoreBizHandler {

    @Autowired
    private MultiStoreClient multiStoreClient;


    public abstract List<MultiStorePointDetailDTO> handle(MultiStoreListBizReqDTO request);


    /**
     * 获取策略类型
     *
     * @return type
     */
    public abstract MultiStoreBizTypeEnum getBizType();


    protected abstract void validateRequest(MultiStoreListBizReqDTO request);
    protected void commonValidateRequest(MultiStoreListBizReqDTO request) {
        if (request == null) {
            throw new BizException("参数错误");
        }
    }

    public BaseResponse<List<MultiStorePointDetailDTO>> queryStoreLabel(MultiStoreListBizReqDTO request) {
        BaseResponse<List<MultiStorePointDetailDTO>> listBaseResponse = multiStoreClient.queryStoreLabel(request);
        log.debug("多级场地管理列表返回:{}", listBaseResponse);
        return listBaseResponse;
    }

    public List<MultiStorePointDetailDTO> queryMultiStoreList(MultiStoreListBizReqDTO request) {
        BaseResponse<List<MultiStorePointDetailDTO>> listBaseResponse = queryStoreLabel(request);
        return Optional.ofNullable(ResponseUtils.checkResponse(listBaseResponse)).orElse(Lists.newArrayList());
    }

    /*
     **
     * 过滤树结构，仅保留包含有效场地的节点及其祖先路径，同时删除无效兄弟节点
     */
    public static <K, V> List<MultiStorePointDetailDTO> filterValidNodes(Map<K, V> bizDataMap,
            Function<MultiStorePointDetailDTO, K> keyExtractor, BiConsumer<MultiStorePointDetailDTO, V> resultSetter,
            List<MultiStorePointDetailDTO> nodeList) {
        return MultiStoreTreeFilter.filterValidNodes(bizDataMap, keyExtractor, resultSetter, nodeList);
    }

    protected <K, V> List<MultiStorePointDetailDTO> filterValidNodes(Map<K, V> bizDataMap,
            Function<MultiStorePointDetailDTO, K> keyExtractor, BiConsumer<MultiStorePointDetailDTO, V> resultSetter,
            MultiStoreListBizReqDTO request) {
        log.debug("bizDataMap.size: {},[{}]", bizDataMap.size(), JSONObjectUtils.toJSONString(bizDataMap));
        List<MultiStorePointDetailDTO> dtoList = queryMultiStoreList(request);
        log.debug("dtoList.size: {},list[{}]", dtoList.size(),JSONObjectUtils.toJSONString(dtoList));
        if (CollectionUtils.isEmpty(dtoList)) {
            log.warn("查询场地数据为空");
            return org.assertj.core.util.Lists.emptyList();
        }
        return filterValidNodes(bizDataMap, keyExtractor, resultSetter, dtoList);
    }

}
