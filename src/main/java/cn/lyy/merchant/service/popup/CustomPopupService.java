package cn.lyy.merchant.service.popup;

import cn.lyy.merchant.dto.popup.MultiPopCheckDTO;
import cn.lyy.merchant.dto.popup.PopReceivedDTO;
import cn.lyy.merchant.dto.popup.PopResult;

/**
 * 本地通、乐摇摇知识讲坛弹窗控制
 * 临时使用，后续弹窗会有通用功能控制
 */
public interface CustomPopupService {

    /**
     * 检查是否需要弹窗
     * 
     * @param type 1 -- 本地通；2 -- 知识讲坛
     * @param adUserId 当前用户id
     * @param merchantId 
     * @return
     */
    boolean checkIsNeedPop(int type, Long adUserId, Long merchantId);

    PopResult checkIsNeedPopMulti(MultiPopCheckDTO dto);
    
    void multiReceived(PopReceivedDTO dto);

    /**
     * 用户关闭弹窗记录已收到
     * 
     * @param type 1 -- 本地通；2 -- 知识讲坛
     * @param adUserId
     */
    void received(int type, Long adUserId);

    /**
     * 清除记录，测试用
     * 
     * @param adUserId
     */
    void clearRecord(Long adUserId);

    void clearMultiRecord(PopReceivedDTO dto);
}
