package cn.lyy.merchant.service.popup.impl;

import static java.util.Optional.ofNullable;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.lyy.merchant.api.service.MerchantEquipmentService;
import cn.lyy.merchant.dto.popup.MultiPopCheckDTO;
import cn.lyy.merchant.dto.popup.PopReceivedDTO;
import cn.lyy.merchant.dto.popup.PopResult;
import cn.lyy.merchant.redis.MerchantRedisClient;
import cn.lyy.merchant.redis.MerchantRedisKeyEnum;
import cn.lyy.merchant.service.popup.CustomPopupService;
import cn.lyy.merchant.utils.RemoteResponseUtils;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * @date: 2022/12/27
 * @author: YL
 */
@Service
public class CustomPopupServiceImpl implements CustomPopupService {

    @Autowired
    private MerchantEquipmentService merchantEquipmentService;

    @Value("${custom.popup.status:3}")
    private Integer customPopupStatus;

    /**
     * 2022一年内活动娱乐商家（兑币机、娃娃机、扭蛋机）数量不到11万，使用不同的HASH存储弹窗记录，每个不超5000，总bucket数取32,完全能满足且有富余
     */
    private static final long BUCKET = 31;

    private static final String SCRIPT = "local current = redis.call('HSET', KEYS[1], ARGV[1], ARGV[2]); "
            + " local t = redis.call('TTL',KEYS[1]); if t == -1 then redis.call('EXPIREAT',KEYS[1],ARGV[3]) end; return current";

    /**
     * prefix:bucket:day
     */
    private static final String KEY_PATTERN = "%s%d:%d";

    private static final List<String> CHECKED_TYPE_VALUES = Arrays.asList("DBJ", "WWJ", "NDJ");

    @Override
    public boolean checkIsNeedPop(int type, Long adUserId, Long merchantId) {
        if ((customPopupStatus & type) == 0) {
            return false;
        }

        if (checkUnMatchEquipmentTypes(CHECKED_TYPE_VALUES, merchantId)) {
            return false;
        }

        String key = generateKey(MerchantRedisKeyEnum.MERCHANT_USER_CUSTOM_TEMPORARY_POPUP_RECORD.getKey(), adUserId);
        String value = MerchantRedisClient.hgetByKey(key, adUserId.toString());

        // 可能是redis挂了，弹就是
        if (StrUtil.isBlank(value)) {
            return true;
        }

        int record = Integer.parseInt(value);

        return (record & type) == 0;
    }

    @Override
    public PopResult checkIsNeedPopMulti(MultiPopCheckDTO dto) {
        List<Integer> types = dto.getTypes().stream().filter(type -> (customPopupStatus & type) != 0).collect(Collectors.toList());
        if (CollUtil.isEmpty(types)) {
            return new PopResult(false, null);
        }

        if (checkUnMatchEquipmentTypes(dto.getCheckEquipmentTypeValues(), dto.getMerchantId())) {
            return new PopResult(false, null);
        }
        
        String key = generateKey(dto.getKeyPrefix(), dto.getUserId(), dto.getBucket());
        String value = MerchantRedisClient.hgetByKey(key, dto.getUserId().toString());

        // 可能是redis挂了，弹就是
        if (StrUtil.isBlank(value)) {
            return new PopResult(true, types.get(0));
        }

        int record = Integer.parseInt(value);

        for (Integer type : types) {
            if ((type & record) == 0) {
                return new PopResult(true, type);
            }
        }
        
        return new PopResult(false, null);
    }

    private boolean checkUnMatchEquipmentTypes(List<String> equipmentTypeValues, Long merchantId) {
        if (CollUtil.isEmpty(equipmentTypeValues)) {
            return false;
        }
        
        return RemoteResponseUtils.getData(merchantEquipmentService
                .countEquipmentByMerchantIdAndTypeValues(merchantId, equipmentTypeValues), 0) <= 0;
    }

    @Override
    public void multiReceived(PopReceivedDTO dto) {
        Long adUserId = dto.getUserId();
        
        String key = generateKey(dto.getKeyPrefix(), adUserId, dto.getBucket());

        String value = MerchantRedisClient.hgetByKey(key, adUserId.toString());
        Integer record = ofNullable(value).filter(StrUtil::isNotBlank).map(Integer::parseInt).orElse(0);
        String newValue = String.valueOf(record | dto.getType());

        MerchantRedisClient.execute(Long.class, SCRIPT, Collections.singletonList(key), adUserId.toString(), newValue,
                String.valueOf(unixTimestampOfTomorrowStart()));
    }

    @Override
    public void received(int type, Long adUserId) {
        String key = generateKey(MerchantRedisKeyEnum.MERCHANT_USER_CUSTOM_TEMPORARY_POPUP_RECORD.getKey(), adUserId);

        String value = MerchantRedisClient.hgetByKey(key, adUserId.toString());
        Integer record = ofNullable(value).filter(StrUtil::isNotBlank).map(Integer::parseInt).orElse(0);
        String newValue = String.valueOf(record | type);

        MerchantRedisClient.execute(Long.class, SCRIPT, Collections.singletonList(key), adUserId.toString(), newValue,
                String.valueOf(unixTimestampOfTomorrowStart()));
    }

    @Override
    public void clearRecord(Long adUserId) {
        String key = generateKey(MerchantRedisKeyEnum.MERCHANT_USER_CUSTOM_TEMPORARY_POPUP_RECORD.getKey(), adUserId);

        MerchantRedisClient.hdel(key, adUserId.toString());
    }

    @Override
    public void clearMultiRecord(PopReceivedDTO dto) {
        String key = generateKey(dto.getKeyPrefix(), dto.getUserId(), dto.getBucket());

        MerchantRedisClient.hdel(key, dto.getUserId().toString());
    }

    private String generateKey(String prefix, long adUserId) {
        return String.format(KEY_PATTERN, prefix, findBucket(adUserId, BUCKET), getDay());
    }

    private String generateKey(String prefix, long adUserId, long bucket) {
        return String.format(KEY_PATTERN, prefix, findBucket(adUserId, bucket), getDay());
    }

    private int getDay() {
        return LocalDate.now(ZoneId.systemDefault()).getDayOfMonth();
    }

    private long findBucket(Long adUserId, long bucket) {
        return adUserId & bucket;
    }

    private long unixTimestampOfTomorrowStart() {
        LocalDate startOfTomorrow = LocalDate.now(ZoneId.systemDefault()).plusDays(1L);
        return startOfTomorrow.atStartOfDay().toEpochSecond(ZoneOffset.ofHours(8)) + new Random().nextInt(30);
    }
}
