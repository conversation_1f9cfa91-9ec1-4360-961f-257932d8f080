package cn.lyy.merchant.service.divide.handler;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.merchant.api.service.DivideRuleClient;
import cn.lyy.merchant.api.service.rule.DivideDefaultRuleRelationClient;
import cn.lyy.merchant.constant.merchantDivide.MerchantDivideConditonTypeCodeEnum;
import cn.lyy.merchant.constant.merchantDivide.MerchantDivideDistributorTypeEnum;
import cn.lyy.merchant.dto.divide.DivideConditionRequestDTO;
import cn.lyy.merchant.dto.divide.DivideRuleConfigDTO;
import cn.lyy.merchant.dto.divide.DivideRuleStoreConfigDTO;
import cn.lyy.merchant.dto.divide.MerchantEquipmentDivideDTO;
import cn.lyy.merchant.dto.divide.MerchantStoreDivideDTO;
import cn.lyy.merchant.dto.divide.enums.DivideDefaultRuleRelationEnum;
import cn.lyy.merchant.dto.divide.enums.DivideRuleEquipmentRelationEnum;
import cn.lyy.merchant.dto.divide.request.DivideDefaultRuleRelationDTO;
import cn.lyy.merchant.dto.divide.request.DivideRuleEquipmentRelationLogSaveDTO;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import com.alibaba.fastjson.JSON;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import static java.util.Optional.ofNullable;

/**
 * <AUTHOR>
 * @date 2023/5/30
 */
@Component
@Slf4j
public class DivideAsyncHandler {
    
    @Autowired
    private DivideDefaultRuleRelationClient divideDefaultRuleRelationClient;


    @Autowired
    private DivideRuleClient divideRuleClient;

    @Async
    public void saveDivideRuleEquipmentRelationLog(AdUserInfoDTO userInfoDTO, String operateModule, String operateContent) {
        try {
            DivideRuleEquipmentRelationLogSaveDTO saveDTO = new DivideRuleEquipmentRelationLogSaveDTO();
            saveDTO.setAdOrgId(userInfoDTO.getAdOrgId());
            saveDTO.setBelongType(DivideRuleEquipmentRelationEnum.MERCHANT_BELONG_TYPE.getKey());
            saveDTO.setCreated(LocalDateTime.now());
            saveDTO.setOperateAccount(userInfoDTO.getPhone());
            saveDTO.setOperator(userInfoDTO.getUserName());
            saveDTO.setOperateModule(operateModule);
            saveDTO.setOperateContent(operateContent);
            divideDefaultRuleRelationClient.saveDivideRuleEquipmentRelationLog(saveDTO);
        } catch (Exception e) {
            log.error("[抽成关联关系日志] 异常 :{} 操作模块:{} 操作内容:{}", userInfoDTO, operateModule, operateContent, e);
        }
    }

    @Async
    public void batchConditionAddOrCancel(DivideRuleConfigDTO dto, AdUserInfoDTO userInfoDTO) {
        List<String> equipmentValueList = ofNullable(dto.getMerchantEquipmentDTOList()).orElse(new ArrayList<>()).stream().distinct().map(MerchantEquipmentDivideDTO::getEquipmentValue).collect(Collectors.toList());
        List<String> excludeEquipments = ofNullable(dto.getExcludeEquipments()).orElse(new ArrayList<>()).stream().distinct().map(MerchantEquipmentDivideDTO::getEquipmentValue).collect(Collectors.toList());

        DivideConditionRequestDTO requestDTO = new DivideConditionRequestDTO();
        requestDTO.setConditionType(MerchantDivideConditonTypeCodeEnum.EQUIPMENT.getCode());
        requestDTO.setDistributorId(userInfoDTO.getAdOrgId());
        requestDTO.setDistributorType(MerchantDivideDistributorTypeEnum.MERCHANT.getType());
        requestDTO.setConditionIds(equipmentValueList);
        requestDTO.setExcludeConditionIds(excludeEquipments);
        requestDTO.setCreator(userInfoDTO.getUserName());
        requestDTO.setAdUserId(userInfoDTO.getAdUserId());
        requestDTO.setIsFullSet(dto.getIsFullSet());
        requestDTO.setELabelIds(dto.getLabels());
        requestDTO.setStoreIds(dto.getEquipmentGroupIdList());
        requestDTO.setETypeIds(dto.getEquipmentTypeIdList());
        requestDTO.setFullSetFilterEquipmentValue(dto.getQueryStr());
        if (Objects.nonNull(dto.getRuleId())) {
            requestDTO.setRuleId(dto.getRuleId());
            BaseResponse response = divideRuleClient.conditionAdd(requestDTO);
            log.info("[规则分账]设备分账,设备规则关联设置,请求参数:[{}],当前登录信息:[{}],结果[{}]", JSON.toJSONString(dto), userInfoDTO, response);
        } else {
            BaseResponse response = divideRuleClient.conditionCancel(requestDTO);
            log.info("[规则分账]设备分账,设备规则关联设置,请求参数:[{}],当前登录信息:[{}],结果[{}]", JSON.toJSONString(dto), userInfoDTO, response);
        }
    }

    /**
     * 场地规则设置
     * @param dto
     * @param merchantId
     * @param adUserId
     */
    @Async
    public void batchStoreConditionAddOrCancel(DivideRuleStoreConfigDTO dto, Long merchantId, Long adUserId, String userAccount) {
        List<Long> storeIds = ofNullable(dto.getStores()).orElse(new ArrayList<>()).stream().distinct().map(MerchantStoreDivideDTO::getStoreId).collect(Collectors.toList());
        List<Long> excludeStoreIds = ofNullable(dto.getExcludeStores()).orElse(new ArrayList<>()).stream().distinct().map(MerchantStoreDivideDTO::getStoreId).collect(Collectors.toList());
        List<String> storeIdsStr = ofNullable(storeIds).orElse(new ArrayList<>()).stream().map(Object::toString).collect(Collectors.toList());
        List<String> excludeStoreIdsStr = ofNullable(excludeStoreIds).orElse(new ArrayList<>()).stream().map(Object::toString).collect(Collectors.toList());

        DivideConditionRequestDTO req = new DivideConditionRequestDTO();
        req.setConditionType(MerchantDivideConditonTypeCodeEnum.STORE.getCode());
        req.setDistributorId(merchantId);
        req.setDistributorType(MerchantDivideDistributorTypeEnum.MERCHANT.getType());
        req.setConditionIds(storeIdsStr);
        req.setExcludeConditionIds(excludeStoreIdsStr);
        req.setFullSetFilterStoreName(dto.getStoreName());
        req.setCreator(userAccount);
        req.setAdUserId(adUserId);
        req.setIsClearGroupEquCondition(dto.getIsClearGroupEquCondition());
        req.setIsFullSet(dto.getIsFullSet());
        req.setCooperationMode(dto.getCooperationMode());
        if (Objects.nonNull(dto.getRuleId())) {
            req.setRuleId(dto.getRuleId());
            BaseResponse response = divideRuleClient.conditionAdd(req);
            log.info("[规则分账]场地分账,场地规则关联设置,商户ID:{},用户ID:{},入参:{},响应:{}", merchantId, adUserId, req, response);
        } else {
            BaseResponse response = divideRuleClient.conditionCancel(req);
            log.info("[规则分账]场地分账,场地规则关联取消,商户ID:{},用户ID:{},入参:{},响应:{}", merchantId, adUserId, req, response);
        }
    }

    @Async
    public void batchSaveOrCancelDivideDefaultRuleRelation(List<Long> groupIds, AdUserInfoDTO userInfoDTO, DivideRuleConfigDTO dto) {
        try {
            if (dto.getRuleId() != null && dto.getDefaultRuleFlag()) {
                if (CollectionUtils.isNotEmpty(groupIds)) {
                    List<DivideDefaultRuleRelationDTO> defaultRuleRelationList = new ArrayList<>();
                    for (Long groupId : groupIds) {
                        DivideDefaultRuleRelationDTO defaultRuleRelationDTO = new DivideDefaultRuleRelationDTO();
                        defaultRuleRelationDTO.setDivideRuleId(dto.getRuleId());
                        defaultRuleRelationDTO.setDistributorId(userInfoDTO.getAdOrgId());
                        defaultRuleRelationDTO.setBusinessType(DivideDefaultRuleRelationEnum.GROUP_BUSINESS_ID.getKey());
                        defaultRuleRelationDTO.setBusinessId(groupId);
                        defaultRuleRelationDTO.setCreatedBy(userInfoDTO.getAdUserId());
                        defaultRuleRelationDTO.setUpdatedBy(userInfoDTO.getAdUserId());
                        defaultRuleRelationDTO.setCreateTime(new Date());
                        defaultRuleRelationDTO.setUpdateTime(defaultRuleRelationDTO.getCreateTime());
                        defaultRuleRelationList.add(defaultRuleRelationDTO);
                    }
                    divideDefaultRuleRelationClient.batchSaveOrUpdate(defaultRuleRelationList);
                }
            }
            //删除默认规则
            else if (null == dto.getRuleId() && dto.getDefaultRuleFlag()) {
                if (CollectionUtils.isNotEmpty(groupIds)) {
                    DivideDefaultRuleRelationDTO divideDefaultRuleRelationDTO = new DivideDefaultRuleRelationDTO();
                    divideDefaultRuleRelationDTO.setDistributorId(userInfoDTO.getAdOrgId());
                    divideDefaultRuleRelationDTO.setBusinessType(DivideDefaultRuleRelationEnum.GROUP_BUSINESS_ID.getKey());
                    divideDefaultRuleRelationDTO.setBusinessIdList(groupIds);
                    divideDefaultRuleRelationClient.batchDelete(divideDefaultRuleRelationDTO);
                }
            }
        }catch (Exception e){
            log.error("设备分成默认规则设置异常", e);
        }

    }
}
