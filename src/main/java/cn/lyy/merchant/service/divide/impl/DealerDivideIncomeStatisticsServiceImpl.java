package cn.lyy.merchant.service.divide.impl;

import static java.util.Optional.ofNullable;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.merchant.api.service.AdOrgClient;
import cn.lyy.merchant.api.service.DivideClient;
import cn.lyy.merchant.api.service.MerchantGroupService;
import cn.lyy.merchant.dto.account.DivideUserDTO;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.service.divide.DealerDivideIncomeStatisticsService;
import cn.lyy.merchant.utils.RemoteResponseUtils;
import java.util.Arrays;
import java.util.Objects;
import java.util.stream.Collectors;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import java.util.List;

/**
 * 资金池分账统计服务实现类
 *
 * <AUTHOR>
 * @date 2025-7-11/
 */
@Service
@Slf4j
public class DealerDivideIncomeStatisticsServiceImpl implements DealerDivideIncomeStatisticsService {

    @Autowired
    private AdOrgClient adOrgClient;

    @Autowired
    private MerchantGroupService merchantGroupService;

    @Autowired
    private DivideClient divideClient;

    /**
     * 根据用户身份判断是否使用其有权限的场地ID列表作为查询条件
     *
     * @param userId 用户ID
     * @param inputStoreIds 原始传入的场地ID列表
     * @return 若原始列表为空且非主账号，则返回用户所属场地ID列表；否则返回原始列表
     */
    @Override
    public List<Long> getUserAuthorizedStoreIds(Long userId, List<Long> inputStoreIds, Boolean isDivideReceiverIncome) {
        if(ofNullable(isDivideReceiverIncome).orElse(Boolean.FALSE)){
            return inputStoreIds;
        }
        boolean isPrimary = isPrimaryUser(userId);
        if (CollectionUtils.isEmpty(inputStoreIds) && !isPrimary) {
            return getUserAuthorizedGroupIds(userId);
        }
        return inputStoreIds;
    }

    /**
     * 获取运营商ID（根据是否为分账接收方判断）
     *
     * @param currentLoginAdOrgId 当前登录用户的商户ID
     * @param isDivideReceiverIncome 是否为分账接收方
     * @return 如果是分账接收方，则返回其对应的运营商IID；否则返回当前用户商户ID
     */
    @Override
    public List<Long> getDistributorId(Long currentLoginAdOrgId, Boolean isDivideReceiverIncome) {
        if (Boolean.TRUE.equals(ofNullable(isDivideReceiverIncome).orElse(Boolean.FALSE))) {
            // 查询该用户对应的主商户ID
            List<Long> distributorIds = ofNullable(getDistributorIdByDivideAdOrgId(currentLoginAdOrgId))
                    .filter(Objects::nonNull).orElseThrow(() -> new BusinessException("商户数据获取失败"));
            return distributorIds;
        }
        // 默认返回当前登录用户的组织ID作为分销商ID
        return Arrays.asList(currentLoginAdOrgId);
    }

    /**
     * 获取分账接收方对应的分账商户ID,如果是分账接收方，则返回当前登录用户的组织ID作为分成方商户ID
     * @param currentLoginAdOrgId 当前登录用户的商户ID
     * @param divideDistributorId
     * @param isDivideReceiverIncome 是否为分账接收方
     * @return
     */
    @Override
    public Long getDivideReceiverDivideDistributorId(Long currentLoginAdOrgId, Long divideDistributorId, Boolean isDivideReceiverIncome){
        if (Boolean.TRUE.equals(ofNullable(isDivideReceiverIncome).orElse(Boolean.FALSE))) {
            return currentLoginAdOrgId;
        }
        return divideDistributorId;
    }

    @Override
    public List<Long> getDivideReceiverDivideDistributorIds(Long currentLoginAdOrgId, List<Long> divideDistributorIds, Boolean isDivideReceiverIncome){
        if (Boolean.TRUE.equals(ofNullable(isDivideReceiverIncome).orElse(Boolean.FALSE))) {
            return Arrays.asList(currentLoginAdOrgId);
        }
        return divideDistributorIds;
    }

    /**
     * 根据分账商户ID获取其关联的运营商IID
     *
     * @param divideAdOrgId 分账组织ID
     * @return 若存在关联分销商ID则返回，否则返回 null
     */
    public List<Long> getDistributorIdByDivideAdOrgId(Long divideAdOrgId) {
        List<DivideUserDTO> list = ofNullable(divideClient.getLyyDivideUserListByOrgId(divideAdOrgId))
                .filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode())
                .map(BaseResponse::getData)
                .orElse(null);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream()
                    .map(DivideUserDTO::getLyyDistributorId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        }
        return null;
    }


    /**
     * 判断指定用户是否为主账号
     *
     * @param userId 用户ID
     * @return true 表示是主账号，false 表示不是
     */
    private boolean isPrimaryUser(Long userId) {
        BaseResponse<Boolean> response = adOrgClient.checkPrimary(userId);
        return ofNullable(response)
                .filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode())
                .map(BaseResponse::getData)
                .orElse(Boolean.TRUE);
    }

    /**
     * 获取当前用户有权限访问的所有设备分组ID（即场地ID）
     *
     * @param userId 当前登录用户ID
     * @return 场地ID列表
     * @throws BusinessException 如果远程调用失败或数据为空
     */
    private List<Long> getUserAuthorizedGroupIds(Long userId) {
        List<Long> groupIds = ofNullable(merchantGroupService.getAdUserGroupIdList(userId))
                .filter(RemoteResponseUtils::checkResponse)
                .map(RemoteResponseUtils::getData)
                .orElseThrow(() -> new BusinessException("场地数据获取失败"));
        log.info("[分账统计] 查询子账号过滤场地，当前登录账号ID:{}, 有权限场地:{}个", userId, groupIds.size());
        return groupIds;
    }
}
