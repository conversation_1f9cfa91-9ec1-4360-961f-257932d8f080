package cn.lyy.merchant.service.divide;

import java.util.List;
/**
 * 资金池分账统计服务实现类
 *
 * <AUTHOR>
 * @date 2025-7-11/
 */
public interface DealerDivideIncomeStatisticsService {

    /**
     * 根据用户身份判断是否使用其有权限的场地ID列表作为查询条件
     *
     * @return 若原始列表为空且非主账号，则返回用户所属场地ID列表；否则返回原始列表
     */
    List<Long> getUserAuthorizedStoreIds(Long userId, List<Long> inputStoreIds, Boolean isDivideReceiverIncome);

    /**
     * 获取运营商ID（根据是否为分账接收方判断）
     *
     * @param currentLoginAdOrgId 当前登录用户的商户ID
     * @param isDivideReceiverIncome 是否为分账接收方
     * @return 如果是分账接收方，则返回其对应的运营商IID；否则返回当前用户商户ID
     */
    List<Long> getDistributorId(Long currentLoginAdOrgId, Boolean isDivideReceiverIncome);

    /**
     * 获取分账接收方分成方ID（根据是否为分账接收方判断）
     *
     * @param currentLoginAdOrgId 当前登录用户的商户ID
     * @param isDivideReceiverIncome 是否为分账接收方
     * @return 如果是分账接收方，则返回其对应的运营商IID；否则返回当前用户商户ID
     */
    Long getDivideReceiverDivideDistributorId(Long currentLoginAdOrgId, Long divideDistributorId, Boolean isDivideReceiverIncome);

    /**
     * 获取分账接收方分成方ID（根据是否为分账接收方判断）
     * @param currentLoginAdOrgId
     * @param divideDistributorIds
     * @param isDivideReceiverIncome
     * @return
     */
    List<Long> getDivideReceiverDivideDistributorIds(Long currentLoginAdOrgId, List<Long> divideDistributorIds, Boolean isDivideReceiverIncome);

}
