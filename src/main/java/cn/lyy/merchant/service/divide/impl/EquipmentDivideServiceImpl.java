package cn.lyy.merchant.service.divide.impl;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.merchant.api.service.AdOrgClient;
import cn.lyy.merchant.api.service.DivideClient;
import cn.lyy.merchant.api.service.DivideRuleClient;
import cn.lyy.merchant.api.service.rule.DivideDefaultRuleRelationClient;
import cn.lyy.merchant.dto.account.DivideUserInfoDTO;
import cn.lyy.merchant.dto.divide.*;
import cn.lyy.merchant.dto.divide.enums.DivideRuleEquipmentRelationEnum;
import cn.lyy.merchant.dto.divide.request.DivideRuleEquipmentRelationLogQueryDTO;
import cn.lyy.merchant.dto.divide.response.DivideRuleEquipmentRelationLogVO;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import cn.lyy.merchant.service.divide.IEquipmentDivideService;
import cn.lyy.merchant.service.divide.handler.DivideAsyncHandler;
import cn.lyy.merchant.util.PageConverter;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import static java.util.Optional.ofNullable;

/**
 * <AUTHOR>
 * @date 2023/5/26
 */
@Service
@Slf4j
public class EquipmentDivideServiceImpl implements IEquipmentDivideService {
    @Autowired
    private DivideDefaultRuleRelationClient divideDefaultRuleRelationClient;

    @Autowired
    private DivideRuleClient divideRuleClient;

    @Autowired
    private DivideAsyncHandler divideAsyncHandler;

    @Autowired
    private DivideClient divideClient;

    @Autowired
    private AdOrgClient adOrgClient;

    private static final Integer INSERT_SIZE = 500;

    /**
     * 获取设备分成/抽成变更记录
     *
     * @param dto         入参
     * @param userInfoDTO 用户信息
     * @return
     */
    @Override
    public BaseResponse<PageInfo<DivideRuleEquipmentRelationLogVO>> pageDivideRuleEquipmentRelationLog(DivideRuleEquipmentRelationLogQueryVO dto, AdUserInfoDTO userInfoDTO) {
        DivideRuleEquipmentRelationLogQueryDTO divideRuleEquipmentRelationLogQueryDTO = new DivideRuleEquipmentRelationLogQueryDTO();
        BeanUtils.copyProperties(dto, divideRuleEquipmentRelationLogQueryDTO);
        divideRuleEquipmentRelationLogQueryDTO.setAdOrgId(userInfoDTO.getAdOrgId());
        divideRuleEquipmentRelationLogQueryDTO.setBelongType(DivideRuleEquipmentRelationEnum.MERCHANT_BELONG_TYPE.getKey());
        return divideDefaultRuleRelationClient.pageDivideRuleEquipmentRelationLog(divideRuleEquipmentRelationLogQueryDTO);
    }

    /**
     * @param dto
     * @param userInfoDTO
     * @return
     */
    @Override
    public BaseResponse conditionAddOrCancel(DivideRuleConfigDTO dto, AdUserInfoDTO userInfoDTO) {
        BaseResponse baseResponse = new BaseResponse();
        boolean isFullSet = ofNullable(dto.getIsFullSet()).orElse(Boolean.FALSE).booleanValue();
        if (!isFullSet && ofNullable(dto.getMerchantEquipmentDTOList()).orElse(new ArrayList<>()).size() > INSERT_SIZE) {
            log.warn("[规则分账]设备分账,设备规则关联设置,非全选" + "单批最多设置" + INSERT_SIZE + "个" + ",入参:{},用户:{}", JSON.toJSONString(dto), userInfoDTO);
            baseResponse.setCode(ResponseCodeEnum.FAIL.getCode());
            baseResponse.setMessage("单批最多设置" + INSERT_SIZE + "个");
            return baseResponse;
        }
        if (isFullSet) {
            log.info("[规则分账]设备分账,设备规则关联设置,全选,入参:{},用户:{}", JSON.toJSONString(dto), userInfoDTO);
            divideAsyncHandler.batchConditionAddOrCancel(dto, userInfoDTO);
        } else {
            log.info("[规则分账]设备分账,设备规则关联设置,非全选,入参:{},用户:{}", JSON.toJSONString(dto), userInfoDTO);
            List<String> equipmentValueList = ofNullable(dto.getMerchantEquipmentDTOList()).orElse(new ArrayList<>()).stream().map(MerchantEquipmentDivideDTO::getEquipmentValue)
                .filter(Objects::nonNull).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(equipmentValueList)) {
                divideAsyncHandler.batchConditionAddOrCancel(dto, userInfoDTO);
            }
        }
        return baseResponse;
    }

    @Override
    public BaseResponse conditionStoreAddOrCancel(DivideRuleStoreConfigDTO dto, Long merchantId, Long adUserId, String userAccount) {
        BaseResponse response = new BaseResponse();
        if(ofNullable(dto.getStores()).orElse(new ArrayList<>()).size() > INSERT_SIZE){
            response.setCode(ResponseCodeEnum.FAIL.getCode());
            response.setMessage("单批最多设置" + INSERT_SIZE + "个");
            return response;
        }
        log.info("[规则分账]场地分账,商户ID:{},用户ID:{},入参:{}", merchantId, adUserId, dto);
        List<Long> storeIds = ofNullable(dto.getStores()).orElse(new ArrayList<>()).stream().map(MerchantStoreDivideDTO::getStoreId).filter(Objects::nonNull).distinct()
            .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(storeIds) && !ofNullable(dto.getIsFullSet()).orElse(Boolean.FALSE).booleanValue()){
            log.info("[规则分账]场地分账,入参存在空,不满足跳过,商户ID:{},用户ID:{},入参:{}", merchantId, adUserId, merchantId);
            return response;
        }
        divideAsyncHandler.batchStoreConditionAddOrCancel(dto, merchantId, adUserId, userAccount);
        return response;
    }

    /**
     * 根据adOrgId获取分账规则
     * 如果传了规则id,标记出选择的规则id给前端回显
     */
    @Override
    public DivideRuleSelectVO divideRuleSimpleList(DivideRuleQueryRequestDTO dto) {
        BaseResponse<List<DivideRuleInfoDTO>> response = divideRuleClient.divideRuleSimpleList(dto);
        if (Objects.nonNull(response) && response.getCode() == ResponseCodeEnum.SUCCESS.getCode() && Objects.nonNull(response.getData())) {
            List<DivideRuleInfoDTO> data = response.getData();
            if (Objects.isNull(dto.getId())) {
                return new DivideRuleSelectVO().setSelectRuleList(data);
            }
            Integer selectIndex = null;
            for (int i = 0; i < data.size(); i++) {
                if (dto.getId().equals(data.get(i).getId())) {
                    selectIndex = i;
                    break;
                }
            }
            return new DivideRuleSelectVO().setSelectIndex(selectIndex).setSelectRuleList(data);
        }
        return null;
    }


    @Override
    public PageInfo<DivideRuleInfoMessageDTO> divideRulePage(DivideRuleQueryRequestDTO dto, Long adUserId) {
        PageInfo<DivideRuleInfoMessageDTO> newPageInfo = new PageInfo<>();
        BaseResponse<PageInfo<DivideRuleInfoDTO>> response = divideRuleClient.divideRulePage(dto);
        if (Objects.nonNull(response) && response.getCode() == ResponseCodeEnum.SUCCESS.getCode() && Objects.nonNull(response.getData())) {
            PageInfo<DivideRuleInfoDTO> data = response.getData();
            List<DivideRuleInfoDTO> list = data.getList();
            List<DivideRuleInfoMessageDTO> divideRuleInfoMessageList = new ArrayList<>();
            for (DivideRuleInfoDTO divideRuleInfoDTO : list) {
                DivideRuleInfoMessageDTO divideRuleInfoMessageDTO = new DivideRuleInfoMessageDTO();
                BeanUtils.copyProperties(divideRuleInfoDTO, divideRuleInfoMessageDTO);
                divideRuleInfoMessageDTO.setDivideDistributorIdList(divideRuleInfoDTO.getDivideDistributorIdList());
                divideRuleInfoMessageDTO.setRuleDetailInfoDTO(divideRuleInfoDTO.getRuleDetailInfoDTO());
                List<Long> divideDistributorIdList = divideRuleInfoDTO.getDivideDistributorIdList();
                if (CollectionUtils.isNotEmpty(divideDistributorIdList)) {
                    BaseResponse<List<DivideUserInfoDTO>> lyyDivideUserListResponse = divideClient.getLyyDivideUserList(divideDistributorIdList, dto.getDistributorId());
                    if (Objects.nonNull(lyyDivideUserListResponse) && lyyDivideUserListResponse.getCode() == ResponseCodeEnum.SUCCESS.getCode() && Objects.nonNull(lyyDivideUserListResponse.getData())) {
                        divideRuleInfoMessageDTO.setDivideUserList(lyyDivideUserListResponse.getData());
                    }
                    //如果分成方人员有商家自己的查询商家的姓名和电话
                    if (divideDistributorIdList.contains(dto.getDistributorId())) {
                        BaseResponse<LyyDistributorUserDTO> distributorUserInFo = adOrgClient.findDistributorUserInFo(adUserId.intValue());
                        if (Objects.nonNull(distributorUserInFo) && distributorUserInFo.getCode() == ResponseCodeEnum.SUCCESS.getCode() && Objects.nonNull(distributorUserInFo.getData())) {
                            LyyDistributorUserDTO lyyDistributorUserDTO = distributorUserInFo.getData();
                            DivideUserInfoDTO divideUserInfoDTO = new DivideUserInfoDTO();
                            divideUserInfoDTO.setName(lyyDistributorUserDTO.getName());
                            divideUserInfoDTO.setPhone(lyyDistributorUserDTO.getPhone());
                            divideUserInfoDTO.setLyyDistributorId(lyyDistributorUserDTO.getDivideDistributorId());
                            List<DivideUserInfoDTO> divideUserInFoList = divideRuleInfoMessageDTO.getDivideUserList() == null ? new ArrayList<>() : divideRuleInfoMessageDTO.getDivideUserList();
                            divideUserInFoList.add(divideUserInfoDTO);
                            divideRuleInfoMessageDTO.setDivideUserList(divideUserInFoList);
                        }
                    }
                }
                divideRuleInfoMessageList.add(divideRuleInfoMessageDTO);
            }
            return PageConverter.fromPageInfo(data, divideRuleInfoMessageList);
        }
        return newPageInfo;
    }

    /**
     * 分账规则详情（编辑时展示）
     *
     * @param dto 请求参数
     * @param adUserId
     * @return 返回结果
     */
    @Override
    public BaseResponse<DivideRuleInfoDTO> divideRuleUpdateDetail(DivideRuleSaveRequestDTO dto, Long adUserId) {
        BaseResponse<DivideRuleInfoDTO> response = divideRuleClient.divideRuleUpdateDetail(dto);
        if (Objects.nonNull(response) && response.getCode() == ResponseCodeEnum.SUCCESS.getCode() && Objects.nonNull(response.getData())) {
            getDivideDistributorInfo(response.getData(), dto.getDistributorId(),adUserId);
        }
        return response;
    }


    /**
     * 分账规则详情
     *
     * @param dto 请求参数
     * @param adUserId
     * @return 返回结果
     */
    @Override
    public BaseResponse<DivideRuleInfoDTO> divideRuleDetail(DivideRuleSaveRequestDTO dto, Long adUserId) {
        BaseResponse<DivideRuleInfoDTO> response = divideRuleClient.divideRuleDetail(dto);
        if (Objects.nonNull(response) && response.getCode() == ResponseCodeEnum.SUCCESS.getCode() && Objects.nonNull(response.getData())) {
            getDivideDistributorInfo(response.getData(), dto.getDistributorId(), adUserId);
        }
        return response;
    }

    /**
     * 根据商户中心接口返回信息组装分成放信息
     *  @param data
     * @param distributorId
     * @param adUserId
     */
    @Override
    public void getDivideDistributorInfo(DivideRuleInfoDTO data, Long distributorId, Long adUserId) {
        for (DivideRuleDetailInfoDTO divideRuleDetailInfoDTO : data.getRuleDetailInfoDTO()) {
            DivideRateUserCateInfoDTO userRateData = divideRuleDetailInfoDTO.getUserRateData();
            if (Objects.nonNull(userRateData)) {
                Set<Long> divideDistributionIds = new HashSet<>();
                List<DivideRuleUserInfoDTO> allList = userRateData.getAllList();
                List<DivideRuleUserInfoDTO> balanceChargeList = userRateData.getBalanceChargeList();
                List<DivideRuleUserInfoDTO> icChargeList = userRateData.getIcChargeList();
                List<DivideRuleUserInfoDTO> payStartList = userRateData.getPayStartList();
                List<DivideRuleUserInfoDTO> vipBuyList = userRateData.getVipBuyList();
                getDivideDistributorIds(divideDistributionIds, balanceChargeList);
                getDivideDistributorIds(divideDistributionIds, icChargeList);
                getDivideDistributorIds(divideDistributionIds, payStartList);
                getDivideDistributorIds(divideDistributionIds, vipBuyList);
                getDivideDistributorIds(divideDistributionIds, allList);
                if (CollectionUtils.isNotEmpty(divideDistributionIds)) {
                    BaseResponse<List<DivideUserInfoDTO>> lyyDivideUserListResponse = divideClient.getLyyDivideUserList(new ArrayList<>(divideDistributionIds), distributorId);
                    BaseResponse<LyyDistributorUserDTO> distributorUserInFo = adOrgClient.findDistributorUserInFo(adUserId.intValue());
                    LyyDistributorUserDTO lyyDistributorUserDTO = distributorUserInFo.getData();
                    if (Objects.nonNull(lyyDivideUserListResponse) && lyyDivideUserListResponse.getCode() == ResponseCodeEnum.SUCCESS.getCode() && Objects.nonNull(lyyDivideUserListResponse.getData())) {
                        Map<Long, DivideUserInfoDTO> lyyDivideUserMap = lyyDivideUserListResponse.getData().stream().collect(Collectors.toMap(DivideUserInfoDTO::getAdOrgId, v -> v, (v1, v2) -> v1));
                        buildDivideDistributorInfo(lyyDivideUserMap, allList, lyyDistributorUserDTO);
                        buildDivideDistributorInfo(lyyDivideUserMap, balanceChargeList, lyyDistributorUserDTO);
                        buildDivideDistributorInfo(lyyDivideUserMap, icChargeList, lyyDistributorUserDTO);
                        buildDivideDistributorInfo(lyyDivideUserMap, payStartList, lyyDistributorUserDTO);
                        buildDivideDistributorInfo(lyyDivideUserMap, vipBuyList, lyyDistributorUserDTO);
                    }
                }
            }
        }
        if (log.isDebugEnabled()) {
            log.debug("最终数据:{}", JSON.toJSONString(data));
        }
    }

    /**
     * 根据反查的分成方信息组装回原有对象分成信息
     * @param userInfoMap
     * @param divideRuleUserInfoDTOList
     * @param lyyDistributorUserDTO
     */
    private void buildDivideDistributorInfo(Map<Long, DivideUserInfoDTO> userInfoMap, List<DivideRuleUserInfoDTO> divideRuleUserInfoDTOList, LyyDistributorUserDTO lyyDistributorUserDTO) {
        if(CollectionUtils.isNotEmpty(divideRuleUserInfoDTOList)) {
            for (DivideRuleUserInfoDTO divideRuleUserInfoDTO : divideRuleUserInfoDTOList) {
                //商家自己的分成信息以前设计是没存在lyy_divide_user表的,只能获取的到登录用户的名称和账号
                if (Objects.nonNull(lyyDistributorUserDTO) && lyyDistributorUserDTO.getDivideDistributorId() > 0) {
                    if (divideRuleUserInfoDTO.getDivideDistributorId().equals(lyyDistributorUserDTO.getDivideDistributorId())) {
                        divideRuleUserInfoDTO.setDivideName(lyyDistributorUserDTO.getName());
                        divideRuleUserInfoDTO.setDividePhone(lyyDistributorUserDTO.getPhone());
                        if (log.isDebugEnabled()) {
                            log.debug("根据反查的商家信息组装回原有对象分成信息组装数据:{}", JSON.toJSONString(divideRuleUserInfoDTO));
                        }
                    }
                }
                Optional.ofNullable(userInfoMap.get(divideRuleUserInfoDTO.getDivideDistributorId())).ifPresent(userInfoDTO-> {
                    divideRuleUserInfoDTO.setDivideUserId(userInfoDTO.getLyyDivideUserId());
                    divideRuleUserInfoDTO.setDivideBankCardNum(userInfoDTO.getBankCardNum());
                    divideRuleUserInfoDTO.setDivideBankName(userInfoDTO.getBankCardName());
                    divideRuleUserInfoDTO.setDivideName(userInfoDTO.getName());
                    divideRuleUserInfoDTO.setDividePhone(userInfoDTO.getPhone());
                    if (log.isDebugEnabled()) {
                        log.debug("根据反查的分成方信息组装回原有对象分成信息组装数据:{}", JSON.toJSONString(divideRuleUserInfoDTO));
                    }
                });
            }
        }
    }

    /**
     * 组装分成方商户ID
     *
     * @param divideDistributionIds
     * @param divideRuleUserInfoDTOList
     */
    private void getDivideDistributorIds(Set<Long> divideDistributionIds, List<DivideRuleUserInfoDTO> divideRuleUserInfoDTOList) {
        if (CollectionUtils.isNotEmpty(divideRuleUserInfoDTOList)) {
            for (DivideRuleUserInfoDTO divideRuleUserInfoDTO : divideRuleUserInfoDTOList) {
                divideDistributionIds.add(divideRuleUserInfoDTO.getDivideDistributorId());
            }
        }
    }

}
