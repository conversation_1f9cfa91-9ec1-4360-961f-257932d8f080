package cn.lyy.merchant.service.divide;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.merchant.dto.divide.*;
import cn.lyy.merchant.dto.divide.response.DivideRuleEquipmentRelationLogVO;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import com.github.pagehelper.PageInfo;

/**
 * <AUTHOR>
 * @date 2023/5/26
 */
public interface IEquipmentDivideService {

    /**
     * 获取设备分成记录列表(分页)
     * @param dto 入参
     * @param userInfoDTO 用户信息
     * @return
     */
    BaseResponse<PageInfo<DivideRuleEquipmentRelationLogVO>> pageDivideRuleEquipmentRelationLog(DivideRuleEquipmentRelationLogQueryVO dto, AdUserInfoDTO userInfoDTO);

    /**
     * 新增/取消规则和设备的关联（批量）
     * @param dto
     * @param userInfoDTO
     * @return
     */
    BaseResponse conditionAddOrCancel(DivideRuleConfigDTO dto, AdUserInfoDTO userInfoDTO);

    /**
     * 新增/取消规则和场地的关联（批量）
     * @param dto
     * @param merchantId
     * @param adUserId
     * @param userAccount
     */
    BaseResponse conditionStoreAddOrCancel(DivideRuleStoreConfigDTO dto, Long merchantId, Long adUserId, String userAccount);

    /**
     * 根据adOrgId获取分账规则
     * 如果传了规则id,标记出选择的规则id给前端回显
     */
    DivideRuleSelectVO divideRuleSimpleList(DivideRuleQueryRequestDTO dto);

    /**
     * 分账规则分页列表查询
     * @param dto
     * @param adUserId
     * @return
     */
    PageInfo<DivideRuleInfoMessageDTO> divideRulePage(DivideRuleQueryRequestDTO dto, Long adUserId);

    /**
     * 分账规则详情（编辑时展示）
     * @param dto 请求参数
     * @param adUserId
     * @return 返回结果
     */
    BaseResponse<DivideRuleInfoDTO> divideRuleUpdateDetail(DivideRuleSaveRequestDTO dto, Long adUserId);

    /**
     * 分账规则详情（编辑时展示）
     * @param dto 请求参数
     * @param adUserId
     * @return 返回结果
     */
    BaseResponse<DivideRuleInfoDTO> divideRuleDetail(DivideRuleSaveRequestDTO dto, Long adUserId);
    /**
     * 初始化商户信息
     * @param data
     * @param distributorId
     * @param adUserId
     */
    void getDivideDistributorInfo(DivideRuleInfoDTO data, Long distributorId, Long adUserId);
}
