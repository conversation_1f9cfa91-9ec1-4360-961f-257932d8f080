package cn.lyy.merchant.service.group;

import cn.lyy.merchant.dto.group.GroupLabelRequestDTO;
import cn.lyy.merchant.dto.group.GroupLabelResponseDTO;

import java.util.List;

public interface EquipmentGroupLabelService {
    /**
     * 保存或更新场地标签
     * @param dto
     * @return
     */
    boolean saveOrUpdate(GroupLabelRequestDTO dto);

    /**
     * 删除场地标签
     * @param dto
     * @return
     */
    boolean deleteLabel(GroupLabelRequestDTO dto);

    /**
     * 获取场地标签列表
     * @param dto
     * @return
     */
    List<GroupLabelResponseDTO> getGroupLabels(GroupLabelRequestDTO dto);
}
