package cn.lyy.merchant.service.group.impl;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.merchant.api.service.MerchantGroupService;
import cn.lyy.merchant.dto.group.GroupLabelRequestDTO;
import cn.lyy.merchant.dto.group.GroupLabelResponseDTO;
import cn.lyy.merchant.service.group.EquipmentGroupLabelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static java.util.Optional.ofNullable;

@Service
@Slf4j
public class EquipmentGroupLabelServiceImpl implements EquipmentGroupLabelService {

    @Autowired
    private MerchantGroupService merchantGroupService;

    /**
     * 保存或更新场地标签
     * @param dto
     * @return
     */
    @Override
    public boolean saveOrUpdate(GroupLabelRequestDTO dto) {
        return ofNullable(merchantGroupService.saveOrUpdate(dto).getData()).orElse(false);
    }

    /**
     * 删除场地标签
     * @param dto
     * @return
     */
    @Override
    public boolean deleteLabel(GroupLabelRequestDTO dto) {
        BaseResponse baseResponse = merchantGroupService.deleteLabel(dto);
        if(ResponseCodeEnum.SUCCESS.getCode() == baseResponse.getCode()){
            return true;
        }
        return false;
    }

    /**
     * 获取场地标签列表
     * @param dto
     * @return
     */
    @Override
    public List<GroupLabelResponseDTO> getGroupLabels(GroupLabelRequestDTO dto) {
        return merchantGroupService.getGroupLabels(dto).getData();
    }


}
