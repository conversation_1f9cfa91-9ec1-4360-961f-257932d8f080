package cn.lyy.merchant.service;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.dto.Pagination;
import cn.lyy.lyy_consumption_api.merchant.GrantCoinsDTO;
import cn.lyy.merchant.dto.common.UserInfoDTO;
import cn.lyy.merchant.dto.member.*;

/**
 * <p>Title:fork</p>
 * <p>Desc: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020/11/23
 */
public interface MemberService {

    /**
     * 会员列表
     * @param param
     * @return
     */
    Pagination<MemberInfoDTO> memberList(MemberInfoQueryDTO param);

    /**
     * 会员详情
     * @param memberInfoRequestDTO
     * @return
     */
    MemberInfoDTO memberDetail(MemberInfoQueryDTO memberInfoRequestDTO);

    /**
     * 赠送红包
     * @param settingDTO
     * @return
     */
    int giftRedPacket(MemberGiftRedPacketSettingDTO settingDTO);

    /**
     * 赠送红包记录
     * @param param
     * @return
     */
    Pagination<GrantCoinsDTO> giftRedPacketRecordList(MemberInfoQueryDTO param);

    /**
     * 清空余额
     * @param memberInfoRequestDTO
     * @return
     */
    int clearBalance(MemberInfoQueryDTO memberInfoRequestDTO);

    /**
     * 申诉数量（未申诉）
     * @param userInfoDTO
     * @return
     */
    BaseResponse<Integer> appealCount(UserInfoDTO userInfoDTO);

    /**
     * 申诉处理+退款逻辑
     * @param param 申诉参数
     */
    void handleAppeal(AppealHandleParam param);

    Pagination<AppealRecordDTO> appealList(Integer status, Long adUserId, Long adOrgId, Integer pageIndex, Integer pageSize);

    AppealRecordDTO appealDetail(Long appealRecordId);
}
