package cn.lyy.merchant.service.tag.impl;

import cn.hutool.json.JSONUtil;
import cn.lyy.base.dto.Status;
import cn.lyy.bigdata.api.client.UserTagClient;
import cn.lyy.merchant.api.service.merchant.MerchantClient;
import cn.lyy.merchant.config.MemberQueryConfig;
import cn.lyy.merchant.constants.BusinessExceptionEnums;
import cn.lyy.merchant.dto.group.response.AuthorizedGroupDTO;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.service.tag.TagUserService;
import cn.lyy.merchant.utils.ResponseCheckUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.starter.common.resp.RespBodyUtil;
import com.lyy.user.account.infrastructure.constant.TagBusinessTypeEnum;
import com.lyy.user.account.infrastructure.resp.RespBody;
import com.lyy.user.account.infrastructure.user.dto.tag.TagBindUserDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagCountUserQueryDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagStatusDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUnBindUserDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserDetailDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserListDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserQueryDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserSaveDTO;
import com.lyy.user.account.infrastructure.user.feign.TagFeignClient;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * @description:
 * @author: qgw
 * @date on 2021/4/19.
 * @Version: 1.0
 */
@Service
@Slf4j
public class TagUserServiceImpl implements TagUserService {

    @Autowired
    private TagFeignClient tagFeignClient;
    @Autowired
    private MerchantClient merchantClient;

    /**
     * 是否对所有用户开放会员列表的“限制子账号查看的场地标签”功能
     */
    @Value("${member.list.group.tag.full-switch:false}")
    private Boolean fullSwitch;

    @Value("${merchant.multiStoreMerchants:''}")
    private List<Long> multiStoreMerchants = Lists.newArrayList();

    @Autowired
    private UserTagClient userTagClient;

    @Autowired
    private MemberQueryConfig memberQueryConfig;

    @Override
    public Page<TagUserListDTO> list(TagUserQueryDTO queryDTO, AdUserInfoDTO currentUser) {

        RespBody<Page<TagUserListDTO>> respBody = null;
        if (memberQueryConfig.getMemberSelectdbQuery(currentUser.getAdOrgId())){
            respBody =  userTagClient.list(queryDTO);
            log.debug("selectdb查询");
        }else {
            respBody = tagFeignClient.list(queryDTO);
        }

        if(GlobalErrorCode.OK.getCode().equals(respBody.getCode())){
            Page<TagUserListDTO> tagResult = respBody.getBody();
            tagResult.setRecords(filterGroupTags(tagResult.getRecords(), currentUser));
            return tagResult;
        }
        log.warn("查询用户标签失败:{}",respBody);
        handleException(respBody);
        return null;
    }

    @Override
    public List<TagUserListDTO> listAllTag(TagUserQueryDTO queryDTO, AdUserInfoDTO currentUser) {
        List<TagUserListDTO> tagList = ResponseCheckUtil.getData(tagFeignClient.listAllTag(queryDTO), Lists.newArrayList());
        return filterGroupTags(tagList, currentUser);
    }

    /**
     * 过滤掉子账号没权限看到的场地标签，然后返回过滤后的标签
     *
     * @param records 过滤后的标签
     * @return {@link List }<{@link TagUserListDTO }>
     */
    private List<TagUserListDTO> filterGroupTags(List<TagUserListDTO> records, AdUserInfoDTO currentUser) {
        // 全局开关，方便快速切换回旧逻辑
        if(!fullSwitch) {
            log.info("会员列表-限制子账号看到的标签范围-全量用户开关关闭，即将以旧逻辑返回标签");
            return records;
        }
        // 主账号无需过滤
        if(CollectionUtils.isEmpty(records) || currentUser.getIsApprover()) {
            return records;
        }
        // 只过滤场地标签
        boolean isGroupTag = records.stream().anyMatch(tagUser -> Objects.equals(TagBusinessTypeEnum.GROUP_NAME.getStatus(), tagUser.getBusinessType()));
        if(!isGroupTag) {
            return records;
        }
        log.info("会员列表-未过滤的场地标签个数：{}", records.size());
        List<AuthorizedGroupDTO> authorizedGroupList = RespBodyUtil.getData(merchantClient.listAuthorizedGroup(currentUser.getAdOrgId(), currentUser.getAdUserId()));
        if(CollectionUtils.isEmpty(authorizedGroupList)) {
            log.info("会员列表-获取子账号有权限，查询到的场地列表为空，商户ID：{}，用户ID：{}", currentUser.getAdOrgId(), currentUser.getAdUserId());
            return Lists.newArrayList();
        }
        Function<AuthorizedGroupDTO, String> function;
        if (multiStoreMerchants.contains(currentUser.getAdOrgId())) {
            // 多级场地商家使用场地ID作为标签名称
            function = ag -> ag.getLyyEquipmentGroupId().toString();
        } else {
            // 有权限的场地的名称
            function = AuthorizedGroupDTO::getName;
        }
        Set<String> watchableGroups = authorizedGroupList.stream()
                .filter(Objects::nonNull)
                .map(function)
                .collect(Collectors.toSet());
        log.debug("获取到的有权限的场地列表是：{}", log.isDebugEnabled()? JSONUtil.toJsonStr(watchableGroups): watchableGroups);
        // 过滤当前子账号的没有权限的场地
        List<TagUserListDTO> filteredGroupTags = records.stream()
                .filter(Objects::nonNull)
                .filter(tagUserListDTO -> watchableGroups.contains(tagUserListDTO.getName()))
                .collect(Collectors.toList());
        log.info("会员列表-过滤后的场地标签个数：{}", filteredGroupTags.size());
        return filteredGroupTags;
    }

    @Override
    public String saveOrUpdateTagUser(TagUserSaveDTO tagUserSaveDTO) {
        RespBody<Long> respBody = tagFeignClient.saveOrUpdateTagUser(tagUserSaveDTO);
        if(GlobalErrorCode.OK.getCode().equals(respBody.getCode())){
            return  respBody.getBody().toString();
        }
        log.warn("新增或修改用户标签失败:{}",respBody);
        handleException(respBody);
        return null;
    }

    @Override
    public Boolean changeTagStatus(TagStatusDTO dto) {
        RespBody respBody = tagFeignClient.changeTagStatus(dto);
        if(GlobalErrorCode.OK.getCode().equals(respBody.getCode())){
            return (Boolean)respBody.getBody();
        }
        log.warn("变更标签状态失败:{}",respBody);
        handleException(respBody);
        return false;
    }

    @Override
    public Boolean bindUser(TagBindUserDTO dto) {
        RespBody respBody = tagFeignClient.bindUser(dto);
        if (GlobalErrorCode.OK.getCode().equals(respBody.getCode())) {
            return (Boolean) respBody.getBody();
        }
        log.warn("标签用户失败:{}",respBody);
        handleException(respBody);
        return false;
    }

    @Override
    public Boolean unBindUser(TagUnBindUserDTO dto) {
        RespBody respBody = tagFeignClient.unBindUser(dto);
        if (GlobalErrorCode.OK.getCode().equals(respBody.getCode())) {
            return (Boolean) respBody.getBody();
        }
        log.warn("标签解绑用户失败:{}",respBody);
        handleException(respBody);
        return false;
    }

    @Override
    public Boolean updateTagName(TagDTO dto) {
        RespBody respBody = tagFeignClient.updateTagName(dto);

        if (GlobalErrorCode.OK.getCode().equals(respBody.getCode())) {
            return (Boolean) respBody.getBody();
        }
        log.warn("修改标签名称失败:{}",respBody);
        handleException(respBody);
        return false;
    }

    @Override
    public Page<TagUserListDTO> listTagByUser(TagUserQueryDTO dto) {

        RespBody<Page<TagUserListDTO>> respBody = null;
        if (memberQueryConfig.getMemberSelectdbQuery(dto.getMerchantId())){
            respBody =  userTagClient.listTagByUser(dto);
            log.debug("selectdb查询");
        }else {
            respBody = tagFeignClient.listTagByUser(dto);
        }

        if (GlobalErrorCode.OK.getCode().equals(respBody.getCode())) {
            return  respBody.getBody();
        }
        log.warn("根据用户查所属标签失败:{}",respBody);
        handleException(respBody);
        return null;
    }

    @Override
    public TagUserDetailDTO findByTagId(TagUserQueryDTO dto) {

        RespBody<TagUserDetailDTO> respBody = null;
        if (memberQueryConfig.getMemberSelectdbQuery(dto.getMerchantId())){
            respBody =  userTagClient.findByTagId(dto);
            log.debug("selectdb查询");
        }else {
            respBody = tagFeignClient.findByTagId(dto);
        }



        if (GlobalErrorCode.OK.getCode().equals(respBody.getCode())) {
            return  respBody.getBody();
        }
        log.warn("根据条件查标签的用户失败:{}",respBody);
        handleException(respBody);
        return null;
    }
    @Override
    public Long countFindByTagId(TagCountUserQueryDTO dto) {
        RespBody<Long> respBody = tagFeignClient.countFindByTagId(dto);

        if (GlobalErrorCode.OK.getCode().equals(respBody.getCode())) {
            return  respBody.getBody();
        }
        log.warn("根据条件查标签的用户数量失败:{}",respBody);
        handleException(respBody);
        return null;
    }

    private void handleException(RespBody respBody) {
        if (GlobalErrorCode.INTERNAL_SERVER_ERROR.getCode().equals(respBody.getCode())) {
            throw new BusinessException(BusinessExceptionEnums.OPERATE_ERROR);
        }
        throw new BusinessException(String.valueOf(Status.STATUS_FAIL),respBody.getMessage());
    }
}
