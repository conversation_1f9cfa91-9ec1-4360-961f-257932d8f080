package cn.lyy.merchant.service.tag;

import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.user.account.infrastructure.user.dto.tag.TagBindUserDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagCountUserQueryDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagStatusDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUnBindUserDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserDetailDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserListDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserQueryDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserSaveDTO;
import java.util.List;

/**
 * @description:
 * @author: qgw
 * @date on 2021/4/16.
 * @Version: 1.0
 */
public interface TagUserService {

    /**
     * 查询用户标签
     * @param queryDTO
     * @return
     */
    Page<TagUserListDTO> list(TagUserQueryDTO queryDTO, AdUserInfoDTO currentUser);

    /**
     * 查询指定商户的所有标签
     * @param queryDTO
     * @return
     */
    List<TagUserListDTO> listAllTag(TagUserQueryDTO queryDTO, AdUserInfoDTO currentUser);

    /**
     * 新增或修改用户标签
     * @param tagUserSaveDTO
     * @return
     */
    String saveOrUpdateTagUser(TagUserSaveDTO tagUserSaveDTO);
    /**
     * 变更标签状态
     * @param dto
     * @return
     */
    Boolean changeTagStatus(TagStatusDTO dto);

    /**
     * 标签绑定用户
     * @param dto
     * @return
     */
    Boolean bindUser(TagBindUserDTO dto);

    /**
     * 标签解绑用户
     * @param dto
     * @return
     */
    Boolean unBindUser(TagUnBindUserDTO dto);


    /**
     * 修改场地标签或者设备类型的标签
     * @param dto
     * @return
     */
    Boolean updateTagName(TagDTO dto);


    Page<TagUserListDTO> listTagByUser(TagUserQueryDTO dto);

    /**
     * 根据条件查标签的用户
     * @param dto
     * @return
     */
    TagUserDetailDTO findByTagId(TagUserQueryDTO dto);

    /**
     * 根据条件查标签的用户数量
     * @param dto
     * @return
     */
    Long countFindByTagId(TagCountUserQueryDTO dto);
}
