package cn.lyy.merchant.service.merchantfunction.handler.page;

import static java.util.Optional.ofNullable;

import cn.hutool.core.collection.CollUtil;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.base.util.DateUtil;
import cn.lyy.lyy_cmember_service_api.IAiDbjService;
import cn.lyy.lyy_cmember_service_api.IProductMatrixService;
import cn.lyy.lyy_cmember_service_api.dto.aidbj.AiDbjEquipmentResponseDTO;
import cn.lyy.lyy_cmember_service_api.dto.aidbj.AiDbjRequestDTO;
import cn.lyy.lyy_cmember_service_api.dto.aidbj.AiDbjServiceDTO;
import cn.lyy.lyy_cmember_service_api.dto.productmatrix.ProductMatrixLimitedTimeApplyDTO;
import cn.lyy.lyy_cmember_service_api.dto.productmatrix.ProductMatrixReportDTO;
import cn.lyy.lyy_cmember_service_api.enums.AiDbjConstants;
import cn.lyy.marketing.api.service.ActivityOperationClient;
import cn.lyy.marketing.dto.constants.ActivityStatusEnum;
import cn.lyy.marketing.dto.constants.ActivityTypeEnum;
import cn.lyy.marketing.dto.promotion.ActivityDTO;
import cn.lyy.marketing.dto.promotion.request.ActivityPageRequestDTO;
import cn.lyy.marketing.dto.promotion.request.ActivityUsedRequestDTO;
import cn.lyy.merchant.api.service.MerchantEquipmentService;
import cn.lyy.merchant.constants.SysPageFunctionConstants;
import cn.lyy.merchant.dto.merchant.MerchantEquipmentDTO;
import cn.lyy.merchant.dto.merchant.equpiment.request.MerchantEquipmentBaseInfoQueryRequest;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import cn.lyy.tools.equipment.LyyConstant;
import com.lyy.ram.service.infrastructure.constant.RamServiceConstant;
import com.lyy.ram.service.infrastructure.util.RamFunctionUtil;
import com.lyy.ram.service.interfaces.dto.function.response.PersonalFunctionResp;
import com.lyy.user.infrastructure.dto.activityeffect.productmatrix.DaProductMatrixExamineDiDTO;
import com.lyy.user.infrastructure.req.ProductMatrixQueryRequest;
import com.lyy.user.infrastructure.resp.RespBody;
import com.lyy.user.interfaces.openfeign.ProductMatrixClientOpenFeign;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 页面功能规则配置 娱乐业务数据处理
 */
@Component
@Slf4j
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class PlayPageFunctionHandler extends AbstractPageFunctionHandler implements BizPageFunctionHandler {

    private final MerchantEquipmentService merchantEquipmentService;
    private final IAiDbjService aiDbjService;
    private final ActivityOperationClient activityOperationClient;
    private final IProductMatrixService iProductMatrixService;
    private final ProductMatrixClientOpenFeign productMatrixClientOpenFeign;

    private final static String AI_BIND_SUBSCRIBE_EXPIRATION_POP = "aiBindSubscribeExpirationPop";

    @Override
    public void handlePopupParam(AdUserInfoDTO currentUser, HashMap<String, String> functionParam) {
        boolean isApprover = RamServiceConstant.YES.equals(functionParam.get(SysPageFunctionConstants.APPROVER));
        long start = System.currentTimeMillis();
        // AI兑币机弹窗
        if (isApprover) {
            // 获取兑币机设备列表
            MerchantEquipmentBaseInfoQueryRequest equipmentListQuery = new MerchantEquipmentBaseInfoQueryRequest();
            equipmentListQuery.setMerchantId(currentUser.getAdOrgId());
            equipmentListQuery.setEquipmentTypeId((long) LyyConstant.DBJ_TYPE_ID);
            List<MerchantEquipmentDTO> merchantEquipmentList = ofNullable(merchantEquipmentService.listEquipment(equipmentListQuery)).map(BaseResponse::getData).orElse(null);
            if (CollUtil.isNotEmpty(merchantEquipmentList)) {
                // 获取AI兑币机设备
                AiDbjRequestDTO requestDTO = new AiDbjRequestDTO();
                requestDTO.setEquipmentList(merchantEquipmentList.stream().map(MerchantEquipmentDTO::getEquipmentValue).collect(Collectors.toList()));
                List<String> aiDbjList = ofNullable(aiDbjService.getServiceExpireValueList(requestDTO)).map(cn.lyy.lyy_cmember_service_api.response.BaseResponse::getData).orElse(null);
                if (CollUtil.isNotEmpty(aiDbjList)) {
                    functionParam.put(SysPageFunctionConstants.AI_DBJ_COUNT, String.valueOf(aiDbjList.size()));
                }

                // 获取AI兑币机在用(免费和收费)设备
                AiDbjRequestDTO serviceEquipmentRequest = new AiDbjRequestDTO();
                serviceEquipmentRequest.setEquipmentList(requestDTO.getEquipmentList());
                serviceEquipmentRequest.setLyyDistributorId(currentUser.getAdOrgId().intValue());
                List<AiDbjEquipmentResponseDTO> serviceEquipmentList = aiDbjService.getServiceEquipmentData(serviceEquipmentRequest).getData();
                if (CollUtil.isNotEmpty(serviceEquipmentList)) {
                    long basicCnt = serviceEquipmentList.stream().filter(o -> Objects.equals(o.getVersion(), AiDbjConstants.VersionEnum.BASIC.getType())).count();
                    if (basicCnt > 0) {
                        functionParam.put(SysPageFunctionConstants.AI_DBJ_DATA_REPORT_POPUP, RamServiceConstant.YES);
                    } else {
                        long serviceCnt = serviceEquipmentList.stream().filter(o -> Objects.equals(o.getVersion(), AiDbjConstants.VersionEnum.PREMIUM.getType())).count();
                        if (serviceCnt > 0) {
                            functionParam.put(SysPageFunctionConstants.AI_DBJ_DATA_REPORT_POPUP, RamServiceConstant.YES);
                        }
                    }
                }
            }
            debugLog("handlePopupParam===>>>AI兑币机===>>>cost:{}", System.currentTimeMillis() - start);
        }

        // 智能导购弹窗
        start = System.currentTimeMillis();
        Integer proportion = 0;
        ActivityPageRequestDTO activityPageRequest = new ActivityPageRequestDTO();
        activityPageRequest.setLyyDistributorId(currentUser.getAdOrgId());
        activityPageRequest.setPageIndex(1);
        activityPageRequest.setPageSize(1);
        activityPageRequest.setStatus(ActivityStatusEnum.get(ActivityStatusEnum.EFFECTIVE.getValue()));
        activityPageRequest.setActivityType(Collections.singletonList(ActivityTypeEnum.PRODUCT_MATRIX));
        List<ActivityDTO> activityDTOList = ofNullable(activityOperationClient.listDrainage(activityPageRequest)).map(BaseResponse::getData).orElse(null);
        if (CollUtil.isNotEmpty(activityDTOList)) {
            ProductMatrixReportDTO reportDTO = ofNullable(iProductMatrixService.getReportConfig(currentUser.getAdOrgId().intValue())).map(cn.lyy.lyy_cmember_service_api.response.BaseResponse::getData).orElse(null);
            if (reportDTO != null) {
                ProductMatrixQueryRequest request = new ProductMatrixQueryRequest();
                request.setStartDate(DateUtil.format(reportDTO.getStartTime(), "yyyy-MM-dd"));
                request.setEndDate(DateUtil.format(DateUtil.addDateByDays(reportDTO.getEndTime(), 1), "yyyy-MM-dd"));
                request.setAdOrgId(currentUser.getAdOrgId());
                List<DaProductMatrixExamineDiDTO> list = ofNullable(productMatrixClientOpenFeign.getExamineDataAll(request)).map(RespBody::getBody).orElse(null);
                if (list != null) {
                    BigDecimal examinePayment = BigDecimal.ZERO;
                    BigDecimal noExaminePayment = BigDecimal.ZERO;
                    for (DaProductMatrixExamineDiDTO i : list) {
                        if (i.getExamineType() != null) {
                            if (i.getExamineType() == 1) {
                                examinePayment = i.getPayment();
                            }
                            if (i.getExamineType() == 0) {
                                noExaminePayment = i.getPayment();
                            }
                        }
                    }
                    if (examinePayment.compareTo(BigDecimal.ZERO) != 0 && noExaminePayment.compareTo(BigDecimal.ZERO) != 0
                            && examinePayment.compareTo(noExaminePayment) >= 0) {
                        proportion = examinePayment.subtract(noExaminePayment).multiply(BigDecimal.valueOf(100))
                                .divide(noExaminePayment, 0, RoundingMode.HALF_UP).intValue();
                    }
                }
            }
        }
        functionParam.put(SysPageFunctionConstants.SHOPPING_GUIDE_PROPORTION, String.valueOf(proportion));
        debugLog("handlePopupParam===>>>智能导购弹窗===>>>cost:{}", System.currentTimeMillis() - start);

        // 智能导购试用报名弹窗
        if (isApprover) {
            start = System.currentTimeMillis();
            String merchantShoppingGuideTrialReg = RamServiceConstant.NO;
            cn.lyy.lyy_cmember_service_api.response.BaseResponse<ProductMatrixLimitedTimeApplyDTO> response = iProductMatrixService.getLimitedTimeApply(currentUser.getAdOrgId().intValue());
            if (ResponseCodeEnum.SUCCESS.getCode() == response.getCode() && response.getData() == null) {
                // 申请状态未初始化
                ActivityUsedRequestDTO requestDTO = new ActivityUsedRequestDTO();
                requestDTO.setMerchantIds(Collections.singletonList(currentUser.getAdOrgId()));
                requestDTO.setActivityType(Collections.singletonList(ActivityTypeEnum.PRODUCT_MATRIX));
                requestDTO.setStatus(ActivityStatusEnum.EFFECTIVE);
                List<ActivityDTO> activityApplyList = ofNullable(activityOperationClient.listActivityByCondition(requestDTO)).map(BaseResponse::getData).orElse(null);
                if (CollUtil.isEmpty(activityApplyList) || CollUtil.isEmpty(activityApplyList.get(0).getLyyEquipmentGroupIds())) {
                    //当前开通活动场地数量＜1
                    merchantShoppingGuideTrialReg = RamServiceConstant.YES;
                }
            }
            functionParam.put(SysPageFunctionConstants.SHOPPING_GUIDE_TRIAL_REG, merchantShoppingGuideTrialReg);
            debugLog("handlePopupParam===>>>智能导购试用报名===>>>cost:{}", System.currentTimeMillis() - start);
        }
    }

    @Override
    public void filterPopupResult(AdUserInfoDTO currentUser, PersonalFunctionResp functionResult, HashMap<String, String> commonParam) {
        log.info("filterPopupResult===>>>绑定订阅会员过期续费提醒弹窗，[currentUser, functionResult, commonParam]={}，{}，{}", currentUser,
            functionResult, commonParam);
        if (RamFunctionUtil.hasPopup(functionResult, AI_BIND_SUBSCRIBE_EXPIRATION_POP)) {
            Boolean flag = false;
            try {
                MerchantEquipmentBaseInfoQueryRequest equipmentListQuery = new MerchantEquipmentBaseInfoQueryRequest();
                equipmentListQuery.setMerchantId(currentUser.getAdOrgId());
                equipmentListQuery.setEquipmentTypeId((long) LyyConstant.DBJ_TYPE_ID);
                List<MerchantEquipmentDTO> merchantEquipmentList = ofNullable(
                    merchantEquipmentService.listEquipment(equipmentListQuery)).map(BaseResponse::getData).orElse(null);
                if (CollUtil.isNotEmpty(merchantEquipmentList)) {
                    AiDbjRequestDTO requestDTO = new AiDbjRequestDTO();
                    requestDTO.setEquipmentList(
                        merchantEquipmentList.stream().map(MerchantEquipmentDTO::getEquipmentValue).collect(Collectors.toList()));
                    requestDTO.setLyyDistributorId(currentUser.getAdOrgId().intValue());
                    List<AiDbjServiceDTO> list = aiDbjService.getExpiringSoonWithBindSubscribeList(requestDTO).getData();
                    if (CollUtil.isNotEmpty(list)) {
                        flag = true;
                    }
                }
            } catch (Exception e) {
                log.error("filterPopupResult===>>>绑定订阅会员过期续费提醒弹窗，获取结果失败，商户ID:{}，错误描述:{}",
                    currentUser.getAdOrgId(), e);
            }
            debugLog("filterPopupResult===>>>绑定订阅会员过期续费提醒弹窗，获取结果成功，商户ID:{}，结果:{}", currentUser.getAdOrgId(), flag);
            if (!flag) {
                RamFunctionUtil.removePopup(functionResult, AI_BIND_SUBSCRIBE_EXPIRATION_POP);
            }
        }
    }
}
