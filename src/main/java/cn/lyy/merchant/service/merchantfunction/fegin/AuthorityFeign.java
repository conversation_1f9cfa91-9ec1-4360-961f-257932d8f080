package cn.lyy.merchant.service.merchantfunction.fegin;

import cn.lyy.authority_service_api.AdResourcesDTO;
import com.alibaba.fastjson.JSONObject;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "lyy-authority-service")
public interface AuthorityFeign {

    @GetMapping("/merchant/system/function/getGroupFunctionRule")
    public JSONObject getAllFunctionRule(@RequestParam(value="systemId") Integer systemId);

    @PostMapping("/authority/resources/menu/getMenusAndButtonByUsername")
    public List<AdResourcesDTO> getUserResource(@RequestParam("username") String username, @RequestParam("systemId") Integer systemId);

}
