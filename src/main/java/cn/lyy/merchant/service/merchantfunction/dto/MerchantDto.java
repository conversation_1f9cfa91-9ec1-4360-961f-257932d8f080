package cn.lyy.merchant.service.merchantfunction.dto;

import lombok.Data;

import java.util.Set;

/**
 * 商家信息
 * <AUTHOR>
 * @version 1.0
 * @since 2020/11/16
 */
@Data
public class MerchantDto {
    private Long id;
    private Set<EquipmentType> equipmentTypes;             //商家拥有的设备类型
    private Set<BlackWhiteList> blackWhiteLists;           //商家拥有的黑白名单
    private Set<Role> roles;         //商家拥有的角色和资源权限
    private Set<Reouces> reouces;         //商家拥有的角色和资源权限
    private Long accountType = 2l;         //商家账户类型：主账户，子账户,1:标识主账户，2标识子账户
    private Set<EquipmentBusinessType> equipmentBusinessTypes;          //商家设备业务类型

    @Data
    public class EquipmentType extends BaseCheck{
        @Override
        public boolean equals(Object obj){
            if(obj == null){
                return false;
            }
            if(this == obj){
                return true;
            }
            if(obj instanceof EquipmentType){
                if(this.getId().equals(((EquipmentType) obj).getId())){
                    return true;
                }
            }
            return false;
        }
        @Override
        public int hashCode(){
            return this.getId().hashCode();
        }
    }

    @Data
    public class BlackWhiteList extends BaseCheck{
        private Integer type;       //黑白名单类型 1：白名单，2：黑名单

        @Override
        public boolean equals(Object obj){
            if(obj == null){
                return false;
            }
            if(this == obj){
                return true;
            }
            if(obj instanceof BlackWhiteList){
                if(this.getId().equals(((BlackWhiteList) obj).getId()) && this.getType().intValue() == ((BlackWhiteList) obj).getType().intValue()){
                    return true;
                }
            }
            return false;
        }
        @Override
        public int hashCode(){
            return this.getId().hashCode() * this.getType().hashCode();
        }
    }

    @Data
    public class Role extends BaseCheck{
        private String code;
    }

    @Data
    public class Reouces extends BaseCheck{
        private String code;
    }

    @Data
    public class EquipmentBusinessType extends BaseCheck{
        @Override
        public boolean equals(Object obj){
            if(obj == null){
                return false;
            }
            if(this == obj){
                return true;
            }
            if(obj instanceof EquipmentBusinessType){
                if(this.getId().equals(((EquipmentBusinessType) obj).getId())){
                    return true;
                }
            }
            return false;
        }
        @Override
        public int hashCode(){
            return this.getId().hashCode();
        }
    }

    @Data
    public class BaseCheck {
        private String id;
        private String name;
    }
}
