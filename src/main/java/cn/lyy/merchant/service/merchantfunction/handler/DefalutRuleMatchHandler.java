package cn.lyy.merchant.service.merchantfunction.handler;

import cn.lyy.merchant.service.merchantfunction.contant.MerchantFunctionContant;
import cn.lyy.merchant.service.merchantfunction.dto.MerchantDto;
import lombok.extern.slf4j.Slf4j;

import java.util.HashSet;
import java.util.Set;

@Slf4j
public class DefalutRuleMatchHandler<M extends MerchantDto.BaseCheck> {

    public boolean check(Set<M> has, String rule){
        //没有配置或者配置了任意符号*都视为匹配上
        if(rule == null || rule.trim().isEmpty() || MerchantFunctionContant.ANY.equals(rule)){
            return true;
        }

        if(has == null){
            has = new HashSet<>();
        }

        int flag = rule.substring(1).split(MerchantFunctionContant.SPLIT).length;
        int match = 0;
        int noMatch = 0;
        String matchRule = rule.replace(MerchantFunctionContant.INVERT,"").replace(MerchantFunctionContant.AND,"").replace(MerchantFunctionContant.ONLY,"");
        matchRule = ","+matchRule+",";
        if(has.size()>0){
            for(M m: has){
                //当id为空时候，cantains空是真，所以这里需要将空值转换是的不包含
                if(m.getId() == null || m.getId().trim().isEmpty()){
                    m.setId("%%%%%%%%%");
                }
                if(matchRule.contains(","+m.getId()+",")){
                    match++;
                    if(rule.contains(MerchantFunctionContant.INVERT)){
                        log.info("用来匹配的规则字符串：{},匹配到的规则：{}，来匹配的值：{}",matchRule,rule,m.getId());
                    }
                }else{
                    noMatch++;
                }
            }
        }

        //且的情况
        if(rule.startsWith(MerchantFunctionContant.AND)){
            if(match != flag){
                return false;
            }
            //有且只允许同时仅拥有这些设备类型的商家进入
            if(rule.endsWith(MerchantFunctionContant.ONLY)){
                if(noMatch>0){
                    return false;
                }
            }

            return true;

        }

        //取反的情况
        if(rule.startsWith(MerchantFunctionContant.INVERT)){
            //取反的情况下又且
            if(rule.startsWith(MerchantFunctionContant.INVERT+MerchantFunctionContant.AND)){
                if(match == flag && noMatch == 0){
                    return false;
                }
            }

            if(match>0){
                return false;
            }

            return true;

        }

        //剩下开头没有任何特殊的情况
        if(match ==0){
            return false;
        }
        return true;
    }
}
