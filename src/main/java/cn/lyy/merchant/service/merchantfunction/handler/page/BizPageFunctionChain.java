package cn.lyy.merchant.service.merchantfunction.handler.page;

import cn.hutool.extra.spring.SpringUtil;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import com.lyy.ram.service.interfaces.dto.function.response.FunctionCenterResp;
import com.lyy.ram.service.interfaces.dto.function.response.MarketingFunctionResp;
import com.lyy.ram.service.interfaces.dto.function.response.PersonalFunctionResp;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Component
public class BizPageFunctionChain {

    public final List<BizPageFunctionHandler> handlerList = new ArrayList<>(7);

    @PostConstruct
    public void init() {
        handlerList.add(SpringUtil.getBean(ChargingPageFunctionHandler.class));
        handlerList.add(SpringUtil.getBean(MassagePageFunctionHandler.class));
        handlerList.add(SpringUtil.getBean(MiddlePageFunctionHandler.class));
        handlerList.add(SpringUtil.getBean(PaymentPageFunctionHandler.class));
        handlerList.add(SpringUtil.getBean(PlayPageFunctionHandler.class));
        handlerList.add(SpringUtil.getBean(VendingPageFunctionHandler.class));
        handlerList.add(SpringUtil.getBean(WashingPageFunctionHandler.class));
        handlerList.add(SpringUtil.getBean(FinalPageFunctionHandler.class));
    }

    /**
     * 获取功能规则参数
     */
    public void processFunctionParam(AdUserInfoDTO currentUser, HashMap<String, String> functionParam) {
        handlerList.forEach(handler -> handler.handleFunctionParam(currentUser, functionParam));
    }

    /**
     * 获取弹窗规则参数
     */
    public void processPopupParam(AdUserInfoDTO currentUser, HashMap<String, String> functionParam) {
        handlerList.forEach(handler -> handler.handlePopupParam(currentUser, functionParam));
    }

    /**
     * 获取营销中心规则参数
     */
    public void processMarketingParam(AdUserInfoDTO currentUser, HashMap<String, String> functionParam) {
        handlerList.forEach(handler -> handler.handleMarketingParam(currentUser, functionParam));
    }

    /**
     * 添加业务属性，并过滤最终首页功能结果
     */
    public void filterFunctionResult(AdUserInfoDTO currentUser, PersonalFunctionResp functionResult, HashMap<String, String> commonParam) {
        handlerList.forEach(handler -> handler.filterFunctionResult(currentUser, functionResult, commonParam));
    }

    /**
     * 添加业务属性，并过滤最终功能中心结果
     */
    public void filterFunctionCenterResult(AdUserInfoDTO currentUser, FunctionCenterResp functionResult, HashMap<String, String> commonParam) {
        handlerList.forEach(handler -> handler.filterFunctionCenterResult(currentUser, functionResult, commonParam));
    }

    /**
     * 添加业务属性，并过滤最终弹窗结果
     */
    public void filterPopupResult(AdUserInfoDTO currentUser, PersonalFunctionResp popupResult, HashMap<String, String> commonParam) {
        handlerList.forEach(handler -> handler.filterPopupResult(currentUser, popupResult, commonParam));
    }

    /**
     * 添加业务属性，并过滤最终弹窗结果
     */
    public void filterMarketingResult(AdUserInfoDTO currentUser, MarketingFunctionResp marketingResult, HashMap<String, String> commonParam) {
        handlerList.forEach(handler -> handler.filterMarketingResult(currentUser, marketingResult, commonParam));
    }

}
