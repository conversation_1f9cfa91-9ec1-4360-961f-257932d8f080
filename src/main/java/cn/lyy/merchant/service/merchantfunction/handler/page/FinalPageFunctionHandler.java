package cn.lyy.merchant.service.merchantfunction.handler.page;

import cn.hutool.core.collection.CollUtil;
import cn.lyy.authority_service_api.AdRoleFunctionDTO;
import cn.lyy.authority_service_api.miscroservice.AuthorityService;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import cn.lyy.merchant.util.SAASPermissionUtil;
import cn.lyy.tools.constants.SystemConstants;
import com.lyy.ram.service.infrastructure.util.RamFunctionUtil;
import com.lyy.ram.service.interfaces.dto.function.response.FunctionCenterResp;
import com.lyy.ram.service.interfaces.dto.function.response.MarketingFunctionResp;
import com.lyy.ram.service.interfaces.dto.function.response.PersonalFunctionResp;
import com.lyy.starter.common.resp.RespBodyUtil;
import java.util.HashMap;
import java.util.List;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 页面功能规则配置 最终业务数据处理
 * <AUTHOR>
 * @date 2024/09/24
 */
@Component
@Slf4j
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class FinalPageFunctionHandler extends AbstractPageFunctionHandler implements BizPageFunctionHandler {

    private final AuthorityService authorityService;

    @Override
    public void filterFunctionResult(AdUserInfoDTO currentUser, PersonalFunctionResp functionResult, HashMap<String, String> commonParam) {
        // 标准版角色加锁
        applySaasStandardRoleLock(functionResult, currentUser.getAuthorityUserId(), RamFunctionUtil::setSubscriptLock);
    }

    @Override
    public void filterFunctionCenterResult(AdUserInfoDTO currentUser, FunctionCenterResp functionResult,
            HashMap<String, String> commonParam) {
        // 标准版角色加锁
        applySaasStandardRoleLock(functionResult, currentUser.getAuthorityUserId(), RamFunctionUtil::setSubscriptLock);
    }

    @Override
    public void filterMarketingResult(AdUserInfoDTO currentUser, MarketingFunctionResp marketingResult,
            HashMap<String, String> commonParam) {
        // 标准版角色加锁
        applySaasStandardRoleLock(marketingResult, currentUser.getAuthorityUserId(), RamFunctionUtil::setSubscriptLock);
    }

    private <T> void applySaasStandardRoleLock(T result, Long authorityUserId, BiConsumer<T, List<String>> lockFunction) {
        if (SAASPermissionUtil.enabled()) {
            // SAAS标准版角色
            List<AdRoleFunctionDTO> adRoleFunctionDTOList = RespBodyUtil.getData(authorityService
                    .getRoleFunctionByRoleValue(SystemConstants.SYSTEM_MERCHANT_ID, authorityUserId, SystemConstants.MERCHANT_SAAS_STANDARD));
            if (CollUtil.isNotEmpty(adRoleFunctionDTOList)) {
                // 不需要加锁的功能
                List<String> excludeFunctionId = adRoleFunctionDTOList.parallelStream().map(AdRoleFunctionDTO::getAuthFunctionId)
                        .collect(Collectors.toList());
                // 其他功能加锁
                lockFunction.accept(result, excludeFunctionId);
            }
        }
    }
}
