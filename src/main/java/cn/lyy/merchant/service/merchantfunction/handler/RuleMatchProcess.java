package cn.lyy.merchant.service.merchantfunction.handler;

import cn.hutool.extra.spring.SpringUtil;
import cn.lyy.merchant.service.merchantfunction.dto.FunctionRule;
import cn.lyy.merchant.service.merchantfunction.dto.MerchantDto;

import java.util.ArrayList;
import java.util.List;


public class RuleMatchProcess {

    private List<RuleMatchHandler> handlerList = new ArrayList<>();

    public RuleMatchProcess(){
        handlerList.add(SpringUtil.getBean("accountRuleMatchHandler"));
        handlerList.add(SpringUtil.getBean("blackWhiteRuleMatchHandler"));
        handlerList.add(SpringUtil.getBean("equipmentTypeRuleMatchHandler"));
        handlerList.add(SpringUtil.getBean("resourceRuleMatchHandler"));
        handlerList.add(SpringUtil.getBean("roleRuleMatchHandler"));
        handlerList.add(SpringUtil.getBean("equipmentBusinessTypeRuleMatchHandler"));
    }

    public FunctionRule doMatch(MerchantDto merchant,List<FunctionRule> rules){
        if(rules == null || rules.isEmpty()){
            return null;
        }
        //TODO 将规则按照功能优先级排序，假设已排序好
        for(FunctionRule rule : rules){
            boolean lastResult = true;
            //处理设备类型，商家白名单等规则
            for(RuleMatchHandler ruleMatchHandler : handlerList){
                boolean result = ruleMatchHandler.hander(merchant,rule);
                if(!result){
                    lastResult = false;
                    break;
                }
            }
            //剩下规则，主账户等

            if(lastResult){
                return rule;
            }
        }

        return null;
    }
}
