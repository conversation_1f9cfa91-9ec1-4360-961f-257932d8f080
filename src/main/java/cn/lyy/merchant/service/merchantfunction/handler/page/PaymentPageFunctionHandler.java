package cn.lyy.merchant.service.merchantfunction.handler.page;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.merchant.api.service.MerchantEquipmentService;
import cn.lyy.merchant.api.service.MerchantInfoClient;
import cn.lyy.merchant.constants.SysPageFunctionConstants;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import cn.lyy.merchant.service.merchant.MerchantMaterialService;
import cn.lyy.merchant.service.merchant.dto.MerchantInfoStatusConstant;
import cn.lyy.merchant.service.merchant.dto.MerchantInfoStatusDTO;
import com.lyy.ram.service.infrastructure.constant.RamServiceConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Optional;

import static java.util.Optional.ofNullable;

/**
 * 页面功能规则配置 支付业务数据处理
 */
@Component
@Slf4j
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class PaymentPageFunctionHandler extends AbstractPageFunctionHandler implements BizPageFunctionHandler {

    private final MerchantInfoClient merchantInfoClient;
    private final MerchantMaterialService merchantMaterialService;
    private final MerchantEquipmentService merchantEquipmentService;

    @Override
    public void handlePopupParam(AdUserInfoDTO currentUser, HashMap<String, String> functionParam) {
        // 商户进件状态
        long start = System.currentTimeMillis();
        String newBiePaymentTask = RamServiceConstant.NO;
        String merchantUnRealName = RamServiceConstant.NO;
        String merchantExperienceNotice = RamServiceConstant.NO;
        String merchantUnBindCard = RamServiceConstant.NO;
        String merchantAccessInfoError = RamServiceConstant.NO;
        MerchantInfoStatusDTO merchantInfoStatus = merchantMaterialService.getMerchantInfoTipStatus(currentUser.getAdOrgId(), null, null);
        Boolean isExperience = ofNullable(merchantInfoStatus.getIsExperience()).orElse(false);
        String merchantInExperience = isExperience ? RamServiceConstant.YES : RamServiceConstant.NO;
        // 新手任务判断,审核状态为3或者开通体验期即完成开通支付新手任务
        if (MerchantInfoStatusConstant.StatusEnum.APPROVED_EFFECTIVE.getCode().equals(merchantInfoStatus.getStatus())) {
            newBiePaymentTask = RamServiceConstant.YES;
            merchantInfoStatus = merchantMaterialService.getMerchantInfoStatus(currentUser.getAdOrgId(), null, null);
            if (MerchantInfoStatusConstant.TypeEnum.SUBMIT_INFO.getCode().equals(merchantInfoStatus.getType())) {
                if (MerchantInfoStatusConstant.StatusEnum.NOT_SUBMIT_INFO.getCode().equals(merchantInfoStatus.getStatus())) {
                    // 商户未提交资料
                    merchantUnBindCard = RamServiceConstant.YES;
                } else if (MerchantInfoStatusConstant.StatusEnum.REVIEW_REJECTED.getCode().equals(merchantInfoStatus.getStatus())
                        || MerchantInfoStatusConstant.StatusEnum.TO_NE_MODIFY.getCode().equals(merchantInfoStatus.getStatus())) {
                    // 资料审核不通过
                    merchantAccessInfoError = RamServiceConstant.YES;
                }
            }
            if (MerchantInfoStatusConstant.StatusEnum.MICRO_MERCHANT_SIGN_CONTRACT.getCode().equals(merchantInfoStatus.getStatus())
                    || MerchantInfoStatusConstant.StatusEnum.WECHAT_INTERNET_CONNECTION.getCode().equals(merchantInfoStatus.getStatus())
                    || MerchantInfoStatusConstant.StatusEnum.ALIPAY_SIGN_CONTRACT.getCode().equals(merchantInfoStatus.getStatus())) {
                // 商户存在未实名资料
                merchantUnRealName = RamServiceConstant.YES;
            }
            if (MerchantInfoStatusConstant.StatusEnum.EXPERIENCE_EXPIRE_NOTICE.getCode().equals(merchantInfoStatus.getStatus())) {
                // 商户体验期即将过期
                merchantExperienceNotice = RamServiceConstant.YES;
            }
        } else {
            merchantInfoStatus = merchantMaterialService.getMerchantInfoStatus(currentUser.getAdOrgId(), null, null);
            if (MerchantInfoStatusConstant.StatusEnum.EXPERIENCE_EXPIRE_NOTICE.getCode().equals(merchantInfoStatus.getStatus())) {
                // 商户体验期即将过期
                merchantExperienceNotice = RamServiceConstant.YES;
            }
        }
        functionParam.put(SysPageFunctionConstants.MERCHANT_UN_BIND_CARD, merchantUnBindCard);
        functionParam.put(SysPageFunctionConstants.MERCHANT_UN_REAL_NAME, merchantUnRealName);
        functionParam.put(SysPageFunctionConstants.MERCHANT_IN_EXPERIENCE, merchantInExperience);
        functionParam.put(SysPageFunctionConstants.MERCHANT_EXPERIENCE_NOTICE, merchantExperienceNotice);
        functionParam.put(SysPageFunctionConstants.MERCHANT_ACCESS_INFO_ERROR, merchantAccessInfoError);
        functionParam.put(SysPageFunctionConstants.NEWBIE_TASK_PAYMENT, newBiePaymentTask);
        debugLog("getCommonParam===>>>商户进件状态===>>>cost:{}", System.currentTimeMillis() - start);

        start = System.currentTimeMillis();
        String newBieEquipmentTask = RamServiceConstant.NO;
        boolean bindEquipment = ofNullable(merchantEquipmentService.hasFirstEquipment(currentUser.getAdOrgId())).map(BaseResponse::getData).orElse(false);
        if (bindEquipment) {
            newBieEquipmentTask = RamServiceConstant.YES;
        }
        functionParam.put(SysPageFunctionConstants.NEWBIE_TASK_EQUIPMENT, newBieEquipmentTask);
        debugLog("getCommonParam===>>>是否完成设备任务===>>>cost:{}", System.currentTimeMillis() - start);

        String newBieAllTask = RamServiceConstant.NO;
        if (RamServiceConstant.YES.equals(newBiePaymentTask) && RamServiceConstant.YES.equals(newBieEquipmentTask)) {
            newBieAllTask = RamServiceConstant.YES;
        }
        functionParam.put(SysPageFunctionConstants.NEWBIE_TASK_ALL, newBieAllTask);
        debugLog("getCommonParam===>>>是否完成新手任务===>>>{}", newBieAllTask);
    }


    @Override
    public void handleMarketingParam(AdUserInfoDTO currentUser, HashMap<String, String> functionParam) {
        // 查询商户是否完成新手支付任务
        MerchantInfoStatusDTO merchantInfoStatus = merchantMaterialService.getMerchantInfoTipStatus(currentUser.getAdOrgId(), null, null);
        if (MerchantInfoStatusConstant.StatusEnum.APPROVED_EFFECTIVE.getCode().equals(merchantInfoStatus.getStatus())) {
            functionParam.put(SysPageFunctionConstants.NEWBIE_TASK_PAYMENT, RamServiceConstant.YES);
        }
        String merchantOpenDivide = RamServiceConstant.NO;
        //体验期未开通分账
        if (ofNullable(merchantInfoStatus.getIsExperience()).orElse(false)) {
            functionParam.put(SysPageFunctionConstants.MERCHANT_OPEN_DIVIDE, merchantOpenDivide);
            return;
        }
        // 查询商户是否开通了分账
        BaseResponse<Boolean> checkOpenDivide = merchantInfoClient.checkOpenDivide(currentUser.getAdOrgId(), null, null);
        Optional<Boolean> openDivideOpt = Optional.ofNullable(checkOpenDivide).map(BaseResponse::getData);
        if (openDivideOpt.isPresent() && openDivideOpt.get()) {
            merchantOpenDivide = RamServiceConstant.YES;
        }
        functionParam.put(SysPageFunctionConstants.MERCHANT_OPEN_DIVIDE, merchantOpenDivide);
    }
}
