package cn.lyy.merchant.service.merchantfunction.impl;

import static cn.lyy.merchant.constants.SysPageFunctionConstants.WALLET_SETTLEMENT;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.PrimaryCategoryEnum;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.equipment.dto.equipment.EquipmentTypeDTO;
import cn.lyy.merchant.api.service.MerchantWhiteClient;
import cn.lyy.merchant.config.PrimaryCategoryConfig;
import cn.lyy.merchant.config.PrimaryCategoryConfig.PrimaryCategory;
import cn.lyy.merchant.constants.SysPageFunctionConstants;
import cn.lyy.merchant.dto.merchant.AdOrgAttachDTO;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import cn.lyy.merchant.redis.MerchantRedisClient;
import cn.lyy.merchant.redis.MerchantRedisKeyEnum;
import cn.lyy.merchant.repository.merchant.MerchantRepository;
import cn.lyy.merchant.service.equipment.EquipmentTypeService;
import cn.lyy.merchant.service.merchantfunction.SysPageFunctionService;
import cn.lyy.merchant.service.merchantfunction.handler.page.BizPageFunctionChain;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.lyy.ram.service.infrastructure.constant.RamServiceConstant;
import com.lyy.ram.service.interfaces.dto.function.request.MarketingFunctionQueryReq;
import com.lyy.ram.service.interfaces.dto.function.request.PersonalFunctionQueryReq;
import com.lyy.ram.service.interfaces.dto.function.response.FunctionCenterResp;
import com.lyy.ram.service.interfaces.dto.function.response.MarketingFunctionResp;
import com.lyy.ram.service.interfaces.dto.function.response.PersonalFunctionResp;
import com.lyy.ram.service.interfaces.feign.IFunctionFeignClient;
import com.lyy.starter.common.resp.RespBody;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class SysPageFunctionServiceImpl implements SysPageFunctionService {

    private final EquipmentTypeService equipmentTypeService;
    private final MerchantWhiteClient merchantWhiteClient;
    private final BizPageFunctionChain bizPageFunctionChain;
    private final IFunctionFeignClient functionFeignClient;
    private final MerchantRepository merchantRepository;
    private final PrimaryCategoryConfig primaryCategoryConfig;

    /**
     * 打印调试日志
     */
    private void debugLog(String msg, Object... var) {
        if (log.isDebugEnabled()) {
            log.debug(msg, var);
        }
    }

    private HashMap<String, String> getCommonParam(AdUserInfoDTO user) {
        HashMap<String, String> functionParam;
        // 缓存思路：用户访问首页功能时，一般会在短时间内请求功能中心和弹窗等接口，因此把功能参数进行较短的缓存，暂定10秒
        String functionParamJson = MerchantRedisClient.get(MerchantRedisKeyEnum.MERCHANT_PAGE_FUNCTION_PARAM, user.getAdUserId().toString());
        if (StrUtil.isEmpty(functionParamJson)) {
            functionParam = new HashMap<>();
            String uuid = UUID.randomUUID().toString(true);
            // 主账号
            long start = System.currentTimeMillis();
            String approver = user.getIsApprover() != null && user.getIsApprover() ? RamServiceConstant.YES : RamServiceConstant.NO;
            functionParam.put(SysPageFunctionConstants.APPROVER, approver);
            debugLog("getCommonParam===>>>{}主账号{}===>>>cost:{}", uuid, user.getAuthorityUserId(), System.currentTimeMillis() - start);

            start = System.currentTimeMillis();
            List<String> configTypes = Optional.ofNullable(merchantRepository.getMerchantAttach(user.getAdOrgId()))
                    .map(AdOrgAttachDTO::getPrimaryCategory)
                    .map(PrimaryCategoryEnum::getCategory)
                    .map(pc -> primaryCategoryConfig.getPrimaryCategories().get(pc.name()))
                    .map(PrimaryCategory::getEquipmentType)
                    .orElse(Lists.newArrayList());

            List<String> pcTypes = Lists.newArrayList(configTypes);
            debugLog("getCommonParam===>>>{}主营品类配置设备类型: {} cost:{}", uuid, pcTypes, System.currentTimeMillis() - start);

            // 设备类型
            start = System.currentTimeMillis();
            List<EquipmentTypeDTO> equipmentTypeList = equipmentTypeService.selectAllEquipmentType(user.getAdUserId(), user.getAdOrgId());
            if (CollUtil.isNotEmpty(equipmentTypeList)) {
                List<String> types = equipmentTypeList.stream().map(EquipmentTypeDTO::getValue).collect(Collectors.toList());
                pcTypes.addAll(types);
                functionParam.put(SysPageFunctionConstants.EQUIPMENT_TYPE_BALANCE_LIST,
                        equipmentTypeList.stream().filter(t -> t.getBalanceType() != null).distinct().map(t -> t.getBalanceType().toString()).collect(Collectors.joining(",")));
            }
            if (CollectionUtils.isNotEmpty(pcTypes)) {
                functionParam.put(SysPageFunctionConstants.EQUIPMENT_TYPE_LIST, pcTypes.stream().distinct().collect(Collectors.joining(",")));
            }
            debugLog("getCommonParam===>>>{}设备类型===>>> types: {}, cost:{}", uuid, pcTypes, System.currentTimeMillis() - start);

            // 黑白名单
            start = System.currentTimeMillis();
            BaseResponse<List<Integer>> bwList = merchantWhiteClient.getAllWhiteByDistributorId(user.getAdOrgId());
            if (bwList.getCode() == ResponseCodeEnum.SUCCESS.getCode() && CollUtil.isNotEmpty(bwList.getData())) {
                functionParam.put(SysPageFunctionConstants.BW_LIST, bwList.getData().stream().map(String::valueOf).collect(Collectors.joining(",")));
            }
            debugLog("getCommonParam===>>>{}黑白名单===>>>cost:{}", uuid, System.currentTimeMillis() - start);

            MerchantRedisClient.setex(MerchantRedisKeyEnum.MERCHANT_PAGE_FUNCTION_PARAM, user.getAdUserId().toString(), JSONObject.toJSONString(functionParam));
        } else {
            // 缓存直接返回
            functionParam = JSONObject.parseObject(functionParamJson, HashMap.class);
            debugLog("getCommonParam===>>>命中缓存，直接返回");
        }
        return functionParam;
    }

    private HashMap<String, String> getFunctionParam(AdUserInfoDTO user) {
        HashMap<String, String> functionParam = getCommonParam(user);
        bizPageFunctionChain.processFunctionParam(user, functionParam);
        return functionParam;
    }

    private HashMap<String, String> getPopupParam(AdUserInfoDTO currentUser) {
        // 弹窗相关逻辑文档整理：https://leyaoyao.yuque.com/vbutb7/qw48a7/gmzvqv8hs4zh8gfs#GiW2
        HashMap<String, String> functionParam = getCommonParam(currentUser);
        bizPageFunctionChain.processPopupParam(currentUser, functionParam);
        return functionParam;
    }

    private HashMap<String, String> getMarketingParam(AdUserInfoDTO currentUser) {
        HashMap<String, String> functionParam = getCommonParam(currentUser);
        bizPageFunctionChain.processMarketingParam(currentUser, functionParam);
        return functionParam;
    }

    @Override
    public RespBody<PersonalFunctionResp> getPersonalFunction(AdUserInfoDTO currentUser, PersonalFunctionQueryReq req, Boolean isWalletSettlement) {
        HashMap<String, String> commonParam = getFunctionParam(currentUser);
        commonParam.put(WALLET_SETTLEMENT,String.valueOf(isWalletSettlement));
        req.setParam(commonParam);
        RespBody<PersonalFunctionResp> resp = functionFeignClient.getPersonalFunction(req);
        if (log.isDebugEnabled()) {
            log.debug("PersonalFunctionResp:{}",resp);
        }
        if (resp.getBody() != null) {
            bizPageFunctionChain.filterFunctionResult(currentUser, resp.getBody(), commonParam);
        }
        return resp;
    }

    @Override
    public RespBody<FunctionCenterResp> getFunctionCenter(AdUserInfoDTO currentUser, PersonalFunctionQueryReq req, boolean isWalletSettlement) {
        HashMap<String, String> commonParam = getFunctionParam(currentUser);
        commonParam.put(WALLET_SETTLEMENT,String.valueOf(isWalletSettlement));
        req.setParam(commonParam);
        RespBody<FunctionCenterResp> resp = functionFeignClient.functionCenter(req);
        if (log.isDebugEnabled()) {
            log.debug("PersonalFunctionResp:{}",resp);
        }
        if (resp.getBody() != null) {
            bizPageFunctionChain.filterFunctionCenterResult(currentUser, resp.getBody(), commonParam);
        }
        return resp;
    }

    @Override
    public RespBody<PersonalFunctionResp> getPopupFunction(AdUserInfoDTO currentUser, PersonalFunctionQueryReq req) {
        HashMap<String, String> commonParam = getPopupParam(currentUser);
        req.setParam(commonParam);
        RespBody<PersonalFunctionResp> resp = functionFeignClient.getPersonalFunction(req);
        if (resp.getBody() != null) {
            bizPageFunctionChain.filterPopupResult(currentUser, resp.getBody(), commonParam);
        }
        return resp;
    }

    @Override
    public RespBody<MarketingFunctionResp> getMarketingFunction(AdUserInfoDTO currentUser, MarketingFunctionQueryReq req, boolean isWalletSettlement) {
        HashMap<String, String> commonParam = getMarketingParam(currentUser);
        commonParam.put(WALLET_SETTLEMENT,String.valueOf(isWalletSettlement));
        req.setParam(commonParam);
        RespBody<MarketingFunctionResp> resp = functionFeignClient.getMarketingFunction(req);
        if (resp.getBody() != null) {
            bizPageFunctionChain.filterMarketingResult(currentUser, resp.getBody(), commonParam);
        }
        return resp;
    }

    @Override
    public RespBody<MarketingFunctionResp> getMarketingCenter(AdUserInfoDTO currentUser, MarketingFunctionQueryReq req, boolean isWalletSettlement) {
        HashMap<String, String> commonParam = getMarketingParam(currentUser);
        commonParam.put(WALLET_SETTLEMENT,String.valueOf(isWalletSettlement));
        req.setParam(commonParam);
        RespBody<MarketingFunctionResp> resp = functionFeignClient.getMarketingCenter(req);
        if (resp.getBody() != null) {
            bizPageFunctionChain.filterMarketingResult(currentUser, resp.getBody(), commonParam);
        }
        return resp;
    }

}
