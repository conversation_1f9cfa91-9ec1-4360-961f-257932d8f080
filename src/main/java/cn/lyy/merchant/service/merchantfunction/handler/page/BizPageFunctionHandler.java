package cn.lyy.merchant.service.merchantfunction.handler.page;

import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import com.lyy.ram.service.interfaces.dto.function.response.FunctionCenterResp;
import com.lyy.ram.service.interfaces.dto.function.response.MarketingFunctionResp;
import com.lyy.ram.service.interfaces.dto.function.response.PersonalFunctionResp;

import java.util.HashMap;

/**
 * 页面功能规则配置 业务数据处理
 */
public interface BizPageFunctionHandler {

    /**
     * 处理功能参数
     */
    void handleFunctionParam(AdUserInfoDTO currentUser, HashMap<String, String> functionParam);

    /**
     * 处理弹窗参数
     */
    void handlePopupParam(AdUserInfoDTO currentUser, HashMap<String, String> functionParam);

    /**
     * 处理营销中心参数
     */
    void handleMarketingParam(AdUserInfoDTO currentUser, HashMap<String, String> functionParam);

    /**
     * 添加业务属性，并过滤最终首页功能结果
     */
    void filterFunctionResult(AdUserInfoDTO currentUser, PersonalFunctionResp functionResult, HashMap<String, String> commonParam);

    /**
     * 添加业务属性，并过滤最终功能中心结果
     */
    void filterFunctionCenterResult(AdUserInfoDTO currentUser, FunctionCenterResp functionResult, HashMap<String, String> commonParam);

    /**
     * 添加业务属性，并过滤最终弹窗结果
     */
    void filterPopupResult(AdUserInfoDTO currentUser, PersonalFunctionResp functionResult, HashMap<String, String> commonParam);

    /**
     * 添加业务属性，并过滤最终营销中心结果
     */
    void filterMarketingResult(AdUserInfoDTO currentUser, MarketingFunctionResp marketingResult, HashMap<String, String> commonParam);

}
