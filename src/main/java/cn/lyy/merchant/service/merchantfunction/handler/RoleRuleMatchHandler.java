package cn.lyy.merchant.service.merchantfunction.handler;

import cn.lyy.merchant.service.merchantfunction.dto.FunctionRule;
import cn.lyy.merchant.service.merchantfunction.dto.MerchantDto;
import org.springframework.stereotype.Component;

@Component
public class RoleRuleMatchHandler extends DefalutRuleMatchHandler implements RuleMatchHandler{

    @Override
    public boolean hander(MerchantDto merchant, FunctionRule rule) {
        return check(merchant.getRoles(), rule.getRole());
    }
}
