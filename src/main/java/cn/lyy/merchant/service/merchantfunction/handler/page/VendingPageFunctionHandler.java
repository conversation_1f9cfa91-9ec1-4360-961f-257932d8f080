package cn.lyy.merchant.service.merchantfunction.handler.page;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.dto.Pagination;
import cn.lyy.base.utils.RedisClient;
import cn.lyy.marketing.api.service.DistributorListConfigClient;
import cn.lyy.marketing.dto.list.DistributorListEntranceDTO;
import cn.lyy.merchant.api.service.AdOrgClient;
import cn.lyy.merchant.api.service.MerchantEquipmentService;
import cn.lyy.merchant.constants.SysPageFunctionConstants;
import cn.lyy.merchant.dto.merchant.AdOrgFullDTO;
import cn.lyy.merchant.dto.merchant.EquipmentThirdFactoryCountDTO;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import cn.lyy.vending.common.dto.param.VendingOrderParam;
import cn.lyy.vending.common.dto.response.DistributorAlgorithmFeeDTO;
import cn.lyy.vending.common.enums.base.VendingOrderEnum;
import cn.lyy.vending.rpc.base.ResultResponse;
import cn.lyy.vending.rpc.dto.DynamicIdentityDistributorPageDTO;
import cn.lyy.vending.rpc.dto.req.DynamicIdentityDistributorQueryReq;
import cn.lyy.vending.rpc.feign.DynamicIdentityDistributorRpc;
import cn.lyy.vending.rpc.feign.VendingOrderRpc;
import com.alibaba.fastjson.JSON;
import com.lyy.equipment.interfaces.feign.equipment.IotEquipmentExtensionServiceFeignClient;
import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.ram.service.infrastructure.constant.RamServiceConstant;
import com.lyy.ram.service.infrastructure.util.RamFunctionUtil;
import com.lyy.ram.service.interfaces.dto.function.response.FunctionCenterResp;
import com.lyy.ram.service.interfaces.dto.function.response.PersonalFunctionResp;
import com.lyy.user.account.infrastructure.account.dto.AccountConditionDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountDTO;
import com.lyy.user.account.infrastructure.account.feign.AccountFeignClient;
import com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum;
import com.lyy.user.account.infrastructure.resp.RespBody;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.lyy.vending.rpc.utils.VendingResultUtils.checkBaseResponse;
import static java.util.Optional.ofNullable;

/**
 * 页面功能规则配置 零售业务数据处理
 */
@Component
@Slf4j
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class VendingPageFunctionHandler extends AbstractPageFunctionHandler implements BizPageFunctionHandler {

    // 待确认订单功能编码
    private final static String FUNC_ABNORMAL_ORDER_SHJ = "abnormal_order_shj";
    private final static String DYNAMIC_IDENTITY_CONFIG_KEY = "dynamic:identity:config";

    private final DistributorListConfigClient distributorListConfigClient;
    private final MerchantEquipmentService merchantEquipmentService;
    private final IotEquipmentExtensionServiceFeignClient iotEquipmentExtensionServiceFeignClient;
    private final DynamicIdentityDistributorRpc dynamicIdentityDistributorRpc;
    private final AccountFeignClient accountFeignClient;
    private final AdOrgClient adOrgClient;
    private final VendingOrderRpc vendingOrderService;

    /**
     * 获取商家复购易活动记录
     */
    private DistributorListEntranceDTO getDistributorListEntrance(AdUserInfoDTO user) {
        BaseResponse<List<DistributorListEntranceDTO>> distributorListConfigResp = distributorListConfigClient.findEntrance(user.getAdOrgId());
        Optional<List<DistributorListEntranceDTO>> distributorListConfigOpt = Optional.ofNullable(distributorListConfigResp).map(BaseResponse::getData);
        if (distributorListConfigOpt.isPresent()) {
            List<DistributorListEntranceDTO> configList = distributorListConfigOpt.get();
            if (CollUtil.isNotEmpty(configList)) {
                configList = configList.stream().filter(cfg -> cfg.getType() == 1).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(configList)) {
                    return configList.get(0);
                }
            }
        }
        return null;
    }

    private AccountDTO getMerchantBalance(Long userId) {
        AccountDTO accountDTO = null;
        // 查询账户余额
        AccountConditionDTO accountConditionDTO = new AccountConditionDTO();
        accountConditionDTO.setMerchantId(0L);
        accountConditionDTO.setUserId(userId);
        accountConditionDTO.setClassifies(CollectionUtil.newArrayList(BenefitClassifyEnum.MERCHANT_RECOGNITION_BALANCE.getCode()));
        RespBody<List<AccountDTO>> respBody = accountFeignClient.accountInfo(accountConditionDTO);
        List<AccountDTO> accounts = null;
        if (respBody != null) {
            if (!GlobalErrorCode.OK.getCode().equals(respBody.getCode())) {
                if (log.isWarnEnabled()) {
                    log.warn("查询商户余额接口失败，错误信息：{}", respBody);
                }
                //商户不存在1010005,，如果商户不存在，当作余额为0
                if ("1010005".equals(respBody.getCode())) {
                    accountDTO = new AccountDTO();
                    accountDTO.setBalance(BigDecimal.ZERO);
                    accountDTO.setTotal(BigDecimal.ZERO);
                    return accountDTO;
                }
                log.error("handlePopupParam===>>>查询商户余额接口失败，错误信息：{}", JSON.toJSONString(respBody));
            } else {
                accounts = respBody.getBody();
            }
        }

        if (accounts == null) {
            return null;
        }
        for (AccountDTO dto : accounts) {
            if (BenefitClassifyEnum.MERCHANT_RECOGNITION_BALANCE.getCode().equals(dto.getClassify())) {
                accountDTO = dto;
            }
        }
        return accountDTO;
    }

    /**
     * 根据商户ID获取商户设置了算法识别费的渠道
     */
    private Map<String, BigDecimal> getDistributorAlgorithmFeeDTOS(Long distributorId) {
        Map<String, BigDecimal> result = new HashMap<>();
        try {
            DynamicIdentityDistributorQueryReq dynamicIdentityDistributorQueryReq = new DynamicIdentityDistributorQueryReq();
            dynamicIdentityDistributorQueryReq.setDistributorIds(new HashSet<>(Collections.singletonList(distributorId)));
            dynamicIdentityDistributorQueryReq.setPageNo(1);
            dynamicIdentityDistributorQueryReq.setPageSize(1);
            ResultResponse<Pagination<DynamicIdentityDistributorPageDTO>> resultResponse = dynamicIdentityDistributorRpc.pageList(dynamicIdentityDistributorQueryReq);
            checkBaseResponse(true, resultResponse, "查询识别费商户列表");
            DynamicIdentityDistributorPageDTO distributorDTO = null;
            if (resultResponse.getData() != null && CollectionUtil.isNotEmpty(resultResponse.getData().getItems())) {
                distributorDTO = resultResponse.getData().getItems().get(0);
            }

            // 设置了识别费的算法
            List<DistributorAlgorithmFeeDTO> setFeeAlgorithmList;
            // 如果商家设置了某个算法渠道的识别费，那么这个值一定不为空,有可能的值是[],所有小于3也直接返回空
            if (distributorDTO == null
                    || StringUtils.isEmpty(distributorDTO.getDistributorAlgorithmFee())
                    || distributorDTO.getDistributorAlgorithmFee().length() < 3) {
                log.warn("没有为商户{}设置算法识别费。", distributorId);
                return result;
            }
            setFeeAlgorithmList = JSON.parseArray(distributorDTO.getDistributorAlgorithmFee(), DistributorAlgorithmFeeDTO.class);
            result = setFeeAlgorithmList.stream()
                    .collect(Collectors.toMap(DistributorAlgorithmFeeDTO::getChannel, DistributorAlgorithmFeeDTO::getUserFee));

        } catch (Exception e) {
            log.error("获取商户设置算法列表失败:" + e);
        }
        return result;
    }

    private int getAbnormalOrderCount(AdUserInfoDTO currentUser) {
        VendingOrderParam param = VendingOrderParam.builder()
                .createdAfter(DateUtil.offsetDay(new Date(), -180))
                .createdBefore(new Date())
                .tradeStatusList(CollectionUtil.newArrayList(VendingOrderEnum.TradeStatus.CONFIRM.getTradeStatus(), VendingOrderEnum.TradeStatus.OPEN_DOOR.getTradeStatus(), VendingOrderEnum.TradeStatus.CLOSE_DOOR.getTradeStatus(), VendingOrderEnum.TradeStatus.IN_SETTLEMENT.getTradeStatus()))
                .extStatusList(VendingOrderEnum.ExStatus.abnormalStatus)
                .orderType(VendingOrderEnum.OrderType.DYNAMIC.getType())
                .exHandleStatus(VendingOrderEnum.ExHandleStatus.WAIT_APPLY.getExHandleStatus())
                .adOrgId(currentUser.getAdOrgId())
                .build();
        ResultResponse<Long> longResultResponse = vendingOrderService.countByCondition(param);
        return ofNullable(longResultResponse).map(ResultResponse::getData).map(Long::intValue).orElse(0);
    }

    @Override
    public void handlePopupParam(AdUserInfoDTO currentUser, HashMap<String, String> functionParam) {
        long start = System.currentTimeMillis();
        // 复购易弹窗
        DistributorListEntranceDTO config = getDistributorListEntrance(currentUser);
        if (config != null && config.getPopupType() == 1) {
            functionParam.put(SysPageFunctionConstants.MERCHANT_FGY_POPUP, RamServiceConstant.YES);
        }

        // 识别费通知弹窗
        String equipmentTypeList = functionParam.get(SysPageFunctionConstants.EQUIPMENT_TYPE_LIST);
        if (equipmentTypeList != null && equipmentTypeList.contains("SHJ")) {
            boolean dynamicArkIsExist = false;
            List<Long> equipIds = ofNullable(merchantEquipmentService.findEquipmentIdsByMerchantId(currentUser.getAdOrgId(), Boolean.TRUE)).map(BaseResponse::getData).orElse(null);
            if (CollectionUtils.isNotEmpty(equipIds)) {
                // 是否存在有 business_type = 82 的设备
                Integer count = ofNullable(iotEquipmentExtensionServiceFeignClient.countByBusinessTypeAndEids(82, equipIds)).map(com.lyy.equipment.infrastructure.resp.RespBody::getBody).orElse(0);
                if (count > 0) {
                    debugLog("handlePopupParam===>>>零售弹窗===>>>有动态柜:{}", currentUser.getAdOrgId());
                    dynamicArkIsExist = true;
                }
            }
            if (dynamicArkIsExist) {
                AdOrgFullDTO adOrgFullDTO = adOrgClient.selectAllInfoByAdOrgId(currentUser.getAdOrgId()).getBody();
                ofNullable(adOrgFullDTO).map(AdOrgFullDTO::getPlatformUserId).ifPresent(platformUserId -> {
                    // 查询该商户的余额
                    ofNullable(this.getMerchantBalance(adOrgFullDTO.getPlatformUserId())).ifPresent(accountDTO -> {
                        // 查询该商户有多少设备
                        List<EquipmentThirdFactoryCountDTO> thirdFactoryCount = merchantEquipmentService.countEquipmentThirdFactory(currentUser.getAdOrgId()).getBody();
                        if (CollectionUtil.isNotEmpty(thirdFactoryCount)) {
                            boolean balanceLessThanTen = false;
                            boolean balanceNotEnough = false;
                            // 设备总数
                            int count = thirdFactoryCount.stream().mapToInt(EquipmentThirdFactoryCountDTO::getCount).sum();
                            BigDecimal minAmount = new BigDecimal(10).multiply(new BigDecimal(count));
                            // 如果有设备，判断余额小于设备数*10 元，，添加一条弹窗
                            if (accountDTO.getBalance().compareTo(minAmount) < 0) {
                                debugLog("handlePopupParam===>>>零售弹窗===>>>余额小于设备数*10:{}", currentUser.getAdOrgId());
                                balanceLessThanTen = true;
                            }
                            // 查询配置信息
                            String configString = RedisClient.get(DYNAMIC_IDENTITY_CONFIG_KEY);
                            debugLog(configString);
                            debugLog("handlePopupParam===>>>零售弹窗===>>>DYNAMIC_IDENTITY_CONFIG_KEY:{}", configString);
                            DynamicIdentityFeeConfigDTO dynamicIdentityFeeConfigDTO = JSON.parseObject(configString, DynamicIdentityFeeConfigDTO.class);
                            if (dynamicIdentityFeeConfigDTO != null) {
                                // 获取商户设置过的算法识别费
                                Map<String, BigDecimal> userChannelFeeMap = this.getDistributorAlgorithmFeeDTOS(currentUser.getAdOrgId());
                                Map<String, DynamicIdentityFeeConfigItemDTO> dynamicIdentityFeeConfigItemMap = dynamicIdentityFeeConfigDTO.getDynamicIdentityFeeList().stream()
                                        .peek(feeDTO -> {
                                            BigDecimal userFee = userChannelFeeMap.get(feeDTO.getChannel());
                                            if (userFee != null) {
                                                feeDTO.setUserFee(userFee);
                                            }
                                        })
                                        .collect(Collectors.toMap(DynamicIdentityFeeConfigItemDTO::getChannel, Function.identity(), (o, o2) -> o));

                                for (EquipmentThirdFactoryCountDTO equipmentThirdFactoryCountDTO : thirdFactoryCount) {
                                    DynamicIdentityFeeConfigItemDTO dynamicIdentityFeeConfigItemDTO = dynamicIdentityFeeConfigItemMap.get(equipmentThirdFactoryCountDTO.getThirdFactory());
                                    if (dynamicIdentityFeeConfigItemDTO != null) {
                                        // 当前余额不够扣除费用，添加一条弹窗
                                        if (accountDTO.getBalance().compareTo(dynamicIdentityFeeConfigItemDTO.getUserFee()) < 0) {
                                            debugLog("handlePopupParam===>>>零售弹窗===>>>余额不够扣除费用:{}", currentUser.getAdOrgId());
                                            balanceNotEnough = true;
                                            break;
                                        }
                                    }
                                }
                            }
                            if (balanceNotEnough) {
                                functionParam.put(SysPageFunctionConstants.IDENTIFY_FEE_BNE_POPUP, RamServiceConstant.YES);
                            } else if (balanceLessThanTen) {
                                functionParam.put(SysPageFunctionConstants.IDENTIFY_FEE_BLT_POPUP, RamServiceConstant.YES);
                            }
                        }
                    });
                });
            }
        }
        debugLog("handlePopupParam===>>>零售弹窗===>>>cost:{}", System.currentTimeMillis() - start);
    }

    @Override
    public void handleMarketingParam(AdUserInfoDTO currentUser, HashMap<String, String> functionParam) {
        // 查询商户是否有复购易活动配置
        DistributorListEntranceDTO config = getDistributorListEntrance(currentUser);
        if (config != null && config.getStatus() == 1) {
            functionParam.put(SysPageFunctionConstants.MERCHANT_FGY_CONFIG, RamServiceConstant.YES);
        }
    }

    @Override
    public void filterFunctionResult(AdUserInfoDTO currentUser, PersonalFunctionResp functionResult, HashMap<String, String> commonParam) {
        if (RamFunctionUtil.hasFunction(functionResult, FUNC_ABNORMAL_ORDER_SHJ)) {
            // 查待确认订单的数量
            int count = getAbnormalOrderCount(currentUser);
            if (count > 0) {
                // 设置脚标数量
                RamFunctionUtil.setSubscriptContent(functionResult, FUNC_ABNORMAL_ORDER_SHJ, String.valueOf(count));
            }
        }
    }

    @Override
    public void filterFunctionCenterResult(AdUserInfoDTO currentUser, FunctionCenterResp functionResult, HashMap<String, String> commonParam) {
        if (RamFunctionUtil.hasFunction(functionResult, FUNC_ABNORMAL_ORDER_SHJ)) {
            // 调接口查待确认订单的数量
            int count = getAbnormalOrderCount(currentUser);
            if (count > 0) {
                // 设置脚标数量
                RamFunctionUtil.setSubscriptContent(functionResult, FUNC_ABNORMAL_ORDER_SHJ, String.valueOf(count));
            }
        }
    }

    @Getter
    @Setter
    public static class DynamicIdentityFeeConfigDTO {
        private List<DynamicIdentityFeeConfigItemDTO> dynamicIdentityFeeList;
    }

    @Getter
    @Setter
    public static class DynamicIdentityFeeConfigItemDTO {

        /**
         * 渠道
         */
        private String channel;

        /**
         * 渠道名称
         */
        private String channelName;
        /**
         * 用户费用名称
         */
        private String userFeeName;

        /**
         * 乐摇摇费用名称
         */
        private String lyyFeeName;

        /**
         * 用户费用
         */
        private BigDecimal userFee;

        /**
         * 乐摇摇的费用
         */
        private BigDecimal lyyFee;

        /**
         * logo
         */
        private String logo;

    }
}
