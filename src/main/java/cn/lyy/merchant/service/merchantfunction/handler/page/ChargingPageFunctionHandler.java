package cn.lyy.merchant.service.merchantfunction.handler.page;

import static cn.lyy.merchant.constants.SysPageFunctionConstants.MARKET_PRE_RECHARGE;
import static cn.lyy.merchant.constants.SysPageFunctionConstants.WALLET_SETTLEMENT;
import static java.util.Optional.ofNullable;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.lyy.authority_service_api.AdResourcesDTO;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.merchant.constants.SysPageFunctionConstants;
import cn.lyy.merchant.constants.SystemConstants;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import cn.lyy.merchant.microservice.SystemChargeEquipmentApi;
import cn.lyy.merchant.microservice.charging.CarChargingMarketApi;
import cn.lyy.merchant.microservice.charging.CarChargingOrderApi;
import cn.lyy.merchant.microservice.charging.dto.OrderRefundApplyPageReq;
import cn.lyy.merchant.microservice.charging.dto.OrderRefundApplyPageResp;
import cn.lyy.merchant.microservice.charging.dto.ShowGaodeMarketDTO;
import cn.lyy.merchant.microservice.charging.dto.ShowGaodeMarketVO;
import cn.lyy.merchant.service.remote.AuthorityService;
import cn.lyy.tools.equipment.LyyConstant;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.lyy.advert.api.internal.api.distributor.insurance.LyyDistributorInsuranceApi;
import com.lyy.advert.api.internal.api.freecard.FreeCardUserApi;
import com.lyy.advert.api.internal.dto.cdz.LyyCdzWorkOrderDTO;
import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.payment.divide.bff.interfaces.MerchantDivideAssociationClient;
import com.lyy.payment.divide.bff.request.MerchantDivideAssociationQueryReqDTO;
import com.lyy.ram.service.infrastructure.constant.RamServiceConstant;
import com.lyy.ram.service.infrastructure.util.RamFunctionUtil;
import com.lyy.ram.service.interfaces.dto.function.response.FunctionCenterItemResp;
import com.lyy.ram.service.interfaces.dto.function.response.FunctionCenterResp;
import com.lyy.ram.service.interfaces.dto.function.response.MarketingFunctionResp;
import com.lyy.ram.service.interfaces.dto.function.response.PersonalFunctionResp;
import com.lyy.starter.common.resp.RespBody;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 页面功能规则配置 充电桩业务数据处理
 */
@Component
@Slf4j
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class ChargingPageFunctionHandler extends AbstractPageFunctionHandler implements BizPageFunctionHandler {

    // 汽充桩退款申请功能编码
    private final static String FUNC_REFUND_APPLY = "refundApply";
    private final static String STATION_MANAGE = "charging-merchant-menu-stationManage";
    private final static String EQUIPMENT_PORT_MANAGE = "charging-merchant-menu-portManage";
    private final static String TEMPLATE_MANAGE = "charging-merchant-menu-templateManage";
    private final static String START_ORDER_MANAGE = "charging-merchant-menu-queryOrderRecord";

    /**
     * 分账查询入口
     */
    private final static String MERCHANT_DIVIDE_ACCESS = "merchantDivideAccess";

    private final static String GAODE_BANNER = "carChargingGaodeBanner";
    private final static String GAODE_MARKET_ICON = "carChargingGaode";
    private final static String GAODE_HOME_POP = "carChargingGaodePop";
    private final static String GAODE_ICON = "carChargingGaodeIcon";

    private final FreeCardUserApi freeCardUserApi;
    private final LyyDistributorInsuranceApi lyyDistributorInsuranceApi;
    private final SystemChargeEquipmentApi systemChargeApi;
    private final CarChargingOrderApi carChargingOrderApi;

    private final MerchantDivideAssociationClient merchantDivideAssociationClient;
    private final CarChargingMarketApi carChargingMarketApi;
    private final AuthorityService authorityService;

    private long getRefundApplyCount(AdUserInfoDTO currentUser) {
        OrderRefundApplyPageReq req = new OrderRefundApplyPageReq();
        req.setPageIndex(1);
        req.setPageSize(1);
        req.setMerchantId(currentUser.getAdOrgId());
        req.setApplyStatusEnumList(Collections.singletonList("APPLYING"));
        OrderRefundApplyPageResp resp = carChargingOrderApi.orderRefundApplyPage(req).getBody();
        if (resp != null) {
            return resp.getTotalSize();
        }
        return 0;
    }

    @Override
    public void handleFunctionParam(AdUserInfoDTO currentUser, HashMap<String, String> functionParam) {
        long start = System.currentTimeMillis();
        Integer lyyDistributorId = currentUser.getAdOrgId().intValue();
        BaseResponse<PageInfo<LyyCdzWorkOrderDTO>> cdzWorkOrderPage = freeCardUserApi.getWorkOrderPage(null, lyyDistributorId, null, 1, 1);
        BaseResponse<PageInfo<LyyCdzWorkOrderDTO>> xyjWorkOrderPage = freeCardUserApi.getWorkOrderPage(null, lyyDistributorId,
                LyyConstant.XYJ_TYPE_ID, 1, 1);
        Integer cdzWorkOrderNum = ofNullable(cdzWorkOrderPage).map(BaseResponse::getData).map(PageInfo::getSize).orElse(0);
        Integer xyjWorkOrderNum = ofNullable(xyjWorkOrderPage).map(BaseResponse::getData).map(PageInfo::getSize).orElse(0);
        Integer totalWorkOrderNum = cdzWorkOrderNum + xyjWorkOrderNum;
        functionParam.put(SysPageFunctionConstants.CHARGE_WORK_ORDER_NUM, String.valueOf(cdzWorkOrderNum));
        functionParam.put(SysPageFunctionConstants.WASH_WORK_ORDER_NUM, String.valueOf(xyjWorkOrderNum));
        functionParam.put(SysPageFunctionConstants.TOTAL_WORK_ORDER_NUM, String.valueOf(totalWorkOrderNum));
        if (log.isDebugEnabled()) {
            log.debug("handleFunctionParam===>>>充电桩功能===>>>cost:{}", System.currentTimeMillis() - start);
        }
    }

    @Override
    public void handlePopupParam(AdUserInfoDTO currentUser, HashMap<String, String> functionParam) {
        boolean isApprover = RamServiceConstant.YES.equals(functionParam.get(SysPageFunctionConstants.APPROVER));
        long start = System.currentTimeMillis();
        Integer cdzWorkOrderNum = ofNullable(freeCardUserApi.totalWorkOrderNum(currentUser.getAdOrgId().intValue(), null)).map(
                BaseResponse::getData).orElse(0);
        Integer xyjWorkOrderNum = ofNullable(
                freeCardUserApi.totalWorkOrderNum(currentUser.getAdOrgId().intValue(), LyyConstant.XYJ_TYPE_ID)).map(BaseResponse::getData)
                .orElse(0);
        Integer totalWorkOrderNum = cdzWorkOrderNum + xyjWorkOrderNum;
        functionParam.put(SysPageFunctionConstants.CHARGE_WORK_ORDER_NUM, String.valueOf(cdzWorkOrderNum));
        functionParam.put(SysPageFunctionConstants.WASH_WORK_ORDER_NUM, String.valueOf(xyjWorkOrderNum));
        functionParam.put(SysPageFunctionConstants.TOTAL_WORK_ORDER_NUM, String.valueOf(totalWorkOrderNum));

        // 充电桩保险
        boolean forcedOpenDialogFlag = ofNullable(
                lyyDistributorInsuranceApi.checkForcedOpenDialogFlag(currentUser.getAdOrgId(), isApprover)).map(BaseResponse::getData)
                .orElse(false);
        if (forcedOpenDialogFlag) {
            ofNullable(lyyDistributorInsuranceApi.searchForcedEquipmentGroupByTypeValue(currentUser.getAdOrgId(),
                    LyyConstant.CDZ_EQUIPMENT_VALUE)).map(BaseResponse::getData)
                    .ifPresent(forcedEquipmentGroupId -> functionParam.put(SysPageFunctionConstants.CHARGE_INSURANCE_GROUP_25,
                            String.valueOf(forcedEquipmentGroupId)));
        }
        boolean saasDialogFlag = ofNullable(lyyDistributorInsuranceApi.checkSaasDialogFlag(currentUser.getAdOrgId(), isApprover)).map(
                BaseResponse::getData).orElse(false);
        if (saasDialogFlag) {
            ofNullable(lyyDistributorInsuranceApi.searchSaasEquipmentGroupByTypeId(currentUser.getAdOrgId(),
                    LyyConstant.CDZ_EQUIPMENT_VALUE)).map(BaseResponse::getData)
                    .ifPresent(saasEquipmentGroupId -> functionParam.put(SysPageFunctionConstants.CHARGE_INSURANCE_GROUP_20,
                            String.valueOf(saasEquipmentGroupId)));
        }

        // 流量卡续费弹窗
        boolean flowCardRenew = ofNullable(systemChargeApi.merchantChargePop(currentUser.getAdOrgId())).map(
                com.lyy.starter.common.resp.RespBody::getBody).orElse(false);
        if (flowCardRenew) {
            functionParam.put(SysPageFunctionConstants.CHARGE_FLOWCARDRENEW_POPUP, RamServiceConstant.YES);
        }

        // 升购储值弹窗
        boolean upwardPurchase = ofNullable(
                freeCardUserApi.getSwichOpenStatus(currentUser.getAdOrgId().intValue(), LyyConstant.CDZ_TYPE_ID)).map(BaseResponse::getData)
                .orElse(true);
        if (!upwardPurchase) {
            // 未开启时才弹出
            functionParam.put(SysPageFunctionConstants.CHARGE_UPWARDPURCHASE_POPUP, RamServiceConstant.YES);
        }

        if (log.isDebugEnabled()) {
            log.debug("handlePopupParam===>>>充电桩弹窗===>>>cost:{}", System.currentTimeMillis() - start);
        }
    }


   public void walletSettlementMenu(AdUserInfoDTO currentUser, PersonalFunctionResp functionResult, HashMap<String, String> commonParam){
        boolean walletSettlement = Boolean.parseBoolean(commonParam.getOrDefault(WALLET_SETTLEMENT, "false"));
        if(walletSettlement){
            debugLog("钱包结算商家处理权限菜单,{}",currentUser.getUserName());
            if(functionResult instanceof FunctionCenterResp){
                RamFunctionUtil.removeFunction((FunctionCenterResp)functionResult,SysPageFunctionConstants.MEMBER_MENU);
                RamFunctionUtil.removeFunction((FunctionCenterResp)functionResult,SysPageFunctionConstants.APPEAL_FEEDBACK_MENU);
                RamFunctionUtil.removeFunction((FunctionCenterResp)functionResult,SysPageFunctionConstants.REDEEM_MENU);
                RamFunctionUtil.removeFunction((FunctionCenterResp)functionResult,SysPageFunctionConstants.LABEL_MANAGEMENT_MENU);
                RamFunctionUtil.removeFunction((FunctionCenterResp)functionResult,SysPageFunctionConstants.IC_CARD_MANAGE);
                RamFunctionUtil.removeFunction((FunctionCenterResp)functionResult,SysPageFunctionConstants.ELEC_COUNT);
                RamFunctionUtil.removeFunction((FunctionCenterResp)functionResult,SysPageFunctionConstants.COMMISSION_INDEX);
                RamFunctionUtil.removeFunction((FunctionCenterResp)functionResult,SysPageFunctionConstants.PRICE_MODEL);
                RamFunctionUtil.removeFunction((FunctionCenterResp)functionResult,SysPageFunctionConstants.OPERATIONAL_DATA);
            }else {
                RamFunctionUtil.removeFunction(functionResult,SysPageFunctionConstants.MEMBER_MENU);
                RamFunctionUtil.removeFunction(functionResult,SysPageFunctionConstants.APPEAL_FEEDBACK_MENU);
                RamFunctionUtil.removeFunction(functionResult,SysPageFunctionConstants.REDEEM_MENU);
                RamFunctionUtil.removeFunction(functionResult,SysPageFunctionConstants.LABEL_MANAGEMENT_MENU);
                RamFunctionUtil.removeFunction(functionResult,SysPageFunctionConstants.IC_CARD_MANAGE);
                RamFunctionUtil.removeFunction(functionResult,SysPageFunctionConstants.ELEC_COUNT);
                RamFunctionUtil.removeFunction(functionResult,SysPageFunctionConstants.COMMISSION_INDEX);
                RamFunctionUtil.removeFunction(functionResult,SysPageFunctionConstants.PRICE_MODEL);
                RamFunctionUtil.removeFunction(functionResult,SysPageFunctionConstants.OPERATIONAL_DATA);
            }

            List<AdResourcesDTO> resourcesDTOList = authorityService.getMenusAndButtonByUserId(currentUser.getAuthorityUserId());


            // 没有场地管理权限的删除场地管理
            boolean flag = resourcesDTOList.stream().anyMatch(menu -> STATION_MANAGE.equals(menu.getValue()));
            if(Boolean.FALSE.equals(flag)){
                debugLog("钱包结算商家处理权限菜单,删除场地");
                if(functionResult instanceof FunctionCenterResp){
                    RamFunctionUtil.removeFunction((FunctionCenterResp)functionResult,SysPageFunctionConstants.STATION_MENU);
                }else {
                    RamFunctionUtil.removeFunction(functionResult,SysPageFunctionConstants.STATION_MENU);
                }
            }
            // 端口管理权限
            boolean portFlag = resourcesDTOList.stream().anyMatch(menu -> EQUIPMENT_PORT_MANAGE.equals(menu.getValue()));
            if(Boolean.FALSE.equals(portFlag)){
                if(functionResult instanceof FunctionCenterResp){
                    RamFunctionUtil.removeFunction((FunctionCenterResp)functionResult,SysPageFunctionConstants.PORT_MANAGEMENT_MENU);
                }else {
                    RamFunctionUtil.removeFunction(functionResult,SysPageFunctionConstants.PORT_MANAGEMENT_MENU);
                }
            }

            // 模板管理权限
            boolean templateFlag = resourcesDTOList.stream().anyMatch(menu -> TEMPLATE_MANAGE.equals(menu.getValue()));
            if(Boolean.FALSE.equals(templateFlag)){
                if(functionResult instanceof FunctionCenterResp){
                    RamFunctionUtil.removeFunction((FunctionCenterResp)functionResult,SysPageFunctionConstants.TEMPLATE_MENU);
                }else {
                    RamFunctionUtil.removeFunction(functionResult,SysPageFunctionConstants.TEMPLATE_MENU);
                }
            }

            // 启动记录权限
            boolean orderFlag = resourcesDTOList.stream().anyMatch(menu -> START_ORDER_MANAGE.equals(menu.getValue()));
            if(Boolean.FALSE.equals(orderFlag)){
                if(functionResult instanceof FunctionCenterResp){
                    RamFunctionUtil.removeFunction((FunctionCenterResp)functionResult,SysPageFunctionConstants.CHARGING_ORDER_MENU);
                }else {
                    RamFunctionUtil.removeFunction(functionResult,SysPageFunctionConstants.CHARGING_ORDER_MENU);
                }
            }
        }
    }

    @Override
    public void filterFunctionResult(AdUserInfoDTO currentUser, PersonalFunctionResp functionResult, HashMap<String, String> commonParam) {

        walletSettlementMenu(currentUser, functionResult, commonParam);

        if (RamFunctionUtil.hasFunction(functionResult, FUNC_REFUND_APPLY)) {
            // 查退款申请的数量
            long count = getRefundApplyCount(currentUser);
            debugLog("filterFunctionResult===>>>获取汽充桩退款申请数量：{}", count);
            if (count > 0) {
                // 设置脚标数量
                RamFunctionUtil.setSubscriptContent(functionResult, FUNC_REFUND_APPLY, String.valueOf(count));
            }
        }
        //分账查询入口
        if (RamFunctionUtil.hasFunction(functionResult, MERCHANT_DIVIDE_ACCESS)) {
            debugLog("getAssociationCount===>>>分账查询入口：{}", JSON.toJSON(functionResult));
            MerchantDivideAssociationQueryReqDTO merchantDivideAssociationQueryReqDTO = new MerchantDivideAssociationQueryReqDTO();
            Long adOrgId = currentUser.getAdOrgId();
            merchantDivideAssociationQueryReqDTO.setDivideDistributorId(adOrgId);
            Integer data = ofNullable(merchantDivideAssociationClient.getAssociationCount(merchantDivideAssociationQueryReqDTO)).filter(
                            res -> GlobalErrorCode.OK.getCode().equals(res.getCode()))
                    .map(RespBody::getBody).orElse(0);
            debugLog("getAssociationCount===>>>分账查询关联关系数量：{}", data);
            if (data == 0) {
                RamFunctionUtil.removeFunction(functionResult, MERCHANT_DIVIDE_ACCESS);
            }
        }

        if (RamFunctionUtil.hasFunction(functionResult, GAODE_ICON)) {
            RespBody<ShowGaodeMarketVO> respBody = null;
            try {
                respBody = carChargingMarketApi.showGaodeMarket(
                        new ShowGaodeMarketDTO().setMerchantId(currentUser.getAdOrgId()));
            } catch (Exception e) {
                log.info("filterFunctionResult===>>>获取汽充桩高德营销配置结果:{}", respBody);
            }
            debugLog("filterFunctionResult===>>>获取汽充桩高德营销配置结果:{}", respBody);
            Boolean data = ofNullable(respBody)
                    .filter(resp -> GlobalErrorCode.OK.getCode().equals(resp.getCode()))
                    .map(resp -> resp.getBody().getShow())
                    .orElse(false);

            if (!data) {
                RamFunctionUtil.removeFunction(functionResult, GAODE_ICON);
            }
        }

        // 高德banner
        if (CollUtil.isNotEmpty(functionResult.getBannerList())) {
            for (FunctionCenterItemResp ele : functionResult.getBannerList()) {
                if (StrUtil.equals(ele.getValue(), GAODE_BANNER)) {
                    RespBody<ShowGaodeMarketVO> respBody = null;
                    try {
                        respBody = carChargingMarketApi.showGaodeMarket(
                                new ShowGaodeMarketDTO().setMerchantId(currentUser.getAdOrgId()));
                    } catch (Exception e) {
                        log.info("filterFunctionResult===>>>获取汽充桩高德营销配置结果:{}", respBody);
                    }

                    debugLog("filterFunctionResult===>>>获取汽充桩高德营销配置结果:{}", respBody);

                    Boolean data = ofNullable(respBody)
                            .filter(resp -> GlobalErrorCode.OK.getCode().equals(resp.getCode()))
                            .map(resp -> resp.getBody().getShow())
                            .orElse(false);

                    if (!data) {
                        functionResult.getBannerList().remove(ele);
                    }
                }

                break;
            }
        }
    }

    @Override
    public void filterFunctionCenterResult(AdUserInfoDTO currentUser, FunctionCenterResp functionResult,
            HashMap<String, String> commonParam) {

        walletSettlementMenu(currentUser, functionResult, commonParam);

        if (RamFunctionUtil.hasFunction(functionResult, FUNC_REFUND_APPLY)) {
            // 查退款申请的数量
            long count = getRefundApplyCount(currentUser);
            debugLog("filterFunctionResult===>>>获取汽充桩退款申请数量：{}", count);
            if (count > 0) {
                // 设置脚标数量
                RamFunctionUtil.setSubscriptContent(functionResult, FUNC_REFUND_APPLY, String.valueOf(count));
            }
        }

        //分账查询入口
        if (RamFunctionUtil.hasFunction(functionResult, MERCHANT_DIVIDE_ACCESS)) {
            debugLog("getAssociationCount===>>>分账查询入口：{}", JSON.toJSON(functionResult));
            MerchantDivideAssociationQueryReqDTO merchantDivideAssociationQueryReqDTO = new MerchantDivideAssociationQueryReqDTO();
            Long adOrgId = currentUser.getAdOrgId();
            merchantDivideAssociationQueryReqDTO.setDivideDistributorId(adOrgId);
            Integer data = ofNullable(merchantDivideAssociationClient.getAssociationCount(merchantDivideAssociationQueryReqDTO)).filter(
                            res -> GlobalErrorCode.OK.getCode().equals(res.getCode()))
                    .map(RespBody::getBody).orElse(0);
            debugLog("getAssociationCount===>>>分账查询关联关系数量：{}", data);
            if (data == 0) {
                RamFunctionUtil.removeFunction(functionResult, MERCHANT_DIVIDE_ACCESS);
            }
        }

        if (RamFunctionUtil.hasFunction(functionResult, GAODE_ICON)) {
            RespBody<ShowGaodeMarketVO> respBody = null;
            try {
                respBody = carChargingMarketApi.showGaodeMarket(
                        new ShowGaodeMarketDTO().setMerchantId(currentUser.getAdOrgId()));
            } catch (Exception e) {
                log.info("filterFunctionCenterResult===>>>获取汽充桩高德营销配置结果:{}", respBody);
            }
            debugLog("filterFunctionCenterResult===>>>获取汽充桩高德营销配置结果:{}", respBody);
            Boolean data = ofNullable(respBody)
                    .filter(resp -> GlobalErrorCode.OK.getCode().equals(resp.getCode()))
                    .map(resp -> resp.getBody().getShow())
                    .orElse(false);

            if (!data) {
                RamFunctionUtil.removeFunction(functionResult, GAODE_ICON);
            }
        }
    }


    @Override
    public void filterPopupResult(AdUserInfoDTO currentUser, PersonalFunctionResp functionResult, HashMap<String, String> commonParam) {
        if (RamFunctionUtil.hasPopup(functionResult, GAODE_HOME_POP)) {
            RespBody<ShowGaodeMarketVO> respBody = null;
            try {
                respBody = carChargingMarketApi.showGaodeMarket(
                        new ShowGaodeMarketDTO().setMerchantId(currentUser.getAdOrgId()));
            } catch (Exception e) {
                log.info("filterPopupResult===>>>获取汽充桩高德营销配置结果:{}", respBody);
            }

            debugLog("filterPopupResult===>>>获取汽充桩高德营销配置结果:{}", respBody);

            Boolean data = ofNullable(respBody)
                    .filter(resp -> GlobalErrorCode.OK.getCode().equals(resp.getCode()))
                    .map(resp -> resp.getBody().getShow())
                    .orElse(false);

            if (!data) {
                RamFunctionUtil.removePopup(functionResult, GAODE_HOME_POP);
            }
        }
    }

    @Override
    public void filterMarketingResult(AdUserInfoDTO currentUser, MarketingFunctionResp marketingResult,
            HashMap<String, String> commonParam) {
        if (RamFunctionUtil.hasMarketing(marketingResult, GAODE_MARKET_ICON)) {
            RespBody<ShowGaodeMarketVO> respBody = null;
            try {
                respBody = carChargingMarketApi.showGaodeMarket(
                        new ShowGaodeMarketDTO().setMerchantId(currentUser.getAdOrgId()));
            } catch (Exception e) {
                log.info("filterMarketingResult===>>>获取汽充桩高德营销配置结果:{}", respBody);
            }

            debugLog("filterMarketingResult===>>>获取汽充桩高德营销配置结果:{}", respBody);

            Boolean data = ofNullable(respBody)
                    .filter(resp -> GlobalErrorCode.OK.getCode().equals(resp.getCode()))
                    .map(resp -> resp.getBody().getShow())
                    .orElse(false);

            if (!data) {
                RamFunctionUtil.removeMarketing(marketingResult, GAODE_MARKET_ICON);
            }
        }

        boolean walletSettlement = Boolean.parseBoolean(commonParam.getOrDefault(WALLET_SETTLEMENT, "false"));
        if (walletSettlement) {
            RamFunctionUtil.removeMarketing(marketingResult, MARKET_PRE_RECHARGE);
        }

    }
}
