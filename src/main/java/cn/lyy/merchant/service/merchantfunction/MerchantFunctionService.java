package cn.lyy.merchant.service.merchantfunction;

import cn.lyy.merchant.service.merchantfunction.dto.FunctionRule;
import cn.lyy.merchant.service.merchantfunction.dto.MerchantDto;

import java.util.List;
import java.util.Map;

/**
 * B端商家功能
 * <AUTHOR>
 * @version 1.0
 * @since 2020/11/16
 */
public interface MerchantFunctionService {

    public Boolean checkMerchantFunction(MerchantDto merchant, List<FunctionRule> rules);

    public Map<String, Map<String,List>> getFunctionRule(Integer systemId);
}
