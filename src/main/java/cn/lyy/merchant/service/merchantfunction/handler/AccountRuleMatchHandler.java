package cn.lyy.merchant.service.merchantfunction.handler;

import cn.lyy.merchant.service.merchantfunction.dto.FunctionRule;
import cn.lyy.merchant.service.merchantfunction.dto.MerchantDto;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Set;

@Component
public class AccountRuleMatchHandler extends DefalutRuleMatchHandler implements RuleMatchHandler{

    @Override
    public boolean hander(MerchantDto merchant, FunctionRule rule) {
        Set<MerchantDto.BaseCheck> accountSet = new HashSet<>();
        MerchantDto.BaseCheck account = new MerchantDto().new BaseCheck();
        account.setId(merchant.getAccountType()+"");
        accountSet.add(account);
        return check(accountSet, rule.getAccountType() == null ? null : rule.getAccountType()+"");
    }
}
