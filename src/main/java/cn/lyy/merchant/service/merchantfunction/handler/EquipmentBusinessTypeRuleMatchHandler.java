package cn.lyy.merchant.service.merchantfunction.handler;

import cn.lyy.merchant.service.merchantfunction.dto.FunctionRule;
import cn.lyy.merchant.service.merchantfunction.dto.MerchantDto;
import cn.lyy.merchant.service.merchantfunction.fegin.MerchantFeign;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Set;

@Slf4j
@Component
public class EquipmentBusinessTypeRuleMatchHandler extends DefalutRuleMatchHandler implements RuleMatchHandler{

    @Autowired
    private MerchantFeign merchantFeign;

    @Override
    public boolean hander(MerchantDto merchant, FunctionRule rule) {
        if(rule != null && rule.getExtend() != null && rule.getExtend().getString("equipmentBusinessType") != null && !rule.getExtend().getString("equipmentBusinessType").trim().isEmpty()){
            JSONObject equipmentBusiessType = merchantFeign.getMerchantEquipmentBusinessType(merchant.getId());
            if(equipmentBusiessType == null){
                return false;
            }

            if(equipmentBusiessType.getIntValue("code") != 0){
                return false;
            }

            JSONArray datas = equipmentBusiessType.getJSONArray("data");
            if(datas == null || datas.size() ==0){
                return false;
            }
            Set<MerchantDto.EquipmentBusinessType> equipmentTypes = new HashSet<>();
            for(int i=0; i<datas.size();i++){
                String businessType = datas.getString(i);
                if(businessType != null && !businessType.trim().isEmpty() && !"null".equals(businessType)){
                    MerchantDto.EquipmentBusinessType ebt = merchant.new EquipmentBusinessType();
                    ebt.setId(businessType);
                    equipmentTypes.add(ebt);
                }
            }
            if(equipmentTypes.isEmpty()){
                return false;
            }
            log.info("商家{}设备类型扩展：{}",merchant.getId(),JSONObject.toJSON(equipmentTypes).toString());
            merchant.setEquipmentBusinessTypes(equipmentTypes);
        }
        return check(merchant.getEquipmentBusinessTypes(), rule.getExtend() == null ? null : rule.getExtend().getString("equipmentBusinessType"));
    }
}
