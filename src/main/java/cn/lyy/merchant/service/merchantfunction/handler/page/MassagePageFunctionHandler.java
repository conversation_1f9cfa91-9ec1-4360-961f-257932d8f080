package cn.lyy.merchant.service.merchantfunction.handler.page;

import cn.lyy.lyy_cmember_service_api.UserCardService;
import cn.lyy.lyy_cmember_service_api.form.MemberCardQueryForm;
import cn.lyy.lyy_cmember_service_api.response.BaseResponse;
import cn.lyy.merchant.constants.SysPageFunctionConstants;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import cn.lyy.tools.equipment.LyyConstant;
import com.lyy.ram.service.infrastructure.constant.RamServiceConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static java.util.Optional.ofNullable;

/**
 * 页面功能规则配置 按摩业务数据处理
 */
@Component
@Slf4j
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class MassagePageFunctionHandler extends AbstractPageFunctionHandler implements BizPageFunctionHandler {

    private final UserCardService userCardService;

    @Override
    public void handlePopupParam(AdUserInfoDTO currentUser, HashMap<String, String> functionParam) {
        // 按摩椅次卡
        long start = System.currentTimeMillis();
        MemberCardQueryForm memberCardQueryForm = new MemberCardQueryForm();
        memberCardQueryForm.setCardType(Collections.singletonList("time"));
        List<Long> equipmentTypeIdList = new ArrayList<>(3);
        equipmentTypeIdList.add((long) LyyConstant.AMY_TYPE_ID);
        equipmentTypeIdList.add((long) LyyConstant.AMD_TYPE_ID);
        equipmentTypeIdList.add((long) LyyConstant.ZLJ_TYPE_ID);
        memberCardQueryForm.setEquipmentTypeIdList(equipmentTypeIdList);
        memberCardQueryForm.setDistributorId(currentUser.getAdOrgId().intValue());
        boolean effectiveMemberCard = ofNullable(userCardService.getHasEffectiveMemberCard(memberCardQueryForm)).map(BaseResponse::getData).orElse(false);
        if (effectiveMemberCard) {
            functionParam.put(SysPageFunctionConstants.MASSAGE_TIME_POPUP, RamServiceConstant.YES);
        }
        if (log.isDebugEnabled()) {
            log.debug("handlePopupParam===>>>按摩椅弹窗===>>>cost:{}", System.currentTimeMillis() - start);
        }
    }

    @Override
    public void handleMarketingParam(AdUserInfoDTO currentUser, HashMap<String, String> functionParam) {

    }
}
