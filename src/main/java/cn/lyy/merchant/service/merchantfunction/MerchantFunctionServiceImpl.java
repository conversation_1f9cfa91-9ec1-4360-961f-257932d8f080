package cn.lyy.merchant.service.merchantfunction;


import cn.lyy.merchant.service.merchantfunction.dto.FunctionRule;
import cn.lyy.merchant.service.merchantfunction.dto.MerchantDto;
import cn.lyy.merchant.service.merchantfunction.dto.SystemFunction;
import cn.lyy.merchant.service.merchantfunction.fegin.AuthorityFeign;
import cn.lyy.merchant.service.merchantfunction.fegin.MerchantFeign;
import cn.lyy.merchant.service.merchantfunction.handler.RuleMatchProcess;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Service
@Slf4j
public class MerchantFunctionServiceImpl implements MerchantFunctionService {

    private RuleMatchProcess ruleMatchProcess;
    @Autowired
    private AuthorityFeign authorityFeign;

    @Autowired
    private MerchantFeign merchantFeign;

    @Override
    public Boolean checkMerchantFunction(MerchantDto merchant, List<FunctionRule> rules) {
        //ruleMatchProcess.doMatch(merchant,rules);
        if(ruleMatchProcess == null){
            ruleMatchProcess = new RuleMatchProcess();
        }
        FunctionRule rule = ruleMatchProcess.doMatch(merchant,rules);
        if(rule != null){
            log.info("商家：{},匹配了规则：{}",JSONObject.toJSON(merchant).toString(),JSONObject.toJSON(rule).toString());
            return true;
        }
        return false;
    }

    @Override
    public Map<String,Map<String,List>> getFunctionRule(Integer systemId) {
        JSONObject result = authorityFeign.getAllFunctionRule(systemId);
        log.info("{}",result.toJSONString());
        if(result.containsKey("code") && result.getInteger("code") == 200){
            Map<String,Map<String,List>> map =new HashMap<>();

            //处理功能菜单
            if(result.getJSONObject("data").getJSONObject("functions") == null){
                return null;
            }else{
                Map<String,List> functionMap = new HashMap<>();
                //处理菜单功能
                JSONObject functions = result.getJSONObject("data").getJSONObject("functions");
                //获取菜单所有的分组
                Set<String> groups = functions.keySet();
                for(String group: groups){
                    if(functionMap.get(group) == null){
                        List<SystemFunction> functionList = functions.getJSONArray(group).toJavaList(SystemFunction.class);
                        functionMap.put(group,functionList);
                    }
                }
                map.put("functions",functionMap);
            }

            //处理规则
            if(result.getJSONObject("data").getJSONObject("rules") != null){
                Map<String,List> ruleMap = new HashMap<>();
                //获取菜单规则
                JSONObject rules = result.getJSONObject("data").getJSONObject("rules");
                Set<String> ruleKeys = rules.keySet();
                for(String function: ruleKeys){
                    if(ruleMap.get(function) == null){
                        List<FunctionRule> functionRuleList = rules.getJSONArray(function).toJavaList(FunctionRule.class);
                        ruleMap.put(function,functionRuleList);
                    }
                }
                map.put("rules",ruleMap);
            }

            return map;
        }
        return null;
    }
}
