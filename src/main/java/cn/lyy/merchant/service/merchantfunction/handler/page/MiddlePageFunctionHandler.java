package cn.lyy.merchant.service.merchantfunction.handler.page;

import static java.util.Optional.ofNullable;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.lyy.authority_service_api.AdUserDTO;
import cn.lyy.authority_service_api.miscroservice.AuthorityService;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.lyy_api.IncomeVersionClient;
import cn.lyy.merchant.api.service.EquipmentProblemClient;
import cn.lyy.merchant.api.service.MerchantEquipmentService;
import cn.lyy.merchant.api.service.MerchantWhiteClient;
import cn.lyy.merchant.constants.SysPageFunctionConstants;
import cn.lyy.merchant.dto.merchant.EquipmentProblemCountReqDTO;
import cn.lyy.merchant.dto.merchant.LyyDistributorWhiteListVO;
import cn.lyy.merchant.dto.merchant.MerchantUserEquipmentProblemCountVO;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import cn.lyy.merchant.utils.RemoteResponseUtils;
import com.lyy.charge.dto.product.request.AiAbleUseRequest;
import com.lyy.charge.dto.subscribe.ProductSubscribeQueryDTO;
import com.lyy.charge.dto.subscribe.SubscribedProductDetailVO;
import com.lyy.charge.enums.bill.ProductCode;
import com.lyy.charge.rpc.product.AiProductClient;
import com.lyy.charge.rpc.product.ProductSubscribeClient;
import com.lyy.equipment.interfaces.feign.equipment.IotEquipmentExtensionServiceFeignClient;
import com.lyy.platform.dto.response.inform.PlatformChargeReceiptOrderRemindDTO;
import com.lyy.platform.dto.response.rule.PlatformBizChargeEquipmentTypeDTO;
import com.lyy.platform.enums.PlatformEnum;
import com.lyy.platform.rpc.PlatformChargeInformApi;
import com.lyy.platform.rpc.rule.PlatformBizChargeEquipmentTypeApi;
import com.lyy.ram.service.infrastructure.constant.RamServiceConstant;
import com.lyy.ram.service.infrastructure.util.RamFunctionUtil;
import com.lyy.ram.service.interfaces.dto.function.response.FunctionCenterResp;
import com.lyy.ram.service.interfaces.dto.function.response.PersonalFunctionResp;
import com.lyy.starter.common.resp.RespBody;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 页面功能规则配置 中台业务数据处理
 */
@Component
@Slf4j
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class MiddlePageFunctionHandler extends AbstractPageFunctionHandler implements BizPageFunctionHandler {

    // 申诉反馈功能编码
    private static final String FUNC_COMPLAINT_FEEDBACK = "complaint_feedback";

    /**
     * AI功能菜单
     */
    private static final String AI_FUNCTION_BANNER = "aiFunctionBanner";
    private static final String OLD_INCOME_AUTH_CODE = "oldOperatingStatistics";

    private static final String NEW_INCOME_VERSION = "merchantReport";
    private static final String OLD_INCOME_VERSION = "incomeStatistics";

    private final MerchantEquipmentService merchantEquipmentService;
    private final IotEquipmentExtensionServiceFeignClient iotEquipmentExtensionServiceFeignClient;
    private final PlatformChargeInformApi platformChargeInformApi;
    private final PlatformBizChargeEquipmentTypeApi bizChargeEquipmentTypeApi;
    private final ProductSubscribeClient productSubscribeClient;
    private final AiProductClient aiProductClient;
    private final EquipmentProblemClient equipmentProblemClient;
    private final AuthorityService authorityService;
    private final MerchantWhiteClient merchantWhiteClient;
    private final IncomeVersionClient incomeVersionClient;

    @Value("${page.function.jykb.whiteList:4090}")
    private String jykbWhiteList;

    @Value("${page.banner.aiBanner.whiteList:8968}")
    private String aiBannerWhiteList;

    @Value("${page.popup.canceledSaasExperience.whiteList:8970}")
    private Integer canceledSaasExperienceWhiteList;

    @Value("${page.popup.canceledSaasExperience.showDays:30}")
    private Long canceledSaasExperienceShowDays;

    @Value("${check.weak-password: true}")
    private boolean checkWeakPassword;

    @Value("${income.whiteList.old:1918}")
    private String oldIncomeWhiteList;

    @Value("${income.whiteList.new:1905}")
    private String newIncomeWhiteList;

    @Value("${income.whiteList.force-new:6646}")
    private String forceNewIncomeWhiteList;

    @Value("#{'${merchant.login.check.weakPassword:}'.split(',')}")
    @Getter
    @Setter
    private List<String> weakPasswords;

    private long getEquipmentProblemCount(AdUserInfoDTO currentUser) {
        EquipmentProblemCountReqDTO problemCountReqDTO = new EquipmentProblemCountReqDTO();
        problemCountReqDTO.setMerchantIds(Collections.singletonList(currentUser.getAdOrgId()));
        problemCountReqDTO.setAdUserIds(Collections.singletonList(currentUser.getAdUserId()));
        problemCountReqDTO.setStartDate(DateUtil.offsetDay(new Date(), -7));
        problemCountReqDTO.setEndDate(new Date());
        List<MerchantUserEquipmentProblemCountVO> problemCountList = equipmentProblemClient.count(problemCountReqDTO).getData();
        if (CollUtil.isNotEmpty(problemCountList)) {
            MerchantUserEquipmentProblemCountVO equipmentProblemCountVO = problemCountList.get(0);
            return ofNullable(equipmentProblemCountVO).map(MerchantUserEquipmentProblemCountVO::getProblemCount).orElse(0L);
        }
        return 0L;
    }

    @Override
    public void handlePopupParam(AdUserInfoDTO currentUser, HashMap<String, String> functionParam) {
        boolean isApprover = RamServiceConstant.YES.equals(functionParam.get(SysPageFunctionConstants.APPROVER));
        String equipmentTypeList = functionParam.get(SysPageFunctionConstants.EQUIPMENT_TYPE_LIST);

        // 服务费缴费弹窗：根据是否有动态柜标识（dynamicArkIsExist），调用不同的接口
        long start = System.currentTimeMillis();
        boolean dynamicArkIsExist = false;
        if (equipmentTypeList != null && equipmentTypeList.contains("SHJ")) {
            List<Long> equipIds = ofNullable(merchantEquipmentService.findEquipmentIdsByMerchantId(currentUser.getAdOrgId(), Boolean.TRUE)).map(BaseResponse::getData).orElse(null);
            if (CollectionUtils.isNotEmpty(equipIds)) {
                // 是否存在有 business_type = 82 的设备
                Integer count = ofNullable(iotEquipmentExtensionServiceFeignClient.countByBusinessTypeAndEids(82, equipIds)).map(com.lyy.equipment.infrastructure.resp.RespBody::getBody).orElse(0);
                if (count > 0) {
                    dynamicArkIsExist = true;
                }
            }
        }
        Long platformId = PlatformEnum.LYY.getPlatformId();
        Long adUserId = currentUser.getAdUserId();
        if (isApprover) {
            adUserId = null;
        }
        // 具体逻辑是查询商户是否有对应的欠费平台收费账单，零售接口会每次查询需要弹出，原接口则是一天只会弹出一次
        BaseResponse<PlatformChargeReceiptOrderRemindDTO> platformChargeRemindResponse;
        if (dynamicArkIsExist) {
            platformChargeRemindResponse = platformChargeInformApi.findVendingChargeReceiptOrderRemind(platformId, currentUser.getAdOrgId(), adUserId, 1026L);
        } else {
            platformChargeRemindResponse = platformChargeInformApi.findChargeReceiptOrderRemind(platformId, currentUser.getAdOrgId(), adUserId);
        }
        PlatformChargeReceiptOrderRemindDTO remind = platformChargeRemindResponse.getData();
        if (remind != null) {
            functionParam.put(SysPageFunctionConstants.FEE_TO_PAY_TYPE, remind.getRemindType());
            functionParam.put(SysPageFunctionConstants.FEE_TO_PAY_CONTENT, remind.getRemindContent());
        }
        debugLog("handlePopupParam===>>>待缴服务费===>>>cost:{}", System.currentTimeMillis() - start);

        // 业务服务费弹窗
        start = System.currentTimeMillis();
        List<PlatformBizChargeEquipmentTypeDTO> bizChargeEquipmentTypeList = ofNullable(bizChargeEquipmentTypeApi.listAll(platformId)).map(com.lyy.starter.common.resp.RespBody::getBody).orElse(null);
        if (bizChargeEquipmentTypeList != null) {
            for (PlatformBizChargeEquipmentTypeDTO bizChargeEquipmentTypeDTO : bizChargeEquipmentTypeList) {
                if (equipmentTypeList != null && equipmentTypeList.contains(bizChargeEquipmentTypeDTO.getValue())) {
                    functionParam.put(SysPageFunctionConstants.BIZ_FEE_POPUP, RamServiceConstant.YES);
                    break;
                }
            }
        }
        debugLog("handlePopupParam===>>>业务服务费弹窗===>>>cost:{}", System.currentTimeMillis() - start);

        // 经营快报弹窗 -- 先判断是否在白名单内
        String bwList = functionParam.get(SysPageFunctionConstants.BW_LIST);
        if (bwList != null && bwList.contains(jykbWhiteList)) {
            ProductSubscribeQueryDTO query = new ProductSubscribeQueryDTO();
            query.setProductCode("ISMS");
            query.setMerchantId(currentUser.getAdOrgId());
            query.setAccountId(currentUser.getAdUserId());
            boolean upwardPurchase = ofNullable(productSubscribeClient.whetherSubscribed(query)).map(BaseResponse::getData).orElse(true);
            if (!upwardPurchase) {
                // 未订阅时才弹出
                functionParam.put(SysPageFunctionConstants.BUSINESS_EXPRESS_POPUP, RamServiceConstant.YES);
            }
            debugLog("handlePopupParam===>>>经营快报弹窗===>>>cost:{}", System.currentTimeMillis() - start);
        }
    }

    @Override
    public void filterFunctionResult(AdUserInfoDTO currentUser, PersonalFunctionResp functionResult, HashMap<String, String> commonParam) {
        if (RamFunctionUtil.hasFunction(functionResult, FUNC_COMPLAINT_FEEDBACK)) {
            // 查申诉反馈的数量
            long count = getEquipmentProblemCount(currentUser);
            if (count > 0) {
                // 设置脚标数量
                RamFunctionUtil.setSubscriptContent(functionResult, FUNC_COMPLAINT_FEEDBACK, String.valueOf(count));
            }
        }

        if (hasBanner(functionResult, AI_FUNCTION_BANNER)) {
            String bwList = commonParam.get(SysPageFunctionConstants.BW_LIST);
            log.debug("filterFunctionResult===>>>AI功能banner===>>>bwList:{}", bwList);
            if (bwList != null && bwList.contains(aiBannerWhiteList)) {
                //当前账户是否是主账户
                boolean isApprover = RamServiceConstant.YES.equals(commonParam.get(SysPageFunctionConstants.APPROVER));
                if (!isApprover) {
                    //子账户AI功能是否可使用判断
                    AiAbleUseRequest ableUseRequest = new AiAbleUseRequest();
                    ableUseRequest.setMerchantId(currentUser.getAdOrgId());
                    ableUseRequest.setUserId(currentUser.getAdUserId());
                    Boolean ableUse = ofNullable(aiProductClient.checkAbleUse(ableUseRequest)).map(BaseResponse::getData).orElse(false);
                    if (!ableUse) {
                        removeBanner(functionResult, AI_FUNCTION_BANNER);
                    }
                }
            } else {
                removeBanner(functionResult, AI_FUNCTION_BANNER);
            }
        }

        filterAuthCode(currentUser, functionResult, commonParam);
    }

    private void filterAuthCode(AdUserInfoDTO currentUser, PersonalFunctionResp functionResult, HashMap<String, String> commonParam) {
        if (CollectionUtils.isEmpty(functionResult.getAuthCode())) {
            return;
        }
        if (!functionResult.getAuthCode().contains(OLD_INCOME_AUTH_CODE)) {
            return;
        }
        String bwList = commonParam.get(SysPageFunctionConstants.BW_LIST);
        boolean white = bwList != null && (bwList.contains(oldIncomeWhiteList) || bwList.contains(newIncomeWhiteList));
        boolean forceNew = bwList != null && bwList.contains(forceNewIncomeWhiteList);
        if (!white || forceNew) {
            functionResult.getAuthCode().remove(OLD_INCOME_AUTH_CODE);
            return;
        }
        if (bwList.contains(oldIncomeWhiteList) && bwList.contains(newIncomeWhiteList)) {
            //新旧名单都存在,判断用户最后一次停留版本,默认旧版本
            String incomeVersion = ofNullable(incomeVersionClient.getVersion(currentUser.getAdOrgId(), currentUser.getAdUserId()))
                .map(RespBody::getBody).orElse(OLD_INCOME_VERSION);
            if (Objects.equals(incomeVersion, NEW_INCOME_VERSION)) {
                functionResult.getAuthCode().remove(OLD_INCOME_AUTH_CODE);
            }
            return;
        }
        //单一名单,新名单新版本,旧名单旧版本
        if (bwList.contains(newIncomeWhiteList)) {
            functionResult.getAuthCode().remove(OLD_INCOME_AUTH_CODE);
        }
    }

    @Override
    public void filterFunctionCenterResult(AdUserInfoDTO currentUser, FunctionCenterResp functionResult, HashMap<String, String> commonParam) {
        if (RamFunctionUtil.hasFunction(functionResult, FUNC_COMPLAINT_FEEDBACK)) {
            // 查申诉反馈的数量
            long count = getEquipmentProblemCount(currentUser);
            if (count > 0) {
                // 设置脚标数量
                RamFunctionUtil.setSubscriptContent(functionResult, FUNC_COMPLAINT_FEEDBACK, String.valueOf(count));
            }
        }
    }

    @Override
    public void filterPopupResult(AdUserInfoDTO currentUser, PersonalFunctionResp functionResult, HashMap<String, String> commonParam) {
        log.debug("filterPopupResult-->中台弹窗:{}", functionResult);
        if (RamFunctionUtil.hasPopup(functionResult, SysPageFunctionConstants.WEAK_PASSWORD_POPUP)) {
            if (log.isDebugEnabled()) {
                log.debug("用户弱密码弹框:{}, 用户信息:{}, 全局开关:{}", functionResult, currentUser, checkWeakPassword);
            }
            if (checkWeakPassword) {
                Long adUserId = currentUser.getAuthorityUserId();
                AdUserDTO adUserDTO = authorityService.getUserById(adUserId);
                if (adUserDTO == null || !weakPasswords.contains(adUserDTO.getPassword())) {
                    RamFunctionUtil.removePopup(functionResult, SysPageFunctionConstants.WEAK_PASSWORD_POPUP);
                }
            } else {
                RamFunctionUtil.removePopup(functionResult, SysPageFunctionConstants.WEAK_PASSWORD_POPUP);
            }
        }
        //saas免费体验提示弹窗
        saasExperiencePopupFilter(currentUser, functionResult);
        //已退订saas免费体验弹窗
        canceledSaasExperiencePopupFilter(currentUser, functionResult, commonParam);
    }

    private void saasExperiencePopupFilter(AdUserInfoDTO currentUser, PersonalFunctionResp functionResult) {
        if (RamFunctionUtil.hasPopup(functionResult, SysPageFunctionConstants.SAAS_EXPERIENCE_POPUP)) {
            ProductSubscribeQueryDTO query = new ProductSubscribeQueryDTO();
            query.setProductCode(ProductCode.SAAS.getCode());
            query.setMerchantId(currentUser.getAdOrgId());
            query.setAccountId(currentUser.getAdUserId());
            SubscribedProductDetailVO subscribedDetail =
                ofNullable(productSubscribeClient.detail(query)).map(BaseResponse::getData).orElse(null);
            if (log.isDebugEnabled()) {
                log.debug("saas免费体验提示弹窗:{}, 订阅详情:{}", functionResult, subscribedDetail);
            }
            if (Objects.isNull(subscribedDetail)) {
                RamFunctionUtil.removePopup(functionResult, SysPageFunctionConstants.SAAS_EXPERIENCE_POPUP);
                return;
            }
            if (Objects.equals(subscribedDetail.getPaidWhether(), true)
                || (Objects.nonNull(subscribedDetail.getSubscribeEndTime()) && subscribedDetail.getSubscribeEndTime().before(new Date()))
                || Objects.isNull(subscribedDetail.getExperienceEndTime()) || subscribedDetail.getExperienceEndTime().before(new Date())) {
                RamFunctionUtil.removePopup(functionResult, SysPageFunctionConstants.SAAS_EXPERIENCE_POPUP);
            }
        }
    }

    private void canceledSaasExperiencePopupFilter(AdUserInfoDTO currentUser, PersonalFunctionResp functionResult, HashMap<String, String> commonParam) {
        //已退订saas免费体验弹窗 8970白名单+主账号+加入白名单30天内+退订状态且不是30天内重新订阅又退订的
        if (RamFunctionUtil.hasPopup(functionResult, SysPageFunctionConstants.CANCELED_SAAS_EXPERIENCE_POPUP)) {
            LyyDistributorWhiteListVO white = RemoteResponseUtils.getData(
                merchantWhiteClient.getByTypeAndDistributorId(currentUser.getAdOrgId(), canceledSaasExperienceWhiteList));
            if (Objects.isNull(white)) {
                RamFunctionUtil.removePopup(functionResult, SysPageFunctionConstants.CANCELED_SAAS_EXPERIENCE_POPUP);
                return;
            }
            //当前账户是否是主账户
            boolean isApprover = RamServiceConstant.YES.equals(commonParam.get(SysPageFunctionConstants.APPROVER));
            if (!isApprover) {
                RamFunctionUtil.removePopup(functionResult, SysPageFunctionConstants.CANCELED_SAAS_EXPERIENCE_POPUP);
                return;
            }
            long betweenDay = DateUtil.betweenDay(white.getCreated(), new Date(), true);
            if (betweenDay > canceledSaasExperienceShowDays) {
                RamFunctionUtil.removePopup(functionResult, SysPageFunctionConstants.CANCELED_SAAS_EXPERIENCE_POPUP);
                return;
            }
            ProductSubscribeQueryDTO query = new ProductSubscribeQueryDTO();
            query.setProductCode(ProductCode.SAAS.getCode());
            query.setMerchantId(currentUser.getAdOrgId());
            query.setAccountId(currentUser.getAdUserId());
            SubscribedProductDetailVO subscribedDetail =
                ofNullable(productSubscribeClient.detail(query)).map(BaseResponse::getData).orElse(null);
            if (Objects.isNull(subscribedDetail)) {
                RamFunctionUtil.removePopup(functionResult, SysPageFunctionConstants.CANCELED_SAAS_EXPERIENCE_POPUP);
                return;
            }
            if (Objects.isNull(subscribedDetail.getSubscribeEndTime())
                || subscribedDetail.getSubscribeEndTime().after(white.getCreated())) {
                RamFunctionUtil.removePopup(functionResult, SysPageFunctionConstants.CANCELED_SAAS_EXPERIENCE_POPUP);
            }
        }
    }
}
