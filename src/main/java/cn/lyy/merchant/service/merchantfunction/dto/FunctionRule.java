package cn.lyy.merchant.service.merchantfunction.dto;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

/**
 * 商家功能模块规则
 * 每个属性之间是且的关系即例如配置了设备类型又配置了白名单，那么必须是这个白名单内的有配置的设备类型才让访问
 * 每个规则有优先级，最终匹配结果以优先级最高的规则为准
 * 如果没有匹配到规则，则默认禁止访问
 * *：表示任意值
 * !：表示取反，例如设备类型!娃娃机，即非娃娃机，其他品类都满足
 * &：表示且，即多个值之间取且，例如设备类型：&娃娃机,兑币机，即当商家既有娃娃机和兑币机的时候才能访问（默认每个值之间都是或的关系）
 * $：表示值必须一模一样，例如设备类型选择了娃娃机$,那么商家有且只能有娃娃机的商家才能访问    1,2,3,4
 */
@Data
public class FunctionRule {
    private Integer id;
    private String name;
    private String result;              //规则结果：allow允许，forbid禁止
    private int priority;               //规则优先级：值越大越高
    private String role;                //角色
    private String resource;            //资源权限
    private Integer accountType;        //账户类型：主账户，子账户
    private String equipmentType;       //设备类型
    private String equipments;          //设备
    private String merchants;           //商家
    private String bwList;              //黑白名单
    private JSONObject extend;          //规则扩展属性，例如只允许18岁以上的访问等

    private String code;                //规则作用的功能对象
    private Integer systemId;
    private String superScript;         //角标

}
