package cn.lyy.merchant.service.merchantfunction.handler;

import cn.lyy.merchant.service.merchantfunction.dto.FunctionRule;
import cn.lyy.merchant.service.merchantfunction.dto.MerchantDto;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class BlackWhiteRuleMatchHandler extends DefalutRuleMatchHandler implements RuleMatchHandler{

    @Override
    public boolean hander(MerchantDto merchant, FunctionRule rule) {
        if(rule != null){
            log.info("商家：{}，规则：{}，匹配结果：{}",merchant.getId(),rule.getBwList(),check(merchant.getBlackWhiteLists(), rule.getBwList()));
        }
        return check(merchant.getBlackWhiteLists(), rule.getBwList());
    }
}
