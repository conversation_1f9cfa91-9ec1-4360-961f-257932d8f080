package cn.lyy.merchant.service.merchantfunction.handler.page;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.lyy_cmember_service_api.WashRechargeActivityClient;
import cn.lyy.lyy_cmember_service_api.dto.washrecharge.WashRechargeActivityMerchantDTO;
import cn.lyy.marketing.api.service.ActivityOperationClient;
import cn.lyy.marketing.dto.constants.ActivityStatusEnum;
import cn.lyy.marketing.dto.constants.ActivityTypeEnum;
import cn.lyy.marketing.dto.promotion.ActivityDTO;
import cn.lyy.marketing.dto.promotion.request.ActivityPageRequestDTO;
import cn.lyy.merchant.constants.SysMarketingIconConstants;
import cn.lyy.merchant.constants.SysPageFunctionConstants;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import cn.lyy.merchant.service.business.wash.WashActivityJudgeService;
import com.github.pagehelper.PageInfo;
import com.lyy.ram.service.infrastructure.constant.RamServiceConstant;
import com.lyy.ram.service.infrastructure.util.RamFunctionUtil;
import com.lyy.ram.service.interfaces.dto.function.response.MarketingFunctionResp;
import com.lyy.ram.service.interfaces.dto.function.response.PersonalFunctionResp;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;

import static java.util.Optional.ofNullable;

/**
 * 页面功能规则配置 洗水业务数据处理
 */
@Component
@Slf4j
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class WashingPageFunctionHandler extends AbstractPageFunctionHandler implements BizPageFunctionHandler {

    private final ActivityOperationClient activityOperationClient;
    private final WashRechargeActivityClient washRechargeActivityClient;
    private final WashActivityJudgeService washActivityPageFunctionJudgeService;

    @Override
    public void handlePopupParam(AdUserInfoDTO currentUser, HashMap<String, String> functionParam) {
        // 洗水预充
        long start = System.currentTimeMillis();
        String washRechargePopup = RamServiceConstant.NO;
        ActivityPageRequestDTO requestDTO = new ActivityPageRequestDTO();
        requestDTO.setPageSize(1);
        requestDTO.setPageIndex(1);
        requestDTO.setActivityType(Collections.singletonList(ActivityTypeEnum.WASH_RECHARGE));
        requestDTO.setLyyDistributorId(0L);
        requestDTO.setStatus(ActivityStatusEnum.EFFECTIVE);
        PageInfo<ActivityDTO> pageInfo = ofNullable(activityOperationClient.listActivity(requestDTO)).map(BaseResponse::getData).orElse(null);
        if (pageInfo != null) {
            if (CollectionUtils.isNotEmpty(pageInfo.getList())) {
                // 平台活动只会有一个生效,拿第一个
                ActivityDTO washChargeActivity = pageInfo.getList().get(0);
                WashRechargeActivityMerchantDTO activityMerchantDTO = ofNullable(washRechargeActivityClient.isActivityMerchant(washChargeActivity.getPromotionActivityId(), currentUser.getAdOrgId()))
                        .map(cn.lyy.lyy_cmember_service_api.response.BaseResponse::getData).orElse(null);
                if (activityMerchantDTO == null) {
                    washRechargePopup = RamServiceConstant.YES;
                }
            }
        }
        functionParam.put(SysPageFunctionConstants.WASH_RECHARGE_POPUP, washRechargePopup);
        if (log.isDebugEnabled()) {
            log.debug("handlePopupParam===>>>洗水弹窗===>>>cost:{}", System.currentTimeMillis() - start);
        }
    }

    //B端默认返回全部展示,这里过滤掉不符合的弹窗
    @Override
    public void filterPopupResult(AdUserInfoDTO currentUser, PersonalFunctionResp functionResult, HashMap<String, String> commonParam) {

        if (RamFunctionUtil.hasPopup(functionResult, SysPageFunctionConstants.WASH_CAMPUS_CARD_POPUP)) {
            Boolean result = washActivityPageFunctionJudgeService.campusCardPopUp(currentUser);
            log.debug("校园卡filterPopupResult:{}",result);
            if (!Objects.equals(Boolean.TRUE,result)) {
                RamFunctionUtil.removePopup(functionResult, SysPageFunctionConstants.WASH_CAMPUS_CARD_POPUP);
            }
        }
    }


    //B端默认返回全部展示, 这里过滤掉不符合展示在营销中心的图标
    @Override
    public void filterMarketingResult(AdUserInfoDTO currentUser, MarketingFunctionResp marketingResult,
            HashMap<String, String> commonParam) {
        if (RamFunctionUtil.hasMarketing(marketingResult, SysMarketingIconConstants.WASH_CAMPUS_CARD_ICON)) {
            Boolean result = washActivityPageFunctionJudgeService.campusCardMarketingShow(currentUser);
            log.debug("校园卡filterMarketingResult:{}",result);
            if (!Objects.equals(Boolean.TRUE,result)) {
                RamFunctionUtil.removeMarketing(marketingResult,  SysMarketingIconConstants.WASH_CAMPUS_CARD_ICON);
            }
        }
    }
}
