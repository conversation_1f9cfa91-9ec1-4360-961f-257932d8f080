package cn.lyy.merchant.service.merchantfunction.handler;

import cn.lyy.merchant.service.merchantfunction.dto.FunctionRule;
import cn.lyy.merchant.service.merchantfunction.dto.MerchantDto;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class EquipmentTypeRuleMatchHandler extends DefalutRuleMatchHandler implements RuleMatchHandler{

    @Override
    public boolean hander(MerchantDto merchant, FunctionRule rule) {
        //log.info("商家：{},设备类型：{}，匹配结果：{}",merchant.getId(), JSONObject.toJSON(merchant.getEquipmentTypes()).toString(),check(merchant.getBlackWhiteLists(), rule.getBwList()));
        return check(merchant.getEquipmentTypes(), rule.getEquipmentType());
    }
}
