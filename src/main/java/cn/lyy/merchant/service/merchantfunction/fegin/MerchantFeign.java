package cn.lyy.merchant.service.merchantfunction.fegin;

import cn.lyy.authority_service_api.AdResourcesDTO;
import com.alibaba.fastjson.JSONObject;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "merchant-service")
public interface MerchantFeign {

    @GetMapping("/rest/merchant/equipment/getMerchantEquipmentBusinessType")
    public JSONObject getMerchantEquipmentBusinessType(@RequestParam(value="merchantId") long merchantId);

}
