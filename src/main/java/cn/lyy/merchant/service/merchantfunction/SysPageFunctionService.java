package cn.lyy.merchant.service.merchantfunction;

import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import com.lyy.ram.service.interfaces.dto.function.request.MarketingFunctionQueryReq;
import com.lyy.ram.service.interfaces.dto.function.request.PersonalFunctionQueryReq;
import com.lyy.ram.service.interfaces.dto.function.response.FunctionCenterResp;
import com.lyy.ram.service.interfaces.dto.function.response.MarketingFunctionResp;
import com.lyy.ram.service.interfaces.dto.function.response.PersonalFunctionResp;
import com.lyy.starter.common.resp.RespBody;

/**
 * 页面功能 service
 *
 * <AUTHOR>
 * @since 2024-03-05
 */
public interface SysPageFunctionService {

    RespBody<PersonalFunctionResp> getPersonalFunction(AdUserInfoDTO currentUser, PersonalFunctionQueryReq req, Boolean isWalletSettlement);

    RespBody<FunctionCenterResp> getFunctionCenter(AdUserInfoDTO currentUser, PersonalFunctionQueryReq req, boolean equals);

    RespBody<PersonalFunctionResp> getPopupFunction(AdUserInfoDTO currentUser, PersonalFunctionQueryReq req);

    RespBody<MarketingFunctionResp> getMarketingFunction(AdUserInfoDTO currentUser, MarketingFunctionQueryReq req, boolean equals);

    RespBody<MarketingFunctionResp> getMarketingCenter(AdUserInfoDTO currentUser, MarketingFunctionQueryReq req, boolean equals);


}
