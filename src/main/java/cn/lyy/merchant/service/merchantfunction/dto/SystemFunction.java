package cn.lyy.merchant.service.merchantfunction.dto;

import lombok.Data;

@Data
public class SystemFunction {

    private Long id;
    private String name;
    private String code;
    private String location;
    private Integer systemId;
    private String functionGroup;
    private String functionGroupName;
    private String context;
    private String icon;
    private String superScript;
    private String showName;
    private String url;
    private Long parentId;
    private Integer orderIndex;
    private Integer groupOrderIndex;
    private Integer type;//菜单类型:1:功能菜单，2:非功能菜单
}
