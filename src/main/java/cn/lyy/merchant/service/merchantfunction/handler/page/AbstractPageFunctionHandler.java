package cn.lyy.merchant.service.merchantfunction.handler.page;

import cn.hutool.core.collection.CollUtil;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import com.lyy.ram.service.interfaces.dto.function.response.FunctionCenterItemResp;
import com.lyy.ram.service.interfaces.dto.function.response.FunctionCenterResp;
import com.lyy.ram.service.interfaces.dto.function.response.MarketingFunctionResp;
import com.lyy.ram.service.interfaces.dto.function.response.PersonalFunctionResp;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;

/**
 * 页面功能规则配置
 */
@Slf4j
public abstract class AbstractPageFunctionHandler implements BizPageFunctionHandler {

    /**
     * 打印调试日志
     */
    protected void debugLog(String msg, Object... var) {
        if (log.isDebugEnabled()) {
            log.debug(msg, var);
        }
    }

    /**
     * 判断是否存在指定value的banner
     * @param resp
     * @param bannerValue
     * @return
     */
    protected boolean hasBanner(PersonalFunctionResp resp, String bannerValue) {
        if (CollUtil.isNotEmpty(resp.getBannerList())) {
            for (FunctionCenterItemResp item : resp.getBannerList()) {
                if (item.getValue().equals(bannerValue)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 移除指定value的banner
     * @param resp
     * @param bannerValue
     * @return
     */
    protected boolean removeBanner(PersonalFunctionResp resp, String bannerValue) {
        boolean result = false;
        if (CollUtil.isNotEmpty(resp.getBannerList())) {
            for (FunctionCenterItemResp item : resp.getBannerList()) {
                if (item.getValue().equals(bannerValue)) {
                    resp.getBannerList().remove(item);
                    result = true;
                    break;
                }
            }
        }
        return result;
    }

    @Override
    public void handleFunctionParam(AdUserInfoDTO currentUser, HashMap<String, String> functionParam) {
    }

    @Override
    public void handlePopupParam(AdUserInfoDTO currentUser, HashMap<String, String> functionParam) {
    }

    @Override
    public void handleMarketingParam(AdUserInfoDTO currentUser, HashMap<String, String> functionParam) {
    }

    @Override
    public void filterFunctionResult(AdUserInfoDTO currentUser, PersonalFunctionResp functionResult, HashMap<String, String> commonParam) {
    }

    @Override
    public void filterFunctionCenterResult(AdUserInfoDTO currentUser, FunctionCenterResp functionResult, HashMap<String, String> commonParam) {
    }

    @Override
    public void filterPopupResult(AdUserInfoDTO currentUser, PersonalFunctionResp functionResult, HashMap<String, String> commonParam) {
    }

    @Override
    public void filterMarketingResult(AdUserInfoDTO currentUser, MarketingFunctionResp marketingResult, HashMap<String, String> commonParam) {
    }

}

