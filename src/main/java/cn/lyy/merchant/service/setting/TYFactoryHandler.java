package cn.lyy.merchant.service.setting;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.equipment.dto.SettingEquipmentInfo;
import cn.lyy.equipment.dto.equipment.EquipmentDTO;
import cn.lyy.equipment.dto.machine.Setting;
import cn.lyy.equipment.service.SettingsService;
import cn.lyy.merchant.util.MachineUtil;
import cn.lyy.tools.util.GsonUtils;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * @description:
 * @author: qgw
 * @date on 2020/11/11.
 * @Version: 1.0
 */
@Slf4j
@Service("tyFactoryHandler")
public class TYFactoryHandler implements FactoryHandler {


    @Autowired
    private SettingsService settingsService;

    @Override
    public BaseResponse handle(EquipmentDTO equipment, String settingInfo, Map<String, Object> paraMap, String type, Long adUserId) {
        BaseResponse baseResponse = new BaseResponse();
        Setting settings = new Gson().fromJson(settingInfo, new TypeToken<Setting>() {
        }.getType());
        MachineUtil.decode(settings, paraMap);
        int functionCode = settings.getMachineType().equals("兑币机") ? 21 : 6;
        SettingEquipmentInfo param = new SettingEquipmentInfo();
        param.setData(MachineUtil.getBytes(settings));
        param.setUniqueCode(equipment.getUniqueCode());
        param.setFunctionCode(String.valueOf(functionCode));
        param.setSendType(EQUIP_SETTING);
        boolean result = settingsService.issue(param).getData();
        log.debug("参数设置 -> {}", GsonUtils.toJson(param));
        if (!result) {
            baseResponse.setCode(ResponseCodeEnum.FAIL.getCode());
            baseResponse.setMessage("设置参数失败，请确保设备网络畅通");
            return baseResponse;
        }
        // 设置成功，将配置同步到设备所在投放地址的兑币机优惠设置
        if ("兑币机".equals(settings.getMachineType()) && settings.getParams().size() >= 6) {
            //TODO: 2020/11/12   2.0与1.0合并，暂时不处理  必须处理的
            //MLyyDiscountRule.updateDbjRule(equipment.getEquipmentGroupId(),1, settings.getParams().get(0).getComponentValue(), adUserId);
            //MLyyDiscountRule.updateDbjRule(equipment.getEquipmentGroupId(), 5, settings.getParams().get(1).getComponentValue(), adUserId);
            //MLyyDiscountRule.updateDbjRule(equipment.getEquipmentGroupId(), 10, settings.getParams().get(2).getComponentValue(), adUserId);
            //MLyyDiscountRule.updateDbjRule(equipment.getEquipmentGroupId(), 20, settings.getParams().get(3).getComponentValue(), adUserId);
            //MLyyDiscountRule.updateDbjRule(equipment.getEquipmentGroupId(), 50, settings.getParams().get(4).getComponentValue(), adUserId);
            //MLyyDiscountRule.updateDbjRule(equipment.getEquipmentGroupId(), 100, settings.getParams().get(5).getComponentValue(), adUserId);
        }

        return baseResponse;
    }
}
