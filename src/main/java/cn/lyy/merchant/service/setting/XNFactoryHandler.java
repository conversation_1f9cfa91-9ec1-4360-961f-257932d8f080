package cn.lyy.merchant.service.setting;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.equipment.dto.SettingEquipmentInfo;
import cn.lyy.equipment.dto.equipment.EquipmentDTO;
import cn.lyy.equipment.service.SettingsService;
import cn.lyy.merchant.dto.setting.XnSettingInfo;
import cn.lyy.merchant.util.YTQUtils;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * @description:
 * @author: qgw
 * @date on 2020/11/11.
 * @Version: 1.0
 */
@Slf4j
@Service("xnFactoryHandler")
public class XNFactoryHandler implements FactoryHandler {
    @Autowired
    private SettingsService settingsService;

    @Override
    public BaseResponse handle(EquipmentDTO equipment, String settingInfo, Map<String, Object> paraMap, String type, Long adUserId) {
        BaseResponse baseResponse = new BaseResponse();
        XnSettingInfo settings = new Gson().fromJson(settingInfo, new TypeToken<XnSettingInfo>() {
        }.getType());
        settings.gameModel = YTQUtils.setOptions(settings.gameModel, paraMap.get("gameModel") == null ? "" : paraMap.get("gameModel").toString(), 0,
                4);
        settings.throwcoins = YTQUtils.setOptions(settings.throwcoins,
                paraMap.get("throwcoins") == null ? "" : paraMap.get("throwcoins").toString(), 0, 100);
        settings.gameAmount = YTQUtils.setOptions(settings.gameAmount,
                paraMap.get("gameAmount") == null ? "" : paraMap.get("gameAmount").toString(), 0, 100);
        settings.gameTime = YTQUtils.setOptions(settings.gameTime, paraMap.get("gameTime") == null ? "" : paraMap.get("gameTime").toString(), 5,
                60);
        settings.winCount = YTQUtils.setOptions(settings.winCount, paraMap.get("winCount") == null ? "" : paraMap.get("winCount").toString(), 1,
                250);
        settings.giveGameAcount = YTQUtils.setOptions(settings.giveGameAcount,
                paraMap.get("giveGameAcount") == null ? "" : paraMap.get("giveGameAcount").toString(), 0, 60);
        settings.strongVoltage = YTQUtils.setFloatOptions(settings.strongVoltage,
                paraMap.get("strongVoltage") == null ? "" : paraMap.get("strongVoltage").toString(), 15.0f, 45.7f);
        settings.strongVoltageTime = YTQUtils.setFloatOptions(settings.strongVoltageTime,
                paraMap.get("strongVoltageTime") == null ? "" : paraMap.get("strongVoltageTime").toString(), 0.1f, 3.0f);
        settings.weakVoltage = YTQUtils.setFloatOptions(settings.weakVoltage,
                paraMap.get("weakVoltage") == null ? "" : paraMap.get("weakVoltage").toString(), 4.5f, 20.0f);
        settings.toTheWeak = YTQUtils.setOptions(settings.toTheWeak, paraMap.get("toTheWeak") == null ? "" : paraMap.get("toTheWeak").toString(), 0,
                1);
        settings.telekinesis = YTQUtils.setOptions(settings.telekinesis,
                paraMap.get("telekinesis") == null ? "" : paraMap.get("telekinesis").toString(), 0, 1);
        settings.reserveGameAcountSwitch = YTQUtils.setOptions(settings.reserveGameAcountSwitch,
                paraMap.get("reserveGameAcountSwitch") == null ? "" : paraMap.get("reserveGameAcountSwitch").toString(), 0, 1);
        settings.standbyMusic = YTQUtils.setOptions(settings.standbyMusic,
                paraMap.get("standbyMusic") == null ? "" : paraMap.get("standbyMusic").toString(), 0, 4);
        SettingEquipmentInfo param = new SettingEquipmentInfo();
        param.setData(settings.getBytes());
        param.setUniqueCode(equipment.getUniqueCode());
        param.setFunctionCode("6");
        param.setSendType(EQUIP_SETTING);
        boolean result = settingsService.issue(param).getData();
        if (!result) {
            baseResponse.setCode(ResponseCodeEnum.FAIL.getCode());
            baseResponse.setMessage("设置参数失败，请确保设备网络畅通");
            return baseResponse;
        }
        return baseResponse;
    }
}

