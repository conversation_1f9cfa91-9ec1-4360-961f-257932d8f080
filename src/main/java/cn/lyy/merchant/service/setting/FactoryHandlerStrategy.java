package cn.lyy.merchant.service.setting;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

import static java.util.Optional.ofNullable;

@Service
public class FactoryHandlerStrategy {

    private final Map<String, FactoryHandler> handlerMap = new HashMap<>();

    @Autowired
    public FactoryHandlerStrategy(Map<String, FactoryHandler> handlerMap) {
        this.handlerMap.clear();
        handlerMap.forEach(this.handlerMap::put);
    }

    public FactoryHandler getHandler(String type) {
        return ofNullable(handlerMap.get(type)).orElse(null);
    }

}