package cn.lyy.merchant.service.setting;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.equipment.dto.SettingEquipmentInfo;
import cn.lyy.equipment.dto.equipment.EquipmentDTO;
import cn.lyy.equipment.service.SettingsService;
import cn.lyy.merchant.dto.setting.SwDbjSettingInfo;
import cn.lyy.merchant.util.YTQUtils;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * @description:
 * @author: qgw
 * @date on 2020/11/11.
 * @Version: 1.0
 */
@Slf4j
@Service("glFactoryHandler")
public class GLFactoryHandler implements FactoryHandler {
    @Autowired
    private SettingsService settingsService;

    @Override
    public BaseResponse handle(EquipmentDTO equipment, String settingInfo, Map<String, Object> paraMap, String type, Long adUserId) {
        BaseResponse baseResponse = new BaseResponse();
        SwDbjSettingInfo settings = new Gson().fromJson(settingInfo, new TypeToken<SwDbjSettingInfo>() {
        }.getType());
        String oneCoins = paraMap.get("oneCoins") == null ? "" : paraMap.get("oneCoins").toString();
        String fiveCoins = paraMap.get("fiveCoins") == null ? "" : paraMap.get("fiveCoins").toString();
        String tenCoins = paraMap.get("tenCoins") == null ? "" : paraMap.get("tenCoins").toString();
        String twentyCoins = paraMap.get("twentyCoins") == null ? "" : paraMap.get("twentyCoins").toString();
        String fiftyCoins = paraMap.get("fiftyCoins") == null ? "" : paraMap.get("fiftyCoins").toString();
        String hundredCoins = paraMap.get("hundredCoins") == null ? "" : paraMap.get("hundredCoins").toString();
        settings.oneCoins = YTQUtils.setOptions(settings.oneCoins, oneCoins, 0, 999);
        settings.fiveCoins = YTQUtils.setOptions(settings.fiveCoins, fiveCoins, 0, 999);
        settings.tenCoins = YTQUtils.setOptions(settings.tenCoins, tenCoins, 0, 999);
        settings.twentyCoins = YTQUtils.setOptions(settings.twentyCoins, twentyCoins, 0, 999);
        settings.fiftyCoins = YTQUtils.setOptions(settings.fiftyCoins, fiftyCoins, 0, 999);
        settings.hundredCoins = YTQUtils.setOptions(settings.hundredCoins, hundredCoins, 0, 999);
        SettingEquipmentInfo param = new SettingEquipmentInfo();
        param.setData(settings.getBytes(38));
        param.setUniqueCode(equipment.getUniqueCode());
        param.setFunctionCode("21");
        param.setSendType(EQUIP_SETTING);
        boolean result = settingsService.issue(param).getData();
        if (!result) {
            baseResponse.setCode(ResponseCodeEnum.FAIL.getCode());
            baseResponse.setMessage("设置参数失败，请确保设备网络畅通");
            return baseResponse;
        }
        // 设置成功，将配置同步到设备所在投放地址的兑币机优惠设置
        if (equipment != null && equipment.getEquipmentGroupId() > 0) {
            //TODO: 2020/11/12   2.0与1.0合并，暂时不处理  必须处理的
            //MLyyDiscountRule.updateDbjRule(equipment.getEquipmentGroupId(), 1, oneCoins, adUserId);
            //MLyyDiscountRule.updateDbjRule(equipment.getEquipmentGroupId(), 5, fiveCoins, adUserId);
            //MLyyDiscountRule.updateDbjRule(equipment.getEquipmentGroupId(), 10, tenCoins, adUserId);
            //MLyyDiscountRule.updateDbjRule(equipment.getEquipmentGroupId(), 20, twentyCoins, adUserId);
            //MLyyDiscountRule.updateDbjRule(equipment.getEquipmentGroupId(), 50, fiftyCoins, adUserId);
            //MLyyDiscountRule.updateDbjRule(equipment.getEquipmentGroupId(), 100, hundredCoins, adUserId);
        }

        return baseResponse;
    }
}
