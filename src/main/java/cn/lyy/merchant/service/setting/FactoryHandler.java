package cn.lyy.merchant.service.setting;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.equipment.dto.equipment.EquipmentDTO;

import java.util.Map;

/**
 * @description:
 * @author: qgw
 * @date on 2020/11/11.
 * @Version: 1.0
 */
public interface FactoryHandler {

    String EQUIP_SETTING = "setting";
    String FACTORY_HANDLER_SUFFIX = "FactoryHandler";

    BaseResponse handle(EquipmentDTO equipment, String settingInfo, Map<String, Object> paraMap, String type, Long adUserId);
}
