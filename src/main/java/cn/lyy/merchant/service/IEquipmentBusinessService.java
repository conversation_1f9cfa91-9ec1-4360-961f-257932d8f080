package cn.lyy.merchant.service;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.equipment.dto.equipment.EquipmentDTO;
import cn.lyy.equipment.dto.equipment.EquipmentStatusDTO;
import cn.lyy.equipment.dto.equipment.EquipmentUpdateDTO;
import cn.lyy.equipment.dto.equipment.PositionDTO;
import cn.lyy.merchant.dto.RemoteStartParamDTO;
import cn.lyy.merchant.dto.RemoteStopParamDTO;
import cn.lyy.merchant.dto.WashFaultStatusDTO;
import cn.lyy.merchant.dto.equipment.*;
import cn.lyy.merchant.dto.request.MerchantEquipmentRequest;
import cn.lyy.merchant.dto.response.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> {<EMAIL>}
 * @date 2020/11/2 22:59
 **/
public interface IEquipmentBusinessService {

    Boolean getWasherOperationAsyncEnable();

    /**
     * 根据uniqueCode获取设备信息
     * @param uniqueCode
     * @return
     */
    EquipmentDTO getByUniqueCode(String uniqueCode);
    /**
     * 根据value获取设备信息
     * @param value
     * @return
     */
    EquipmentDTO getByValue(String value);

    /**
     * 查询设备列表
     * @param request
     * @return
     */
    MerchantEquipmentDTO selectEquipment(MerchantEquipmentRequest request);

    /**
     * 查询设备列表(超过400台设备)
     * @param request
     * @return
     */
    MerchantEquipmentDTO equipmentCount(MerchantEquipmentRequest request);
    /**
     * 查询设备列表
     * @param query
     * @param checkOnline
     * @return
     */
    List<EquipmentListDTO> queryByTypeAndGroup(EquipmentQueryDTO query, Boolean checkOnline);

    /**
     * 远程启动
     * @param param
     */
    void remoteStart(RemoteStartParamDTO param);

    /**
     * 更新设备机台号和备注
     * @param equipmentUpdateDTO
     * @param adOrgId
     * @param adUserId
     * @return
     */
    BaseResponse updateEquipmentInfo(EquipmentUpdateDTO equipmentUpdateDTO, Long adOrgId, Long adUserId);

    /**
     * 获取设备详情信息
     * @param equipmentId
     * @return
     */
    EquipmentResponseDTO getEquipmentDetailById(Long equipmentId);

    /**
     * 获取设备仓位信息
     * @param equipmentId
     */
    List<PositionDTO> getPositionInfoByEquipmentId(Long equipmentId);

    /**
     * 刷新设备信号
     * @param uniqueCode
     * @return
     */
    Integer getEquipmentSignal(String uniqueCode);

    /**
     * 更新故障状态
     * @param faultStatusDTO
     * @param token
     * @return
     */
    Integer updateFaultStatus(WashFaultStatusDTO faultStatusDTO, String token);

    /**
     * 设备解绑
     * @param dto
     */
    boolean unbind(EquipmentUpdateRequestDTO dto);

    /**
     * 根据设备类型id获取设备类型的扩展功能列表
     * @param equipmentTypeId
     * @return
     */
    List<EquipmentExtendFunctionResponseDTO> extendFunctionList(Long equipmentTypeId);

    Boolean onlineStatus(String equipmentValue);

    EquipmentProfileDTO equipmentProfile(String equipmentValue, Long currentAdOrgId);

    /**
     * 查询设备支持的功能
     * @param equipmentValue
     * @return
     */
    EquipmentFunctionDTO supportFunction(String equipmentValue);

    Boolean remoteStop(RemoteStopParamDTO param, String webSocketToken);

    /**
     * 查询故障洗衣机设备列表
     * @param query
     * @return
     */
    List<EquipmentStatusDTO> listErrorWasher(EquipmentQueryDTO query);

    /**
     * 查询设备列表
     * @param request
     * @return
     */
    MerchantEquipmentDTO selectGroupEquipmentList(MerchantEquipmentRequest request);

    /**
     * 查询场地设备信息，只显示场地及场地下面的信息，内容与list接口类似，但是没有其他详细的设备信息，
     * 主要用于某些选择场地的请求
     * @param request
     * @return
     */
    MerchantEquipmentDTO findGroupEquipment(MerchantEquipmentRequest request);

    /**
     * 查询在线离线数
     * @param merchantId 商户id
     * @param userId 用户id
     * @return 返回数据
     */
    Map<String, Integer> onlineAndOfflineNumber(Long merchantId, Long userId);
}
