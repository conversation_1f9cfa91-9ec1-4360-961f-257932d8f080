package cn.lyy.merchant.schedule;

import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import cn.lyy.base.utils.converter.CommonConverterTools;
import cn.lyy.merchant.api.service.AdOrgClient;
import cn.lyy.merchant.api.service.MerchantShopClient;
import cn.lyy.merchant.api.service.MerchantWhiteClient;
import cn.lyy.merchant.constants.OrderBusinessType;
import cn.lyy.merchant.constants.WhiteDistributorEnum;
import cn.lyy.merchant.dao.odps.MerchantOrderMapper;
import cn.lyy.merchant.dto.MerchantOrderDTO;
import cn.lyy.merchant.dto.MerchantPayOrderStatisticsDTO;
import cn.lyy.merchant.dto.RefundDetailDTO;
import cn.lyy.merchant.dto.merchant.AdOrgDTO;
import cn.lyy.merchant.dto.merchant.LyyDistributorWhiteListVO;
import cn.lyy.merchant.dto.merchant.SaasStatisticsExportDTO;
import cn.lyy.merchant.util.DateUtils;
import cn.lyy.merchant.util.DateUtils.DatePattern;
import com.alibaba.fastjson.JSON;
import com.lyy.oss.service.impl.AliyunOss;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import java.io.File;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 *  上线数据统计
 * <AUTHOR>
 */
@JobHandler(value = "statisticsPayOrderJobHandlerV2")
@Component
@Slf4j
public class StatisticsPayOrderSchedule extends IJobHandler {

    @Autowired
    private MerchantOrderMapper merchantOrderMapper;


    @Autowired
    private MerchantShopClient merchantShopClient;

    @Autowired
    private AdOrgClient adOrgClient;

    @Autowired
    private MerchantWhiteClient merchantWhiteClient;

    @Value("${cdz.global.merchantId}")
    private Long globalMerchantId;

   @Override
    public ReturnT<String> execute(String param) {
       try {
           //param 例如，2021-11,2021-12 只生成这两个月，如果已有记录会是删除原有记录，默认查上月不会删除
           Long curTime = System.currentTimeMillis();
           log.info("{}入参：{}" , curTime,param);

           List<String> execMonths = new ArrayList<>();
           boolean deleteBefore = false;
           if(StringUtils.isNotBlank(param)) {
               //查多个月
               String[] items = param.split(",");
               for (int i = 0; i < items.length; i++) {
                   execMonths.add(items[0]+"-01");
               }
               deleteBefore = true;
           }else{
               //没有传参，默认查上一个月
               String yearMonthStr = DateUtils.dateFormat(DateUtils.now().plusMonths(-1), DateUtils.DatePattern.yyyy_MM);
               execMonths.add(yearMonthStr+"-01");
           }

           List<AdOrgDTO> allMerchants = null;
           //globalMerchantId =  1033636L;
           if (globalMerchantId!=null) {
               AdOrgDTO adOrgDTO = new AdOrgDTO();
               adOrgDTO.setAdOrgId(globalMerchantId);
               allMerchants = new ArrayList<>();
               allMerchants.add(adOrgDTO);
           }else {
               //查询所有符合条件的商家
               allMerchants = adOrgClient.getLyy2MerchantId().getData();
               List<LyyDistributorWhiteListVO> listVOS = merchantWhiteClient.getWhiteDistributorByType(WhiteDistributorEnum.GRAY_MERCHANT.getType()).getData();
               if (CollectionUtils.isEmpty(listVOS)) {
                   log.info("查询到的白名单商家为空");
                   return SUCCESS;
               }
               allMerchants = listVOS.stream().map(a -> {
                   AdOrgDTO adOrgDTO = new AdOrgDTO();
                   adOrgDTO.setAdOrgId(a.getLyyDistributorId().longValue());
                   return adOrgDTO;
               }).collect(Collectors.toList());
           }
           if (CollectionUtils.isEmpty(allMerchants)) {
               log.info("{}转换后的商家为空",curTime);
               return SUCCESS;
           }

           //每次查100个，循环查
           BigDecimal pageSize = new BigDecimal(100);
           //商家的数量
           BigDecimal allMerchantIdSize = new BigDecimal(allMerchants.size());
           //循环多少页，向上取整（商家数量除以每页数量）
           int page = allMerchantIdSize.divide(pageSize).setScale( 0, BigDecimal.ROUND_UP).intValue();
           log.info("{}准备导出月份: {} , 查到{}个3.0商户，分{}页处理，每页{}个商户",curTime, execMonths , allMerchants.size() , page , pageSize);

           //查询每一页，默认100个
           for (int currentPage = 0; currentPage < page; currentPage++) {
               int pageIndex = currentPage * pageSize.intValue();
               //结束的商家index
               int endIndex = pageIndex + pageSize.intValue();
               if (endIndex > allMerchants.size() - 1) {
                   endIndex = allMerchants.size();
               }

               List<AdOrgDTO> adOrgs = allMerchants.subList(pageIndex, endIndex);
               List<Integer> merchantIdsInt = adOrgs.stream().map(item -> item.getAdOrgId().intValue()).collect(Collectors.toList());
               log.info("{}开始第{}页，商家id为：{}" ,curTime, currentPage,merchantIdsInt);

               //把每一页的每一个月的数据放到一个map中
               Map<String, List<MerchantPayOrderStatisticsDTO>> exportExcelRecords = new HashMap<>();
               //记录excel文件的map
               Map<String, File> exportExcelFileRecords = new HashMap<>();
               //记录excel的url的map
               Map<String, String> exportExcelUrlRecords = new HashMap<>();


               //1.分开查每一个月份,并分开放到map中
               for (String month:execMonths) {
                   LocalDate localDate = DateUtils.localDateParse(month, DatePattern.yyyy_MM_dd);
                   //第一天
                   String firstDay = DateUtils.dateFormat(DateUtils.firstDayOfMonth(localDate), DateUtils.DatePattern.yyyyMMdd);
                   //最后一天
                   String lastDay = DateUtils.dateFormat(DateUtils.lastDayOfMonth(localDate), DateUtils.DatePattern.yyyyMMdd);

                   log.info("{}商家id：{}，开始时间：{}，结束时间：{}",curTime,merchantIdsInt,firstDay,lastDay);
                   //todo 查询数仓的订单导出数据
                   List<MerchantPayOrderStatisticsDTO> allMerchantPayOrders = merchantOrderMapper.getExportOrderList(merchantIdsInt,firstDay,lastDay);
                   //List<MerchantPayOrderStatisticsDTO> allMerchantPayOrders = new ArrayList<>();
                   //MerchantPayOrderStatisticsDTO vo = new MerchantPayOrderStatisticsDTO();
                   //vo.setActuaAmount(new BigDecimal("22"));
                   //vo.setEquipmentValue("9233455");
                   //vo.setDistributorId("1193587");
                   //allMerchantPayOrders.add(vo);
                   if(CollectionUtils.isEmpty(allMerchantPayOrders)) {
                       log.info("{}查询结果没有数据",curTime);
                       continue;
                   }

                   //从数仓查出来的数据，把每一个月的数据先放到一个map中
                   allMerchantPayOrders.forEach(order -> {
                       //order.setActuaAmount(CommonUtil.centToYuan(order.getActuaAmount());
                       List<MerchantPayOrderStatisticsDTO> merchantPayOrderStatisticsVos = exportExcelRecords
                           .computeIfAbsent(order.getDistributorId() + "_" + month, k -> new ArrayList<>());
                       merchantPayOrderStatisticsVos.add(order);
                   });

                   //日志记录信息
                   adOrgs.forEach(adOrg -> {
                       List<MerchantPayOrderStatisticsDTO> merchantPayOrderStatisticsVos = exportExcelRecords.get(adOrg.getAdOrgId() + "_"
                               + month);
                       int size = merchantPayOrderStatisticsVos == null ? 0 : merchantPayOrderStatisticsVos.size();
                       if(size > 0) {
                           log.info("{}根据商家id和月份，[{}]找到订单数{}条",curTime, adOrg.getAdOrgId() + "_" + month, size);
                       }
                   });
               }

               //2.删除原有export记录
               if (deleteBefore) {
                   execMonths.forEach(month -> {
                       adOrgs.stream().filter(adOrg -> exportExcelRecords.get(adOrg.getAdOrgId() + "_" + month) != null).forEach(adOrg -> {
                           String yearMonthStr = String.valueOf(month);
                           int yearInt = Integer.parseInt(yearMonthStr.substring(0, 4));
                           int monthInt = Integer.parseInt(yearMonthStr.substring(5, 7));

                           SaasStatisticsExportDTO export = new SaasStatisticsExportDTO();
                           export.setAdOrgId(adOrg.getAdOrgId());
                           export.setDataType("mer_order");
                           export.setExportType("excel");
                           export.setStaYear(yearInt);
                           export.setStaMonth(monthInt);
                           export.setStaType("month");
                           merchantShopClient.deleteStatisticsExport(export);
                           log.info("{}[{}]删除已存在export, 转换的年月{}-{}",curTime, adOrg.getAdOrgId() + "_" + month, yearInt, monthInt);
                       });
                   });
               }

               //3.根据vo生成excel文件
               execMonths.forEach(month -> {
                   adOrgs.stream().filter(adOrg -> exportExcelRecords.get(adOrg.getAdOrgId() + "_" + month) != null).forEach(adOrg -> {
                       //订单数据
                       List<MerchantPayOrderStatisticsDTO> statisticsPayOrderExcelVos = exportExcelRecords.get(adOrg.getAdOrgId() + "_" + month);

                       //处理退款到余额的退款金额
                       for (MerchantPayOrderStatisticsDTO vo:statisticsPayOrderExcelVos) {
                           BigDecimal refundAmount = vo.getRefundAmount();
                           String refundDetail = vo.getRefundDetail();

                           //在线支付的退款金额=0，并且退款详情不为空，并且有余额支付字样,并且退款明细的退款金额大于0
                           if (refundAmount!=null && refundAmount.compareTo(BigDecimal.ZERO)==0 && StringUtils.isNotBlank(refundDetail)) {
                               List<RefundDetailDTO> refundDetailDTOS = JSON.parseArray(refundDetail, RefundDetailDTO.class);
                               log.debug(JSON.toJSONString(refundDetailDTOS));
                               for (RefundDetailDTO refund:refundDetailDTOS) {
                                   if (refund.getPayType()!=null && refund.getPayType()==1 && refund.getPayAmount()!=null && refund.getPayAmount().compareTo(BigDecimal.ZERO)>0) {
                                       refundAmount = refundAmount.add(refund.getPayAmount());
                                   }
                               }
                               vo.setRefundAmount(refundAmount);
                               vo.setRefundDetail(null);
                           }

                           //处理收益
                           if ("原路".equals(vo.getRefundPath())) {
                               if (refundAmount!=null) {
                                   vo.setProfit(vo.getActuaAmount().subtract(refundAmount));
                               }else {
                                   vo.setProfit(vo.getActuaAmount());
                               }
                           }else if ("余额".equals(vo.getRefundPath())) {
                              vo.setProfit(vo.getActuaAmount());
                           }
                           //处理订单类型
                           if (vo.getBusinessType()!=null) {
                               vo.setOrderType(vo.getBusinessType().toString());
                               Optional.ofNullable(OrderBusinessType.findDescription(vo.getBusinessType())).ifPresent(typ -> vo.setOrderType(typ.getDescription()));
                           }
                       }
                       List<MerchantOrderDTO> resultList = CommonConverterTools.convert(MerchantOrderDTO.class,statisticsPayOrderExcelVos);

                       OutputStream outputStream = null;
                       //将Excel Workbook刷出到输出流
                       ExcelWriter writer = ExcelUtil.getWriter();
                       try {
                           //构建输出流
                           String fileName = month + "_订单数据.xls";
                           Path tmpPath = Paths.get("excel_export", "mer_order" , String.valueOf(adOrg.getAdOrgId()));
                           tmpPath.toFile().mkdirs();
                           Path filePath = tmpPath.resolve(fileName);
                           //File file = new File(tmpPath.toFile().getPath()+"/"+fileName);
                           outputStream = Files.newOutputStream(filePath, StandardOpenOption.CREATE_NEW);

                           writer.addHeaderAlias("outTradeNo", "订单编号");
                           writer.addHeaderAlias("orderType", "订单类型");
                           writer.addHeaderAlias("channelOrderNo", "第三方支付订单编号");
                           writer.addHeaderAlias("actuaAmount", "支付金额");
                           writer.addHeaderAlias("refundAmount", "退款金额");
                           writer.addHeaderAlias("refundPath", "退款路径");
                           writer.addHeaderAlias("profit", "收益（退款到余额的资金已结算，不影响实际收益");
                           writer.addHeaderAlias("status", "订单状态");

                           writer.addHeaderAlias("clientType", "支付渠道");
                           writer.addHeaderAlias("equipmentValue", "设备编码");
                           writer.addHeaderAlias("equipmentClass", "设备品类");
                           writer.addHeaderAlias("groupName", "场地名称");
                           writer.addHeaderAlias("createTime", "订单创建时间");
                           writer.addHeaderAlias("distributorId", "商家id");

                           //writer.merge(3, "人员信息");
                           writer.write(resultList,true);
                           writer.flush(outputStream, true);

                           exportExcelFileRecords.put(adOrg.getAdOrgId() + "_" + month, filePath.toFile());

                       } catch (Exception e) {
                           e.printStackTrace();
                           log.error("{}[{}]生成文件失败：{}",curTime, adOrg.getAdOrgId() + "_" + month, e.getMessage());
                       }finally {
                           // 关闭writer，释放内存
                           writer.close();
                       }
                   });
               });

               //4.上传excel文件到阿里云
               execMonths.forEach(month -> {
                   adOrgs.stream().filter(adOrg -> exportExcelRecords.get(adOrg.getAdOrgId() + "_" + month) != null).forEach(adOrg -> {
                       File file = exportExcelFileRecords.get(adOrg.getAdOrgId() + "_" + month);
                       String url = AliyunOss.upload("saas/export/merorder/" + adOrg.getAdOrgId() + "/" + file.getName(), file);
                       System.out.println("====================="+url);
                       exportExcelUrlRecords.put(adOrg.getAdOrgId() + "_" + month, url);
                       log.info("{}[{}]上传到oss url：{}",curTime, adOrg.getAdOrgId() + "_" + month, url);
                   });
               });

               //5.插入记录表
               execMonths.forEach(month -> {
                   adOrgs.stream().filter(adOrg -> exportExcelRecords.get(adOrg.getAdOrgId() + "_" + month) != null).forEach(adOrg -> {
                       String url = exportExcelUrlRecords.get(adOrg.getAdOrgId() + "_" + month);
                       List<MerchantPayOrderStatisticsDTO> merchantPayOrderStatisticsDos = exportExcelRecords.get(adOrg.getAdOrgId() + "_" + month);

                       SaasStatisticsExportDTO export = new SaasStatisticsExportDTO();
                       export.setAdOrgId(adOrg.getAdOrgId());
                       export.setCreated(new Date());
                       export.setDataType("mer_order");
                       export.setExportDataSum(merchantPayOrderStatisticsDos == null ? 0 : merchantPayOrderStatisticsDos.size());
                       export.setExportType("excel");
                       export.setExportUrl(url);

                       String yearMonthStr = String.valueOf(month);
                       int year = Integer.parseInt(yearMonthStr.substring(0, 4));
                       int monthI = Integer.parseInt(yearMonthStr.substring(5, 7));
                       export.setStaYear(year);
                       export.setStaMonth(monthI);
                       export.setStaDay(0);
                       export.setStaType("month");
                       export.setTitle(year + "年" + monthI + "月数据报表");
                       merchantShopClient.saveStatisticsExport(export);

                       log.info("{}[{}]成功插入到数据表，{}条数据",curTime, adOrg.getAdOrgId() + "_" + month, export.getExportDataSum());
                   });
               });

               //6.删除excel文件
               exportExcelFileRecords.entrySet().forEach(entry -> {
                   log.info("{}准备清除文件",curTime);
                   File file = entry.getValue();
                   if (file != null && file.exists() && !file.isDirectory()) {
                       file.delete();
                   }
               });
           }
           return SUCCESS;

       }catch (Exception e) {
           log.error("定时查询订单导出出错",e);
           return FAIL;
       }
   }
}
