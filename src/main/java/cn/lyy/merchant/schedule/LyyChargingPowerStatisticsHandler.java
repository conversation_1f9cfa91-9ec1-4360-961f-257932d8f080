package cn.lyy.merchant.schedule;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.util.DateUtil;
import cn.lyy.life.api.charging.dto.LyyChargingPowerStatisticsDTO;
import cn.lyy.life.api.charging.dto.LyyChargingPowerStatisticsParamDTO;
import cn.lyy.life.api.charging.service.LyyChargingElectricApi;
import cn.lyy.merchant.api.service.MerchantWhiteClient;
import cn.lyy.merchant.dao.odps.LyyChargingPowerStatisticsMapper;
import cn.lyy.merchant.dto.LyyChargingPowerStatisticsRequestDTO;
import cn.lyy.merchant.dto.merchant.LyyDistributorWhiteListVO;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
@JobHandler(value = "lyyChargingPowerStatisticsHandler")
public class LyyChargingPowerStatisticsHandler extends IJobHandler {

    @Resource
    private MerchantWhiteClient merchantWhiteClient;

    @Resource
    private LyyChargingPowerStatisticsMapper lyyChargingPowerStatisticsMapper;

    @Resource
    private LyyChargingElectricApi lyyChargingElectricApi;

    @Value("${chargingPowerStatistics.pageCount:1000}")
    private Integer pageCount = 1000;

    private final static Integer LYY_CHARGING_POWER_STATISTICS_WHITE_TYPE = 2005;

    @Override
    public ReturnT<String> execute(String param) throws Exception {

        LyyChargingPowerStatisticsRequestDTO requestDTO = null;
        if (StringUtils.isNotBlank(param)) {
            requestDTO = JSONObject.parseObject(param,LyyChargingPowerStatisticsRequestDTO.class);
        } else {
            BaseResponse<List<LyyDistributorWhiteListVO>> listBaseResponse = merchantWhiteClient.getWhiteDistributorByType(LYY_CHARGING_POWER_STATISTICS_WHITE_TYPE);
            if (0 != listBaseResponse.getCode() && null != listBaseResponse.getData()) {
                log.warn("获取白名单商户失败,response:{}",listBaseResponse);
                return ReturnT.FAIL;
            }

            requestDTO = new LyyChargingPowerStatisticsRequestDTO();
            List<String> collect = listBaseResponse.getData().stream().map(vo -> vo.getLyyDistributorId().toString()).collect(Collectors.toList());
            log.info("功率电量定时任务商户名单:{}",collect);
            requestDTO.setMerchantIds(String.join(",",collect));
            requestDTO.setFromDate(DateUtil.format(DateUtil.addDateByDays(-1),DateUtil.SIMPLE_PATTERN));
        }

        if (null == requestDTO) {
            log.warn("requestDTO is null");
            return ReturnT.FAIL;
        }

        Integer count = lyyChargingPowerStatisticsMapper.getLyyChargingPowerStatisticsCount(requestDTO);

        if (0 == count) {
            log.warn("查询数仓数据量为0");
            return ReturnT.FAIL;
        }

        Integer pageTotal = count / pageCount + 1;
        for (int i = 0; i < pageTotal; i++) {
            Integer offset = 0 == i ? 0 : (i) * pageCount;

            requestDTO.setLimit(pageCount);
            requestDTO.setOffset(offset);
            List<LyyChargingPowerStatisticsDTO> lyyChargingPowerStatisticsList = lyyChargingPowerStatisticsMapper.getLyyChargingPowerStatisticsList(requestDTO);
            if (null != lyyChargingPowerStatisticsList && lyyChargingPowerStatisticsList.size() > 0) {
                LyyChargingPowerStatisticsParamDTO paramDTO = new LyyChargingPowerStatisticsParamDTO();
                paramDTO.setParamList(lyyChargingPowerStatisticsList);
                lyyChargingElectricApi.powerElectricBatchInert(paramDTO);
            }
        }

        return ReturnT.SUCCESS;
    }
}
