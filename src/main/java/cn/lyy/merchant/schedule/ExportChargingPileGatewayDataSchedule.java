package cn.lyy.merchant.schedule;

import cn.lyy.merchant.factory.SqlPropertySourceFactory;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.nio.file.Path;
import java.nio.file.Paths;

@Slf4j
@Component
@JobHandler("exportChargingPileGatewayDataSchedule")
@PropertySource(value = "classpath:sql/ExportChargingPileGatewayDataSql.sql", factory = SqlPropertySourceFactory.class)
public class ExportChargingPileGatewayDataSchedule extends BaseExportDataSchedule implements EnvironmentAware {


    private Integer pageCount = 3000;

    private String[] sqlKey = new String[]{"柏来设备出货数明细","柏来设备在线数明细","飞宇设备出货数明细","飞宇设备在线数明细","电川设备出货数明细","电川设备在线数明细","诚马设备出货数明细","诚马设备在线数明细","公牛设备出货数明细","公牛设备在线数明细","烁飞新单机设备出货数明细","烁飞新单机设备在线数明细"};

    private Path path = Paths.get("excel_export", "gateway_data");

    private String ossPath = "saas/export/gatewayData/";

    private String titleName = "网关数据导出明细";

    @Override
    public void setEnvironment(Environment environment) {

        super.env = environment;
        super.pageCount = pageCount;
        super.sqlKey = sqlKey;
        super.tmpPath = path;
        super.ossPath = ossPath;
        super.titleName = titleName;
    }


    @Override
    public ReturnT<String> execute(String param) {
        return super.execute(param);
    }
}
