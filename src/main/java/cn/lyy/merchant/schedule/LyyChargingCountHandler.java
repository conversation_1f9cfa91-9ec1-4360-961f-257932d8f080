package cn.lyy.merchant.schedule;

import cn.lyy.merchant.api.service.LyyChargingOrderContrastClient;
import cn.lyy.merchant.dao.odps.LyyChargingOrderContrastMapper;
import cn.lyy.merchant.dto.ordercount.LyyChargingOrderContrast;
import cn.lyy.merchant.dto.ordercount.LyyChargingOrderContrastReq;
import cn.lyy.merchant.schedule.batchSwitch.BatchSwitchingMerchants;
import cn.lyy.merchant.util.DateUtils;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
@JobHandler(value = "LyyChargingCountHandler")
public class LyyChargingCountHandler extends IJobHandler {

    @Resource
    private LyyChargingOrderContrastMapper lyyChargingOrderContrastMapper;

    @Resource
    private LyyChargingOrderContrastClient lyyChargingOrderContrastClient;

    @Override
    public ReturnT<String> execute(String param) throws Exception {

        if (StringUtils.isBlank(param)) {
            log.error("入参不可为空");
            return ReturnT.FAIL;
        }
        JSONObject paramJson = JSONObject.parseObject(param);
        log.info("入参：{}",paramJson);
        List<Long> merchantIds = null;
        if (null != paramJson.get("batchSwitching")) {
            String merchantIdsStr = BatchSwitchingMerchants.KEYWORDS_MAPPING.get(paramJson.getString("batchSwitching"));
            merchantIds = Lists.newArrayList(merchantIdsStr.split(","))
                    .stream().map(Long::valueOf).collect(Collectors.toList());
        } else {
            merchantIds = Lists.newArrayList(paramJson.getString("merchantIds").split(","))
                    .stream().map(Long::valueOf).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(merchantIds)) {
            log.error("商户id为空");
            return FAIL;
        }

        String endDs = DateUtils.dateFormat(new Date(), DateUtils.DatePattern.yyyyMMdd);
        String startDs = DateUtils.dateFormat(DateUtils.addDay(new Date(), -90), DateUtils.DatePattern.yyyyMMdd);
        log.info("startDs:{},end:{}",startDs,endDs);
        for (int i = 1; ; i++) {
            List<LyyChargingOrderContrast> page = lyyChargingOrderContrastMapper.getPage(500, i == 1 ? 0 : (i-1) * 500, merchantIds, startDs, endDs);
            log.info("pageSize:{}",page.size());
            if (CollectionUtils.isEmpty(page)) {
                break;
            }
            LyyChargingOrderContrastReq req = new LyyChargingOrderContrastReq();
            req.setLyyChargingOrderContrasts(page);
            lyyChargingOrderContrastClient.batchInsert(req);
        }



        return ReturnT.SUCCESS;
    }
}
