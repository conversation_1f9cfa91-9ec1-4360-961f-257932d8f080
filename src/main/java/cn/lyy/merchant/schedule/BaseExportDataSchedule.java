package cn.lyy.merchant.schedule;

import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import cn.lyy.base.util.DateUtil;
import cn.lyy.merchant.api.service.MerchantShopClient;
import cn.lyy.merchant.constants.BusinessExceptionEnums;
import cn.lyy.merchant.dao.odps.ExportDataMapper;
import cn.lyy.merchant.dto.merchant.SaasStatisticsExportDTO;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.utils.ZipUtil;
import com.lyy.oss.service.impl.AliyunOss;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardOpenOption;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 *  数据报表通用处理流程
 *  说明：  1.新建job类继承此基类 为此父类必须参数赋值 详细参考 ExportChargingPileGatewayDataSchedule
 *         2.参数说明
 *              （1）env ：此参数存储sql语句，通过子类解析.sql文件存储
 *              （2）pageCount：大数据量情况下分页获取数据，默认单页2000条
 *              （3）sqlKey：.sql文件 --!后面的单行内容，配置化，获取sql
 *              （4）ossPath：上传至oss的路径
 *              （5）tmpPath：生成文件路径
 *              （6）isPage：是否开启分页，默认开启
 *              （7）titleName：上传oss会生成对应记录入库，记录名称
 *         3. .sql文件说明 参考：sql/ExportChargingPileGatewayDataSql.sql
 *              （1）--! 后内容作用一是用来获取sql，二是做为生成excel文件名称，尽量概括sql内容
 *              （2）查询字段会作为excel表头
 *              （3）分页查询的语句必须有统计数据数量的sql
 */
@Slf4j
@Component
public class BaseExportDataSchedule extends IJobHandler {

    //sql存储
    Environment env;

    private CountDownLatch countDownLatch = null;

    @Resource
    private ExportDataMapper exportDataMapper;

    // 单页获取数据条数
    Integer pageCount = 2000;

    //读取的sql key
    String[] sqlKey;

    //oss路径
    String ossPath;

    //生成文件路径
    Path tmpPath;

    //是否分页 分页sql必包含获取数据总量的sql
    Boolean isPage = true;

    //存放记录明称
    String titleName;

    Map<String,Object> queryParam = new HashMap<>();

    @Resource
    private MerchantShopClient merchantShopClient;

    public void setParam (String ossPath , String titleName) {
        this.ossPath = ossPath;
        this.titleName = titleName;
    }

    public void setQueryParam (Map<String,Object> queryParam) {

        if (queryParam.size() > 0) {
            for (Map.Entry<String,Object> entry : queryParam.entrySet()) {
                this.queryParam.put(":" + entry.getKey(),entry.getValue());
            }
        }
    }

    @Override
    public ReturnT<String> execute(String param){

        Map<String,String> map = toMap();
        countDownLatch = new CountDownLatch(map.size());
        ExecutorService executorService = Executors.newFixedThreadPool(map.size());


        for (Map.Entry<String, String> entry : map.entrySet()) {

            executorService.submit(new ExportDataThread(entry));

        }

        executorService.shutdown();

        //等待生成文件结束
        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        String ossUrl = "";
        File zipfile = ZipUtil.compress(tmpPath.toString(), null, null);
        String dateStr = DateUtil.getFormatString(new Date(),"yyyyMMdd");
        ossUrl = ossPath.concat(dateStr + ".zip");
        try {
            ossUrl = AliyunOss.upload(ossUrl, new FileInputStream(zipfile));

        } catch (Exception e) {
            throw new BusinessException(BusinessExceptionEnums.OSS_UPLOAD_ERROR);
        }

        //输出记录
        SaasStatisticsExportDTO export = new SaasStatisticsExportDTO();
        export.setCreated(new Date());
        export.setDataType("gateway_data");
        export.setExportType("excel");
        export.setExportUrl(ossUrl);
        String timeStr = new SimpleDateFormat("yyyyMMddHH").format(new Date());
        Integer year = Integer.parseInt(timeStr.substring(0, 4));
        Integer month = Integer.parseInt(timeStr.substring(4, 6));
        Integer day = Integer.parseInt(timeStr.substring(6,8));
        export.setStaYear(year);
        export.setStaMonth(month);
        export.setStaDay(day);
        export.setStaType("day");
        export.setTitle(timeStr + "_" + titleName);
        log.info("数据报表导出，名称：{},下载链接:{}",export.getTitle(),export.getExportUrl());
        merchantShopClient.saveStatisticsExport(export);

        //删除压缩包
        zipfile.delete();

        return SUCCESS;
    }

    /**
     * sql封装进map
     * @return
     */
    public Map<String,String> toMap () {

        Map<String,String> map = new HashMap<>();


        for (String str : sqlKey) {
            map.put(str,env.getProperty(str));
        }

        return map;
    }

    private void generateFile(Map<String,String> headers, List resultList, Path filePath, Path mkdirPath) {

        OutputStream outputStream = null;
        //将Excel Workbook刷出到输出流
        ExcelWriter writer = ExcelUtil.getWriter(true);
        try {
            //构建输出流
            mkdirPath.toFile().mkdirs();
            outputStream = Files.newOutputStream(filePath, StandardOpenOption.CREATE_NEW);

            for (Map.Entry<String,String> entry : headers.entrySet()) {

                writer.addHeaderAlias(entry.getKey(), entry.getValue());

            }

            writer.write(resultList,true);
            writer.flush(outputStream, true);

        } catch (Exception e) {

            log.error("生成{}文件失败，error:{}",filePath,e);
            throw new BusinessException("生成excel文件" + filePath + "失败");

        }finally {
            // 关闭writer，释放内存
            writer.close();
        }
    }

    class ExportDataThread implements Callable {

        private Map.Entry<String,String> entry;

        public ExportDataThread (Map.Entry<String,String> entry) {
            this.entry = entry;
        }

        @Override
        public Object call() {
            try {

                List<LinkedHashMap<String,Object>> list = new ArrayList();
                LinkedHashMap<String,String> header = new LinkedHashMap<>();

                String dateStr = new SimpleDateFormat("yyyyMMdd").format(new Date());
                String fileName = entry.getKey() + ".xlsx";
                Path mkdirPath = tmpPath.resolve(dateStr);
                Path filePath = mkdirPath.resolve(fileName);
                //如果文件存在 则不用重复生成
                if (filePath.toFile().exists()) {
                    return Boolean.TRUE;
                }

                if (isPage) {
                    String countSql = env.getProperty(entry.getKey() + "_count");
                    countSql = doSql(countSql);
                    if (StringUtils.isBlank(countSql)) {
                        throw new BusinessException("无法获取[" + entry.getKey() + "]统计数据总量的sql");
                    }
                    Integer count = exportDataMapper.data_count_execute(countSql);
                    count = null == count ? 0 : count;
                    //分页获取数据
                    Integer pageTotal = count / pageCount + 1;
                    for (int i = 0; i < pageTotal; i++) {
                        Integer offset = 0 == i ? 0 : (i) * pageCount;
                        String sql = entry.getValue() + " limit " + pageCount + " offset " + offset;
                        sql = doSql(sql);
                        List<LinkedHashMap<String,Object>> shipmentsInfoDtoS =
                                exportDataMapper.execute_sql(sql);
                        list.addAll(shipmentsInfoDtoS);
                    }

                } else {
                    list = exportDataMapper.execute_sql(entry.getValue());
                }
                if (null != list && list.size() > 0) {
                    LinkedHashMap<String, Object> map = list.get(0);
                    for (Map.Entry<String,Object> entryHeader : map.entrySet()) {
                        header.put(entryHeader.getKey(), entryHeader.getKey());
                    }
                }
                generateFile(header, list, filePath,mkdirPath);
            } catch (Exception e) {
                log.error("export data {} error：{}",entry.getKey(),e);
                throw e;
            } finally {
                countDownLatch.countDown();
            }
            return Boolean.TRUE;
        }
    }

    private String doSql (String sql) {

        String regexp = "";
        if (queryParam.size() > 0) {
            for (Map.Entry<String,Object> entry : queryParam.entrySet()) {
                regexp = regexp + entry.getKey() +  "|";
            }
        }

        if (StringUtils.isNotBlank(regexp)) {
            regexp = regexp.substring(0 , regexp.length() - 1);
            StringBuffer sb = new StringBuffer();

            Pattern p = Pattern.compile(regexp);

            Matcher m = p.matcher(sql);

            while (m.find()) {
                m.appendReplacement(sb, queryParam.get(m.group()).toString());
            }
            m.appendTail(sb);
            return sb.toString();
        }
        return sql;
    }

}
