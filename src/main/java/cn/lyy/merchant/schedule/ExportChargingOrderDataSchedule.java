package cn.lyy.merchant.schedule;

import cn.lyy.base.util.DateUtil;
import cn.lyy.base.util.MD5Util;
import cn.lyy.merchant.factory.SqlPropertySourceFactory;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
@JobHandler("exportChargingOrderDataSchedule")
@PropertySource(value = "classpath:sql/ExportChargingOrderDataSql.sql", factory = SqlPropertySourceFactory.class)
public class ExportChargingOrderDataSchedule extends BaseExportDataSchedule implements EnvironmentAware {


    @Value("${export.charging3.order.distributorIds}")
    private String distributorIds;

    private Integer pageCount = 1000;

    private String[] sqlKey = new String[]{"3.0充电记录导出"};

    private Path path = Paths.get("excel_export", "charging_order");

    @Override
    public void setEnvironment(Environment environment) {

        super.env = environment;
        super.pageCount = pageCount;
        super.sqlKey = sqlKey;
        super.tmpPath = path;
    }


    @Override
    public ReturnT<String> execute(String param) {

        String[] split = distributorIds.split(",");
        for (String s : split) {
            Map<String,Object> queryMap = new HashMap<>();
            queryMap.put("distributor_id",s);
            queryMap.put("ds", DateUtil.getFormatString(DateUtil.addDateByDays(-2),"yyyyMMdd"));
            queryMap.put("endds",DateUtil.getFormatString(DateUtil.addDateByDays(-1),"yyyyMMdd"));
            JSONObject jsonObject = JSONObject.parseObject(param);
            if (null != jsonObject && null != jsonObject.get("startDate") && null != jsonObject.get("endDate")) {
                queryMap.put("ds", jsonObject.get("startDate"));
                queryMap.put("endds",jsonObject.get("endDate"));
            }
            String str  = MD5Util.MD5("charging_order_" + s);
            super.setParam("saas/export/" + str + "/" , s + "_充电记录导出");
            super.setQueryParam(queryMap);
            super.execute(param);
        }

        return SUCCESS;
    }
}
