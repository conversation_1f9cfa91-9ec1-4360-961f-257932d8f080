package cn.lyy.merchant.filter;

import cn.lyy.merchant.constants.BusinessExceptionEnums;
import cn.lyy.merchant.exception.BusinessException;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.Optional;
import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import lombok.Data;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * 将公共参数放入本地线程 配合数据填充使用
 */
@Slf4j
public class CommonParamsContext implements Filter {

    private static final ThreadLocal<CommonParamsVo> local = new InheritableThreadLocal<>();
    private static final String BELONG_TO = "authorization-belong-to";

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws ServletException, IOException {
        try {
            initCommonParams((HttpServletRequest) request);
            chain.doFilter(request, response);
        } finally {
            if (local.get() != null) {
                local.remove();
            }
        }
    }

    private void initCommonParams(HttpServletRequest request) {
        CommonParamsVo commonParamsVo = new CommonParamsVo();
        Field[] declaredFields = commonParamsVo.getClass().getDeclaredFields();
        for (int i = 0; i < declaredFields.length; i++) {
            Field field = declaredFields[i];
            String name = field.getName();
            String parameter = request.getHeader(name);
            if (StringUtils.isNotBlank(parameter)) {
                field.setAccessible(true);
                try {
                    Class<?> type = field.getType();
                    if (type.isAssignableFrom(Long.class)) {
                        field.set(commonParamsVo, Long.parseLong(parameter));
                    } else {
                        field.set(commonParamsVo, parameter);
                    }
                } catch (IllegalAccessException e) {
                    log.error("base controller error", e);
                }
            }
        }
        commonParamsVo.setBelongTo(Optional.ofNullable(request.getHeader(BELONG_TO)).map(Long::parseLong).orElse(null));
        local.set(commonParamsVo);
    }

    public static CommonParamsVo getCommonParams() {
        return local.get();
    }

    public static Long getMerchantId() {
        return Optional.ofNullable(local.get()).map(CommonParamsVo::getMerchantId).orElse(null);
    }

    public static Long getBelongTo() {
        return Optional.ofNullable(local.get()).map(CommonParamsVo::getBelongTo).orElse(null);
    }

    public static Long getOrgnizationId() {
        return Optional.ofNullable(local.get()).map(CommonParamsVo::getOrgnizationId).orElse(null);
    }


    public static Long getStoreId() {
        return Optional.ofNullable(local.get()).map(CommonParamsVo::getStoreId).orElse(null);
    }

    public static Long getNotNullMerchantId() {
        Long merchantId = Optional.ofNullable(local.get()).map(CommonParamsVo::getMerchantId).orElse(null);
        if (merchantId == null) {
            throw new BusinessException(BusinessExceptionEnums.MERCHANT_ID_IS_NUMM_ERROR);
        }
        return merchantId;
    }

    public static Long getAdUserId() {
        return Optional.ofNullable(local.get()).map(CommonParamsVo::getAdUserId).orElse(null);
    }

    public static void setBelongTo(Long belongTo) {
        local.get().setBelongTo(belongTo);
    }

    @Data
    @ToString
    public class CommonParamsVo {

        /**
         * 组织ID
         */
        private Long orgnizationId;

        /**
         * 商户ID
         */
        private Long merchantId;

        private Long belongTo;

        /**
         * 门店ID
         */
        private Long storeId;

        /**
         * 后台用户ID
         */
        private Long adUserId;

        private String userName;
    }
}
