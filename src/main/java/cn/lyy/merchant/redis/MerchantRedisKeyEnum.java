package cn.lyy.merchant.redis;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2019/10/10 17:20
 * @description B端使用redis key
 */
@Getter
@AllArgsConstructor
public enum MerchantRedisKeyEnum {

    /**
     *
     */
    COMMON_REGION_PROVINCE("merchant:common:region:province", "省份缓存", -1),
    COMMON_REGION_CITY("merchant:common:region:city", "城市缓存", -1),
    COMMON_REGION_CITY_ID("merchant:common:region:cityId", "城市id缓存", -1),
    COMMON_REGION_DISTRICT("merchant:common:region:district", "地区缓存", -1),
    COMMON_REGION_ALL("merchant:common:region:all", "所有省市区对应关系", -1),

    COMMON_SESSION("merchant:common:session:", "session使用", 1800),

    RED_COINS_DISABLED_GROUP("merchant:redCoins:disabledGroup", "红包禁用场地id", -1),
    ENTERPRISE_MSG_PACKAGE_ID("merchant:enterpriseMsg:packageId", "企业号消息缓存", 60),

    REGISTER_CONCURRENT_LOCK("merchant:register:concurrentLock:", "注册并发控制", 20),
    INTEGRAL_DELIVERY_CONCURRENT_LOCK("merchant:integralMall:concurrentLock:", "派积分并发控制", 5),

    MERCHANT_INFO_SUBMIT_SPECIAL_SWITCH("merchant:infoSubmit:specialSwitch", "特约商户模块显示开关", -1),
    MERCHANT_INFO_SUBMIT_BUSINESS_VERIFY_SWITCH("merchant:infoSubmit:businessVerifySwitch", "进件验证商户企业信息前校验开关", -1),
    MERCHANT_INFO_SUBMIT_TEST_MERCHANT("merchant:infoSubmit:testMerchant", "特约商户模块测试商户id", -1),

    OFFICIAL_ACCOUNT_CONFIG_CONCURRENT_LOCK("merchant:officialAccount:settingConcurrentLock:", "公众号配置并发控制", 3),
    MARKETING_LEISURE_ACTIVITY_CONCURRENT_LOCK("merchant:marketing:leisureSaveConcurrentLock", "闲时券创建并发控制", 3),

    @Deprecated
    INCOME_STATISTICS_SWITCH("merchant:incomeStatistics:showSwitch", "收益统计展示开关", -1),
    WITHDRAW_CONCURRENT_LOCK("merchant:withdraw:concurrentLock:", "提现并发控制", 3),
    MERCHANT_PAY_CONCURRENT_LOCK("merchant:pay:concurrentLock:", "商户支付并发控制", 30),
    MARKETING_ACTIVITY_SAVE_CONCURRENT_LOCK("merchant:marketing:saveActivityConcurrentLock:", "营销活动保存并发控制", 3),

    MERCHAN_DATA_UPDATE_MCHNAME_LOCK("merchant:merchantData:updateMchNameLock:", "商户简称修改并发控制", 10),
    MERCHAN_DATA_UPDATE_SAME_ACTNAME_LOCK("merchant:merchantData:updateSameActNameLock:", "同名银行卡修改并发控制", 10),
    MERCHAN_DATA_UPDATE_DIF_ACTNAME_LOCK("merchant:merchantData:updateDifActNameLock:", "非同名银行卡修改并发控制", 10),

    MERCHANT_VENDING_MOTOR_TEST_LIMIT("merchant:vendingMotorTest:limit:", "售货机批量设备一键测试", 90),

    STATISTIC_EXCEL_DOWNLOAD_CODE("merchant:statistics:downFile", "扭蛋机经营数据excel下载code", 60 * 30),
    MARKETING_DIVIDE_WECHAT_CODE("merchant:divide:wechat:code:", "分账微信账号绑定二维码时效", 60 * 10),

    FIRMWARE_ALIPAY_EQUIPMENT("merchant:firmware:alipay:equipment", "支付设备版本号", -1),
    FIRMWARE_ALIPAY_REVERSE_EQUIPMENT("merchant:firmware:alipay:reverse:equipment", "支付直连反扫设备版本号", -1),
    APP_ALIPAY_EQUIPMENT("merchant:app:alipay:equipment", "App支付直连设备", -1),
    APP_WECHAT_FACE_EQUIPMENT("merchant:app:wechatface:equipment", "App微信刷脸设备", -1),

    WITHDRAW_WXAUTH_CODE("merchant:withdraw:wxauth:code:", "支付提现微信账号绑定二维码时效", 60 * 30),

    LOLLIPOP_VALUE_HASH("merchant:lollipop:value:hash", "棒棒糖机缓存列表", -1),
    EQUIPMENT_SERVER_SWITCH("merchant:equipment:server:switch","设备服务开关", -1),

    ELECTRONIC_HOME_MODAL("merchant:electronic:home:modal:","电子类首页弹窗", -1),
    ADVERT_SWITCH_HOME_MODAL("merchant:advert:home:modal","广告开关首页弹窗", -1),
    
    ASYNC_REFUND_LOCK("merchant:async:lock:", "异步退款业务锁", 3 * 60),
    BAIDUYUN_ACCESS_CODE("merchant:baiduyun:accesscode:", "百度云授权码", 60 * 60 * 24 * 28),

    MERCHANT_FACTORYMCHDIVIDE_WECHAT_CODE("merchant:factorymchdivide:wechat:code:", "工厂商户分账微信账号绑定二维码时效", 60 * 10),

    MERCHANT_OCR_LIMITLOCK("merchant:ocr:limitlock:", "OCR识别限制锁", 60 * 60 * 24),
    LOAN_TOKEN_KEY("opendata:loan:token:", "拉卡拉贷款token", 60),
    MERCHANT_VERIFY_CODE("merchant:verify:code:","商户注册图形验证码",60*3),
    MERCHANT_MODIFY_ACCOUNT_LIMIT("merchant:modify:account:limit:","商户修改账号次数限制",60*3),
    MERCHANT_USER_REFRESH_LOGIN_STATUS("saas2:merchant:user:refresh:login-status", "商户更新登陆状态",600),
    MERCHANT_ALL_ROLE_AND_AUTH("saas3:merchant:user:role-auth", "B端所有请求路径与角色的列表", 1800),
    MERCHANT_REGISTER_CONCURRENT_LIMIT("saas3:merchant:register:limit:", "商户注册限制", 5),
    EQUIPMENT_INTER_WORKING("equipment_inter_working:equipment_register_system:","设备互通",60 * 60),

    MERCHANT_ANNUAL_REPORT("merchant:annual:report:", "商家年度报告", 60 * 60 * 24),
    MERCHANT_ANNUAL_REPORT_POP_UPS_MERCHANT_ID("merchant:annual:report:popups:merchantid:", "商家年度报告弹窗商户已弹次数", 7 * 60 * 60 * 24),
    MERCHANT_ANNUAL_REPORT_POP_UPS_DATE_MERCHANT_ID("merchant:annual:report:popups:date:merchantid:", "商家年度报告弹窗商户弹窗日期", 7 * 60 * 60 * 24),
    
    MERCHANT_USER_CUSTOM_TEMPORARY_POPUP_RECORD("merchant:custom:popup:record:user:", "自定义弹窗记录前缀", 0),

    MERCHANT_PAGE_FUNCTION_PARAM("merchant:page:function:param:", "商家页面功能参数缓存前缀", 10),
    MERCHANT_EXPORTEMAIL_CONCURRENT_LOCK("merchant:exportemail:concurrentLock:", "导出经营数据并发控制", 10),
    ;


    private String key;
    private String description;

    /**
     * 单位：秒
     */
    private Integer timeout;
}
