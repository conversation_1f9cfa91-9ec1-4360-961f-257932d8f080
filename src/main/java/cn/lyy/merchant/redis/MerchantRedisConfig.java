package cn.lyy.merchant.redis;

import cn.lyy.base.util.StringUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisClusterConfiguration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisNode;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import redis.clients.jedis.JedisPoolConfig;

import java.util.Set;

import static org.springframework.util.Assert.isTrue;
import static org.springframework.util.Assert.notNull;
import static org.springframework.util.StringUtils.split;

/**
 * <AUTHOR>
 * @date 2019/10/9 19:49
 * @description B端单独使用的Redis配置
 */
@Configuration
public class MerchantRedisConfig {

    @Value("${merchant.redis.cluster.nodes}")
    private String clusterNodes;

    @Value("${merchant.redis.cluster.max-redirects}")
    private int clusterMaxRedirects;

    @Value("${merchant.redis.password}")
    private String password;

    @Value("${merchant.redis.pool.max-idle}")
    private int poolMaxIdle;

    @Value("${merchant.redis.pool.max-active}")
    private int poolMaxActive;

    @Value("${merchant.redis.pool.max-wait}")
    private int poolMaxWait;

    private StringRedisTemplate redisTemplate() {
        StringRedisTemplate temple = new StringRedisTemplate();
        temple.setConnectionFactory(connectionFactory());
        // 如果不是使用Spring注入形式，需要调用一下这个初始化方法
        temple.afterPropertiesSet();
        return temple;
    }

    private RedisClusterConfiguration redisClusterConfiguration() {
        RedisClusterConfiguration cluster = new RedisClusterConfiguration();
        Set<String> hostAndPortSet = StringUtil.commaDelimitedListToSet(clusterNodes);
        for (String hostAndPort : hostAndPortSet) {
            cluster.addClusterNode(readHostAndPortFromString(hostAndPort));
        }
        cluster.setMaxRedirects(clusterMaxRedirects);
        return cluster;
    }

    private RedisNode readHostAndPortFromString(String hostAndPort) {
        String[] args = split(hostAndPort, ":");
        notNull(args, "HostAndPort need to be seperated by  ':'.");
        isTrue(args.length == 2,
                "Host and Port String needs to specified as host:port");
        return new RedisNode(args[0], Integer.valueOf(args[1]).intValue());
    }

    private RedisConnectionFactory connectionFactory() {
        RedisClusterConfiguration cluster = redisClusterConfiguration();
        JedisPoolConfig poolConfig = poolConfig(poolMaxIdle, poolMaxActive, poolMaxWait);
        JedisConnectionFactory factory = new JedisConnectionFactory(cluster, poolConfig);
        factory.setPassword(password);
        factory.setDatabase(0);
        // 初始化连接pool
        factory.afterPropertiesSet();
        return factory;
    }

    private JedisPoolConfig poolConfig(int maxIdle, int maxTotal, long maxWaitMillis) {
        JedisPoolConfig poolConfig = new JedisPoolConfig();
        poolConfig.setMaxIdle(maxIdle);
        poolConfig.setMaxTotal(maxTotal);
        poolConfig.setMaxWaitMillis(maxWaitMillis);
        poolConfig.setTestOnBorrow(false);
        poolConfig.setTestWhileIdle(true);
        return poolConfig;
    }

    public StringRedisTemplate getRedisTemplate() {
        return redisTemplate();
    }
}
