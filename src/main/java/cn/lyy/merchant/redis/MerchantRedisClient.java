package cn.lyy.merchant.redis;

import cn.hutool.core.collection.CollUtil;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnectionCommands;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static java.util.Optional.ofNullable;

/**
 * <AUTHOR>
 * @date 2019-10-10 16:35:22
 * @description B端单独使用的Redis客户端
 */
@Slf4j
@Component
public class MerchantRedisClient {

    private static StringRedisTemplate template;

    private static ObjectMapper om = new ObjectMapper();

    @Autowired
    private MerchantRedisConfig merchantRedisConfig;

    @PostConstruct
    public void init() {
        template = merchantRedisConfig.getRedisTemplate();
    }

    /**
     * 执行脚本
     * 
     * @param resultClz <p>{@link org.springframework.data.redis.connection.ReturnType} 
     * @param script 脚本
     * @param keys 
     * @param args
     * @return 
     */
    public static <T> T execute(Class<T> resultClz, String script, List<String> keys, Object... args) {
        return eval(resultClz, script, keys, args);
    }
    
    /**
     * hset
     * @param redisKey key枚举
     * @param field 领域
     * @param value 值
     */
    public static void hset(MerchantRedisKeyEnum redisKey, String field, String value) {
        hset(redisKey.getKey(), field, value);
    }

    /**
     * hget
     * @param redisKey key枚举
     * @param field 领域
     * @return
     */
    public static String hget(MerchantRedisKeyEnum redisKey, String field) {
        return hget(redisKey.getKey(), field);
    }

    public static String hgetByKey(String redisKey, String field) {
        return hget(redisKey, field);
    }
    
    /**
     * incr
     * @param redisKey key枚举
     * @param specialValue 拼在key后面的特殊值
     * @return
     */
    public static long incr(MerchantRedisKeyEnum redisKey, String specialValue) {
        return incr(redisKey.getKey().concat(ofNullable(specialValue).orElse("")));
    }
    /**
     * decr
     * @param redisKey key枚举
     * @param specialValue 拼在key后面的特殊值
     * @return
     */
    public static void decr(MerchantRedisKeyEnum redisKey, String specialValue) {
        decr(redisKey.getKey().concat(ofNullable(specialValue).orElse("")));
    }

    /**
     *
     * @param redisKey key枚举
     * @param specialValue 拼在key后面的特殊值
     * @param value 值
     */
    public static void set(MerchantRedisKeyEnum redisKey, String specialValue, String value) {
        set(redisKey.getKey().concat(ofNullable(specialValue).orElse("")), value);
    }

    /**
     * set
     * @param redisKey key 枚举
     * @param value 值
     */
    public static void set(MerchantRedisKeyEnum redisKey, String value) {
        set(redisKey.getKey(), value);
    }

    /**
     * setex
     * @param redisKey key枚举
     * @param specialValue 拼在key后面的特殊值
     * @param value
     */
    public static void setex(MerchantRedisKeyEnum redisKey, String specialValue, String value) {
        setex(redisKey.getKey().concat(ofNullable(specialValue).orElse("")),redisKey.getTimeout(), value);
    }

    /**
     * existes
     * @param redisKey key枚举
     * @param specialValue 拼在key后面的特殊值
     * @return
     */
    public static boolean exists(MerchantRedisKeyEnum redisKey, String specialValue) {
        return exists(redisKey.getKey().concat(ofNullable(specialValue).orElse("")));
    }

    /**
     * del
     * @param redisKey key枚举
     * @param specialValue 拼在key后面的特殊值
     */
    public static void del(MerchantRedisKeyEnum redisKey, String specialValue) {
        del(redisKey.getKey().concat(ofNullable(specialValue).orElse("")));
    }

    /**
     * hexists
     * @param redisKey key枚举
     * @param field 领域
     * @return
     */
    public static boolean hexists(MerchantRedisKeyEnum redisKey, String field) {
        return hexists(redisKey.getKey(), field);
    }

    /**
     * expire
     * @param redisKey key枚举
     * @param specialValue 拼在key后面的特殊值
     */
    public static void expire(MerchantRedisKeyEnum redisKey, String specialValue) {
        expire(redisKey.getKey().concat(ofNullable(specialValue).orElse("")), redisKey.getTimeout());
    }

    /**
     * 指定失效时间过期
     * 
     * @param redisKey
     * @param specialValue
     * @param unixTimestamp unix 时间戳（s）
     */
    public static void expireAt(MerchantRedisKeyEnum redisKey, String specialValue, long unixTimestamp) {
        expireAt(redisKey.getKey().concat(ofNullable(specialValue).orElse("")), unixTimestamp);
    }

    /**
     * get
     * @param redisKey key枚举
     * @param specialValue 拼在key后面的特殊值
     */
    public static String get(MerchantRedisKeyEnum redisKey, String specialValue) {
        return get(redisKey.getKey().concat(ofNullable(specialValue).orElse("")));
    }

    /**
     * get
     * @param redisKey key枚举
     * @return
     */
    public static String get(MerchantRedisKeyEnum redisKey) {
        return get(redisKey.getKey());
    }
    private static <T> void setObject(String key, T object) {
        if (!StringUtils.isEmpty(key)) {
            if (object instanceof Number) {
                template.opsForValue().set(key, String.valueOf(object));
            } else if (object.getClass().isPrimitive()) {
                template.opsForValue().set(key, String.valueOf(object));
            } else if (object instanceof String) {
                template.opsForValue().set(key, String.valueOf(object));
            } else {
                try {
                    String value = om.writeValueAsString(object);
                    template.opsForValue().set(key, value);
                } catch (JsonProcessingException var3) {
                    log.error(var3.getMessage(), var3);
                }
            }
        }
    }

    private static <T> void setNxObject(String key, T object) {
        if (!StringUtils.isEmpty(key)) {
            if (object instanceof Number) {
                template.opsForValue().setIfAbsent(key, String.valueOf(object));
            } else if (object.getClass().isPrimitive()) {
                template.opsForValue().setIfAbsent(key, String.valueOf(object));
            } else if (object instanceof String) {
                template.opsForValue().setIfAbsent(key, String.valueOf(object));
            } else {
                try {
                    String value = om.writeValueAsString(object);
                    template.opsForValue().setIfAbsent(key, value);
                } catch (JsonProcessingException var3) {
                    log.error(var3.getMessage(), var3);
                }

            }
        }
    }

    private static void setExpireObject(String key, Object object, int seconds) {
        if (!StringUtils.isEmpty(key)) {
            if (object instanceof Number) {
                template.opsForValue().set(key, String.valueOf(object), (long) seconds, TimeUnit.SECONDS);
            } else if (object.getClass().isPrimitive()) {
                template.opsForValue().set(key, String.valueOf(object), (long) seconds, TimeUnit.SECONDS);
            } else if (object instanceof String) {
                template.opsForValue().set(key, (String) object, (long) seconds, TimeUnit.SECONDS);
            } else {
                try {
                    String value = om.writeValueAsString(object);
                    template.opsForValue().set(key, value, (long) seconds, TimeUnit.SECONDS);
                } catch (JsonProcessingException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
    }

    private static <T> Object getObject(String key, Class<T> clazz) {
        String value = template.opsForValue().get(key);
        if (StringUtils.isEmpty(value)) {
            return null;
        } else if (clazz.getName().equals(String.class.getName())) {
            return value;
        } else if (clazz.getName().equals(Integer.class.getName())) {
            return Integer.valueOf(value);
        } else if (clazz.getName().equals(Double.class.getName())) {
            return Double.valueOf(value);
        } else if (clazz.getName().equals(Float.class.getName())) {
            return Float.valueOf(value);
        } else if (clazz.getName().equals(Byte.class.getName())) {
            return Byte.valueOf(value);
        } else if (clazz.getName().equals(Long.class.getName())) {
            return Long.valueOf(value);
        } else if (clazz.getName().equals(Short.class.getName())) {
            return Short.valueOf(value);
        } else if (clazz.getName().equals(BigDecimal.class.getName())) {
            return new BigDecimal(value);
        } else {
            try {
                return om.readValue(template.opsForValue().get(key), clazz);
            } catch (JsonParseException var4) {
                log.error(var4.getMessage(), var4);
            } catch (JsonMappingException var5) {
                log.error(var5.getMessage(), var5);
            } catch (IOException var6) {
                log.error(var6.getMessage(), var6);
            }

            return null;
        }
    }

    private static Integer append(String key, String value) {
        return StringUtils.isEmpty(key) ? 0 : template.opsForValue().append(key, value);
    }

    private static Long decr(String key) {
        return StringUtils.isEmpty(key) ? 0L : template.opsForValue().increment(key, -1L);
    }

    private static Long decrBy(String key, long integer) {
        return StringUtils.isEmpty(key) ? 0L : template.opsForValue().increment(key, -integer);
    }

    private static void del(String key) {
        ofNullable(key).filter(k -> k.length() > 0).ifPresent((k) -> template.delete(key));
    }

    public static Boolean exists(String key) {
        if (StringUtils.isEmpty(key)) {
            log.error("exists key is null");
            return false;
        } else {
            try {
                return template.hasKey(key);
            } catch (Exception var2) {
                log.error("exists:{}", key);
                log.error(var2.getMessage(), var2);
                return false;
            }
        }
    }

    private static boolean expire(String key, int seconds) {
        return StringUtils.isEmpty(key) ? false : template.expire(key, (long) seconds, TimeUnit.SECONDS);
    }

    private static boolean expireAt(String key, long unixTime) {
        return StringUtils.isEmpty(key) ? false : template.expireAt(key, new Date(unixTime));
    }

    private static String get(String key) {
        return StringUtils.isEmpty(key) ? null : template.opsForValue().get(key);
    }

    private static String getSet(String key, String value) {
        return StringUtils.isEmpty(key) ? null : template.opsForValue().getAndSet(key, value);
    }

    private static String getrange(String key, long startOffset, long endOffset) {
        return StringUtils.isEmpty(key) ? null : template.opsForValue().get(key, startOffset, endOffset);
    }

    public static Long hdel(String key, String field) {
        return StringUtils.isEmpty(key) ? 0L : template.opsForHash().delete(key, field);
    }

    private static Boolean hexists(String key, String field) {
        return StringUtils.isEmpty(key) ? false : template.opsForHash().hasKey(key, field);
    }

    private static String hget(String key, String field) {
        if (StringUtils.isEmpty(key)) {
            log.error("hget key is null");
            return null;
        } else {
            try {
                Object value = template.opsForHash().get(key, field);
                return null == value ? null : value.toString();
            } catch (Exception var3) {
                log.error("hget:" + key + ";" + field);
                log.error(var3.getMessage(), var3);
                return null;
            }
        }
    }

    private static Map<String, String> hgetAll(String key) {
        if (StringUtils.isEmpty(key)) {
            return null;
        } else {
            Map<String, String> resultMap = new HashMap();
            Map<Object, Object> all = template.opsForHash().entries(key);
            if (null == all) {
                return resultMap;
            } else {

                for (Object o : all.entrySet()) {
                    Map.Entry<Object, Object> entry = (Map.Entry) o;
                    resultMap.put(entry.getKey().toString(), entry.getValue().toString());
                }

                return resultMap;
            }
        }
    }

    private static Long hincrBy(String key, String field, long value) {
        return StringUtils.isEmpty(key) ? 0L : template.opsForHash().increment(key, field, value);
    }

    private static Set<String> hkeys(String key) {
        if (StringUtils.isEmpty(key)) {
            return null;
        } else {
            Set<Object> set = template.opsForHash().keys(key);
            return ofNullable(set).map(s -> {
                Set<String> resultSet = new HashSet();
                Iterator it = set.iterator();
                while (it.hasNext()) {
                    Object obj = it.next();
                    resultSet.add(obj.toString());
                }
                return resultSet;
            }).orElse(new HashSet<>());
        }
    }

    private static Long hlen(String key) {
        return StringUtils.isEmpty(key) ? null : template.opsForHash().size(key);
    }

    private static List<String> hmget(String key, String... fields) {
        if (StringUtils.isEmpty(key)) {
            return null;
        } else {
            Collection<Object> c = new ArrayList();
            int var4 = fields.length;

            Collections.addAll(c, fields);

            List<Object> objList = template.opsForHash().multiGet(key, c);
            List<String> resultList = new ArrayList();
            if (null == objList) {
                return resultList;
            } else {
                Iterator var9 = objList.iterator();

                while (var9.hasNext()) {
                    Object obj = var9.next();
                    resultList.add(obj.toString());
                }

                return resultList;
            }
        }
    }

    private static void hmset(String key, Map<String, String> hash) {
        if (!StringUtils.isEmpty(key)) {
            template.opsForHash().putAll(key, hash);
        }
    }

    private static void hset(String key, String field, String value) {
        if (!StringUtils.isEmpty(key)) {
            template.opsForHash().put(key, field, value);
        }
    }

    public static void hsetnx(String key, String field, Object value) {
        ofNullable(key).filter(k -> k.length() > 0)
                .ifPresent(k -> template.opsForHash().putIfAbsent(k, field, value));
    }

    private static void hsetnx(String key, String field, String value) {
        ofNullable(key).filter(k -> k.length() > 0)
                .ifPresent(k -> template.opsForHash().putIfAbsent(k, field, value));
    }

    private static List<String> hvals(String key) {
        return ofNullable(key).filter(k -> k.length() > 0)
                .map(k -> ofNullable(template.opsForHash().values(k)).orElse(new ArrayList<>())
                        .stream().map(Object::toString)
                        .collect(Collectors.toList())).orElse(null);
    }

    private static Long incr(String key) {
        return StringUtils.isEmpty(key) ? 0L : template.opsForValue().increment(key, 1L);
    }

    private static Long incrBy(String key, long integer) {
        return StringUtils.isEmpty(key) ? 0L : template.opsForValue().increment(key, integer);
    }

    private static String lindex(String key, long index) {
        return StringUtils.isEmpty(key) ? null : template.opsForList().index(key, index);
    }

    private static Long llen(String key) {
        return ofNullable(key).filter(k -> k.length() > 0).map(k -> template.opsForList().size(k))
                .orElse(0L);
    }

    private static String lpop(String key) {
        return ofNullable(key).filter(k -> k.length() > 0).map(k -> template.opsForList().leftPop(k))
                .orElse(null);
    }

    private static Long lpush(String key, String value) {
        return ofNullable(key).filter(k -> k.length() > 0).map(k -> template.opsForList().leftPush(key, value))
                .orElse(0L);
    }

    /**
     * @param:
     * @return:
     * @auther: Seraphim
     * @date: 2018/10/18 15:26
     * @description: 插入集合对象到redis
     */
    private static void lpush(String key, Collection collection) {
        ofNullable(key).filter(k -> k.length() > 0)
                .map(k -> template.opsForList().leftPushAll(k, collection));
    }

    private static List<String> lrange(String key, long start, long end) {
        return ofNullable(key).filter(k -> k.length() > 0).map(k -> template.opsForList().range(k, start, end))
                .orElse(null);
    }

    private static Long lrem(String key, long count, String value) {
        return ofNullable(key).filter(k -> k.length() > 0).map(k -> template.opsForList().remove(k, count, value))
                .orElse(0L);
    }

    private static void lset(String key, long index, String value) {
        ofNullable(key).filter(k -> k.length() > 0).ifPresent(k -> template.opsForList().set(k, index, value));
    }

    private static void ltrim(String key, long start, long end) {
        ofNullable(key).filter(k -> k.length() > 0).ifPresent(k -> template.opsForList().trim(k, start, end));
    }

    private static String rpop(String key) {
        return ofNullable(key).filter(k -> k.length() > 0).map(k -> template.opsForList().rightPop(key))
                .orElse(null);
    }

    private static Long rpush(String key, String... string) {
        return StringUtils.isEmpty(key) ? 0L : template.opsForList().rightPushAll(key, string);
    }

    private static Long sadd(String key, String member) {
        return ofNullable(key).filter(k -> k.length() > 0).map(k -> template.opsForSet().add(k, member))
                .orElse(0L);
    }

    private static Long scard(String key) {
        return ofNullable(key).filter(k -> k.length() > 0).map(k -> template.opsForSet().size(k))
                .orElse(0L);
    }

    private static void set(String key, String value) {
        ofNullable(key).filter(k -> k.length() > 0)
                .ifPresent(k -> template.opsForValue().set(k, value));
    }

    private static void setex(String key, int seconds, String value) {
        if (!StringUtils.isEmpty(key) && !StringUtils.isEmpty(value)) {
            template.opsForValue().set(key, value, (long) seconds, TimeUnit.SECONDS);
        }
    }

    private static boolean setnx(String key, String value) {
        return !StringUtils.isEmpty(key) && !StringUtils.isEmpty(value) ? template.opsForValue().setIfAbsent(key, value) : false;
    }

    private static void setrange(String key, long offset, String value) {
        if (!StringUtils.isEmpty(key)) {
            template.opsForValue().set(key, value, offset);
        }
    }

    private static Boolean sismember(String key, String member) {
        return StringUtils.isEmpty(key) ? false : template.opsForSet().isMember(key, member);
    }

    private static Set<String> smembers(String key) {
        return StringUtils.isEmpty(key) ? null : template.opsForSet().members(key);
    }

    private static String spop(String key) {
        return ofNullable(key).filter(k -> k.length() > 0).map(k -> template.opsForSet().pop(k))
                .orElse(null);
    }

    private static String srandmember(String key) {
        return ofNullable(key).filter(k -> k.length() > 0).map(k -> template.opsForSet().randomMember(k))
                .orElse(null);
    }

    private static Long srem(String key, String member) {
        return StringUtils.isEmpty(key) ? 0L : template.opsForSet().remove(key, member);
    }

    private static String substr(String key, int start, int end) {
        return StringUtils.isEmpty(key) ? null : template.opsForValue().get(key, (long) start, (long) end);
    }

    private static boolean zadd(String key, double score, String member) {
        return StringUtils.isEmpty(key) ? false : template.opsForZSet().add(key, member, score);
    }

    private static Long zcard(String key) {
        return StringUtils.isEmpty(key) ? 0L : template.opsForZSet().size(key);
    }

    private static Long zcount(String key, double min, double max) {
        return StringUtils.isEmpty(key) ? 0L : template.opsForZSet().count(key, min, max);
    }

    private static Double zincrby(String key, double score, String member) {
        return StringUtils.isEmpty(key) ? null : template.opsForZSet().incrementScore(key, member, score);
    }

    private static Set<String> zrange(String key, int start, int end) {
        return StringUtils.isEmpty(key) ? null : template.opsForZSet().range(key, (long) start, (long) end);
    }

    private static Set<String> zrangeByScore(String key, double min, double max) {
        return StringUtils.isEmpty(key) ? null : template.opsForZSet().rangeByScore(key, min, max);
    }

    private static Set<String> zrangeByScore(String key, double min, double max, int offset, int count) {
        return StringUtils.isEmpty(key) ? null : template.opsForZSet().rangeByScore(key, min, max, (long) offset, (long) count);
    }

    private static Long zrank(String key, String member) {
        return StringUtils.isEmpty(key) ? 0L : template.opsForZSet().rank(key, member);
    }

    private static Long zrem(String key, String member) {
        if (StringUtils.isEmpty(key)) {
            return 0L;
        } else {
            Object[] values = new Object[]{member};
            return template.opsForZSet().remove(key, values);
        }
    }

    private static Long zremrangeByRank(String key, int start, int end) {
        return StringUtils.isEmpty(key) ? 0L : template.opsForZSet().removeRange(key, (long) start, (long) end);
    }

    private static Long zremrangeByScore(String key, double start, double end) {
        return StringUtils.isEmpty(key) ? 0L : template.opsForZSet().removeRangeByScore(key, start, end);
    }

    private static Set<String> zrevrange(String key, int start, int end) {
        return StringUtils.isEmpty(key) ? null : template.opsForZSet().reverseRange(key, (long) start, (long) end);
    }

    private static Set<String> zrevrangeByScore(String key, double max, double min) {
        return StringUtils.isEmpty(key) ? null : template.opsForZSet().reverseRangeByScore(key, min, max);
    }

    private static Set<String> zrevrangeByScore(String key, double max, double min, int offset, int count) {
        return StringUtils.isEmpty(key) ? null : template.opsForZSet().reverseRangeByScore(key, min, max, (long) offset, (long) count);
    }

    private static Long zrevrank(String key, String member) {
        return StringUtils.isEmpty(key) ? null : template.opsForZSet().reverseRank(key, member);
    }

    private static Double zscore(String key, String member) {
        return StringUtils.isEmpty(key) ? null : template.opsForZSet().score(key, member);
    }

    private static void publish(String channel, String message) {
        template.convertAndSend(channel, message);
    }

    private static Set<String> keys(String pattern) {
        return template.keys(pattern);
    }

    private static String ping() {
        return template.execute((RedisCallback) RedisConnectionCommands::ping).toString();
    }
    
    private static <T> T eval(Class<T> resultClz, String script, List<String> keys, Object... args) {
        try {
            DefaultRedisScript<T> scriptCommand = new DefaultRedisScript<>();
            scriptCommand.setScriptText(script);
            scriptCommand.setResultType(resultClz);

             return template.execute(scriptCommand, keys, args);
        } catch (Exception e) {
            log.warn("eval failed", e);
            log.warn("eval failed, script:{}, keys:{}, args:{}", script, keys, Objects.isNull(args) ? "" : Arrays.asList(args));
        }

        return null;
    }
}
