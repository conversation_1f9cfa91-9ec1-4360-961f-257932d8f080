package cn.lyy.merchant.microservice;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.lyy_data_service_api.DaMerchantAnnualReportYiDTO;
import cn.lyy.lyy_data_service_api.LyyDistributorStatisticsDayDTO;
import com.lyy.charge.dto.response.Pagination;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class MerchantServiceFallbackFactory implements FallbackFactory<MerchantService> {

    @Override
    public MerchantService create(Throwable cause) {
        if (cause != null && StringUtils.isNotEmpty(cause.getMessage())) {
            log.error("fallback reason was:" + cause.getMessage(), cause);
        }
        return new MerchantService() {

            @Override
            public BaseResponse<Integer> countEquipmentByMerchantIdAndTypeValues(Long merchantId, List<String> typeValues) {
                return new BaseResponse<>(0);
            }
        };
    }
}