package cn.lyy.merchant.microservice;

import cn.lyy.base.dto.Pagination;
import cn.lyy.equipment_coins_api.LyyCoinsReportDTO;
import cn.lyy.merchant.api.fallback.MerchantEquipmentServiceFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "equipment-coins-service", fallbackFactory = MerchantEquipmentServiceFallbackFactory.class)
public interface IEquipmentCoinsClient {

    @GetMapping({"/coinsReport/page"})
    Pagination<LyyCoinsReportDTO> coinsReportPage(@RequestParam("pageIndex") Integer pageIndex, @RequestParam("pageSize") Integer pageSize, @RequestParam("begin") String begin, @RequestParam("end") String end, @RequestParam("equipmentUniqueCode") String equipmentUniqueCode);
}
