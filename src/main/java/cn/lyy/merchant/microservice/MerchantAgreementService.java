package cn.lyy.merchant.microservice;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.merchant.dto.AgreementBatchSignReqDTO;
import cn.lyy.merchant.dto.AgreementQueryReqDTO;
import cn.lyy.merchant.dto.LegalUserAgreementRespDTO;
import cn.lyy.merchant.dto.MerchantAgreementQueryDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

/**
 * <AUTHOR> =￣ω￣=
 * @date 2022/12/5
 */
@FeignClient(
        name = "legal-server",
        fallbackFactory = MerchantAgreementServiceFallbackFactory.class
)
public interface MerchantAgreementService {

    @PostMapping("/legal/agreement/batch/sign")
    BaseResponse<Void> batchSignAgreement(AgreementBatchSignReqDTO agreementBatchSignReqDTO);

    /**
     * 获取协议详情列表
     * @param reqDTO
     * @return
     */
    @PostMapping("/legal/agreement/info/get")
    BaseResponse<List<LegalUserAgreementRespDTO>> getUserAgreementInfo(MerchantAgreementQueryDTO reqDTO);
}
