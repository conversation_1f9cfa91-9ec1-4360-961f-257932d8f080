package cn.lyy.merchant.microservice;

import cn.lyy.base.dto.Pagination;
import cn.lyy.base.util.StringUtil;
import cn.lyy.equipment_coins_api.LyyCoinsReportDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @ClassName: EquipmentCoinsClientFallbackFactory
 * @description: TODO
 * @author: pengkun
 * @create: 2020-11-06 12:00
 * @Version 1.0
 **/
@Slf4j
@Component
public class EquipmentCoinsClientFallbackFactory implements FallbackFactory<IEquipmentCoinsClient> {
    @Override
    public IEquipmentCoinsClient create(Throwable cause) {
        if (cause != null && StringUtil.isNotEmpty(cause.getMessage())) {
            log.error("fallback reason was:" + cause.getMessage(), cause);
        }

        return new IEquipmentCoinsClient(){
            @Override
            public Pagination<LyyCoinsReportDTO> coinsReportPage(Integer pageIndex, Integer pageSize, String begin, String end, String equipmentUniqueCode) {
                log.error("coinsReportPage接口调用熔断");
                return new Pagination();
            }
        };
    }
}
