package cn.lyy.merchant.microservice;

import com.lyy.commodity.rpc.fallback.CommodityRelatedServiceFallbackFactory;
import com.lyy.commodity.rpc.feign.ICommodityRelatedService;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 类描述：
 * <p>
 *
 * <AUTHOR>
 * @since 2020/11/19 11:37
 */
@FeignClient(name = "commodity", fallbackFactory = CommodityRelatedServiceFallbackFactory.class)
public interface CommodityRelatedService extends ICommodityRelatedService {
}
