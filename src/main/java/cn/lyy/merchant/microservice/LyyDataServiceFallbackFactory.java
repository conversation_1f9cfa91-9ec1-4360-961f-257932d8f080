package cn.lyy.merchant.microservice;

import cn.lyy.lyy_data_service_api.DaMerchantAnnualReportYiDTO;
import cn.lyy.lyy_data_service_api.LyyDistributorStatisticsDayDTO;
import com.lyy.charge.dto.response.Pagination;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class LyyDataServiceFallbackFactory implements FallbackFactory<LyyDataService> {

    @Override
    public LyyDataService create(Throwable cause) {
        if (cause != null && StringUtils.isNotEmpty(cause.getMessage())) {
            log.error("fallback reason was:" + cause.getMessage(), cause);
        }
        return new LyyDataService() {
            @Override
            public List<LyyDistributorStatisticsDayDTO> getDistributorStatisticsDay(String distributorIds, String statisticsDate) {
                return new ArrayList<>();
            }

            @Override
            public DaMerchantAnnualReportYiDTO getByMerchantIdAndYear(String merchantId, String year) {
                return new DaMerchantAnnualReportYiDTO();
            }

            @Override
            public Pagination<DaMerchantAnnualReportYiDTO> listPageByYear(String year, BigDecimal excludeGmvAmount, int pageIndex, int pageSize) {
                return new Pagination<DaMerchantAnnualReportYiDTO>();
            }

            @Override
            public Integer count(String year, BigDecimal excludeGmvAmount) {
                return 0;
            }
        };
    }
}