package cn.lyy.merchant.microservice;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.merchant.dto.request.WasherGainStatusDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import cn.lyy.base.communal.bean.BaseResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <AUTHOR>
 * @className: WasherBusinessClient
 * @date 2021/2/22
 */
@FeignClient(name = "washer-business", fallbackFactory = WasherBusinessClientFallbackFactory.class)
public interface WasherBusinessClient {

    /**
     * 清除设备的故障状态
     * @param equipmentValue
     * @return
     */
    @GetMapping("/washer/customer/equipment/clearErrorStatus")
    BaseResponse<Boolean> clearErrorStatus(@RequestParam(name = "equipmentValue") String equipmentValue);

    /**
     * 异步-清除设备的故障状态
     * @param dto
     * @return
     */
    @PostMapping("/washer/customer/equipment/clearErrorStatusAsync")
    BaseResponse<Boolean> clearErrorStatusAsync(@RequestBody WasherGainStatusDTO dto);
}
