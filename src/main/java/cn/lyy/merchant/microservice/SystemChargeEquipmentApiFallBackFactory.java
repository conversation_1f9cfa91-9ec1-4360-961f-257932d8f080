package cn.lyy.merchant.microservice;

import com.lyy.charge.dto.bill.SystemChargeEquipmentBillFlowDTO;
import com.lyy.charge.dto.bill.SystemChargePaymentAmountDTO;
import com.lyy.charge.dto.charge.*;
import com.lyy.charge.dto.equipment.EquipmentStatusChangeDTO;
import com.lyy.charge.dto.order.ChargeOrderDetailMonthGroupDTO;
import com.lyy.charge.dto.order.SystemChargeOrderDTO;
import com.lyy.charge.dto.order.SystemChargeOrderDetailDTO;
import com.lyy.charge.dto.order.SystemChargeOrderMonthStatisticsDTO;
import com.lyy.charge.dto.response.BaseResponse;
import com.lyy.charge.dto.response.Pagination;
import com.lyy.charge.enums.equipment.EquipmentStatusMsgTypeEnum;
import com.lyy.charge.param.charge.SystemChargeClauseRecordParam;
import com.lyy.charge.param.charge.SystemChargeListParam;
import com.lyy.platform.enums.response.ResponseEnum;
import com.lyy.starter.common.resp.RespBody;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class SystemChargeEquipmentApiFallBackFactory implements FallbackFactory<SystemChargeEquipmentApi> {
    @Override
    public SystemChargeEquipmentApi create(Throwable throwable) {
        log.error(throwable.getMessage(), throwable);
        return new SystemChargeEquipmentApi() {

            @Override
            public BaseResponse<List<SystemChargeClauseRecordDTO>> getByAdOrgIdOrAgentUserId(Long distributorId, Long agentUserId) {
                log.error("【熔断】(getByAdOrgIdOrAgentUserId)：{},{}", distributorId, agentUserId);
                return BaseResponse.businessFusing();
            }

            @Override
            public BaseResponse<Boolean> agreeClause(SystemChargeClauseRecordParam param) {
                log.error("【熔断】(agreeClause)：{}", param);
                return BaseResponse.businessFusing();
            }

            @Override
            public RespBody<Boolean> merchantChargePop(Long merchantId) {
                log.error("【熔断】(merchantChargePop)：{}", merchantId);
                return RespBody.fail(ResponseEnum.B_FUSING.getCode().toString(), ResponseEnum.B_FUSING.getMessage());
            }

            @Override
            public BaseResponse<List<SystemChargeDistributorInfoDTO>> distributorInfo(Long distributorId) {
                log.error("【熔断】(distributorInfo)：{}", distributorId);
                return BaseResponse.businessFusing();
            }

            @Override
            public BaseResponse<DistributorChargeEquipmentInfoDTO> waitChargeEquipmentStatistics(Long distributorId, String equipmentTypeValue) {
                log.error("【熔断】(waitChargeEquipmentStatistics)：{}, {}", distributorId, equipmentTypeValue);
                return BaseResponse.businessFusing();
            }

            @Override
            public BaseResponse<Pagination<DistributorChargeEquipmentStateDTO>> distributorEquipmentState(Long distributorId, String state, Integer pageIndex, Integer pageSize, String equipmentTypeValue) {
                log.error("【熔断】(distributorEquipmentState)：{},{},{},{},{}", distributorId, state, pageIndex, pageSize,equipmentTypeValue);
                return BaseResponse.businessFusing();
            }

            @Override
            public BaseResponse<DistributorChargeEquipmentStatisticsDTO> distributorEquipmentStatistics(Long distributorId, String equipmentTypeValue) {
                log.error("【熔断】(distributorEquipmentStatistics)：{}, {}", distributorId, equipmentTypeValue);
                return BaseResponse.businessFusing();
            }

            @Override
            public BaseResponse<List<SystemChargeOrderMonthStatisticsDTO>> systemChargeOrderMonthStatistics(Long distributorId, String equipmentTypeValue) {
                log.error("【熔断】(systemChargeOrderMonthStatistics)：{}, {}", distributorId, equipmentTypeValue);
                return BaseResponse.businessFusing();
            }

            @Override
            public BaseResponse<Pagination<SystemChargeOrderDTO>> listSystemChargeOrder(SystemChargeListParam param) {
                log.error("【熔断】(listSystemChargeOrder)：{}", param);
                return BaseResponse.businessFusing();
            }

            @Override
            public BaseResponse<Pagination<ChargeOrderDetailMonthGroupDTO>> listSystemChargeOrderDetail(SystemChargeListParam param) {
                log.error("【熔断】(listSystemChargeOrderDetail)：{}", param);
                return BaseResponse.businessFusing();
            }
            /**
             * 发送设备状态变更消息具体看-->{@link EquipmentStatusMsgTypeEnum}
             * @param statusChangeDTO
             * @return
             */
            @Override
            public BaseResponse sendEquipmentChangeMsg(EquipmentStatusChangeDTO statusChangeDTO) {
                log.error("【熔断】(sendEquipmentChangeMsg)：{}", statusChangeDTO);
                return BaseResponse.businessFusing();
            }

            @Override
            public BaseResponse<SystemChargeEquipmentBillFlowDTO> findByOutTradeNo(String outTradeNo) {
                log.error("【熔断】(findByOutTradeNo)：{}", outTradeNo);
                return BaseResponse.businessFusing();
            }

            @Override
            public BaseResponse<Long> distributorChargeTotalAmount(Long distributorId, String chargeMonth, String chargeType) {
                log.error("【熔断】(distributorChargeTotalAmount)：{},{},{}", distributorId, chargeMonth, chargeType);
                return BaseResponse.businessFusing();
            }

            @Override
            public BaseResponse<Pagination<SystemChargeEquipmentBillFlowDTO>> distributorChargeBillFlow(Long distributorId, Integer equipmentTypeId, String chargeDate, Integer pageIndex, Integer pageSize) {
                log.error("【熔断】(distributorChargeBillFlow)：{},{},{},{},{}", distributorId, equipmentTypeId, chargeDate, pageIndex, pageSize);
                return BaseResponse.businessFusing();
            }

            @Override
            public BaseResponse<List<String>> findChargeTypeValues() {
                log.error("【熔断】-->系统收费设备类型编号列表:findChargeTypeValues");
                return BaseResponse.businessFusing();
            }

            @Override
            public BaseResponse<Pagination<SystemChargeOrderDetailDTO>> listOrderDetail(SystemChargeListParam param) {
                log.error("【熔断】(listOrderDetail)：{}", param);
                return BaseResponse.businessFusing();
            }

            @Override
            public BaseResponse<List<SystemChargePaymentAmountDTO>> listDistributorChargeTotalAmount(Long distributorId, String chargeMonth, String chargeType) {
                log.error("【熔断】(listDistributorChargeTotalAmount)：{},{},{}", distributorId, chargeMonth, chargeType);
                return BaseResponse.businessFusing();
            }
        };
    }
}
