package cn.lyy.merchant.microservice;

import cn.lyy.lyy_data_service_api.iotcard.IotCardStatusDTO;
import cn.lyy.lyy_data_service_api.iotcard.QueryBatchIoTCardParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name = "lyy-data-service",fallbackFactory = DataServiceClientFallbackFactory.class)
public interface IDataServiceClient {

    /**
     * 批量查询流量卡开停状态
     * @param param
     * @return
     */
    @PostMapping("/iotCard/queryBatch")
    List<IotCardStatusDTO> queryBatchIoTCardStatus(@RequestBody QueryBatchIoTCardParam param);
}
