package cn.lyy.merchant.microservice;

import cn.lyy.lyy_data_service_api.DaMerchantAnnualReportYiDTO;
import cn.lyy.lyy_data_service_api.LyyDistributorStatisticsDayDTO;
import com.lyy.charge.dto.response.Pagination;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.util.List;

@FeignClient(
    name = "lyy-data-service",
    fallbackFactory = LyyDataServiceFallbackFactory.class
)
public interface LyyDataService {

    @GetMapping({"/distributor/statistics/day"})
    List<LyyDistributorStatisticsDayDTO> getDistributorStatisticsDay(@RequestParam("distributorIds") String distributorIds, @RequestParam("statisticsDate") String statisticsDate);

    @GetMapping("/report/annual/merchant")
    DaMerchantAnnualReportYiDTO getByMerchantIdAndYear(@RequestParam("merchantId") String merchantId, @RequestParam("year") String year);

    @GetMapping("/report/annual/merchant/page")
    Pagination<DaMerchantAnnualReportYiDTO> listPageByYear(@RequestParam("year") String year, @RequestParam("excludeGmvAmount") BigDecimal excludeGmvAmount,
                                                           @RequestParam("pageIndex") int pageIndex, @RequestParam("pageSize") int pageSize);

    @GetMapping("/report/annual/merchant/count")
    Integer count(@RequestParam("year") String year, @RequestParam("excludeGmvAmount") BigDecimal excludeGmvAmount);
}