package cn.lyy.merchant.microservice;

import com.lyy.charge.dto.bill.SystemChargeEquipmentBillFlowDTO;
import com.lyy.charge.dto.bill.SystemChargePaymentAmountDTO;
import com.lyy.charge.dto.charge.*;
import com.lyy.charge.dto.equipment.EquipmentStatusChangeDTO;
import com.lyy.charge.dto.order.ChargeOrderDetailMonthGroupDTO;
import com.lyy.charge.dto.order.SystemChargeOrderDTO;
import com.lyy.charge.dto.order.SystemChargeOrderDetailDTO;
import com.lyy.charge.dto.order.SystemChargeOrderMonthStatisticsDTO;
import com.lyy.charge.dto.response.BaseResponse;
import com.lyy.charge.dto.response.Pagination;
import com.lyy.charge.param.charge.SystemChargeClauseRecordParam;
import com.lyy.charge.param.charge.SystemChargeListParam;
import com.lyy.starter.common.resp.RespBody;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


@FeignClient(name = "commercial-charges-service", fallbackFactory = SystemChargeEquipmentApiFallBackFactory.class)
public interface SystemChargeEquipmentApi {
    /**
     * 根据商家ID/或者代理商id获取条款信息
     *
     * @param distributorId 商户ID
     * @param agentUserId   代理商ID
     * @return 条款信息
     */
    @GetMapping("/rest/charge/clauseRecord/getByDistributorId")
    BaseResponse<List<SystemChargeClauseRecordDTO>> getByAdOrgIdOrAgentUserId(@RequestParam(value = "distributorId", required = false) Long distributorId,
                                                                              @RequestParam(value = "agentUserId", required = false) Long agentUserId);

    /**
     * 商家条款处理
     *
     * @param param 参数信息
     * @return true or false
     */
    @PostMapping("/rest/charge/clauseRecord/agreeClause")
    BaseResponse<Boolean> agreeClause(@RequestBody SystemChargeClauseRecordParam param);

    /**
     * 催收弹窗
     *
     * @param merchantId 商家ID
     * @return 催收弹窗
     */
    @GetMapping("/rest/charge/equipment/merchant-charge-pop")
    RespBody<Boolean> merchantChargePop(@RequestParam("merchantId") Long merchantId);

    /**
     * 获取商家系统缴费信息
     *
     * @param distributorId 商家ID
     * @return 系统缴费信息
     */
    @GetMapping("/rest/charge/equipment/distributorInfo")
    BaseResponse<List<SystemChargeDistributorInfoDTO>> distributorInfo(@RequestParam("distributorId") Long distributorId);

    /**
     * 商家今年待缴费设备信息
     *
     * @param distributorId 商家ID
     * @return 商家今年待缴费设备信息
     */
    @GetMapping("/rest/charge/equipment/waitChargeEquipmentStatistics")
    BaseResponse<DistributorChargeEquipmentInfoDTO> waitChargeEquipmentStatistics(@RequestParam("distributorId") Long distributorId,
                                                                                  @RequestParam(value = "equipmentTypeValue", required = false) String equipmentTypeValue);

    /**
     * 商家代缴费设备列表
     *
     * @param distributorId 商家ID
     * @param state         设备状态
     * @param pageIndex     pageIndex
     * @param pageSize      pageSize
     * @return 缴费设备列表
     */
    @GetMapping("/rest/charge/equipment/distributorEquipmentState")
    BaseResponse<Pagination<DistributorChargeEquipmentStateDTO>> distributorEquipmentState(
            @RequestParam("distributorId") Long distributorId,
            @RequestParam("state") String state,
            @RequestParam("pageIndex") Integer pageIndex,
            @RequestParam("pageSize") Integer pageSize,
            @RequestParam(value = "equipmentTypeValue", required = false) String equipmentTypeValue);

    /**
     * 商家缴费设备统计
     *
     * @param distributorId 商家ID
     * @return 缴费设备统计
     */
    @GetMapping("/rest/charge/equipment/distributorEquipmentStatistics")
    BaseResponse<DistributorChargeEquipmentStatisticsDTO> distributorEquipmentStatistics(@RequestParam("distributorId") Long distributorId,
                                                                                         @RequestParam(value = "equipmentTypeValue", required = false) String equipmentTypeValue);

    /**
     * 缴费账单月统计
     *
     * @param distributorId 商户ID
     * @return 缴费账单月统计
     */
    @GetMapping("/system/charge/order/monthStatistics")
    BaseResponse<List<SystemChargeOrderMonthStatisticsDTO>> systemChargeOrderMonthStatistics(@RequestParam("distributorId") Long distributorId,
                                                                                             @RequestParam(value = "equipmentTypeValue", required = false) String equipmentTypeValue);

    /**
     * 系统收费订单列表
     *
     * @param param 查询参数
     * @return SystemChargeOrderDTO
     */
    @PostMapping("/system/charge/order/listSystemChargeOrder")
    BaseResponse<Pagination<SystemChargeOrderDTO>> listSystemChargeOrder(@RequestBody SystemChargeListParam param);

    /**
     * 系统收费订单列表
     *
     * @param param 查询参数
     * @return SystemChargeOrderDetail
     */
    @PostMapping("/system/charge/orderDetail/listOrderDetail")
    BaseResponse<Pagination<ChargeOrderDetailMonthGroupDTO>> listSystemChargeOrderDetail(@RequestBody SystemChargeListParam param);

    /**
     * 查询商家订单金额
     *
     * @param distributorId 商家ID
     * @param chargeDate    缴费日期（yyyy-MM-dd）
     * @param chargeType    缴费类型
     * @return 合计缴费金额
     */
    @GetMapping("/system/charge/order/distributorChargeTotalAmount")
    BaseResponse<Long> distributorChargeTotalAmount(
            @RequestParam("distributorId") Long distributorId,
            @RequestParam(value = "chargeDate", required = false) String chargeDate,
            @RequestParam(value = "chargeType", required = false) String chargeType
    );

    /**
     * 查询商家订单金额
     *
     * @param distributorId 商家ID
     * @param chargeDate    缴费日期（yyyy-MM-dd）
     * @param chargeType    缴费类型
     * @return 合计缴费金额
     */
    @GetMapping("/system/charge/order/listDistributorChargeTotalAmount")
    BaseResponse<List<SystemChargePaymentAmountDTO>> listDistributorChargeTotalAmount(
            @RequestParam("distributorId") Long distributorId,
            @RequestParam(value = "chargeDate", required = false) String chargeDate,
            @RequestParam(value = "chargeType", required = false) String chargeType
    );

    /**
     * 查询商家自动缴费流水
     *
     * @param distributorId 商家ID
     * @param chargeDate    缴费日期（yyyy-MM-dd）
     * @param pageIndex     pageIndex
     * @param pageSize      pageSize
     * @return 自动扣费流水
     */
    @GetMapping("/rest/charge/equipment/bill/flow/distributorChargeBillFlow")
    BaseResponse<Pagination<SystemChargeEquipmentBillFlowDTO>> distributorChargeBillFlow(
            @RequestParam("distributorId") Long distributorId,
            @RequestParam("equipmentTypeId") Integer equipmentTypeId,
            @RequestParam("chargeDate") String chargeDate,
            @RequestParam("pageIndex") Integer pageIndex,
            @RequestParam("pageSize") Integer pageSize
    );

    /**
     * 发送设备状态变更消息具体看
     * @param statusChangeDTO
     * @return
     */
    @PostMapping(value = "/rest/charge/equipment/change/msg")
    BaseResponse sendEquipmentChangeMsg(@RequestBody EquipmentStatusChangeDTO statusChangeDTO);

    /**
     *根据支付单号查询一条流水
     * @param outTradeNo:支付单号
     * @CreateDate:    2021/1/15 11:11
     */
    @GetMapping("/rest/charge/equipment/bill/flow/findByOutTradeNo")
    BaseResponse<SystemChargeEquipmentBillFlowDTO> findByOutTradeNo(@RequestParam("outTradeNo") String outTradeNo);

    /**
     * 查询系统收费类型编号列表用于注册商家签协议进行判断
     * @return
     */
    @GetMapping("/system/charge/config/charge/type/values")
    public BaseResponse<List<String>> findChargeTypeValues();

    /**
     * 系统收费订单明细列表 支付成功之后的明细缴费设备
     * @param param 查询参数
     * @return SystemChargeOrderDetail
     */
    @PostMapping("/system/charge/orderDetail/listOrderDetail2Agent")
    BaseResponse<Pagination<SystemChargeOrderDetailDTO>> listOrderDetail(@RequestBody SystemChargeListParam param);

}
