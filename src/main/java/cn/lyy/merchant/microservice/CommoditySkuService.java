package cn.lyy.merchant.microservice;

import com.lyy.commodity.rpc.fallback.SkuServiceFallbackFactory;
import com.lyy.commodity.rpc.feign.ISkuService;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 类描述：
 * <p>
 *
 * <AUTHOR>
 * @since 2020/11/19 11:37
 */
@FeignClient(name = "commodity", fallbackFactory = SkuServiceFallbackFactory.class)
public interface CommoditySkuService extends ISkuService {
}
