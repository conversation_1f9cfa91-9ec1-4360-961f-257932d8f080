package cn.lyy.merchant.microservice;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.merchant.dto.AgreementBatchSignReqDTO;
import cn.lyy.merchant.dto.LegalUserAgreementRespDTO;
import cn.lyy.merchant.dto.MerchantAgreementQueryDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> =￣ω￣=
 * @date 2022/12/5
 */
@Slf4j
@Component
public class MerchantAgreementServiceFallbackFactory implements FallbackFactory<MerchantAgreementService> {

    @Override
    public MerchantAgreementService create(Throwable cause) {
        if (cause != null && StringUtils.isNotEmpty(cause.getMessage())) {
            log.error("fallback reason was:" + cause.getMessage(), cause);
        }
        return new MerchantAgreementService() {
            @Override
            public BaseResponse<Void> batchSignAgreement(AgreementBatchSignReqDTO agreementBatchSignReqDTO) {
                return null;
            }

            @Override
            public BaseResponse<List<LegalUserAgreementRespDTO>> getUserAgreementInfo(MerchantAgreementQueryDTO reqDTO) {
                return new BaseResponse();
            }
        };
    }
}
