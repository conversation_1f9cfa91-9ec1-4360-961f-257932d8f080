package cn.lyy.merchant.microservice;

import cn.lyy.base.communal.bean.BaseResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR> =￣ω￣=
 * @date 2022/12/9
 */
@FeignClient(
        name = "merchant-service",
        fallbackFactory = MerchantServiceFallbackFactory.class
)
public interface MerchantService {

    @PostMapping({"/rest/merchant/equipment/countEquipmentByMerchantIdAndTypeValues"})
    BaseResponse<Integer> countEquipmentByMerchantIdAndTypeValues(@RequestParam("merchantId") Long merchantId,
                                                                  @RequestBody List<String> typeValues);
}
