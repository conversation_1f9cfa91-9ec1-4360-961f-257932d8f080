package cn.lyy.merchant.microservice;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.redis.dto.request.RedisQueryRequestDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 类描述：
 * <p>
 *
 * <AUTHOR>
 * @since 2021/3/10 18:25
 */
@FeignClient(name = "customer-redis-service")
public interface CustomerRedisClient {

    @PostMapping({"/redis/operation/get"})
    BaseResponse<String> get(@RequestBody RedisQueryRequestDTO param);

}
