package cn.lyy.merchant.microservice;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.base.util.StringUtil;
import cn.lyy.websocket.dto.ClientRegisterDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @ClassName: WebSocketRegisterClientFallbackFactory
 * @description: TODO
 * @author: pengkun
 * @create: 2020-11-12 09:18
 * @Version 1.0
 **/
@Slf4j
@Component
public class WebSocketRegisterClientFallbackFactory implements FallbackFactory<IWebsocketRegisterClient> {
    @Override
    public IWebsocketRegisterClient create(Throwable cause) {
        if (cause != null && StringUtil.isNotEmpty(cause.getMessage())) {
            log.error("fallback reason was:" + cause.getMessage(), cause);
        }
        return new IWebsocketRegisterClient(){
            @Override
            public BaseResponse<String> generate(ClientRegisterDTO var1) {
                log.error("coinsReportPage接口调用熔断");
                BaseResponse<String> resp = new BaseResponse();
                resp.setCode(ResponseCodeEnum.FAIL.getCode());
                return resp;
            }
        };
    }
}
