package cn.lyy.merchant.microservice;

import cn.lyy.base.util.StringUtil;
import cn.lyy.lyy_data_service_api.iotcard.IotCardStatusDTO;
import cn.lyy.lyy_data_service_api.iotcard.QueryBatchIoTCardParam;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName: DataServiceClientFallbackFactory
 * @description: TODO
 * @author: pengkun
 * @create: 2020-11-10 18:03
 * @Version 1.0
 **/
@Slf4j
@Component
public class DataServiceClientFallbackFactory implements FallbackFactory<IDataServiceClient> {
    @Override
    public IDataServiceClient create(Throwable cause) {
        if (cause != null && StringUtil.isNotEmpty(cause.getMessage())) {{
            log.error("fallback reason was:" + cause.getMessage(), cause);
        }}
        return new IDataServiceClient(){

            @Override
            public List<IotCardStatusDTO> queryBatchIoTCardStatus(QueryBatchIoTCardParam param) {
                log.error("调用queryBatchIoTCardStatus微服务接口异常");
                return new ArrayList<>();
            }
        };
    }

}
