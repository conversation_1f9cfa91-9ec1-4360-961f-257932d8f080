package cn.lyy.merchant.microservice;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.base.util.StringUtil;
import cn.lyy.merchant.dto.request.WasherGainStatusDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @className: WasherBusinessClientFallbackFactory
 * @date 2021/2/22
 */
@Slf4j
@Component
public class WasherBusinessClientFallbackFactory implements FallbackFactory<WasherBusinessClient> {
    @Override
    public WasherBusinessClient create(Throwable cause) {
        if (cause != null && StringUtil.isNotEmpty(cause.getMessage())) {
            log.error("fallback reason was:" + cause.getMessage(), cause);
        }
        return new WasherBusinessClient(){

            /**
             * 清除设备的故障状态
             *
             * @param equipmentValue
             * @return
             */
            @Override
            public BaseResponse<Boolean> clearErrorStatus(String equipmentValue) {

                log.error("获取未处理申诉记录appealCount接口调用熔断");
                BaseResponse<Boolean> resp = new BaseResponse();
                resp.setCode(ResponseCodeEnum.FAIL.getCode());
                return resp;
            }

            @Override
            public BaseResponse<Boolean> clearErrorStatusAsync(WasherGainStatusDTO dto) {
                log.error("异步清除设备的故障状态-调用熔断");
                BaseResponse<Boolean> resp = new BaseResponse();
                resp.setCode(ResponseCodeEnum.FAIL.getCode());
                return resp;
            }
        };
    }
}
