package cn.lyy.merchant.microservice;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.websocket.api.fallback.WebSocketRegisterFallbackFactory;
import cn.lyy.websocket.dto.ClientRegisterDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(
        name = "websocket-authority-center",
        fallbackFactory = WebSocketRegisterFallbackFactory.class
)
public interface IWebsocketRegisterClient {

    @PostMapping({"/token/generate"})
    BaseResponse<String> generate(@RequestBody ClientRegisterDTO var1);
}
