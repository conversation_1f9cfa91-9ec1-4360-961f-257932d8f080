package cn.lyy.merchant.microservice.charging;

import cn.lyy.merchant.microservice.charging.dto.ShowGaodeMarketDTO;
import cn.lyy.merchant.microservice.charging.dto.ShowGaodeMarketVO;
import com.lyy.starter.common.resp.RespBody;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@FeignClient(name = "carcharging-market-server", fallbackFactory = CarChargingMarketApiFallBackFactory.class)
public interface CarChargingMarketApi {

    @PostMapping("/api/mp/adv/gaode/show")
    RespBody<ShowGaodeMarketVO> showGaodeMarket(@RequestBody ShowGaodeMarketDTO dto);

}
