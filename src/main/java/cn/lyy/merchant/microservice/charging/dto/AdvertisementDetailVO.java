package cn.lyy.merchant.microservice.charging.dto;

import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR> <<EMAIL>>
 * @date 2022/12/09 18:26
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class AdvertisementDetailVO {

    /**
     * 主键Id,自增
     */
    private Long id;

    /**
     * 广告名称
     */
    private String name;

    /**
     * 广告位编码
     *
     * @see com.lyy.charging.car.common.consts.market.AdvertisementCodeEnum
     */
    private List<String> adCode;

    /**
     * 1 全局弹窗 2 常驻广告
     */
    private Integer adCodeType;

    /**
     * 排序
     */
    private Integer adSort;

    /**
     * 广告类型 1:不需要跳转 2:活动id .....
     *
     * @see com.lyy.charging.car.common.consts.market.AdvertisementTypeEnum
     */
    private Integer adType;

    /**
     * 广告类型对应的值 type=1，为空字符串， type=2，为活动id.........
     */
    private String adTypeValue;

    /**
     * 是否可用 1：正常  0：禁用
     */
    private Integer enabled;

    /**
     * 图片地址
     */
    private String imgUrl;

    /**
     * 备注
     */
    private String remark;

    /**
     * 显示规则 1：显示N次  2：按日显示   3:每次打开
     *
     * @see com.lyy.charging.car.common.consts.market.AdvertisementDisplayEnum
     */
    private Integer displayRule;

    /**
     * 显示次数
     */
    private Integer displayTimes;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 商户ID
     */
    private Long merchantId;
}
