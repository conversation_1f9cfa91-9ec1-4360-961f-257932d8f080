package cn.lyy.merchant.microservice.charging;

import cn.lyy.merchant.microservice.charging.dto.OrderRefundApplyPageReq;
import cn.lyy.merchant.microservice.charging.dto.OrderRefundApplyPageResp;
import com.lyy.starter.common.resp.RespBody;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@FeignClient(name = "carcharging-order-server", fallbackFactory = CarChargingOrderApiFallBackFactory.class)
public interface CarChargingOrderApi {

    /**
     * 汽充桩退款申请订单列表
     */
    @PostMapping("/api/mw/orderRefundApply/page")
    RespBody<OrderRefundApplyPageResp> orderRefundApplyPage(@RequestBody OrderRefundApplyPageReq req);

}
