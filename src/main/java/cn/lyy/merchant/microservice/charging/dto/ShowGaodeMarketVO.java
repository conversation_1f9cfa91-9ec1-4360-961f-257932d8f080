package cn.lyy.merchant.microservice.charging.dto;

import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2025-01-01
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class ShowGaodeMarketVO {

    /**
     * 如果购买过，就是true，如果没，但在黑名单就是false
     */
    private Boolean show;

    private BigDecimal realPrice;

    private List<AdvertisementDetailVO> adList;

}
