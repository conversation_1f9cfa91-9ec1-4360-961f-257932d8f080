package cn.lyy.merchant.microservice.charging;

import cn.lyy.merchant.microservice.charging.dto.OrderRefundApplyPageReq;
import cn.lyy.merchant.microservice.charging.dto.OrderRefundApplyPageResp;
import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.starter.common.resp.RespBody;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class CarChargingOrderApiFallBackFactory implements FallbackFactory<CarChargingOrderApi> {
    @Override
    public CarChargingOrderApi create(Throwable throwable) {
        log.error(throwable.getMessage(), throwable);
        return new CarChargingOrderApi() {

            @Override
            public RespBody<OrderRefundApplyPageResp> orderRefundApplyPage(OrderRefundApplyPageReq req) {
                log.error("【熔断】carcharging-order-server(orderRefundApplyPage),{}", req);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

        };
    }
}
