package cn.lyy.merchant.microservice.charging;

import cn.lyy.merchant.microservice.charging.dto.ShowGaodeMarketDTO;
import cn.lyy.merchant.microservice.charging.dto.ShowGaodeMarketVO;
import com.lyy.starter.common.resp.RespBody;
import feign.hystrix.FallbackFactory;
import java.math.BigDecimal;
import java.util.ArrayList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class CarChargingMarketApiFallBackFactory implements FallbackFactory<CarChargingMarketApi> {

    @Override
    public CarChargingMarketApi create(Throwable throwable) {
        //log.error(throwable.getMessage(), throwable);
        return new CarChargingMarketApi() {

            @Override
            public RespBody<ShowGaodeMarketVO> showGaodeMarket(ShowGaodeMarketDTO req) {
                log.info("【熔断】carcharging-market-server showGaodeMarket 请求参数:{} 原因:{}", req, throwable.getMessage());
                return RespBody.ok(new ShowGaodeMarketVO(false, new BigDecimal(9999), new ArrayList<>()));
            }

        };
    }
}
