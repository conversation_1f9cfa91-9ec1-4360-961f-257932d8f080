package cn.lyy.merchant.billing;

import cn.lyy.merchant.billing.fallback.ConsumerSolutionClientFallback;
import com.github.pagehelper.PageInfo;
import com.lyy.billing.interfaces.consumer.dto.ConsumerSolutionDTO;
import com.lyy.billing.interfaces.consumer.dto.ConsumerSolutionDetailDTO;
import com.lyy.billing.interfaces.consumer.dto.EquipmentConsumerSolutionDTO;
import com.lyy.billing.interfaces.consumer.dto.request.AssociationEquipmentReqDTO;
import com.lyy.billing.interfaces.consumer.dto.request.ConsumerSolutionBillingCalculateDTO;
import com.lyy.billing.interfaces.consumer.dto.request.ConsumerSolutionBillingPayDTO;
import com.lyy.billing.interfaces.consumer.dto.request.ConsumerSolutionEquipmentSaveDTO;
import com.lyy.billing.interfaces.consumer.dto.request.ConsumerSolutionEquipmentUpdateDTO;
import com.lyy.billing.interfaces.consumer.dto.request.ConsumerSolutionInfoSaveDTO;
import com.lyy.billing.interfaces.consumer.dto.request.ConsumerSolutionInfoUpdateDTO;
import com.lyy.billing.interfaces.consumer.dto.request.EquipmentConsumerSolutionReqDTO;
import com.lyy.starter.common.resp.RespBody;
import java.math.BigDecimal;
import java.util.List;
import javax.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2021/03/09
 */
@FeignClient(name = "billing-system", fallbackFactory = ConsumerSolutionClientFallback.class)
public interface ConsumerSolutionClient {
    /**
     * 新增消费方案
     * @param saveDTO
     * @return
     */
    @PostMapping("/consumer/solution/save")
    RespBody<Long> save(@RequestBody ConsumerSolutionInfoSaveDTO saveDTO, @RequestHeader("merchantId") Long merchantId);

    /**
     * 查询消费方案列表
     * @param merchantId  商户id
     * @param category  类别 1 所有 2 门店区域 3 指定门店
     * @param store  门店
     * @param region 区域
     * @param isCount  是否需要统计数量
     * @return
     */
    @GetMapping("/consumer/solution/list")
    RespBody<List<ConsumerSolutionDTO>> findConsumerSolutionList(
        @RequestParam("merchantId") Long merchantId,
        @RequestParam("category") Integer category,
        @RequestParam(value = "store",required = false) Long store,
        @RequestParam(value = "region",required = false) Long region,
        @RequestParam(value = "name",required = false) String name,
        @RequestParam(value = "equipmentTypeId",required = false) Long equipmentTypeId,
        @RequestParam(value = "ids",required = false) List<Long> ids,
        @RequestParam(value = "associationType",required = false) Integer associationType,
        @RequestParam("isCount") Boolean isCount,
        @RequestParam(value = "showCommodity", required = false) Boolean showCommodity,
        @RequestParam(value = "stores",required = false) List<Long> stores,
        @RequestHeader("merchantId") Long orgId);

    /**
     * 分页查询消费方案列表
     */
    @GetMapping("/consumer/solution/page")
    RespBody<PageInfo<ConsumerSolutionDTO>> findConsumerSolutionPage(
        @RequestParam("merchantId") Long merchantId,
        @RequestParam("category") Integer category,
        @RequestParam(value = "store", required = false) Long store,
        @RequestParam(value = "region", required = false) Long region,
        @RequestParam("isCount") Boolean isCount,
        @RequestParam(value = "name",required = false) String name,
        @RequestParam(value = "pageIndex") Integer pageIndex,
        @RequestParam(value = "pageSize") Integer pageSize,
        @RequestHeader("merchantId") Long orgId);

    /**
     * 设置设备消费方案
     * @param saveDTO
     * @return
     */
    @PostMapping("/consumer/solution/equipment")
    RespBody<Boolean> saveEquipmentConsumerSolution(@RequestBody ConsumerSolutionEquipmentSaveDTO saveDTO, @RequestHeader("merchantId") Long merchantId);

    /**
     * 调整消费方案
     * @param updateDTO
     * @return
     */
    @PostMapping("/consumer/solution/edit")
    RespBody<Boolean> edit(@RequestBody ConsumerSolutionInfoUpdateDTO updateDTO, @RequestHeader("merchantId") Long merchantId);

    /**
     * 修改设备消费方案
     * @param updateDTO
     * @return
     */
    @PostMapping("/consumer/solution/equipment/edit")
    RespBody<Boolean> editEquipmentConsumerSolution(@RequestBody ConsumerSolutionEquipmentUpdateDTO updateDTO, @RequestHeader("merchantId") Long merchantId);

    /**
     * 查询设备消费方案列表
     * @param deviceIds  设备ids
     * @param category  类型 1 刷卡消费方案 2 扫码消费方案
     * @return
     */
    @GetMapping("/consumer/solution/equipment/list")
    RespBody<List<EquipmentConsumerSolutionDTO>> findEquipmentConsumerSolutionList(
        @RequestParam(value = "type", required = false, defaultValue = "1") Integer type,
        @RequestParam("deviceIds") List<Long> deviceIds,
        @RequestParam(value = "category",required = false) Integer category,
        @RequestHeader("merchantId") Long merchantId);

    /**
     * 删除消费方案
     * @param id
     * @return
     */
    @DeleteMapping("/consumer/solution/{id}")
    RespBody<Boolean> deleteConsumerSolution(@PathVariable("id") Long id, @RequestHeader("merchantId") Long merchantId);

    @GetMapping("/consumer/solution/{id}")
    RespBody<ConsumerSolutionDetailDTO> getConsumerSolution(@PathVariable("id") Long id, @RequestHeader("merchantId") Long merchantId);

    /**
     * 批量改设备消费方案
     * @param updateList
     * @return
     */
    @PostMapping("/consumer/solution/equipment/batch/edit")
    RespBody<Boolean> batchEditEquipmentConsumerSolution(@RequestBody List<ConsumerSolutionEquipmentUpdateDTO> updateList, @RequestHeader("merchantId") Long merchantId);

    /**
     * 批量设置设备消费方案
     * @param saveList
     * @return
     */
    @PostMapping("/consumer/solution/equipment/batch")
    RespBody<Boolean> batchSaveEquipmentConsumerSolution(@RequestBody List<ConsumerSolutionEquipmentSaveDTO> saveList, @RequestHeader("merchantId") Long merchantId);

    /**
     * 获取关联消费方案设备列表
     *
     * @param solutionIds 消费方案
     */
    @GetMapping("/consumer/solution/findDeviceIdsBySolution")
    RespBody<List<Long>> findDeviceIdsBySolution(
        @RequestParam(value = "type",required = false,defaultValue = "1") Integer type,
        @RequestParam("solutionIds") List<Long> solutionIds,
        @RequestHeader("merchantId") Long merchantId);

    /**
     * 删除设备关联消费方案
     */
    @DeleteMapping("/equipment/batch/delete")
    RespBody<Boolean> batchDeleteConsumerSolutionEquipment(
        @RequestParam(value = "type", required = false, defaultValue = "1") Integer type,
        @RequestParam("deviceIds") List<Long> deviceIds,
        @RequestParam(value = "category", required = false, defaultValue = "1") Integer category,
        @RequestHeader("merchantId") Long merchantId);

    @PostMapping("/consumer/solution/billing/pay")
    RespBody<Boolean> billingPay(@Valid @RequestBody ConsumerSolutionBillingPayDTO billingPay, @RequestHeader("merchantId") Long merchantId);

    @PostMapping("/consumer/solution/billing/calculate")
    RespBody<BigDecimal> billingCalculate(@Valid @RequestBody ConsumerSolutionBillingCalculateDTO billingCalculate, @RequestHeader("merchantId") Long merchantId);

    @GetMapping("/consumer/solution/detail")
    RespBody<ConsumerSolutionDetailDTO> detail(@RequestParam("id") Long id, @RequestParam("category") Integer category,
        @RequestParam(value = "type",required = false,defaultValue = "1") Integer type,
        @RequestHeader("merchantId") Long merchantId);

    @PostMapping("/consumer/solution/equipment/association")
    RespBody<Boolean> associationEquipments(@RequestBody AssociationEquipmentReqDTO associationEquipmentReqDTO, @RequestHeader("merchantId") Long merchantId);

    @PostMapping("/consumer/solution/list/equipment")
    RespBody<List<EquipmentConsumerSolutionDTO>> findConsumerSolutionEquipmentList(@RequestBody EquipmentConsumerSolutionReqDTO request,@RequestHeader("merchantId") Long merchantId);
}
