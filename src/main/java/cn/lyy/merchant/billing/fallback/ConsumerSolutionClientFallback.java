package cn.lyy.merchant.billing.fallback;

import cn.lyy.merchant.billing.ConsumerSolutionClient;
import com.github.pagehelper.PageInfo;
import com.lyy.billing.interfaces.consumer.dto.ConsumerSolutionDTO;
import com.lyy.billing.interfaces.consumer.dto.ConsumerSolutionDetailDTO;
import com.lyy.billing.interfaces.consumer.dto.EquipmentConsumerSolutionDTO;
import com.lyy.billing.interfaces.consumer.dto.request.AssociationEquipmentReqDTO;
import com.lyy.billing.interfaces.consumer.dto.request.ConsumerSolutionBillingCalculateDTO;
import com.lyy.billing.interfaces.consumer.dto.request.ConsumerSolutionBillingPayDTO;
import com.lyy.billing.interfaces.consumer.dto.request.ConsumerSolutionEquipmentSaveDTO;
import com.lyy.billing.interfaces.consumer.dto.request.ConsumerSolutionEquipmentUpdateDTO;
import com.lyy.billing.interfaces.consumer.dto.request.ConsumerSolutionInfoSaveDTO;
import com.lyy.billing.interfaces.consumer.dto.request.ConsumerSolutionInfoUpdateDTO;
import com.lyy.billing.interfaces.consumer.dto.request.EquipmentConsumerSolutionReqDTO;
import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.starter.common.resp.RespBody;
import feign.hystrix.FallbackFactory;
import java.math.BigDecimal;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021/03/09
 */
@Component
@Slf4j
public class ConsumerSolutionClientFallback implements FallbackFactory<ConsumerSolutionClient> {

    @Override
    public ConsumerSolutionClient create(Throwable throwable) {
        if (throwable != null && StringUtils.isNotEmpty(throwable.getMessage())) {
            log.error("fallback reason was:" + throwable.getMessage(), throwable);
        }
        return new ConsumerSolutionClient() {

            @Override
            public RespBody<Long> save(ConsumerSolutionInfoSaveDTO saveDTO, Long merchantId) {
                log.error("billing-system 接口熔断===> 新增消费方案,saveDTO:{}", saveDTO);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<List<ConsumerSolutionDTO>> findConsumerSolutionList(Long merchantId, Integer category, Long store, Long region,
                String name, Long equipmentTypeId, List<Long> ids, Integer associationType, Boolean isCount, Boolean showCommodity, List<Long> stores, Long orgId) {
                log.error("billing-system 接口熔断===> 查询消费方案列表,merchantId:{},category:{},store:{},region:{},name:{},equipmentTypeId:{},ids:{},associationType:{},isCount:{},showCommodity:{},stores:{}",
                    merchantId, category, store, region, name, equipmentTypeId, ids, associationType, isCount, showCommodity, stores);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<PageInfo<ConsumerSolutionDTO>> findConsumerSolutionPage(Long merchantId, Integer category, Long store,
                Long region, Boolean isCount, String name, Integer pageIndex, Integer pageSize, Long orgId) {
                log.error("billing-system 接口熔断===> 分页查询消费方案列表,merchantId:{},category:{},store:{},region:{},isCount:{},name:{},pageIndex:{},pageSize:{}", merchantId,
                    category, store, region, isCount, name, pageIndex, pageSize);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Boolean> saveEquipmentConsumerSolution(ConsumerSolutionEquipmentSaveDTO saveDTO, Long merchantId) {
                log.error("billing-system 接口熔断===> 设置设备消费方案,saveDTO:{}", saveDTO);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Boolean> edit(ConsumerSolutionInfoUpdateDTO updateDTO, Long merchantId) {
                log.error("billing-system 接口熔断===> 编辑消费方案,updateDTO:{}", updateDTO);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Boolean> editEquipmentConsumerSolution(ConsumerSolutionEquipmentUpdateDTO updateDTO, Long merchantId) {
                log.error("billing-system 接口熔断===> 修改设备消费方案,updateDTO:{}", updateDTO);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<List<EquipmentConsumerSolutionDTO>> findEquipmentConsumerSolutionList(Integer type, List<Long> deviceIds, Integer category, Long merchantId) {
                log.error("billing-system 接口熔断===> 查询设备消费方案列表,type:{},deviceIds:{},category:{}", type, deviceIds, category);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Boolean> deleteConsumerSolution(Long id, Long merchantId) {
                log.error("billing-system 接口熔断===> 删除消费方案,id:{}", id);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<ConsumerSolutionDetailDTO> getConsumerSolution(Long id, Long merchantId) {
                log.error("billing-system 接口熔断===> 获取消费方案详情,id:{}", id);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Boolean> batchEditEquipmentConsumerSolution(List<ConsumerSolutionEquipmentUpdateDTO> updateList, Long merchantId) {
                log.error("billing-system 接口熔断===> 批量修改设备消费方案,updateList:{}", updateList);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Boolean> batchSaveEquipmentConsumerSolution(List<ConsumerSolutionEquipmentSaveDTO> saveList, Long merchantId) {
                log.error("billing-system 接口熔断===> 批量设置设备消费方案,saveList:{}", saveList);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<List<Long>> findDeviceIdsBySolution(Integer type, List<Long> solutionIds, Long merchantId) {
                log.error("billing-system 接口熔断===> 获取关联消费方案设备列表,type:{},solutionIds:{}", type,solutionIds);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Boolean> batchDeleteConsumerSolutionEquipment(Integer type, List<Long> deviceIds, Integer category, Long merchantId) {
                log.error("billing-system 接口熔断===> 删除设备关联消费方案,type:{},deviceIds:{}", type,deviceIds);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Boolean> billingPay(ConsumerSolutionBillingPayDTO billingPay, Long merchantId) {
                log.error("billing-system 接口熔断===> 设备消费方案计费支付,billingPay:{}",billingPay);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<BigDecimal> billingCalculate(ConsumerSolutionBillingCalculateDTO billingCalculate, Long merchantId) {
                log.error("billing-system 接口熔断===> 设备消费方案计费计算,billingCalculate:{}",billingCalculate);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<ConsumerSolutionDetailDTO> detail(Long id, Integer category, Integer type, Long merchantId) {
                log.error("billing-system 接口熔断===> 消费方案详情,id:{},category:{},type:{}", id, category, type);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Boolean> associationEquipments(AssociationEquipmentReqDTO associationEquipmentReqDTO, Long merchantId) {
                log.error("billing-system 接口熔断===> 消费方案关联设备,associationEquipmentReqDTO:{}", associationEquipmentReqDTO);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<List<EquipmentConsumerSolutionDTO>> findConsumerSolutionEquipmentList(EquipmentConsumerSolutionReqDTO request, Long merchantId) {
                log.error("billing-system 接口熔断===> 获取设备消费方案列表,request:{}", request);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }
        };
    }
}
