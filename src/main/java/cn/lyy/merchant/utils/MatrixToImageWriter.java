package cn.lyy.merchant.utils;

import com.google.zxing.common.BitMatrix;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;


public class MatrixToImageWriter {
    private static final int BLACK = 0xFF000000;
    private static final int WHITE = 0xFFFFFFFF;
    private static final int TITLE_HEIGHT = 32;
    private MatrixToImageWriter() {
    }

    public static BufferedImage toBufferedImage(BitMatrix matrix){
        return MatrixToImageWriter.toBufferedImage(matrix , null);
    }

    public static BufferedImage toBufferedImage(BitMatrix matrix , String qrCodeTitle) {
        int width = matrix.getWidth();
        int height = matrix.getHeight();
        BufferedImage image = new BufferedImage(width, height+TITLE_HEIGHT, BufferedImage.TYPE_INT_RGB);
        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height+TITLE_HEIGHT; y++) {
                if(y < height)
                    image.setRGB(x, y, matrix.get(x, y) ? BLACK : WHITE);
                else
                    image.setRGB(x, y, WHITE);

            }
        }

        if(qrCodeTitle != null) {
            try {
                pressText(qrCodeTitle , image);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return image;
    }

    public static void writeToFile(BitMatrix matrix, String format, File file) throws IOException {
        BufferedImage image = toBufferedImage(matrix);
        if (!ImageIO.write(image, format, file)) {
            throw new IOException("Could not write an image of format " + format + " to " + file);
        }
    }

    public static void writeToStream(BitMatrix matrix, String format, OutputStream stream) throws IOException {
        BufferedImage image = toBufferedImage(matrix);
        if (!ImageIO.write(image, format, stream)) {
            throw new IOException("Could not write an image of format " + format);
        }
    }
    /**
     * 给二维码图片加上文字
     */
    public static void pressText(String pressText, BufferedImage image) throws IOException {
        pressText = new String(pressText.getBytes(), "utf-8");
        Graphics g = image.createGraphics();
        g.setColor(Color.BLACK);
        Font font = new Font(null, Font.BOLD, TITLE_HEIGHT);
        FontMetrics metrics = g.getFontMetrics(font);

        // 文字在图片中的坐标 这里设置在中间
        int startX = (image.getWidth() - metrics.stringWidth(pressText)) / 2;
        int startY = image.getHeight()-5;
        g.setFont(font);
        g.drawString(pressText, startX, startY);
        g.dispose();
    }

}