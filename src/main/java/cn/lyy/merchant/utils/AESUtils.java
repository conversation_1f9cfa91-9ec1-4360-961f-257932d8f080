package cn.lyy.merchant.utils;

import lombok.extern.slf4j.Slf4j;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.security.MessageDigest;

/**
 * <p>Title:saas2</p>
 * <p>Desc: AES加密工具</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020/11/16
 */
@Slf4j
public class AESUtils {
    /**
     * 密钥算法
     */
    private static final String ALGORITHM = "AES";
    /**
     * 加解密算法/工作模式/填充方式
     */
    private static final String ALGORITHM_MODE_PADDING = "AES/ECB/PKCS5Padding";

    /**
     * AES加密
     *
     * @param data
     * @return
     * @throws Exception
     */
    public static String encrypt(String data , String pwd)  {
        try {
            return encrypt(data.getBytes("UTF-8") , pwd);
        } catch (Exception e) {
            log.error("AES encrypt",e);
        }

        return null;

    }

    public static String encrypt(byte[] data, String pwd)  {
        try {
            SecretKeySpec keySpec = new SecretKeySpec(MD5(pwd).toLowerCase().getBytes(), ALGORITHM);

            // 创建密码器
            Cipher cipher = Cipher.getInstance(ALGORITHM_MODE_PADDING);
            // 初始化
            cipher.init(Cipher.ENCRYPT_MODE, keySpec);
            return new BASE64Encoder().encode(cipher.doFinal(data));

        } catch (Exception e) {
            log.error("AES encrypt",e);
        }
        return null;
    }

    /**
     * AES解密
     *
     * @param base64Data
     * @return
     * @throws Exception
     */
    public static String decrypt(String base64Data , String key){
        try{
            SecretKeySpec keySpec = new SecretKeySpec(MD5(key).toLowerCase().getBytes(), ALGORITHM);

            Cipher cipher = Cipher.getInstance(ALGORITHM_MODE_PADDING);
            cipher.init(Cipher.DECRYPT_MODE, keySpec);
            return new String(cipher.doFinal(new BASE64Decoder().decodeBuffer(base64Data)));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static byte[] decrypt2Byte(String base64Data , String key){
        try{
            SecretKeySpec keySpec = new SecretKeySpec(MD5(key).toLowerCase().getBytes(), ALGORITHM);

            Cipher cipher = Cipher.getInstance(ALGORITHM_MODE_PADDING);
            cipher.init(Cipher.DECRYPT_MODE, keySpec);
            return cipher.doFinal(new BASE64Decoder().decodeBuffer(base64Data));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 生成MD5
     * @param data  待处理数据
     * @return  加密结果
     * @throws Exception
     */
    public static final String MD5(String data) throws Exception {
        MessageDigest md = MessageDigest.getInstance("MD5");
        byte[] array = md.digest(data.getBytes("UTF-8"));
        StringBuilder sb = new StringBuilder();
        for (byte item : array) {
            sb.append(Integer.toHexString((item & 0xFF) | 0x100).substring(1,3));
        }
        return sb.toString().toUpperCase();
    }
}
