package cn.lyy.merchant.utils;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.util.Objects;
import java.util.Set;

/**
 * <p>Title:saas2</p>
 * <p>Desc: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020/11/14
 */
public class ValidatorUtils {

    private static final Validator VALIDATOR;

    static {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        VALIDATOR = factory.getValidator();
    }

    /**
     * 校验DTO入参，若出错则返回出错信息
     * @param ts
     * @param <T>
     * @return
     */
    public static <T> String validDTO(T... ts) {
        for (T t : ts) {
            Set<ConstraintViolation<T>> validate = VALIDATOR.validate(t);
            if(!validate.isEmpty()){
                ConstraintViolation<T> next = validate.iterator().next();
                return next.getMessage();
            }
        }
        return null;
    }

    /**
     * 校验字符串是否为空
     * @param objs
     * @return
     */
    public static boolean validEmpty(Object... objs){
        for (Object obj : objs) {
            if(Objects.isNull(obj))
                return true;
        }
        return false;
    }
}
