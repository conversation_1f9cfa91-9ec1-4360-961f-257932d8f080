package cn.lyy.merchant.utils;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.HashMap;
import java.util.Map;


@Slf4j
public class QrcodeUtil {
    public static boolean write(String content , File file){
        return QrcodeUtil.write(content,null,file);
    }
    public static boolean write(String content , String qrCodeTitle , File file) {
        try {
            return  write(content, qrCodeTitle , new FileOutputStream(file));
        } catch (FileNotFoundException e) {
            e.printStackTrace();
            log.error("write out qrcode error" , e);
        }
        return Boolean.FALSE;
    }
    public static boolean write(String content, String qrCodeTitle , OutputStream outputStream) {
        boolean success = Boolean.FALSE;
        if (StringUtils.isEmpty(content))
            return success;

        //log.debug("qrcode write out , content: {}" , content);

        MultiFormatWriter multiFormatWriter = new MultiFormatWriter();
        Map hints = new HashMap();
        //设置字符集编码类型
        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
        //设置二维码四周的白色边框 ,默认是4,默认为4的时候白色边框实在是太粗了
        hints.put(EncodeHintType.MARGIN, 0);
        BitMatrix bitMatrix = null;
        try {
            bitMatrix = multiFormatWriter.encode(content, BarcodeFormat.QR_CODE, 300, 300, hints);
            BufferedImage image = MatrixToImageWriter.toBufferedImage(bitMatrix , qrCodeTitle);
            //输出二维码图片流
            try {
                ImageIO.write(image, "png", outputStream);
                outputStream.flush();
                outputStream.close();
                success = Boolean.TRUE;
            } catch (IOException e) {
                log.error("write out qrcode error" , e);
            }
        } catch (Exception e1) {
            log.error("write out qrcode error" , e1);
        }

        return success;
    }
}
