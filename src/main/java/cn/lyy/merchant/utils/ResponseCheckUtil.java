package cn.lyy.merchant.utils;

import static java.util.Optional.ofNullable;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.merchant.exception.BusinessException;
import com.lyy.equipment.infrastructure.resp.RespBody;
import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.merchant.bff.base.ResponseBody;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

/**
 *
 * <AUTHOR>
 * @date 2022/3/5
 **/
@Slf4j
public class ResponseCheckUtil {
    private ResponseCheckUtil() {
    }

    private static final String ERROR_MESSAGE = "rpc调用错误:{}";

    public static <T> T getData(RespBody<T> respBody) {
        if(log.isDebugEnabled()){
            log.debug("远程调用结果 respBody:{}",respBody);
        }
        boolean result = GlobalErrorCode.OK.getCode().equals(respBody.getCode());
        if (!result) {
            log.error(ERROR_MESSAGE, respBody);
            throw new BusinessException(respBody.getMessage());
        }
        return respBody.getBody();
    }

    public static <T> T getData(com.lyy.starter.common.resp.RespBody<T> respBody) {
        if(log.isDebugEnabled()){
            log.debug("远程调用结果 respBody:{}",respBody);
        }
        boolean result = GlobalErrorCode.OK.getCode().equals(respBody.getCode());
        if (!result) {
            log.error(ERROR_MESSAGE, respBody);
            throw new BusinessException(respBody.getMessage());
        }
        return respBody.getBody();
    }

    public static <T> T getData(com.lyy.user.account.infrastructure.resp.RespBody<T> response, T other) {
        return ofNullable(response)
                .filter(r -> {
                    boolean success = StringUtils.equals(GlobalErrorCode.OK.getCode(), r.getCode());
                    if (!success) {
                        log.warn("请求失败 response: {}", response);
                    }
                    log.debug("请求用户信息：{}", r);
                    return success;
                })
                .map(com.lyy.user.account.infrastructure.resp.RespBody::getBody)
                .orElse(other);
    }


    public static <T> T getData(BaseResponse<T> baseResponse) {
        if(log.isDebugEnabled()){
            log.debug("远程调用结果 baseResponse:{}",baseResponse);
        }
        boolean result = ResponseCodeEnum.SUCCESS.getCode()==baseResponse.getCode();
        if (!result) {
            log.error(ERROR_MESSAGE, baseResponse);
            throw new BusinessException(baseResponse.getMessage());
        }
        return baseResponse.getData();
    }

    public static <T> T getData(ResponseBody<T> responseBody) {
        if (log.isDebugEnabled()) {
            log.debug("远程调用结果 responseBody:{}", responseBody);
        }
        boolean result = GlobalErrorCode.OK.getCode().equals(responseBody.getCode());
        if (!result) {
            log.error(ERROR_MESSAGE, responseBody);
            throw new BusinessException(responseBody.getMessage());
        }
        return responseBody.getBody();
    }

    public static <T> T getDataSkipError(BaseResponse<T> baseResponse) {
        if (log.isDebugEnabled()) {
            log.debug("远程调用结果 baseResponse:{}", baseResponse);
        }

        return ofNullable(baseResponse.getCode())
                .filter(code -> Objects.equals(ResponseCodeEnum.SUCCESS.getCode(), code))
                .map(code -> baseResponse.getData())
                .orElse(null);
    }

}
