package cn.lyy.merchant.utils;

import cn.lyy.merchant.exception.BusinessException;
import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitAdjustDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountQueryDTO;
import com.lyy.user.account.infrastructure.account.feign.AccountFeignClient;
import com.lyy.user.account.infrastructure.constant.AccountRecordTypeEnum;
import com.lyy.user.account.infrastructure.constant.AdjustTypeEnum;
import com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum;
import com.lyy.user.account.infrastructure.constant.BenefitClassifyGroupEnum;
import com.lyy.user.account.infrastructure.resp.RespBody;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static java.util.Optional.ofNullable;

/**
 * @ClassName: UserMemberUtils
 * @description: 新会员工具类
 * @author: pengkun
 * @date: 2021/12/01
 **/
@Slf4j
@Component("merchantUserMemberUtils")
public class UserMemberUtils {
    @Autowired
    private AccountFeignClient accountFeignClient;

    /**
     * 用户权益处理
     *
     * @param merchantId            商户id
     * @param userId                用户id
     * @param operator              操作人
     * @param adjustTypeEnum        操作类型枚举
     *                              INCREMENT 加
     *                              DECREMENT 减
     * @param benefitClassifyEnum   权益类型
     * @param accountRecordTypeEnum 消费记录类型
     * @param resource              来源
     * @param amount                金额
     * @return
     */
    public void handlerUserBenefit(Long merchantId, Long userId, Long operator,
                                   AdjustTypeEnum adjustTypeEnum, BenefitClassifyEnum benefitClassifyEnum,
                                   AccountRecordTypeEnum accountRecordTypeEnum, String resource, BigDecimal amount) {
        AccountBenefitAdjustDTO accountBenefitAdjustDTO = new AccountBenefitAdjustDTO();
        accountBenefitAdjustDTO.setMerchantId(merchantId);
        accountBenefitAdjustDTO.setUserId(userId);
        accountBenefitAdjustDTO.setOperator(operator);
        accountBenefitAdjustDTO.setAdjustType(adjustTypeEnum);
        accountBenefitAdjustDTO.setClassify(benefitClassifyEnum.getCode());
        accountBenefitAdjustDTO.setRecordType(accountRecordTypeEnum.getCode());
        accountBenefitAdjustDTO.setAmount(amount);
        accountBenefitAdjustDTO.setResource(resource);
        RespBody respBody = accountFeignClient.merchantAdjustBenefit(accountBenefitAdjustDTO);
        if (log.isDebugEnabled()) {
            log.debug("用户权益处理,请求参数:{},返回:{}", accountBenefitAdjustDTO, respBody);
        }
        if (!GlobalErrorCode.OK.getCode().equals(respBody.getCode())) {
            log.error("用户权益处理,请求参数:{},返回:{}", accountBenefitAdjustDTO, respBody);
            throw new BusinessException("-1", "赠送红包写权益失败");
        }
    }

    public Map<Integer, BigDecimal> getUserBenefitCount(Long merchantId, Long merchantUserId, Long userId,
                                                        Integer balanceType, List<Integer> excludeClassify,
                                                        List<Long> storeIds) {
        AccountQueryDTO param = new AccountQueryDTO();
        param.setMerchantId(merchantId);
        param.setMerchantUserId(merchantUserId);
        param.setUserId(userId);
        param.setStoreIds(storeIds);
        param.setBenefitClassify(BenefitClassifyGroupEnum.getClassifyByType(ofNullable(balanceType).orElse(2)));
        // 去除广告红包
        param.setExcludeClassify(ofNullable(excludeClassify).orElse(Collections.singletonList(BenefitClassifyEnum.ADVERT_RED_PACKET.getCode())));
        RespBody<Map<Integer, BigDecimal>> respBody = accountFeignClient.benefitCount(param);
        if (log.isDebugEnabled()) {
            log.debug("用户权益获取:{},{}", param, respBody);
        }
        Map<Integer, BigDecimal> map = null;
        if (GlobalErrorCode.OK.getCode().equals(respBody.getCode())) {
            map = respBody.getBody();
        } else {
            log.error("用户权益获取失败,请求参数:{},返回:{}", param, respBody);
        }
        return map;
    }
}
