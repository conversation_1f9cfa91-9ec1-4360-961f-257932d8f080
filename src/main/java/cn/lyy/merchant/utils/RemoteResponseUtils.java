package cn.lyy.merchant.utils;

import static java.util.Optional.ofNullable;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.merchant.constants.BusinessExceptionEnums;
import cn.lyy.merchant.exception.BusinessException;
import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.user.account.infrastructure.resp.RespBody;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

/**
 * 远程调用响应处理工具类

 */
@Slf4j
public class RemoteResponseUtils {

    /**
     * 检查响应结果是否正常
     */
    public static boolean checkResponse(RespBody respBody){
        return ofNullable(respBody)
                .map(RespBody::getCode)
                .map(code-> GlobalErrorCode.OK.getCode().equals(code))
                .orElse(false);

    }

    /**
     * 检查响应结果是否正常
     */
    public static boolean checkResponse(BaseResponse baseResponse){
        return ofNullable(baseResponse)
                .map(BaseResponse::getCode)
                .filter(Objects::nonNull)
                .map(code-> ResponseCodeEnum.SUCCESS.getCode() == code)
                .orElse(false);

    }

    public static <T> T getData(BaseResponse<T> baseResponse) {
        return ofNullable(baseResponse)
                .filter(b -> {
                            log.debug("[远程调用]-BaseResponse-getData: {}", b);
                            return ResponseCodeEnum.SUCCESS.getCode() == b.getCode();
                        }
                ).map(BaseResponse::getData).orElse(null);
    }

    /**
     * 得到返回值，不报错，并赋予默认值
     * @param baseResponse
     * @param defaultValue
     * @param <T>
     * @return
     */
    public static <T> T getData(BaseResponse<T> baseResponse,T defaultValue) {
        return ofNullable(baseResponse)
                .filter(b -> {
                            log.debug("[远程调用]-BaseResponse-getData: {}", b);
                            return ResponseCodeEnum.SUCCESS.getCode() == b.getCode();
                        }
                ).map(BaseResponse::getData).orElse(defaultValue);
    }

    /**
     * 得到返回值，不报错，并赋予默认值
     * @param respBody
     * @param defaultValue
     * @param <T>
     * @return
     */
    public static <T> T getData(RespBody<T> respBody,T defaultValue) {
        return ofNullable(respBody)
                .filter(b -> {
                    log.debug("[远程调用]-RespBody-getData: {}", b);
                    return GlobalErrorCode.OK.getCode().equals(b.getCode());
                }).map(RespBody::getBody).orElse(defaultValue);
    }

    public static <T> T getData(RespBody<T> respBody) {
        return ofNullable(respBody)
                .filter(b -> {
                    log.debug("[远程调用]-RespBody-getData: {}", b);
                    return GlobalErrorCode.OK.getCode().equals(b.getCode());
                }).map(RespBody::getBody).orElse(null);
    }


    public static <T> T getData(com.lyy.billing.infrastructure.resp.RespBody<T> respBody) {
        return ofNullable(respBody)
                .filter(b -> {
                    log.debug("[billing-system远程调用]-RespBody-getData: {}", b);
                    return GlobalErrorCode.OK.getCode().equals(b.getCode());
                }).map(com.lyy.billing.infrastructure.resp.RespBody::getBody).orElse(null);
    }

    public static <T> T getData(com.lyy.billing.infrastructure.resp.RespBody<T> respBody, T defaultValue) {
        return ofNullable(respBody)
                .filter(b -> {
                    log.debug("[billing-system远程调用]-RespBody-getData: {}", b);
                    return GlobalErrorCode.OK.getCode().equals(b.getCode());
                }).map(com.lyy.billing.infrastructure.resp.RespBody::getBody).orElse(defaultValue);
    }

    public static <T> T getDataThrowException(com.lyy.starter.common.resp.RespBody<T> respBody) {
        boolean result = GlobalErrorCode.OK.getCode().equals(respBody.getCode());
        if (!result) {
            log.warn("[billing-system远程调用]失败:{}", respBody.getMessage());
            throw new BusinessException(respBody.getCode(), respBody.getMessage());
        }
        return respBody.getBody();
    }

    public static <T> T getData(com.lyy.starter.common.resp.RespBody<T> respBody) {
        return ofNullable(respBody)
                .filter(b -> {
                    log.debug("[billing-system远程调用]-RespBody-getData: {}", b);
                    return GlobalErrorCode.OK.getCode().equals(b.getCode());
                }).map(com.lyy.starter.common.resp.RespBody::getBody).orElse(null);
    }

    public static <T> T getData(com.lyy.starter.common.resp.RespBody<T> respBody, T defaultValue) {
        return ofNullable(respBody)
                .filter(b -> {
                    log.debug("[billing-system远程调用]-RespBody-getData: {}", b);
                    return GlobalErrorCode.OK.getCode().equals(b.getCode());
                }).map(com.lyy.starter.common.resp.RespBody::getBody).orElse(defaultValue);
    }

    public static Integer longToInteger(Long param) {
        return ofNullable(param).map(Long::intValue).orElse(null);
    }

    public static Long integerToLong(Integer param) {
        return ofNullable(param).map(Integer::longValue).orElse(null);
    }

    public static List<Long> listIntegerToLong(List<Integer> param) {
        if (CollectionUtils.isEmpty(param)) {
            return new ArrayList<>();
        }
        return param.stream().map(Integer::longValue).collect(Collectors.toList());
    }

    public static <T> T getRespBodyData(com.lyy.starter.common.resp.RespBody<T> respBody, T defaultValue) {
        return ofNullable(respBody)
                .filter(res -> GlobalErrorCode.OK.getCode().equals(res.getCode()))
                .map(com.lyy.starter.common.resp.RespBody::getBody)
                .orElse(defaultValue);
    }

    public static <T> T getCommonData(com.lyy.starter.common.resp.RespBody<T> respBody) {
        return ofNullable(respBody)
                .filter(b -> {
                    log.debug("[远程调用]-RespBody-getData: {}", b);
                    return GlobalErrorCode.OK.getCode().equals(b.getCode());
                })
                .map(com.lyy.starter.common.resp.RespBody<T>::getBody)
                .orElse(null);
    }
}
