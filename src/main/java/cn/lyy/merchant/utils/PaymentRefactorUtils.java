package cn.lyy.merchant.utils;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/10/24
 */
@Deprecated
@Slf4j
public class PaymentRefactorUtils {

    public static <T> T getData(BaseResponse<T> baseResponse, T defaultValue) {
        if (log.isDebugEnabled()) {
            log.debug("[paymentRefactorUtil]-baseResponse: {}", baseResponse);
        }

        return Optional.ofNullable(baseResponse).filter((b) -> ResponseCodeEnum.SUCCESS.getCode() == b.getCode()).map(BaseResponse::getData).orElse(defaultValue);
    }
}