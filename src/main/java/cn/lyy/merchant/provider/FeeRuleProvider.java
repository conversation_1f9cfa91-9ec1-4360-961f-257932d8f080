package cn.lyy.merchant.provider;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.equipment.dto.EquipmentInfoDTO;
import cn.lyy.equipment.service.EquipmentService;
import cn.lyy.merchant.constants.BusinessExceptionEnums;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.request.BatchFeeRuleSaveDTO;
import cn.lyy.merchant.dto.request.FeeCommodityDTO;
import cn.lyy.merchant.dto.request.RuleDelDTO;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.service.commodity.FeeRuleService;
import com.lyy.commodity.rpc.constants.CategoryEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 计费规则接口，主要用于微服调用,内容功能与 @link FeeRuleController 一致
 *
 * <AUTHOR>
 * @className: FeeRuleProvider
 * @date 2021/1/4
 */
@Slf4j
@RestController
@RequestMapping("/provider/fee-rule")
public class FeeRuleProvider extends BaseController {

    @Autowired
    private FeeRuleService feeRuleService;

    @Autowired
    private EquipmentService equipmentService;


    /**
     * 获取设备的计费规则
     * 与接口 listEquipment 基本一致，但是修改distributorId为设备的注册商户id
     * @param equipmentValue
     * @return
     */
    @GetMapping(value = "/getFeeRuleByEquipment")
    public BaseResponse<List<FeeCommodityDTO>> getFeeRuleByEquipment(@RequestParam("equipmentValue") String equipmentValue) {

        EquipmentInfoDTO equipmentInfoDTO = equipmentService.getEquipmentInfoByValue(equipmentValue).getData();
        if (equipmentInfoDTO == null) {
            throw new BusinessException(BusinessExceptionEnums.DEVICE_NOT_EXISTS);
        }
        List<FeeCommodityDTO> list = feeRuleService.listByCondition(equipmentInfoDTO.getDistributorId().longValue(),
                equipmentInfoDTO.getEquipmentGroupId().longValue(),
                equipmentInfoDTO.getEquipmentTypeId().longValue(),
                equipmentInfoDTO.getEquipmentId().longValue(),
                CategoryEnum.DEVICE_SERVICE.getCode(),null);
        // 规则设置场地信息
        list.forEach(feeCommodityDTO -> feeCommodityDTO.setGroupId(equipmentInfoDTO.getEquipmentGroupId().longValue()));
        return ResponseUtils.success(list);
    }

    @PostMapping(value = "/batchSaveOrUpdate")
    public  BaseResponse batchSaveOrUpdate(@RequestBody BatchFeeRuleSaveDTO batchFeeRuleSaveDTO,
                                           @RequestParam("adOrgId") Long adOrgId, @RequestParam("adUserId") Long adUserId){
        log.info("FeeRuleProvider.batchFeeRulesaveOrUpdate-->{}",batchFeeRuleSaveDTO);
        feeRuleService.batchSaveOrUpdate(batchFeeRuleSaveDTO,adOrgId,adUserId);
        return ResponseUtils.success();
    }

    @PostMapping(value = "/del")
    public  BaseResponse del(@RequestBody RuleDelDTO ruleDelDTO,
                             @RequestParam("adOrgId") Long adOrgId, @RequestParam("adUserId") Long adUserId){
        log.info("FeeRuleProvider.del-->{}" ,ruleDelDTO);
        feeRuleService.delFee(ruleDelDTO,adOrgId);
        return ResponseUtils.success();
    }
}
