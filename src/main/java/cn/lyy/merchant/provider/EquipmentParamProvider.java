package cn.lyy.merchant.provider;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.dto.Status;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.service.equipment.EquipmentParamService;
import cn.lyy.tools.constants.open.FunctionConstants;
import cn.lyy.tools.equipment.OpenSettingInfo;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.util.Asserts;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 设备参数设置
 * <AUTHOR>
 * @className: EquipmentParamProvider
 * @date 2021/1/8
 */

@Slf4j
@RestController
@RequestMapping("/provider/equipment-param")
public class EquipmentParamProvider extends BaseController {
    @Autowired
    private EquipmentParamService equipmentParamService;
    /**
     * 1.0协议方式获取主板参数列表信息
     * @param uniqueCode
     * @return
     */
    @PostMapping(value = "s1/paramConfigList")
    public BaseResponse<List<Object>> s1ParamConfigList(@RequestParam String uniqueCode) {
        String settingInfo = equipmentParamService.queryParamInfoFromS1(uniqueCode, FunctionConstants.BSYS_SAAS_QUERY_FUNCTION, null);
        if (StringUtils.isBlank(settingInfo)){
            log.info(" {} 设备无法协议上的自定义参数信息",uniqueCode);
            BaseResponse baseResponse = success(new ArrayList<>(0));
            baseResponse.setMessage("协议无自定义参数信息");
            return baseResponse;
        }
        Gson gson = new Gson();
        List<Object> list = gson.fromJson(settingInfo, new TypeToken<List<Object>>() {
        }.getType());
        return success(list);
    }

    /**
     * 1.0协议方式查询参数项信息
     * @param uniqueCode
     * @param data
     * @return
     */
    @PostMapping(value = "s1/paramQuery")
    public BaseResponse<Map<String, String>> s1ParamQuery(String uniqueCode, String data) {
        try {
            String settingInfo = equipmentParamService.queryParamInfoFromS1(uniqueCode, FunctionConstants.BSYS_SAAS_QUERY_PARAM, data);
            Asserts.notBlank(settingInfo, "请求参数异常！");
            Gson gson = new Gson();
            Map<String, String> hashMap = gson.fromJson(settingInfo, new TypeToken<HashMap<String, String>>() {
            }.getType());
            return success(hashMap);
        } catch (BusinessException be) {
            log.warn(be.getMessage(), be);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return error(Status.STATUS_FAIL,"获取参数信息失败");
    }

    /**
     * 1.0协议方式设置参数项信息
     * @param info
     * @return
     */
    @PostMapping(value = "s1/batchSettingParam")
    public BaseResponse<Map<String,Boolean>> s1BatchSettingParam(@RequestParam("uniqueCodeList") String uniqueCodeList, @RequestBody OpenSettingInfo info) {
        log.info("{} 设备，需要设置主板参数 -->{}",uniqueCodeList,info);
        if(StringUtils.isBlank(uniqueCodeList)){
            return error(Status.STATUS_FAIL,"缺少需要设置参数的设备");
        }

        if(info == null || StringUtils.isBlank(info.getData())){
            return error(Status.STATUS_FAIL,"设置参数内容非法");
        }
        Set<String> uniqueCodeSet = Arrays.stream(uniqueCodeList.split(","))
                //去重空格
                .map(String::trim)
                //只允许16位的设备号
                .filter(str->str.length() == 16)
                //转为set来进行去重
                .collect(Collectors.toSet());
        if(uniqueCodeSet.isEmpty()){
            return error(Status.STATUS_FAIL,"设备数据非法，无法设置");
        }
        List<OpenSettingInfo> infoList = uniqueCodeSet.stream()
                .map(uniqueCode->{
                    OpenSettingInfo targetInfo = new OpenSettingInfo();
                    BeanUtils.copyProperties(info,targetInfo);
                    targetInfo.setUniqueCode(uniqueCode);
                    targetInfo.setFunctionCode(FunctionConstants.BSYS_SAAS_SETTING);
                    return targetInfo;
                }).collect(Collectors.toList());
        Map<String,Boolean> resultMap = equipmentParamService.batchSettingParamInfoFromS1(infoList);
        return success(resultMap);
    }

}
