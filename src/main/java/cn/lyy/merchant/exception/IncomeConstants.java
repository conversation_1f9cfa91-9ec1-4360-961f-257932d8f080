package cn.lyy.merchant.exception;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2019/9/19 15:29
 **/
@AllArgsConstructor
@Getter
public enum IncomeConstants {

    FAIL("E0000", "系统异常"),
    BUSINESS_ERROR("E0001", "业务异常"),
    GROUP_EMPTY("E0002", "场地为空"),
    PARAM_ERROR("E0003", "参数错误"),
    EQUIPMENT_EMPTY("E0004", "设备为空");

    private String code;

    private String message;
}
