package cn.lyy.merchant.exception;

import cn.lyy.merchant.constants.BusinessExceptionEnums;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 类描述：
 * <p>
 *
 * <AUTHOR>
 * @since 2020/11/5 11:35
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
public class BusinessException extends RuntimeException {

    private String msg;

    private String exCode;

    public BusinessException(BusinessExceptionEnums e) {
        super(e.getMsg());
        this.exCode = e.getExCode();
        this.msg = e.getMsg();
    }

    public BusinessException(BusinessExceptionEnums e, String... extraMsg) {
        this.exCode = e.getExCode();
        this.msg = String.format(e.getMsg(), extraMsg);
    }

    public BusinessException(String msg) {
        super(msg);
        this.exCode = BusinessExceptionEnums.PARAM_ERROR.getExCode();
        this.msg = msg;
    }

    public BusinessException(String exCode,String msg) {
        super(msg);
        this.msg = msg;
        this.exCode = exCode;
    }
}
