package cn.lyy.merchant.exception;


import cn.lyy.merchant.constants.BusinessExceptionEnums;

public  class CustomAssert {

    /**
     * 是否为空，抛出异常
     * @param source 判断对象
     * @param msg 错误提示
     */
    public static void assertNotNull(Object source,String msg) {
        if (source == null) {
            throw new BusinessException(msg);
        }
    }

    public static void assertNotNull(Object source, BusinessExceptionEnums exceptionEnums) {
        if (source == null) {
            throw new BusinessException(exceptionEnums);
        }
    }

    public static void assertTo(Object source,Object target,BusinessExceptionEnums exceptionEnums) {
        if (source.equals(target)) {
            throw new BusinessException(exceptionEnums);
        }
    }
    public static void assertTo(Object source,Object target,String msg) {
        if (source.equals(target)) {
            throw new BusinessException(msg);
        }
    }
}
