package cn.lyy.merchant.exception;

import static java.util.Optional.ofNullable;
import static org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication.Type.SERVLET;

import java.sql.SQLIntegrityConstraintViolationException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolationException;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.base.utils.ResponseUtils;
import feign.FeignException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.NoHandlerFoundException;

/**
 * 全局异常处理类
 *
 * <AUTHOR>
 */
@Slf4j
@RestControllerAdvice
@ConditionalOnClass(HttpServletRequest.class)
@ConditionalOnWebApplication(type = SERVLET)
public class GlobalExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    @ExceptionHandler(value = BusinessException.class)
    public BaseResponse<String> bizExceptionHandler(HttpServletRequest req, BusinessException e) {
        BaseResponse<String> res = new BaseResponse<>();
        res.setMessage(e.getMessage());
        res.setExCode(e.getExCode());
        if (log.isDebugEnabled()) {
            log.debug("[业务异常(生产不打印)] -> {}",  ExceptionUtils.getStackTrace(e));
        }
        res.setCode(ResponseCodeEnum.BUSINESS_EXCEPTION.getCode());
        return res;
    }

    @ExceptionHandler(BindException.class)
    public BaseResponse<String> bindException(HttpServletRequest req, BindException e) {
        logger.error("bind error", e);
        final List<FieldError> fieldErrors = e.getFieldErrors();
        StringBuilder builder = new StringBuilder();
        for (FieldError error : fieldErrors) {
            builder.append(error.getField()).append(":").append(error.getDefaultMessage()).append("；");
        }
        return new BaseResponse<>(HttpStatus.BAD_REQUEST.value(), builder.toString());
    }

    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public BaseResponse<String> methodNoSupported(HttpServletRequest req, HttpRequestMethodNotSupportedException e) {
        logger.error("method no supported ", e);
        String msg = e.getMethod() + "不被允许的请求方法！请使用：" + e.getSupportedHttpMethods();
        return new BaseResponse<>(HttpStatus.METHOD_NOT_ALLOWED.value(), msg);
    }

    @ExceptionHandler(ConstraintViolationException.class)
    public BaseResponse<String> constraintViolationException(HttpServletRequest req, ConstraintViolationException e) {
        logger.error("", e);
        String builder = e.getConstraintViolations().stream()
            .map(violation -> violation.getPropertyPath().toString() + ":" + violation.getMessage() + "；").collect(Collectors.joining());
        return new BaseResponse<>(HttpStatus.BAD_REQUEST.value(), builder);
    }

    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    public BaseResponse<String> argumentNotValid(HttpServletRequest req, MethodArgumentNotValidException e) {
        logger.error("", e);
        BindingResult result = e.getBindingResult();
        final List<FieldError> fieldErrors = result.getFieldErrors();
        StringBuilder builder = new StringBuilder();
        for (FieldError error : fieldErrors) {
            builder.append(error.getField()).append(":").append(error.getDefaultMessage()).append("；");
        }
        return new BaseResponse<>(HttpStatus.BAD_REQUEST.value(), builder.toString());
    }


    @ExceptionHandler(value = MethodArgumentTypeMismatchException.class)
    public BaseResponse<String> argumentTypeMismatch(HttpServletRequest req, MethodArgumentTypeMismatchException e) {
        StringBuilder sb = new StringBuilder();
        ofNullable(req.getParameterMap()).orElse(new HashMap<>(0)).forEach((k, v)->{
            sb.append(k).append(":").append(Arrays.toString(v)).append(";");
        });
        logger.error("==========>请求路径:{}, 请求参数:{}, 错误信息:{}", req.getRequestURL(), sb.substring(0, sb.length()>0? sb.length()-1:0), e);
        String msg = e.getName() + "必须是：" + e.getRequiredType() + "类型！";
        return ResponseUtils.error(HttpStatus.BAD_REQUEST.value(), msg);
    }

    @ExceptionHandler(value = HttpMediaTypeNotSupportedException.class)
    public BaseResponse<String> mediaTypeNotSupported(HttpServletRequest req, HttpMediaTypeNotSupportedException e) {
        logger.error("", e);
        String msg = e.getContentType() + "不支持！Content-Type必须是：" + e.getSupportedMediaTypes() + "类型！";
        return ResponseUtils.error(HttpStatus.BAD_REQUEST.value(), msg);
    }


    @ExceptionHandler(NoHandlerFoundException.class)
    public BaseResponse<String> handle(HttpServletRequest request, NoHandlerFoundException e) {
        logger.error("", e);
        return new BaseResponse<>(404, "地址错误！！！" + request.getRequestURI() + "非法访问!");
    }

    @ExceptionHandler({SQLIntegrityConstraintViolationException.class, DataIntegrityViolationException.class})
    public BaseResponse<String> violation(HttpServletRequest req, SQLIntegrityConstraintViolationException e) {
        logger.error("", e);
        return ResponseUtils.error(ResponseCodeEnum.FAIL.getCode(), "关联数据错误！");
    }

    @ExceptionHandler(DuplicateKeyException.class)
    public BaseResponse<String> duplicate(HttpServletRequest req, DuplicateKeyException e) {
        logger.error("", e);
        return ResponseUtils.error(ResponseCodeEnum.FAIL.getCode(), "重复提交数据！");
    }

    @ExceptionHandler(HttpMessageNotReadableException.class)
    public BaseResponse<String> notReadable(HttpServletRequest req, HttpMessageNotReadableException e) {
        logger.error("", e);
        return ResponseUtils.error(ResponseCodeEnum.FAIL.getCode(), "请求格式有误！");
    }

    @ExceptionHandler(FeignException.class)
    public BaseResponse<String> feignServerError(FeignException e){
        logger.error("", e);
        return ResponseUtils.error(ResponseCodeEnum.FAIL.getCode(), "未处理的服务内部调用异常！");
    }

    @ExceptionHandler(value = {IllegalArgumentException.class})
    public BaseResponse<String> illegalArgumentError(IllegalArgumentException e){
        logger.error("", e);
        return ResponseUtils.error(HttpStatus.BAD_REQUEST.value(), "请求参数异常:"+e.getMessage());
    }

    @ExceptionHandler(value = {MissingServletRequestParameterException.class})
    public BaseResponse<String> missingServletRequestParameterError(MissingServletRequestParameterException e, HttpServletRequest request){
        logger.warn("url:{}", request.getRequestURI(), e);
        return ResponseUtils.error(HttpStatus.BAD_REQUEST.value(), "请求参数异常！");
    }

    @ExceptionHandler(value = {SessionTimeoutException.class})
    public BaseResponse<String> sessionTimeoutException(SessionTimeoutException e) {
        logger.warn("", e);
        return ResponseUtils.error(ResponseCodeEnum.FAIL.getCode(), "商户信息加载失败！");
    }

    @ExceptionHandler(value = Exception.class)
    public BaseResponse<String> otherExceptionHandler(HttpServletRequest req, Exception e) {

        logger.error("请求异常{}, {}", req.getRequestURI(), e.getMessage(), e);
        BaseResponse<String> response = new BaseResponse<>();
        response.setCode(ResponseCodeEnum.FAIL.getCode());
        response.setMessage("系统错误！");
        return response;
    }

}
