package cn.lyy.merchant.exception;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Date 2019/9/19 15:24
 * @Description 经营统计异常
 **/
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class IncomeToDayException extends RuntimeException {

    private String code;

    private String message;

    public IncomeToDayException(IncomeConstants constants) {
        super();
        this.code = constants.getCode();
        this.message = constants.getMessage();
    }
}
