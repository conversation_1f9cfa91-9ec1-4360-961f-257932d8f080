package cn.lyy.merchant.async;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.equipment.service.EquipmentRedisService;
import cn.lyy.tools.constants.open.FunctionConstants;
import cn.lyy.websocket.constant.ClientTypeEnum;
import cn.lyy.websocket.constant.MessageTypeEnum;
import cn.lyy.websocket.mq.WebSocketMessageProducer;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 设备参数异步查询类
 *
 * <AUTHOR>
 * @create 2021/3/12 11:31
 */
@Slf4j
@Component
public class EquipmentParamAsync {

    @Resource
    private WebSocketMessageProducer webSocketMessageProducer;

    @Resource
    private EquipmentRedisService equipmentRedisService;


    @Async(value ="taskExecutor")
    public void getEquipmentParam(String toUniqueId,String functionCode,String queryData,String key){
        log.info("执行异步任务,theadName:{},时间:{}",Thread.currentThread().getName(), new Date());
        String settingInfo = null;
        //查功能的最多3秒,因为不需要查主板
        final int maxCheckNum = 6;
        int checkTime = FunctionConstants.BSYS_SAAS_QUERY_FUNCTION.equals(functionCode)?maxCheckNum:20;
        for (int i = 0; i < checkTime; i++) {
            //每隔500毫秒查1次
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                break;
            }
            log.debug("重试第{}次获取参数",i);
            BaseResponse<String> response = equipmentRedisService.getString(key.toString());
            if(ResponseCodeEnum.SUCCESS.getCode() == response.getCode() && org.apache.commons.lang3.StringUtils.isNoneBlank(response.getData())){
                settingInfo = response.getData();
                break;
            }

        }
        BaseResponse baseResponse = null;
        // 默认以参数列表的方式返回
        String  messageType =  MessageTypeEnum.EQUIPMENT_PARAM_LIST.getType();
        String sendContent = null;
        if(!StringUtils.isEmpty(settingInfo)){
            // 如果参数信息不为空
            log.debug("参数查询成功");
            if(StringUtils.isEmpty(queryData)){
                // 查询全部的
                JSONArray settingArr = JSON.parseArray(settingInfo);
                baseResponse = ResponseUtils.success(settingArr);
                sendContent = JSON.toJSONString(baseResponse);
                messageType = MessageTypeEnum.EQUIPMENT_PARAM_LIST.getType();

            }else{
                // 查询消息主体
                JSONObject settingJson = JSON.parseObject(settingInfo);
                baseResponse = ResponseUtils.success(settingJson);
                sendContent = JSON.toJSONString(baseResponse);
                messageType = MessageTypeEnum.EQUIPMENT_PARAM.getType();
            }
        }else{
            log.debug("参数查询失败");
            baseResponse = ResponseUtils.error(-1,"设备参数查询失败");
            sendContent = JSON.toJSONString(baseResponse);
        }
        webSocketMessageProducer.send(messageType, toUniqueId, ClientTypeEnum.MERCHANT_H5.getType(), sendContent);
    }
}
