package cn.lyy.merchant.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 类描述：远程启动参数对象
 * <p>
 *
 * <AUTHOR>
 * @since 2020/11/5 16:27
 */
@Getter
@Setter
@ToString
public class RemoteStartParamDTO {

    /**
     * 设备编号
     */
    private String equipmentValue;

    /**
     * 币数
     */
    private Integer coins;

    /**
     * 通道
     */
    private Integer channel;

    /**
     * 电量
     */
    private BigDecimal elec;

    /**
     * 时间
     */
    private Integer time;

    /**
     * 计费模式
     * ELEC 电量
     * TIME  时长
     */
    private String mode;

    /**
     * 套餐价格
     */
    private BigDecimal price;

    /**
     * 主板编号
     */
    private String mainBoard;

    /**
     * 操作人
     */
    private Long adUserId;


    /**
     * 设备套餐名称-串口洗衣机
     */
    private String groupServiceName;
}
