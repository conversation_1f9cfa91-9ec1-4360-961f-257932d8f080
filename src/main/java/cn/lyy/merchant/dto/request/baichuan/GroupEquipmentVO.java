package cn.lyy.merchant.dto.request.baichuan;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/3/14
 */
@Data
public class GroupEquipmentVO implements Serializable {

    /**
     * 设备id
     */
    @ApiModelProperty("设备id")
    private Long equipmentId;

    /**
     * 设备编号
     */
    @ApiModelProperty("设备编号")
    private String value;

    /**
     * 是否已关联消费方案
     */
    @ApiModelProperty("是否已关联消费方案")
    private Boolean isAssociation;

    /**
     * 设备类型id
     */
    @ApiModelProperty("设备类型id")
    private Long equipmentTypeId;

}
