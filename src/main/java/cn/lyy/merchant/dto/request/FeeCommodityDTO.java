package cn.lyy.merchant.dto.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 类描述：计费规则商品
 * <p>
 *
 * <AUTHOR>
 * @since 2020/11/18 15:39
 */
@Getter
@Setter
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FeeCommodityDTO {

    /**
     * 关联设备ID
     */
    private Long relateEquipmentId;

    /**
     * 商品展示id
     */
    private Long displayId;
    /**
     * 单价
     */
    @NotNull(message = "商品单价不能为空")
    private BigDecimal price;

    /**
     * 套餐名称
     */
    private String name;

    /**
     * 场地ID
     */
    private Long groupId;

    /**
     * 支持多个价值规则
     */
    private List<PriceValueDTO> priceValueList;

    /**
     * 是否显示标题
     */
    private Boolean isShowTitle;

    /**
     * 是否显示单位
     */
    private Boolean isShowFeeRuleUnit;

    /**
     *  有值代表是区间类型
     */
    private Integer rangeCategory;

    /**
     * 有效的时间类型
     */
    private Integer validTimeCategory;


    /**
     * 商品分类 对于充电桩来说就是
     * ELEC 电量
     * TIME 时长
     *
     */
    @NotNull(message = "商品分类不能为空")
    private String classifyCode;

    private String categoryCode;

    /**
     * 商品名称
     */
    private String title;

    /**
     * 是否启用，默认启用
     */
    private Boolean isUse;

    /**
     * 关联的商品
     */
    private List<FeeRuleComposeDTO> composes;

    /**
     * 范围开始值
     */
    private String rangeStart;

    /**
     * 范围结束值
     */
    private String rangeEnd;
}
