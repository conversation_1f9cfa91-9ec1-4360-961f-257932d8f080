package cn.lyy.merchant.dto.request.baichuan;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotEmpty;
import lombok.Data;

@Data
@ApiModel("计量商品")
public class MeteringCommodity {

    @ApiModelProperty("商品id")
    private Long commodityId;

    @ApiModelProperty("名称")
    private String commodityName;

    @ApiModelProperty("免费量")
    private BigDecimal free;

    @ApiModelProperty("每24小时封顶金额")
//    @DecimalMin(value = "0.01", message = "每24小时封顶金额需大于0")
    private BigDecimal dayMaxAmount;

    @ApiModelProperty("封顶金额")
//    @DecimalMin(value = "0.01", message = "封顶金额需大于0")
    private BigDecimal maxAmount;

    @ApiModelProperty("押金")
    private BigDecimal deposit;

    @ApiModelProperty("计费规则")
    @NotEmpty(message = "计费规则为空")
    @Valid
    private List<StageRule> stageRuleList;
}
