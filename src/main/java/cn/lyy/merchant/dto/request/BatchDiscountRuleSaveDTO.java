package cn.lyy.merchant.dto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>Desc:计费规则保存 </p>
 *
 */
@Data
public class BatchDiscountRuleSaveDTO implements Serializable {

    /**
     * 场地id 集合
     */
    @NotNull
    private List<Long> codes;

    @NotNull
    private Long equipmentTypeId;

    private List<DiscountRuleDetailDTO> discountRule;

    @Data
    public static class DiscountRuleDetailDTO implements Serializable {


        /** 详情id，修改或刪除时传递 */
        private Long detailId;

        @NotNull
        private Long equipmentTypeId;

        private String equipmentTypeValue;

        //@NotNull
        private Long groupId;

        /** 套餐标题，如脱水，快洗，标准等 */
        private String title;

        /** 单价，单位为元 */
        private BigDecimal price;
        /** 取值(字符串，可以是取值范围) */
        @NotNull(message = "取值不能为空！")
        private BigDecimal coin;
        /** 取值的单位(1 次/币，2 时长，3 电量，4 功率区间，5 时段，6 时段，7 件，8 币， 9时长电量) */
        @NotNull(message = "取值单位不能为空！")
        private Integer unit;

    }
}
