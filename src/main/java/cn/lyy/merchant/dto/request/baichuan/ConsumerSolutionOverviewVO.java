package cn.lyy.merchant.dto.request.baichuan;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

@Data
public class ConsumerSolutionOverviewVO {

    @ApiModelProperty("消费方案名称")
    private String name;

    @ApiModelProperty("设备类型id")
    private Long equipmentTypeId;

    @ApiModelProperty("设备类型名称")
    private String equipmentTypeName;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("消费方案ID")
    private Long id;

    @ApiModelProperty("配置id")
    private Long settingId;

    @ApiModelProperty("配置名称")
    private String settingName;

    @ApiModelProperty("商户ID")
    private Long merchantId;

    @ApiModelProperty("设备数")
    private Integer equipmentNum;

    @ApiModelProperty("是否默认方案")
    private Boolean isDefault;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updated;
}
