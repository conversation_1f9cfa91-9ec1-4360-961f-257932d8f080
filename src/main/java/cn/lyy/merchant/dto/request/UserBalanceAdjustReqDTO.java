package cn.lyy.merchant.dto.request;

import cn.lyy.merchant.dto.member.BenefitDetailQueryDTO;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 用户余额信息
 */
@Getter
@Setter
@ToString
public class UserBalanceAdjustReqDTO {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 商户id
     */
    private Long distributorId;

    /**
     * 场地id
     */
    private Long groupId;

    /**
     * 操作人id
     */
    private Long operatorId;
    /**
     * 1-全部清空余额  2全部清空余币 3全部清空余额余币 4清空指定批次 5清空指定类型 6清空查询
     */
    private Integer isEmpty;
    /**
     * 操作类型：1 商家b端调整
     */
    private Integer operationType;

    /**
     * 场地余额
     */
    @ApiModelProperty(hidden = true)
    @Deprecated
    private List<UserGroupBalanceDTO> groupBalances;

    /**
     * 红包币
     */
    private BigDecimal redCoins;

    /**
     * 红包余额
     */
    private BigDecimal redAmount;

    /**
     * 派币
     */
    private BigDecimal grantCoins;

    /**
     * 派余额
     */
    private BigDecimal grantAmount;

    /**
     * 权益ID
     */
    private Long accountBenefitId;

    /**
     * 权益类型
     */
    private Integer classify;

    /**
     * 商户调整余额
     */
    private BigDecimal amount;

    /**
     * 调整余币
     */
    private BigDecimal coins;

    /**
     * 备注
     */
    private String remark;

    private BenefitDetailQueryDTO benefitDetailQuery;

    private String upTime;
    private String downTime;
    private Integer expiryDateCategory;

}
