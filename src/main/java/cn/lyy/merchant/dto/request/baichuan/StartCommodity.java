package cn.lyy.merchant.dto.request.baichuan;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("启动套餐")
public class StartCommodity {

    @ApiModelProperty("商品id")
    private BigDecimal commodityId;

    @ApiModelProperty("支付金额")
    private BigDecimal price;

    @ApiModelProperty("套餐权益金额")
    private BigDecimal benefitPrice;

    @ApiModelProperty("定价值")
    private BigDecimal fixPriceValue;

    @ApiModelProperty("定价值单位(次/分钟/度)")
    private Integer unit;

    @ApiModelProperty("模拟投币数")
    private BigDecimal coins;

    @ApiModelProperty("套餐名称")
    private String commodityName;

    @ApiModelProperty("是否推荐")
    private Boolean isRecommend;

    @ApiModelProperty("是否启用")
    private Boolean enable;

    @ApiModelProperty("套餐排序")
    private Integer sort;
}
