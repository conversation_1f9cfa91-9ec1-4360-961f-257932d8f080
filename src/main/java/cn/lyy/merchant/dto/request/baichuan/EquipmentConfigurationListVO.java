package cn.lyy.merchant.dto.request.baichuan;

import com.lyy.billing.interfaces.consumer.dto.EquipmentConfigurationBillingSettingDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 设备配置列表
 * <AUTHOR>  lizhengxian
 * @createDate   :  2023/5/9 09:58
 * @since        :  1.0.0
 */
@Data
public class EquipmentConfigurationListVO implements Serializable {

    @ApiModelProperty("设备配置id")
    private Long id;

    @ApiModelProperty("设备配置名称")
    private String name;

    @ApiModelProperty("储值类型名称")
    private String storedTypeName;


    @ApiModelProperty("状态,0启用,1停用")
    private Integer status;
    @ApiModelProperty("计费方式0套餐模式,1计量模式")
    private Integer billingWay;

    @ApiModelProperty("计费方式名称")
    private String billingWayName;

    @ApiModelProperty("基础功能")
    private List<Integer> basisFunction;
    @ApiModelProperty("储值类型")
    private Integer storedType;
    @ApiModelProperty("储值引导开关")
    private Boolean guideRechargeSwitch;
    @ApiModelProperty("配置单位")
    private Integer unit;
    @ApiModelProperty("配置单位名称")
    private String unitName;
    @ApiModelProperty("单位类型名称")
    private String unitTypeName;
    @ApiModelProperty("计费配置")
    private EquipmentConfigurationBillingSettingDTO billingSetting;

    @ApiModelProperty("是否默认")
    private Boolean defaultValue;

}
