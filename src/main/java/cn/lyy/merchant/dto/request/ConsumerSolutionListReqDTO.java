package cn.lyy.merchant.dto.request;

import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/3/8
 */
@Data
public class ConsumerSolutionListReqDTO {

    @NotNull(message = "商户ID不能为空")
    private Long merchantId;

    /**
     * 范围 1 商户 2 指定门店
     */
    @NotNull(message = "范围不能为空")
    private Integer scope;

    private List<Long> store;

    private String keyword;

    private Long equipmentTypeId;

    @NotNull(message = "是否需要统计数量不能为空")
    private Boolean isCount;
}
