package cn.lyy.merchant.dto.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class EquipmentGetByCodeDTO {
    /** 设备码 */
    String code;

    /**
     *  附加的设备类型，支持JYJ类型
     */
    //String attachEquipmentType;

    /** 设备类型，来自上一次扫码的设备 */
    String equipmentType;

    /** 产品id，来自上一次扫码的设备 */
    private Long productId;

    /** 协议id，来自上一次扫码的设备 */
    Long protocolId;
}