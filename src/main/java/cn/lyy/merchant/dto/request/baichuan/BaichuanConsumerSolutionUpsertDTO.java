package cn.lyy.merchant.dto.request.baichuan;

import com.lyy.billing.interfaces.consumer.dto.baichuan.ConsumerSolutionVO.OtherSetting;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/5/5
 */
@Data
public class BaichuanConsumerSolutionUpsertDTO implements Serializable {

    /**
     * 方案id
     */
    @ApiModelProperty("方案id")
    private Long id;

    /**
     * 配置id
     */
    @ApiModelProperty("配置id")
    private Long settingId;

    /**
     * 消费方案名称
     */
    @ApiModelProperty("消费方案名称")
    private String name;

    /**
     * 设备类型
     */
    @ApiModelProperty("设备类型")
    private Long equipmentTypeId;

    /**
     * 计费类型（1.套餐模式 2.计量模式)
     */
    @ApiModelProperty("计费类型（1.套餐模式 2.计量模式)")
    private Integer model;

    /**
     * 启动方式（1.支付启动 2. 次卡启动 3. 储值启动）
     */
    @ApiModelProperty("基础设置（0.支持储值 1.支持信用分 2.支持押金）")
    private List<Integer> baseSetting;

    /**
     * 支付方式（1.支付 2. 次卡 3. 储值 5.支付分）
     */
    @ApiModelProperty("支付方式（1.支付 2. 次卡 3. 储值 5.支付分）")
    private List<Integer> startWay;

    /**
     * 描述
     */
    @ApiModelProperty("描述")
    private String description;

    /**
     * 其他配置
     */
    @ApiModelProperty("其他配置")
    private OtherSetting otherSetting;

    /**
     * 退款配置
     */
    @ApiModelProperty("退款配置")
    private RefundSetting refundSetting;

    /**
     * 是否配置下默认消费方案
     */
    @ApiModelProperty("是否配置下默认消费方案")
    private Boolean isDefault;

    @ApiModelProperty("储值套餐列表")
    private List<RechargeCommodity> rechargeCommodityList;

    @ApiModelProperty("启动套餐列表")
    @Valid
    private List<StartCommodity> startCommodityList;

    @ApiModelProperty("计量商品")
    @Valid
    private MeteringCommodity meteringCommodity;

}
