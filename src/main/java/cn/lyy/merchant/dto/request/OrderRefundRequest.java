package cn.lyy.merchant.dto.request;

import cn.lyy.base.utils.validate.FeeValidator;
import cn.lyy.open.order.dto.request.OrderRefundDTO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * @createTime 2020-12-02
 * @auther peterguo
 *
 * @Description 发起退款
 */
@Getter
@Setter
@ToString
public class OrderRefundRequest {

    /**
     * 交易单号
     */
    @NotNull(message = "交易单号不能为空")
    private String outTradeNo;

    /**
     * 退款金额
     */
    @FeeValidator
    private BigDecimal refundAmount;

    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空")
    private Long userId;

    /**
     * 退款方式
     */
    private Integer refundType;

    /**
     * 退款详情
     */
    private List<OrderRefundDTO.RefundDetail> refundDetail; //退款详情

    /**
     * 退款子订单详情
     */
    @NotNull(message = "子订单不能为空")
    private List<OrderRefundDTO.RefundOrderItemDTO> refundOrders;

    /**
     * 商户id
     */
    private Long distributorId;
}
