package cn.lyy.merchant.dto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>Desc:计费规则保存 </p>
 *
 */
@Data
public class BatchFeeRuleSaveDTO implements Serializable {

    /**
     *  设备编号集合
     */
    @NotNull
    private List<String> codes;

    /**
     * 设备类型
     */
    //@NotNull
    private Long equipmentTypeId;

    private String equipmentType;

    @NotBlank
    private String classifyCode;

    /**
     * 是否显示标题
     */
    private Boolean isShowTitle;

    /**
     * 是否显示单位
     */
    private Boolean isShowFeeRuleUnit;

    /**
     * 计费规则
     */
    @NotNull
    private List<BatchFeeRuleDTO> feeRules;

    @Data
    public static class BatchFeeRuleDTO{

        public BatchFeeRuleDTO(){

        }

        /** 详情id，修改或刪除时传递 */
        private Long detailId;

        /** 套餐标题，如脱水，快洗，标准等 */
        @ApiModelProperty("标题")
        @Deprecated
        private String title;

        @ApiModelProperty("商品名称")
        private String name;

        /** 单价，单位为元 */
        @NotNull(message = "价格不能为空！")
        private BigDecimal price;

        //@NotNull(message = "取值不能为空！")
        @Deprecated
        private BigDecimal value;
        /** 取值的单位(1 次/币，2 时长，3 电量，4 功率区间，5 时段，6 时段，7 件，8 币， 9时长电量) */
        //@NotNull(message = "取值单位不能为空！")
        @Deprecated
        private Integer valueUnit;

        @Deprecated
        private List<PriceValueDTO> fixPriceValueList;

        private List<PriceValueDTO> priceValueList;

        /**
         * 是否启用，禁用规则
         */
        private Boolean isUse;

        /**
         * 组合商品
         */
        private List<FeeRuleComposeDTO> composes;

        /**
         * 范围类型
         */
        private Integer rangeCategory;

        /**
         * 范围开始值
         */
        private String rangeStart;

        /**
         * 范围结束值
         */
        private String rangeEnd;
    }

}
