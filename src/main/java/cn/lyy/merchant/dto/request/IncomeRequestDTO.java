package cn.lyy.merchant.dto.request;

import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Pattern;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2019/9/19 15:34
 **/
@Data
@ToString
public class IncomeRequestDTO {

    /**
     * 场地
     */
    private List<Integer> groups;

    /**
     * 设备类型
     */
    private List<Integer> equipmentType;

    /**
     * 标签
     */
    private List<Integer> labels;

    /**
     * 开始日期
     */
    @NotBlank(
            message = "开始时间不能为空"
    )
    @Pattern(
            regexp = "^(\\d{4})-(0\\d{1}|1[0-2])-(0\\d{1}|[12]\\d{1}|3[01])$",
            message = "开始时间格式不正确"
    )
    private String startDate;

    /**
     * 结束日期
     */
    @NotBlank(
            message = "结束时间不能为空"
    )
    @Pattern(
            regexp = "^(\\d{4})-(0\\d{1}|1[0-2])-(0\\d{1}|[12]\\d{1}|3[01])$",
            message = "结束时间格式不正确"
    )
    private String endDate;

    /**
     * 页码
     */
    private Integer pageIndex;

    /**
     * 页条数
     */
    private Integer pageSize;

    /**
     * 排序字段
     */
    protected Integer sortField;


    /**
     * 排序字段
     */
    protected  String field;

    /**
     * 排序方式
     */
    protected String sortDirection;
}
