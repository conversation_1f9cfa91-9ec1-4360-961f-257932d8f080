/**
 * 
 */
package cn.lyy.merchant.dto.request;


import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * <AUTHOR> 2019年5月22日
 */
@Data
public class PublicAccountConfigForm {

	private String appId;

	private String type;

	private String industryId1;

	private String industryId2;

	private List<MenuItem> menus;
	/**
	*引导语
	*/
	private String lead;

	/**
	 * 场地配置
	 */
	private List<LyyGroupConfigFrom> groupConfigFromList;

	/**
	 * 公众号账号
	 */
	private String officialAccout;

	@Data
	@NoArgsConstructor
	public static class MenuItem {

		private String name;

		private String type;

		private String url;

		private String appid;

		private String pagepath;

		private boolean isEditable;

		private List<MenuItem> sub_button;

		public MenuItem(String name, String type, String url, boolean isEditable){
			this.name = name;
			this.type = type;
			this.url = url;
			this.isEditable = isEditable;
		}

	}

	@Data
	@NoArgsConstructor
	public static class LyyGroupConfigFrom {

		private Integer lyyGroupConfigId;

		/**
		 * 公众号授权表id
		 */
		private Integer lyyComponentAuthorizerId;

		/**
		 * 商户id
		 */
		private Integer lyyDistributorId;

		/**
		 * 场地id
		 */
		private Integer lyyEquipmentGroupId;

		/**
		 * 场地名称
		 */
		private String groupName;

		/**
		 * 场地详细地址
		 */
		private String address;

		/**
		 * 状态 0：正常（引导关注）；1：强制关注；2：不关注
		 */
		private Integer status;

		public LyyGroupConfigFrom(Integer lyyComponentAuthorizerId, Integer lyyDistributorId, Integer lyyEquipmentGroupId,
								  String groupName, String address, Integer status) {
			this.lyyComponentAuthorizerId = lyyComponentAuthorizerId;
			this.lyyDistributorId = lyyDistributorId;
			this.lyyEquipmentGroupId = lyyEquipmentGroupId;
			this.groupName = groupName;
			this.address = address;
			this.status = status;
		}

		public LyyGroupConfigFrom(Integer lyyGroupConfigId, Integer lyyComponentAuthorizerId, Integer lyyDistributorId,
								  Integer lyyEquipmentGroupId, String groupName, String address, Integer status) {
			this.lyyGroupConfigId = lyyGroupConfigId;
			this.lyyComponentAuthorizerId = lyyComponentAuthorizerId;
			this.lyyDistributorId = lyyDistributorId;
			this.lyyEquipmentGroupId = lyyEquipmentGroupId;
			this.groupName = groupName;
			this.address = address;
			this.status = status;
		}
	}
}
