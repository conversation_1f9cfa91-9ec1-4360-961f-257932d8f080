package cn.lyy.merchant.dto.request;

import cn.lyy.equipment.dto.equipment.RegisterItemDTO;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description 设备注册dto
 * <AUTHOR>
 * @Date 2019/8/24
 * @Version 1.0
 **/
@Data
@ToString(callSuper = true)
public class EquipmentRegisterDTO {

//    /** 设备唯一码 */
////    @NotNull(message = "设备唯一码不能为空！")
//    private List<String> codes;

    /**
     * 设备信息
     */
    @NotNull
    private List<RegisterItemDTO> equipments;

    /** 设备类型名称 */
    @NotBlank(message = "设备类型名称不能为空！")
    private String equipmentType;

    /** 场地ID */
    @NotNull(message = "场地ID不能为空！")
    private Long groupId;

    /** 计费规则 */
    @NotNull(message = "计费规则不能为空！")
    private List<FeeCommodityDTO> feeRule;

    /** 折扣规则 */
    private List<FeeCommodityDTO> discountRule;

    /**
     * 商户id
     */
    private Long distributorId;

    /**
     * 当前登录用户
     */
    private Long userId;

    /**
     * 是否展示套餐的title ,设置默认值 true
     */
    private Boolean showTitle=true;

    /**
     * 是否展示套餐的单位 ,设置默认值 true
     */
    private Boolean showUnit=true;

    /**
     * 是否是转移设备
     */
    private Boolean isTransfer = false;
}
