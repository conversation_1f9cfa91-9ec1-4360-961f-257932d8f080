package cn.lyy.merchant.dto.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * 类描述：组合商品
 * <p>
 *
 * <AUTHOR>
 * @since 2021/2/22 14:10
 */
@Getter
@Setter
@ToString
public class FeeRuleComposeDTO {

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 套餐名称
     */
    private String name;

    /**
     * 价值列表
     */
    private List<PriceValueDTO> priceValueList;

    /**
     * 取值类型
     * @see com.lyy.commodity.rpc.constants.ClassifyCodeEnum
     */
    private String classifyCode;

    /**
     * 商品分类
     * @see com.lyy.commodity.rpc.constants.CategoryEnum
     */
    private String categoryCode;
}
