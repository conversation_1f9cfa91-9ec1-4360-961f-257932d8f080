package cn.lyy.merchant.dto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.List;

/**
 * <p>Desc:计费规则保存 </p>
 *
 */
@Data
public class FeeRuleSaveReqDTO implements Serializable {

    private static final long serialVersionUID = -406792932849287840L;

    /** 详情id，修改或刪除时传递 */
    private Long detailId;

    @ApiModelProperty("设备ID")
    @NotNull
    private Long equipmentId;

    private Long equipmentTypeId;

    private String equipmentTypeValue;


    /** 套餐标题，如脱水，快洗，标准等 */
    @ApiModelProperty("标题")
    private String title;

    /** 单价，单位为元 */
    @NotNull(message = "价格不能为空！")
    private BigDecimal price;

    /**
     * 取值 和单位，支持多种
     */
    private List<FixPriceValueDTO> fixPriceValueList;
    /** 取值类型（次数：num，电量：elec，时间：time */
    @NotNull
    private String classifyCode;

    /**
     * 商品分类
     * @see com.lyy.commodity.rpc.constants.CategoryEnum
     */
    private String categoryCode;
    /**
     * 组合商品
     */
    private List<FeeRuleComposeDTO> composes;

    /**
     * 范围类型
     * @see com.lyy.commodity.rpc.constants.RangeCategoryEnum
     */
    private Integer rangeCategory;

    /**
     * 范围开始值
     */
    private String rangeStart;

    /**
     * 范围结束值
     */
    private String rangeEnd;
}
