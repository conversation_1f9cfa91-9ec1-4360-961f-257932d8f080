package cn.lyy.merchant.dto.request.baichuan;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
public class EquipmentAssociationReqDTO implements Serializable {

    @ApiModelProperty("消费方案id")
    @NotNull(message = "关联设备类型不能为空")
    private Long consumerSolutionId;

    @ApiModelProperty("添加关联设备ids")
    private List<Long> addEquipmentIds;

    @ApiModelProperty("移除关联设备ids")
    private List<Long> removeEquipmentIds;

    @ApiModelProperty("是否强制关联")
    @NotNull(message = "是否强制关联不能为空")
    private Boolean forceUpdate;
}
