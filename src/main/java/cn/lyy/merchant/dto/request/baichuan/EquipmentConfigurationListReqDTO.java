package cn.lyy.merchant.dto.request.baichuan;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 设备配置列表
 * <AUTHOR>  l<PERSON><PERSON><PERSON><PERSON>n
 * @createDate   :  2023/5/9 09:58
 * @since        :  1.0.0
 */
@Data
public class EquipmentConfigurationListReqDTO implements Serializable {

    @NotBlank(message="设备类型不能为空")
    @ApiModelProperty("设备类型")
    private String equipmentType;


}
