package cn.lyy.merchant.dto.request.baichuan;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

@Data
@ApiModel("储值套餐")
public class RechargeCommodity {

    @ApiModelProperty("商品id")
    private Long commodityId;

    @ApiModelProperty("支付金额")
    private BigDecimal price;

    @ApiModelProperty("充值数量")
    private BigDecimal recharge;

    @ApiModelProperty("赠送数量")
    private BigDecimal present;

    @ApiModelProperty("有效期")
    private Integer effectiveDay;

    @ApiModelProperty("套餐名称")
    private String commodityName;

    @ApiModelProperty("是否推荐")
    private Boolean isRecommend;

    @ApiModelProperty("是否启用")
    private Boolean enable;

    @ApiModelProperty("套餐排序")
    private Integer sort;
}
