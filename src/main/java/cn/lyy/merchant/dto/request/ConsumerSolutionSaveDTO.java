package cn.lyy.merchant.dto.request;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/3/7
 */
@Data
public class ConsumerSolutionSaveDTO implements Serializable {

    /**
     * 消费方案名称
     */
    @NotBlank(message = "消费方案名称不能为空")
    private String name;

    /**
     * 消费方案类型（1. 刷卡消费方案 2. 扫码消费方案）
     */
    @NotNull(message = "消费方案类型不能为空")
    private Integer category;

    /**
     * 设备类型
     */
    private Long equipmentType;

    private String equipmentTypeName;

    /**
     * 计费类型（1. 计次消费 2. 计时消费)
     */
    private Integer model;

    /**
     * 启动方式（1.支付启动 2. 次卡启动 3. 储值启动）
     */
    private List<Integer> startWay;

    /**
     * 门店
     */
    @NotEmpty(message = "门店不能为空")
    private List<Long> stores;

    /**
     * 描述
     */
    private String description;

    /**
     * 关联设备类型 1 资产设备 2 IOT终端
     */
    private Integer associationType;

    private Boolean forceUpdate;

    /**
     * 关联设备列表
     */
    private List<Long> equipmentIds;

    /**
     * 免费时长
     */
    private Long freeTime;

    /**
     * 定价类型
     */
    private Integer pricingType;

    /**
     * 押金
     */
    private BigDecimal price;

    /**
     * 每24小时封顶金额
     */
    private BigDecimal dayMaxAmount;

    private String appid;

    /**
     * 渠道：wechat微信 alipay支付宝
     */
    private String channel;

    @NotEmpty(message = "计费信息不能为空")
    private List<ConsumerSolutionSaveDTO.StageTimeInfo> stageTimeList;

    @Data
    public static class StageTimeInfo {
        /**
         * 计费单价
         */
        private BigDecimal amount;

        /**
         * 计费时长
         */
        private BigDecimal stageTime;
        /**
         * 计费时长类型  1-分钟
         */
        private String stageTimeType;
        /**
         * 阶段排序
         */
        private Integer sort;
    }

}
