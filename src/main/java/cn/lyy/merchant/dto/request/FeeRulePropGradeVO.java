package cn.lyy.merchant.dto.request;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2019/11/27
 * @Version 1.0
 **/
@Data
@ToString
@EqualsAndHashCode
public class FeeRulePropGradeVO {
    /** 时间范围-开始，时：分 */
    private String value1;
    /** 时间范围-结束，时：分 */
    private String value2;
    /** 单价，元 ，当price1+price2大于0时忽略price价格，使用price1+price2 */
    private BigDecimal price;
    /** 单位 */
    private String unitText;
    /** 单价1，元 */
    private BigDecimal price1;
    /** 单价2，元 */
    private BigDecimal price2;

    public FeeRulePropGradeVO() {
    }
//    public FeeRulePropGradeVO(SaasFeeRulePropGrade entity) {
//        this.value1 = entity.getValue1();
//        this.value2 = entity.getValue2();
//        this.price = CommonUtil.centToYuan(entity.getPrice());
//        this.unitText = entity.getUnitText();
//        this.price1 = CommonUtil.centToYuan(entity.getPrice1());
//        this.price2 = CommonUtil.centToYuan(entity.getPrice2());
//    }
}
