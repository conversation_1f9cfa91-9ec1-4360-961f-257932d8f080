package cn.lyy.merchant.dto.request.baichuan;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/5/5
 */
@Data
public class ConsumerSolutionGroupReqDTO implements Serializable {

    /**
     * 消费方案id
     */
    @ApiModelProperty("消费方案id")
    private Long consumerSolutionId;

    /**
     * 场地ids
     */
    @ApiModelProperty("场地ids")
    private List<Long> groupIds;

    @ApiModelProperty("搜索关键字")
    private String queryStr;
}
