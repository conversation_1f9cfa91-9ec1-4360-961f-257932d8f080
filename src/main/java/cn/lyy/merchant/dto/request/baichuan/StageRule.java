package cn.lyy.merchant.dto.request.baichuan;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
public class StageRule {

    /**
     * 计费单价
     */
    @ApiModelProperty("计费单价")
//    @DecimalMin(value = "0.01", message = "计费单价需大于0")
    @NotNull(message = "计费单价为空")
    private BigDecimal amount;

    /**
     * 阶梯计算单位范围
     */
    @ApiModelProperty("阶梯计算单位范围")
//    @DecimalMin(value = "0.01", message = "计算单位范围需大于0")
    @NotNull(message = "计算单位范围为空")
    private BigDecimal stageCalculateRange;

    /**
     * 阶梯范围
     */
    @ApiModelProperty("阶梯范围")
    private BigDecimal stageRange;

    /**
     * 范围起始值
     */
    @ApiModelProperty("范围起始值")
//    @Min(value = 0, message = "阶梯范围起始值不能小于0")
    private BigDecimal rangeStart;

    /**
     * 范围结束值
     */
    @ApiModelProperty("范围结束值")
//    @DecimalMin(value = "0.01", message = "阶梯范围结束值需大于0")
    private BigDecimal rangeEnd;

    /**
     * 单位类型  1-分钟
     */
    @ApiModelProperty("单位类型  1-分钟")
    @NotNull(message = "计量单位为空")
    private Integer unit;

    /**
     * 阶段排序
     */
    @ApiModelProperty("阶段排序 0起始")
    @NotNull(message = "阶段排序为空")
    private Integer sort;
}