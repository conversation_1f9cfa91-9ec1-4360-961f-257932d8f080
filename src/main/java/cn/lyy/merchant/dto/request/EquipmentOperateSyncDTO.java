package cn.lyy.merchant.dto.request;

import lombok.Data;

import java.util.List;

/**
 * <p>Title:saas2</p>
 * <p>Desc: 设备操作（注册、解绑、转移）同步至工厂端</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020/5/26
 */
@Data
public class EquipmentOperateSyncDTO {

    /**
     * 商家id
     */
    private Long lyyDistributorId;
    /**
     * 设备码
     */
    private List<String> equipmentValues;
    /**
     * 操作类型 1注册 2解绑 3转移
     */
    private Integer operateType;

    public static EquipmentOperateSyncDTO create(Long lyyDistributorId, List<String> equipmentValues, Integer operateType) {
        EquipmentOperateSyncDTO syncDTO = new EquipmentOperateSyncDTO();
        syncDTO.lyyDistributorId = lyyDistributorId;
        syncDTO.equipmentValues = equipmentValues;
        syncDTO.operateType = operateType;
        return syncDTO;
    }
}
