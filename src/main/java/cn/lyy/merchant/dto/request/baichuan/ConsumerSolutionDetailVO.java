package cn.lyy.merchant.dto.request.baichuan;

import com.lyy.billing.interfaces.consumer.dto.baichuan.ConsumerSolutionVO.OtherSetting;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/5/5
 */
@Data
public class ConsumerSolutionDetailVO implements Serializable {

    @ApiModelProperty("消费方案ID")
    private Long consumerSolutionId;

    @ApiModelProperty("设备类型")
    private Long equipmentTypeId;

    @ApiModelProperty("设备类型名称")
    private String equipmentTypeName;

    @ApiModelProperty("方案名称")
    private String name;

    /**
     * 描述
     */
    private String description;

    /**
     * 配置id
     */
    @ApiModelProperty("配置id")
    private Long settingId;

    @ApiModelProperty("配置名称")
    private String settingName;

    /**
     * 计费类型（1.套餐模式 2.计量模式)
     */
    @ApiModelProperty("计费类型（1.套餐模式 2.计量模式)")
    private Integer model;

    /**
     * 基础设置（1.支持储值 2.支持信用分 3.支持押金）
     */
    @ApiModelProperty("基础设置（1.支持储值 2.支持信用分 3.支持押金）")
    private List<Integer> baseSetting;

    /**
     * 支付方式（1.支付 2. 次卡 3. 储值 5.支付分）
     */
    @ApiModelProperty("支付方式（1.支付 2. 次卡 3. 储值 5.支付分）")
    private List<Integer> startWay;

    /**
     * 其他配置
     */
    @ApiModelProperty("其他配置")
    private OtherSetting otherSetting;

    /**
     * 退款配置
     */
    @ApiModelProperty("退款配置")
    private RefundSetting refundSetting;

    /**
     * 是否配置下默认消费方案
     */
    @ApiModelProperty("是否配置下默认消费方案")
    private Boolean isDefault;

    @ApiModelProperty("储值套餐列表")
    private List<RechargeCommodity> rechargeCommodityList;

    @ApiModelProperty("启动套餐列表")
    private List<StartCommodity> startCommodityList;

    @ApiModelProperty("计量商品")
    private MeteringCommodity meteringCommodity;

    @ApiModelProperty("关联设备数量")
    private Long equipmentNum;

}
