package cn.lyy.merchant.dto.request;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>Title:saas2</p>
 * <p>Desc:计费规则详情 </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/11/28
 */
@Deprecated
@Data
public class FeeRuleDetailDTO implements Serializable {

    private static final long serialVersionUID = -406792932849287840L;

    /** 详情id，修改或刪除时传递 */
    private Long detailId;
    /** 工厂主板模式code */
    private String modeCode;
    /** 套餐标题，如脱水，快洗，标准等 */
    private String title;
    /** 单价，单位为元 */
    @NotNull(message = "价格不能为空！")
    private BigDecimal unitPrice;
    /** 取值(字符串，可以是取值范围) */
    @NotBlank(message = "取值不能为空！")
    private String unitValue;
    /** 取值的单位(次数：num, 电量：du, 分钟：min, 升：L, 毫升：ML) */
    @NotBlank(message = "取值单位不能为空！")
    private String unit;
    /** 是否启用 */
    private Boolean open;

    /** 取值类型（次数：num，电量：elec，时间：time */
    private String valueType;

    /** 附加参数 */
    private JSONObject prop;

}
