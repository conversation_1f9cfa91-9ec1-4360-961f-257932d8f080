package cn.lyy.merchant.dto.request;

import java.io.Serializable;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/3/14
 */
@Data
public class GroupConsumerSolutionReqDTO implements Serializable {

    /**
     * 设备类型ids
     */
    private List<Long> equipmentTypeList;

    /**
     * 场地ids
     */
    private List<Long> groupIds;

    /**
     * 设备标签ids
     */
    private List<Long> labels;

    /**
     * 地区ids
     */
    private List<Long> districtIds;

    private String queryStr;

    private Integer associationType;

    private Integer category;

    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空")
    private Long adUserId;
}
