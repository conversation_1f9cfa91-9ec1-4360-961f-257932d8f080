package cn.lyy.merchant.dto.request.baichuan;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/5/5
 */
@Data
public class BaichuanConsumerSolutionReqDTO {

    @ApiModelProperty("搜索关键字")
    private String keyword;

    @ApiModelProperty("设备类型")
    private Long equipmentTypeId;

    @ApiModelProperty("配置")
    private Long settingId;

    @ApiModelProperty("设备编号")
    private String equipmentValue;
}
