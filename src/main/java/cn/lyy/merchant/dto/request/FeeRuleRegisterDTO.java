package cn.lyy.merchant.dto.request;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <p>Title:saas2</p>
 * <p>Desc: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/11/26
 */
@Data
public class FeeRuleRegisterDTO implements Serializable {

    private static final long serialVersionUID = 5593962177061363728L;

    /** 设备id */
    @NotNull(message = "设备id不能为空！")
    private Long equipmentId;
    /** 设备value */
    @NotNull(message = "设备编码不能为空！")
    private String value;
    /** 场地id */
    @NotNull(message = "场地不能为空！")
    private Long groupId;
    /** 设备类型id */
    @NotNull(message = "设备类型不能为空！")
    private Long equipmentTypeId;
    /** 设备类型value */
    @NotBlank(message = "设备类型不能为空！")
    public String equipmentTypeValue;

    /** 计费规则标题 */
    private List<FeeRuleTitle> titles;


    @Data
    public static class FeeRuleTitle {
        /** 规则标题，如计费规则，加液计费规则 */
        @NotBlank(message = "规则标题不能为空！")
        private String title;
        /** 规则级别，如1,2 */
        @NotNull(message = "规则级别不能为空！")
        private Integer level;
        /** 计费规则参数，比如是否支持加液 */
        private JSONObject props;
        /** 计费规则详情，记录具体的规则 */
        private List<FeeRuleDetailDTO> details;
        /** 计费标准 */
        String feeMode;
        /** 分段计费 */
        private List<FeeRulePropGradeVO> grades;
    }

}
