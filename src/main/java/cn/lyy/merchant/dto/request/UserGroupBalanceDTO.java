package cn.lyy.merchant.dto.request;

import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 场地币信息
 */
@Getter
@Setter
@ToString
public class UserGroupBalanceDTO {

    /**
     * 场地id
     */
    private Long groupId;
    /**
     * 场地名称
     */
    private String groupName;

    /**
     * 充值余额
     */
    private BigDecimal amount;

    /**
     * 充值币
     */
    private BigDecimal coins;

    /**
     * 抽奖币
     */
    private BigDecimal discountCoins;

    /**
     * 广告币
     */
    private BigDecimal adCoins;

    /**
     * 免费闲时币
     */
    private BigDecimal idleCoinsFree;

    /**
     * 闲时币
     */
    private BigDecimal idleCoins;
}
