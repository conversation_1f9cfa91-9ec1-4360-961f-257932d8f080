package cn.lyy.merchant.dto.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @ClassName: IcCardBalanceChangeDTO
 * @description: IC卡变更
 * @author: pengkun
 * @create: 2020-11-05 14:41
 * @Version 1.0
 **/
@Setter
@Getter
@ToString
public class IcCardBalanceChangeDTO {
    /**
     * 卡号
     */
    String cardNo;
    /**
     * 总金额
     */
    BigDecimal amount;
    /**
     * 变更金额，不是总金额，正负数
     */
    BigDecimal changeAmount;
    /**
     * 创建日期，只用于列表展示
     */
    String created;
}
