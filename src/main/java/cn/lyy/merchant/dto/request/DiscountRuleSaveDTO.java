package cn.lyy.merchant.dto.request;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>Desc:计费规则保存 </p>
 *
 */
@Data
public class DiscountRuleSaveDTO implements Serializable {

    private static final long serialVersionUID = -406792932849287840L;

    /** 详情id，修改或刪除时传递 */
    private Long detailId;

    @ApiModelProperty("设备类型Id")
    @NotNull
    private Long equipmentTypeId;

    @ApiModelProperty("设备类型Value")
    private String equipmentTypeValue;

    @ApiModelProperty("场地ID")
   //@NotNull
    private Long groupId;

    /** 套餐标题，如脱水，快洗，标准等 */
    @ApiModelProperty("标题")
    private String title;

    /** 单价，单位为元 */
    @NotNull(message = "价格不能为空！")
    private BigDecimal price;
    /** 取值(字符串，可以是取值范围) */
    @NotNull(message = "取值不能为空！")
    private BigDecimal unitValue;
    /** 取值的单位(1 次/币，2 时长，3 电量，4 功率区间，5 时段，6 时段，7 件，8 币， 9时长电量) */
    @NotNull(message = "取值单位不能为空！")
    private Integer unit;

}
