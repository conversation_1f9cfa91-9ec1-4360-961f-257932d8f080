package cn.lyy.merchant.dto.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @createTime 2020-12-02
 * @auther peterguo
 * @Description 订单查询
 */
@Getter
@Setter
@ToString
public class OrderQueryRequest {

    /**
     * 搜索框数据 订单号/用户id/设备编号
     */
    private String searchWord;

    /**
     * 交易类型
     */
    private List<Integer> transType;

    /**
     * 支付类型
     */
    private List<Integer> payMode;

    /**
     * 支付渠道
     */
    private List<Integer> payClient;

    /**
     * 订单状态
     */
    private List<Integer> orderStatus;

    /**
     * 设备类型
     */
    private List<Long> equipmentTypeId;

    /**
     * 店铺id(包括场地和虚拟商铺)
     */
    private List<Long> storeId;

    /**
     * 开始时间
     */
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "开始时间不能为空")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "结束时间不能为空")
    private LocalDateTime endTime;

    private Integer pageSize = 10;

    private Integer pageIndex = 1;

    /**
     * 交易最小金额
     */
    private BigDecimal orderMinAmount;

    /**
     * 交易最大金额
     */
    private BigDecimal orderMaxAmount;


    /**
     * 经销商id
     */
    private Long distributorId;


}
