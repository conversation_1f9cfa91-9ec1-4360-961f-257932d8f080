package cn.lyy.merchant.dto.request.baichuan;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/5/5
 */
@Data
@ToString(callSuper = true)
public class BaichuanConsumerSolutionPageReqDTO extends BaichuanConsumerSolutionReqDTO{

    /**
     * 分页索引
     */
    @NotNull(message = "页码不能为空")
    @Max(value = 1000, message = "分页索引不能超过1000")
    private Long current;
    /**
     * 每页条数
     */
    @NotNull(message = "页条数不能为空")
    @Max(value = 100, message = "每页条数不能超过100")
    private Long size;

}
