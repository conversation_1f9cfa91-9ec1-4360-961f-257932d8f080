package cn.lyy.merchant.dto.request;

import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2019/8/24
 * @Version 1.0
 **/
@Data
@ToString
public class EquipmentRuleDTO {
    /** 规则id , 新增不需要填写，修改时必填 */
    private Long id;
    /** 金额 */
    private BigDecimal price;
    /** 币数、送多少元等对应值 */
    private BigDecimal value;
    /** 是否删除该规则，true|false */
    private Boolean delete;
    /** 标题，暂时只用于计费规则 */
    private String title;
}
