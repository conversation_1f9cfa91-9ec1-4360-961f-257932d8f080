package cn.lyy.merchant.dto.request;

import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2019/8/24
 * @Version 1.0
 **/
@Data
@ToString
public class DiscountDTO {
    /** 规则类型 , 1: 充X元送Y元 */
    Long ruleType;
    /** 规则 */
    @NotNull(message = "充值规则详情不能为空！")
    List<EquipmentRuleDTO> rules;
}
