package cn.lyy.merchant.dto.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR> {<EMAIL>}
 * @date 2020/7/20 14:12
 **/
@Getter
@Setter
@ToString
public class MerchantEquipmentRequest {

    /**
     * 商户账号
     */
    private Long adUser;

    /**
     * 商户
     */
    private Long distributor;

    /**
     * 是否主账号
     */
    private Boolean primary;

    /**
     * 场地列表
     */
    private List<Long> groups;

    /**
     * 设备类型
     */
    private List<Long> equipmentType;

    /**
     * 标签
     */
    private List<Long> labels;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 搜索内容
     */
    private String context;

    /**
     * 设备种类
     */
    private String deviceType;

    /**
     * 设备类型名称
     */
    private List<String> equipmentTypeValues;
}
