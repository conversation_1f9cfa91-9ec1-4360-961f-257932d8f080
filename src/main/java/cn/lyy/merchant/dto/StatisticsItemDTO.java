package cn.lyy.merchant.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @ClassName: StatisticsItemDTO
 * @description: ic卡统计
 * @author: pengkun
 * @create: 2020-11-05 13:42
 * @Version 1.0
 **/
@Setter
@Getter
@ToString
public class StatisticsItemDTO {
    /**
     * 日期
     */
    private String date;
    /**
     * 消费金额
     */
    private BigDecimal amount;
    /**
     * 卡数
     */
    private Integer cardCount;
    /**
     * 充值金额
     */
    private BigDecimal reChargeAmount;
}
