package cn.lyy.merchant.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2019/8/15
 * @Version 1.0
 **/
@Data
@ApiModel("分页记录")
@AllArgsConstructor
public class ListVO<T> implements Serializable {
    @ApiModelProperty("请求页码")
    private Long page;
    @ApiModelProperty("页记录")
    private Long size;
    @ApiModelProperty("总记录数")
    private Long total;
    @ApiModelProperty("当页记录列表")
    private List<T> data;

}
