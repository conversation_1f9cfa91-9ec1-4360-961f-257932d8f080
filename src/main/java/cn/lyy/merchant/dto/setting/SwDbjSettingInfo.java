package cn.lyy.merchant.dto.setting;


import cn.lyy.merchant.util.YTQUtils;

/**
 * 松旺兑币机设备设置数据
 */
public class SwDbjSettingInfo {

    /**
     * 1元出币
     */
    public int oneCoins;

    /**
     * 5元出币
     */
    public int fiveCoins;

    /**
     * 10元出币
     */
    public int tenCoins;

    /**
     * 20元出币
     */
    public int twentyCoins;

    /**
     * 50元出币
     */
    public int fiftyCoins;

    /**
     * 100元出币
     */
    public int hundredCoins;

    public SwDbjSettingInfo(int oneCoins, int fiveCoins, int tenCoins, int twentyCoins, int fiftyCoins, int hundredCoins) {
        this.oneCoins = oneCoins;
        this.fiveCoins = fiveCoins;
        this.tenCoins = tenCoins;
        this.twentyCoins = twentyCoins;
        this.fiftyCoins = fiftyCoins;
        this.hundredCoins = hundredCoins;
    }

    public byte[] getBytes(int machineType) {
        byte[] packet = new byte[14];
        packet[0] = (byte) machineType;//设备类型:32-松旺兑币机 38-谷粒兑币机
        packet[1] = (byte) 12;//参数有效长度
        YTQUtils.into2Packet(packet, 2, oneCoins);
        YTQUtils.into2Packet(packet, 4, fiveCoins);
        YTQUtils.into2Packet(packet, 6, tenCoins);
        YTQUtils.into2Packet(packet, 8, twentyCoins);
        YTQUtils.into2Packet(packet, 10, fiftyCoins);
        YTQUtils.into2Packet(packet, 12, hundredCoins);
        return packet;
    }
}
