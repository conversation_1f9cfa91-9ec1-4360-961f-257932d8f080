package cn.lyy.merchant.dto.setting;


import cn.lyy.merchant.util.YTQUtils;

/**
 * Created by john on 2016/10/28.
 */
public class SettingInfo {
    public int machineType;
    public int dataLength;
    public int language;
    public int modeltype;
    public int throwcoins;
    public int gameAmount;
    public int gameBase;
    public int winCount;
    public int gameTime;
    public int telekinesis;
    public int musicSwitch;
    public int autoDemoTime;
    public int bootClear;
    public int firstStrength;
    public int secondStrength;
    public int thirdStrength;
    public int fourthStrength;
    public int endStrength;
    public int waiteTimeBelow;
    public int waiteTimeUp;
    public int outPosition;
    public int startPosition;
    public int frontSpeed;
    public int leftSpeed;
    public int upSpeed;
    public int downSpeed;
    public int downTime;
    public int spare;
    public int changeTime;
    public int exchangeFunction;
    public int bill;
    public int coins;
    public int firstMeal;
    public int secondMeal;
    public int thirdMeal;
    public int fourthMeal;
    public int fifthMeal;
    public int fixedMoney;
    public int games;
    public int money;
    public int addSwitch;
    public int addTime;
    public int addShowTime;
    public int addClear;
    public int needCoins;
    public int salesSwitch;
    public int salesRandom;
    public int forceCatch;
    public int moneyPlusSwitch;

    public SettingInfo() {
    }

    public SettingInfo(int machineType, int dataLength, int language, int modeltype, int throwcoins, int gameAmount, int gameBase, int winCount, int gameTime, int telekinesis, int musicSwitch, int autoDemoTime, int bootClear, int firstStrength, int secondStrength, int thirdStrength, int fourthStrength, int endStrength, int waiteTimeBelow, int waiteTimeUp, int outPosition, int startPosition, int frontSpeed, int leftSpeed, int upSpeed, int downSpeed, int downTime, int spare, int changeTime, int exchangeFunction, int bill, int coins, int firstMeal, int secondMeal, int thirdMeal, int fourthMeal, int fifthMeal, int fixedMoney, int games, int money, int addSwitch, int addTime, int addShowTime, int addClear, int needCoins, int salesSwitch, int salesRandom, int forceCatch, int moneyPlusSwitch) {
        this.machineType = machineType;
        this.dataLength = dataLength;
        this.language = language;
        this.modeltype = modeltype;
        this.throwcoins = throwcoins;
        this.gameAmount = gameAmount;
        this.gameBase = gameBase;
        this.winCount = winCount;
        this.gameTime = gameTime;
        this.telekinesis = telekinesis;
        this.musicSwitch = musicSwitch;
        this.autoDemoTime = autoDemoTime;
        this.bootClear = bootClear;
        this.firstStrength = firstStrength;
        this.secondStrength = secondStrength;
        this.thirdStrength = thirdStrength;
        this.fourthStrength = fourthStrength;
        this.endStrength = endStrength;
        this.waiteTimeBelow = waiteTimeBelow;
        this.waiteTimeUp = waiteTimeUp;
        this.outPosition = outPosition;
        this.startPosition = startPosition;
        this.frontSpeed = frontSpeed;
        this.leftSpeed = leftSpeed;
        this.upSpeed = upSpeed;
        this.downSpeed = downSpeed;
        this.downTime = downTime;
        this.spare = spare;
        this.changeTime = changeTime;
        this.exchangeFunction = exchangeFunction;
        this.bill = bill;
        this.coins = coins;
        this.firstMeal = firstMeal;
        this.secondMeal = secondMeal;
        this.thirdMeal = thirdMeal;
        this.fourthMeal = fourthMeal;
        this.fifthMeal = fifthMeal;
        this.fixedMoney = fixedMoney;
        this.games = games;
        this.money = money;
        this.addSwitch = addSwitch;
        this.addTime = addTime;
        this.addShowTime = addShowTime;
        this.addClear = addClear;
        this.needCoins = needCoins;
        this.salesSwitch = salesSwitch;
        this.salesRandom = salesRandom;
        this.forceCatch = forceCatch;
        this.moneyPlusSwitch = moneyPlusSwitch;
    }


    public byte[] getBytes() {
        byte[] packet = new byte[63];
        packet[0] =  (byte) 31;//设备类型:31-松旺娃娃机
        packet[1] = (byte) dataLength;//参数有效长度
        packet[2] = (byte) language;//语言选择
        packet[3] = (byte) modeltype;//机台模式
        packet[4] = (byte) throwcoins;//投?币
        packet[5] = (byte) gameAmount;//游戏?局
        YTQUtils.into2Packet(packet, 6, gameBase);//游戏基数
        packet[8] = (byte) winCount; //1   游戏中奖次数
        packet[9] = (byte) gameTime; //30  游戏时间
        packet[10] = (byte) telekinesis;//1   隔空取物
        packet[11] = (byte) musicSwitch; //0   背景音乐开关
        packet[12] = (byte) autoDemoTime;//0   自动演示间隔时间
        packet[13] = (byte) bootClear; //1   开机投币清零
        YTQUtils.into2Packet(packet, 14, firstStrength);//400  第1段爪力
        YTQUtils.into2Packet(packet, 16, secondStrength);//200  第2段爪力
        YTQUtils.into2Packet(packet, 18, thirdStrength);//100  第3段爪力
        YTQUtils.into2Packet(packet, 20, fourthStrength);//120  第4段爪力
        YTQUtils.into2Packet(packet, 22, endStrength);//400  保夹爪力
        packet[24] = (byte) waiteTimeBelow;//8   爪子在底部停留时间
        packet[25] = (byte) waiteTimeUp; //4   爪子在顶部停留时间
        packet[26] = (byte) outPosition; //0   出物口位置
        packet[27] = (byte) startPosition;//0   爪子起始位置
        packet[28] = (byte) frontSpeed;//7   前后电机速度
        packet[29] = (byte) leftSpeed;//7   左右电机速度
        packet[30] = (byte) upSpeed;//9   爪子电机向上速度
        packet[31] = (byte) downSpeed;//9   爪子电机向下速度
        packet[32] = (byte) downTime;//18  爪子向下时间
        packet[33] = (byte) spare;//0   备用 固定0
        packet[34] = (byte) changeTime;//5   上升爪力变化时间
        packet[35] = (byte) exchangeFunction;//0  兑币功能
        packet[36] = (byte) bill;//1  纸钞数
        packet[37] = (byte) coins;//1  币数
        YTQUtils.into2Packet(packet, 38, firstMeal); //0  1.赠币套餐1
        YTQUtils.into2Packet(packet, 40, secondMeal);//0  2.赠币套餐2
        YTQUtils.into2Packet(packet, 42, thirdMeal);//0  3.赠币套餐3
        YTQUtils.into2Packet(packet, 44, fourthMeal); //0  4.赠币套餐4
        YTQUtils.into2Packet(packet, 46, fifthMeal);//0  5.赠币套餐5
        YTQUtils.into2Packet(packet, 48, fixedMoney);//20  礼品保夹金额
        packet[50] = (byte) games;//1  局数
        packet[51] = (byte) money;//2  金额
        packet[52] = (byte) addSwitch;//0  累加显示开关
        packet[53] = (byte) addTime;//5  累加保留时间
        packet[54] = (byte) addShowTime;//3  累加显示时间
        packet[55] = (byte) addClear;//0  累加归零
        packet[56] = (byte) needCoins;//0  保夹再投币
        packet[57] = (byte) salesSwitch;//0  促销开关
        YTQUtils.into2Packet(packet, 58, salesRandom);//10 促销
        packet[60] = (byte) forceCatch;//0 强爪从第几次开始
        packet[61] = (byte) moneyPlusSwitch;//0  金额板累加显示开关
        return packet;
    }
}
