package cn.lyy.merchant.dto.setting;


import cn.lyy.merchant.util.YTQUtils;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by john on 2016/10/28.
 */
@Getter
@Setter
public class XnSettingInfo {
    public int machineType;//设备类型
    public int dataLength;//参数区有效长度
    public int standbyMusic;//待机音乐  0关闭，1～4第几首待机音乐
    public int throwcoins;//投?币    范围(1至100)
    public int gameAmount;//游戏?局	 范围(1至100)
    public int gameTime;//游戏时间 5～60秒
    public int telekinesis;//空中抓物 0关闭，1开启
    public int gameModel;//游戏模式
    public int winCount;//中奖概率 1～250
    public float strongVoltage;//强抓力电压 整数部分+小数部分，15.0～47.5
    public float weakVoltage;//弱抓力电压 整数部分+小数部分，15.0～47.5
    public float strongVoltageTime;//强抓力维持时间 整数部分+小数部分，0.1～3.0
    public int toTheWeak;//到顶转弱 0关闭，1开启
    public int reserveGameAcountSwitch;//局数开机保留 0关闭，1开启
    public int giveGameAcount;//连投几局送一局 0关闭，1～60表示连续投几个币就送一个币

    public XnSettingInfo() {
    }

    public XnSettingInfo(int machineType, int dataLength, int standbyMusic, int throwcoins, int gameAmount, int gameTime, int telekinesis, int gameMode, int winCount, float strongVoltage, float weakVoltage, float strongVoltageTime, int toTheWeak, int reserveGameAcountSwitch, int giveGameAcount) {
        this.machineType = machineType;
        this.dataLength = dataLength;
        this.standbyMusic = standbyMusic;
        this.throwcoins = throwcoins;
        this.gameAmount = gameAmount;
        this.gameTime = gameTime;
        this.telekinesis = telekinesis;
        this.gameModel = gameMode;
        this.winCount = winCount;
        this.strongVoltage = strongVoltage;
        this.weakVoltage = weakVoltage;
        this.strongVoltageTime = strongVoltageTime;
        this.toTheWeak = toTheWeak;
        this.reserveGameAcountSwitch = reserveGameAcountSwitch;
        this.giveGameAcount = giveGameAcount;
    }

    public byte[] getBytes() {
        byte[] packet = new byte[19];
        packet[0] = (byte) 35;//设备类型
        packet[1] = (byte) dataLength;//参数有效长度
        packet[2] = (byte) standbyMusic;//待机音乐  0关闭，1～4第几首待机音乐
        packet[3] = (byte) throwcoins;//投?币    范围(1至100)
        packet[4] = (byte) gameAmount;//游戏?局	 范围(1至100)
        packet[5] = (byte) gameTime;//游戏时间 5～60秒
        packet[6] = (byte) telekinesis;//空中抓物 0关闭，1开启
        packet[7] = (byte) gameModel;//游戏模式
        YTQUtils.into2Packet(packet, 8, winCount);//中奖概率 1～250
        YTQUtils.floatTo2Packet(packet, 10, strongVoltage);//强抓力电压 整数部分+小数部分，15.0～47.5
        YTQUtils.floatTo2Packet(packet, 12, weakVoltage);//弱抓力电压 整数部分+小数部分，15.0～47.5
        YTQUtils.floatTo2Packet(packet, 14, strongVoltageTime);//强抓力维持时间 整数部分+小数部分，0.1～3.0
        packet[16] = (byte) toTheWeak;//到顶转弱 0关闭，1开启
        packet[17] = (byte) reserveGameAcountSwitch;//局数开机保留 0关闭，1开启
        packet[18] = (byte) giveGameAcount;//连投几局送一局 0关闭，1～60表示连续投几个币就送一个币
        return packet;
    }
}
