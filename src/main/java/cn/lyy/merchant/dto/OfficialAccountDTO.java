package cn.lyy.merchant.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class OfficialAccountDTO implements Serializable {

    private static final long serialVersionUID = -7383408279298008507L;
    
    /**
     * 主键id
     */
    private Long lyyOfficialAccoutId;

    private Long lyyCustomerId;

    /**
     * 公众号账号
     */
    private String officialAccout;

    /**
     * 公众号名称
     */
    private String officialAccoutName;

    /**
     * 公众号api_key
     */
    private String apiKey;

    /**
     * 公众号secret_key
     */
    private String secretKey;

    /**
     * 公众号app_id
     */
    private String appId;

    /**
     * 公众号app_secret
     */
    private String appSecret;

    /**
     * 描述
     */
    private String description;

    /**
     * access_token
     */
    private String accessToken;

    /**
     * refresh_toker
     */
    private String refreshToken;

    /**
     * 是否鉴权
     */
    private String isauthorized;

    /**
     * 二维码
     */
    private String qrCode;

    /**
     * 广告主id
     */
    private Long lyyAdvertiserId;

    /**
     * 业务类型：PRE-前置关注公众号、ADVERT-广告业务公众号
     */
    private String businesstype;

    /**
     * 关注链接
     */
    private String followUrl;

    private String authorizationExpiresDate;


    private Integer platformType;

    /**
     * 公众号类型，FWH:服务号
     */
    private String type;

    private Integer verifyTypeInfo;

    /**
     * 方法信息?
     */
    private String funcInfo;
}
