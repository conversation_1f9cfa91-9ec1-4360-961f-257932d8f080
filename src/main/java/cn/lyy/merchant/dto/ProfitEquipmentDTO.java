package cn.lyy.merchant.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>Title:saas2</p>
 * <p>Desc: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/8/29
 */
@Data
@ApiModel(value = "设备收益统计")
@ToString
public class ProfitEquipmentDTO {

    @ApiModelProperty(value = "线下投币数")
    private Integer offlineCoins;

    @ApiModelProperty(value = "线上收益")
    private BigDecimal amount;

    @ApiModelProperty(value = "线下现金消费")
    private BigDecimal offlineAmount;

    @ApiModelProperty(value = "统计明细")
    List<NameValueVO> amounts;
    @ApiModelProperty(value = "设备id")
    private Integer equipmentId;
    @ApiModelProperty(value = "设备编号")
    private String equipmentValue;

    @ApiModelProperty(value = "设备备注")
    private String equipmentRemark;
}
