package cn.lyy.merchant.dto;

import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> @date 2020/10/22
 * @title
 */
@Data
@ToString
public class WashFaultEquipmentTDO {
    /**
     * 设备ID
     */
    @NotNull(message = "设备id为空")
    private Long equipmentId;
    /**
     * 设备编号为空
     */
    @NotNull(message = "设备编号为空")
    private String equipmentValue;
    /**
     * 设备唯一码为空
     */
    @NotNull(message = "设备编号为空")
    private String uniqueCode;
    // 前端传递过来的位置
    private Integer deviceListIndex;
}
