package cn.lyy.merchant.dto.member;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @description:
 * @author: qgw
 * @date on 2021-06-07.
 * @Version: 1.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class BenefitDetailQueryDTO extends BenefitQueryDTO {

    /**
     * 页码
     */
    @Range(min = 1, message = "页码参数错误")
    @NotNull(message = "页码必须传值")
    private Integer pageIndex;

    /**
     * 数据量
     */
    @Range(min = 10, message = "页码参数错误")
    @NotNull(message = "页码size必须传值")
    private Integer pageSize;

    private List<String> notHandleBenefitIds;
}
