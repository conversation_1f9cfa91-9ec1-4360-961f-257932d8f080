package cn.lyy.merchant.dto.member;

import cn.lyy.merchant.util.StringArrayToLongDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.deser.std.CollectionDeserializer;
import com.lyy.user.account.infrastructure.base.LongFromStringDeserializer;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 派发福利DTO
 * <AUTHOR>
 * @create 2021/6/19 16:11
 */
@Data
public class PayoutWelfareDTO {

    /**
     * 商户用户ID 集合
     */
    @JsonDeserialize(using = StringArrayToLongDeserializer.class)
    private List<Long> merchantUserIdList;

    /**
     * 是否是全部用户
     */
    private Boolean isAllUser;

    /**
     * 全用户剔除之外的用户
     */
    @JsonDeserialize(using = StringArrayToLongDeserializer.class)
    private List<Long> exceptMerchantUserIdList;

    /**
     * 商户操作人ID
     */
    private Long adUserId;

    /**
     * 是否全品类
     */
    private Boolean isAllEquipmentType;

    /**
     * 品类ID
     */
    private List<Long> equipmentTypeIdList;

    /**
     *  是否全场地
     */
    private Boolean isAllGroup;

    /**
     * 生效场地
     */
    private List<Long> groupIdList;

    /**
     * 赠送余额
     */
    @Max(value = 999)
    private BigDecimal payoutBalance;

    /**
     * 赠送余币
     */
    @Max(value = 999)
    private BigDecimal payoutCoin;

    /**
     * 搜索关键字  支持微信昵称，会员ID,手机号码
     */
    private String keyword;

    /**
     * 场地标签集合
     */
    private List<Long> groupTagIdList;

    /**
     * 普通标签
     */
    private List<Long> otherTagIdList;
    /**
     * 设备类型标签
     */
    private List<Long> equipmentTypeTagIdList;

    /**
     * 性别标签
     */
    @JsonDeserialize(using = StringArrayToLongDeserializer.class)
    private List<Long> sexTagIdList;

    /**
     * 累计消费最小值
     */
    private Integer totalConsumeMoneyMin;

    /**
     * 累计消费最大值
     */
    private Integer totalConsumeMoneyMax;

    /**
     * 余额最小值
     */
    private Integer totalBalanceMin;

    /**
     * 余额最大值
     */
    private Integer totalBalanceMax;

    /**
     * 余币最小值
     */
    private Integer totalCoinMin;

    /**
     * 余币最大值
     */
    private Integer totalCoinMax;

}
