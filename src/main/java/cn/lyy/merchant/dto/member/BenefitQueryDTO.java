package cn.lyy.merchant.dto.member;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.lyy.user.account.infrastructure.base.LongFromStringDeserializer;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: qgw
 * @date on 2021-06-07.
 * @Version: 1.0
 */
@Getter
@Setter
@ToString
public class BenefitQueryDTO  {


    /**
     * 商家用户id
     */
    @NotNull(message = "商家用户id必须传值")
    @JsonSerialize(using = ToStringSerializer.class)
    @JsonDeserialize(using = LongFromStringDeserializer.class)
    private Long merchantUserId;


    /**
     * 场地id
     */
    private List<Long> storeIds;

    /**
     * 设备类型id
     */
    private List<Long> equipmentTypeIds;


    /**
     * 权益ID集合 -调整记录id（权益账户表ID）
     */
    private List<String> benefitIds;

    /**
     * 权益分组类型
     * 1币
     * 2余额
     * 3券类型
     */
    private Integer benefitGroupType;

    /**
     * 权益类型值
     */
    private Integer classify;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * true 选择全部
     */
    private Boolean  chooseAll;

    /**
     * 不处理的用户IDs
     */
    private List<Long> notHandleBenefitIdList;


    /**
     * 不查的权益类型
     */
    private List<Integer> excludeClassify;

}
