package cn.lyy.merchant.dto.equipment;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>Title:saas2</p>
 * <p>Desc: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/10/16
 */
@Data
@ApiModel("设备参数")
public class EquipmentExtenddParamSettingDTO implements Serializable {

    private static final long serialVersionUID = -2457895988140498964L;

    @ApiModelProperty("设备号")
    private String deviceNo;
    @ApiModelProperty("设置指令")
    private Integer cmd;
    @ApiModelProperty("查询超时时间，为空时，默认为10")
    private Integer timeout;
    @ApiModelProperty("参数设置列表信息")
    private List<EquipmentExtendParamJsonDTO.ButtonsBean.ParamsBean> paramList;

}
