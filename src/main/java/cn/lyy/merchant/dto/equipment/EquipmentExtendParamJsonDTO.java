package cn.lyy.merchant.dto.equipment;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>Title:saas2</p>
 * <p>Desc: 设备扩展参数 DTO</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/10/16
 */
@Data
public class EquipmentExtendParamJsonDTO implements Serializable {

    private static final long serialVersionUID = -3260272384754675174L;

    private String brand;
    private String uniqueCode;
    /**
     * 设备类型
     */
    private String machineType;
    private Long boardId;
    private int paramsLength;
    private String isGroup;
    private List<ButtonsBean> buttons;

    @Data
    public static class ButtonsBean {

        private Long buttonId;

        private String name;
        /**
         * 查询码
         */
        private Integer queryFunctionCode;
        /**
         * 功能设置码
         */
        private Integer functionCode;
        /**
         * 是否刷新
         */
        private String isRefresh;
        private String disabled;
        /**
         * 参数长度
         */
        private int paramsLength;
        /**
         * 子参数
         */
        private List<ParamsBean> params;

        @Data
        @ApiModel("参数")
        public static class ParamsBean {
            private int id;
            private String name;
            private int length;
            @ApiModelProperty("是否可见")
            private String isVisible;
            @ApiModelProperty("是否可用")
            private String disabled;
            private String description;
            @ApiModelProperty("控件类型 switch(开关), select(列表), inputFloat(输入浮点数), inputInt(输入整数)")
            private String componentType;
            @ApiModelProperty("控件值类型 int或int2(分段定义)或float1(高低位方式)或float2(乘10方式) negativeint(支持负整数) hex(16进制类型，但是值存的是10进制的值)")
            private String componentValueType;
            @ApiModelProperty("值")
            private String componentValue;
            @ApiModelProperty("单位")
            private String componentValueUnit;
            private String componentJson;
//            private Range componentValueRange;
//            private Switch componentValueSwitch;
//            private Array componentValueArray;

            /**
             * 设置浮点数、整数范围
             */
            @Data
            public static class ComponentValueRange {
                private String min;
                private String max;
            }

            /**
             * 设置开关对应值
             */
            @Data
            public static class ComponentValueSwitch {
                private String open;
                private String close;
            }

            /**
             * 设置列表内容
             */
            @Data
            public static class ComponentValueArray {
                private String value;
                private String text;

                public ComponentValueArray(String value, String text) {
                    this.value = value;
                    this.text = text;
                }
            }
        }
    }
}
