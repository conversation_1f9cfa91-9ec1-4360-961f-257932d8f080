package cn.lyy.merchant.dto.multistore;

import cn.lyy.merchant.constants.MultiStoreBizTypeEnum;
import cn.lyy.merchant.dto.equipment.rule.DivideRuleGroupEquipmentInfoRequestDTO;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import cn.lyy_dto.wash.multistore.request.MultiStoreListReqDTO;
import com.lyy.payment.merchant.dto.divide.MerchantDivideStatisticsQueryRequestDTO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2025/6/11 星期三 14:51
 * @description:
 */
@Setter
@Getter
@ToString(callSuper = true)
public class MultiStoreListBizReqDTO extends MultiStoreListReqDTO {

    private AdUserInfoDTO adUserDTO;
    /**
     * @see MultiStoreBizTypeEnum
     * 默认新加DTO 命名格式为:  bizType + "ReqDTO"
     */

    private DivideRuleGroupEquipmentInfoRequestDTO divideRuleStoreReqDTO;


    private String  bizType;



}
