package cn.lyy.merchant.dto.response;

import cn.lyy.merchant.constants.FeeModeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2019/11/20
 */

@ApiModel("计费规则-选项列表")
@Data
public class FeeModeVO {

    @ApiModelProperty("计费标准值: TIME, ELEC")
    private String code;

    @ApiModelProperty("计费标准名称: TIME-按时间计费, ELEC-按电量计费，DYNAMIC-按初始功率计费")
    private String name;

    @ApiModelProperty("计费标准解释")
    private String unitDesc;


    public static FeeModeVO ofFeeModeEnum(String feeMode) {

        FeeModeEnum feeModeEnum = FeeModeEnum.getFeeModeByCode(feeMode);

        FeeModeVO feeModeVO = new FeeModeVO();
        feeModeVO.setCode(feeModeEnum.getCode());
        feeModeVO.setName(feeModeEnum.getName());
        feeModeVO.setUnitDesc(feeModeEnum.getUnitDesc());

        return feeModeVO;
    }
}
