package cn.lyy.merchant.dto.response;

import cn.lyy.equipment.dto.equipment.EquipmentTypeDTO;
import cn.lyy.merchant.dto.merchant.response.MerchantGroupDTO;
import cn.lyy.merchant.dto.request.FeeCommodityDTO;
import cn.lyy.merchant.dto.template.EquipmentRegisterTmplDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

import java.util.List;

/** 
 * 设备扫码注册出参 
 **/

@Data
public class BindCheckDTO{

    /** 设备码 */
    private String code;
    /** 设备id */
    private Long equipmentId;
    /** 设备品类信息 */
    private EquipmentTypeDTO equipmentType;
    /** 设备场地信息 */
    private MerchantGroupDTO equipmentGroup;
    /** 设备计费单位列表 */
    private List<FeeRuleUnitDTO> feeUnitList;
    /** 计费规则 **/
    private List<FeeCommodityDTO> feeRuleList;

    /**
     * 附加规则，用于一个设备 对应其他的设备类型计费规则
     */
    private List<FeeCommodityDTO> attachFeeRuleList;
    /**
     * 计费模式列表
     */
    private List<FeeModeVO> feeModeList;
    /** 设备注册模板 */
    private EquipmentRegisterTmplDTO registerTmpl;
    /** 是否支持批量 */
    private Boolean ifBatchRegister;
    /** 产品id */
    private Long productId;
    /** 协议id */
    private Long protocolId;

    /** 备注信息 */
    private String remarks;

    /** 场地机台号 */
    private Integer groupNumber;

    /**
     * 1=可注册,2=已注册
     * @see RegisterStatus
     */
    private Integer registerStatus;

    /**
     * 计费规则是否展示标题
     */
    private Boolean showTitle;

    /**
     * 计费规则是否展示单位
     */
    private Boolean showUnit;

    /**
     * 扩展信息
     */
    private String extendParam;

    @Getter
    @AllArgsConstructor
    public enum RegisterStatus {
        /**
         * 注册状态
         */
        AVAILABLE(1, "可注册"),
        REGISTERED(2, "已被注册");

        private Integer status;
        private String msg;
    }
}