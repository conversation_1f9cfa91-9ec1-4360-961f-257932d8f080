package cn.lyy.merchant.dto.response;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/3/7
 */
@Data
public class ConsumerSolutionDetailDTO implements Serializable {

    /**
     * 消费方案ID
     */
    private Long consumerSolutionId;

    private Integer category;

    /**
     * 设备类型
     */
    private Long equipmentType;

    /**
     * 方案名称
     */
    private String name;

    /**
     * 关联设备数
     */
    private Long equipmentCount;

    /**
     * 描述
     */
    private String description;

    /**
     * 押金
     */
    private BigDecimal deposit;

    /**
     * 免费时长
     */
    private Long freeTime;

    /**
     * 定价类型
     */
    private Integer pricingType;

    private Long commodityId;

    /**
     * 适用门店
     */
    private List<Long> stores;

    /**
     * 押金
     */
    private BigDecimal price;

    /**
     * 每24小时封顶金额
     */
    private BigDecimal dayMaxAmount;

    @NotEmpty(message = "计费信息不能为空")
    private List<ConsumerSolutionDetailDTO.StageTimeInfo> stageTimeList;

    @Data
    public static class StageTimeInfo {
        /**
         * 计费单价
         */
        private BigDecimal amount;

        /**
         * 计费时长
         */
        private BigDecimal stageTime;
        /**
         * 计费时长类型  1-分钟
         */
        private String stageTimeType;
        /**
         * 阶段排序
         */
        private Integer sort;
    }
}
