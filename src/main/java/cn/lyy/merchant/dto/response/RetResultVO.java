package cn.lyy.merchant.dto.response;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
@Data
@ToString
public class RetResultVO<T> implements Serializable {


    private static final long serialVersionUID = 7577304394129121548L;
    private int result = 0;
    private String description = "";
    private T data;

    public RetResultVO() {
    }

    public static RetResultVO create(Object data){
        RetResultVO ret = new RetResultVO();
        ret.setResult(0);
        ret.setDescription("success");
        ret.setData(data);
        return ret;
    }
    public static RetResultVO create(int code , String msg){
        RetResultVO ret = new RetResultVO();
        ret.setResult(code);
        ret.setDescription(msg);
        ret.setData(null);
        return ret;
    }
    public static RetResultVO create(int code, String msg, Object data){
        RetResultVO ret = new RetResultVO();
        ret.setResult(code);
        ret.setDescription(msg);
        ret.setData(data);
        return ret;
    }


    public boolean success(){
        return result == 0;
    }
}
