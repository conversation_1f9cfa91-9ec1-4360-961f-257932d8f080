package cn.lyy.merchant.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.Map;

/**
 * @ClassName: MerchantShopNoticeResponseDTO
 * @description: TODO
 * @author: pengkun
 * @create: 2020-11-16 16:59
 * @Version 1.0
 **/
@Setter
@Getter
@ToString
public class MerchantShopNoticeResponseDTO {

    /**
     * 公告id
     */
    private Long noticeId;
    /**
     * 公告内容
     */
    private String content;
    /**
     * 标题
     */
    private String title;
    /**
     * 生成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date create;
    /**
     * 关联店铺
     */
    private Map<Long, String> groups;
}
