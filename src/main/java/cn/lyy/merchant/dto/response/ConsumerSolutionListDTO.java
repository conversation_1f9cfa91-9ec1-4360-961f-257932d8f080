package cn.lyy.merchant.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lyy.billing.interfaces.consumer.dto.ConsumerSolutionCommodityDTO;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR>
 * @date 2021/03/15
 */
@Data
public class ConsumerSolutionListDTO {

    /**
     * 消费方案名称
     */
    private String name;

    private Integer category;

    private Long equipmentType;

    private String equipmentTypeName;

    /**
     * 描述
     */
    private String description;

    /**
     * 消费方案ID
     */
    private Long id;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 门店数
     */
    private Integer storeNum;

    /**
     * 设备数
     */
    private Integer equipmentNum;

    private Long freeTime;

    private Integer pricingType;

    /**
     * 押金
     */
    private BigDecimal price;

    /**
     * 每24小时封顶金额
     */
    private BigDecimal dayMaxAmount;

    private List<ConsumerSolutionListDTO.StageTimeInfo> stageTimeList;

    @Data
    public static class StageTimeInfo {
        /**
         * 计费单价
         */
        private BigDecimal amount;

        /**
         * 计费时长
         */
        private BigDecimal stageTime;
        /**
         * 计费时长类型  1-分钟
         */
        private String stageTimeType;
        /**
         * 阶段排序
         */
        private Integer sort;
    }

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updated;
}
