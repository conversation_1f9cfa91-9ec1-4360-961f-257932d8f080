package cn.lyy.merchant.dto.response;

import cn.lyy.open.order.dto.request.OrderDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.lyy.advert.api.internal.dto.cdz.LyyInsuranceOrderDTO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * @createTime 2020-12-02
 * @auther peterguo
 *
 * @Description 订单详情
 */
@Getter
@Setter
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OrderInfoDetailDTO {

    /**
     * 支付类型
     */
    private Integer payMode;

    /**
     * 交易类型
     */
    private Integer transType;

    /**
     * 设备相关
     */
    private OrderDTO.EquipmentDetail equipmentDetail;

    /**
     * 会员相关
     */
    private OrderInfoDTO.MemberDetail memberDetail;

    /**
     * 商品相关
     */
    private List<OrderGoodsDetail> goodsDetails;

    /**
     * 订单相关
     */
    private OrderInfoDTO.OrderDetail orderDetail;

    private LyyInsuranceOrderDTO lyyInsuranceOrderDTO; //保险相关

    /**
     * 营销相关
     */
    private List<OrderDTO.MarketingDetail> marketingDetails;

    /**
     * 退款单号 如果发生退款时查询退款信息
     */
    private String refundNo;

    private Integer businessType;

    @Getter
    @Setter
    @ToString
    public static class OrderGoodsDetail {

        /**
         * 商品id
         */
        private Long goodsId;

        /**
         * 购买的商品数量
         */
        private BigDecimal buyQuantity;

        /**
         * 退款的商品数量
         */
        private BigDecimal refundQuantity;

        /**
         * 支付金额
         */
        private BigDecimal payAmount;

        /**
         * 实际支付金额
         */
        private BigDecimal actualPayAmount;

        /**
         * 商品名称
         */
        private String goodsName;

        /**
         * 商品简称
         */
        private String goodsShortName;

        /**
         * 商品icon
         */
        private String goodIcon;

    }

}
