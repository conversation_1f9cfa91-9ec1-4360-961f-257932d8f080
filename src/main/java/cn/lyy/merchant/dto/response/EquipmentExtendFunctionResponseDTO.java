package cn.lyy.merchant.dto.response;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@ToString
public class EquipmentExtendFunctionResponseDTO {
    /**
     * 功能名称
     */
    private String name;
    /**
     * 功能图标
     */
    private String icon;
    /**
     * 创建时间
     */
    private String created;
    /**
     * 功能类型
     */
    private Integer type;
    /**
     * 功能类型名称
     */
    private String typeName;
    /**
     * 关联saas_merchant_user_menu.menu_code，可多个，英文逗号分隔
     */
    private String relateAuthCode;
}
