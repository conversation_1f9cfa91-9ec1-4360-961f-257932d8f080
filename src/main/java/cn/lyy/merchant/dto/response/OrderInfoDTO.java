package cn.lyy.merchant.dto.response;

import cn.lyy.open.order.dto.OrderItemAggregationDTO;
import cn.lyy.open.order.dto.request.OrderDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.lyy.advert.api.internal.dto.cdz.LyyInsuranceOrderDTO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * @createTime 2020-12-02
 * @auther peterguo
 * @Description
 */
@Getter
@Setter
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OrderInfoDTO {

    private MemberDetail memberDetail; //会员相关

    private List<OrderItemAggregationDTO> subOrders;

    private OrderDetail orderDetail; //订单相关

    private LyyInsuranceOrderDTO lyyInsuranceOrderDTO; //保险相关

    private List<OrderDTO.MarketingDetail> marketingDetails;//营销明细

    @Getter
    @Setter
    @ToString
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    public static class MemberDetail{

        private Long userId;

        private String phoneNo;

        private String headImg;

        private String nickName;
    }

    @Getter
    @Setter
    @ToString
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    public static class OrderDetail {

        private Integer orderStatus; //订单状态

        private String outTradeNo; //订单号

        private Boolean facePay = false; //是否刷脸

        @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime createTime; //创建时间

        @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime paymentTime;//支付完成时间

        private BigDecimal actualAmount; //实付金额

        private BigDecimal totalAmount; //订单金额

        private Integer payMode;    //支付方式

        private Integer payClient; //支付渠道

        private String storeName; //场地名称

        private String orderNo; //支付订单号

        private Set<Integer> payModeList;    //支付方式


    }

}
