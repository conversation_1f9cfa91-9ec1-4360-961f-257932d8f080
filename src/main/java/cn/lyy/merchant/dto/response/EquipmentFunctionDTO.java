package cn.lyy.merchant.dto.response;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @description:
 * @author: qgw
 * @date on 2020/12/1.
 * @Version: 1.0
 */
@Getter
@Setter
@ToString
public class EquipmentFunctionDTO {
    /**
     * 是否支持远程停止
     */
    private Boolean supportRemoteStop;

    /**
     * 是否支持多种计费模式下发
     */
    private Boolean supportMultiMode;

    /**
     * 是否加液
     */
    private Boolean supportLiquid;
}
