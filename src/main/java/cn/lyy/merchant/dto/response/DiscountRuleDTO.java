package cn.lyy.merchant.dto.response;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class DiscountRuleDTO {

    private List<Rule> discountRule;

    private String equipmentTypeName;

    private Object registerImpl;

    @Data
    public class Rule{
        private Long id;
        private BigDecimal coin;
        private Long lyyEquipmentGroupId;
        private Long lyyEquipmentTypeId;
        private BigDecimal price;
        private Integer unit;
    }

}


