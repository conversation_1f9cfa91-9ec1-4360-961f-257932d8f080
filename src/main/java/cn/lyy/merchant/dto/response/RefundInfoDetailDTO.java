package cn.lyy.merchant.dto.response;

import cn.lyy.base.utils.validate.FeeValidator;
import cn.lyy.open.order.dto.request.OrderRefundDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @createTime 2020-12-02
 * @auther peterguo
 *
 * @Description 退款单详情
 */
@Getter
@Setter
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RefundInfoDetailDTO {

    private Integer status; //退款单状态

    private String refundNo; //退款单号

    private LocalDateTime refundTime; //退款时间

    private List<OrderRefundDTO.RefundDetail> refundDetail; //退款信息详情

    @FeeValidator
    private BigDecimal refundAmount;//退款金额

}
