package cn.lyy.merchant.dto.response;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @ClassName: IcCardConsumeDTO
 * @description: ic卡消费记录
 * @author: pengkun
 * @create: 2020-12-15 15:13
 * @Version 1.0
 **/
@Setter
@Getter
@ToString
public class IcCardConsumeDTO {
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 设备类型名称
     */
    private String equipmentTypeName;
    /**
     * 设备Value
     */
    private String equipmentValue;
    /**
     * 场地名称
     */
    private String groupName;
    /**
     * 卡号
     */
    private String cardNo;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 消费类型 1.刷卡消费，2.费用退款 3.余额增加 4.余额扣减
     */
    private Integer type;
    /**
     * 时间
     */
    private String createTime;
    /**
     * 设备Id
     */
    private Long equipmentId;
    /**
     * 设备唯一码
     */
    private String uniqueCode;
    /**
     * 流水金额（元）
     */
    private BigDecimal flowAmount;

    /**
     * 赠送金额（元）
     */
    private BigDecimal flowPresent;
}
