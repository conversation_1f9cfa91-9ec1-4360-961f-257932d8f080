package cn.lyy.merchant.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>Title:saas2</p>
 * <p>Desc: 场地收益数据统计 DTO</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/8/27
 */
@Data
@ApiModel(value = "场地收益统计")
@ToString
public class ProfitGroupDTO {

    @ApiModelProperty(value = "场地名称")
    private String groupName;
    @ApiModelProperty(value = "场地id")
    private Long groupId;
    @ApiModelProperty(value = "场地总收益")
    private BigDecimal amount;

    @ApiModelProperty(value = "统计明细")
    List<NameValueVO> amounts;

    /**
     * 线上收益
     */
    private BigDecimal onlineAmount;

}
