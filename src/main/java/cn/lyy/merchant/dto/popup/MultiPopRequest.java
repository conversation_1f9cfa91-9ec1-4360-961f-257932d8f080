package cn.lyy.merchant.dto.popup;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * @date: 2023/3/23
 * @author: YL
 */
@ApiModel("多id弹窗")
@Data
public class MultiPopRequest {
    
    @NotEmpty(message = "弹窗编号缺失")
    @ApiModelProperty(value = "弹窗编号", required = true)
    private List<Integer> types;

    @NotNull(message = "弹窗设备类型控制缺失")
    @ApiModelProperty(value = "弹窗设备类型控制", required = true)
    private List<String> checkEquipmentTypeValues;
}
