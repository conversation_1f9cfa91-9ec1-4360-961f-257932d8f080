package cn.lyy.merchant.dto.popup;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @date: 2023/3/23
 * @author: YL
 */
@ApiModel("弹窗控制信息")
@Data
@AllArgsConstructor
public class PopResult {

    @ApiModelProperty("是否弹出")
    private boolean popup;

    @ApiModelProperty("弹出编号")
    private Integer type;
}
