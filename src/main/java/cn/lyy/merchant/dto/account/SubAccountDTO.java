package cn.lyy.merchant.dto.account;

import cn.lyy.authority_service_api.merchant.EditableResourcesDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * @description:
 * @author: qgw
 * @date on 2021/3/4.
 * @Version: 1.0
 */
@Data
@ToString
@ApiModel("子账户岗位信息出参")
public class SubAccountDTO {


    @ApiModelProperty("用户名")
    private String userName;

    @ApiModelProperty(value = "昵称")
    private String nickName;

    @ApiModelProperty("账号名称")
    private String phone;

    @ApiModelProperty(value = "密码")
    private String password;

    @ApiModelProperty(value = "用户组织id")
    private Long adOrgId;

    @ApiModelProperty(value = "用户id")
    private Long adUserId;

    @ApiModelProperty(value = "头像")
    private String avatar;

    @ApiModelProperty("场地id列表")
    private List<Long> groupIds;

    @ApiModelProperty(value = "设备数")
    private Integer equipmentNum;

    @ApiModelProperty(value = "用户注册名称")
    private String name;

    @ApiModelProperty("用户所属系统id")
    private Long adSystemId;

    @ApiModelProperty("用户归属")
    private Long belongTo;

    @ApiModelProperty("权限库用户id")
    private Long authorityUserId;

    @ApiModelProperty("角色编码")
    private List<String> roleValue;

    @ApiModelProperty("角色编号")
    private List<MerchantRoleDTO> roles;

    @ApiModelProperty("是否主账号")
    private Boolean isApprover;

    @ApiModelProperty("权限")
    private List<EditableResourcesDTO> resources;
}
