package cn.lyy.merchant.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> =￣ω￣=
 * @date 2022/12/2
 */
@Data
public class DaMerchantAnnualReportYiVO {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 商户ID
     */
    private String merchantId;

    /**
     * 商户注册日期
     */
    private String merchantRegisterTm;

    /**
     * 商户注册日期距今的天数
     */
    private Integer merchantRegisterGapDays;

    /**
     * 商户全年的总营收gmv
     */
    private BigDecimal gmvAmount;

    /**
     * 商户全年支付用户数
     */
    private Integer paymentUserNum;

    /**
     * 销量最高套餐金额
     */
    private BigDecimal mostSetAmount;

    /**
     * 销量最高套餐全年gmv占比
     */
    private Double mostSetRatio;

    /**
     * 全年单日最高gmv
     */
    private BigDecimal maxGmvAmount;

    /**
     * GMV文案
     */
    private String gmvAmountDesc;

    /**
     * 全年单日最高gmv日期
     */
    private String maxGmvDate;

    /**
     * 全年最晚的订单时间（以凌晨5点为时间界限）
     */
    private String timeEndLatest;

    /**
     * 历史商户场地总数（merchant=all）
     */
    private Integer totalGroupNum;

    /**
     * 全年新增注册场地数（merchant=all）
     */
    private Integer newRegisterGroupNum;

    /**
     * 商户今年的补贴收入
     */
    private BigDecimal totalAllowanceAmount;

    /**
     * 商户今年补贴收益最高的产品
     */
    private String maxAllowanceProductName;

    /**
     * 商户消费者今年购买次数最多的产品
     */
    private String mostPurchaseProductName;

    /**
     * 年份
     */
    private String year;


    /**
     * 客单价
     */
    private BigDecimal atv;

    private Boolean hasDbj;
}
