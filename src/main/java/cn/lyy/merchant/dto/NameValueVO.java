package cn.lyy.merchant.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.AllArgsConstructor;

import java.io.Serializable;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2019/8/30
 * @Version 1.0
 **/
@Data
@ApiModel("名称和值，通用")
@AllArgsConstructor
public class NameValueVO implements Serializable {
    @ApiModelProperty("名称")
    String name;
    @ApiModelProperty("值")
    String value;
}
