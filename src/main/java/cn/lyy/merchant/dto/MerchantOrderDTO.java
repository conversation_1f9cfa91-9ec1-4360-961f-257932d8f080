package cn.lyy.merchant.dto;

import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class MerchantOrderDTO {
    /**
     * 订单编号
     */
    private String outTradeNo;

    /**
     * 订单类型
     */
    private String orderType;

    /**
     * 第三方支付订单编号
     */
    private String channelOrderNo;

    /**
     * 支付金额
     */
    private BigDecimal actuaAmount;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 收益
     */
    private BigDecimal profit;

    /**
     * 订单状态
     */
    private String status;

    private String refundPath;

    /**
     * 支付渠道
     */
    private String clientType;

    /**
     * 设备编码
     */
    private String equipmentValue;

    /**
     * 设备品类
     */
    private String equipmentClass;

    /**
     * 场地名称
     */
    private String groupName;

    /**
     * 订单创建时间
     */
    private String createTime;

    /**
     * 商家id
     */
    private String distributorId;
}
