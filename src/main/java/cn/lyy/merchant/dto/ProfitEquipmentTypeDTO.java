package cn.lyy.merchant.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <p>Title:saas2</p>
 * <p>Desc: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/8/29
 */
@Data
@ApiModel(value = "设备类型收益统计")
@ToString
public class ProfitEquipmentTypeDTO {
    @ApiModelProperty(value = "设备类型编号")
    private Long equipmentTypeId;
    @ApiModelProperty(value = "设备类型名称")
    private String equipmentType;
    @ApiModelProperty(value = "设备类型图标")
    private String equipmentTypeIcon;

    @ApiModelProperty(value = "统计明细")
    List<NameValueVO> amounts;

    @Deprecated
    @ApiModelProperty(value = "设备id数组")
    private List<Integer> equipmentIds;

}
