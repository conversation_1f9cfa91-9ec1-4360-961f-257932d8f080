package cn.lyy.merchant.dto;

import cn.lyy.merchant.dto.common.UserInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <p>Title:saas2</p>
 * <p>Desc: 收益数据统计 DTO</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/8/27
 */
@Data
@ApiModel(value = "收益统计")
@ToString
public class ProfitDTO implements Serializable {


    @ApiModelProperty(value = "线下投币数")
    private Integer offlineCoins;

    @ApiModelProperty(value = "线上收益")
    private BigDecimal amount;

    @ApiModelProperty(value = "线下现金消费")
    private BigDecimal offlineAmount;

    @ApiModelProperty(value = "平台补贴")
    private BigDecimal adAmount;

    @ApiModelProperty(value = "统计明细")
    List<NameValueVO> amounts;

    @ApiModelProperty(value = "总收益")
    private BigDecimal totalIncome;

    @ApiModelProperty(value = "设备id")
    private Integer equipmentId;
    @ApiModelProperty(value = "设备编号")
    private Integer equipmentValue;

    @ApiModelProperty(value = "查询条件")
    private DateQueryResult queryOffset;

    @ApiModelProperty(value = "支付笔数")
    private Integer onlineCount;

    @Data
    @EqualsAndHashCode(callSuper=true)
    public static class Request extends UserInfoDTO {
        @ApiModelProperty(value = "时间范围，TODAY:今天，YESTARDAY:昨天，SEVENDAY:近7天")
        private String dateRange;
        @ApiModelProperty(value = "起始时间")
        private LocalDate startDate;
        @ApiModelProperty(value = "结束时间")
        private LocalDate endDate;
        @ApiModelProperty(value = "场地编号")
        private Long groupId;
        @ApiModelProperty(value = "设备类型编号")
        private Long equipmentTypeId;
        @ApiModelProperty(value = "统计类型，GROUP:场地，EQUIPMENT_TYPE:设备类型，EQUIPMENT:设备")
        private String type;
        @ApiModelProperty(value = "设备id列表")
        private List<Integer> equipmentIds;
        @ApiModelProperty(value = "场地编号列表")
        private List<Long> groupIds;
    }

    @Data
    @EqualsAndHashCode(callSuper=true)
    public static class OffsetRequest extends Request {
        @ApiModelProperty(value = "查询偏移单位，day:天，week:周，month:月")
        private String offsetType;
        @ApiModelProperty(value = "查询纬度，time：时间，group：场地，equipment:设备")
        private String queryType;
        @ApiModelProperty(value = "查询偏移值,0表示当天、周、月，向前查是负数，没有正数,例如上周：-1 , 单位为week")
        private Integer offset;


    }
}
