package cn.lyy.merchant.dto.user;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2021/4/15 09:31
 * 描述：所有余额数量
 */
@Getter
@Setter
@ToString
public class AllBalanceDTO {

    /**
     * 总币数
     */
    private BigDecimal coins;

    /**
     * 总余额
     */
    private BigDecimal money;

    /**
     * 卡数量
     */
    private BigDecimal card;

    /**
     * 券数量
     */
    private BigDecimal coupon;

    /**
     * ic卡数量
     */
    private BigDecimal icCard;

}
