package cn.lyy.merchant.dto.user;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.lyy.user.account.infrastructure.base.LongFromStringDeserializer;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @ClassName: UserDTO
 * @description: 用户信息
 * @author: pengkun
 * @date: 2021/05/31
 **/
@Setter
@Getter
@ToString
public class UserDTO {
    /**
     * 商户用户id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @JsonDeserialize(using = LongFromStringDeserializer.class)
    private Long merchantUserId;
    /**
     * 商户id
     */
    private Long merchantId;
    /**
     * 用户id
     */
    private Long lyyUserId;
    /**
     * 手机号码
     */
    private String telephone;
    /**
     * 头像
     */
    private String headImg;
    /**
     * 用户昵称
     */
    private String name;
    /**
     * 性别
     */
    private String gender;
    /**
     * openId
     */
    private String openid;
    /**
     * 生日
     */
    private String birthday;
    /**
     * unionid
     */
    private String unionid;
    /**
     * 用户类别
     */
    private String userType;
    /**
     * 城市id
     */
    private Long lyyCityId;
    /**
     * 省份城市
     */
    private String provinceCity;

    /**
     * 省份Id
     */
    private Long provinceId;
    /**
     * 区域Id
     */
    private Long regionId;
}
