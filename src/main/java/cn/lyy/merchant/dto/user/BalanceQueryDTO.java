package cn.lyy.merchant.dto.user;

import com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum;
import com.lyy.user.account.infrastructure.constant.BenefitClassifyGroupEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

@Getter
@Setter
@ToString
public class BalanceQueryDTO {

    /**
     * 商家用户id
     */
    @NotNull(message = "请提供需要查询的用户")
    private Long merchantUserId;

    /**
     * 场地id
     */
    private List<Long> storeId;

    /**
     * 设备类型id
     */
    private List<Long> equipmentTypeId;

    /**
     * 余额查询类型
     * @see BenefitClassifyGroupEnum
     */
    private Integer balanceType;

    public List<Integer> getClassifyList() {
        return BenefitClassifyGroupEnum.getClassifyByType(this.balanceType);
    }
}
