package cn.lyy.merchant.dto;

import cn.lyy.merchant.dto.common.UserInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

/**
 * <p>Title:saas2</p>
 * <p>Desc: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/8/28
 */
@Data
@ApiModel("分页")
@ToString(callSuper = true)
public class PageDTO extends UserInfoDTO {
    public PageDTO(Integer pageIndex, Integer pageSize) {
        this.pageIndex = pageIndex;
        this.pageSize = pageSize;
    }

    public PageDTO() {
    }

    @ApiModelProperty(value = "页数", required = true)
    private Integer pageIndex;
    @ApiModelProperty(value = "数量", required = true)
    private Integer pageSize;

    public int getPageOffset(){
        if(pageIndex != null && pageSize != null)
            return (pageIndex -1) * pageSize;
        return 0;
    }
}
