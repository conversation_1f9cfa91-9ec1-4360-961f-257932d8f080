package cn.lyy.merchant.dto;

import lombok.Data;


@Data
public class AdvertOperationDTO {

    private Long lyyOfficialAccoutId;
    /**
     * 业务类型
     */
    private String businesstype;
    /**
     * 公众号app_id
     */
    private String appId;
    /**
     * 公众号api key
     */
    private String apiKey;
    /**
     * 公众号id
     */
    private String officialAccout;
    /**
     * 平台类型
     */
    private Integer platformType;
    /**
     * 商家id
     */
    private Long adOrgId;

    public static AdvertOperationDTO query(String appId) {
        AdvertOperationDTO dto = new AdvertOperationDTO();
        dto.setAppId(appId);
        return dto;
    }

    public static AdvertOperationDTO update(Long lyyOfficialAccoutId, Integer platformType) {
        AdvertOperationDTO dto = new AdvertOperationDTO();
        dto.setLyyOfficialAccoutId(lyyOfficialAccoutId);
        dto.setPlatformType(platformType);
        return dto;
    }
}
