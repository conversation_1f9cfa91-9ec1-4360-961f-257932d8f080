package cn.lyy.merchant.dto;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> =￣ω￣=
 * @date 2022/12/5
 */
@Data
public class AgreementBatchSignReqDTO {

    private Long legalAgreementSignId;

    private Integer version;

    private String status;

//    @NotNull(
//            message = "签署人id不能为空"
//    )
    private String userId;

    private String appId;

    private List<LegalAgreementSignReqDTO> legalAgreements;

//    @NotNull(message = "公司编号不能为空")
    private String companyCode;

    @NotNull(message = "服务编号不能为空")
    private String serviceCode;

    @NotNull(message = "产品编号不能为空")
    private String productCode;

    private String userTypeCode;

    @NotNull(message = "操作类型不能为空")
    private String operationType;

    private Integer currentVersion;

    private Long agreementSignId;

    private String title;

    private List<Long> agreementIds;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date created;

    private String createdby;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updated;

    private String updatedby;

    @Data
    public static class LegalAgreementSignReqDTO {

        private @NotNull(
                message = "协议id不能为空"
        ) Long agreementId;

        private @NotNull(
                message = "协议版本不能为空"
        ) Integer version;

        private String title;

        private Long agreementSignId;

        private List<ExtDataModelReqDTO> extDataList;

        @Data
        public static class ExtDataModelReqDTO {

            private String field;

            private String value;
        }
    }
}
