package cn.lyy.merchant.dto;

import cn.lyy.equipment.dto.equipment.EquipmentListResponseDTO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR> {<EMAIL>}
 * @date 2020/7/21 11:00
 **/
@Getter
@Setter
@ToString
public class MerchantGroupDTO {

    private Long equipmentGroupId;
    private String name;
    private String address;
    private String description;
    private Long distributorId;
    private Long districtId;
    private String addressType;
    private String isCommonPlace;
    private String isAmountCommon;
    private String lyyVersion;
    private Integer allNum;
    private Integer offLineNum;
    private Integer onLineNum;
    private List<EquipmentListResponseDTO> equipments;

}
