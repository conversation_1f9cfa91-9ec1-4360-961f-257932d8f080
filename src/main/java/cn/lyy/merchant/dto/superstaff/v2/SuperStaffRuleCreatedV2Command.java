package cn.lyy.merchant.dto.superstaff.v2;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * @date: 2023-11-16
 * @author: YUNLONG
 */
@Data
public class SuperStaffRuleCreatedV2Command {

    @ApiModelProperty("规则名，无则按<设置n>生成")
    private String name;

    @NotEmpty(message = "规则项缺失")
    private List<SuperStaffRuleV2CreateItem> items;

    @ApiModelProperty("需要绑定的设备编号")
    private String equipmentValue;
}
