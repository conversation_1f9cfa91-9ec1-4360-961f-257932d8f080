package cn.lyy.merchant.dto.superstaff;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * @date: 2023-10-24
 * @author: YUNLONG
 */
@ApiModel("规则编辑")
@Data
public class SuperStaffRuleUpdateRequest {

    @ApiModelProperty(value = "规则ID", required = true)
    @NotNull(message = "规则ID缺失")
    private Long ruleId;

    @ApiModelProperty(value = "规则名称", required = true)
    @NotNull(message = "规则名称缺失")
    private String name;

    @Valid
    @ApiModelProperty("有修改的规则项")
    private List<SuperStaffRuleUpdateItem> items;
}
