package cn.lyy.merchant.dto.superstaff;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * @date: 2023-10-20
 * @author: YUNLONG
 */
@ApiModel("规则绑定场地")
@Data
public class SuperStaffRuleBindStoreDTO {

    @ApiModelProperty("场地名查询")
    private String query;

    @ApiModelProperty(value = "规则id", required = true)
    @NotNull(message = "规则缺失")
    private Long ruleId;

    @ApiModelProperty("非全选时传选择的")
    private List<Long> groupIds;

    @ApiModelProperty("全选时传选择的")
    private List<Long> excludeGroupIds;

    @ApiModelProperty(value = "是否全选", required = true)
    @NotNull(message = "是否全选缺失")
    private Boolean selectAll;
}
