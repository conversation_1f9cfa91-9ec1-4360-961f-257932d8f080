package cn.lyy.merchant.dto.superstaff.v2;

import cn.lyy.marketing.dto.constants.superstaff.SuperStaffItemKeyEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @date: 2023-10-19
 * @author: YUNLONG
 */
@ApiModel("规则项目新增")
@Data
public class SuperStaffRuleV2CreateItem {

    /**
     * @see SuperStaffItemKeyEnum
     */
    @ApiModelProperty(value = "规则项目key")
    private String key;

    @ApiModelProperty(value = "自定义文字")
    private String content;

    @ApiModelProperty("是否自动生成-推荐套餐用")
    private Boolean autoGenerate;

    @ApiModelProperty("图片资源id")
    private Long materialId;

    @ApiModelProperty("广告名称")
    private String adsName;

    @ApiModelProperty("扩展信息json")
    private String data;

    @NotBlank(message = "项类型缺失")
    @ApiModelProperty("项类型")
    private String type;

    @ApiModelProperty("是否启用")
    private Boolean enable;

    @ApiModelProperty("海报推荐id")
    private Long activityId;
}
