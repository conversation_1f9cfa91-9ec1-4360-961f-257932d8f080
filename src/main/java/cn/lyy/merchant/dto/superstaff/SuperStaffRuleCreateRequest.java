package cn.lyy.merchant.dto.superstaff;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * @date: 2023-10-18
 * @author: YUNLONG
 */
@ApiModel("新建规则")
@Data
public class SuperStaffRuleCreateRequest {

    @ApiModelProperty(value = "规则名称")
    private String name;

    @Valid
    @ApiModelProperty(value = "规则项目")
    @NotEmpty(message = "规则项目缺失")
    private List<SuperStaffRuleCreateItem> items;
}
