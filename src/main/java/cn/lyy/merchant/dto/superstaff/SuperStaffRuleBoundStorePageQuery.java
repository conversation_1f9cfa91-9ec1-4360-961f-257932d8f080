package cn.lyy.merchant.dto.superstaff;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

/**
 * @date: 2023-10-20
 * @author: YUNLONG
 */
@Data
public class SuperStaffRuleBoundStorePageQuery {

    private String query;

    @NotNull(message = "规则id未指定")
    private Long ruleId;

    @NotNull(message = "页码不能为空")
    @Min(value = 1L, message = "页码从1开始")
    private Integer current;
    /**
     * 每页条数
     */
    @NotNull(message = "页条数不能为空")
    @Range(min = 1, max = 100L, message = "每页条数1~100")
    private Integer size;
}
