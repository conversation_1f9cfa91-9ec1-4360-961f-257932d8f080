package cn.lyy.merchant.dto.superstaff;

import cn.lyy.marketing.dto.constants.superstaff.SuperStaffResourceTemplateTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import lombok.Data;

/**
 * @date: 2023-11-03
 * @author: YUNLONG
 */
@ApiModel("超级导购员资源模板保存")
@Data
public class SuperStaffResourceTemplateCreateDTO {

    @NotBlank(message = "需指定模板名称")
    @ApiModelProperty(value = "模板名称", required = true)
    private String name;

    /**
     * @see SuperStaffResourceTemplateTypeEnum
     */
    @Null(message = "需指定模板类型")
    @ApiModelProperty(value = "类型", required = true)
    private Integer type;

    @NotBlank(message = "需指定模板内容")
    @ApiModelProperty(value = "模板内容", required = true)
    private String template;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("序号")
    private Long serial;

    @NotNull(message = "需指定知否展示")
    @ApiModelProperty(value = "是否展示", required = true)
    private Boolean show;
}
