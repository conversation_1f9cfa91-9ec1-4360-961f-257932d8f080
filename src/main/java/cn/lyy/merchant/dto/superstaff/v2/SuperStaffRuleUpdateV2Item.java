package cn.lyy.merchant.dto.superstaff.v2;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @date: 2023-10-19
 * @author: YUNLONG
 */
@ApiModel("规则项目修改")
@Data
public class SuperStaffRuleUpdateV2Item {

    @ApiModelProperty(value = "规则项id，新拓展的项可以没有")
    private Long itemId;

    @NotBlank(message = "项类型缺失")
    @ApiModelProperty("项类型")
    private String type;

    @ApiModelProperty(value = "规则项key", required = true)
    private String key;

    @ApiModelProperty(value = "自定义文字，无会抹掉原设置语音文字")
    private String content;

    @ApiModelProperty("是否自动生成-推荐套餐用")
    private Boolean autoGenerate;

    @ApiModelProperty("新的广告资源id，如有变更，生成新广告")
    private Long materialId;

    @ApiModelProperty("新的广告名称，如有变更")
    private String adsName;

    @ApiModelProperty("扩展json信息")
    private String data;

    @ApiModelProperty("是否启用")
    private Boolean enable;

    @ApiModelProperty("海报推荐id")
    private Long activityId;
}
