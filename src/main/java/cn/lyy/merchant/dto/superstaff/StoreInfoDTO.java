package cn.lyy.merchant.dto.superstaff;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @date: 2023-10-24
 * @author: YUNLONG
 */
@ApiModel("场地信息")
@Data
public class StoreInfoDTO {

    @ApiModelProperty("场地id")
    private Long storeId;

    @ApiModelProperty("场地名称")
    private String name;
    @ApiModelProperty("场地地址")
    private String address;

    @ApiModelProperty("场地省")
    private String provinceName;

    @ApiModelProperty("场地城市")
    private String cityName;

    @ApiModelProperty("场地区")
    private String district;

    @ApiModelProperty("场地类型编码")
    private String addressType;

    @ApiModelProperty("场地类型名称")
    private String addressTypeName;

    @ApiModelProperty("场地设备数")
    private Integer equipmentCount;

    @ApiModelProperty("导购规则id")
    private Long superStaffRuleId;

    @ApiModelProperty("导购规则名称")
    private String superStaffRuleName;

    @ApiModelProperty("是否已绑定")
    private Boolean bound;
}
