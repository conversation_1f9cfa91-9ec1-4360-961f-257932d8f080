package cn.lyy.merchant.dto.superstaff;

import cn.lyy.marketing.dto.constants.superstaff.SuperStaffItemKeyEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * @date: 2023-10-19
 * @author: YUNLONG
 */
@ApiModel("规则项目新增")
@Data
public class SuperStaffRuleCreateItem {

    /**
     * @see SuperStaffItemKeyEnum
     */
    @ApiModelProperty(value = "规则项目key")
    @NotBlank(message = "类型缺失")
    private String key;

    @ApiModelProperty(value = "自定义文字")
    private String content;

    @ApiModelProperty("是否自动生成-推荐套餐用")
    private Boolean autoGenerate;

    @ApiModelProperty("图片资源id")
    private Long materialId;

    @ApiModelProperty("广告名称")
    private String adsName;

    @ApiModelProperty("扩展信息json")
    private String data;
}
