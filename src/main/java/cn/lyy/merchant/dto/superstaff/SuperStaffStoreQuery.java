package cn.lyy.merchant.dto.superstaff;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

/**
 * @date: 2023-10-24
 * @author: YUNLONG
 */
@ApiModel("分页场地列表查询")
@Data
public class SuperStaffStoreQuery {

    @ApiModelProperty("查询")
    private String query;

    @NotNull(message = "页码不能为空")
    @Min(value = 1L, message = "页码从1开始")
    private Integer current;
    /**
     * 每页条数
     */
    @NotNull(message = "页条数不能为空")
    @Range(min = 1, max = 100L, message = "每页条数1~100")
    private Integer size;

    @ApiModelProperty(hidden = true)
    private int limitSize;

    @ApiModelProperty(hidden = true)
    private boolean needFilterStoreInternal;

    @ApiModelProperty(hidden = true)
    private Long merchantId;

    @ApiModelProperty(hidden = true)
    private Long adUserId;
}
