package cn.lyy.merchant.dto.superstaff.v2;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * @date: 2023-11-16
 * @author: YUNLONG
 */
@Data
public class EquipmentWithRuleInfoDTO {

    @ApiModelProperty("设备id")
    private Long equipmentId;

    @ApiModelProperty("设备编号")
    private String value;

    @ApiModelProperty("设备唯一码")
    private String uniqueCode;

    @ApiModelProperty("设备类型id")
    private Long equipmentTypeId;

    @ApiModelProperty("设备类型名称")
    private String equipmentTypeName;

    @ApiModelProperty("导购规则id")
    private Long superStaffRuleId;

    @ApiModelProperty("导购规则名称")
    private String superStaffRuleName;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("套餐结束时间，为空标识永久")
    private Date endDate;

    @ApiModelProperty("套餐版本,0-基础版，1-高级版")
    private Integer version;

    @ApiModelProperty("场地id")
    private Long storeId;
}
