package cn.lyy.merchant.dto.superstaff.v2;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * @date: 2023-11-16
 * @author: YUNLONG
 */
@Data
public class StoreWithEquipmentListDTO {

    @ApiModelProperty("场地id")
    private Long storeId;

    @ApiModelProperty("场地名称")
    private String name;
    @ApiModelProperty("场地地址")
    private String address;

    @ApiModelProperty("场地省")
    private String provinceName;

    @ApiModelProperty("场地城市")
    private String cityName;

    @ApiModelProperty("场地区")
    private String district;

    @ApiModelProperty("场地类型编码")
    private String addressType;

    @ApiModelProperty("场地类型名称")
    private String addressTypeName;

    @ApiModelProperty("场地设备数")
    private Integer equipmentCount;

    @ApiModelProperty("设备列表")
    private List<EquipmentWithRuleInfoDTO> equipments;
}
