package cn.lyy.merchant.dto.superstaff.v2;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * @date: 2023-10-24
 * @author: YUNLONG
 */
@ApiModel("规则编辑")
@Data
public class SuperStaffRuleUpdateV2Command {

    @ApiModelProperty(value = "规则ID", required = true)
    @NotNull(message = "规则ID缺失")
    private Long ruleId;

    @ApiModelProperty(value = "规则名称", required = true)
    @NotNull(message = "规则名称缺失")
    private String name;

    @Valid
    @ApiModelProperty("有修改的规则项，有id的会编辑，无id新增")
    private List<SuperStaffRuleUpdateV2Item> items;

    @ApiModelProperty("需要绑定的设备编号")
    private String equipmentValue;
}
