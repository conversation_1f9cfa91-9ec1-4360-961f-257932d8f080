package cn.lyy.merchant.dto.superstaff;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

/**
 * @date: 2023-10-24
 * @author: YUNLONG
 */
@ApiModel("场地列表查询")
@Data
public class SuperStaffStoreSelectQuery {

    @ApiModelProperty("查询")
    private String query;

    @ApiModelProperty(value = "规则id", required = true)
    @NotNull(message = "规则id缺失")
    private Long ruleId;

    @NotNull(message = "页码不能为空")
    @Min(value = 1L, message = "页码从1开始")
    private Integer current;
    /**
     * 每页条数
     */
    @NotNull(message = "页条数不能为空")
    @Range(min = 1, max = 100L, message = "每页条数1~100")
    private Integer size;
}
