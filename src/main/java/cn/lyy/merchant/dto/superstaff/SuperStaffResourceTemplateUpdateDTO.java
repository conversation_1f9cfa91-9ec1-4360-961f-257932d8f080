package cn.lyy.merchant.dto.superstaff;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * @date: 2023-11-03
 * @author: YUNLONG
 */
@ApiModel("超级导购员资源模板修改")
@Data
public class SuperStaffResourceTemplateUpdateDTO {

    @NotNull(message = "需指定id")
    @ApiModelProperty(value = "id", required = true)
    private Long id;

    @NotBlank(message = "需指定模板名称")
    @ApiModelProperty(value = "模板名称", required = true)
    private String name;

    @NotBlank(message = "需指定模板内容")
    @ApiModelProperty(value = "模板内容", required = true)
    private String template;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("序号")
    private Long serial;
}
