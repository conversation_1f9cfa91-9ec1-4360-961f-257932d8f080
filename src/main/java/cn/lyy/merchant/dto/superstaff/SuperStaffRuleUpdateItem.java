package cn.lyy.merchant.dto.superstaff;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @date: 2023-10-19
 * @author: YUNLONG
 */
@ApiModel("规则项目修改")
@Data
public class SuperStaffRuleUpdateItem {

    @ApiModelProperty(value = "规则项id，新拓展的项可以没有")
    private Long itemId;

    @ApiModelProperty(value = "规则项key", required = true)
    @NotBlank(message = "规则项key缺失")
    private String key;

    @ApiModelProperty(value = "自定义文字")
    private String content;

    @ApiModelProperty("是否自动生成-推荐套餐用")
    private Boolean autoGenerate;

    @ApiModelProperty("新的广告资源id，如有变更")
    private Long materialId;

    @ApiModelProperty("新的广告名称，如有变更")
    private String adsName;

    @ApiModelProperty("扩展json信息")
    private String data;
}
