package cn.lyy.merchant.dto.productmatrix;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @description
 * <AUTHOR>
 * @date 2023/4/14 13:52
*/
@ApiModel("参与测试场地")
@Data
public class ExamineGroupResult {

    @ApiModelProperty("活动扫码人数")
    private List<String> group;

    @ApiModelProperty("商家实际到账")
    private Integer count;
}
