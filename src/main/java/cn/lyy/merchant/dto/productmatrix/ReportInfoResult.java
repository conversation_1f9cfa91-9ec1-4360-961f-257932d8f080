package cn.lyy.merchant.dto.productmatrix;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @description
 * <AUTHOR>
 * @date 2023/4/14 13:52
*/
@ApiModel("测试报告内容")
@Data
public class ReportInfoResult {

    @ApiModelProperty("有导购活动扫码人数")
    private Integer scanUser;

    @ApiModelProperty("无导购活动扫码人数")
    private Integer noScanUser;

    @ApiModelProperty("有导购付费人数")
    private Integer examineUserNum;

    @ApiModelProperty("无导购付费人数")
    private Integer noExamineUserNum;

    @ApiModelProperty("有导购付费金额")
    private BigDecimal examineRevenue;

    @ApiModelProperty("无导购付费金额")
    private BigDecimal noExamineRevenue;

    @ApiModelProperty("有导购商家实际到账")
    private BigDecimal examinePayment;

    @ApiModelProperty("无导购商家实际到账")
    private BigDecimal noExaminePayment;

    @ApiModelProperty("有导购人均付费金额")
    private BigDecimal perPayUser;

    @ApiModelProperty("无导购人均付费金额")
    private BigDecimal noPerPayUser;

    @ApiModelProperty("有导购复购人数统计")
    private Integer repurchaseUser;

    @ApiModelProperty("无导购复购人数统计")
    private Integer noRepurchaseUser;

    @ApiModelProperty("有导购≥10元套餐订单数")
    private Integer over10Amount;

    @ApiModelProperty("无导购≥10元套餐订单数")
    private Integer noOver10Amount;

    @ApiModelProperty("有导购≥20元套餐订单数")
    private Integer over20Amount;

    @ApiModelProperty("无导购≥20元套餐订单数")
    private Integer noOver20Amount;

    @ApiModelProperty("有导购≥50元套餐订单数")
    private Integer over50Amount;

    @ApiModelProperty("无导购≥50元套餐订单数")
    private Integer noOver50Amount;

    @ApiModelProperty("有导购≥100元套餐订单数")
    private Integer over100Amount;

    @ApiModelProperty("无导购≥100元套餐订单数")
    private Integer noOver100Amount;
}
