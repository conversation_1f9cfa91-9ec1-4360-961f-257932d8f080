package cn.lyy.merchant.dto.productmatrix;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @description
 * <AUTHOR>
 * @date 2023/4/14 13:52
*/
@ApiModel("报告内容配置")
@Data
public class ReportConfigResult {

    @ApiModelProperty("数据统计周期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty("数据统计周期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty("活动扫码人数")
    private Boolean scanUser;

    @ApiModelProperty("商家实际到账")
    private Boolean examinePayment;

    @ApiModelProperty("人均付费金额")
    private Boolean perPayUser;

    @ApiModelProperty("复购人数统计")
    private Boolean repurchaseUser;

    @ApiModelProperty("导购类型订单统计")
    private Boolean packageDistribution;

    @ApiModelProperty("≥10元套餐占比")
    private Boolean over10Amount;

    @ApiModelProperty("≥20元套餐占比")
    private Boolean over20Amount;

    @ApiModelProperty("≥50元套餐占比")
    private Boolean over50Amount;

    @ApiModelProperty("≥100元套餐占比")
    private Boolean over100Amount;

    @ApiModelProperty("一个月收入预测")
    private Boolean revenueEstimation;
}
