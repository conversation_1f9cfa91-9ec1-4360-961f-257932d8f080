package cn.lyy.merchant.dto.productmatrix;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @description
 * <AUTHOR>
 * @date 2023/4/14 13:52
*/
@ApiModel("成功导购数据")
@Data
public class PackageDistributionResult {

    @ApiModelProperty("成功导购笔数")
    private Integer count;

    @ApiModelProperty("活动扫码人数")
    private List<PackageDistributionDTO> packageDistribution;
}
