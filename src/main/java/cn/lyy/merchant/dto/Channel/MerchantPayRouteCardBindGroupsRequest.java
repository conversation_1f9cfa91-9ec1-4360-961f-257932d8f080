package cn.lyy.merchant.dto.Channel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * @date: 2023/4/1
 * @author: YL
 */
@ApiModel("卡绑定多场地")
@Data
public class MerchantPayRouteCardBindGroupsRequest {

    @ApiModelProperty("卡资料id")
    @NotNull(message = "卡资料id缺失")
    private Long swiftPassMerchantId;
    
    @ApiModelProperty("场地ids，传空表示全选")
    private List<Long> equipmentGroupIds;

    @ApiModelProperty("全选后取消选择的场地ids")
    private List<Long> excludeGroupIds;
}
