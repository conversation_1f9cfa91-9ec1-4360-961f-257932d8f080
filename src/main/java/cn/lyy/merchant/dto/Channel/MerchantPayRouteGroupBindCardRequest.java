package cn.lyy.merchant.dto.Channel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * @date: 2023/4/1
 * @author: YL
 */
@ApiModel("场地绑定卡")
@Data
public class MerchantPayRouteGroupBindCardRequest {

    @ApiModelProperty("卡资料id")
    @NotNull(message = "卡资料id缺失")
    private Long swiftPassMerchantId;
    
    @ApiModelProperty("场地id")
    @NotNull(message = "场地id缺失")
    private Long equipmentGroupId;
}
