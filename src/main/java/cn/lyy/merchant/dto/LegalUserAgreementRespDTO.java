package cn.lyy.merchant.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> =￣ω￣=
 * @date 2022/12/10
 */
@Data
public class LegalUserAgreementRespDTO {

    private Long legalAgreementSignId;

    private Long legalAgreementId;

    private String title;

    private String introduction;

    private String content;

    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date created;

    @JsonFormat(
            pattern = "yyyy-MM-dd"
    )
    private Date startDate;

    @JsonFormat(
            pattern = "yyyy-MM-dd"
    )
    private Date endDate;

    private Integer version;

    private String signStatus = "2";

    private String extData;

    private String serviceCode;
}
