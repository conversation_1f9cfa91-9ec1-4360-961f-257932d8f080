package cn.lyy.merchant.dto.divide;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDateTime;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.ToString;

/**
* @Author: liuFaLin
* @Description:
* @DateTime: 15:44 2023/5/26
*/
@Data
@ToString
public class DivideRuleEquipmentRelationLogQueryVO {

    /**
     * 操作账号
     */
    private String operateAccount;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作内容
     */
    private String operateContent;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;

    /**
     * 页码
     */
    @NotNull(message = "页码不能为空")
    private Integer pageIndex;

    /**
     * 页大小
     */
    @NotNull(message = "页大小不能为空")
    private Integer pageSize;
}
