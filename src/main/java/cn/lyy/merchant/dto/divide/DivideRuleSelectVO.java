package cn.lyy.merchant.dto.divide;

import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

/**
* @Author: liuFaLin
* @Description:
* @DateTime: 11:40 2023/6/2
*/
@Data
@ToString
@Accessors(chain = true)
public class DivideRuleSelectVO {
    /**
     * 选择的位置
     */
    private Integer selectIndex;

    /**
     * 选择抽成规则列表
     */
    private List<DivideRuleInfoDTO> selectRuleList;

}
