package cn.lyy.merchant.dto.divide;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/30
 */
@Data
@ToString
public class DivideRuleConfigDTO implements Serializable {
    private static final long serialVersionUID = -6646196150132278610L;

    /**
     * 设备信息,主要是要设备号的场地ID
     */
    private List<MerchantEquipmentDivideDTO> merchantEquipmentDTOList;

    /**
     * 规则ID
     */
    private Long ruleId;

    /**
     * 默认规则设置
     */
    private Boolean defaultRuleFlag;

    /**
     * 批量设置情况下排除部分场地的设置
     */
    private List<MerchantEquipmentDivideDTO> excludeEquipments;

    /**
     * 是否全量设置
     */
    private Boolean isFullSet;

    /**
     * 设备标签搜索
     */
    private List<Long> labels;
    /**
     * 设备场地ID
     */
    private List<Long> equipmentGroupIdList;
    /**
     * 设备类型
     */
    private List<Long> equipmentTypeIdList;
    /**
     * 设备编号搜索
     */
    private String queryStr;

}
