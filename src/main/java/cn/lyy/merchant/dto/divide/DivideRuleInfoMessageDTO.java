package cn.lyy.merchant.dto.divide;

import cn.lyy.merchant.constant.DivideEnum;
import cn.lyy.merchant.dto.account.DivideUserInfoDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

/**
* @Author: liuFaLin
* @Description: 分账规则结果DTO
* @DateTime: 10:42 2023/6/5
*/
@Data
@ToString
public class DivideRuleInfoMessageDTO {


    /**
     * 规则id
     */
    private Long id;

    /**
     * 规则名称
     */
    private String name;

    /**
     * 启用状态，1-启用，2-禁用
     */
    private Integer status;
    private String statusStr;
    public String getStatusStr(){
        DivideEnum.DivideStatusEnum e = DivideEnum.DivideStatusEnum.findByCode(status);
        return e == null ? "" : e.getDesc();
    }

    /**
     * 规则状态，1-待开始，2-进行中，3-已结束
     */
    private Integer ruleStatus;
    private String ruleStatusStr;
    public String getRuleStatusStr(){
        DivideEnum.DivideRuleStatusEnum e = DivideEnum.DivideRuleStatusEnum.findByCode(ruleStatus);
        return e == null ? "" : e.getDesc();
    }

    /**
     * 抽成周期类型，1-单周期，2-多周期
     */
    private Integer periodType;
    private String periodTypeStr;
    public String getPeriodTypeStr(){
        DivideEnum.DividePeriodTypeEnum e = DivideEnum.DividePeriodTypeEnum.findByCode(periodType);
        return e == null ? "" : e.getDesc();
    }

    /**
     * 主体id
     */
    private Long distributorId;

    /**
     * 主体类型，4-代理商，2-商家
     */
    private Integer distributorType;

    /**
     * 当前周期开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;

    /**
     * 当前周期结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;

    /**
     * 类目类型，1-统一，2-分类
     */
    private Integer category;
    private String categoryStr;
    public String getCategoryStr(){
        DivideEnum.DivideCategoryEnum e = DivideEnum.DivideCategoryEnum.findByCode(category);
        return e == null ? "" : e.getDesc();
    }

    /**
     * 分成人员ID
     */
    private List<Long> divideDistributorIdList;

    /**
     * 分成人员信息
     */
    private List<DivideUserInfoDTO> divideUserList;


    /**
     * 分成人数
     */
    private Integer userCount;

    /**
     * 关联设备数
     */
    private Integer equipmentCount;

    /**
     * 是否变更中，1-是，2-否
     */
    private Integer isChange;

    private List<DivideRuleDetailInfoDTO> ruleDetailInfoDTO;

    private String conditionId;

    /**
     * 业务类型,djb-多金宝,lyy-乐摇摇
     */
    private String businessType;

    /**
     * 层级类型
     */
    private String levelCode;

    /**
     * 当前周期的规则详情id
     */
    private Long currentRuleDetailId;

    /**
     * 是否自动结算 1-自动结算 2-记账  为空默认为自动结算
     */
    private Integer autoSettlement;
    /**
     * 规则存在变更的类型 @link
     * cn.lyy.merchant.constant.DivideEnum.DivideRuleChangeOperationTypeEnum
     */
    private String ruleChangeOperationType;

    /**
     * 场地名称
     */
    private String groupName;
    /**
     * 场地使用数量
     */
    private Integer storeCount;

}
