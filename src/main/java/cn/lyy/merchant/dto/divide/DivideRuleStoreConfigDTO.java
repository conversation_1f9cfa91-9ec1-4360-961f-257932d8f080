package cn.lyy.merchant.dto.divide;

import java.util.List;
import lombok.Data;
import lombok.ToString;

/**
 * 场地分账设置
 * <AUTHOR>
 */
@Data
@ToString
public class DivideRuleStoreConfigDTO {
    /**
     * 设备信息,主要是要场地ID
     */
    private List<MerchantStoreDivideDTO> stores;
    /**
     * 规则ID
     */
    private Long ruleId;

    /**
     * 是否清除该商户所选场地下的所有设备关联及待生效的关联
     */
    private Boolean isClearGroupEquCondition;

    /**
     * 批量设置情况下排除部分场地的设置
     */
    private List<MerchantStoreDivideDTO> excludeStores;

    /**
     * 是否全量设置
     */
    private Boolean isFullSet;

    /**
     * 场地名称
     */
    private String storeName;
    /**
     * 资金池-合作方式 1:商家自营 2:联合经营
     */
    private Integer cooperationMode;

}
