package cn.lyy.merchant.dto.divide;

import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/5/30
 */
@Data
@ToString
public class MerchantEquipmentDivideDTO implements Serializable {
    private static final long serialVersionUID = -9178018004876849436L;

    /**
     * 设备编号
     */
    @NotNull(message = "设备编号不能为空")
    private String equipmentValue;

    /**
     * 场地ID
     */
    private Long groupId;
}
