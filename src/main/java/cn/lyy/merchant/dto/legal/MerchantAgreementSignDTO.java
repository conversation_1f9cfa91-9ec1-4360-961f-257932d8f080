package cn.lyy.merchant.dto.legal;

import com.lyy.legal.interfaces.agreement.dto.request.LegalAgreementSignReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@ApiModel("协议签署")
@Data
public class MerchantAgreementSignDTO {

    @ApiModelProperty("签署操作，1-签署，2-撤销签署")
    @NotNull(message = "签署操作类型不能为空")
    private String operationType;

    @ApiModelProperty("服务编号")
    @NotNull(message = "服务编号不能为空")
    private String serviceCode;

    @ApiModelProperty("产品编号")
    @NotNull(message = "产品编号不能为空")
    private String productCode;

    @ApiModelProperty("签署对象类型")
    @NotNull(message = "签署对象类型不能为空")
    private String userTypeCode;
    
    @Valid
    @NotEmpty(message = "签署协议缺失")
    @ApiModelProperty("签署协议信息")
    private List<LegalAgreementSignReqDTO> legalAgreements;

}
