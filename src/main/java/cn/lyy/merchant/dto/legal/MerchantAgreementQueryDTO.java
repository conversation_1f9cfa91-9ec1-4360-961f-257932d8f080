package cn.lyy.merchant.dto.legal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@ApiModel("获取协议")
@Data
public class MerchantAgreementQueryDTO {

    @ApiModelProperty(value = "组织，LYY", hidden = true)
    private String companyCode;

    @ApiModelProperty("产品编号")
    private String productCode;

    @ApiModelProperty("服务编号")
    @NotNull(message = "服务编号缺失")
    private String serviceCode;

    @ApiModelProperty("用户类型：MERCHANT-B端商家")
    private String userTypeCode;

    @ApiModelProperty(hidden = true)
    private Long adOrgId;
}
