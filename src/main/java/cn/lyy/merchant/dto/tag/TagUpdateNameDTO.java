package cn.lyy.merchant.dto.tag;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.lyy.user.account.infrastructure.base.LongFromStringDeserializer;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;

/**
 * @description:
 * @author: qgw
 * @date on 2021-04-23.
 * @Version: 1.0
 */
@ToString
@Getter
@Setter
public class TagUpdateNameDTO {

    @JsonDeserialize(using = LongFromStringDeserializer.class)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long tagId;

    private String oldName;

    @NotEmpty(message = "标签新名称不能为空")
    private String newName;
}
