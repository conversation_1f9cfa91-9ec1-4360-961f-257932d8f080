package cn.lyy.merchant.dto.tag;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.lyy.user.account.infrastructure.base.LongFromStringDeserializer;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @description: 只有标签，没有用户数据
 * @author: qgw
 * @date on 2021-06-04.
 * @Version: 1.0
 */
@Getter
@Setter
@ToString
public class TagListDTO {

    @JsonDeserialize(using = LongFromStringDeserializer.class)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 标签名称
     */
    private String name;

    /**
     * 标签编码
     */
    private String code;


    /**
     * 标签类型，1:自动, 2:手动
     */
    private Integer category;


    private Integer businessType;

    private String businessTypeName;


    /**
     * 是否可用，true:是，false：否
     */
    private Boolean active;

}
