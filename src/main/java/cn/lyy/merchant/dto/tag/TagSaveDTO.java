package cn.lyy.merchant.dto.tag;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.lyy.user.account.infrastructure.base.LongFromStringDeserializer;
import com.lyy.user.account.infrastructure.base.StringArrayToLongDeserializer;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @description: 用户标签 创建或修改
 * @author: qgw
 * @date on 2021/4/2. @Version: 1.0
 */
@Getter
@Setter
@ToString
public class TagSaveDTO {

    @JsonDeserialize(using = LongFromStringDeserializer.class)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 标签名称
     */
    @NotEmpty(message = "标签名称不能为空")
    private String name;

    /**
     * 标签类型，1:自动, 2:手动
     */
    @NotNull(message = "标签类型不能为空")
    private Integer category;

    /**
     * 备注
     */
    private String description;

    /**
     * 是否可用，true:是，false：否
     */
    private Boolean active;

    @JsonDeserialize(using = StringArrayToLongDeserializer.class)
    private List<Long> userIds;
}
