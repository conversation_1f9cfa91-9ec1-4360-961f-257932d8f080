package cn.lyy.merchant.dto.tag;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.lyy.user.account.infrastructure.base.LongFromStringDeserializer;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;

/**
 * @description: 标签管理页面查询 DTO
 * @author: qgw
 * @date on 2021/4/19.
 * @Version: 1.0
 */
@ToString
@Getter
@Setter
public class TagQueryDTO {
    /**
     * 标签
     */
    @JsonDeserialize(using = LongFromStringDeserializer.class)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /**
     * 标签
     */
    @JsonDeserialize(using = LongFromStringDeserializer.class)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long merchantUserId;

    /**
     * 商户id
     */
    private Long merchantId;

    /**
     * 标签名称
     */
    private String name;

    /**
     * 启用状态，默认查启动状态的
     */
    private Boolean active;
    /**
     *
     * 1.自动标签
     * 2.手动标签
     * */
    @NotNull(message = "请指定标签类型")
    @Range(min = 1, message = "标签类型错误",max = 2)
    private Integer category;
    /**
     *
     *     NORMAL(0, "普通标签"),
     *     GROUP_NAME(1, "场地名称"),
     *     EQUIPMENT(2, "设备类型"),
     *     PAY_TYPE(3, "支付方式标签"),
     *     SEX(4, "性别标签"),
     *     MEMBER(5, "会员标签");
     * */
    private Integer businessType;

    /**
     * 页码
     */
    @Range(min = 1, message = "页码参数错误")
    @NotNull
    private Integer pageIndex;

    /**
     * 数据量
     */
    @NotNull
    @Range(min = 10, message = "页码参数错误")
    private Integer pageSize;

    /**
     * 默认是查标签关联用户数据
     * flase是不查
     */
    private Boolean queryUserNumber;
}
