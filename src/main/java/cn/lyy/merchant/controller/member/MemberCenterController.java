package cn.lyy.merchant.controller.member;

import cn.hutool.core.lang.Validator;
import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.lyy_data_service_api.LyyDistributorStatisticsDayDTO;
import cn.lyy.merchant.controller.common.UserModelContrller;
import cn.lyy.merchant.dto.common.UserInfoDTO;
import cn.lyy.merchant.dto.member.MemberGiftRedPacketSettingDTO;
import cn.lyy.merchant.dto.member.MemberInfoDTO;
import cn.lyy.merchant.dto.member.MemberInfoQueryDTO;
import cn.lyy.merchant.microservice.LyyDataService;
import cn.lyy.merchant.service.MemberService;
import cn.lyy.merchant.utils.ValidatorUtils;
import cn.lyy.tools.util.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Strings;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 * <p>Desc: 会员中心接口（旧）</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020/11/21
 */
@Slf4j
@RequestMapping("/rest/member-center")
@RestController
public class MemberCenterController extends UserModelContrller {

    @Autowired
    private MemberService memberService;

    @Autowired
    private LyyDataService lyyDataService;


    /**
     * 获取未处理申诉记录数量
     * @param param
     * @return
     */
    @PostMapping("/appealCount")
    @AuthorityResource(name = "获取未处理申诉记录数量", value = "mb_appealCount", seq = 2, role = "Saas_Merchant", parentValue = "MemberCenter")
    public BaseResponse<Integer> appealCount(@ModelAttribute(value = "param") JSONObject param){
        UserInfoDTO userInfoDTO = JSON.toJavaObject(param, UserInfoDTO.class);
        return memberService.appealCount(userInfoDTO);
    }


    /**
     * 获取会员总数
     * @return
     */
    @GetMapping("/total")
    @AuthorityResource(name = "获取会员总数", value = "mb_member_total_info", seq = 4, role = "Saas_Merchant", parentValue = "MemberCenter")
    public BaseResponse<Integer> total() {
        Date query;
        Date yesterday = DateUtil.getYesterday();
        Date current = new Date();
        // 如果是 4点 之前取前天，否则取昨天。统计时间为 3点
        query = DateUtil.add(current, Calendar.HOUR_OF_DAY, -4)
                .after(DateUtil.getDateDate(current))
                ? yesterday
                : DateUtil.add(yesterday, Calendar.DAY_OF_MONTH, -1);

        List<LyyDistributorStatisticsDayDTO> json = lyyDataService.getDistributorStatisticsDay(
                String.valueOf(getAdOrgIdNotNull()), DateUtil.format(query, "yyyy-MM-dd"));
        if (json != null && json.size() > 0) {
            LyyDistributorStatisticsDayDTO dto = json.get(0);
            return ResponseUtils.success(dto.getUserCount());
        }
        return ResponseUtils.success(0);
    }
    /**
     * 获取会员列表
     * @param param
     * @return
     */
    @PostMapping("/memberList")
    @AuthorityResource(name = "获取会员列表", value = "mb_memberList", seq = 5, role = "Saas_Merchant", parentValue = "MemberCenter")
    public BaseResponse memberList(@ModelAttribute(value = "param") JSONObject param) {
        MemberInfoQueryDTO queryDTO = JSON.toJavaObject(param, MemberInfoQueryDTO.class);
        String error = ValidatorUtils.validDTO(queryDTO);
        if(StringUtils.isNotBlank(error)) {
            return error(ResponseCodeEnum.PARAMETER_ERROR.getCode(), error);
        }
        return success(memberService.memberList(queryDTO));
    }

    /**
     * 获取会员详情
     * @param param
     * @return
     */
    @PostMapping("/memberDetail")
    @AuthorityResource(name = "获取会员详情", value = "mb_memberDetail", seq =6, role = "Saas_Merchant", parentValue = "MemberCenter")
    public BaseResponse<MemberInfoDTO> memberDetail(@ModelAttribute(value = "param") JSONObject param) {
        String lyyUserId = param.getString("lyyUserId");
        if(StringUtils.isNotBlank(lyyUserId) && !Validator.isNumber(lyyUserId)){
            return error(ResponseCodeEnum.PARAMETER_ERROR.getCode(), "参数类型错误");
        }
        MemberInfoQueryDTO queryDTO = JSON.toJavaObject(param, MemberInfoQueryDTO.class);
        queryDTO.setTelephone(param.getString("lyyUserPhone"));
        if(Objects.isNull(queryDTO.getLyyUserId()) && Strings.isNullOrEmpty(queryDTO.getTelephone())) {
            return error(ResponseCodeEnum.PARAMETER_ERROR.getCode(), ResponseCodeEnum.PARAMETER_ERROR.getMsg());
        }
        return success(memberService.memberDetail(queryDTO));
    }

    /**
     * 赠送红包
     * @param param
     * @return
     */
    @PostMapping("/sendRedBag")
    @AuthorityResource(name = "赠送红包", value = "mb_sendRedBag", seq = 7, role = "Saas_Merchant", parentValue = "MemberCenter")
    public BaseResponse sendRedBag(@ModelAttribute(value = "param") JSONObject param){
        MemberGiftRedPacketSettingDTO settingDTO = JSON.toJavaObject(param, MemberGiftRedPacketSettingDTO.class);
        return success(memberService.giftRedPacket(settingDTO));
    }


    /**
     * 获取赠送红包记录列表
     * @param param
     * @return
     */
    @PostMapping("/redBagRecordList")
    @AuthorityResource(name = "获取赠送红包记录", value = "mb_redBagRecordList", seq = 8, role = "Saas_Merchant", parentValue = "MemberCenter")
    public BaseResponse redBagRecordList(@ModelAttribute(value = "param") JSONObject param) {
        MemberInfoQueryDTO queryDTO = JSON.toJavaObject(param, MemberInfoQueryDTO.class);
        String error = ValidatorUtils.validDTO(param);
        if(StringUtils.isNotBlank(error)) {
            return error(ResponseCodeEnum.PARAMETER_ERROR.getCode(), ResponseCodeEnum.PARAMETER_ERROR.getMsg());
        }
        return success(memberService.giftRedPacketRecordList(queryDTO));
    }

    /**
     * 清除会员余额
     * @param param
     * @return
     */
    @PostMapping("/clearBalance")
    @AuthorityResource(name = "清除会员余额", value = "mb_clearBalance", seq = 9, role = "Saas_Merchant", parentValue = "MemberCenter")
    public  BaseResponse clearBalance(@ModelAttribute(value = "param") JSONObject param){
        MemberInfoQueryDTO queryDTO = JSON.toJavaObject(param, MemberInfoQueryDTO.class);
        return success(memberService.clearBalance(queryDTO));
    }
}
