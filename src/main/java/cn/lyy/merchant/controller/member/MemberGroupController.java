package cn.lyy.merchant.controller.member;

import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.merchant.controller.common.BaseController;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.user.account.infrastructure.member.dto.*;
import com.lyy.user.account.infrastructure.member.feign.MemberGroupFeignClient;
import com.lyy.user.account.infrastructure.resp.RespBody;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

/**
 * 新会员中心接口-会员组接口
 * <AUTHOR>
 * @className: MemberGroupController
 * @date 2021/4/22
 */
@Slf4j
@RestController
@RequestMapping(value = "rest/member/group")
public class MemberGroupController extends BaseController {

    @Autowired
    private MemberGroupFeignClient memberGroupFeignClient;
//    @Autowired
//    private EquipmentTypeService equipmentTypeService;
//    @Autowired
//    private MerchantGroupService merchantGroupService;
//    @Autowired
//    private IEquipmentBusinessService equipmentBusinessService;

    /**
     * 分页获取会员数据
     * @param memberGroupPageRequestDTO
     * @return
     */
    @PostMapping("findByPage")
    @AuthorityResource(name = "分页获取会员数据", value = "mb_findMemberGroupByPage", seq = 2, role = "Saas_Merchant", parentValue = "MemberCenter")
    public BaseResponse<Page<MemberGroupPageResponseDTO>> findByPage(@RequestBody MemberGroupPageRequestDTO memberGroupPageRequestDTO){
        memberGroupPageRequestDTO.setMerchantId(getAdOrgIdNotNull());
        RespBody<Page<MemberGroupPageResponseDTO>> respBody = memberGroupFeignClient.findByPage(memberGroupPageRequestDTO);
        log.debug("分页获取会员数据结果为 -->{}",respBody);
        if(GlobalErrorCode.OK.getCode().equals(respBody.getCode())){
            return ResponseUtils.success(respBody.getBody());
        }
        String msg = Optional.ofNullable(respBody.getMessage()).orElse("分页获取会员数据失败");
        return ResponseUtils.error(ResponseCodeEnum.FAIL.getCode(),msg);
    }

    /**
     * 获取默认会员组信息
     * @return
     */
    @GetMapping("getDefaultMemberGroup")
    @AuthorityResource(name = "获取默认会员组信息", value = "mb_getDefaultMemberGroup", seq = 2, role = "Saas_Merchant", parentValue = "MemberCenter")
    public BaseResponse<MemberGroupInfoSaveDTO> getDefaultMemberGroup(){
        RespBody<MemberGroupInfoSaveDTO> respBody = memberGroupFeignClient.getDefaultMemberGroup();
        log.debug("获取默认会员组信息结果为 -->{}",respBody);
        if(GlobalErrorCode.OK.getCode().equals(respBody.getCode())){
            return ResponseUtils.success(respBody.getBody());
        }
        return ResponseUtils.error(ResponseCodeEnum.FAIL.getCode(),"获取默认会员组信息失败");
    }

    /**
     * 保存会员组全部信息，包括等级，升级策略等信息
     * @param memberGroupInfoSaveDTO
     * @return 会员id
     */
    @PostMapping("saveMemberGroupInfo")
    @AuthorityResource(name = "保存会员组全部信息", value = "mb_saveMemberGroupInfo", seq = 2, role = "Saas_Merchant", parentValue = "MemberCenter")
    public BaseResponse<Long> saveMemberGroupInfo(@RequestBody MemberGroupInfoSaveDTO memberGroupInfoSaveDTO){
        memberGroupInfoSaveDTO.getMemberGroupSaveDTO().setMerchantId(getAdOrgIdNotNull());
        RespBody<Long> respBody = memberGroupFeignClient.saveMemberGroupInfo(memberGroupInfoSaveDTO);
        log.debug("保存会员组信息结果为 -->{}",respBody);
        if(GlobalErrorCode.OK.getCode().equals(respBody.getCode())){
            return ResponseUtils.success(respBody.getBody());
        }
        String msg = Optional.ofNullable(respBody.getMessage()).orElse("保存会员组信息失败");
        return ResponseUtils.error(ResponseCodeEnum.FAIL.getCode(),msg);
    }

    /**
     * 更新会员组状态
     * @param memberGroupDTO
     * @return
     */
    @PostMapping("updateStatus")
    @AuthorityResource(name = "更新会员组状态", value = "mb_updateMemberGroupStatus", seq = 2, role = "Saas_Merchant", parentValue = "MemberCenter")
    public BaseResponse<Boolean> updateStatus(@RequestBody MemberGroupDTO memberGroupDTO){
        memberGroupDTO.setMerchantId(getAdOrgIdNotNull());
        RespBody<Boolean> respBody = memberGroupFeignClient.updateStatus(memberGroupDTO);
        log.debug("更新会员组状态结果为 -->{}",respBody);
        if(GlobalErrorCode.OK.getCode().equals(respBody.getCode())){
            return ResponseUtils.success(respBody.getBody());
        }
        return ResponseUtils.error(ResponseCodeEnum.FAIL.getCode(),"更新会员组状态失败");
    }

    /**
     * 获取会员组详情(包括统计会员信息)
     * @param memberGroupId 会员组ID
     * @return
     */
    @GetMapping("getInfoCountById")
    @AuthorityResource(name = "获取会员组详情(包括统计会员信息)", value = "mb_getMemberGroupInfoCountById", seq = 2, role = "Saas_Merchant", parentValue = "MemberCenter")
    public BaseResponse<MemberGroupResponseDTO> getInfoCountById(@RequestParam("memberGroupId") Long memberGroupId){
        RespBody<MemberGroupResponseDTO> respBody = memberGroupFeignClient.getInfoCountById(getAdOrgIdNotNull(),memberGroupId);
        log.debug("获取 {} 会员组详情(包括统计会员信息)结果为 -->{}",memberGroupId,respBody);
        if(GlobalErrorCode.OK.getCode().equals(respBody.getCode())){
            return ResponseUtils.success(respBody.getBody());
        }
        String msg = Optional.ofNullable(respBody.getMessage()).orElse("获取会员组详情(包括统计会员信息)失败");
        return ResponseUtils.error(ResponseCodeEnum.FAIL.getCode(),msg);
    }

    /**
     * 获取会员组详情(用于保存信息)
     * @param memberGroupId 会员组ID
     * @return
     */
    @GetMapping("getInfoSaveById")
    @AuthorityResource(name = "获取会员组详情(用于保存信息)", value = "mb_getMemberGroupInfoSaveById", seq = 2, role = "Saas_Merchant", parentValue = "MemberCenter")
    public BaseResponse<MemberGroupInfoSaveDTO> getInfoSaveById(@RequestParam("memberGroupId") Long memberGroupId){
        RespBody<MemberGroupInfoSaveDTO> respBody = memberGroupFeignClient.getInfoSaveById(getAdOrgIdNotNull(),memberGroupId);
        log.debug("获取 {} 会员组详情(用于保存信息)结果为 -->{}",memberGroupId,respBody);
        if(GlobalErrorCode.OK.getCode().equals(respBody.getCode())){
            return ResponseUtils.success(respBody.getBody());
        }
        String msg = Optional.ofNullable(respBody.getMessage()).orElse("获取会员组详情(用于保存信息)失败");
        return ResponseUtils.error(ResponseCodeEnum.FAIL.getCode(),msg);
    }

    /**
     * 根据用户组信息分页获取会员信息
     * @param current 当前页
     * @param size
     * @param memberGroupId
     * @return
     */
    @GetMapping("findMemberByMemberGroup")
    @AuthorityResource(name = "根据用户组信息分页获取会员信息", value = "mb_findMemberByMemberGroup", seq = 2, role = "Saas_Merchant", parentValue = "MemberCenter")
    public BaseResponse<Page<MemberInfoDTO>> findMemberByMemberGroup(@RequestParam("current") Integer current, @RequestParam("size") Integer size,
                                                                     @RequestParam("memberGroupId") Long memberGroupId,
                                                                     @RequestParam(value = "memberLevelId",required = false) Long memberLevelId){
        RespBody<Page<MemberInfoDTO>> respBody = memberGroupFeignClient.findMemberByMemberGroup(current,size,getAdOrgIdNotNull(),memberGroupId,memberLevelId);
        log.debug("根据 {} 用户组信息分页获取会员信息结果为 -->{}",memberGroupId,respBody);
        if(GlobalErrorCode.OK.getCode().equals(respBody.getCode())){
            return ResponseUtils.success(respBody.getBody());
        }
        String msg = Optional.ofNullable(respBody.getMessage()).orElse("分页获取会员信息失败");
        return ResponseUtils.error(ResponseCodeEnum.FAIL.getCode(),msg);
    }
    /**
     * 删除会员组信息
     * @param memberGroupId 会员组ID
     * @return
     */
    @GetMapping("removeByMemberGroup")
    @AuthorityResource(name = "删除会员组信息", value = "mb_removeByMemberGroup", seq = 5, role = "Saas_Merchant", parentValue = "MemberCenter")
    public BaseResponse<Boolean> removeByMemberGroup(@RequestParam("memberGroupId") Long memberGroupId){
        RespBody<Boolean> respBody = memberGroupFeignClient.removeByMemberGroup(getAdOrgIdNotNull(),memberGroupId);
        log.debug("删除 {} 会员组详情)结果为 -->{}",memberGroupId,respBody);
        if(GlobalErrorCode.OK.getCode().equals(respBody.getCode())){
            return ResponseUtils.success(respBody.getBody());
        }
        return ResponseUtils.error(ResponseCodeEnum.FAIL.getCode(),"删除会员组失败");
    }

//    /**
//     * 获取商户下的设备、场地、品类信息
//     * @return
//     */
//    @GetMapping("getAssociated")
//    @AuthorityResource(name = "获取商户下的设备、场地、品类信息", value = "mb_getAssociated", seq = 2, role = "Saas_Merchant", parentValue = "MemberCenter")
//    public BaseResponse<Map<String,Object>> getAssociated(){
//        Map<String,Object> resultMap = new HashMap<>();
//        AdUserInfoDTO adUserInfoDTO = getCurrentUser();
//        //品类信息
//        List<EquipmentTypeDTO> equipmentTypeList = equipmentTypeService.selectAllEquipmentType(adUserInfoDTO.getAdUserId(),adUserInfoDTO.getAdOrgId());
//        resultMap.put("equipmentTypeList",equipmentTypeList);
//        //场地信息
//        MerchantGroupRequest request = MerchantGroupRequest.builder()
//                .distributor(adUserInfoDTO.getAdOrgId())
//                .showGroupLabel(false)
//                .isActive(1)
//                .build();
//        List<MerchantGroupDTO> equipmentGroupList = ofNullable(merchantGroupService.selectGroup(request).getData()).orElse(Collections.emptyList());
//        resultMap.put("equipmentGroupList",equipmentGroupList);
//        //设备查询
//        EquipmentQueryDTO equipmentQueryDTO = new EquipmentQueryDTO();
//        equipmentQueryDTO.setLyyDistributorId(adUserInfoDTO.getAdOrgId().intValue());
//        List<EquipmentListDTO>  equipmentList = equipmentBusinessService.queryByTypeAndGroup(equipmentQueryDTO,false);
//        resultMap.put("equipmentList",equipmentList);
//        return ResponseUtils.success(resultMap);
//    }

}
