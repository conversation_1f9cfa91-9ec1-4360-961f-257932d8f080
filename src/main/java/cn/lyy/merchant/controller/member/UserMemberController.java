package cn.lyy.merchant.controller.member;

import static java.util.Optional.ofNullable;

import cn.hutool.core.collection.CollUtil;
import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.base.converter.CommonConverterTools;
import cn.lyy.base.dto.JsonObject;
import cn.lyy.base.dto.Status;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.bigdata.api.client.MerchantUserClient;
import cn.lyy.life.api.charging.service.TerraceDataMappingApi;
import cn.lyy.lyy_cmember_service_api.UserCardService;
import cn.lyy.lyy_cmember_service_api.enums.ResponseEnum;
import cn.lyy.lyy_coupon_api.LyyCouponService;
import cn.lyy.lyy_coupon_api.dto.customer.UserAllCouponDTO;
import cn.lyy.lyy_ic_service_api.LyyIcService;
import cn.lyy.lyy_ic_service_api.dto.UserIcCardsDTO;
import cn.lyy.lyy_ic_service_api.form.IcCardQueryForm;
import cn.lyy.merchant.config.MemberQueryConfig;
import cn.lyy.merchant.constants.BusinessExceptionEnums;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.member.BenefitDetailQueryDTO;
import cn.lyy.merchant.dto.member.BenefitQueryDTO;
import cn.lyy.merchant.dto.member.PayoutWelfareDTO;
import cn.lyy.merchant.dto.member.query.ExternalBalanceQuery;
import cn.lyy.merchant.dto.member.vo.ExternalBalanceVO;
import cn.lyy.merchant.dto.request.UserBalanceAdjustReqDTO;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import cn.lyy.merchant.dto.user.BalanceQueryDTO;
import cn.lyy.merchant.dto.user.UserDTO;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.service.IEquipmentGroupBusinessService;
import cn.lyy.merchant.service.member.UserMemberService;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.pojo.entity.ThirdBenefitAccount;
import com.lyy.pojo.query.ThirdAccountQuery;
import com.lyy.service.ThirdBenefitService;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitDataDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitResultDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountConditionDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountQueryDTO;
import com.lyy.user.account.infrastructure.account.feign.AccountFeignClient;
import com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum;
import com.lyy.user.account.infrastructure.constant.BenefitClassifyGroupEnum;
import com.lyy.user.account.infrastructure.member.dto.MemberUserPageDTO;
import com.lyy.user.account.infrastructure.member.feign.MemberFeignClient;
import com.lyy.user.account.infrastructure.resp.RespBody;
import com.lyy.user.account.infrastructure.statistics.dto.MerchantStatisticsRecordDTO;
import com.lyy.user.account.infrastructure.statistics.dto.MerchantUserStatisticsListDTO;
import com.lyy.user.account.infrastructure.statistics.dto.StatisticsUserQueryDTO;
import com.lyy.user.account.infrastructure.statistics.dto.UserStatisticsConditionDTO;
import com.lyy.user.account.infrastructure.statistics.dto.UserStatisticsRecordDTO;
import com.lyy.user.account.infrastructure.statistics.feign.StatisticsFeignClient;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserListDTO;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserQueryDTO;
import com.lyy.user.account.infrastructure.user.dto.UserInfoDTO;
import com.lyy.user.account.infrastructure.user.feign.UserFeignClient;
import com.lyy.user.app.interfaces.facade.rpc.IUserRpc;
import io.swagger.annotations.ApiOperation;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 新会员中心接口
 *
 * <AUTHOR>
 * @create 2021/4/19 10:53
 */
@RestController
@RequestMapping(value = "/userMember")
@Slf4j
public class UserMemberController extends BaseController {

    @Autowired
    private UserMemberService userMemberService;

    @Autowired
    private AccountFeignClient accountFeignClient;

    @Autowired
    private UserFeignClient userFeignClient;
    @Autowired
    private MemberFeignClient memberFeignClient;

    @Autowired
    private IEquipmentGroupBusinessService equipmentGroupBusinessService;

    @Autowired
    private LyyIcService lyyIcService;

    @Autowired
    private LyyCouponService lyyCouponService;

    @Autowired
    private UserCardService userCardService;

    @Autowired
    private StatisticsFeignClient statisticsFeignClient;
    @Autowired
    private TerraceDataMappingApi terraceDataMappingApi;
    @Autowired
    private ThirdBenefitService thirdBenefitService;
    @Autowired
    private IUserRpc iUserRpc;
    /**
     * 币类型权益
     */
    private static final List<Integer> COINS_CLASSIFIES = BenefitClassifyGroupEnum.getClassifyByType(1);
    /**
     * 金额类型权益
     */
    private static final List<Integer> BALANCE_CLASSIFIES = BenefitClassifyGroupEnum.getClassifyByType(2);
    /**
     * 券类型权益
     */
    private static final List<Integer> COUPON_CLASSIFIES = BenefitClassifyGroupEnum.getClassifyByType(3);

    /**
     * 商户查询用户会员列表
     *
     * @param merchantUserQueryDTO
     * @return
     */
    @PostMapping("/queryUserListByMerchant")
    @AuthorityResource(name = "商户查询用户会员列表", value = "mb_userList", seq = 2, role = "Saas_Merchant", parentValue = "MemberCenter")
    public BaseResponse<Page<MerchantUserListDTO>> queryUserListByMerchant(@RequestBody MerchantUserQueryDTO merchantUserQueryDTO) {
        AdUserInfoDTO currentUser = getCurrentUser();
        merchantUserQueryDTO.setMerchantId(currentUser.getAdOrgId());
        Page<MerchantUserListDTO> pageInfo = userMemberService.queryUserListByMerchant(merchantUserQueryDTO, currentUser);
        return ResponseUtils.success(pageInfo);
    }

    /**
     * 查询用户会员总数
     * @param merchantUserQueryDTO
     * @return
     */
    @PostMapping("/countUserListByMerchant")
    @AuthorityResource(name = "商户查询用户会员总数", value = "mb_count_userList", seq = 2, role = "Saas_Merchant", parentValue = "MemberCenter")
    public BaseResponse<Long> countUserListByMerchant(@RequestBody MerchantUserQueryDTO merchantUserQueryDTO) {
        merchantUserQueryDTO.setMerchantId(this.getAdOrgIdNotNull());
        Long l = userMemberService.countUserListByMerchant(merchantUserQueryDTO);
        return ResponseUtils.success(l);
    }

    /**
     * 查询用户余额
     *
     * @return 返回key为类型的余额map
     */
    @PostMapping("/balance")
    @AuthorityResource(name = "查询用户余额", value = "mb_accountBalance", seq = 2, role = "Saas_Merchant", parentValue = "MemberCenter")
    public BaseResponse<Map<Integer, BigDecimal>> balanceList(@RequestBody BalanceQueryDTO param) {
        AccountQueryDTO query = new AccountQueryDTO();
        query.setMerchantId(super.getAdOrgIdNotNull());
        query.setMerchantUserId(param.getMerchantUserId());
        query.setBenefitClassify(param.getClassifyList());
        query.setStoreIds(param.getStoreId());
        query.setEquipmentTypeIds(param.getEquipmentTypeId());
        RespBody<Map<Integer, BigDecimal>> resp = accountFeignClient.benefitCount(query);
        if (!GlobalErrorCode.OK.getCode().equals(resp.getCode())) {
            throw new BusinessException(resp.getMessage());
        }
        return ResponseUtils.success(resp.getBody());
    }

    ///**
    // * 查询账户流水
    // */
    //@PostMapping("/account/record")
    //@AuthorityResource(name = "查询账户流水", value = "mb_accountRecord", seq = 2, role = "Saas_Merchant", parentValue = "MemberCenter")
    //public BaseResponse<Page<AccountRecordDTO>> accountRecord(@RequestBody AccountRecordQueryDTO param) {
    //    param.setMerchantId(super.getAdOrgIdNotNull());
    //    RespBody<Page<AccountRecordDTO>> resp = accountFeignClient.recordPage(param);
    //    if (!GlobalErrorCode.OK.getCode().equals(resp.getCode())) {
    //        throw new BusinessException(resp.getMessage());
    //    }
    //    return ResponseUtils.success(resp.getBody());
    //}

//    /**
//     * 查询权益详情记录 （未使用接口）
//     *
//     * @param param
//     * @return
//     */
//    @Deprecated
//    @PostMapping("/benefit/record")
//    @AuthorityResource(name = "查询权益详情记录", value = "mb_benefitRecord", seq = 2, role = "Saas_Merchant", parentValue = "MemberCenter")
//    public BaseResponse<List<AccountBenefitDTO>> benefitRecord(@RequestBody AccountBenefitConditionDTO param) {
//        param.setMerchantId(super.getAdOrgIdNotNull());
//        RespBody<List<AccountBenefitDTO>> resp = accountFeignClient.benefitRecord(param);
//        if (!GlobalErrorCode.OK.getCode().equals(resp.getCode())) {
//            throw new BusinessException(resp.getMessage());
//        }
//        return ResponseUtils.success(resp.getBody());
//    }

    /**
     * 用户权益信息-汇总
     *
     * @param param
     * @return
     */
    @PostMapping("/benefit/findAccountBenefitData")
    @AuthorityResource(name = "查询用户权益汇总信息", value = "mb_findAccountBenefitData", seq = 6, role = "Saas_Merchant", parentValue = "MemberCenter")
    public BaseResponse<AccountBenefitDataDTO> findAccountBenefitData(@Valid @RequestBody BenefitQueryDTO param) {
        log.debug("查询用户权益信息-汇总：{}", param);
        Long merchantId = super.getAdOrgIdNotNull();
        AccountBenefitQueryDTO dto = new AccountBenefitQueryDTO();
        BeanUtils.copyProperties(param, dto);
        dto.setMerchantUserId(param.getMerchantUserId());
        dto.setMerchantId(merchantId);
        if (param.getClassify()==null && param.getBenefitGroupType() != null) {
            Integer benefitGroupType = param.getBenefitGroupType();
            dto.setClassify(BenefitClassifyGroupEnum.getClassifyByType(benefitGroupType));
        } else {
            dto.setClassify(Collections.singletonList(param.getClassify()));
        }
        if (!CollectionUtils.isEmpty(param.getBenefitIds())) {
            dto.setBenefitIds(param.getBenefitIds().stream().map(Long::valueOf).collect(Collectors.toList()));
        }

        dto.setPageIndex(1);
        dto.setPageSize(10);
        dto.setExcludeExpire(true);
        RespBody<AccountBenefitDataDTO> resp = accountFeignClient.findAccountBenefitData(dto);
        if (!GlobalErrorCode.OK.getCode().equals(resp.getCode())) {
            throw new BusinessException(resp.getMessage());
        }
        AccountBenefitDataDTO data = resp.getBody();
        List<String> factories = listFactory(merchantId);
        if (!CollectionUtils.isEmpty(factories)) {
            ThirdAccountQuery thirdAccountQuery = new ThirdAccountQuery()
                    .setMerchantUserId(param.getMerchantUserId())
                    .setMerchantId(merchantId)
                    .setFactoryIds(factories);
            BigDecimal thirdBalance = getThirdBalance(thirdAccountQuery);
            AccountBenefitResultDTO abr = new AccountBenefitResultDTO();
            abr.setBalance(thirdBalance);
            abr.setClassify(BenefitClassifyEnum.HISTORY_BALANCE.getCode());
            abr.setClassifyName(BenefitClassifyEnum.HISTORY_BALANCE.getDesc());
            if (!CollectionUtils.isEmpty(data.getData())) {
                data.getData().add(abr);
            } else {
                List<AccountBenefitResultDTO> dataList = Lists.newArrayList(abr);
                data.setData(dataList);
            }
        }
        return ResponseUtils.success(data);
    }

    /**
     * 查询用户权益信息-明细
     *
     * @param param
     * @return
     */
    @PostMapping("/benefit/listBenefitDetail")
    @AuthorityResource(name = "查询用户权益信息-明细", value = "mb_listBenefitDetail", seq = 7, role = "Saas_Merchant", parentValue = "MemberCenter")
    public BaseResponse<Page<AccountBenefitDTO>> listBenefitDetail(@Valid @RequestBody BenefitDetailQueryDTO param) {
        log.debug("查询用户权益信息-明细：{}", param);
        AccountBenefitQueryDTO dto = new AccountBenefitQueryDTO();
        BeanUtils.copyProperties(param, dto);
        dto.setMerchantUserId(param.getMerchantUserId());
        dto.setMerchantId(super.getAdOrgIdNotNull());
        if (param.getClassify()==null && param.getBenefitGroupType() != null) {
            Integer benefitGroupType = param.getBenefitGroupType();
            dto.setClassify(BenefitClassifyGroupEnum.getClassifyByType(benefitGroupType));
        } else {
            dto.setClassify(Collections.singletonList(param.getClassify()));
        }
        if (!CollectionUtils.isEmpty(param.getBenefitIds())) {
            dto.setBenefitIds(param.getBenefitIds().stream().map(Long::valueOf).collect(Collectors.toList()));
        }
        dto.setCountSql(false);        RespBody<Page<AccountBenefitDTO>> resp = accountFeignClient.listBenefitDetail(dto);
        if (!GlobalErrorCode.OK.getCode().equals(resp.getCode())) {
            throw new BusinessException(resp.getMessage());
        }
        return ResponseUtils.success(resp.getBody());
    }

    /**
     * 获取选中权益的全部剩余金额
     *
     * @param param
     * @return
     */
//    @PostMapping("/benefit/getChooseBenefitBalance")
//    @AuthorityResource(name = "获取选中权益的全部剩余金额", value = "mb_getChooseBenefitBalance", seq = 7, role = "Saas_Merchant", parentValue = "MemberCenter")
//    public BaseResponse<BigDecimal> getChooseBenefitBalance(@Valid @RequestBody BenefitDetailQueryDTO param) {
//        log.debug("获取选中权益的全部剩余金额：{}", param);
//        AccountBenefitQueryDTO dto = new AccountBenefitQueryDTO();
//        BeanUtils.copyProperties(param, dto);
//        dto.setMerchantUserId(param.getMerchantUserId());
//        dto.setMerchantId(super.getAdOrgIdNotNull());
//        if (param.getClassify()==null && param.getBenefitGroupType() != null) {
//            Integer benefitGroupType = param.getBenefitGroupType();
//            dto.setClassify(BenefitClassifyGroupEnum.getClassifyByType(benefitGroupType));
//        } else {
//            dto.setClassify(Collections.singletonList(param.getClassify()));
//        }
//        if (!CollectionUtils.isEmpty(param.getBenefitIds())) {
//            dto.setBenefitIds(param.getBenefitIds().stream().map(Long::valueOf).collect(Collectors.toList()));
//        }
//        if (!CollectionUtils.isEmpty(param.getNotHandleBenefitIds())) {
//            dto.setNotHandleBenefitIdList(param.getNotHandleBenefitIds().stream().map(Long::valueOf).collect(Collectors.toList()));
//        }
//        dto.setOperatorId(super.getAdUserIdNotNull());
//
//        RespBody<BigDecimal> resp = accountFeignClient.getChooseBenefitBalance(dto);
//        if (!GlobalErrorCode.OK.getCode().equals(resp.getCode())) {
//            throw new BusinessException(resp.getMessage());
//        }
//        return ResponseUtils.success(resp.getBody());
//    }


//    /**
//     * 清除权益余额-选择特定权益ID
//     *
//     * @return
//     */
//    @PostMapping("/benefit/clear")
//    @AuthorityResource(name = "清除权益余额", value = "mb_benefitClear", seq = 2, role = "Saas_Merchant", parentValue = "MemberCenter")
//    public BaseResponse benefitClear(@RequestBody List<String> param) {
//        log.debug("清除权益余额-选择特定权益ID：{}", param);
//        if (CollectionUtils.isEmpty(param)) {
//            log.error("请选择权益ID:");
//            return ResponseUtils.error(Status.STATUS_FAIL, "请选择权益ID");
//        }
//        List<AccountBenefitAdjustDTO> paramList = new ArrayList<>();
//        for (String id : param) {
//            AccountBenefitAdjustDTO adjustDTO = new AccountBenefitAdjustDTO();
//            adjustDTO.setMerchantId(super.getAdOrgIdNotNull());
//            adjustDTO.setId(Long.valueOf(id));
//            paramList.add(adjustDTO);
//        }
//        accountFeignClient.benefitClear(paramList);
//        return ResponseUtils.success();
//    }

    /**
     * 筛选条件下的权益清除
     *
     * @return
     */
//    @PostMapping("/benefit/cleanAll")
//    @AuthorityResource(name = "筛选条件下的权益清除", value = "mb_benefitCleanAll", seq = 22, role = "Saas_Merchant", parentValue = "MemberCenter")
//    public BaseResponse benefitCleanAll(@Valid @RequestBody BenefitQueryDTO param) {
//        log.debug("筛选条件下的权益清除：{}", param);
//        AccountBenefitQueryDTO dto = new AccountBenefitQueryDTO();
//        BeanUtils.copyProperties(param, dto);
//        dto.setMerchantUserId(param.getMerchantUserId());
//        dto.setMerchantId(super.getAdOrgIdNotNull());
//        if (param.getClassify()==null && param.getBenefitGroupType() != null) {
//            Integer benefitGroupType = param.getBenefitGroupType();
//            dto.setClassify(BenefitClassifyGroupEnum.getClassifyByType(benefitGroupType));
//        } else {
//            dto.setClassify(Collections.singletonList(param.getClassify()));
//        }
//        if (!CollectionUtils.isEmpty(param.getBenefitIds())) {
//            dto.setBenefitIds(param.getBenefitIds().stream().map(Long::valueOf).collect(Collectors.toList()));
//        }
//        dto.setPageIndex(1);
//        dto.setPageSize(Integer.MAX_VALUE);
//        RespBody<Void> resp = accountFeignClient.benefitCleanAll(dto);
//        if (!GlobalErrorCode.OK.getCode().equals(resp.getCode())) {
//            throw new BusinessException(resp.getMessage());
//        }
//        return ResponseUtils.success();
//    }
    /**
     * 获取用户信息
     *
     * @param userId         用户id
     * @param merchantUserId 商户用户Id
     * @return
     */
    @GetMapping("/getUserInfo")
    @AuthorityResource(name = "获取用户信息", value = "mb_new_getUserInfo", seq = 3, role = "Saas_Merchant", parentValue = "MemberCenter")
    public BaseResponse<UserDTO> getUserInfo(@RequestParam("userId") Long userId,
                                             @RequestParam(value = "merchantUserId", required = false) Long merchantUserId) {
        log.debug("获取用户信息:{},{}", userId, merchantUserId);
        if (userId == null) {
            return ResponseUtils.error(Status.STATUS_PARAMETER_ERROR, "参数异常");
        }
        Long adOrgId = getAdOrgIdNotNull();
        UserInfoDTO userInfoDTO = null;
        if (merchantUserId != null) {
            userInfoDTO = ofNullable(userFeignClient.getUserInfoByUserIdAndMerchantUserId(userId, merchantUserId, adOrgId))
                    .filter(r -> GlobalErrorCode.OK.getCode().equals(r.getCode()))
                    .map(RespBody::getBody).orElseThrow(() -> new BusinessException(BusinessExceptionEnums.FOUND_USER_INFO_ERROR));
        } else {
            userInfoDTO = ofNullable(userFeignClient.getUserInfoByUserIdAndMerchantId(userId, adOrgId))
                    .filter(r -> GlobalErrorCode.OK.getCode().equals(r.getCode()))
                    .map(RespBody::getBody).orElseThrow(() -> new BusinessException(BusinessExceptionEnums.FOUND_USER_INFO_ERROR));
        }
        UserDTO userDTO = CommonConverterTools.convert(UserDTO.class, userInfoDTO);

        List<com.lyy.user.app.interfaces.facade.dto.UserDTO> userList = Optional.ofNullable(iUserRpc.findByIds(Arrays.asList(userId)).getBody()).orElse(new ArrayList<>());
        if(CollUtil.isNotEmpty(userList)){
             com.lyy.user.app.interfaces.facade.dto.UserDTO userInfo = userList.get(0);
            // 获取最新的用户头像昵称
            if(Objects.nonNull(userDTO)){
                if(StringUtils.isEmpty(userDTO.getHeadImg())){
                    userDTO.setHeadImg(userInfo.getHeadImg());
                }
                if(StringUtils.isEmpty(userDTO.getName())){
                    userDTO.setName(userInfo.getName());
                }
                if(StringUtils.isEmpty(userDTO.getGender()) || "未知".equals(userDTO.getGender())){
                    userDTO.setGender(userInfo.getGender());
                }
            }
        }
        //获取省市区中文名称
        if (userInfoDTO.getProvinceId() != null) {
            StringBuilder address = new StringBuilder();
            //获取省份
            List<Map<String, Object>> dataList = equipmentGroupBusinessService.getAllDistrict();
            List<Map<String, Object>> cityList = getAreaInfo(dataList, address, userInfoDTO.getProvinceId(), true);
            List<Map<String, Object>> regionList = getAreaInfo(cityList, address, userInfoDTO.getLyyCityId(), true);
            getAreaInfo(regionList, address, userInfoDTO.getRegionId(), false);
            if (StringUtils.isNotBlank(address.toString())) {
                userDTO.setProvinceCity(address.toString());
            }
        }
        return ResponseUtils.success(userDTO);
    }

    /**
     * 区域信息获取
     *
     * @param list    区域list
     * @param address 拼接的地址信息
     * @param id      id
     * @param flag    标识
     * @return
     */
    private List<Map<String, Object>> getAreaInfo(List<Map<String, Object>> list, StringBuilder address, Long id, boolean flag) {
        List<Map<String, Object>> resultList = null;
        if (id != null) {
            if (list != null && list.size() > 0) {
                for (Map<String, Object> map : list) {
                    if (map.containsKey("value") && id.toString().equals(map.get("value"))) {
                        address.append(map.get("text"));
                        if (flag && map.containsKey("children")) {
                            resultList = (List<Map<String, Object>>) map.get("children");
                        }
                    }
                }
            }
        }
        return resultList;
    }

    /**
     * 注销C端用户
     *
     * @param userId         用户id
     * @param merchantUserId 商户用户id
     * @return
     */
    @GetMapping("/delUser")
    @AuthorityResource(name = "注销C端用户", value = "mb_new_delUser", seq = 4, role = "Saas_Merchant", parentValue = "MemberCenter")
    public BaseResponse<Boolean> delUser(@RequestParam("userId") Long userId, @RequestParam("merchantUserId") Long merchantUserId) {
        log.debug("注销用户");
        if (merchantUserId == null) {
            return ResponseUtils.error(Status.STATUS_PARAMETER_ERROR, "参数异常");
        }
        boolean flag = ofNullable(userFeignClient.deleteUserInfo(userId, merchantUserId, getAdOrgIdNotNull(), getAdUserIdNotNull()))
                .filter(r -> GlobalErrorCode.OK.getCode().equals(r.getCode()))
                .map(RespBody::getBody).orElse(false);
        return ResponseUtils.success(flag);
    }

    /**
     * 获取用户账户信息
     *
     * @return
     */
    @GetMapping("/getUserAccountInfo")
    @AuthorityResource(name = "获取用户账户信息", value = "mb_new_getUserAccountInfo", seq = 5, role = "Saas_Merchant", parentValue = "MemberCenter")
    public BaseResponse<Map<String, Object>> getUserAccountInfo(@RequestParam("userId") Long userId,
                                                                @RequestParam(value = "merchantUserId",required = false) Long merchantUserId) {
        Map<String, Object> map = new HashMap<>(3);

        //获取设备信息
        AtomicReference<BigDecimal> allCoins = new AtomicReference<>(BigDecimal.ZERO);
        AtomicReference<BigDecimal> allBalance = new AtomicReference<>(BigDecimal.ZERO);
        AtomicReference<BigDecimal> allCoupon = new AtomicReference<>(BigDecimal.ZERO);
        //会员卡
        AtomicReference<BigDecimal> memberCardCount = new AtomicReference<>(BigDecimal.ZERO);
        //IC卡
        AtomicReference<BigDecimal> icCardCount = new AtomicReference<>(BigDecimal.ZERO);
        AtomicReference<BigDecimal> historyBalance = new AtomicReference<>(BigDecimal.ZERO);
        AtomicReference<Boolean> hasHistoryBalance = new AtomicReference<>(false);

        Long merchantId = getAdOrgIdNotNull();
        //用户账户信息
        AccountConditionDTO conditionDTO = new AccountConditionDTO();
        conditionDTO.setUserId(userId);
        conditionDTO.setMerchantId(merchantId);
        conditionDTO.setMerchantUserId(merchantUserId);
        conditionDTO.setExcludeExpired(true);
        RespBody<List<AccountDTO>> respBody = accountFeignClient.accountInfo(conditionDTO);
        if (!GlobalErrorCode.OK.getCode().equals(respBody.getCode())) {
            throw new BusinessException("账户信息查询失败");
        }
        List<AccountDTO> accountDTOList = respBody.getBody();
        if (accountDTOList != null && accountDTOList.size() > 0) {
            //遍历账户数据进行汇总
            accountDTOList.forEach(accountDTO -> {
                if (accountDTO.getBalance() != null) {
                    if (COINS_CLASSIFIES.contains(accountDTO.getClassify())) {
                        allCoins.getAndSet(allCoins.get().add(accountDTO.getBalance()));
                    } else if (BALANCE_CLASSIFIES.contains(accountDTO.getClassify())) {
                        allBalance.getAndSet(allBalance.get().add(accountDTO.getBalance()));
                    } else if (COUPON_CLASSIFIES.contains(accountDTO.getClassify())) {
                        allCoupon.getAndSet(allCoupon.get().add(accountDTO.getBalance()));
                    } else if (BenefitClassifyEnum.MEMBER_CARD.getCode().equals(accountDTO.getClassify())) {
                        // TODO: 2021-06-16 会员卡数量 待完善
                        //memberCardCount.getAndSet(memberCardCount.get().add(accountDTO))
                    } else if (BenefitClassifyEnum.HISTORY_BALANCE.getCode().equals(accountDTO.getClassify())) {
                        hasHistoryBalance.set(true);
                        historyBalance.getAndSet(historyBalance.get().add(accountDTO.getBalance()));
                    }
                    //ic卡数量
                }
            });
        }
        List<String> factories = listFactory(merchantId);
        if (!CollectionUtils.isEmpty(factories)) {
            ThirdAccountQuery thirdAccountQuery = new ThirdAccountQuery()
                    .setMerchantUserId(merchantUserId)
                    .setUserId(userId)
                    .setMerchantId(merchantId)
                    .setFactoryIds(factories);
            BigDecimal thirdBalance = getThirdBalance(thirdAccountQuery);
            hasHistoryBalance.set(true);
            historyBalance.getAndSet(historyBalance.get().add(thirdBalance));
        }
        map.put("historyBalance", hasHistoryBalance.get() ? historyBalance.get() : null);
        map.put("allCoins", allCoins.get());
        map.put("allBalance", allBalance.get());
        //券数量要加上平台券的数量
        JsonObject userCouponJson = lyyCouponService.getUserCoupon(userId);
        if(userCouponJson!=null && userCouponJson.getData()!=null){
            Gson gson = new GsonBuilder()
                    .setDateFormat("yyyy-MM-dd HH:mm:ss")
                    .create();
            String js = gson.toJson(userCouponJson.getData());
            UserAllCouponDTO userAllCouponDTO = gson.fromJson(js,UserAllCouponDTO.class);
            if(userAllCouponDTO != null){
                log.debug("平台券可用数量：{}",userAllCouponDTO.getLyyCouponCanUsed().size());
                allCoupon.getAndSet(allCoupon.get().add(BigDecimal.valueOf(userAllCouponDTO.getLyyCouponCanUsed().size())));
            }
        }
        map.put("allCoupon", allCoupon.get().setScale(0, BigDecimal.ROUND_UP));
        //会员卡
        cn.lyy.lyy_cmember_service_api.response.BaseResponse<Integer> countMemberCards = userCardService.countMemberUserByDistributor(
                merchantId.intValue(), userId);
        if (countMemberCards != null && countMemberCards.getCode().equals(ResponseEnum.SUCCESS.getCode())) {
            Integer data = countMemberCards.getData();
            log.debug(">>>>>>>> 是该商户下的会员卡数量: [{}] <<<<<<<<", data);
            memberCardCount.getAndSet(memberCardCount.get().add(BigDecimal.valueOf(data)));
        }
        map.put("memberCardCount", memberCardCount.get().setScale(0, BigDecimal.ROUND_UP));

        IcCardQueryForm form = new IcCardQueryForm();
        form.setMerchantIds(Collections.singletonList(merchantId.intValue()));
        //传在线卡或离线卡类型不影响IC卡数量统计
        form.setIcCardType(1);
        form.setUserId(userId);
        UserIcCardsDTO userIcCardsDTO = lyyIcService.findIcCards(form);
        map.put("icCardCount", userIcCardsDTO==null?0:userIcCardsDTO.getOfflineCnt()+userIcCardsDTO.getOnlineCnt());

        //用户账户信息
        UserStatisticsConditionDTO param = new UserStatisticsConditionDTO();
        param.setUserId(userId);
        param.setMerchantId(merchantId);
        param.setMerchantUserId(merchantUserId);
        RespBody<UserStatisticsRecordDTO> resp = statisticsFeignClient.find(param);
        if (!GlobalErrorCode.OK.getCode().equals(resp.getCode())) {
            throw new BusinessException(resp.getMessage());
        }
        // 避免空指针
        UserStatisticsRecordDTO responseDTO = ofNullable(resp.getBody()).orElse(new UserStatisticsRecordDTO());
        map.put("allConsume", ofNullable(responseDTO.getPayAmount()).orElse(BigDecimal.ZERO)
                .add(ofNullable(responseDTO.getPayForServiceAmount()).orElse(BigDecimal.ZERO)));
        return ResponseUtils.success(map);
    }

    /**
     * 商家给用户派送福利
     *
     * @param payoutWelfareDTO
     * @return
     */
    @PostMapping(value = "/payout/welfare")
    @AuthorityResource(name = "派发福利", value = "mb_new_payoutWelfare", seq = 6, role = "Saas_Merchant", parentValue = "MemberCenter")
    public BaseResponse payoutWelfare(@RequestBody @Validated PayoutWelfareDTO payoutWelfareDTO) {
        log.info("派发福利请求参数：param:{}", JSON.toJSONString(payoutWelfareDTO));
        userMemberService.payoutWelfare(payoutWelfareDTO,this.getAdOrgIdNotNull());
        return ResponseUtils.success();
    }


    /**
     * 分页获取商户的会员信息
     * @param current
     * @param size
     * @return
     */
    @GetMapping("/member/findPageMemberUser")
    @AuthorityResource(name = "分页获取商户的会员信息", value = "mb_findPageMemberUser", seq = 2, role = "Saas_Merchant", parentValue = "MemberCenter")
    public BaseResponse<Page<MemberUserPageDTO>> findPageMemberUser(@RequestParam("current") Integer current, @RequestParam("size") Integer size,
                                                                    @RequestParam("userId") Long userId){
        RespBody<Page<MemberUserPageDTO>> respBody = memberFeignClient.findPageMemberUser(current,size,this.getAdOrgIdNotNull(), userId);
        log.debug("分页获取商户的会员信息 -->{}",respBody);
        if(GlobalErrorCode.OK.getCode().equals(respBody.getCode())){
            return ResponseUtils.success(respBody.getBody());
        }
        return ResponseUtils.error(ResponseCodeEnum.FAIL.getCode(),"分页获取商户的会员信息失败");

    }

    /**
     * 获取商户的统计信息
     * @return
     */
    @GetMapping("/statistics/getMerchantStatistics")
    @AuthorityResource(name = "获取商户的统计信息", value = "mb_getMerchantStatistics", seq = 2, role = "Saas_Merchant", parentValue = "MemberCenter")
    public BaseResponse<MerchantStatisticsRecordDTO> getMerchantStatistics(){
        log.info("getMerchantStatistics");
        RespBody<MerchantStatisticsRecordDTO> respBody  = statisticsFeignClient.getMerchantStatistics(this.getAdOrgIdNotNull());
        MerchantStatisticsRecordDTO merchantStatisticsRecordDTO =  ofNullable(respBody).filter( r -> GlobalErrorCode.OK.getCode().equals(r.getCode())).map(RespBody::getBody).orElse(null);
        return ResponseUtils.success(merchantStatisticsRecordDTO);
    }


    /**
     * 查询统计用户列表
     * @param statisticsUserQueryDTO
     * @return
     */
    @PostMapping("/statistics/queryStatisticsUserList")
    @AuthorityResource(name = "查询统计用户列表", value = "mb_queryStatisticsUserList", seq = 2, role = "Saas_Merchant", parentValue = "MemberCenter")
    public BaseResponse<Page<MerchantUserStatisticsListDTO>> queryStatisticsUserList(@RequestBody StatisticsUserQueryDTO statisticsUserQueryDTO) {
        statisticsUserQueryDTO.setMerchantId(this.getAdOrgIdNotNull());
        statisticsUserQueryDTO.setCountSql(false);
        RespBody<Page<MerchantUserStatisticsListDTO>> pageInfo = statisticsFeignClient.queryStatisticsUserList(statisticsUserQueryDTO);
        Page<MerchantUserStatisticsListDTO> merchantUserStatisticsList=  ofNullable(pageInfo).filter(r -> GlobalErrorCode.OK.getCode().equals(r.getCode())).map(RespBody::getBody).orElseThrow(()->new BusinessException(pageInfo.getMessage()));
        return ResponseUtils.success(merchantUserStatisticsList);
    }

    /**
     * 查询用户统计详情
     * @param userStatisticsConditionDTO
     * @return
     */
    @PostMapping("/statistics/findUser")
    @AuthorityResource(name = "查询用户统计详情", value = "mb_findStatisticsUser", seq = 2, role = "Saas_Merchant", parentValue = "MemberCenter")
    public BaseResponse<UserStatisticsRecordDTO> findStatisticsUser(@RequestBody UserStatisticsConditionDTO userStatisticsConditionDTO) {
        userStatisticsConditionDTO.setMerchantId(this.getAdOrgIdNotNull());
        RespBody<UserStatisticsRecordDTO> resp = statisticsFeignClient.find(userStatisticsConditionDTO);
        UserStatisticsRecordDTO userStatisticsRecordDTO =  ofNullable(resp).filter(r -> GlobalErrorCode.OK.getCode().equals(r.getCode())).map(RespBody::getBody).orElseThrow(()->new BusinessException(resp.getMessage()));
        return ResponseUtils.success(userStatisticsRecordDTO);
    }

    @PostMapping("/balance/adjust")
    @AuthorityResource(name = "商家调整用户余额余币", value = "mb_adjustUserBenefit", seq = 2, role = "Saas_Merchant", parentValue = "MemberCenter")
    public BaseResponse<Void> adjustUserBenefit(@RequestBody UserBalanceAdjustReqDTO userBalanceAdjustReqDTO) {
        log.info("调整余额余币,param:{}", userBalanceAdjustReqDTO);
        userMemberService.adjustUserBenefit(userBalanceAdjustReqDTO, getAdOrgIdNotNull(), getAdUserIdNotNull());
        return ResponseUtils.success();
    }

    @ApiOperation(value = "查询历史余额")
    @GetMapping("/balance/external")
    @AuthorityResource(name = "查询历史余额", value = "mb_externalBalance", seq = 2, role = "Saas_Merchant", parentValue = "MemberCenter")
    public BaseResponse<ExternalBalanceVO> externalBalance(ExternalBalanceQuery query) {
        Long merchantId = getAdOrgIdNotNull();
        BigDecimal thirdBalance = BigDecimal.ZERO;
        List<String> factories = listFactory(merchantId);
        if (!CollectionUtils.isEmpty(factories)) {
            ThirdAccountQuery thirdAccountQuery = new ThirdAccountQuery()
                    .setUserId(query.getUserId())
                    .setMerchantId(merchantId)
                    .setFactoryIds(factories);
            thirdBalance = getThirdBalance(thirdAccountQuery);
        }
        return ResponseUtils.success(new ExternalBalanceVO(thirdBalance));
    }

    private List<String> listFactory(Long merchantId) {
        cn.lyy.life.api.commonlife.response.BaseResponse<List<Long>> response = terraceDataMappingApi.getTerraceFactoryIdsByMerchantId(
                null, merchantId);
        return ofNullable(response)
                .map(e -> e.getData())
                .orElse(Lists.newArrayList())
                .stream()
                .map(Object::toString)
                .collect(Collectors.toList());
    }

    public BigDecimal getThirdBalance(ThirdAccountQuery query) {
        BigDecimal thirdBalance = thirdBenefitService.listThirdAccount(query)
                .stream()
                .map(ThirdBenefitAccount::getBalance)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        log.debug("thirdBalance: {}", thirdBalance);
        return thirdBalance;
    }


}
