package cn.lyy.merchant.controller.member;

import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.bigdata.api.dto.BigDataAccountConsumption;
import cn.lyy.bigdata.api.dto.BigDataAccountRecordQueryDTO;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.service.member.MemberConsumptionService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.user.account.infrastructure.account.dto.AccountConsumption;
import com.lyy.user.account.infrastructure.account.dto.AccountRecordQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.ConsumptionTradeType;
import com.lyy.user.account.infrastructure.constant.AccountRecordTypeGroupEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * @description:
 * @author: qgw
 * @date on 2021-06-03.
 * @Version: 1.0
 */
@Slf4j
@RestController
@RequestMapping("/rest/member/consumption")
public class MemberConsumptionController extends BaseController {

    @Autowired
    private MemberConsumptionService memberConsumptionService;

    @PostMapping("/list")
    @AuthorityResource(name = "查询消费记录", value = "mb_member_listConsumptionRecord", seq = 14, role = "Saas_Merchant", parentValue = "MemberCenter")
    public BaseResponse<Page<AccountConsumption>> listConsumptionRecord(@Valid @RequestBody AccountRecordQueryDTO param) {

        try {
            param.setMerchantId(getAdOrgIdNotNull());
            log.debug("查询消费记录:{}", param);
            return ResponseUtils.success(memberConsumptionService.listConsumptionRecord(param));
        } catch (Exception e) {
            log.error("查询消费记录失败", e);
            return ResponseUtils.error(ResponseCodeEnum.FAIL.getCode(),"查询消费记录失败");
        }
    }
    @GetMapping("/tradeTypeList")
    @AuthorityResource(name = "查询交易类型", value = "mb_member_tradeTypeList", seq = 15, role = "Saas_Merchant", parentValue = "MemberCenter")
    public BaseResponse<List<ConsumptionTradeType>> tradeTypeList() {
        return ResponseUtils.success(AccountRecordTypeGroupEnum.list());
    }

    @PostMapping("/v2/list")
    public BaseResponse<Page<BigDataAccountConsumption>> listConsumptionRecordV2(@Valid @RequestBody BigDataAccountRecordQueryDTO param) {

        try {
            param.setMerchantId(getAdOrgIdNotNull());
            log.info("查询消费记录v2:{}", param);
            return ResponseUtils.success(memberConsumptionService.listConsumptionRecordV2(param));
        } catch (Exception e) {
            log.error("查询消费记录失败", e);
            return ResponseUtils.error(ResponseCodeEnum.FAIL.getCode(),"查询消费记录失败");
        }
    }

}
