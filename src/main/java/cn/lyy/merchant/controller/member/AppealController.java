package cn.lyy.merchant.controller.member;

import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.dto.Pagination;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.merchant.api.service.MerchantMemberAppealService;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.member.AppealHandleParam;
import cn.lyy.merchant.service.MemberService;
import cn.lyy.merchant.dto.member.AppealRecordDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 类描述：申诉相关接口
 * <p>
 *
 * <AUTHOR>
 * @since 2021/2/1 15:55
 */
@RestController
@RequestMapping("/appeal")
@Slf4j
public class AppealController extends BaseController {

    @Autowired
    private MemberService memberService;

    @Resource
    private MerchantMemberAppealService merchantMemberAppealService;

    /**
     * 处理申诉
     * @param param
     * @return
     */
    @PostMapping("/handle")
    @AuthorityResource(name = "处理申诉", value = "mb_appealHandle", seq = 1, role = "Saas_Merchant", parentValue = "MemberCenter")
    public BaseResponse handle(@RequestBody @Valid AppealHandleParam param, BindingResult result) {
        if (log.isDebugEnabled()) {
            log.debug("申诉退款执行，param: {}", param);
        }
        checkParameter(result);
        param.setDistributorId(getAdOrgIdNotNull());
        param.setAdUserId(getAdUserIdNotNull());
        memberService.handleAppeal(param);
        return ResponseUtils.success();
    }

    @GetMapping(value = "/list")
    @AuthorityResource(name = "B端申诉列表", value = "mb_appeal_list", role = "Saas_Merchant", parentValue = "MemberCenter")
    public BaseResponse<Pagination<AppealRecordDTO>> appealList(@RequestParam("status") Integer status,
                                                 @RequestParam("pageIndex") Integer pageIndex,
                                                 @RequestParam("pageSize") Integer pageSize){
        log.info("申诉列表，参数，status:{}",status);
        Pagination<AppealRecordDTO> list = memberService.appealList(status, this.getCurrentUser().getAdUserId(), this.getCurrentUser().getAdOrgId(), pageIndex, pageSize);
        return ResponseUtils.success(list);
    }

    @GetMapping(value = "/detail")
    @AuthorityResource(name = "B端申诉详情", value = "mb_appeal_detail", role = "Saas_Merchant", parentValue = "MemberCenter")
    public  BaseResponse<AppealRecordDTO> appealDetail(@RequestParam("appealRecordId") Long appealRecordId){
        log.info("申诉详情，参数，appealRecordId:{}",appealRecordId);
        AppealRecordDTO record = memberService.appealDetail(appealRecordId);
        return ResponseUtils.success(record);
    }
}
