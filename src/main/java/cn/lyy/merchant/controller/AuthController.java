package cn.lyy.merchant.controller;

import cn.lyy.auth.request.AuthTokenRequest;
import cn.lyy.authority_service_api.AdUserDTO;
import cn.lyy.authority_service_api.dto.LogoutDTO;
import cn.lyy.authority_service_api.miscroservice.AuthorityService;
import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.base.dto.JsonObject;
import cn.lyy.base.dto.Status;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.merchant.constants.SystemConstants;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.service.AuthService;
import cn.lyy.merchant.service.impl.UserRoleServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

import static java.util.Optional.ofNullable;

/**
 * <AUTHOR>
 * @date 11/20/20 2:34 PM
 * </p>
 */
@RestController
@RequestMapping("/rest/user")
@Slf4j
public class AuthController extends BaseController {

    private final static String AUTHORIZATION_BAR = "authorization-bar";

    @Autowired
    private AuthorityService authorityService;

    @Autowired
    private AuthService authService;

    @Autowired
    private UserRoleServiceImpl userRoleService;

    @PostMapping("/login")
    public BaseResponse<String> login(@RequestParam("username") String username,
                                      @RequestParam("password") String password) {
        log.debug("用户登录:username={}, password={}", username, password);
        if (StringUtils.isBlank(username) || StringUtils.isBlank(password)) {
            return ResponseUtils.error(ResponseCodeEnum.PARAMETER_ERROR.getCode(), "账号或者密码为空");
        }
        String ticket = userRoleService.login(username,password);
        if (StringUtils.isNotBlank(ticket)) {
            userRoleService.updateSystemUserAndRole(false,username);
            return ResponseUtils.success(ticket);
        }
        AdUserDTO userDTO = new AdUserDTO();
        userDTO.setUsername(username);
        JsonObject json = authorityService.loadUserByUsername(SystemConstants.MERCHANT_BACKEND_SYSTEM_ID, userDTO);
        //没有3.0账号
        if (json.getResult() != Status.STATUS_SUCCESS) {
            userRoleService.updateSystemUserAndRoleSync(false,username);
            ticket = userRoleService.login(username,password);
            if (StringUtils.isNotBlank(ticket)) {
                return ResponseUtils.success(ticket);
            }
            //return ResponseUtils.error(ResponseCodeEnum.BUSINESS_EXCEPTION.getCode(), "");
        }
        return ResponseUtils.error(ResponseCodeEnum.BUSINESS_EXCEPTION.getCode(), "用户名或者密码错误");
    }

    @PostMapping("/logout")
    public BaseResponse<Boolean> logout(HttpServletRequest request) {
        String ticket = request.getHeader(AUTHORIZATION_BAR);
        log.debug("系统推出的ticker值为:{}", ticket);
        LogoutDTO logout = new LogoutDTO();
        logout.setTicket(ticket);
        boolean result = ofNullable(authorityService.businessLogOut(logout)).filter(j -> 0 == j.getResult())
                .map(JsonObject::getData).map(d -> (boolean) d).orElse(false);
        return ResponseUtils.success(result);
    }

    /**
     * 生成授权token
     *
     * @return
     */
    @PostMapping("/generateToken")
    @AuthorityResource(name = "生成授权token", value = "generate_token", role = "Saas_Merchant")
    public JsonObject generateToken() {
        JsonObject json = new JsonObject();
        AuthTokenRequest authTokenRequest = new AuthTokenRequest();
        authTokenRequest.setDistributorId(getCurrentUser().getAdOrgId());
        String token = authService.generateToken(authTokenRequest);
        json.setData(token);
        return json;
    }
}
