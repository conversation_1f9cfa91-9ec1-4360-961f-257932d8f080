package cn.lyy.merchant.controller.popup;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.CustomPopupRecordRequest;
import cn.lyy.merchant.dto.popup.MultiPopCheckDTO;
import cn.lyy.merchant.dto.popup.MultiPopRequest;
import cn.lyy.merchant.dto.popup.PopReceivedDTO;
import cn.lyy.merchant.dto.popup.PopResult;
import cn.lyy.merchant.redis.MerchantRedisKeyEnum;
import cn.lyy.merchant.service.popup.CustomPopupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.Arrays;
import java.util.List;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @date: 2022/12/28
 * @author: YL
 */
@Api(tags = "自定义弹窗")
@Slf4j
@RestController
@RequestMapping("/rest/popup")
public class CustomPopupController extends BaseController {

    /**
     * 支持单次最多128万账号弹窗
     */
    private static final long MULTI_BUCKET = 255;
    
    private static final String MULTI_PREFIX = MerchantRedisKeyEnum.MERCHANT_USER_CUSTOM_TEMPORARY_POPUP_RECORD.getKey() + "multi:";

    @Autowired
    private CustomPopupService customPopupService;
    
    @Value("${custom.popup.studyUrl}")
    private String studyUrl;
    
    @GetMapping("/study/url")
    public BaseResponse<String> studyUrl() {
        return success(studyUrl);
    }
    
    @GetMapping("/isPop")
    public BaseResponse<Boolean> isPop(@RequestParam Integer type) {

        boolean isNeedPop = customPopupService.checkIsNeedPop(type, getAdUserIdNotNull(), getAdOrgIdNotNull());
        
        return success(isNeedPop);
    }
    
    @PostMapping("/received/record")
    public BaseResponse<Void> receivedRecord(@RequestBody @Valid CustomPopupRecordRequest request) {
        Long adUserId = getAdUserIdNotNull();

        customPopupService.received(request.getType(), adUserId);
        
        return success();
    }
    
    @DeleteMapping("/record/clear")
    public BaseResponse<Void> clearRecord() {
        Long adUserId = getAdUserIdNotNull();

        customPopupService.clearRecord(adUserId);

        return success();
    }
    
    @ApiOperation("抖音活动弹窗")
    @PostMapping("/multi/dy/is-pop")
    public BaseResponse<PopResult> isPop(@RequestBody @Valid MultiPopRequest request) {
        MultiPopCheckDTO popCheckDTO = MultiPopCheckDTO.builder()
                .bucket(MULTI_BUCKET)
                .merchantId(getAdOrgIdNotNull())
                .userId(getAdUserIdNotNull())
                .keyPrefix(MULTI_PREFIX)
                .checkEquipmentTypeValues(request.getCheckEquipmentTypeValues())
                .types(request.getTypes())
                .build();

        PopResult popResult = customPopupService.checkIsNeedPopMulti(popCheckDTO);
        return success(popResult);
    }

    @ApiOperation("弹窗确认")
    @PostMapping("/multi/receive")
    public BaseResponse<Void> multiReceived(@RequestBody @Valid CustomPopupRecordRequest request) {
        PopReceivedDTO recordDTO = PopReceivedDTO.builder()
                .bucket(MULTI_BUCKET)
                .keyPrefix(MULTI_PREFIX)
                .userId(getAdUserIdNotNull())
                .type(request.getType())
                .build();

        customPopupService.multiReceived(recordDTO);
        
        return success();
    }

    @ApiOperation("弹窗确认记录清除")
    @DeleteMapping("/multi/record")
    public BaseResponse<Void> clearMultiRecord() {
        PopReceivedDTO recordDTO = PopReceivedDTO.builder()
                .bucket(MULTI_BUCKET)
                .keyPrefix(MULTI_PREFIX)
                .userId(getAdUserIdNotNull())
                .build();
        
        customPopupService.clearMultiRecord(recordDTO);

        return success();
    }
    
    @ApiOperation("弹窗确认-测试")
    @PostMapping("/multi/receive/t")
    public BaseResponse<Void> multiReceivedRecordT(@RequestBody @Valid CustomPopupRecordRequest request) {
        log.warn("custom multi popup record t, recordValue:{}", request.getRecordValue());
        
        PopReceivedDTO recordDTO = PopReceivedDTO.builder()
                .bucket(MULTI_BUCKET)
                .keyPrefix(MULTI_PREFIX)
                .userId(request.getRecordValue())
                .type(request.getType())
                .build();

        customPopupService.multiReceived(recordDTO);

        return success();
    }
}
