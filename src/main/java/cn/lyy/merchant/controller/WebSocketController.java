package cn.lyy.merchant.controller;

import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.dto.Status;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.microservice.IWebsocketRegisterClient;
import cn.lyy.websocket.constant.ClientTypeEnum;
import cn.lyy.websocket.dto.ClientRegisterDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName: WebSocketController
 * @description: TODO
 * @author: pengkun
 * @create: 2020-11-12 08:55
 * @Version 1.0
 **/
@RestController
@RequestMapping("/websocket")
@Slf4j
public class WebSocketController extends BaseController {

    @Autowired
    private IWebsocketRegisterClient websocketRegisterClient;

    @GetMapping("/token")
    @AuthorityResource(name = "获取websocket", value = "mb_websocket_token", seq = 11, role = "Saas_Merchant", parentValue = "Equipment")
    public BaseResponse<String> getToken(){
        log.debug("获取websocket");
        BaseResponse<String> baseResponse = new BaseResponse<>();
        ClientRegisterDTO registerDTO = new ClientRegisterDTO();
        registerDTO.setClientType(ClientTypeEnum.MERCHANT_H5.getType());
        registerDTO.setUniqueId(getWebSocketToken());
        BaseResponse<String> tokenResult = websocketRegisterClient.generate(registerDTO);
        Integer code = tokenResult.getCode();
        if(code == 0){
            baseResponse.setMessage("获取token成功");
            baseResponse.setData(tokenResult.getData());
        }else {
            baseResponse.setCode(Status.STATUS_FAIL);
            baseResponse.setMessage("获取token失败");
        }
        return baseResponse;
    }
}
