package cn.lyy.merchant.controller.superstaff;

import static java.util.Optional.ofNullable;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.lyy_api.advert.EquipmentMediaAdsClient;
import cn.lyy.marketing.api.service.superstaff.SuperStaffRuleClient;
import cn.lyy.marketing.dto.constants.superstaff.SuperStaffRuleItemConstant;
import cn.lyy.marketing.dto.superstaff.SuperStaffRuleDTO;
import cn.lyy.marketing.dto.superstaff.SuperStaffRuleDeleteDTO;
import cn.lyy.marketing.dto.superstaff.SuperStaffRuleItemDTO;
import cn.lyy.marketing.dto.superstaff.SuperStaffRuleStoreRemoveDTO;
import cn.lyy.marketing.dto.superstaff.SuperStaffStoreBindRuleDTO;
import cn.lyy.marketing.dto.superstaff.SuperStaffVoiceResourceDTO;
import cn.lyy.marketing.dto.superstaff.SuperStaffVoiceResourceQuery;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.superstaff.SuperStaffRuleCreateRequest;
import cn.lyy.merchant.dto.superstaff.SuperStaffRuleUpdateRequest;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.service.superstaff.SuperStaffService;
import cn.lyy.merchant.utils.RemoteResponseUtils;
import cn.lyy_dto.advert.MediaAdsInfoDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.starter.common.resp.RespBody;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @date: 2023-10-24
 * @author: YUNLONG
 */
@Api(tags = "超级导购员-备用")
@Slf4j
@RestController
@RequestMapping("/rest/back/super-staff")
@Validated
public class SuperStaffBackController extends BaseController {

    @Autowired
    private SuperStaffRuleClient superStaffRuleClient;

    @Autowired
    private SuperStaffService superStaffService;

    @Autowired
    private EquipmentMediaAdsClient equipmentMediaAdsClient;

    @ApiOperation("新建规则")
    @PostMapping("/rule")
    public BaseResponse<Long> createRule(@RequestBody @Valid SuperStaffRuleCreateRequest request, @RequestParam Long merchantId) {

        AdUserInfoDTO currentUser = getCurrentUser();

        Long ruleId = superStaffService.createRule(request, merchantId, currentUser.getAdUserId());

        return ResponseUtils.success(ruleId);
    }

    @ApiOperation("规则编辑")
    @PostMapping("/rule/edit")
    public BaseResponse<Void> editRule(@RequestBody @Valid SuperStaffRuleUpdateRequest request, @RequestParam Long merchantId) {

        AdUserInfoDTO currentUser = getCurrentUser();

        superStaffService.editRule(request, merchantId, currentUser.getAdUserId());

        return ResponseUtils.success();
    }

    @ApiOperation("删除规则")
    @DeleteMapping("/rule/disable")
    public BaseResponse<Void> deleteRule(@RequestParam("ruleId") @NotNull(message = "规则未指定") Long ruleId,
            @RequestParam Long merchantId) {

        AdUserInfoDTO currentUser = getCurrentUser();

        SuperStaffRuleDeleteDTO dto = new SuperStaffRuleDeleteDTO();
        dto.setMerchantId(merchantId);
        dto.setRuleId(ruleId);
        dto.setOperatorId(currentUser.getAdUserId());

        superStaffService.delete(dto);

        return ResponseUtils.success();
    }

    @ApiOperation("单个场地绑定")
    @PostMapping("/rule/bind-store")
    public BaseResponse<Void> ruleBindStore(@RequestBody @Valid SuperStaffStoreBindRuleDTO dto, @RequestParam Long merchantId) {

        AdUserInfoDTO currentUser = getCurrentUser();

        dto.setMerchantId(merchantId);
        dto.setOperatorId(currentUser.getAdUserId());

        return superStaffRuleClient.bind(dto);
    }

    @ApiOperation("场地解绑")
    @PostMapping("/rule/unbind-store")
    public BaseResponse<Void> unbind(@RequestBody @Valid SuperStaffRuleStoreRemoveDTO dto, @RequestParam Long merchantId) {
        AdUserInfoDTO currentUser = getCurrentUser();

        dto.setMerchantId(merchantId);
        dto.setOperatorId(currentUser.getAdUserId());

        superStaffRuleClient.unbind(dto);

        return ResponseUtils.success();
    }

    @ApiOperation("获取规则详情")
    @GetMapping("/rule/detail")
    public BaseResponse<SuperStaffRuleDTO> ruleDetail(@RequestParam @NotNull(message = "未指定规则") Long ruleId,
            @RequestParam Long merchantId) {
        BaseResponse<SuperStaffRuleDTO> response = superStaffRuleClient.ruleDetail(ruleId, merchantId);
        if (!RemoteResponseUtils.checkResponse(response)) {
            return ResponseUtils.error(ResponseCodeEnum.FAIL.getCode(), "规则信息获取失败");
        }

        SuperStaffRuleDTO ruleDTO = RemoteResponseUtils.getData(response);

        fillRuleItemVoiceUrl(ruleDTO);

        return ResponseUtils.success(ruleDTO);
    }

    private void fillRuleItemVoiceUrl(SuperStaffRuleDTO dto) {
        if (Objects.isNull(dto) || CollUtil.isEmpty(dto.getItems())) {
            return;
        }

        Map<Long, String> contentMap = dto.getItems().stream()
                .filter(o -> CharSequenceUtil.isNotBlank(o.getContent()))
                .collect(Collectors.toMap(SuperStaffRuleItemDTO::getId, this::transferContentBreak, (v1, v2) -> v1));

        List<String> contents = new ArrayList<>(contentMap.values());

        SuperStaffVoiceResourceQuery query = new SuperStaffVoiceResourceQuery();
        query.setContents(contents);
        BaseResponse<List<SuperStaffVoiceResourceDTO>> response = superStaffRuleClient.findResourceByContent(query);
        Map<String, SuperStaffVoiceResourceDTO> voiceUrlMap = ofNullable(RemoteResponseUtils.getData(response))
                .map(o -> o.stream().collect(Collectors.toMap(SuperStaffVoiceResourceDTO::getContent, Function.identity(), (v1, v2) -> v1)))
                .orElse(Collections.emptyMap());

        if (CollUtil.isEmpty(voiceUrlMap)) {
            return;
        }

        dto.getItems().forEach(o -> ofNullable(contentMap.get(o.getId()))
                .map(voiceUrlMap::get)
                .map(SuperStaffVoiceResourceDTO::getResourceUrl)
                .ifPresent(o::setVoiceResourceUrl));
    }

    private String transferContentBreak(SuperStaffRuleItemDTO dto) {
        try {
            JSONObject jsonObject = JSON.parseObject(dto.getData());
            String breakContent = jsonObject.getString(SuperStaffRuleItemConstant.TRANSFER_CONTENT);
            if (CharSequenceUtil.isNotBlank(breakContent)) {
                return breakContent;
            }
        } catch (Exception e) {
            log.warn("超级导购员｜解析语音停顿 数据格式非json data:{}", dto.getData(), e);
        }

        return dto.getContent();
    }

    @ApiOperation("获取规则广告信息")
    @GetMapping("/rule/detail/ads-info")
    public BaseResponse<MediaAdsInfoDTO> ruleAdsInfo(@RequestParam @NotNull(message = "未指定广告") Long adsId) {
        RespBody<MediaAdsInfoDTO> response = equipmentMediaAdsClient.findAdByIdDirectly(adsId);

        MediaAdsInfoDTO adsInfo = ofNullable(response.getCode())
                .filter(GlobalErrorCode.OK.getCode()::equals)
                .map(o -> RemoteResponseUtils.getData(response))
                .orElseThrow(() -> new BusinessException("广告信息获取失败"));

        return ResponseUtils.success(adsInfo);
    }
}
