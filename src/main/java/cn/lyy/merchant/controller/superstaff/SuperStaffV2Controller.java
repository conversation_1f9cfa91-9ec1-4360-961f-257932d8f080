package cn.lyy.merchant.controller.superstaff;

import cn.hutool.core.collection.CollUtil;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.lyy_cmember_service_api.dto.aidbj.launch.AiDbjServiceAdRecommendResponseDTO;
import cn.lyy.marketing.api.service.superstaff.SuperStaffV2Client;
import cn.lyy.marketing.dto.superstaff.v2.SuperStaffRuleV2DTO;
import cn.lyy.marketing.dto.superstaff.v2.SuperStaffRuleV2Query;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.superstaff.SuperStaffStoreQuery;
import cn.lyy.merchant.dto.superstaff.v2.StoreWithEquipmentListDTO;
import cn.lyy.merchant.dto.superstaff.v2.SuperStaffRuleCreatedV2Command;
import cn.lyy.merchant.dto.superstaff.v2.SuperStaffRuleUpdateV2Command;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import cn.lyy.merchant.service.superstaff.SuperStaffV2Service;
import cn.lyy.merchant.utils.RemoteResponseUtils;
import com.lyy.user.app.infrastructure.resp.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.Objects;

import static java.util.Optional.ofNullable;

/**
 * @date: 2023-11-16
 * @author: YUNLONG
 */
@Api(tags = "超级导购员v2")
@Slf4j
@RestController
@RequestMapping("/rest/super-staff/v2")
@Validated
public class SuperStaffV2Controller extends BaseController {

    @Autowired
    private SuperStaffV2Client superStaffV2Client;

    @Autowired
    private SuperStaffV2Service superStaffV2Service;

    @ApiOperation("设备场地列表")
    @PostMapping("/equipment-store-list")
    public BaseResponse<PageInfo<StoreWithEquipmentListDTO>> equipmentStoreList(@RequestBody SuperStaffStoreQuery query) {

        AdUserInfoDTO currentUser = getCurrentUser();
        query.setMerchantId(currentUser.getAdOrgId());
        query.setAdUserId(currentUser.getAdUserId());
        query.setNeedFilterStoreInternal(!ofNullable(currentUser.getIsApprover()).orElse(Boolean.TRUE));

        return ResponseUtils.success(superStaffV2Service.equipmentStoreListV2(query));
    }

    @ApiOperation("规则详情")
    @GetMapping("/rule/detail")
    public BaseResponse<SuperStaffRuleV2DTO> ruleDetail(@RequestParam @NotNull(message = "未指定规则") Long ruleId) {

        return ResponseUtils.success(superStaffV2Service.detail(ruleId, getCurrentUser().getAdOrgId()));
    }

    @ApiOperation("默认规则")
    @GetMapping("/rule/default")
    public BaseResponse<SuperStaffRuleV2DTO> ruleDefault() {

        BaseResponse<SuperStaffRuleV2DTO> response = superStaffV2Client.findDefaultRule();
        if (!RemoteResponseUtils.checkResponse(response)) {
            return ResponseUtils.error(ResponseCodeEnum.FAIL.getCode(), "默认规则获取失败");
        }

        return ResponseUtils.success(response.getData());
    }

    @ApiOperation("新增规则")
    @PostMapping("/rule/create")
    public BaseResponse<Long> createRule(@RequestBody @Valid SuperStaffRuleCreatedV2Command command) {
        AdUserInfoDTO currentUser = getCurrentUser();

        Long ruleId = superStaffV2Service.createRule(command, currentUser.getAdOrgId(), currentUser.getAdUserId());

        return ResponseUtils.success(ruleId);
    }

    @ApiOperation("编辑规则")
    @PostMapping("/rule/edit")
    public BaseResponse<Void> editRule(@RequestBody @Valid SuperStaffRuleUpdateV2Command command) {

        AdUserInfoDTO currentUser = getCurrentUser();

        superStaffV2Service.editRule(command, currentUser.getAdOrgId(), currentUser.getAdUserId());

        return ResponseUtils.success();
    }

    @ApiOperation("规则列表")
    @PostMapping("/rule/page")
    public BaseResponse<PageInfo<SuperStaffRuleV2DTO>> rulePage(@RequestBody SuperStaffRuleV2Query query) {
        AdUserInfoDTO currentUser = getCurrentUser();

        query.setMerchantId(currentUser.getAdOrgId());
        BaseResponse<com.github.pagehelper.PageInfo<SuperStaffRuleV2DTO>> response = superStaffV2Client.rulePage(query);
        if (!RemoteResponseUtils.checkResponse(response)) {
            return ResponseUtils.error(ResponseCodeEnum.FAIL.getCode(), "规则获取失败");
        }
        com.github.pagehelper.PageInfo<SuperStaffRuleV2DTO> pageData = response.getData();
        if (Objects.isNull(pageData)) {
            PageInfo<SuperStaffRuleV2DTO> page = PageInfo.<SuperStaffRuleV2DTO>builder()
                    .total(0L)
                    .current((long) query.getCurrent())
                    .size((long) query.getSize())
                    .records(Collections.emptyList())
                    .build();

            return ResponseUtils.success(page);
        }

        PageInfo<SuperStaffRuleV2DTO> page = PageInfo.<SuperStaffRuleV2DTO>builder()
                .records(pageData.getList())
                .total(pageData.getTotal())
                .current((long) pageData.getPageNum())
                .size((long) pageData.getPageSize())
                .build();

        ofNullable(page.getRecords())
                .filter(CollUtil::isNotEmpty)
                .ifPresent(o -> {
                    for (SuperStaffRuleV2DTO rule : o) {
                        if (Objects.equals(Boolean.TRUE, rule.getDefaultMatch())) {
                            rule.setBindEquipments(superStaffV2Service
                                    .totalUnboundCount(currentUser.getAdOrgId(), currentUser.getAdUserId()));
                            return;
                        }
                    }
                });

        return ResponseUtils.success(page);
    }

    @ApiOperation("推荐海报")
    @GetMapping("/get-ad-recommend-info")
    public BaseResponse<AiDbjServiceAdRecommendResponseDTO> getAdRecommendInfo(@RequestParam @NotNull(message = "未指定规则") Long ruleId) {
        return ResponseUtils.success(superStaffV2Service.getAdRecommendInfo(ruleId));
    }
}
