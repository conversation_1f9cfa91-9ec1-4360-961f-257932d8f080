package cn.lyy.merchant.controller.superstaff;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.marketing.api.service.superstaff.SuperStaffResourceTemplateClient;
import cn.lyy.marketing.dto.superstaff.SuperStaffResourceTemplateDTO;
import cn.lyy.marketing.dto.superstaff.SuperStaffResourceTemplateQuery;
import cn.lyy.marketing.dto.superstaff.SuperStaffResourceTemplateSaveDTO;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.superstaff.SuperStaffResourceTemplateCreateDTO;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @date: 2023-11-03
 * @author: YUNLONG
 */
@Api(tags = "超级导购员资源模板")
@Slf4j
@Validated
@RestController
@RequestMapping("/rest/super-staff/resource/template")
public class SuperStaffResourceTemplateController extends BaseController {

    @Autowired
    private SuperStaffResourceTemplateClient resourceTemplateClient;

    @ApiOperation("获取类型对应的资源列表")
    @PostMapping("/type-list")
    public BaseResponse<List<SuperStaffResourceTemplateDTO>> listTemplatesByType(@RequestBody SuperStaffResourceTemplateQuery query) {
        if (Objects.isNull(query.getType())) {
            return ResponseUtils.success(Collections.emptyList());
        }

        return resourceTemplateClient.listTemplates(query);
    }

    @ApiOperation("新增")
    @PostMapping("/create")
    public BaseResponse<Long> create(@RequestBody @Valid SuperStaffResourceTemplateCreateDTO dto) {
        AdUserInfoDTO currentUser = getCurrentUser();

        SuperStaffResourceTemplateSaveDTO saveDTO = new SuperStaffResourceTemplateSaveDTO();
        BeanUtils.copyProperties(dto, saveDTO);
        saveDTO.setOperatorId(currentUser.getAdUserId());

        return resourceTemplateClient.create(saveDTO);
    }

    @ApiOperation("编辑")
    @PostMapping("/edit")
    public BaseResponse<Void> edit(@RequestBody @Valid SuperStaffResourceTemplateSaveDTO dto) {
        AdUserInfoDTO currentUser = getCurrentUser();

        SuperStaffResourceTemplateSaveDTO saveDTO = new SuperStaffResourceTemplateSaveDTO();
        BeanUtils.copyProperties(dto, saveDTO);
        saveDTO.setOperatorId(currentUser.getAdUserId());

        return resourceTemplateClient.edit(dto);
    }

    @ApiOperation("开启使用")
    @GetMapping("/show")
    public BaseResponse<Void> show(@RequestParam @NotNull(message = "需指定id") Long id) {
        AdUserInfoDTO currentUser = getCurrentUser();

        SuperStaffResourceTemplateSaveDTO saveDTO = new SuperStaffResourceTemplateSaveDTO();
        saveDTO.setOperatorId(currentUser.getAdUserId());
        saveDTO.setShow(Boolean.TRUE);
        saveDTO.setId(id);

        return resourceTemplateClient.edit(saveDTO);
    }

    @ApiOperation("暂停使用")
    @GetMapping("/stop-show")
    public BaseResponse<Void> stopShow(@RequestParam @NotNull(message = "需指定id") Long id) {
        AdUserInfoDTO currentUser = getCurrentUser();

        SuperStaffResourceTemplateSaveDTO saveDTO = new SuperStaffResourceTemplateSaveDTO();
        saveDTO.setOperatorId(currentUser.getAdUserId());
        saveDTO.setShow(Boolean.FALSE);
        saveDTO.setId(id);

        return resourceTemplateClient.edit(saveDTO);
    }
}
