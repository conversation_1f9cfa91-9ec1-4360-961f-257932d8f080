package cn.lyy.merchant.controller.superstaff;

import static java.util.Optional.ofNullable;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.income.dto.utils.AddressTypeUtil;
import cn.lyy.lyy_api.advert.EquipmentMediaAdsClient;
import cn.lyy.marketing.api.service.superstaff.SuperStaffRuleClient;
import cn.lyy.marketing.dto.constants.superstaff.SuperStaffRuleItemConstant;
import cn.lyy.marketing.dto.superstaff.StoreSuperStaffRuleInfoDTO;
import cn.lyy.marketing.dto.superstaff.SuperStaffRuleBoundStoreCheckQuery;
import cn.lyy.marketing.dto.superstaff.SuperStaffRuleDTO;
import cn.lyy.marketing.dto.superstaff.SuperStaffRuleDefaultMarkDTO;
import cn.lyy.marketing.dto.superstaff.SuperStaffRuleDeleteDTO;
import cn.lyy.marketing.dto.superstaff.SuperStaffRuleItemDTO;
import cn.lyy.marketing.dto.superstaff.SuperStaffRulePageQuery;
import cn.lyy.marketing.dto.superstaff.SuperStaffRuleStoreCheckRemoveDTO;
import cn.lyy.marketing.dto.superstaff.SuperStaffRuleStorePageQuery;
import cn.lyy.marketing.dto.superstaff.SuperStaffRuleStoreRemoveDTO;
import cn.lyy.marketing.dto.superstaff.SuperStaffStoreBindRuleDTO;
import cn.lyy.marketing.dto.superstaff.SuperStaffStoreRuleListDTO;
import cn.lyy.marketing.dto.superstaff.SuperStaffVoiceResourceDTO;
import cn.lyy.marketing.dto.superstaff.SuperStaffVoiceResourceQuery;
import cn.lyy.merchant.api.service.MerchantGroupService;
import cn.lyy.merchant.constants.BusinessExceptionEnums;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.merchant.request.MerchantGroupRequest;
import cn.lyy.merchant.dto.merchant.response.MerchantGroupDTO;
import cn.lyy.merchant.dto.superstaff.StoreInfoDTO;
import cn.lyy.merchant.dto.superstaff.SuperStaffRuleBindStoreDTO;
import cn.lyy.merchant.dto.superstaff.SuperStaffRuleBoundStorePageQuery;
import cn.lyy.merchant.dto.superstaff.SuperStaffRuleCreateRequest;
import cn.lyy.merchant.dto.superstaff.SuperStaffRuleUpdateRequest;
import cn.lyy.merchant.dto.superstaff.SuperStaffStoreQuery;
import cn.lyy.merchant.dto.superstaff.SuperStaffStoreSelectQuery;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.service.superstaff.SuperStaffService;
import cn.lyy.merchant.utils.RemoteResponseUtils;
import cn.lyy_dto.advert.MediaAdsInfoDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.starter.common.resp.RespBody;
import com.lyy.user.app.infrastructure.resp.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @date: 2023-10-24
 * @author: YUNLONG
 */
@Api(tags = "超级导购员")
@Slf4j
@RestController
@RequestMapping("/rest/super-staff")
@Validated
public class SuperStaffController extends BaseController {

    @Autowired
    private SuperStaffRuleClient superStaffRuleClient;

    @Autowired
    private SuperStaffService superStaffService;

    @Autowired
    private MerchantGroupService merchantGroupService;

    @Autowired
    private EquipmentMediaAdsClient equipmentMediaAdsClient;

    @ApiOperation("新建规则")
    @PostMapping("/rule")
    public BaseResponse<Long> createRule(@RequestBody @Valid SuperStaffRuleCreateRequest request) {

        AdUserInfoDTO currentUser = getCurrentUser();

        Long ruleId = superStaffService.createRule(request, currentUser.getAdOrgId(), currentUser.getAdUserId());

        return ResponseUtils.success(ruleId);
    }

    @ApiOperation("规则编辑")
    @PostMapping("/rule/edit")
    public BaseResponse<Void> editRule(@RequestBody @Valid SuperStaffRuleUpdateRequest request) {

        AdUserInfoDTO currentUser = getCurrentUser();

        superStaffService.editRule(request, currentUser.getAdOrgId(), currentUser.getAdUserId());

        return ResponseUtils.success();
    }

    @ApiOperation("删除规则")
    @DeleteMapping("/rule/disable")
    public BaseResponse<Void> deleteRule(@RequestParam("ruleId") @NotNull(message = "规则未指定") Long ruleId) {

        AdUserInfoDTO currentUser = getCurrentUser();

        SuperStaffRuleDeleteDTO dto = new SuperStaffRuleDeleteDTO();
        dto.setMerchantId(currentUser.getAdOrgId());
        dto.setRuleId(ruleId);
        dto.setOperatorId(currentUser.getAdUserId());

        superStaffService.delete(dto);

        return ResponseUtils.success();
    }

    @ApiOperation("单个场地绑定")
    @PostMapping("/rule/bind-store")
    public BaseResponse<Void> ruleBindStore(@RequestBody @Valid SuperStaffStoreBindRuleDTO dto) {

        AdUserInfoDTO currentUser = getCurrentUser();

        storePrivilegeCheck(Collections.singletonList(dto.getStoreId()), currentUser.getAdUserId(), currentUser.getAdOrgId());

        dto.setMerchantId(currentUser.getAdOrgId());
        dto.setOperatorId(currentUser.getAdUserId());

        return superStaffRuleClient.bind(dto);
    }

    private void storePrivilegeCheck(List<Long> storeIds, Long adUserId, Long merchantId) {
        MerchantGroupRequest request = new MerchantGroupRequest();
        request.setGroups(storeIds);
        request.setAdUser(adUserId);
        request.setDistributor(merchantId);
        request.setIsActive(1);
        BaseResponse<List<MerchantGroupDTO>> response = merchantGroupService.selectGroup(request);
        if (!RemoteResponseUtils.checkResponse(response)) {
            throw new BusinessException("场地数据获取失败");
        }

        List<Long> findStoreIds = ofNullable(RemoteResponseUtils.getData(response))
                .filter(CollUtil::isNotEmpty)
                .map(o -> o.stream().map(MerchantGroupDTO::getEquipmentGroupId).distinct().collect(Collectors.toList()))
                .orElse(Collections.emptyList());

        if (storeIds.size() != findStoreIds.size()) {
            throw new BusinessException("场地无权限");
        }
    }

    @ApiOperation("场地解绑")
    @PostMapping("/rule/unbind-store")
    public BaseResponse<Void> unbind(@RequestBody @Valid SuperStaffRuleStoreRemoveDTO dto) {
        AdUserInfoDTO currentUser = getCurrentUser();

        dto.setMerchantId(currentUser.getAdOrgId());
        dto.setOperatorId(currentUser.getAdUserId());

        storePrivilegeCheck(Collections.singletonList(dto.getStoreId()), currentUser.getAdUserId(), currentUser.getAdOrgId());

        superStaffRuleClient.unbind(dto);

        return ResponseUtils.success();
    }

    @ApiOperation("规则列表")
    @PostMapping("/rule/page")
    public BaseResponse<PageInfo<SuperStaffRuleDTO>> rulePage(@RequestBody SuperStaffRulePageQuery query) {
        AdUserInfoDTO currentUser = getCurrentUser();
        query.setMerchantId(currentUser.getAdOrgId());

        BaseResponse<com.github.pagehelper.PageInfo<SuperStaffRuleDTO>> response = superStaffRuleClient.findRules(query);
        if (!RemoteResponseUtils.checkResponse(response)) {
            return ResponseUtils.error(ResponseCodeEnum.FAIL.getCode(), "规则获取失败");
        }

        com.github.pagehelper.PageInfo<SuperStaffRuleDTO> data = RemoteResponseUtils.getData(response);
        PageInfo<SuperStaffRuleDTO> page = PageInfo.<SuperStaffRuleDTO>builder()
                .current((long) data.getPageNum())
                .size((long) data.getPageSize())
                .total(data.getTotal())
                .records(data.getList())
                .build();

        correctDefaultRuleStoreCount(page.getRecords(), currentUser.getAdOrgId(), currentUser.getAdUserId());

        return ResponseUtils.success(page);
    }

    /**
     * 处理默认规则的适用场地数
     */
    private void correctDefaultRuleStoreCount(List<SuperStaffRuleDTO> rules, Long merchantId, Long adUserId) {
        if (CollUtil.isEmpty(rules)) {
            return;
        }

        rules.stream()
                .filter(o -> Objects.equals(o.getDefaultMatch(), Boolean.TRUE))
                .findFirst()
                .ifPresent(o -> {
                    int unboundCount = superStaffService.totalUnboundCount(merchantId, adUserId, null);

                    int boundStores = ofNullable(o.getBindStores()).orElse(0);

                    o.setBindStores(unboundCount + boundStores);
                });
    }

    @ApiOperation("获取规则详情")
    @GetMapping("/rule/detail")
    public BaseResponse<SuperStaffRuleDTO> ruleDetail(@RequestParam @NotNull(message = "未指定规则") Long ruleId) {

        AdUserInfoDTO currentUser = getCurrentUser();

        BaseResponse<SuperStaffRuleDTO> response = superStaffRuleClient.ruleDetail(ruleId, currentUser.getAdOrgId());
        if (!RemoteResponseUtils.checkResponse(response)) {
            return ResponseUtils.error(ResponseCodeEnum.FAIL.getCode(), "规则信息获取失败");
        }

        SuperStaffRuleDTO ruleDTO = RemoteResponseUtils.getData(response);

        fillRuleItemVoiceUrl(ruleDTO);

        return ResponseUtils.success(ruleDTO);
    }

    private void fillRuleItemVoiceUrl(SuperStaffRuleDTO dto) {
        if (Objects.isNull(dto) || CollUtil.isEmpty(dto.getItems())) {
            return;
        }

        Map<Long, String> contentMap = dto.getItems().stream()
                .filter(o -> CharSequenceUtil.isNotBlank(o.getContent()))
                .collect(Collectors.toMap(SuperStaffRuleItemDTO::getId, this::transferContentBreak, (v1, v2) -> v1));

        List<String> contents = new ArrayList<>(contentMap.values());

        SuperStaffVoiceResourceQuery query = new SuperStaffVoiceResourceQuery();
        query.setContents(contents);
        BaseResponse<List<SuperStaffVoiceResourceDTO>> response = superStaffRuleClient.findResourceByContent(query);
        Map<String, SuperStaffVoiceResourceDTO> voiceUrlMap = ofNullable(RemoteResponseUtils.getData(response))
                .map(o -> o.stream().collect(Collectors.toMap(SuperStaffVoiceResourceDTO::getContent, Function.identity(), (v1, v2) -> v1)))
                .orElse(Collections.emptyMap());

        if (CollUtil.isEmpty(voiceUrlMap)) {
            return;
        }

        dto.getItems().forEach(o -> ofNullable(contentMap.get(o.getId()))
                .map(voiceUrlMap::get)
                .map(SuperStaffVoiceResourceDTO::getResourceUrl)
                .ifPresent(o::setVoiceResourceUrl));
    }

    private String transferContentBreak(SuperStaffRuleItemDTO dto) {
        try {
            JSONObject jsonObject = JSON.parseObject(dto.getData());
            String breakContent = jsonObject.getString(SuperStaffRuleItemConstant.TRANSFER_CONTENT);
            if (CharSequenceUtil.isNotBlank(breakContent)) {
                return breakContent;
            }
        } catch (Exception e) {
            log.warn("超级导购员｜解析语音停顿 数据格式非json data:{}", dto.getData(), e);
        }

        return dto.getContent();
    }

    @ApiOperation("获取规则广告信息")
    @GetMapping("/rule/detail/ads-info")
    public BaseResponse<MediaAdsInfoDTO> ruleAdsInfo(@RequestParam @NotNull(message = "未指定广告") Long adsId) {
        RespBody<MediaAdsInfoDTO> response = equipmentMediaAdsClient.findAdByIdDirectly(adsId);

        MediaAdsInfoDTO adsInfo = ofNullable(response.getCode())
                .filter(GlobalErrorCode.OK.getCode()::equals)
                .map(o -> RemoteResponseUtils.getData(response))
                .orElseThrow(() -> new BusinessException("广告信息获取失败"));

        return ResponseUtils.success(adsInfo);
    }


    @ApiOperation("场地列表-携带规则信息")
    @PostMapping("/store/page-with-rule")
    public BaseResponse<PageInfo<StoreInfoDTO>> storeWithRulePage(@RequestBody SuperStaffStoreQuery query) {

        AdUserInfoDTO currentUser = getCurrentUser();

        MerchantGroupRequest request = new MerchantGroupRequest();
        request.setIsActive(1);
        request.setContext(query.getQuery());
        request.setDistributor(currentUser.getAdOrgId());
        request.setAdUser(currentUser.getAdUserId());
        request.setPageIndex(query.getCurrent());
        request.setPageSize(query.getSize());
        BaseResponse<com.github.pagehelper.PageInfo<MerchantGroupDTO>> response = merchantGroupService.pageGroupSimpleWithEquipmentCount(request);
        if (!RemoteResponseUtils.checkResponse(response)) {
            return ResponseUtils.error(ResponseCodeEnum.FAIL.getCode(), "场地信息获取失败，稍后重试");
        }

        com.github.pagehelper.PageInfo<MerchantGroupDTO> data = RemoteResponseUtils.getData(response);
        PageInfo<StoreInfoDTO> result = PageInfo.<StoreInfoDTO>builder()
                .current((long) data.getPageNum())
                .size((long) data.getPageSize())
                .total(data.getTotal())
                .records(Collections.emptyList())
                .build();
        if (CollUtil.isEmpty(data.getList())) {
            return ResponseUtils.success(result);
        }

        result.setRecords(convertGroup(data.getList(), true));

        fillRuleInfo(result.getRecords(), currentUser.getAdOrgId());

        return ResponseUtils.success(result);
    }

    private List<StoreInfoDTO> convertGroup(List<MerchantGroupDTO> stores, boolean convertAddressType) {
        if (CollUtil.isEmpty(stores)) {
            return Collections.emptyList();
        }

        return stores.stream().map(o -> {
                    StoreInfoDTO store = new StoreInfoDTO();
                    store.setStoreId(o.getEquipmentGroupId());
                    store.setName(o.getName());
                    store.setAddress(o.getAddress());
                    store.setProvinceName(o.getProvinceName());
                    store.setCityName(o.getCityName());
                    store.setDistrict(o.getDistrict());
                    store.setAddressType(o.getAddressType());
                    store.setEquipmentCount(o.getEquipmentCount());

                    if (convertAddressType) {
                        String addressTypeName = ofNullable(o.getAddressType())
                                .filter(CharSequenceUtil::isNotBlank)
                                .map(AddressTypeUtil::getAddressTypeContainAlipay)
                                .orElse("");
                        store.setAddressTypeName(addressTypeName);
                    }

                    return store;
                })
                .collect(Collectors.toList());
    }

    private void fillRuleInfo(List<StoreInfoDTO> stores, Long merchantId) {
        if (CollUtil.isEmpty(stores)) {
            return;
        }

        List<Long> storeIds = stores.stream()
                .map(StoreInfoDTO::getStoreId)
                .distinct()
                .collect(Collectors.toList());

        SuperStaffStoreRuleListDTO dto = new SuperStaffStoreRuleListDTO();
        dto.setMerchantId(merchantId);
        dto.setStoreIds(storeIds);
        BaseResponse<List<StoreSuperStaffRuleInfoDTO>> response = superStaffRuleClient.listStoreRules(dto);
        if (!RemoteResponseUtils.checkResponse(response)) {
            throw new BusinessException(BusinessExceptionEnums.SERVICE_ERROR, "规则获取失败");
        }

        Map<Long, StoreSuperStaffRuleInfoDTO> storeRuleMap = RemoteResponseUtils.getData(response).stream()
                .collect(Collectors.toMap(StoreSuperStaffRuleInfoDTO::getStoreId, Function.identity(), (k1, k2) -> k1));
        stores.forEach(o -> ofNullable(storeRuleMap.get(o.getStoreId()))
                .ifPresent(rule -> {
                    o.setSuperStaffRuleId(rule.getRuleId());
                    o.setSuperStaffRuleName(rule.getRuleName());
                })
        );
    }

    @ApiOperation("场地批量绑定")
    @PostMapping("/rule/bind-store-batch")
    public BaseResponse<Void> batchBindStore(@RequestBody @Valid SuperStaffRuleBindStoreDTO dto) {
        AdUserInfoDTO currentUser = getCurrentUser();

        superStaffService.batchStoreBound(dto, currentUser.getAdOrgId(), currentUser.getAdUserId());

        return ResponseUtils.success();
    }

    @ApiOperation("获取已绑定的场地列表")
    @GetMapping("/rule/page-store-bound")
    public BaseResponse<PageInfo<StoreInfoDTO>> boundStorePage(@RequestBody @Valid SuperStaffRuleBoundStorePageQuery query) {
        AdUserInfoDTO currentUser = getCurrentUser();

        SuperStaffRuleStorePageQuery pageQuery = new SuperStaffRuleStorePageQuery();
        pageQuery.setRuleId(query.getRuleId());
        pageQuery.setCurrent(query.getCurrent());
        pageQuery.setSize(query.getSize());
        BaseResponse<com.github.pagehelper.PageInfo<Long>> response = superStaffRuleClient.boundStoreIdPage(pageQuery);
        if (!RemoteResponseUtils.checkResponse(response)) {
            return ResponseUtils.error(ResponseCodeEnum.FAIL.getCode(), "场地获取失败");
        }

        com.github.pagehelper.PageInfo<Long> pageData = RemoteResponseUtils.getData(response);
        PageInfo<StoreInfoDTO> resultPage = PageInfo.<StoreInfoDTO>builder()
                .records(Collections.emptyList())
                .current((long) pageData.getPageNum())
                .size((long) pageData.getPageSize())
                .total(pageData.getTotal())
                .build();
        if (CollUtil.isEmpty(pageData.getList())) {
            return ResponseUtils.success(resultPage);
        }

        MerchantGroupRequest request = new MerchantGroupRequest();
        request.setIsActive(1);
        request.setDistributor(currentUser.getAdOrgId());
        request.setGroups(pageData.getList());
        BaseResponse<List<MerchantGroupDTO>> listBaseResponse = merchantGroupService.selectGroup(request);
        if (!RemoteResponseUtils.checkResponse(listBaseResponse)) {
            return ResponseUtils.error(ResponseCodeEnum.FAIL.getCode(), "场地信息获取失败，稍后重试");
        }

        resultPage.setRecords(convertGroup(RemoteResponseUtils.getData(listBaseResponse), false));

        handleInvalidStore(pageData.getList(), resultPage.getRecords(), currentUser.getAdOrgId(), query.getRuleId());

        return ResponseUtils.success(resultPage);
    }

    /**
     * 处理失效场地绑定移除
     */
    private void handleInvalidStore(List<Long> storeIds, List<StoreInfoDTO> validStores, Long merchantId, Long ruleId) {
        if (storeIds.size() == validStores.size()) {
            return;
        }

        List<Long> waitCheckStoreIds = new ArrayList<>(storeIds);
        if (CollUtil.isNotEmpty(validStores)) {
            List<Long> validStoreIds = validStores.stream()
                    .map(StoreInfoDTO::getStoreId)
                    .collect(Collectors.toList());

            waitCheckStoreIds.removeAll(validStoreIds);
        }

        SuperStaffRuleStoreCheckRemoveDTO dto = new SuperStaffRuleStoreCheckRemoveDTO();
        dto.setMerchantId(merchantId);
        dto.setStoreIds(waitCheckStoreIds);
        dto.setRuleId(ruleId);
        BaseResponse<Void> response = superStaffRuleClient.invalidStoreUnbindCheck(dto);
        log.info("超级导购员｜失效场地自检，response:{}", response);
    }

    @ApiOperation("获取可选择的场地列表")
    @PostMapping("/rule/store-select")
    public BaseResponse<PageInfo<StoreInfoDTO>> selectStores(@RequestBody @Valid SuperStaffStoreSelectQuery query) {
        AdUserInfoDTO currentUser = getCurrentUser();
        MerchantGroupRequest request = new MerchantGroupRequest();
        request.setIsActive(1);
        request.setPageIndex(query.getCurrent());
        request.setPageSize(query.getSize());
        request.setDistributor(currentUser.getAdOrgId());
        request.setAdUser(currentUser.getAdUserId());
        request.setContext(query.getQuery());
        BaseResponse<com.github.pagehelper.PageInfo<MerchantGroupDTO>> response = merchantGroupService.pageGroup(request);
        if (!RemoteResponseUtils.checkResponse(response)) {
            return ResponseUtils.error(ResponseCodeEnum.FAIL.getCode(), "场地信息获取失败");
        }
        com.github.pagehelper.PageInfo<MerchantGroupDTO> pageData = RemoteResponseUtils.getData(response);
        PageInfo<StoreInfoDTO> result = PageInfo.<StoreInfoDTO>builder()
                .records(convertGroup(pageData.getList(), false))
                .current((long) pageData.getPageNum())
                .size((long) pageData.getPageSize())
                .total(pageData.getTotal())
                .build();

        fillStoreBoundData(result.getRecords(), query.getRuleId());

        return ResponseUtils.success(result);
    }

    /**
     * 设置场地是否已绑定标识
     */
    private void fillStoreBoundData(List<StoreInfoDTO> stores, Long ruleId) {
        if (CollUtil.isEmpty(stores)) {
            return;
        }

        List<Long> storeIds = stores.stream()
                .map(StoreInfoDTO::getStoreId)
                .collect(Collectors.toList());

        SuperStaffRuleBoundStoreCheckQuery query = new SuperStaffRuleBoundStoreCheckQuery();
        query.setRuleId(ruleId);
        query.setStoreIds(storeIds);
        BaseResponse<List<Long>> response = superStaffRuleClient.findBoundStores(query);
        if (!RemoteResponseUtils.checkResponse(response)) {
            throw new BusinessException("场地绑定信息获取失败");
        }

        List<Long> boundStoreIds = ofNullable(RemoteResponseUtils.getData(response))
                .orElse(Collections.emptyList());
        stores.forEach(o -> o.setBound(boundStoreIds.contains(o.getStoreId())));
    }


    @ApiOperation("获取未绑定规则场地数量")
    @GetMapping("/store/unbind-count")
    public BaseResponse<Integer> unbindStoreCount(@RequestParam String query) {
        AdUserInfoDTO currentUser = getCurrentUser();

        return ResponseUtils.success(superStaffService.totalUnboundCount(currentUser.getAdOrgId(), currentUser.getAdUserId(), query));
    }

    @ApiOperation("设置默认规则")
    @GetMapping("/rule/default-mark")
    public BaseResponse<Void> defaultRuleMark(@RequestParam @NotNull(message = "未指定规则") Long ruleId) {
        AdUserInfoDTO currentUser = getCurrentUser();

        SuperStaffRuleDefaultMarkDTO dto = new SuperStaffRuleDefaultMarkDTO();
        dto.setMerchantId(currentUser.getAdOrgId());
        dto.setRuleId(ruleId);
        dto.setOperatorId(currentUser.getAdUserId());

        return superStaffRuleClient.defaultMark(dto);
    }
}
