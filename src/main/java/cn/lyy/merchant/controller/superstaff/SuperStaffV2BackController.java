package cn.lyy.merchant.controller.superstaff;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.marketing.api.service.superstaff.SuperStaffV2Client;
import cn.lyy.marketing.dto.superstaff.v2.SuperStaffEquipmentBindRuleDTO;
import cn.lyy.marketing.dto.superstaff.v2.SuperStaffEquipmentUnBindRuleDTO;
import cn.lyy.marketing.dto.superstaff.v2.SuperStaffRuleV2DTO;
import cn.lyy.marketing.dto.superstaff.v2.SuperStaffSystemDefaultMarkDTO;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.superstaff.v2.SuperStaffRuleCreatedV2Command;
import cn.lyy.merchant.dto.superstaff.v2.SuperStaffRuleUpdateV2Command;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import cn.lyy.merchant.service.superstaff.SuperStaffV2Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @date: 2023-11-16
 * @author: YUNLONG
 */
@Api(tags = "超级导购员v2-back")
@Slf4j
@RestController
@RequestMapping("/rest/super-staff/v2/back")
@Validated
public class SuperStaffV2BackController extends BaseController {

    @Autowired
    private SuperStaffV2Client superStaffV2Client;

    @Autowired
    private SuperStaffV2Service superStaffV2Service;

    @ApiOperation("规则详情")
    @GetMapping("/rule/detail")
    public BaseResponse<SuperStaffRuleV2DTO> ruleDetail(@RequestParam @NotNull(message = "未指定规则") Long ruleId,
            @RequestParam Long merchantId) {

        return ResponseUtils.success(superStaffV2Service.detail(ruleId, merchantId));
    }

    @ApiOperation("新增规则")
    @PostMapping("/rule/create")
    public BaseResponse<Long> createRule(@RequestBody @Valid SuperStaffRuleCreatedV2Command command,
            @RequestParam Long merchantId) {
        AdUserInfoDTO currentUser = getCurrentUser();

        Long ruleId = superStaffV2Service.createRule(command, merchantId, currentUser.getAdUserId());

        return ResponseUtils.success(ruleId);
    }

    @ApiOperation("编辑规则")
    @PostMapping("/rule/edit")
    public BaseResponse<Void> editRule(@RequestBody @Valid SuperStaffRuleUpdateV2Command command,
            @RequestParam Long merchantId) {

        AdUserInfoDTO currentUser = getCurrentUser();

        superStaffV2Service.editRule(command, merchantId, currentUser.getAdUserId());

        return ResponseUtils.success();
    }

    @PostMapping("/rule/bind")
    public BaseResponse<Void> equipmentBind(@RequestBody SuperStaffEquipmentBindRuleDTO dto,
            @RequestParam Long merchantId) {
        dto.setMerchantId(merchantId);
        dto.setOperatorId(getCurrentUser().getAdUserId());

        return superStaffV2Client.equipmentBind(dto);
    }

    @PostMapping("/rule/unbind")
    public BaseResponse<Void> equipmentUnbind(@RequestBody SuperStaffEquipmentUnBindRuleDTO dto,
            @RequestParam Long merchantId) {
        dto.setMerchantId(merchantId);
        dto.setOperatorId(getCurrentUser().getAdUserId());

        return superStaffV2Client.equipmentUnbind(dto);
    }

    @PostMapping("/rule/system-default-mark")
    public BaseResponse<Void> markSystemDefault(@RequestBody SuperStaffSystemDefaultMarkDTO dto) {
        dto.setOperatorId(getCurrentUser().getAdUserId());

        return superStaffV2Client.markSystemDefault(dto);
    }

    @GetMapping({"/rule/default-id"})
    public BaseResponse<Long> defaultRuleId() {

        return superStaffV2Client.defaultRuleId();
    }
}
