package cn.lyy.merchant.controller;

import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.merchant.api.service.MerchantTemplateService;
import cn.lyy.merchant.api.service.MerchantUserService;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.common.SMSSendResult;
import cn.lyy.merchant.dto.template.EquipmentTemplateListDTO;
import cn.lyy.merchant.dto.user.AdUserDTO;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.service.UserService;
import cn.lyy.merchant.service.remote.SmsService;
import cn.lyy.merchant.utils.CommonUtil;
import cn.lyy.merchant.utils.ValidatorUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.aliyun.oss.common.utils.LogUtils.getLog;

/**
 * <p>Title:saas2</p>
 * <p>Desc: 用户接口（绕过鉴权）</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020/11/17
 */
@Slf4j
@RestController
public class UserNonAuthController extends BaseController {

    @Autowired
    private MerchantUserService merchantUserService;
    @Autowired
    private SmsService smsService;
    @Autowired
    private UserService userService;
    @Autowired
    private UserController userController;

    static Pattern p = Pattern.compile("^[0-9A-Za-z]{6,20}$");

    @Value("${sms.sign:}")
    private String registerSmsSign;

    @Autowired
    private MerchantTemplateService templateService;

    @PostMapping("/rest/user/register")
    @AuthorityResource(name = "注册", value = "mb_register", seq = 2, role = "Saas_Merchant", parentValue = "MechantUser")
    public BaseResponse register(@RequestBody JSONObject param) throws BusinessException {
        AdUserDTO adUserDTO = JSON.toJavaObject(param, AdUserDTO.class);
        BaseResponse resultVO = validRegister(adUserDTO);
        if(Objects.nonNull(resultVO)) {
            return resultVO;
        }
        if(Objects.nonNull(adUserDTO.getFid())){
            String fid = (String) adUserDTO.getFid();
            try {
                //解密码fid
                adUserDTO.setFid(this.decryptFactoryId(fid));
            } catch (Exception e) {}
        }
        BaseResponse response =  userService.register(adUserDTO);
        return response.getCode() == ResponseCodeEnum.SUCCESS.getCode() ? success() : error(response.getCode(), response.getMessage());
    }

    @PostMapping("/rest/user/createTelCode")
    @AuthorityResource(name = "生成手机验证码", value = "mb_createTelCode", seq = 3, role = "Saas_Merchant", parentValue = "MechantUser")
    public BaseResponse createTelCode(@RequestBody JSONObject param) {
        AdUserDTO adUserDTO = JSON.toJavaObject(param, AdUserDTO.class);
        String phone = adUserDTO.getPhone();
        String type = adUserDTO.getType();
        Long userId = adUserDTO.getUserId();
        try {
            // 未登录不做校验
            if(! "WITHDRAW".equals(type) && !CommonUtil.isMobile(phone)) {
                return error(ResponseCodeEnum.FAIL.getCode(), "请输入正确的手机号！");
            }
            if("REGISTER".equals(type)) {
                BaseResponse response = merchantUserService.checkMobileExists(phone);
                if(response.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
                    return response;
                }
            }

            boolean hasSend = smsService.hasSendInSeconds(phone, 60);
            if (hasSend) {
                return error(ResponseCodeEnum.FAIL.getCode(), "验证码发送中，请稍后再试！");
            }
            // 生成手机验证码
            SMSSendResult smsSendResult = smsService.sendCode(phone, 0L , registerSmsSign);
            log.debug("phone->{},短信发送结果->{}", phone, smsSendResult);
            if(Objects.nonNull(smsSendResult) && smsSendResult.getCode() == 0){
                return success(60+"");
            }else{
                return error(ResponseCodeEnum.FAIL.getCode(), "短信发送失败！");
            }
        } catch(Exception e) {
            log.error(e.getMessage(), e);
            return error(ResponseCodeEnum.FAIL.getCode(), "短信发送失败，请联系客服！");
        }
    }

    @PostMapping("/rest/user/verifyPhoneCode")
    @AuthorityResource(name = "验证验证码", value = "mb_verifyPhoneCode", seq = 5, role = "Saas_Merchant", parentValue = "MechantUser")
    public BaseResponse verifyPhoneCode(@RequestBody JSONObject param) {
        AdUserDTO adUserDTO = JSON.toJavaObject(param, AdUserDTO.class);
        try {
            if (!CommonUtil.isMobile(adUserDTO.getPhone())) {
                return error(ResponseCodeEnum.FAIL.getCode(), "请输入一个正确的电话号码！");
            }
            if (!validParameter(adUserDTO.getVerifyPhoneCode())) {
                return error(ResponseCodeEnum.FAIL.getCode(), "请输入验证码！");
            }
            //获取链接参数
            boolean result = smsService.validateCode(adUserDTO.getPhone(), adUserDTO.getVerifyPhoneCode());
            if (!result) {
                return error(ResponseCodeEnum.FAIL.getCode(), "请输入正确的验证码！");
            } else {
                return success("验证成功！");
            }
        } catch (Exception e) {
            getLog().error(e.getMessage(), e);
            return error(ResponseCodeEnum.FAIL.getCode(), "验证失败，请联系客服！");
        }
    }

    /**
     * 忘记密码
     * @param param
     * @return
     */
    @PostMapping("/rest/user/forgetPassword")
    @AuthorityResource(name = "忘记密码", value = "mb_forgetPassword", seq = 7, role = "Saas_Merchant", parentValue = "MechantUser")
    public BaseResponse forgetPassword(@RequestBody JSONObject param) {
        return userController.modifyPassword(param);
    }

    public BaseResponse validRegister(AdUserDTO adUserDTO) {
        /**
         * 校验入参
         */
        String error = ValidatorUtils.validDTO(adUserDTO);
        if(StringUtils.isNotBlank(error)) {
            return error(ResponseCodeEnum.PARAMETER_ERROR.getCode(), error);
        }
        String newPassword = adUserDTO.getPassword();
        String repeatPassword = adUserDTO.getRepeatPassword();
        if (!validParameter(adUserDTO.getUserName())) {
            return error(ResponseCodeEnum.FAIL.getCode(), "请输入姓名！");
        }
        if (!validParameter(repeatPassword)) {
            return error(ResponseCodeEnum.FAIL.getCode(), "请输入确认密码！");
        }
        if (!newPassword.equals(repeatPassword)) {
            return error(ResponseCodeEnum.FAIL.getCode(), "两次输入的密码不一致！");
        }
        Matcher m = p.matcher(newPassword);
        if(!m.matches()){
            return error(ResponseCodeEnum.FAIL.getCode(), "密码需要6-20位的字符组合！");
        }
        return null;
    }

    @PostMapping("/rest/tmpl/getPage")
    @AuthorityResource(name = "模板页面查询", value = "mb_getPage", seq = 1, role = "Saas_Merchant", parentValue = "PageTmpl")
    public BaseResponse getPageTmpl(@RequestBody JSONObject param) {
        Long tmplId = param.getLong("tmplId");
        BaseResponse<EquipmentTemplateListDTO> response = templateService.templateDetail(tmplId);
        EquipmentTemplateListDTO data = response.getData();
        return success(data);
    }
}
