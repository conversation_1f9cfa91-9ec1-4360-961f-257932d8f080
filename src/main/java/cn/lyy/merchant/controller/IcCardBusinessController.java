package cn.lyy.merchant.controller;

import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.base.dto.Pagination;
import cn.lyy.base.dto.Status;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.equipment.constant.AccessConstant;
import cn.lyy.equipment.constant.EquipmentOperationConstant;
import cn.lyy.equipment.dto.operation.equipment.ICStatusOperationInfo;
import cn.lyy.equipment.service.IEquipmentService;
import cn.lyy.lyy_ic_service_api.LyyIcService;
import cn.lyy.lyy_ic_service_api.dto.*;
import cn.lyy.lyy_ic_service_api.enums.IcCardGroupRechargeSupportTypes;
import cn.lyy.lyy_ic_service_api.enums.IcCardStatusEnum;
import cn.lyy.lyy_ic_service_api.enums.IcCardTypes;
import cn.lyy.merchant.api.service.MerchantEquipmentService;
import cn.lyy.merchant.api.service.MerchantGroupService;
import cn.lyy.merchant.constants.BusinessChangeEnum;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.ChangeIcCardBalanceDTO;
import cn.lyy.merchant.dto.IcFillingPreferentialGroupListDTO;
import cn.lyy.merchant.dto.StatisticsItemDTO;
import cn.lyy.merchant.dto.merchant.request.MerchantGroupRequest;
import cn.lyy.merchant.dto.merchant.response.MerchantGroupDTO;
import cn.lyy.merchant.dto.response.IcCardConsumeDTO;
import cn.lyy.merchant.dto.response.PreferentialListDTO;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.service.IcBusinessService;
import cn.lyy.tools.constants.merchant.EquipmentTypeConstant;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.lyy.commodity.rpc.constants.CategoryEnum;
import com.lyy.commodity.rpc.dto.request.EquipmentUnbindDTO;
import com.lyy.commodity.rpc.feign.ICommodityRelatedService;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static java.util.Optional.ofNullable;

/**
 * @ClassName: IcCardBusinessController
 * @description: ic卡
 * @author: pengkun
 * @create: 2020-11-03 12:01
 * @Version 1.0
 **/
@RestController
@RequestMapping("/rest/ic")
@Slf4j
public class IcCardBusinessController extends BaseController {

    @Autowired
    private IcBusinessService icBusinessService;

    @Autowired
    private MerchantGroupService merchantGroupService;

    @Autowired
    private MerchantEquipmentService merchantEquipmentService;

    @Autowired
    private LyyIcService icService;

    @Autowired
    private IEquipmentService equipmentService;

    @Resource
    private ICommodityRelatedService commodityRelatedService;

    @Value("${customer.server.domain:https://m.leyaoyao.com}")
    private String customerDomain;

    @Value("${merchant_charging_type_id:1000078}")
    private Integer CHARGING_TYPE;

    /**
     * 统计卡信息
     * @param queryStr
     * @param type
     * @return
     */
    @GetMapping(value = "/find/total/count")
    public BaseResponse findTotalCount(@RequestParam(value = "queryStr",required = false) String queryStr,
                                       @RequestParam(value = "type",required = false) Integer type,
                                       @RequestParam(value = "status",required = false) Integer status) {
        BaseResponse baseResponse = new BaseResponse();
        try {
            Long adOrgId = getAdOrgIdNotNull();
            log.debug("findTotalCount,queryStr:{},type:{},status:{},adOrgId:{}",queryStr,type,status,adOrgId);
            IcCardTotalCountDTO totalCountDTO = icBusinessService.findTotalCount(adOrgId, queryStr,type,status);
            baseResponse.setData(totalCountDTO);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            baseResponse.setCode(Status.STATUS_FAIL);
            baseResponse.setMessage("查询IC卡交易记录-交易明细失败");
        }
        return baseResponse;
    }

    /**
     * 查询IC卡信息
     * @param cardNo
     * @return
     */
    @GetMapping(value = "/findByCardNo")
    @AuthorityResource(name = "查询IC卡信息", value = "mb_findByCardNo", role = "Saas_Merchant", parentValue = "Plugin-IcCard")
    public BaseResponse findByCardNo(@RequestParam("cardNo") String cardNo) {
        log.debug("Method[/rest/ic/findByCardNo],cardNo[{}]", cardNo);
        BaseResponse baseResponse = new BaseResponse();
        try {
            if (StringUtils.isBlank(cardNo)) {
                baseResponse.setCode(Status.STATUS_PARAMETER_ERROR);
                baseResponse.setMessage("IC卡号不能为空");
                return baseResponse;
            }
            LyyIcCardDetailDTO lyyIcCardDetailDTO = icBusinessService.findByCardNo(getAdOrgIdNotNull(), cardNo);
            baseResponse.setData(lyyIcCardDetailDTO);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            baseResponse.setCode(Status.STATUS_FAIL);
            baseResponse.setMessage("查询IC卡信息失败");
        }
        return baseResponse;
    }

    /**
     * IC卡号列表
     * @param queryStr
     * @param status
     * @param type
     * @param pageIndex
     * @param pageSize
     * @return
     */
    @GetMapping(value = "/list")
    @AuthorityResource(name = "IC卡查询列表", value = "mb_ic_list", role = "Saas_Merchant", parentValue = "Plugin-IcCard")
    public BaseResponse list(@RequestParam(value = "queryStr",required = false)String queryStr,
                           @RequestParam(value = "status",required = false)Integer status,
                           @RequestParam(value = "type",required = false) Integer type,
                           @RequestParam(value = "pageIndex",required = false) Integer pageIndex,
                           @RequestParam(value = "pageSize",required = false) Integer pageSize) {
        log.debug("IC卡号列表,queryStr:{},status:{},type:{},pageIndex:{},pageSize:{}",queryStr,status,type,pageIndex,pageSize);
        BaseResponse baseResponse = new BaseResponse();
        try {
            LyyIcListParamDTO lyyIcListParamDTO = new LyyIcListParamDTO();
            lyyIcListParamDTO.setMerchantId(getAdOrgIdNotNull());
            lyyIcListParamDTO.setQueryStr(queryStr);
            lyyIcListParamDTO.setStatus(status);
            lyyIcListParamDTO.setType(type);
            lyyIcListParamDTO.setPageIndex(pageIndex == null ? 1 : pageIndex);
            lyyIcListParamDTO.setPageSize(pageSize == null ? 20 : pageSize);
            Pagination<LyyIcCardDTO> pagination = icBusinessService.queryListForMerchant(lyyIcListParamDTO);

            baseResponse.setData(pagination);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            baseResponse.setCode(Status.STATUS_FAIL);
            baseResponse.setMessage("获取商家IC卡列表失败");
        }
        return baseResponse;
    }

    /**
     * IC卡消费详情
     * @param cardId
     * @param pageSize
     * @param pageIndex
     * @return
     */
    @GetMapping(value = "/listConsume")
    @AuthorityResource(name = "IC卡消费详情", value = "mb_ic_listConsume", seq = 1, role = "Saas_Merchant", parentValue = "Plugin-IcCard")
    public BaseResponse<Pagination<IcCardConsumeDTO>> listConsume(@RequestParam("cardId")Long cardId, @RequestParam("pageSize")Integer pageSize,
                                                                  @RequestParam("pageIndex")Integer pageIndex) {
        log.debug("IC卡消费详情,cardId:{},pageSize:{},pageIndex:{}", cardId,pageSize,pageIndex);
        BaseResponse<Pagination<IcCardConsumeDTO>> baseResponse = new BaseResponse();
        try {
            IcCardFlowQueryRequestDTO requestDTO = new IcCardFlowQueryRequestDTO();
            requestDTO.setLyyIcCardId(cardId);
            requestDTO.setMerchantId(getAdOrgIdNotNull());
            requestDTO.setPageIndex(ofNullable(pageIndex).orElse(1));
            requestDTO.setPageSize(ofNullable(pageSize).orElse(20));
            Pagination<IcCardConsumeDTO> page = icBusinessService.listConsume(requestDTO);
            baseResponse.setData(page);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            baseResponse.setMessage("查询IC卡消费详情失败");
            baseResponse.setCode(Status.STATUS_FAIL);
        }
        return baseResponse;
    }


    /**
     * IC卡保存
     * @param lyyIcCardDTO
     * @return
     */
    @PostMapping(value = "/saveCard")
    @AuthorityResource(name = "保存或修改IC卡信息",value = "mb_saveOrUpdateIcCard",role = "Saas_Merchant",parentValue = "Plugin-IcCard")
    public BaseResponse saveCard(@RequestBody LyyIcCardDTO lyyIcCardDTO) {
        log.debug("Method[/rest/ic/saveCard], Params[{}]", lyyIcCardDTO);
        BaseResponse baseResponse = new BaseResponse();
        if (!IcCardTypes.contains(lyyIcCardDTO.getType())) {
            baseResponse.setCode(Status.STATUS_PARAMETER_ERROR);
            baseResponse.setMessage("请选择一种卡类型");
            return baseResponse;
        }
        // 校验手机号码位数
        Optional.ofNullable(lyyIcCardDTO.getPhone()).filter( r -> r.toString().length() <= 11).orElseThrow(()-> new IllegalArgumentException("phone 参数不能超过11位"));

        lyyIcCardDTO.setMerchantId(getAdOrgIdNotNull());

        // 新增卡，判断卡号是否已存在
        if (null == lyyIcCardDTO.getLyyIcCardId()) {
            LyyIcCardDetailDTO hasDto = icBusinessService.findByCardNo(lyyIcCardDTO.getMerchantId(), lyyIcCardDTO.getCardNo());
            if (null != hasDto) {
                log.info("新增IC卡失败，IC卡号重复，merchantId={}, cardNo={}", lyyIcCardDTO.getMerchantId(), lyyIcCardDTO.getCardNo());
                baseResponse.setMessage("IC卡号重复，请确认后重新输入");
                baseResponse.setCode(Status.STATUS_FAIL);
                return baseResponse;
            }
            lyyIcCardDTO.setMemo("商户手动添加");
        } else {
            if (lyyIcCardDTO.getStatus() != null) {
                if (IcCardTypes.OFFLINE_CARD.getValue().equals(lyyIcCardDTO.getType())) {
                    if (!IcCardStatusEnum.NORMAL.getValue().equals(lyyIcCardDTO.getStatus())
                            && !IcCardStatusEnum.DISABLED.getValue().equals(lyyIcCardDTO.getStatus())) {
                        baseResponse.setCode(Status.STATUS_PARAMETER_ERROR);
                        baseResponse.setMessage("非法状态");
                        return baseResponse;
                    }
                } else {
                    if (!IcCardStatusEnum.contains(lyyIcCardDTO.getStatus())) {
                        baseResponse.setCode(Status.STATUS_PARAMETER_ERROR);
                        baseResponse.setMessage("非法状态");
                        return baseResponse;
                    }
                }
            }
            // 卡片类型不支持修改
            lyyIcCardDTO.setType(null);
        }

        Long id = icBusinessService.saveCard(lyyIcCardDTO);
        if (null == id) {
            baseResponse.setMessage("保存IC卡信息失败");
            baseResponse.setCode(Status.STATUS_FAIL);
        }
        if (id != null) {
            // 下发指令
            List<String> uniqueCodeList = merchantEquipmentService.selectMerchantEquipmentUniqueCode(lyyIcCardDTO.getMerchantId(),
                    EquipmentTypeConstant.SSJ.getCode()).getData();
            log.info("下发到主板信息 cardNo={},merchantId={},status={},uniqueCodeList={}", lyyIcCardDTO.getCardNo(), lyyIcCardDTO.getMerchantId(),
                    lyyIcCardDTO.getStatus(), uniqueCodeList);
            if (uniqueCodeList != null && !uniqueCodeList.isEmpty()) {
                ICStatusOperationInfo info = new ICStatusOperationInfo();
                info.setCardNo(lyyIcCardDTO.getCardNo());
                // 启用ic卡
                info.setOn(lyyIcCardDTO.getStatus() == null || IcCardStatusEnum.NORMAL.getValue().equals(lyyIcCardDTO.getStatus()));
                info.setUniqueCodes(uniqueCodeList);
                info.setUniqueCode(uniqueCodeList.get(0));
                info.setAccessType(AccessConstant.REVERSE.getCode());
                info.setEquipmentType(cn.lyy.equipment.constant.EquipmentTypeConstant.NONE.getCode());
                info.setOperationType(EquipmentOperationConstant.IC_STATUS.getCode());
                info.setDynamic(EquipmentOperationConstant.IC_STATUS.getCode());
                equipmentService.operation(info);
            }
        }
        return baseResponse;
    }

    @PostMapping(value = "/bindCard")
    @AuthorityResource(name = "绑定IC卡信息",value = "mb_bindIcCard",role = "Saas_Merchant",parentValue = "Plugin-IcCard")
    public BaseResponse bindCard(@RequestBody LyyIcCardDTO lyyIcCardDTO) {
        log.debug("Method[/rest/ic/bindCard], Params[{}]", JSON.toJSONString(lyyIcCardDTO));
        BaseResponse baseResponse = new BaseResponse();
        lyyIcCardDTO.setType(IcCardTypes.ONLINE_CARD.getValue());
        lyyIcCardDTO.setStatus(IcCardStatusEnum.NORMAL.getValue());
        Assert.notNull(lyyIcCardDTO.getMerchantId(),"商户ID不能为空");

        // 新增卡，判断卡号是否已存在
        if (null == lyyIcCardDTO.getLyyIcCardId()) {
            LyyIcCardDetailDTO hasDto = icBusinessService.findByCardNo(lyyIcCardDTO.getMerchantId(), lyyIcCardDTO.getCardNo());
            if (null != hasDto) {
                log.info("新增IC卡失败，IC卡号重复，merchantId={}, cardNo={}", lyyIcCardDTO.getMerchantId(), lyyIcCardDTO.getCardNo());
                baseResponse.setMessage("IC卡号重复，请确认后重新输入");
                baseResponse.setCode(Status.STATUS_FAIL);
                return baseResponse;
            }
            lyyIcCardDTO.setMemo("商户手动添加");
        }
        Long id = icBusinessService.saveCard(lyyIcCardDTO);
        if (null == id) {
            baseResponse.setMessage("保存IC卡信息失败");
            baseResponse.setCode(Status.STATUS_FAIL);
        }
        if (id != null) {
            // 下发指令
            List<String> uniqueCodeList = merchantEquipmentService.selectMerchantEquipmentUniqueCode(lyyIcCardDTO.getMerchantId(),
                    EquipmentTypeConstant.SSJ.getCode()).getData();
            log.info("下发到主板信息 cardNo={},merchantId={},status={},uniqueCodeList={}", lyyIcCardDTO.getCardNo(), lyyIcCardDTO.getMerchantId(),
                    lyyIcCardDTO.getStatus(), uniqueCodeList);
            if (uniqueCodeList != null && !uniqueCodeList.isEmpty()) {
                ICStatusOperationInfo info = new ICStatusOperationInfo();
                info.setCardNo(lyyIcCardDTO.getCardNo());
                // 启用ic卡
                info.setOn(lyyIcCardDTO.getStatus() == null || IcCardStatusEnum.NORMAL.getValue().equals(lyyIcCardDTO.getStatus()));
                info.setUniqueCodes(uniqueCodeList);
                info.setUniqueCode(uniqueCodeList.get(0));
                info.setAccessType(AccessConstant.REVERSE.getCode());
                info.setEquipmentType(cn.lyy.equipment.constant.EquipmentTypeConstant.NONE.getCode());
                info.setOperationType(EquipmentOperationConstant.IC_STATUS.getCode());
                info.setDynamic(EquipmentOperationConstant.IC_STATUS.getCode());
                equipmentService.operation(info);
            }
        }
        return baseResponse;
    }
    /**
     * 获取IC卡banner图
     * @return
     */
    @GetMapping(value = "/icBanner")
    public BaseResponse icBanner() {
        BaseResponse baseResponse = new BaseResponse();
        LyyIcConfigDTO result = icBusinessService.icBanner(getAdOrgIdNotNull());
        baseResponse.setData(result);
        return baseResponse;
    }

    /**
     * 更新IC卡banner图
     * @return
     */
    @PostMapping(value = "/setBanner")
    public BaseResponse setBanner(@RequestBody LyyIcConfigDTO lyyIcConfigDTO) {
        BaseResponse baseResponse = new BaseResponse();
        Integer result = icBusinessService.setBanner(lyyIcConfigDTO, getAdOrgIdNotNull());
        baseResponse.setData(result);
        return baseResponse;
    }

    /**
     * 获取商户场地列表，并标记是否支持IC卡充值
     *
     * @return
     */
    @PostMapping(value = "/getFillingPreferentialGroupList")
    public BaseResponse getFillingPreferentialGroupList() {
        BaseResponse baseResponse = new BaseResponse();
        Long adOrgId = getAdOrgIdNotNull();
        List<IcFillingPreferentialGroupListDTO> list = Lists.newArrayList();

        // 获取商户所有场地
        MerchantGroupRequest request = MerchantGroupRequest.builder().distributor(adOrgId).isActive(1).build();
        List<MerchantGroupDTO> groupDTOList = merchantGroupService.selectGroup(request).getData();

        // 获取商户支持IC卡充值的场地
        List<LyyIcFillingPreferentialGroupDto> fpGroups = icBusinessService.getFillingPreferentialGroups(adOrgId);
        Map<Integer, LyyIcFillingPreferentialGroupDto> icFillingPreferentialGroupIdMap = Maps.newHashMap();
        if (null != fpGroups && !fpGroups.isEmpty()) {
            icFillingPreferentialGroupIdMap = Maps.uniqueIndex(fpGroups, LyyIcFillingPreferentialGroupDto::getLyyEquipmentGroupId);
        }
        int supportCnt = 0;
        for (MerchantGroupDTO merchantGroupDTO : groupDTOList) {
            IcFillingPreferentialGroupListDTO dto = new IcFillingPreferentialGroupListDTO();
            BeanUtils.copyProperties(merchantGroupDTO, dto);
            LyyIcFillingPreferentialGroupDto fpGroup = icFillingPreferentialGroupIdMap.get(merchantGroupDTO.getEquipmentGroupId().intValue());
            if (fpGroup != null) {
                dto.setType(fpGroup.getType());
                supportCnt += fpGroup.supportRecharge() ? 1 : 0;
            }
            list.add(dto);
        }
        Map<String, Object> data = Maps.newHashMap();
        data.put("list", list);
        data.put("icGroupCount", supportCnt);
        baseResponse.setData(data);
        return baseResponse;
    }

    @PostMapping(value = "/resetFillingPreferentialGroup")
    public BaseResponse resetFillingPreferentialGroup(@RequestBody ResetFillingPreferentialGroupParamDto dto) {
        log.debug("Method[/rest/ic/resetFillingPreferentialGroup], Params[{}]", dto);
        BaseResponse baseResponse = new BaseResponse();
        if (!IcCardGroupRechargeSupportTypes.contains(dto.getType())) {
            baseResponse.setCode(Status.STATUS_PARAMETER_ERROR);
            baseResponse.setMessage("IC卡充值类型设置不支持[" + dto.getType() + "]");
            return baseResponse;
        }
        dto.setMerchantId(getAdOrgIdNotNull().intValue());
        baseResponse.setData(icService.updateFillingPreferentialGroup(dto));
        return baseResponse;
    }

    /**
     * 获取C端用户的IC卡绑定url
     *
     * @return
     * @throws UnsupportedEncodingException
     */
    @GetMapping(value = "/bindingUrl")
    public BaseResponse icCardBindingUrl() throws UnsupportedEncodingException {
        log.debug("Method[/rest/ic/bindUrl]");
        //TODO 绑定IC卡url修改，替换成小程序
        BaseResponse baseResponse = new BaseResponse();
        String loginUrl = customerDomain + "/customer/wxgroupauth/base";
        String bindingUrl = customerDomain + "/pages/lyy2.html#/?page=IcCardBinding&merchantId=" + getAdOrgIdNotNull();
        String finalUrl = loginUrl + "?equipmentId=0&returnurl=" + URLEncoder.encode(bindingUrl, StandardCharsets.UTF_8.name());
        Integer num = merchantEquipmentService.getGravityCount(getAdOrgIdNotNull()).getData();
        //商户是否有格子柜设备
        if(num != null && num > 0){
            loginUrl = customerDomain + "/customer/wxgroupauth/base?whiteDistributor=Y";
            bindingUrl =  customerDomain + "/pages/lyy2.html?#/?page=icCard&merchantId=" + getAdOrgIdNotNull();
            finalUrl = loginUrl + "&equipmentId=Gravity0&returnurl=" + URLEncoder.encode(bindingUrl, StandardCharsets.UTF_8.name());
        }
        baseResponse.setData(finalUrl);
        return baseResponse;
    }

    /**
     * IC卡统计
     *
     * @return JsonObject
     */
    @GetMapping(value = "/totalIcCardSize")
    public BaseResponse totalIcCardSize(@RequestParam("queryStr") String queryStr) {
        log.debug("Method[/rest/ic/totalIcCardStuSize]，queryStr:{}",queryStr);
        BaseResponse baseResponse = new BaseResponse();
        // queryStr 卡号、手机号、姓名综合搜索字段
        LyyIcCardTotalInfo info = icService.totalIcCardSize(getAdOrgIdNotNull().intValue(), queryStr);
        baseResponse.setData(info);
        return baseResponse;
    }

    /**
     * 获取水杯绑定url
     *
     * @return
     * @throws UnsupportedEncodingException
     */
    @GetMapping(value = "/getIcCardUserBindingUrl")
    public BaseResponse getIcCardUserBindingUrl() throws UnsupportedEncodingException {
        BaseResponse baseResponse = new BaseResponse();
        String loginUrl = customerDomain + "/customer/wxgroupauth/base?whiteDistributor=Y";
        String bindingUrl = customerDomain + "/pages/lyy2.html?#/?page=IcCardUserBinding&merchantId=" + getAdOrgIdNotNull();
        String finalUrl = loginUrl + "&equipmentId=0&returnurl=" + URLEncoder.encode(bindingUrl, StandardCharsets.UTF_8.name());
        baseResponse.setData(finalUrl);
        return baseResponse;
    }

    /**
     * 获取IC卡充值套餐列表
     *
     * @return
     */
    @GetMapping(value = "/fillingPreferentialList")
    @AuthorityResource(name = "获取IC卡充值套餐列表",value = "mb_fillingPreferentialList",role = "Saas_Merchant",parentValue = "Plugin-IcCard")
    public BaseResponse<List<PreferentialListDTO>> fillingPreferentialList() {
        List<PreferentialListDTO> list = icBusinessService.fillingPreferentialList(this.getAdOrgIdNotNull());
        return ResponseUtils.success(list);
    }

    /**
     * 添加IC卡充值套餐
     * @param requestBody
     * @return
     */
    @PostMapping(value = "/saveFillingPreferential")
    @AuthorityResource(name = "添加IC卡充值套餐",value = "mb_saveFillingPreferential",role = "Saas_Merchant",parentValue = "Plugin-IcCard")
    public BaseResponse saveFillingPreferential(@RequestBody JSONObject requestBody) {
        log.info("saveFillingPreferential.param:{}",requestBody);
        BigDecimal price = requestBody.getBigDecimal("price");
        BigDecimal value =  requestBody.getBigDecimal("value");
        Long id = requestBody.getLong("id");
        icBusinessService.saveFillingPreferential(id,price,value,this.getCurrentUser().getAdOrgId(),this.getCurrentUser().getAdUserId());
        return ResponseUtils.success();
    }


    /**
     * 删除IC充值套餐
     *
     * @param requestBody
     * @return
     */
    @PostMapping(value = "/deleteFillingPreferential")
    @AuthorityResource(name = "删除IC充值套餐",value = "mb_deleteFillingPreferential",role = "Saas_Merchant",parentValue = "Plugin-IcCard")
    public BaseResponse deleteFillingPreferential(@RequestBody JSONObject requestBody){
        Long detailId =  requestBody.getLong("lyyIcFillingPreferentialId");
        Assert.notNull(detailId,"lyyIcFillingPreferentialId参数不能为空");
        EquipmentUnbindDTO equipmentUnbindDTO = new EquipmentUnbindDTO();
        equipmentUnbindDTO.setDisplayId(detailId);
        equipmentUnbindDTO.setDistributorId(this.getAdOrgIdNotNull());
        equipmentUnbindDTO.setCategoryCode(CategoryEnum.IC_SET_MEAL.getCode());
        BaseResponse response = commodityRelatedService.unbindDistributorEquipment(equipmentUnbindDTO);
        Optional.ofNullable(response).filter( r -> r.getCode() == ResponseCodeEnum.SUCCESS.getCode()).orElseThrow(() -> new BusinessException(response.getMessage()));
        return ResponseUtils.success();
    }

    /**
     * 每日分析
     * @param date  查询日期，例如，每日：2020-11-05，每月: 2020-11
     * @param beforeNum 查询日期前多少天/月(不包含date当天日期)
     * @return
     */
    @GetMapping(value = "/statistics-day")
    @AuthorityResource(name = "IC卡每日分析", value = "mb_ic_statistics-day", seq = 1, role = "Saas_Merchant", parentValue = "Plugin-IcCard")
    public BaseResponse<List<StatisticsItemDTO>> statisticsIcCardEveryDay(@RequestParam("date") String date,@RequestParam("beforeNum") Integer beforeNum){
        BaseResponse<List<StatisticsItemDTO>> baseResponse = new BaseResponse<>();
        List<StatisticsItemDTO> list = icBusinessService.statisticsIcCardEveryDay(date,beforeNum,getAdOrgIdNotNull());
        baseResponse.setData(list);
        return baseResponse;
    }

    /**
     * 每月分析
     * @param date  查询日期，例如，每日：2020-11-05，每月: 2020-11
     * @param beforeNum 查询日期前多少天/月(不包含date当天日期)
     * @return
     */
    @GetMapping(value = "/statistics-month")
    @AuthorityResource(name = "IC卡每月分析", value = "mb_ic_statistics-month", seq = 2, role = "Saas_Merchant", parentValue = "Plugin-IcCard")
    public BaseResponse<List<StatisticsItemDTO>> statisticsIcCardEveryMonth(@RequestParam("date") String date,@RequestParam("beforeNum") Integer beforeNum){
        BaseResponse<List<StatisticsItemDTO>> baseResponse = new BaseResponse<>();
        List<StatisticsItemDTO> list = icBusinessService.statisticsIcCardEveryMonth(date,beforeNum,getAdOrgIdNotNull());
        baseResponse.setData(list);
        return baseResponse;
    }

    /**
     * 批量修改在线IC卡余额
     * @param changeIcCardBalanceDTO
     * @return
     */
    @PostMapping(value = "/change/balance")
    @AuthorityResource(name = "批量修改在线IC卡余额", value = "mb_changeBalance", role = "Saas_Merchant", parentValue = "Plugin-IcCard")
    public BaseResponse changeIcCardBalance(@RequestBody ChangeIcCardBalanceDTO changeIcCardBalanceDTO){
        log.info("changeIcCardBalance.param:{}",JSON.toJSONString(changeIcCardBalanceDTO));
        BaseResponse baseResponse = new BaseResponse();
        changeIcCardBalanceDTO.setAdOrgId(getAdOrgIdNotNull());
        changeIcCardBalanceDTO.setAdUserId(getAdUserIdNotNull());
        int successNum = icBusinessService.incrOnlineCardBalance(changeIcCardBalanceDTO, BusinessChangeEnum.TYPE_ONLINE_IC_AMOUNT , BusinessChangeEnum.USER_TYPE_PARTNER);
        baseResponse.setData(successNum);
        return baseResponse;
    }

    /**
     * 分页获取ic卡余额修改记录
     * @param pageSize
     * @param pageIndex
     * @param type
     * @param searchKey
     * @return
     */
    @GetMapping("/change/list")
    @AuthorityResource(name = "分页获取ic卡余额修改记录", value = "mb_changeList", role = "Saas_Merchant", parentValue = "Plugin-IcCard")
    public BaseResponse listChangeIcCardBalance(@RequestParam(value = "pageSize",required = false) Integer pageSize,
                                                @RequestParam(value = "pageIndex",required = false) Integer pageIndex,
                                                @RequestParam(value = "type",required = false)Integer type,
                                                @RequestParam(value = "searchKey",required = false)String searchKey) {
        BaseResponse baseResponse = new BaseResponse();
        Pagination<LyyIcCardFlowListDTO> page = icBusinessService.listChangeIcCardBalance(pageSize == null ? 20 : pageSize,pageIndex == null ? 1 :pageIndex,type,getAdOrgIdNotNull(),searchKey);
        baseResponse.setData(page);
        return baseResponse;
    }

}
