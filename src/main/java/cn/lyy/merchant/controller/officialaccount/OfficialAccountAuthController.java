package cn.lyy.merchant.controller.officialaccount;

import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.config.SystemConfigurer;
import cn.lyy.base.utils.redis.RedisKeys;
import cn.lyy.base.utils.wechat.TokenUtil;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.OfficialAccountDTO;
import cn.lyy.merchant.dto.request.OfficialAccountsRequest;
import cn.lyy.merchant.dto.response.AuthDTO;
import cn.lyy.merchant.dto.template.SaasTemplateMessageConfigDTO;
import cn.lyy.merchant.microservice.CustomerRedisClient;
import cn.lyy.merchant.service.officialaccount.OfficialAccountsService;
import cn.lyy.redis.constant.CustomerRedisKeyEnum;
import cn.lyy.redis.dto.request.RedisQueryRequestDTO;
import cn.lyy.redis.service.CustomerRedisService;
import cn.lyy.tools.util.wechat.WechatThirdUtil;
import cn.lyy.tools.util.wechat.response.AuthorizationInfo;
import cn.lyy.tools.util.wechat.response.FuncScope;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.net.URLEncoder;
import java.util.Objects;

/**
 * 类描述： 公众号授权接口
 * <p>
 *
 * <AUTHOR>
 * @since 2021/3/3 14:43
 */
@Slf4j
@Controller
@RequestMapping("/official/account/auth")
public class OfficialAccountAuthController extends BaseController {

    @Value("${project.distributor.domain:https://admin.leyaoyao.com}")
    private String authDomain;

    @Value("${project.merchant.domain:https://3b.leyaoyao.com}")
    private String currentDomain;

    @Autowired
    private OfficialAccountsService officialAccountsService;

    @Autowired
    private CustomerRedisClient customerRedisClient;

    @GetMapping("/url")
    @ResponseBody
    @AuthorityResource(name = "获取公众号绑定url", value = "mb_official_account_url", seq = 2, role = "Saas_Merchant", parentValue = "Plugin-MesTemplate")
    public BaseResponse getAuthUrl() throws Exception {
        AuthDTO authDTO = new AuthDTO();
        // 根据商家ID查询是否已绑定
        SaasTemplateMessageConfigDTO componentAuthorize = officialAccountsService.getTemMesConfig(getAdOrgIdNotNull());
        if (componentAuthorize != null) {
            authDTO.setAppId(componentAuthorize.getAppId());
            log.info("商家已绑定公众号，重新授权，商户id: {}", getAdOrgIdNotNull());
        }
        // 获取第三方component_access_token
        String ticket = queryTicket();
        log.debug("ticket={}", ticket);
        String thirdCacheToken = TokenUtil.getThirdCacheToken(SystemConfigurer.THIRD_CorpID, SystemConfigurer.THIRD_SECRET, ticket);

        // 获取第三方appId
        String componentAppId = SystemConfigurer.THIRD_CorpID;
        // 获取预授权码
        log.info("获取第三方access_token:{}获取第三方appId:{}", thirdCacheToken,componentAppId);
        String preAuthCode = WechatThirdUtil.getPreAuthCode(thirdCacheToken, componentAppId);
        // 回调rui
        String redirectUri = URLEncoder.encode(authDomain + "/lyy/rest/thirdAuth/third/merchant/callback?distributorId=" + getAdOrgIdNotNull(), "UTF-8");
        String authUrl = WechatThirdUtil.AUTH_URL_LINK + "&component_appid=" + componentAppId + "&pre_auth_code=" + preAuthCode + "&redirect_uri="
                + redirectUri + "&auth_type=1" + "#wechat_redirect";
        authDTO.setAuthUrl(authUrl);
        authDTO.setAppId(componentAppId);
        log.info("获取第三方公众号授权链接：{}", authUrl);
        return success(authDTO);
    }

    private String queryTicket() {
        RedisQueryRequestDTO queryRequest = new RedisQueryRequestDTO();
        queryRequest.setKey(CustomerRedisKeyEnum.EMPTY_KEY);
        queryRequest.setSpecialValue(RedisKeys.COMPONENTVERIFYTICKET);
        return customerRedisClient.get(queryRequest).getData();
    }


    @GetMapping("/redirectResponse")
    public String redirectResponse(@RequestParam(value = "authCode") String authCode,
                                         @RequestParam(value = "distributorId") Long distributorId) throws Exception {
        // 回调URL
        String redirectUrl = "redirect:" + currentDomain + "/pages/tencent/tencent?";
        // 设置返回信息给前端判断跳转(1成功)
        int msg = 1;
        // 获取参数
        if (StringUtils.isEmpty(authCode)) {
            String url = redirectUrl + "appId=" + null + "&authCode=" + authCode + "&msg=6";
            log.info("授权回调绑定参数错误，URL: {}", url);
            return url;
        }
        // 获取第三方token
        String ticket = queryTicket();
        String token = TokenUtil.getThirdCacheToken(SystemConfigurer.THIRD_CorpID, SystemConfigurer.THIRD_SECRET, ticket);
        // 获取第三方appId
        String componentAppId = SystemConfigurer.THIRD_CorpID;
        // 使用授权码换取公众号的接口调用凭据和授权信息
        log.info("获取授权信息参数，token: {}, componentAppId:{}, authCode:{}", token, componentAppId, authCode);
        AuthorizationInfo authorizationInfo = WechatThirdUtil.getQueryAuth(token, componentAppId, authCode);
        log.info("获取授权信息, {}", JSONObject.fromObject(authorizationInfo).toString());
        if (authorizationInfo == null) {
            // 0授权失败
            msg = 6;
            String url = redirectUrl + "appId=" + null + "&authCode=" + authCode + "&msg=" + msg;
            log.info("获取授权信息失败，URL: {}", url);
            return url;
        }
        // 获取公众号模版消息和自定义菜单的权限授予情况
        String isMenuAuth = "N";
        String isTemplateAuth = "N";
        for (FuncScope fs : authorizationInfo.getFunc_info()) {
            if (fs != null && fs.getFuncscope_category() != null) {
                Double id = ((Double) fs.getFuncscope_category().get("id"));
                if (id != null && id.intValue() == 7) {// 模板权限
                    isTemplateAuth = "Y";
                }
                if (id != null && id.intValue() == 15) {// 自定义菜单权限
                    isMenuAuth = "Y";
                }
            }
        }
        // 获取授权方appId
        String authorizerAppid = authorizationInfo.getAuthorizer_appid();
        // 查询是否有授权成功保存数据
        OfficialAccountDTO officialAccount = officialAccountsService.getOfficialAccoutByAppId(authorizerAppid);
        // 有可能授权完还没保存到数据库，隔1.5秒后再次查询
//        if (officialAccount == null) {
//            Thread.sleep(3000);
//            officialAccount = officialAccountsService.getOfficialAccoutByAppId(authorizerAppid);
//        }
        // 授权失败
        if (officialAccount == null) {
            // 0授权失败
            msg = 0;
            String url = redirectUrl + "appId=" + authorizerAppid + "&authCode=" + authCode + "&msg=" + msg;
            log.info("授权失败，未查到授权纪录，URL: {} ========", url);
            return url;
        }

        // 判断是否是服务号
        if (!Objects.equals("FWH", officialAccount.getType())) {
            // 2公众号不是服务号
            msg = 2;
            String url = redirectUrl + "appId=" + authorizerAppid + "&authCode=" + authCode + "&msg=" + msg;
            log.info("授权公众号不是服务号，Type:{}，URL: {} ========", officialAccount.getType(), url);
            return url;
        }
        // 判断是否认证
        if (officialAccount.getVerifyTypeInfo() == null || officialAccount.getVerifyTypeInfo() != 0) {
            // 3授权方认证类型不是微信认证
            msg = 3;
            String url = redirectUrl + "appId=" + authorizerAppid + "&authCode=" + authCode + "&msg=" + msg;
            log.info("不是微信认证公众号，授权方认证类型：{}，URL: {} ========", officialAccount.getVerifyTypeInfo(), url);
            return url;
        }
        SaasTemplateMessageConfigDTO saasTemplateMessageConfig = officialAccountsService.getTemMesConfig(authorizerAppid, distributorId);
        if (Objects.nonNull(saasTemplateMessageConfig)) {
            log.info("授权公众号已绑定商家");
            // 授权公众号已绑定商家
            officialAccountsService.saveTemMesConfig(authorizerAppid,distributorId, officialAccount.getOfficialAccout(), null);
            if (Objects.equals(saasTemplateMessageConfig.getLyyDistributorId(), distributorId)) {
                msg = 7;
            } else {
                msg = 4;
            }
            String url = redirectUrl + "appId=" + authorizerAppid + "&authCode=" + authCode + "&msg=" + msg + "&isMenuAuth=" + isMenuAuth + "&isTemplateAuth="
                    + isTemplateAuth;
            log.info("重新授权成功, 重定向URL:{}", url);
            return url;
        }
        //表示公众号为2.0的公众号
        officialAccount.setPlatformType(2);
        officialAccountsService.updatePlatformType(officialAccount.getLyyOfficialAccoutId(), officialAccount.getPlatformType());


        // 调用微服务添加关联纪录:0失败，成功返回ID
        Long result = officialAccountsService.saveTemMesConfig(authorizerAppid, distributorId, officialAccount.getOfficialAccout(), null);
        if (result == 0) {
            // 授权成功关联失败
            msg = 5;
            String url = redirectUrl + "appId=" + authorizerAppid + "&authCode=" + authCode + "&msg=" + msg;
            log.info("授权成功微服务添加关联失败，URL: {} ", url);
            return url;
        }
        if (result == -1) {
            // 商家已绑定公众号
            msg = 8;
            String url = redirectUrl + "appId=" + authorizerAppid + "&authCode=" + authCode + "&msg=" + msg;
            log.info("商家已绑定公众号，URL: {}", url);
            return url;
        }
        // 授权成功
        String url = redirectUrl + "appId=" + authorizerAppid + "&authCode=" + authCode + "&msg=" + msg + "&isMenuAuth=" + isMenuAuth + "&isTemplateAuth="
                + isTemplateAuth;

        log.info("授权成功，URL: {} ", url);

        return url;
    }
}
