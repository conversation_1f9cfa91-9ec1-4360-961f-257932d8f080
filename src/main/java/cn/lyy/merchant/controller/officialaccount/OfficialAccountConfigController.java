package cn.lyy.merchant.controller.officialaccount;

import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.merchant.api.service.AdOrgClient;
import cn.lyy.merchant.constants.ComponentAuthTemplateTypeEnum;
import cn.lyy.merchant.constants.RedisKey;
import cn.lyy.merchant.constants.WechatIndustryCodeEnum;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.OfficialAccountDTO;
import cn.lyy.merchant.dto.TemplateMes;
import cn.lyy.merchant.dto.TemplateMesConfig;
import cn.lyy.merchant.dto.merchant.AdOrgDTO;
import cn.lyy.merchant.dto.request.PublicAccountConfigForm;
import cn.lyy.merchant.dto.template.SaasTemplateMessageConfigDTO;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.service.officialaccount.OfficialAccountsService;
import cn.lyy.merchant.util.DateUtils;
import cn.lyy.merchant.util.MenuUtil;
import cn.lyy.merchant.util.TemplateUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.lyy.lock.LockInfo;
import com.lyy.lock.redis.RedisLock;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.io.UnsupportedEncodingException;
import java.util.*;

/**
 * 类描述：公众号配置相关接口，迁移自saas_merchant项目
 * <p>
 *
 * <AUTHOR>
 * @since 2021/3/3 17:23
 */
@Slf4j
@RestController
@RequestMapping("/official/account/config")
public class OfficialAccountConfigController extends BaseController {

    @Autowired
    private OfficialAccountsService officialAccountsService;

    @Autowired
    private AdOrgClient adOrgClient;

    @Autowired
    @Lazy
    private RedisLock redisLock;

    @Value("${wechat.micro.appid}")
    private String miniAppId;

    /**
     * 获取公众号的appid
     * @return
     */
    @GetMapping("/wechatThirdConfig/getAppIdWithType")
    @AuthorityResource(name = "获取公众号绑定url", value = "mb_official_account_getapp", seq = 2, role = "Saas_Merchant", parentValue = "Plugin-MesTemplate")
    public BaseResponse<Map> getAppIdWithType() {
        Map<String, String> map = new HashMap<>();
        List<SaasTemplateMessageConfigDTO> authedApps = officialAccountsService.getAllTemMesConfig(getAdOrgIdNotNull());
        authedApps.stream().forEach(ca -> map.put("CDZ", ca.getAppId()));
        return ResponseUtils.success(map);
    }


    /**
     * 获取公众号配置
     * @param appId
     * @return
     */
    @GetMapping("/wechatThirdConfig/getConfig")
    @AuthorityResource(name = "获取公众号配置", value = "mb_official_account_getconfig", seq = 2, role = "Saas_Merchant", parentValue = "Plugin-MesTemplate")
    public BaseResponse getConfig(@RequestParam("appId") String appId) {
        // 查询授权账号
        OfficialAccountDTO officialAccount = officialAccountsService.getOfficialAccoutByAppId(appId);
        if (officialAccount == null) {
            throw new BusinessException("授权绑定的公众号信息不存在");
        }
        // 查询关联公众号菜单模板
        SaasTemplateMessageConfigDTO caResponse = officialAccountsService.getTemMesConfig(appId, getAdOrgIdNotNull());
        if (caResponse == null) {
            throw new BusinessException("绑定关联不存在");
        }
        // 查询商户信息
        AdOrgDTO orgDTO = adOrgClient.getById(caResponse.getLyyDistributorId()).getData();
        if (orgDTO == null) {
            throw new BusinessException("绑定商家不存在");
        }
        // 初始化数据
        Map<String, Object> map = initResponseMap(caResponse, officialAccount, orgDTO);
        // 查询行业并更新行业信息
        JSONObject allIndustry = TemplateUtil.getIndustry(officialAccount.getAccessToken());
        if (allIndustry != null) {
            updateIndustry(map, allIndustry);
        }
        return success(map);
    }


    /**
     * 设置公众号
     * @param form
     * @return
     * @throws UnsupportedEncodingException
     */
    @PostMapping("/wechatThirdConfig/updateConfig")
    @AuthorityResource(name = "设置公众号", value = "mb_official_account_setconfig", seq = 2, role = "Saas_Merchant", parentValue = "Plugin-MesTemplate")
    public BaseResponse updateMenu(@RequestBody PublicAccountConfigForm form) {
        log.info("设置公众号请求参数：{} ", new Gson().toJson(form));
        if (form == null) {
            throw new BusinessException("参数错误");
        }
        String type = form.getType();
        if ("menu".equalsIgnoreCase(type) || "all".equalsIgnoreCase(type)) {
            validateMenus(form);
        }

        if (!StringUtils.isEmpty(form.getIndustryId1()) && !StringUtils.isEmpty(form.getIndustryId2())
                && (WechatIndustryCodeEnum.getEnumByCode(form.getIndustryId1()) == null
                || WechatIndustryCodeEnum.getEnumByCode(form.getIndustryId2()) == null)) {
            throw new BusinessException("行业类别参数错误");
        }

        if (org.apache.commons.lang.StringUtils.isEmpty(form.getLead()) && form.getLead().length() > 30) {
            throw new BusinessException("关注导语不能为空或超过30个文字");
        }
        // 查询公众号
        SaasTemplateMessageConfigDTO caResponse = officialAccountsService.getTemMesConfig(form.getAppId(), getAdOrgIdNotNull());
        if (caResponse == null) {
            throw new BusinessException("授权绑定公众号关系不存在");
        }
        OfficialAccountDTO officialAccount = officialAccountsService.getOfficialAccoutByAppId(caResponse.getAppId());
        if (officialAccount == null) {
            throw new BusinessException("授权绑定的公众号信息不存在.");
        }
        validateAccount(officialAccount);
        // 获取accessToken
        String accessToken = officialAccount.getAccessToken();
        if (StringUtils.isEmpty(accessToken)) {
            throw new BusinessException("获取Token失败，请稍后重试");
        }

        // 避免多次快速请求
        lockOpeartion(form.getAppId());

        String eType = caResponse.getEquipmentType();
        SaasTemplateMessageConfigDTO cau = new SaasTemplateMessageConfigDTO();
        cau.setAppId(form.getAppId());
        configMenu(cau, caResponse, form, accessToken);
        configTemplate(cau, caResponse, form, accessToken, "CDZ");

        log.info("最终配置结果：{}", JSONObject.toJSON(cau));
        officialAccountsService.updateSaasTemMesConfig(cau);
        return ResponseUtils.success();
    }

    /**
     * 初始化历史菜单
     * @return
     */
    @PostMapping("/initMenu")
    @AuthorityResource(name = "初始化菜单", value = "mb_official_account_menuinit", seq = 2, role = "Saas_Merchant", parentValue = "Plugin-MesTemplate")
    public BaseResponse initMenu() {
        try {
            updateMineMenu();
            return ResponseUtils.success();
        } catch (Exception e) {
            log.error("[公众号自定义菜单]历史菜单初始化失败");
            throw new BusinessException("历史菜单初始化失败");
        }
    }

    /**
     * 解绑公众号
     * @param appId
     * @return
     */
    @PostMapping("/deleteAuthorization")
    @AuthorityResource(name = "删除授权关系", value = "mb_official_account_del", seq = 2, role = "Saas_Merchant", parentValue = "Plugin-MesTemplate")
    public BaseResponse deleteAuthorization(@RequestParam("appId") String appId) {

        OfficialAccountDTO officialAccount = officialAccountsService.getOfficialAccoutByAppId(appId);
        String accessToken = officialAccount.getAccessToken();
        if (StringUtils.isEmpty(accessToken)) {
            throw new BusinessException("删除菜单获取Token失败，请稍后重试");
        }
        boolean b = MenuUtil.deleteMenu(accessToken);
        if (b) {
            officialAccountsService.deleteSaasTemMesCon(appId);
        } else {
            throw new BusinessException("删除微信端菜单失败，请稍后重试");
        }

        return success();
    }
    
    private void updateMineMenu() {
        int offset = 0, size = 100;
        List<SaasTemplateMessageConfigDTO> cas = new ArrayList<>();
        do {
            cas = officialAccountsService.findByPagination(offset, size);
            for (SaasTemplateMessageConfigDTO ca : cas) {
                Integer oldMenuStatus = ca.getMenuStatus();
                String oldMenuConfig = ca.getMenuConfig();
                OfficialAccountDTO officialAccount = officialAccountsService.getOfficialAccoutByAppId(ca.getAppId());
                String accessToken = officialAccount.getAccessToken();
                if (Integer.valueOf(1).equals(oldMenuStatus) && org.apache.commons.lang.StringUtils.isNotBlank(oldMenuConfig) && org.apache.commons.lang.StringUtils.isNotBlank(accessToken)) {
                    net.sf.json.JSONObject menuConfig = net.sf.json.JSONObject.fromObject(oldMenuConfig);
                    if (!doExchangeMenu(ca.getAppId(), ca.getEquipmentType(), menuConfig)) {
                        log.info(String.format("初始化公众号[%s]菜单——我的XX订单——菜单无需初始化或初始化异常，跳过", ca.getAppId()));
                        continue;
                    }
                    Integer status = ca.getMenuStatus();
                    if (MenuUtil.deleteMenu(accessToken)) {
                        log.info(String.format("初始化公众号[%s]菜单——我的XX订单——删除成功", ca.getAppId()));
                        status = 0;
                        oldMenuConfig = null;
                    }
                    if (MenuUtil.createMenu(menuConfig.toString(), accessToken)) {
                        log.info(String.format("初始化公众号[%s]菜单——我的——创建成功", ca.getAppId()));
                        status = 1;
                        oldMenuConfig = menuConfig.toString();
                    }
                    officialAccountsService.updateMenuConfig(status, oldMenuConfig, ca.getAppId());
                }
            }
            offset += size;
        } while (cas.size() >= size);
    }

    private boolean doExchangeMenu(String appId, String equipmentType, net.sf.json.JSONObject menuConfig) {
        try {
            boolean doExchange = false;
            JSONArray buttons = menuConfig.getJSONArray("button");
            if (buttons != null && !buttons.isEmpty()) {
                for (Object b : buttons) {
                    net.sf.json.JSONObject button = (net.sf.json.JSONObject) b;
                    JSONArray subButtons = button.getJSONArray("sub_button");
                    if (subButtons == null || subButtons.isEmpty()) {
                        // 一级菜单
                        String url = button.getString("url");
                        Boolean editable = button.getBoolean("editable");
                        if (!editable && isMyXxOrderMenu(url)) {
                            button.put("url", getMineUrlByType(equipmentType, url));
                            doExchange = true;
                        }
                    } else {
                        // 二级菜单
                        for (Object sb : subButtons) {
                            net.sf.json.JSONObject subButton = (net.sf.json.JSONObject) sb;
                            String url = subButton.getString("url");
                            Boolean editable = subButton.getBoolean("editable");
                            if (!editable && isMyXxOrderMenu(url)) {
                                subButton.put("url", getMineUrlByType(equipmentType, url));
                                doExchange = true;
                            }
                        }
                    }
                }
            }
            return doExchange;
        } catch (Exception e) {
            log.error(String.format("初始化公众号[%s]菜单——我的XX订单——失败", appId));
            log.error(e.getMessage(), e);
            return false;
        }
    }

    private Object getMineUrlByType(String equipmentType, String url) {
        if ("XYJ".equals(equipmentType)) {
            return url.replace("wash-order.html", "html%2Fmy-wash.html");
        } else if ("CDZ".equals(equipmentType)) {
            return url.replace("charge-order.html", "html%2Fmy-charging.html");
        } else {
            return url;
        }
    }

    private boolean isMyXxOrderMenu(String url) {
        if (url == null) {
            return false;
        }
        return url.contains("charge-order.html") || url.contains("wash-order.html");
    }

    private void configTemplate(SaasTemplateMessageConfigDTO cau, SaasTemplateMessageConfigDTO caResponse, PublicAccountConfigForm form, String accessToken, String eType) {
        try {
            TemplateMesConfig oldTemplateConfig = new Gson().fromJson(caResponse.getTemplateConfig(),
                    TemplateMesConfig.class);
            String type = form.getType();
            String industryId1 = form.getIndustryId1();
            String industryId2 = form.getIndustryId2();
            log.info("公众号模板创建：type:{}", type);
            if ("template".equals(type) || "all".equalsIgnoreCase(type)) {
                List<TemplateMes> templates = new ArrayList<>();
                TemplateMesConfig templateConfig = new TemplateMesConfig();
                // 行业类别未配置 或者 已设置的行业类别与模板对应行业（互联网|电子商务）均不匹配，需要更新行业设置
                if (oldTemplateConfig == null
                        || (!WechatIndustryCodeEnum.CODE1.getCode().equals(oldTemplateConfig.getIndustryId1())
                        && !WechatIndustryCodeEnum.CODE1.getCode().equals(oldTemplateConfig.getIndustryId2()))) {
                    // 查询行业接口出错，行业信息透传失败 ，强制设置
                    if (org.apache.commons.lang.StringUtils.isBlank(industryId1) && org.apache.commons.lang.StringUtils.isBlank(industryId2)) {
                        industryId1 = WechatIndustryCodeEnum.CODE1.getCode();
                        industryId2 = WechatIndustryCodeEnum.CODE3.getCode();
                    }
                    // 需要更新行业设置，但是行业信息不为（互联网|电子商务）
                    if (!WechatIndustryCodeEnum.CODE1.getCode().equals(industryId1) && !WechatIndustryCodeEnum.CODE1.getCode().equals(industryId2)) {
                        throw new IllegalArgumentException("请勾选需要替换为（互联网|电子商务）的行业信息");
                    }
                    TemplateUtil.apiSetIndustry(industryId1, industryId2, accessToken);
                }

                JSONObject allIndustry = TemplateUtil.getIndustry(accessToken);
                if (allIndustry != null) {
                    templateConfig.setGetAllTemplate(allIndustry.toString());
                }
                // 未配置才配置
                List<TemplateMes> oldTempConfig = (oldTemplateConfig == null || oldTemplateConfig.getTemplateIdShorts() == null)
                        ? new ArrayList<>()
                        : oldTemplateConfig.getTemplateIdShorts();
                for (TemplateMes oldTemp : oldTempConfig) {
                    if (!StringUtils.isEmpty(oldTemp.getTemplateId())) {
                        TemplateUtil.dePrivateTemplate(oldTemp.getTemplateId(), accessToken);
                    }
                }
                int templateSuccessNum = addTemplates(eType, templates, accessToken);
                // 查询模板配置结果
                JSONObject allTemp = TemplateUtil.getAllPrivateTemplate(accessToken);
                if (allTemp != null) {
                    templateConfig.setGetAllTemplate(allTemp.toString());
                }
                if (templateSuccessNum > 0) {
                    cau.setTemplateStatus(1);
                    templateConfig.setMsg("模板消息配置成功");
                }
                templateConfig.setTemplateIdShorts(templates);
                cau.setTemplateConfig(JSONObject.toJSONString(templateConfig));
            } else {
                List<TemplateMes> oldTempConfig = (oldTemplateConfig == null || oldTemplateConfig.getTemplateIdShorts() == null)
                        ? new ArrayList<>()
                        : oldTemplateConfig.getTemplateIdShorts();
                for (TemplateMes oldTemp : oldTempConfig) {
                    if (!StringUtils.isEmpty(oldTemp.getTemplateId())) {
                        TemplateUtil.dePrivateTemplate(oldTemp.getTemplateId(), accessToken);
                    }
                }
                cau.setTemplateStatus(0);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    private int addTemplates(String eType, List<TemplateMes> templates, String accessToken) {
        int templateSuccessNum = 0;
        String msg;
//		if ("CDZ".equals(eType)) {
//			// 模板1
//			JSONObject template1Json = TemplateUtil.apiAddTemplate("OPENTM406010873", accessToken);
//			TemplateMes template1 = new TemplateMes(ComponentAuthTemplateTypeEnum.CDZ_JSCD.getValue(),
//					ComponentAuthTemplateTypeEnum.CDZ_JSCD.getName());
//			if (template1Json != null && (Integer) template1Json.get("errcode") == 0) {
//				template1.setTemplateId((String) template1Json.get("template_id"));
//				templateSuccessNum++;
//			} else {
//				msg = template1Json == null ? "配置失败" : "errmsg:" + (template1Json.get("errmsg") + ", errcode:" + template1Json.get("errcode"));
//				template1.setMsg(msg);
//			}
//			templates.add(template1);
        // 模板2
        JSONObject template2Json = TemplateUtil.apiAddTemplate("OPENTM406010873", accessToken);
        TemplateMes template2 = new TemplateMes(ComponentAuthTemplateTypeEnum.CDZ_CDTX.getValue(),
                ComponentAuthTemplateTypeEnum.CDZ_CDTX.getName());
        if (template2Json != null && (Integer) template2Json.get("errcode") == 0) {
            template2.setTemplateId((String) template2Json.get("template_id"));
            templateSuccessNum++;
        } else {
            msg = template2Json == null ? "配置失败" : "errmsg:" + (template2Json.get("errmsg") + ", errcode:" + template2Json.get("errcode"));
            template2.setMsg(msg);
        }
        templates.add(template2);
        return templateSuccessNum;
    }

    private void configMenu(SaasTemplateMessageConfigDTO cau, SaasTemplateMessageConfigDTO caResponse, PublicAccountConfigForm form, String accessToken) {
        Integer oldMenuStatus = caResponse.getMenuStatus();
        String type = form.getType();
        if ("menu".equalsIgnoreCase(type) || "all".equalsIgnoreCase(type)) {
            if (Integer.valueOf(1).equals(oldMenuStatus)) {// 已配置菜单，先删除
                MenuUtil.deleteMenu(accessToken);
            }
            JSONObject obj = new JSONObject();
            obj.put("button", form.getMenus());
            String menuConfig = obj.toString();
            if (MenuUtil.createMenu(menuConfig, accessToken)) {// 成功创建菜单使用新菜单配置
                cau.setMenuStatus(1);
                cau.setMenuConfig(menuConfig);
            }
        } else {
            if (Integer.valueOf(1).equals(oldMenuStatus) && MenuUtil.deleteMenu(accessToken)) {
                cau.setMenuStatus(0);
            }
        }
    }

    private void lockOpeartion(String appId) {
        final LockInfo lockInfo = redisLock.lock(RedisKey.OFFICIAL_ACCOUNT_LOCK_KEY.concat(appId), "Y", 3, 0);
        if (lockInfo == null) {
            throw new BusinessException("正在配置，请稍后重试");
        }
    }


    private void validateAccount(OfficialAccountDTO officialAccount) {
        if (!"Y".equals(officialAccount.getIsauthorized())) {
            throw new BusinessException("您已取消授权，请重新授权后操作.");
        }
        if (!"FWH".equals(officialAccount.getType())) {
            throw new BusinessException("抱歉，暂时只支持配置服务号.");
        }
        // 判断是否有权限
        String funcInfo = officialAccount.getFuncInfo();
        if (StringUtils.isEmpty(funcInfo) || !funcInfo.contains("7;") || !funcInfo.contains("15;")) {
            throw new BusinessException("尚未获取消息管理或自定义菜单权限，请确保已获取权限并授权.");
        }
    }
    

    private void validateMenus(PublicAccountConfigForm form) {
        if (form.getMenus() == null || form.getMenus().isEmpty()) {
            throw new IllegalArgumentException("至少选择一个菜单");
        }
        if (form.getMenus().size() > 3) {
            throw new IllegalArgumentException("最多3个一级菜单");
        }
        for (PublicAccountConfigForm.MenuItem menu : form.getMenus()) {
            menu.setAppid(miniAppId);
            if (org.apache.commons.lang.StringUtils.isEmpty(menu.getName())) {
                throw new IllegalArgumentException("菜单名称不能为空");
            }
            if (menu.getSub_button() != null && !menu.getSub_button().isEmpty()) {// 二级菜单，校验子菜单功能
                menu.setType(null);// 一级菜单按钮无类型
                menu.setAppid(miniAppId);
                validateSecondaryMenus(menu, form.getAppId());
            } else {// 一级菜单，校验url链接
                menu.setType("miniprogram");// 设置菜单类型为view
                validatePrimaryMenu(menu, form.getAppId());
            }
        }
    }

    private void validatePrimaryMenu(PublicAccountConfigForm.MenuItem menu, String appId) {
        if (menu.getName().length() > 6) {
            throw new IllegalArgumentException("一级菜单名称不能超过6个文字");
        }
        if (org.apache.commons.lang.StringUtils.isEmpty(menu.getPagepath())) {
            throw new IllegalArgumentException("小程序链接不能为空");
        }
    }

    private void validateSecondaryMenus(PublicAccountConfigForm.MenuItem menu, String appId) {
        if (menu.getSub_button().size() > 5) {
            throw new IllegalArgumentException("最多5个二级菜单");
        }
        if (menu.getName().length() > 5) {
            throw new IllegalArgumentException("二级菜单的一级菜单名称不能超过5个文字");
        }
        for (PublicAccountConfigForm.MenuItem m : menu.getSub_button()) {
            m.setType("miniprogram");// 设置菜单类型为view
            if (org.apache.commons.lang.StringUtils.isEmpty(m.getName())) {
                throw new IllegalArgumentException("菜单名称不能为空");
            }
            if (m.getName().length() > 6) {
                throw new IllegalArgumentException("二级菜单名称超过不能超过6个文字");
            }
            if (org.apache.commons.lang.StringUtils.isEmpty(menu.getPagepath())) {
                throw new IllegalArgumentException("小程序链接不能为空");
            }
        }
    }

    private void updateIndustry(Map<String, Object> map, JSONObject allIndustry) {
        if (allIndustry.containsKey("primary_industry")) {
            JSONObject primary = allIndustry.getJSONObject("primary_industry");
            JSONObject secondary = allIndustry.getJSONObject("secondary_industry");
            String primaryClass = primary.getString("second_class");
            String secondaryClass = secondary.getString("second_class");
            log.info("查询行业处理结果, primaryClass: {}, secondaryClass:{}", primaryClass, secondaryClass);
            if (!StringUtils.isEmpty(primaryClass)) {
                updatePrimaryIndustry(map, primaryClass);
            }
            if (!StringUtils.isEmpty(secondaryClass)) {
                updateSecondaryIndustry(map, secondaryClass);
            }
            if (!primaryClass.contains("互联网") && !secondaryClass.contains("互联网")) {
                map.put("isShowIndustry", "Y");
            }
        } else {
            map.put("isTemplateAuth", "N");
            map.put("templateAuthMsg", "无权限" + "[" + (allIndustry.containsKey("errcode") ? allIndustry.get("errcode") : "") + "]"
                    + (allIndustry.containsKey("errmsg") ? allIndustry.get("errmsg") : ""));
        }
    }

    private void updateSecondaryIndustry(Map<String, Object> map, String secondaryClass) {
        WechatIndustryCodeEnum s = WechatIndustryCodeEnum.getEnumBySecondClass(secondaryClass);
        if (s != null) {
            map.put("secondaryIndustryFirst", s.getFirst());
            map.put("secondaryIndustrySecond", s.getSecond());
            map.put("secondaryIndustryCode", s.getCode());
        }
    }

    private void updatePrimaryIndustry(Map<String, Object> map, String primaryClass) {
        WechatIndustryCodeEnum f = WechatIndustryCodeEnum.getEnumBySecondClass(primaryClass);
        if (f != null) {
            map.put("primaryIndustryFirst", f.getFirst());
            map.put("primaryIndustrySecond", f.getSecond());
            map.put("primaryIndustryCode", f.getCode());
        }
    }

    private Map<String, Object> initResponseMap(SaasTemplateMessageConfigDTO caResponse, OfficialAccountDTO officialAccount, AdOrgDTO orgDTO) {
        Map<String, Object> map = new HashMap<>();
        map.put("isShowIndustry", "N");
        map.put("isTemplateAuth", "Y");
        map.put("isMenuAuth", "Y");
        map.put("primaryIndustryFirst", "");
        map.put("primaryIndustrySecond", "");
        map.put("primaryIndustryCode", "");
        map.put("secondaryIndustryFirst", "");
        map.put("secondaryIndustrySecond", "");
        map.put("secondaryIndustryCode", "");
        map.put("forceIndustryCode", "1");
        map.put("forceIndustrySecond", "互联网|电子商务");
        map.put("eType", caResponse.getEquipmentType());
        map.put("officialAccountName", officialAccount.getOfficialAccoutName());
        map.put("officialAccount", officialAccount.getOfficialAccout());
        map.put("adOrgId", orgDTO.getAdOrgId());
        map.put("adOrgValue", orgDTO.getValue());
        map.put("adOrgName", orgDTO.getName());

        String funcInfo = officialAccount.getFuncInfo();
        if (StringUtils.isEmpty(funcInfo) || !funcInfo.contains("7;") || !"Y".equals(officialAccount.getIsauthorized())) {
            map.put("isTemplateAuth", "N");
        }
        if (StringUtils.isEmpty(funcInfo) || !funcInfo.contains("15;") || !"Y".equals(officialAccount.getIsauthorized())) {
            map.put("isMenuAuth", "N");
        }

        String menuSuccess = (Integer.valueOf(1).equals(caResponse.getMenuStatus())) ? "Y" : "N";
        map.put("menuSuccess", menuSuccess);
        map.put("menuConfig", "Y".equals(menuSuccess) ? JSONObject.parse(caResponse.getMenuConfig()) : null);
        map.put("templateSuccess", (caResponse.getTemplateStatus() != null && caResponse.getTemplateStatus() == 1) ? "Y" : "N");
        if (officialAccount.getAuthorizationExpiresDate() != null) {
            Date expures = DateUtils.parse(officialAccount.getAuthorizationExpiresDate(), DateUtils.DatePattern.yyyy_MM_dd_HH_mm_ss);
            int day = DateUtils.daysBetween(new Date(), expures);
            if (Objects.nonNull(expures)) {
                if (day == 0 && expures.getTime() > System.currentTimeMillis()) {
                    day = 1;
                }
            }
            map.put("day", Math.max(day, 0));
            log.info("授权过期日期：【{}】，授权时间剩余：【{}】", officialAccount.getAuthorizationExpiresDate(), day);
        }
        return map;
    }
}
