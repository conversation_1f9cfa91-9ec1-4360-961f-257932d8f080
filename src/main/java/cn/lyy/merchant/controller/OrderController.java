package cn.lyy.merchant.controller;

import static cn.lyy.merchant.constants.OrderExceptionEnums.QUERY_PARAM_ERROR;
import static java.util.Optional.ofNullable;

import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.dto.Pagination;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.base.utils.text.JSONObjectUtils;
import cn.lyy.merchant.api.service.MerchantGroupService;
import cn.lyy.merchant.api.service.MerchantShopClient;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.merchant.StatisticsDTO;
import cn.lyy.merchant.dto.merchant.request.MerchantGroupRequest;
import cn.lyy.merchant.dto.merchant.response.MerchantGroupDTO;
import cn.lyy.merchant.dto.request.OrderQueryRequest;
import cn.lyy.merchant.dto.request.OrderRefundRequest;
import cn.lyy.merchant.dto.response.OrderInfoDTO;
import cn.lyy.merchant.dto.response.OrderInfoDetailDTO;
import cn.lyy.merchant.dto.response.RefundInfoDetailDTO;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import cn.lyy.merchant.exception.OrderException;
import cn.lyy.merchant.service.order.OrderService;
import cn.lyy.merchant.service.order.RefundService;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @createTime 2020-12-02
 * @auther peterguo
 *
 * @Description 订单中心
 */

@Slf4j
@RequestMapping("/rest/order")
@RestController
public class OrderController extends BaseController {

    @Autowired
    private OrderService orderService;

    @Autowired
    private RefundService refundService;

    @Autowired
    private MerchantGroupService merchantGroupService;

    @Autowired
    private MerchantShopClient merchantShopClient;

    private static int LONGEST_QUERY_TIME = 31;

    @PostMapping("/getOrderList")
    @AuthorityResource(name = "订单查询", value = "mb_orderQuery", seq = 201, role = "Saas_Merchant", parentValue = "PaymentOrder")
    public BaseResponse getOrderList(@Validated @RequestBody OrderQueryRequest orderQueryRequest){
        log.info("订单查询,参数={}", orderQueryRequest);

        AdUserInfoDTO currentUser = getCurrentUser();

        //空字符串特殊处理 bad code
        ofNullable(orderQueryRequest.getSearchWord())
                .filter(StringUtils::isBlank)
                .map( search ->{
                    orderQueryRequest.setSearchWord(null);
                    return search;
                });

        /**
         *  场地id有传入时使用传入值
         *  场地id未传入时
         *      searchWord有值
         *           如果是订单号 不需要查询场地列表
         *           如果是用户id/设备code 需要查询登陆用户权限
         *      searchWord没有值  需要查询登陆用户权限
         */
        if(Objects.isNull(orderQueryRequest.getStoreId())){
            //查询登陆用户权限 主账号能看到所有订单，子账号只能看到自己权限范围内的场地里面交易的订单
            List<MerchantGroupDTO> groupList = getLoginUserGroupList(currentUser,orderQueryRequest.getSearchWord());
            ofNullable(groupList).map( list ->{
                orderQueryRequest.setStoreId(list.stream().map(MerchantGroupDTO::getEquipmentGroupId).collect(Collectors.toList()));
                return list;
            });
        }

        orderQueryRequest.setDistributorId(currentUser.getAdOrgId());

        //时间参数校验
        LocalDate now = LocalDate.now();
        LocalDate lateOneMonth = now.minusDays(LONGEST_QUERY_TIME);

        //开始时间需要晚于lateOneMonth
        ofNullable(orderQueryRequest.getStartTime())
                .map(LocalDateTime::toLocalDate).map(lateOneMonth::isBefore)
                                                .filter(Boolean.TRUE::equals)
                                                .orElseThrow(() -> new OrderException(QUERY_PARAM_ERROR.getExCode(),QUERY_PARAM_ERROR.getMsg()));

        //结束时间需要早于当前时间
        LocalDate endDate = orderQueryRequest.getStartTime().toLocalDate();
        if(now.isBefore(endDate)){
            throw new OrderException(QUERY_PARAM_ERROR.getExCode(),QUERY_PARAM_ERROR.getMsg());
        }

        BaseResponse response = new BaseResponse();
        log.debug("订单查询请求参数:{}", orderQueryRequest);
        Pagination<OrderInfoDTO> orderInfoDTOList = orderService.getOrderList(orderQueryRequest);
        response.setData(orderInfoDTOList);

        return response;
    }

    /**
     * 获取当前登录用户的场地权限列表
     * @param currentUser
     * @return
     */
    private List<MerchantGroupDTO> getLoginUserGroupList(AdUserInfoDTO currentUser,String searchWord){
        if(Objects.nonNull(searchWord) && searchWord.length() == 32){
          return null;
        }

        //主账号,直接根据商户id进行查询,不传场地数组
        if(currentUser != null && currentUser.getIsApprover()) {
            log.debug("当前登录用户为主账号,adUserId:{},adOrgId:{}", currentUser.getAdUserId(), currentUser.getAdOrgId());
            return null;
        }

        MerchantGroupRequest request = MerchantGroupRequest.builder()
                .adUser(ofNullable(currentUser).map(AdUserInfoDTO::getAdUserId).orElse(null))
//                .isActive(1)
                .distributor(currentUser.getAdOrgId())
                .build();

        //查询登陆用户权限 主账号能看到所有订单，子账号只能看到自己权限范围内的场地里面交易的订单
        return ofNullable(merchantGroupService.selectGroup(request)).map(BaseResponse::getData).orElseGet(ArrayList::new);
    }

    @GetMapping("/getOrderDetail")
    @AuthorityResource(name = "订单详情", value = "mb_orderDetail", seq = 202, role = "Saas_Merchant", parentValue = "PaymentOrder")
    public BaseResponse<OrderInfoDetailDTO> getOrderDetail(@RequestParam("outTradeNo") String outTradeNo){
        log.info("订单详情,outTradeNo={}",outTradeNo);

        OrderInfoDetailDTO orderInfoDetail = orderService.getOrderDetail(outTradeNo);
        BaseResponse response = new BaseResponse();
        response.setData(orderInfoDetail);

        return response;
    }

    @GetMapping("/getRefundDetail")
    @AuthorityResource(name = "退款详情", value = "mb_refundDetail", seq = 203, role = "Saas_Merchant", parentValue = "PaymentOrder")
    public BaseResponse<RefundInfoDetailDTO> getRefundDetail(@RequestParam("refundNo") String refundNo){
        log.info("退款详情,refundNo={}",refundNo);

        RefundInfoDetailDTO refundDetail = refundService.getRefundDetail(refundNo);
        BaseResponse response = new BaseResponse();
        response.setData(refundDetail);

        return response;
    }

    /**
     * 退款接口
     * @param request
     * @return
     */
    @PostMapping("/refund")
    @AuthorityResource(name = "订单退款操作", value = "mb_refundOperate", seq = 204, role = "Saas_Merchant", parentValue = "PaymentOrder")
    public BaseResponse refund(@RequestBody OrderRefundRequest request) {
        log.info("退款参数: {}", log.isDebugEnabled() ? JSONObjectUtils.toJSONString(request): request);
        request.setDistributorId(getAdOrgIdNotNull());
        refundService.refund(request);
        return ResponseUtils.success();
    }

    /**
     * 订单导出
     * @param userOrgId 商家id
     * @return
     */
    @GetMapping("/exportStatistics")
    @AuthorityResource(name = "订单导出", value = "mb_export", seq = 205, role = "Saas_Merchant", parentValue = "PaymentOrder")
    public BaseResponse<List<StatisticsDTO>> exportStatistics(@RequestParam("userOrgId") Long userOrgId) {
        return ResponseUtils.success(merchantShopClient.exportStatistics(userOrgId).getData());
    }
}
