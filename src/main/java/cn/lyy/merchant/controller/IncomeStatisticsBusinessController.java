package cn.lyy.merchant.controller;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.dto.JsonObject;
import cn.lyy.base.dto.Pagination;
import cn.lyy.base.dto.Status;
import cn.lyy.base.util.CollectionUtil;
import cn.lyy.entity.LyyEquipmentAttachVO;
import cn.lyy.equipment.dto.equipment.EquipmentDTO;
import cn.lyy.equipment.service.EquipmentService;
import cn.lyy.income.api.IMerchantIncomeService;
import cn.lyy.income.dto.constant.IncomeSortField;
import cn.lyy.income.dto.constant.ResponseCodeEnum;
import cn.lyy.income.dto.income.*;
import cn.lyy.income.dto.param.MerchantIncomeRequest;
import cn.lyy.merchant.api.service.MerchantGroupService;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.merchant.request.MerchantGroupRequest;
import cn.lyy.merchant.dto.merchant.response.MerchantGroupDTO;
import cn.lyy.merchant.dto.request.IncomeRequestDTO;
import cn.lyy.merchant.exception.IncomeConstants;
import cn.lyy.merchant.exception.IncomeException;
import cn.lyy.merchant.exception.IncomePageException;
import cn.lyy.merchant.exception.IncomeToDayException;
import cn.lyy.merchant.microservice.IEquipmentCoinsClient;
import cn.lyy.tools.util.DateUtil;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.Optional.ofNullable;

/**
 * @ClassName: IncomeStatisticsBusinessController
 * @description: 经营统计
 * @author: pengkun
 * @create: 2020-11-05 17:28
 * @Version 1.0
 **/
@RestController
@RequestMapping("/rest/income")
@Slf4j
public class IncomeStatisticsBusinessController extends BaseController {

    @Autowired
    private IMerchantIncomeService iMerchantIncomeService;

    @Autowired
    private MerchantGroupService merchantGroupService;

    @Autowired
    private IEquipmentCoinsClient equipmentCoinsClient;

    @Autowired
    private EquipmentService equipmentService;

    private DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @PostMapping(value = "/benefit/general", consumes = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse benefitGeneral() {
        BaseResponse baseResponse = new BaseResponse();
        log.debug("首页收益统计");
        MerchantGroupRequest merchantGroupRequest = MerchantGroupRequest.builder().adUser(getAdUserIdNotNull()).build();
        List<MerchantGroupDTO> list = merchantGroupService.selectGroup(merchantGroupRequest).getData();
        if (CollectionUtil.isEmpty(list)) {
            throw new IncomeException(IncomeConstants.GROUP_EMPTY);
        }
        List<Integer> groups = new ArrayList<>();
        list.forEach(l ->{
            groups.add(l.getEquipmentGroupId().intValue());
        });
        MerchantIncomeRequest request = new MerchantIncomeRequest();
        request.setGroups(groups);
        LocalDate now = LocalDate.now();
        request.setStart(now);
        request.setEnd(now);
        request.setDistributor(getAdOrgIdNotNull().intValue());
        try {
            cn.lyy.income.dto.bean.BaseResponse<IncomeResponseDTO> response = iMerchantIncomeService.general(request);
            IncomeResponseDTO data = ofNullable(response).filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode())
                    .map(cn.lyy.income.dto.bean.BaseResponse::getData).orElseThrow(() -> new IncomeException(IncomeConstants.BUSINESS_ERROR));
            baseResponse.setData(data);
        } catch (Exception e) {
            log.error("#====> 首页收益统计异常");
            log.error(e.getMessage(), e);
            throw new IncomeException(IncomeConstants.BUSINESS_ERROR.getCode(), e.getMessage());
        }
        return baseResponse;
    }

    @PostMapping(value = "/benefit/general/today", consumes = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse benefitGeneralToDay() {
        BaseResponse baseResponse = new BaseResponse();
        log.debug("首页收益统计");
        MerchantGroupRequest merchantGroupRequest = MerchantGroupRequest.builder().adUser(getAdUserIdNotNull()).build();
        List<MerchantGroupDTO> list = merchantGroupService.selectGroup(merchantGroupRequest).getData();
        if (CollectionUtil.isEmpty(list)) {
            throw new IncomeException(IncomeConstants.GROUP_EMPTY);
        }
        List<Integer> groups = new ArrayList<>();
        list.forEach(l ->{
            groups.add(l.getEquipmentGroupId().intValue());
        });
        MerchantIncomeRequest request = new MerchantIncomeRequest();
        request.setGroups(groups);
        LocalDate now = LocalDate.now();
        request.setStart(now);
        request.setEnd(now);
        request.setDistributor(getAdOrgIdNotNull().intValue());
        try {
            cn.lyy.income.dto.bean.BaseResponse<IncomeToDayResponseDTO> response = iMerchantIncomeService.generalToDay(request);
            IncomeToDayResponseDTO data = ofNullable(response).filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode())
                    .map(cn.lyy.income.dto.bean.BaseResponse::getData).orElseThrow(() -> new IncomeToDayException(IncomeConstants.BUSINESS_ERROR));
            baseResponse.setData(baseResponse);
        } catch (Exception e) {
            log.error("#====> 首页收益统计异常");
            log.error(e.getMessage(), e);
            throw new IncomeToDayException(IncomeConstants.BUSINESS_ERROR.getCode(), e.getMessage());
        }
        return baseResponse;
    }

    @PostMapping(value = "/benefit/summary", consumes = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse summary(@RequestBody @Validated IncomeRequestDTO request, BindingResult result) {
        BaseResponse baseResponse = new BaseResponse();
        log.debug("经营统计汇总统计/benefit/summary:{}", request);
        if (result.hasErrors()) {
            String message = result.getFieldError().getDefaultMessage();
            throw new IncomeException(IncomeConstants.PARAM_ERROR.getCode(), message);
        }
        List<Integer> groups = request.getGroups();
        if (CollectionUtils.isEmpty(groups)) {
            MerchantGroupRequest merchantGroupRequest = MerchantGroupRequest.builder().adUser(getAdUserIdNotNull()).build();
            List<MerchantGroupDTO> list = merchantGroupService.selectGroup(merchantGroupRequest).getData();
            if (CollectionUtil.isEmpty(list)) {
                throw new IncomeException(IncomeConstants.GROUP_EMPTY);
            }
            List<Integer> finalGroups = new ArrayList<>();
            list.forEach(l ->{
                finalGroups.add(l.getEquipmentGroupId().intValue());
            });
            groups = finalGroups;
        }
        ofNullable(groups).filter(c -> !c.isEmpty())
                .orElseThrow(() -> new IncomeException(IncomeConstants.GROUP_EMPTY));
        request.setGroups(groups);
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        MerchantIncomeRequest incomeRequest = buildRequest(request, groups);
        incomeRequest.setEquipmentTypes(request.getEquipmentType());
        incomeRequest.setEquipmentLabels(request.getLabels());

        cn.lyy.income.dto.bean.BaseResponse<IncomeResponseDTO> response = iMerchantIncomeService.general(incomeRequest);
        IncomeResponseDTO data = ofNullable(response).filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode())
                .map(cn.lyy.income.dto.bean.BaseResponse::getData).orElseThrow(() -> new IncomeException(IncomeConstants.BUSINESS_ERROR));
        baseResponse.setData(data);
        return baseResponse;

    }

    @PostMapping(value = "/benefit/group", consumes = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse groupGeneral(@RequestBody @Validated IncomeRequestDTO request, BindingResult result) {
        BaseResponse baseResponse = new BaseResponse();
        log.debug("场地收益统计/benefit/group:{}", request);
        if (result.hasErrors()) {
            String message = result.getFieldError().getDefaultMessage();
            throw new IncomePageException(IncomeConstants.PARAM_ERROR.getCode(), message);
        }
        List<Integer> groups = request.getGroups();
        if (CollectionUtils.isEmpty(groups)) {
            MerchantGroupRequest merchantGroupRequest = MerchantGroupRequest.builder().adUser(getAdUserIdNotNull()).build();
            List<MerchantGroupDTO> list = merchantGroupService.selectGroup(merchantGroupRequest).getData();
            if (CollectionUtil.isEmpty(list)) {
                throw new IncomeException(IncomeConstants.GROUP_EMPTY);
            }
            List<Integer> finalGroups = new ArrayList<>();
            list.forEach(l ->{
                finalGroups.add(l.getEquipmentGroupId().intValue());
            });
            groups = finalGroups;
        }
        ofNullable(groups).filter(c -> !c.isEmpty())
                .orElseThrow(() -> new IncomePageException(IncomeConstants.GROUP_EMPTY));
        request.setGroups(groups);

        MerchantIncomeRequest incomeRequest = buildRequest(request, groups);

        try {
            if (CollectionUtil.isEmpty(request.getEquipmentType())) {
                incomeRequest.setEquipmentLabels(request.getLabels());
                cn.lyy.income.dto.bean.BaseResponse<PageInfo<IncomeGroupResponseDTO>> response =
                        iMerchantIncomeService.generalGroup(incomeRequest);
                PageInfo<IncomeGroupResponseDTO> data = ofNullable(response).filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode())
                        .map(cn.lyy.income.dto.bean.BaseResponse::getData).orElseThrow(() -> new IncomePageException(IncomeConstants.BUSINESS_ERROR));
                baseResponse.setData(convertPagination(data));
            } else {
                incomeRequest.setEquipmentTypes(request.getEquipmentType());
                incomeRequest.setEquipmentLabels(request.getLabels());
                cn.lyy.income.dto.bean.BaseResponse<PageInfo<IncomeEquipmentTypeResponseDTO>> response =
                        iMerchantIncomeService.generalEquipmentType(incomeRequest);
                PageInfo<IncomeEquipmentTypeResponseDTO> data =
                        ofNullable(response).filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode())
                                .map(cn.lyy.income.dto.bean.BaseResponse::getData)
                                .orElseThrow(() -> new IncomePageException(IncomeConstants.BUSINESS_ERROR));
                baseResponse.setData(convertPagination(data));
            }
        } catch (Exception e) {
            log.error("场地收益异常");
            log.error(e.getMessage(), e);
            throw new IncomePageException(IncomeConstants.BUSINESS_ERROR.getCode(), e.getMessage());
        }
        return baseResponse;
    }

    @PostMapping(value = "/benefit/equipment", consumes = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse equipmentGeneral(@RequestBody @Validated IncomeRequestDTO request, BindingResult result) {
        BaseResponse baseResponse = new BaseResponse();
        log.debug("分页设备收益/benefit/equipment:{}", request);
        if (result.hasErrors()) {
            String message = result.getFieldError().getDefaultMessage();
            throw new IncomePageException(IncomeConstants.PARAM_ERROR.getCode(), message);
        }
        List<Integer> groups = request.getGroups();
        if (CollectionUtils.isEmpty(request.getGroups())) {
            MerchantGroupRequest merchantGroupRequest = MerchantGroupRequest.builder().adUser(getAdUserIdNotNull()).build();
            List<MerchantGroupDTO> list = merchantGroupService.selectGroup(merchantGroupRequest).getData();
            if (CollectionUtil.isEmpty(list)) {
                throw new IncomeException(IncomeConstants.GROUP_EMPTY);
            }
            List<Integer> finalGroups = new ArrayList<>();
            list.forEach(l ->{
                finalGroups.add(l.getEquipmentGroupId().intValue());
            });
            groups = finalGroups;
        }
        ofNullable(groups).filter(c -> !c.isEmpty())
                .orElseThrow(() -> new IncomePageException(IncomeConstants.GROUP_EMPTY));
        request.setGroups(groups);

        MerchantIncomeRequest incomeRequest = buildRequest(request, groups);
        incomeRequest.setEquipmentTypes(request.getEquipmentType());
        incomeRequest.setEquipmentLabels(request.getLabels());

        try {
            cn.lyy.income.dto.bean.BaseResponse<PageInfo<IncomeEquipmentResponseDTO>> response = iMerchantIncomeService.generalEquipment(incomeRequest);
            PageInfo<IncomeEquipmentResponseDTO> data =
                    ofNullable(response).filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode())
                            .map(cn.lyy.income.dto.bean.BaseResponse::getData)
                            .orElseThrow(() -> new IncomePageException(IncomeConstants.BUSINESS_ERROR));
            baseResponse.setData(convertIncomeEquipment(data));
        } catch (Exception e) {
            log.error("设备收益异常");
            log.error(e.getMessage(), e);
            throw new IncomePageException(IncomeConstants.BUSINESS_ERROR.getCode(), e.getMessage());
        }
        return baseResponse;
    }

    @PostMapping(value = "/benefit/equipment/label", consumes = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse equipmentLabelGeneral(@RequestBody @Validated IncomeRequestDTO request, BindingResult result) {
        BaseResponse baseResponse = new BaseResponse();
        log.debug("根据设备标签统计收益:{}", request);
        JsonObject jsonObject = new JsonObject();
        if (result.hasErrors()) {
            String message = result.getFieldError().getDefaultMessage();
            baseResponse.setCode(Status.STATUS_PARAMETER_ERROR);
            baseResponse.setMessage(message);
            return baseResponse;
        }
        if(CollectionUtils.isEmpty(request.getLabels())){
            baseResponse.setCode(Status.STATUS_PARAMETER_ERROR);
//            baseResponse.setMessage(MessageUtils.get("common.param.labelId.miss"));
            return baseResponse;
        }
        MerchantIncomeRequest incomeRequest = new MerchantIncomeRequest();
        incomeRequest.setDistributor(getAdOrgIdNotNull().intValue());
        incomeRequest.setStart(LocalDate.parse(request.getStartDate(), df));
        incomeRequest.setEnd(LocalDate.parse(request.getEndDate(), df));
        incomeRequest.setField(ofNullable(request.getField()).filter(StringUtils::isNotBlank).orElse("equipment_count"));
        incomeRequest.setSort(ofNullable(request.getSortDirection()).filter(StringUtils::isNotBlank).orElse("desc"));
        incomeRequest.setEquipmentLabels(request.getLabels());
        incomeRequest.setPageSize(ofNullable(request.getPageSize()).orElse(20));
        incomeRequest.setPageIndex(ofNullable(request.getPageIndex()).orElse(1));
        incomeRequest.setGroups(request.getGroups());
        try{
            cn.lyy.income.dto.bean.BaseResponse<PageInfo<IncomeEquipmentLabelDTO>> response = iMerchantIncomeService.generalIncomeByLabel(incomeRequest);
            if( ResponseCodeEnum.SUCCESS.getCode() == response.getCode()){
                if(null != response.getData()){
                    PageInfo<IncomeEquipmentLabelDTO> data = response.getData();
                    //数据处理   获取标签名称，获取标签下级是否可点击
                    List<IncomeEquipmentLabelDTO> list = data.getList();
                    if(list != null && list.size() > 0){
                        //TODO
//                        equipmentLabelService.convertIncomeData(list);
                        data.setList(list);
                    }
                    jsonObject.setData(data);
                }
            }else {
                baseResponse.setCode(Status.STATUS_FAIL);
//                jsonObject.setDescription(MessageUtils.get("common.query.timeout"));
            }
        }catch (Exception e){
            log.error("根据设备标签统计收益异常");
            log.error(e.getMessage(), e);
            baseResponse.setCode(Status.STATUS_FAIL);
//            jsonObject.setDescription(MessageUtils.get("statistics.label.fail"));
        }
        return baseResponse;
    }

    /**
     * 设备实际出币
     * @param pageIndex
     * @param pageSize
     * @param startDate
     * @param endDate
     * @param equipmentValue
     * @return
     */
    @GetMapping("/benefit/equipment/reportCoins")
    public BaseResponse equipmentLabelGeneral(@RequestParam(value = "pageIndex",required = false) Integer pageIndex,
                                              @RequestParam(value = "pageSize",required = false) Integer pageSize,
                                              @RequestParam(value = "startDate",required = false) String startDate,
                                              @RequestParam(value = "endDate",required = false) String endDate,
                                              @RequestParam(value = "equipmentValue",required = false) String equipmentValue) {
        BaseResponse baseResponse = new BaseResponse();
        try {
            EquipmentDTO equipment = equipmentService.getInfoByValue(equipmentValue).getData();
            if (null != equipment) {
                if (org.apache.commons.lang.StringUtils.isBlank(startDate)) {
                    startDate = DateUtil.format(DateUtil.getTodayBegin(), "yyyyMMddHHmmss");
                } else {
                    startDate = startDate.replaceAll("-", "");
                    startDate += "000000";
                }
                if (org.apache.commons.lang.StringUtils.isBlank(endDate)) {
                    endDate = DateUtil.format(DateUtil.getTodayEnd(), "yyyyMMddHHmmss");
                } else {
                    endDate = endDate.replaceAll("-", "");
                    endDate += "235959";
                }
                baseResponse.setData(equipmentCoinsClient.coinsReportPage(pageIndex, pageSize, startDate, endDate, equipment.getUniqueCode()));
                return baseResponse;
            }
            throw new IncomePageException(IncomeConstants.PARAM_ERROR.getCode(), IncomeConstants.PARAM_ERROR.getMessage());
        } catch (Exception e) {
            log.error("设备实际出币查询异常，message：", e.getMessage());
            throw new IncomePageException(IncomeConstants.BUSINESS_ERROR.getCode(), e.getMessage());
        }
    }

    /**
     * 数据业务转换
     *
     * @param page 分页数据
     * @return 设备收益
     */
    private Pagination<IncomeEquipmentResponseDTO> convertIncomeEquipment(PageInfo<IncomeEquipmentResponseDTO> page) {
        Pagination<IncomeEquipmentResponseDTO> response = new Pagination<>();

        List<IncomeEquipmentResponseDTO> responses = page.getList();
        if (!CollectionUtils.isEmpty(responses)) {
            List<Integer> equipmentIds =
                    responses.parallelStream().map(IncomeEquipmentResponseDTO::getEquipmentId).collect(Collectors.toList());
            //TODO
            List<LyyEquipmentAttachVO> attach = null;
//            List<LyyEquipmentAttachVO> attach = equipmentAttachService.selectBusinessType(equipmentIds);
            Map<Integer, Integer> map = ofNullable(attach)
                    .orElse(new ArrayList<>())
                    .parallelStream().collect(Collectors.toMap(LyyEquipmentAttachVO::getLyyEquipmentId,
                            LyyEquipmentAttachVO::getBusinessType,
                            (k1, k2) -> k1));
            //增加返回设备刷脸标识,用来处理盒子关联刷脸-关联对接的经营统计展示
            Map<Integer, Integer> facePayMap = ofNullable(attach)
                    .orElse(new ArrayList<>())
                    .parallelStream().collect(Collectors.toMap(LyyEquipmentAttachVO::getLyyEquipmentId,
                            LyyEquipmentAttachVO::getFacePay,
                            (k1, k2) -> k1));
            responses.stream().filter(ie -> facePayMap.containsKey(ie.getEquipmentId()))
                    .forEach(ie -> {
                        if (facePayMap.get(ie.getEquipmentId())!=null){
                            ie.setFacePayFlag(facePayMap.get(ie.getEquipmentId()));
                        }
                    });
            responses.stream().filter(ie -> map.containsKey(ie.getEquipmentId()))
                    .forEach(ie -> {
                        ie.setBusinessType(map.get(ie.getEquipmentId()));
                    });
        }
        response.setItems(responses);
        response.setMaxPage(page.getPages());
        response.setPageSize(page.getPageSize());
        response.setPage(page.getPageNum());
        response.setTotal((int) page.getTotal());
        return response;
    }


    /**
     * 构建查询参数
     *
     * @param request 请求参数
     * @return 服务器参数
     */
    private MerchantIncomeRequest buildRequest(IncomeRequestDTO request, List<Integer> groups) {
        MerchantIncomeRequest incomeRequest = new MerchantIncomeRequest();
        incomeRequest.setDistributor(getAdOrgIdNotNull().intValue());
        incomeRequest.setGroups(groups);
        incomeRequest.setStart(LocalDate.parse(request.getStartDate(), df));
        incomeRequest.setEnd(LocalDate.parse(request.getEndDate(), df));

        if (StringUtils.isNotBlank(request.getField())) {
            String sortField = Stream.of(IncomeSortField.values()).filter(v -> v.getCode().equals(request.getField()))
                    .findFirst().map(IncomeSortField::getCode).orElse(IncomeSortField.AMOUNT.getCode());
            incomeRequest.setField(sortField);
            incomeRequest.setSort(ofNullable(request.getSortDirection()).filter(StringUtils::isNotBlank).orElse("desc"));
        }
        incomeRequest.setPageSize(ofNullable(request.getPageSize()).orElse(20));
        incomeRequest.setPageIndex(ofNullable(request.getPageIndex()).orElse(1));
        return incomeRequest;
    }

    /**
     * 分页数据转换
     *
     * @param page
     * @return
     */
    private <T extends IncomeResponseDTO> Pagination<T> convertPagination(PageInfo<T> page) {
        Pagination<T> response = new Pagination<>();
        response.setItems(page.getList());
        response.setPageSize(page.getPageSize());
        response.setMaxPage(page.getPages());
        response.setPage(page.getPageNum());
        response.setTotal((int) page.getTotal());

        return response;
    }
}
