package cn.lyy.merchant.controller.statistics;

import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.PageDTO;
import cn.lyy.merchant.dto.ProfitDTO;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.service.statistics.ProfitService;
import cn.lyy.merchant.utils.ValidatorUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import static java.util.Optional.ofNullable;

/**
 * 类描述：数据统计接口
 * <p>
 *
 * <AUTHOR>
 * @since 2020/12/8 11:05
 */
@Slf4j
@RestController
@RequestMapping("/profit")
public class StatisticsController extends BaseController {

    @Autowired
    private ProfitService profitService;

    @PostMapping("/getTotalProfit")
    @AuthorityResource(name = "获取经营统计总计", value = "mb_profile_total", seq = 1, role = "Saas_Merchant", parentValue = "Profit")
    public BaseResponse getTotalProfit(@RequestBody ProfitDTO.OffsetRequest param) {
        AdUserInfoDTO adUserInfoDTO = getCurrentUser();
        param.setUserId(ofNullable(adUserInfoDTO).map(AdUserInfoDTO::getAdUserId).orElse(null));
        param.setUserOrgId(ofNullable(adUserInfoDTO).map(AdUserInfoDTO::getAdOrgId).orElse(null));
        param.setIsApprover(ofNullable(adUserInfoDTO).map(AdUserInfoDTO::getIsApprover).orElse(false));
        /**
         * 校验入参
         */
        String error = ValidatorUtils.validDTO(param);
        if (StringUtils.isNotBlank(error)) {
            throw new BusinessException(error);
        }
        ProfitDTO profitDTO = profitService.getCollectProfit(param);
        return ResponseUtils.success(profitDTO);
    }

    @PostMapping("/getProfit")
    @AuthorityResource(name = "获取经营统计详情", value = "mb_profile_condition", seq = 1, role = "Saas_Merchant", parentValue = "Profit")
    public BaseResponse getProfit(@RequestBody JSONObject param) {
        ProfitDTO.OffsetRequest request = JSON.toJavaObject(param, ProfitDTO.OffsetRequest.class);
        PageDTO pageDTO = JSON.toJavaObject(param, PageDTO.class);
        AdUserInfoDTO adUserInfoDTO = getCurrentUser();
        request.setUserId(ofNullable(adUserInfoDTO).map(AdUserInfoDTO::getAdUserId).orElse(null));
        pageDTO.setUserId(ofNullable(adUserInfoDTO).map(AdUserInfoDTO::getAdUserId).orElse(null));
        request.setUserOrgId(ofNullable(adUserInfoDTO).map(AdUserInfoDTO::getAdOrgId).orElse(null));
        pageDTO.setUserOrgId(ofNullable(adUserInfoDTO).map(AdUserInfoDTO::getAdOrgId).orElse(null));
        request.setIsApprover(ofNullable(adUserInfoDTO).map(AdUserInfoDTO::getIsApprover).orElse(false));
        pageDTO.setIsApprover(ofNullable(adUserInfoDTO).map(AdUserInfoDTO::getIsApprover).orElse(false));
        /**
         * 校验入参
         */
        String error = ValidatorUtils.validDTO(request, pageDTO);
        if (StringUtils.isNotBlank(error)) {
            throw new BusinessException(error);
        }
        log.debug("经营统计详情查询,请求参数request:{},pageDTO:{}", request, pageDTO);
        Object groupProfits = profitService.getProfit(request, pageDTO);
        return success(groupProfits);
    }

    /**
     * 首页获取经营统计数据
     *
     * @return
     */
    @GetMapping("/getProfitOrderDetail")
    @AuthorityResource(name = "首页获取经营统计数据", value = "mb_profile_getProfitOrderDetail", seq = 3, role = "Saas_Merchant", parentValue = "Profit")
    public BaseResponse getProfitOrderDetail() {
        AdUserInfoDTO adUserInfoDTO = getCurrentUser();
        return ResponseUtils.success(profitService.getProfitOrderDetail(adUserInfoDTO));
    }

}
