package cn.lyy.merchant.controller.statistics;

import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.dto.Pagination;
import cn.lyy.base.util.DateUtil;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.lyy_consumption_api.SubsidyQueryDTO;
import cn.lyy.lyy_consumption_api.SubsidyRecordDTO;
import cn.lyy.lyy_consumption_api.microservice.IConsumptionApiQueryService;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.service.remote.ConsumptionQueryService;
import cn.lyy.merchant.util.PageConverter;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 类描述：平台补贴
 * <p>
 *
 * <AUTHOR>
 * @since 2020/12/8 17:20
 */
@Slf4j
@RestController
@RequestMapping("/subsidy")
public class PlatformSubsidyController extends BaseController {

    @Autowired
    private IConsumptionApiQueryService consumptionQueryService;

    /**
     * 补贴记录
     * @param query
     * @param result
     * @return
     */
    @AuthorityResource(name = "获取补贴记录列表", value = "mb_subsidy_list", seq = 1, role = "Saas_Merchant", parentValue = "Profit")
    @PostMapping("/list")
    public BaseResponse<Pagination<SubsidyRecordDTO>> subsidyList(@RequestBody @Valid SubsidyQueryDTO query,
                                                                BindingResult result) {
        if (result.hasErrors()) {
            throw new BusinessException(result.getAllErrors().get(0).getDefaultMessage());
        }
        if (DateUtil.getBetweenDays(query.getStartTime(), query.getEndTime()) > 31) {
            throw new BusinessException("查询跨度不可超过1个月");
        }
        query.setDistributorId(getAdOrgIdNotNull());
        Pagination<SubsidyRecordDTO> page = consumptionQueryService.byCondition(query);
        return ResponseUtils.success(page);
    }
}
