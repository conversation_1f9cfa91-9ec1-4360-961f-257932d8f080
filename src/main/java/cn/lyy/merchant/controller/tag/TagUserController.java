package cn.lyy.merchant.controller.tag;

import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.dto.Status;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.merchant.constants.BusinessExceptionEnums;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.tag.TagQueryDTO;
import cn.lyy.merchant.dto.tag.TagSaveDTO;
import cn.lyy.merchant.dto.tag.TagUpdateNameDTO;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.service.tag.TagUserService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.user.account.infrastructure.constant.TagBusinessTypeEnum;
import com.lyy.user.account.infrastructure.constant.UserMemberSysConstants;
import com.lyy.user.account.infrastructure.user.dto.tag.TagBindUserDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagCountUserQueryDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagStatusDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUnBindUserDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserDetailDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserListDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserQueryDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserSaveDTO;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description:
 * @author: qgw
 * @date on 2021/4/16. @Version: 1.0
 */
@Slf4j
@RestController
@RequestMapping("/rest/tag")
public class TagUserController extends BaseController {

    @Autowired
    private TagUserService tagUserService;

    //private static final String REGEX_CHINA = "[\\u4e00-\\u9fa5]";
    private static final String REGEX_CHECK_TITLE = "([\\u4e00-\\u9fa5]|[a-zA-Z0-9]|\\+|\\-|\\(|\\))*";

    //private static final  Pattern CHECK_CHINESE_CHAR = Pattern.compile(REGEX_CHINA);
    private static final  Pattern CHECK_RULE = Pattern.compile(REGEX_CHECK_TITLE);

    /**
     * 查询标签
     *
     * @param param
     * @return
     */
    @PostMapping("/list")
    @AuthorityResource(name = "查询标签", value = "mb_tag_list", seq = 9, parentValue = "MemberCenter", role = "Saas_Merchant")
    public BaseResponse<Page<TagUserListDTO>> list(@Valid @RequestBody TagQueryDTO param) {
        BaseResponse baseResponse = new BaseResponse();
        try {
            log.debug("查询标签,list:{}", param);
            AdUserInfoDTO currentUser = getCurrentUser();
            TagUserQueryDTO tagUserQueryDTO = new TagUserQueryDTO();
            BeanUtils.copyProperties(param, tagUserQueryDTO);
            tagUserQueryDTO.setMerchantId(currentUser.getAdOrgId());
            tagUserQueryDTO.setTagType(UserMemberSysConstants.TAG_MERCHANT_USER);
            tagUserQueryDTO.setBusinessType(param.getBusinessType());
            tagUserQueryDTO.setCategory(param.getCategory());
            tagUserQueryDTO.setCountSql(false);
            Page<TagUserListDTO> list = tagUserService.list(tagUserQueryDTO, currentUser);
            log.debug("查询标签,result:{}", list.getRecords());
            return ResponseUtils.success(list);

        } catch (BusinessException e) {
            baseResponse.setMessage(e.getMsg());
            baseResponse.setCode(Status.STATUS_500);
        } catch (Exception e) {
            log.error("查询标签异常:", e);
            return ResponseUtils.systemError();
        }
        return baseResponse;
    }
    /**
     * 根据用户查所属标签
     * @param dto
     * @return
     */
    @PostMapping("/listTagByUser")
    @AuthorityResource(name = "根据用户查所属标签", value = "mb_tag_listTagByUser", seq = 20, parentValue = "MemberCenter", role = "Saas_Merchant")
    public BaseResponse<Page<TagUserListDTO>> listTagByUser(@Valid @RequestBody TagUserQueryDTO dto) {
        dto.setMerchantId(getAdOrgIdNotNull());
        log.debug("根据用户查所属标签,dto:{}", dto);
        BaseResponse baseResponse = new BaseResponse();
        try {
            dto.setCountSql(false);
            Page<TagUserListDTO> result = tagUserService.listTagByUser(dto);
            log.debug("根据用户查所属标签,result:{}", result);
            return ResponseUtils.success(result);
        } catch (BusinessException e) {
            baseResponse.setMessage(e.getMsg());
            baseResponse.setCode(Status.STATUS_500);
        } catch (Exception e) {
            log.error("根据用户查所属标签异常:", e);
            return ResponseUtils.systemError();
        }
        return baseResponse;
    }

    /**
     * 根据条件查标签的用户
     * @param dto
     * @return
     */
    @PostMapping("/findByTagId")
    @AuthorityResource(name = "根据条件查标签的用户", value = "mb_tag_findByTagId", seq = 10, parentValue = "MemberCenter", role = "Saas_Merchant")
    public BaseResponse<TagUserDetailDTO> findByTagId(@Valid @RequestBody  TagUserQueryDTO dto) {
        dto.setMerchantId(getAdOrgIdNotNull());
        log.debug("根据条件查标签的用户,dto:{}", dto);
        BaseResponse baseResponse = new BaseResponse();
        try {
            dto.setCountSql(false);
            TagUserDetailDTO result = tagUserService.findByTagId(dto);
            log.debug("根据条件查标签的用户,result:{}", result);
            return ResponseUtils.success(result);
        } catch (BusinessException e) {
            baseResponse.setMessage(e.getMsg());
            baseResponse.setCode(Status.STATUS_500);
        } catch (Exception e) {
            log.error("根据条件查标签的用户异常:", e);
            return ResponseUtils.systemError();
        }
        return baseResponse;
    }

    /**
     * 根据条件查标签的用户数量
     * @param dto
     * @return
     */
    @PostMapping("/countFindByTagId")
    @AuthorityResource(name = "根据条件查标签的用户数量", value = "mb_tag_countFindByTagId", seq = 10, parentValue = "MemberCenter", role = "Saas_Merchant")
    public BaseResponse<Long> countFindByTagId(@Valid @RequestBody  TagCountUserQueryDTO dto) {
        dto.setMerchantId(getAdOrgIdNotNull());
        log.debug("根据条件查标签的用户数量,dto:{}", dto);
        BaseResponse baseResponse = new BaseResponse();
        try {
            Long result = tagUserService.countFindByTagId(dto);
            log.debug("根据条件查标签的用户数量,result:{}", result);
            return ResponseUtils.success(result);
        } catch (BusinessException e) {
            baseResponse.setMessage(e.getMsg());
            baseResponse.setCode(Status.STATUS_500);
        } catch (Exception e) {
            log.error("根据条件查标签的用户数量异常:", e);
        }
        return ResponseUtils.systemError();
    }

    /**
     * 新增或修改用户标签
     *
     * @param param
     * @return
     */
    @PostMapping("/saveOrUpdateTagUser")
    @AuthorityResource(name = "新增或修改用户标签", value = "mb_tag_saveOrUpdateTagUser", seq = 11, parentValue = "MemberCenter", role = "Saas_Merchant")
    public BaseResponse<String> saveOrUpdateTagUser(@Valid @RequestBody TagSaveDTO param) {
        log.debug("新增或修改用户标签,dto:{}", param);
        BaseResponse baseResponse = new BaseResponse();
        try {
            validateTagTitle(param.getName());
            TagUserSaveDTO tagUserSaveDTO = new TagUserSaveDTO();
            BeanUtils.copyProperties(param, tagUserSaveDTO);
            tagUserSaveDTO.setMerchantId(getAdOrgIdNotNull());
            tagUserSaveDTO.setTagType(UserMemberSysConstants.TAG_MERCHANT_USER);
            tagUserSaveDTO.setBusinessType(TagBusinessTypeEnum.NORMAL.getStatus());
            tagUserSaveDTO.setOperatorId(getAdUserIdNotNull());

            return ResponseUtils.success(tagUserService.saveOrUpdateTagUser(tagUserSaveDTO));

        } catch (BusinessException e) {
            baseResponse.setMessage(e.getMsg());
            baseResponse.setCode(Status.STATUS_500);
        } catch (Exception e) {
            log.error("新增或修改用户标签失败:", e);
            return ResponseUtils.systemError();
        }
        return baseResponse;
    }

    private  void validateTagTitle(String name) {
        int length = name.length();
        if (length > 18) {
            throw new BusinessException(BusinessExceptionEnums.TAG_NAME_OVER_LENGTH);
        }
        Matcher checkRule = CHECK_RULE.matcher(name);
        if (!checkRule.matches()) {
            throw new BusinessException(BusinessExceptionEnums.TAG_NAME_HAS_ILLEGAL_CHAR);
        }
    }

    /**
     * 修改标签名称
     *
     * @param dto
     * @return
     */
    @PostMapping("/updateTagName")
    @AuthorityResource(name = "修改标签名称", value = "mb_tag_updateTagName", seq = 12, parentValue = "MemberCenter", role = "Saas_Merchant")
    public BaseResponse<Boolean> updateTagName(@Valid @RequestBody TagUpdateNameDTO dto) {
        log.debug("修改标签名称,dto:{}", dto);
        BaseResponse baseResponse = new BaseResponse();
        try {
            TagDTO tagDTO = new TagDTO();
            BeanUtils.copyProperties(dto,tagDTO);
            tagDTO.setMerchantId(getAdOrgIdNotNull());
            tagDTO.setOperatorId(getAdUserIdNotNull());
            return ResponseUtils.success((tagUserService.updateTagName(tagDTO)));
        } catch (BusinessException e) {
            baseResponse.setMessage(e.getMsg());
            baseResponse.setCode(Status.STATUS_500);
        } catch (Exception e) {
            log.error("修改标签名称失败:", e);
            return ResponseUtils.systemError();
        }
        return baseResponse;
    }

    /**
     * 变更标签状态
     *
     * @param dto
     * @return
     */
    @PostMapping("/changeTagStatus")
    @AuthorityResource(name = "变更标签状态", value = "mb_tag_changeTagStatus", seq = 13, parentValue = "MemberCenter", role = "Saas_Merchant")
    public BaseResponse<Boolean> changeTagStatus(@Valid @RequestBody TagStatusDTO dto) {
        log.debug("变更标签状态,dto:{}", dto);
        BaseResponse baseResponse = new BaseResponse();
        try {
            dto.setOperatorId(getAdUserIdNotNull());
            return ResponseUtils.success((tagUserService.changeTagStatus(dto)));
        } catch (BusinessException e) {
            baseResponse.setMessage(e.getMsg());
            baseResponse.setCode(Status.STATUS_500);
        } catch (Exception e) {
            log.error("变更标签状态失败:", e);
            return ResponseUtils.systemError();
        }
        return baseResponse;
    }

    /**
     * 标签绑定用户
     *
     * @param dto
     * @return
     */
    @PostMapping("/bindUser")
    @AuthorityResource(name = "标签绑定用户", value = "mb_tag_bindUser", seq = 14, parentValue = "MemberCenter", role = "Saas_Merchant")
    public BaseResponse<Boolean> bindUser(@Valid @RequestBody TagBindUserDTO dto) {
        log.debug("标签绑定用户,dto:{}", dto);
        BaseResponse baseResponse = new BaseResponse();
        try {
            dto.setOperatorId(getAdUserIdNotNull());
            dto.setMerchantId(getAdOrgIdNotNull());
            return ResponseUtils.success((tagUserService.bindUser(dto)));
        } catch (BusinessException e) {
            baseResponse.setMessage(e.getMsg());
            baseResponse.setCode(Status.STATUS_500);
        } catch (Exception e) {
            log.error("标签绑定用户失败:", e);
            return ResponseUtils.systemError();
        }
        return baseResponse;
    }

    /**
     * 标签解绑用户
     *
     * @param dto
     * @return
     */
    @PostMapping("/unBindUser")
    @AuthorityResource(name = "标签解绑用户", value = "mb_tag_unBindUser", seq = 15, parentValue = "MemberCenter", role = "Saas_Merchant")
    public BaseResponse<Boolean> unBindUser(@Valid @RequestBody TagUnBindUserDTO dto) {
        log.debug("{} 标签解绑用户,dto:{}",getAdUserIdNotNull(), dto);
        BaseResponse baseResponse = new BaseResponse();
        try {
            if (dto.getId() ==null && CollectionUtils.isEmpty(dto.getTagIds())) {
                log.error("标签解绑用户-标签ID不能为空!");
                return ResponseUtils.error(Status.STATUS_FAIL,"标签ID不能为空");
            }
            if (dto.getChooseAll() == null && CollectionUtils.isEmpty(dto.getUserIds())) {
                log.error("标签解绑用户-需要全选或选中用户");
                return ResponseUtils.error(Status.STATUS_FAIL,"需要全选或选中用户");
            }
            dto.setMerchantId(getAdOrgIdNotNull());
            return ResponseUtils.success((tagUserService.unBindUser(dto)));
        } catch (BusinessException e) {
            baseResponse.setMessage(e.getMsg());
            baseResponse.setCode(Status.STATUS_500);
        } catch (Exception e) {
            log.error("标签解绑用户失败:", e);
            return ResponseUtils.systemError();
        }
        return baseResponse;
    }
}
