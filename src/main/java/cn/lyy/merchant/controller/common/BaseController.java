package cn.lyy.merchant.controller.common;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.base.dto.Status;
import cn.lyy.base.util.WebUtil;
import cn.lyy.merchant.api.service.MerchantUserService;
import cn.lyy.merchant.dto.user.UserExtentDTO;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.exception.SessionTimeoutException;
import cn.lyy.merchant.constants.MerchantSystemConstant;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import cn.lyy.merchant.utils.AESUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

import static java.util.Optional.ofNullable;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class BaseController {

    private final static String HEADER_USERNAME = "username";

    private final static String HEADER_AD_USER = "adUserId";


    @Autowired
    private MerchantUserService merchantUserService;

    protected HttpServletRequest getHttpServletRequest() {
        return WebUtil.getHttpServletRequest();
    }

    /**
     * 获取当前组织id， 不为空
     */
    protected Long getAdOrgIdNotNull() {
        AdUserInfoDTO currentUser = getCurrentUser();
        Long adOrgId =  currentUser.getAdOrgId();
        if(adOrgId == null){
            // 日志输出异常数据导致的 程序报错
            log.warn("adOrgId为空,userName:{},authorityUserId:{}",currentUser.getUserName(),currentUser.getAuthorityUserId());
        }
        return adOrgId;
    }

    /**
     * 获取当前用户id， 不为空
     */
    protected Long getAdUserIdNotNull() {
        return getCurrentUser().getAdUserId();
    }

    /**
     * 获取token
     *
     * @return
     */
    protected String getWebSocketToken() {
        Long adOrgId = getAdOrgIdNotNull();
        Long adUserId = getAdUserIdNotNull();
        String token = MerchantSystemConstant.B_WEB_SOCKET_PREFIX  + adOrgId + ":" + adUserId;
        return token;
    }

    /**
     * 获取商户信息
     *
     * @return
     */
    protected AdUserInfoDTO getCurrentUser() {
        String username = getHttpServletRequest().getHeader(HEADER_USERNAME);
        String adUser = getHttpServletRequest().getHeader(HEADER_AD_USER);
        log.debug("当前登录到用户username:{}, adUser:{}", username, adUser);

        AdUserInfoDTO currentUser = ofNullable(username).filter(org.apache.commons.lang3.StringUtils::isNotEmpty)
                .map(merchantUserService::getUserInfo).filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode())
                .map(BaseResponse::getData).orElseThrow(() -> new SessionTimeoutException("未加载到用户信息"));
        ofNullable(adUser).filter(StringUtils::isNotEmpty).map(Long::parseLong).ifPresent(currentUser::setAuthorityUserId);
        return currentUser;
    }

    /**
     * 获取商户信息
     *
     * @return
     */
    protected UserExtentDTO getCurrentUserExtend() {
        String username = getHttpServletRequest().getHeader(HEADER_USERNAME);
        String adUser = getHttpServletRequest().getHeader(HEADER_AD_USER);
        log.debug("当前登录到用户username:{}, adUser:{}", username, adUser);

        UserExtentDTO currentUser = ofNullable(username).filter(org.apache.commons.lang3.StringUtils::isNotEmpty)
                .map(merchantUserService::getUserInfoWithExtent).filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode())
                .map(BaseResponse::getData).orElseThrow(() -> new SessionTimeoutException("未加载到用户信息"));
        ofNullable(adUser).filter(StringUtils::isNotEmpty).map(Long::parseLong).ifPresent(currentUser::setAuthorityUserId);
        return currentUser;
    }

    /**
     * 获取当前商户id
     *
     * @return
     */
    protected long currentUserId() {
        AdUserInfoDTO currentUser = getCurrentUser();
        return currentUser == null ? 0 : currentUser.getAdUserId();
    }

    /**
     * 获取当前商户组织id
     *
     * @return
     */
    protected long currentUserOrgId() {
        AdUserInfoDTO currentUser = getCurrentUser();
        return currentUser == null ? 0 : currentUser.getAdOrgId();
    }

    /**
     * 获取当前商户权限用户id
     *
     * @return
     */
    protected long currentAuthorityUserId() {
        String adUser = getHttpServletRequest().getHeader(HEADER_AD_USER);
        return StringUtils.isNotBlank(adUser) ? Long.parseLong(adUser) : 0;
    }

    protected Long currentFactoryId() {
        RequestAttributes requestAttributes = RequestContextHolder.currentRequestAttributes();

        Long factoryId = null;
        try {
            //加密后的内容
            String factoryIdEncStr = (String) requestAttributes.getAttribute("factoryId",
                    RequestAttributes.SCOPE_SESSION);
            factoryId = decryptFactoryId(factoryIdEncStr);
        } catch (Exception e) {
        }

        return factoryId;
    }

    protected Long decryptFactoryId(String encStr) throws Exception {
        String factoryIdStr = AESUtils.decrypt(new String(Base64.decodeBase64(encStr.getBytes("UTF-8"))), "fgbX1^3*vN"
        ); //解密
        log.debug("获取currentFactoryId: {}", factoryIdStr);
        return Long.parseLong(factoryIdStr);
    }


    protected boolean validParameter(String parameter) {
        return StringUtils.isNotEmpty(parameter) && !"null".equals(parameter) && !"undefined".equals(parameter);
    }

    protected <T> BaseResponse<T> success() {
        BaseResponse<T> response = new BaseResponse<>();
        response.setCode(ResponseCodeEnum.SUCCESS.getCode());
        return response;
    }

    protected <T> BaseResponse<T> success(T data) {
        BaseResponse<T> response = success();
        response.setData(data);
        return response;
    }

    protected BaseResponse error(Integer code, String desc) {
        if (Objects.isNull(code)) {
            code = Status.STATUS_FAIL;
        }
        BaseResponse<Void> error = new BaseResponse<>();
        error.setCode(code);
        error.setMessage(desc);
        return error;
    }

    /**
     * 通用的校验参数
     * @param result
     */
    protected void checkParameter(BindingResult result) {
        if (result.hasErrors()) {
            throw new BusinessException(result.getAllErrors().get(0).getDefaultMessage());
        }
    }
}