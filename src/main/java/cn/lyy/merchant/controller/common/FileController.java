package cn.lyy.merchant.controller.common;

import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.dto.JsonObject;
import cn.lyy.base.utils.ResponseUtils;
import com.lyy.oss.service.impl.AliyunOss;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * 类描述:文件上传
 * <p>
 *
 * <AUTHOR>
 * @since 2020/12/10 09:59
 */
@Slf4j
@RestController
@RequestMapping("/file")
public class FileController {


    @AuthorityResource(name = "文件上传", value = "mb_file_upload", seq = 1, role = "Saas_Merchant", parentValue = "MerchantShop")
    @PostMapping("upload")
    public BaseResponse<String> fileUpload(@RequestParam(value = "file") MultipartFile file,
                                   @RequestParam(value = "fileSuffix") String fileSuffix){
        JsonObject ret = new JsonObject();
        String filePath = null;
        try {
            filePath = AliyunOss.upload(AliyunOss.buildFileName("merchant/", ".".concat(fileSuffix)), file.getInputStream());

            return ResponseUtils.success(filePath);
        } catch (IOException e) {
            log.error("saas file upload error: {}" , e.getMessage());
            return ResponseUtils.systemError();
        }

    }

    private String getFileSuffix(String fileName){
        return fileName.substring(fileName.lastIndexOf("."));
    }
}
