package cn.lyy.merchant.controller.common;

import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import com.alibaba.fastjson.JSONObject;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <p>Title:saas2</p>
 * <p>Desc: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020/11/16
 */
public abstract class UserModelContrller extends BaseController {

    @ModelAttribute
    public JSONObject setParam(@RequestBody(required = false) JSONObject param, Model model){
        if (param == null) {
            return null;
        }
        AdUserInfoDTO currentUser = getCurrentUser();
        param.put("userId" , currentUser == null ? null : currentUser.getAdUserId());
        param.put("userOrgId", currentUser == null ? null : currentUser.getAdOrgId());
        param.put("adOrgId", currentUser == null ? null : currentUser.getAdOrgId());
        param.put("adUserId", currentUser == null ? null : currentUser.getAdUserId());
        param.put("authorityUserId", currentUser == null ? null : currentUser.getAuthorityUserId());
        param.put("factoryOrgId", currentFactoryId());
        param.put("isApprover", currentUser.getIsApprover());
        model.addAttribute("param", param);
        return param;
    }

}
