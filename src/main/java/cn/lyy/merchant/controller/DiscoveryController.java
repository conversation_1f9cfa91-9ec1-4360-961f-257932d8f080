package cn.lyy.merchant.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
public class DiscoveryController {

    private static final Logger logger = LoggerFactory.getLogger(DiscoveryController.class);

    @RequestMapping("/discovery/test")
    public String test(String a, String b) {
        logger.debug("param a -> {},b-> {}", a, b);
        return "D1";
    }
}
