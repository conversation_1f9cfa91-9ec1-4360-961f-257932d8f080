package cn.lyy.merchant.controller.commodity;

import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.request.BatchDiscountRuleSaveDTO;
import cn.lyy.merchant.dto.request.DiscountRuleSaveDTO;
import cn.lyy.merchant.dto.request.RestroreDefaultDTO;
import cn.lyy.merchant.dto.request.RuleDelDTO;
import cn.lyy.merchant.dto.response.DiscountRuleDTO;
import cn.lyy.merchant.service.commodity.DiscountService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/discount/rule")
public class DiscountController extends BaseController {


    @Resource
    private DiscountService discountService;

    /**
     * 保存优惠规则
     * @param discountRuleSaveDTO
     * @return
     */
    @PostMapping(value = "/saveOrUpdate")
    @AuthorityResource(name = "保存或修改优惠规则",value = "mb_saveOrUpdateDiscountRule",role = "Saas_Merchant",parentValue = "Feerule")
    public BaseResponse<Long> saveOrUpdate(@RequestBody @Validated DiscountRuleSaveDTO discountRuleSaveDTO){
        log.info("DiscountController.saveOrUpdate");
        Long detailId = discountService.saveOrUpdate(discountRuleSaveDTO,getCurrentUser().getAdOrgId(),getCurrentUser().getAdUserId());
        return ResponseUtils.success(detailId);
    }

    /**
     * 保存优惠规则
     * @param batchDiscountRuleSaveDTO
     * @return
     */
    @PostMapping(value = "/batchSaveOrUpdate",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @AuthorityResource(name = "保存或修改优惠规则",value = "mb_batchSaveOrUpdateDiscount",role = "Saas_Merchant",parentValue = "Feerule")
    public BaseResponse batchSaveOrUpdate(@RequestBody @Validated BatchDiscountRuleSaveDTO batchDiscountRuleSaveDTO){
        log.info("DiscountController.saveOrUpdate.param:{}", JSON.toJSONString(batchDiscountRuleSaveDTO));
        discountService.batchSaveOrUpdate(batchDiscountRuleSaveDTO,getCurrentUser().getAdOrgId(),getCurrentUser().getAdUserId());
        return ResponseUtils.success();
    }

    /**
     * 删除优惠规则
     * @param ruleDelDTO
     * @return
     */
    @PostMapping(value = "/del")
    @AuthorityResource(name = "删除优惠规则",value = "mb_deleteDiscountRule",role = "Saas_Merchant",parentValue = "Feerule")
    public  BaseResponse delDiscount(@RequestBody @Validated RuleDelDTO ruleDelDTO){
        log.info("DiscountController.param:{}",JSON.toJSONString(ruleDelDTO));
        discountService.delDiscount(ruleDelDTO.getDetailId(),ruleDelDTO.getGroupId(),getCurrentUser().getAdOrgId());
        return ResponseUtils.success();
    }


    /**
     * 获取场地的优惠规则
     * @return
     */
    @GetMapping(value = "/getRuleByGroup")
    @AuthorityResource(name="获取场地的优惠规则",value = "mb_getRuleByGroup",role="Saas_Merchant",parentValue = "Feerule")
    public BaseResponse<DiscountRuleDTO> getDiscountRuleByGroup(@RequestParam(required = false) Long groupId,@RequestParam Long equipmentTypeId) {
        DiscountRuleDTO discountRuleDTO =discountService.getDiscountRuleByGroup(groupId,equipmentTypeId,getCurrentUser().getAdOrgId());
        return ResponseUtils.success(discountRuleDTO);
    }

    @PostMapping(value = "/restoreDefault",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @AuthorityResource(name="恢复场地默认的优惠规则",value = "mb_restoreDefault",role = "Saas_Merchant",parentValue = "Feerule")
    public BaseResponse restoreDefault(@RequestBody @Validated RestroreDefaultDTO restroreDefaultDTO){
        log.info("恢复场地充值优惠，参数:{}",JSON.toJSONString(restroreDefaultDTO));
        discountService.restoreDefault(restroreDefaultDTO,this.getCurrentUser().getAdOrgId(),this.getCurrentUser().getAdUserId());
        return ResponseUtils.success();
    }
}
