package cn.lyy.merchant.controller.commodity;

import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.equipment.dto.EquipmentAttachDTO;
import cn.lyy.equipment.dto.EquipmentInfoDTO;
import cn.lyy.equipment.dto.equipment.EquipmentTypeDTO;
import cn.lyy.equipment.service.EquipmentService;
import cn.lyy.equipment.service.IEquipmentAttachService;
import cn.lyy.equipment.service.IEquipmentFuncQueueService;
import cn.lyy.equipment.service.IEquipmentTypeService;
import cn.lyy.merchant.constants.BusinessExceptionEnums;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.equipment.EquipmentChargingTimeRuleDTO;
import cn.lyy.merchant.dto.request.*;
import cn.lyy.merchant.dto.response.ListFeeModeDTO;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.service.commodity.FeeRuleService;
import cn.lyy.tools.equipment.LyyConstant;
import com.alibaba.fastjson.JSON;
import com.lyy.commodity.rpc.constants.CategoryEnum;
import com.lyy.commodity.rpc.dto.request.SetUseDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.groups.Default;
import java.util.List;

/**
 * 类描述：计费规则
 * <p>
 *
 * <AUTHOR>
 * @since 2020/11/18 16:21
 */
@Slf4j
@RestController
@RequestMapping("/fee/rule")
public class FeeRuleController extends BaseController {

    @Autowired
    private FeeRuleService feeRuleService;

    @Autowired
    private EquipmentService equipmentService;

    @Resource
    private IEquipmentTypeService equipmentTypeService;

    @Resource
    private IEquipmentAttachService equipmentAttachService;

    @Resource
    private IEquipmentFuncQueueService iEquipmentFuncQueueService;

    @Value("${cdz.groupService.intercept:true}")
    private Boolean groupServiceIntercept;


    /**
     * 根据设备类型和场地查询当前已设置的计费规则
     * @param equipmentTypeId 设备类型id
     * @param groupId 场地id
     * @return
     */
    @GetMapping("/list/by/group/equipment/type")
    @AuthorityResource(name = "注册时查询历史计费规则", value="mb_b_group_fee_rule", role = "Saas_Merchant", parentValue = "Equipment")
    public BaseResponse<List<FeeCommodityDTO>> listByGroupAndEquipmentType(@RequestParam("groupId") Long groupId,
                                                                           @RequestParam("equipmentTypeId") Long equipmentTypeId,
                                                                           @RequestParam(required = false, value = "classifyCode") String classifyCode) {
        return ResponseUtils.success(feeRuleService.listByCondition(getCurrentUser().getAdOrgId(), groupId,
                equipmentTypeId, null, CategoryEnum.DEVICE_SERVICE.getCode(), classifyCode));
    }

    /**
     * 根据设备查询当前已设置的计费规则
     * @return
     */
    @GetMapping(value = "/list/by/equipment")
    @AuthorityResource(name = "查询设备的计费规则", value="mb_b_equipment_fee_rule", role = "Saas_Merchant", parentValue = "Plugin-RemoteCoin")
    public BaseResponse<List<FeeCommodityDTO>> listEquipment(@RequestParam("equipmentValue") String equipmentValue,
                                                             @RequestParam(value = "equipmentType",required = false) String equipmentType,
                                                             @RequestParam(value = "category",required = false) String category) {
        EquipmentInfoDTO equipmentInfoDTO = equipmentService.getEquipmentInfoByValue(equipmentValue).getData();
        if (equipmentInfoDTO == null) {
            throw new BusinessException(BusinessExceptionEnums.DEVICE_NOT_EXISTS);
        }
        //充电桩不支持操作1.0套餐
        if (!isGray(equipmentInfoDTO)) {
            throw new BusinessException(BusinessExceptionEnums.NOT_SUPPORT_GROUP_SERVICE);
        }
        Long equipmentTypeId = equipmentInfoDTO.getEquipmentTypeId().longValue();
        // 执行查询设备类型，此处用来兼容 加液机查询套餐
        if(!StringUtils.isEmpty(equipmentType)){
            EquipmentTypeDTO equipmentTypeDTO = equipmentTypeService.getByValue(equipmentType).getData();
            equipmentTypeId = equipmentTypeDTO.getEquipmentTypeId();
        }
        if (StringUtils.isEmpty(category)) {
            category = CategoryEnum.DEVICE_SERVICE.getCode();
        }
        List<FeeCommodityDTO> list = feeRuleService.listByCondition(getCurrentUser().getAdOrgId(),
                equipmentInfoDTO.getEquipmentGroupId().longValue(),
                equipmentTypeId,
                equipmentInfoDTO.getEquipmentId().longValue(),
                category,null);
        // 规则设置场地信息
        list.forEach(feeCommodityDTO -> {
            feeCommodityDTO.setGroupId(equipmentInfoDTO.getEquipmentGroupId().longValue());
        });
        return ResponseUtils.success(list);
    }

    @PostMapping(value = "/saveOrUpdate")
    @AuthorityResource(name = "保存或修改计费规则",value = "mb_saveOrUpdateGrade",role = "Saas_Merchant",parentValue = "Feerule")
    public  BaseResponse saveOrUpdate(@RequestBody @Validated FeeRuleSaveReqDTO feeRuleSaveReqDTO){
        log.info("保存或修改计费规则:param:{}",JSON.toJSONString(feeRuleSaveReqDTO));
        EquipmentInfoDTO equipmentInfoDTO = equipmentService.getEquipmentInfoById(feeRuleSaveReqDTO.getEquipmentId()).getData();
        //充电桩不支持操作1.0套餐
        if (!isGray(equipmentInfoDTO)) {
            throw new BusinessException(BusinessExceptionEnums.NOT_SUPPORT_GROUP_SERVICE);
        }
        feeRuleService.saveOrUpdate(feeRuleSaveReqDTO,getCurrentUser().getAdOrgId(),getCurrentUser().getAdUserId());
        return ResponseUtils.success();
    }

    /**
     * 批量保存或修改计费规则
     * @param batchFeeRuleSaveDTO
     * @return
     */
    @PostMapping(value = "/batchSaveOrUpdate")
    @AuthorityResource(name = "批量保存或修改计费规则",value = "mb_batchSaveOrUpdateFeeRule",role = "Saas_Merchant",parentValue = "Feerule")
    public  BaseResponse batchSaveOrUpdate(@RequestBody @Validated BatchFeeRuleSaveDTO batchFeeRuleSaveDTO){
        log.info("批量保存或修改计费规则.param:{}",JSON.toJSONString(batchFeeRuleSaveDTO));
        feeRuleService.batchSaveOrUpdate(batchFeeRuleSaveDTO,getCurrentUser().getAdOrgId(),getCurrentUser().getAdUserId());
        return ResponseUtils.success();
    }


    @PostMapping(value = "/del")
    @AuthorityResource(name = "删除计费规则",value = "mb_deleteFeeRule",role = "Saas_Merchant",parentValue = "Feerule")
    public  BaseResponse del(@RequestBody @Validated(value = {Default.class,GroupA.class}) RuleDelDTO ruleDelDTO){
        log.info("FeeRuleController.del");
        EquipmentInfoDTO equipmentInfoDTO = equipmentService.getEquipmentInfoByValue(ruleDelDTO.getEquipmentValue()).getData();
        //充电桩不支持操作1.0套餐
        if (!isGray(equipmentInfoDTO)) {
            throw new BusinessException(BusinessExceptionEnums.NOT_SUPPORT_GROUP_SERVICE);
        }
        feeRuleService.delFee(ruleDelDTO,getCurrentUser().getAdOrgId());
        return ResponseUtils.success();
    }


    @GetMapping(value = "/listFeeMode")
    @AuthorityResource(name = "查询计费模式",value = "mb_listFeeMode",role = "Saas_Merchant",parentValue = "Feerule")
    public BaseResponse<ListFeeModeDTO> listFeeMode(String equipmentCode){
        ListFeeModeDTO listFeeModeDTO = feeRuleService.listFeeMode(equipmentCode,this.getCurrentUser().getAdOrgId());
        return ResponseUtils.success(listFeeModeDTO);
    }

    @PostMapping(value = "/setPro")
    @AuthorityResource(name="设置显示属性",value = "mb_setPro",role = "Saas_Merchant",parentValue = "Feerule")
    public BaseResponse setPro(@RequestBody SetProDTO setProDTO){
        log.info("设置显示属性,参数:{}", JSON.toJSONString(setProDTO));
        feeRuleService.setPro(setProDTO,this.getCurrentUser().getAdOrgId());
        return ResponseUtils.success();

    }

    @PostMapping(value = "/setUse")
    @AuthorityResource(name="设置是否启用计费规则",value = "mb_set_use",role = "Saas_Merchant",parentValue = "Feerule")
    public BaseResponse setUse(@RequestBody SetUseDTO setUseDTO){
        log.info("设置是否启用计费规则,参数:{}", JSON.toJSONString(setUseDTO));
        feeRuleService.setFeeFuleUse(setUseDTO,this.getCurrentUser().getAdOrgId());
        return ResponseUtils.success();

    }


    @PostMapping(value = "/updateFeeMode")
    @AuthorityResource(name="更新计费方式",value = "mb_updateFeeMode",role = "Saas_Merchant",parentValue = "Feerule")
    public BaseResponse updateFeeMode(@RequestBody UpdateFeeModeDTO updateFeeModeDTO){
        log.info("更新计费方式，参数：{}",JSON.toJSONString(updateFeeModeDTO));
        feeRuleService.updateFeeMode(updateFeeModeDTO,this.getCurrentUser().getAdOrgId());
        return ResponseUtils.success();
    }


    @GetMapping(value = "/getMainBoardDefaultFeeRule")
    @AuthorityResource(name="获取主板默认的计费规则",value = "mb_get_mainboard_fee_rule",role = "Saas_Merchant",parentValue = "Feerule")
    public BaseResponse<List<FeeCommodityDTO>> getMainBoardDefaultFeeRule(@RequestParam Long productId,
                                                                           @RequestParam String equipmentTypeValue){
        log.info("getMainBoardDefaultFeeRule:参数：{},{}",productId,equipmentTypeValue);
        List<FeeCommodityDTO> list = feeRuleService.getDefaultFeeRule(productId,equipmentTypeValue);
        return ResponseUtils.success(list);
    }


    /**
     * 保存计费规则
     * @param timeRuleDTO
     * @return
     */
    @PostMapping("/saveRule")
    @AuthorityResource(name="保存计费规则",value = "mb_save_rule",role = "Saas_Merchant",parentValue = "Feerule")
    public BaseResponse<Boolean> saveRule(@RequestBody EquipmentChargingTimeRuleDTO timeRuleDTO){
        log.debug("保存计费规则 param:{}", timeRuleDTO);
        return ResponseUtils.success(feeRuleService.save(timeRuleDTO));
    }

    /**
     * 获取计费规则
     * @param equipmentGroupId
     * @param equipmentValue
     * @return
     */
    @GetMapping("/getRule")
    @AuthorityResource(name="获取计费规则",value = "mb_get_rule",role = "Saas_Merchant",parentValue = "Feerule")
    public BaseResponse<EquipmentChargingTimeRuleDTO> getRule(@RequestParam("equipmentGroupId") Long equipmentGroupId, @RequestParam("equipmentValue") String equipmentValue){
        return ResponseUtils.success(feeRuleService.getRule(equipmentGroupId,equipmentValue));
    }


    private Boolean isGray (EquipmentInfoDTO equipmentInfoDTO) {
        if (groupServiceIntercept) {
            if (1000078 == equipmentInfoDTO.getEquipmentTypeId()) {
                BaseResponse<EquipmentAttachDTO> baseResponse = equipmentAttachService.getAttach(equipmentInfoDTO.getUniqueCode());
                if (ResponseCodeEnum.SUCCESS.getCode() == baseResponse.getCode() && null != baseResponse.getData()) {
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(baseResponse.getData().getRegisterSystem())) {
                        if ("1.0".equals(baseResponse.getData().getRegisterSystem())) {
                            return false;
                        }
                    } else {
                        BaseResponse<Boolean> booleanBaseResponse = iEquipmentFuncQueueService.grayEquipment(equipmentInfoDTO.getUniqueCode());
                        if (ResponseCodeEnum.SUCCESS.getCode() == booleanBaseResponse.getCode() && null != booleanBaseResponse.getData() && !booleanBaseResponse.getData()) {
                            return false;
                        }
                    }
                }
            } else if (LyyConstant.YXJ_TYPE_ID == equipmentInfoDTO.getEquipmentTypeId()) {
                BaseResponse<Boolean> booleanBaseResponse = iEquipmentFuncQueueService.grayEquipment(equipmentInfoDTO.getUniqueCode());
                if (ResponseCodeEnum.SUCCESS.getCode() == booleanBaseResponse.getCode() && null != booleanBaseResponse.getData() && !booleanBaseResponse.getData()) {
                    return false;
                }
            }
        }
        return true;
    }
}
