package cn.lyy.merchant.controller.tiktok;

import static java.util.Optional.ofNullable;
import static java.util.Optional.of;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.marketing.api.service.TiktokActivityClient;
import cn.lyy.marketing.dto.statistics.TikTokRedeemRecordItemDTO;
import cn.lyy.marketing.dto.statistics.TikTokRedeemRecordQuery;
import cn.lyy.marketing.dto.statistics.TikTokRedeemSummaryDTO;
import cn.lyy.marketing.dto.statistics.TikTokRedeemSummaryItemDTO;
import cn.lyy.marketing.dto.statistics.TikTokRedeemSummaryQuery;
import cn.lyy.merchant.api.service.EquipmentTypeClient;
import cn.lyy.merchant.api.service.MerchantGroupService;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.equipment.EquipmentTypeDTO;
import cn.lyy.merchant.dto.merchant.request.MerchantGroupRequest;
import cn.lyy.merchant.dto.merchant.response.MerchantGroupDTO;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import cn.lyy.merchant.utils.RemoteResponseUtils;
import com.github.pagehelper.PageInfo;
import com.lyy.starter.common.resp.RespBodyUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PostConstruct;

/**
 * @date: 2023/5/22
 * @author: YL
 */
@Api(tags = "商家抖音核销记录")
@RestController
@RequestMapping("/rest/tiktok/redeem-record")
@Slf4j
public class TikTokRedeemRecordController extends BaseController {

    @Autowired
    private TiktokActivityClient tiktokActivityClient;

    @Autowired
    private MerchantGroupService merchantGroupService;

    @Autowired
    private EquipmentTypeClient equipmentTypeClient;

    private static final HashMap<String,EquipmentTypeDTO> EQUIPMENT_TYPE_VALUE_MAP = new HashMap<>();
    private static final Integer WWJ_DBJ =1;
    private static final Integer XCJ =2;

    /**
     * 抖音核销支持品类
     */
    @Value("#{'${merchant.tiktok-equipment-types:WWJ,DBJ,XCJ}'.split(',')}")
    private List<String> tikTokSupportEquipmentTypeList;

    @PostConstruct
    public void initEquipmentMap() {
        List<EquipmentTypeDTO> allEquipmentType = ofNullable(
                RespBodyUtil.getData(equipmentTypeClient.findByValues(tikTokSupportEquipmentTypeList)))
                .orElse(new ArrayList<>());
        if (CollUtil.isNotEmpty(allEquipmentType)) {
            allEquipmentType.forEach(type -> EQUIPMENT_TYPE_VALUE_MAP.put(type.getValue(), type));
        }
        log.info("抖音核销支持品类tikTokSupportEquipmentTypeList:{}", tikTokSupportEquipmentTypeList);
        log.info("抖音核销支持品类:{}", allEquipmentType);
    }

    @ApiOperation(value = "核销统计汇总")
    @PostMapping({"/tiktokActivity/redeem/summary"})
    public BaseResponse<TikTokRedeemSummaryDTO> queryRedeemSummary(@RequestBody TikTokRedeemSummaryQuery query) {
        query.setDistributorId(getAdOrgIdNotNull());
        AdUserInfoDTO currentUser = getCurrentUser();
        query.setAdUserId(getAdUserIdNotNull());
        query.setMainAccount(currentUser.getIsApprover());
        fillEquipmentTypeIdList(query);
        return tiktokActivityClient.queryRedeemSummary(query);
    }

    private void fillEquipmentTypeIdList(TikTokRedeemSummaryQuery query) {
        log.debug("设备类型queryEquipType：{}",query.getQueryEquipType());
        if (Objects.isNull(query.getQueryEquipType())) {
            return;
        }
        if (EQUIPMENT_TYPE_VALUE_MAP.isEmpty()) {
            initEquipmentMap();
        }
        if (Objects.equals(WWJ_DBJ, query.getQueryEquipType())) {
            Long wwjEquipTypeId = ofNullable(EQUIPMENT_TYPE_VALUE_MAP.get("WWJ")).map(EquipmentTypeDTO::getId).orElse(1000001L);
            Long dbjEquipTypeId = ofNullable(EQUIPMENT_TYPE_VALUE_MAP.get("DBJ")).map(EquipmentTypeDTO::getId).orElse(1000058L);
            query.setEquipmentTypeIds(new ArrayList<>(Arrays.asList(wwjEquipTypeId, dbjEquipTypeId)));
            return;
        }
        if (Objects.equals(XCJ, query.getQueryEquipType())) {
            query.setEquipmentTypeIds(Collections.singletonList(Optional.ofNullable(EQUIPMENT_TYPE_VALUE_MAP.get("XCJ")).map(EquipmentTypeDTO::getId).orElse(1001093L)));
            return;
        }
        log.error("未知设备类型queryEquipType：{}",query.getQueryEquipType());
    }

    @ApiOperation("核销汇总分页")
    @PostMapping({"/tiktokActivity/redeem/summary-item"})
    public BaseResponse<PageInfo<TikTokRedeemSummaryItemDTO>> queryRedeemSummaryItemPage(@RequestBody TikTokRedeemSummaryQuery query) {
        query.setDistributorId(getAdOrgIdNotNull());
        AdUserInfoDTO currentUser = getCurrentUser();
        query.setAdUserId(getAdUserIdNotNull());
        query.setMainAccount(currentUser.getIsApprover());
        fillEquipmentTypeIdList(query);
        return tiktokActivityClient.queryRedeemSummaryItemPage(query);
    }

    @ApiOperation("核销记录")
    @PostMapping({"/tiktokActivity/redeem/record"})
    public BaseResponse<PageInfo<TikTokRedeemRecordItemDTO>> queryRedeemRecordPage(@RequestBody TikTokRedeemRecordQuery query) {
        query.setDistributorId(getAdOrgIdNotNull());
        fillQuery(query);
        AdUserInfoDTO currentUser = getCurrentUser();
        query.setAdUserId(getAdUserIdNotNull());
        query.setMainAccount(currentUser.getIsApprover());
        BaseResponse<PageInfo<TikTokRedeemRecordItemDTO>> response = tiktokActivityClient.queryRedeemRecordPage(query);
        PageInfo<TikTokRedeemRecordItemDTO> data = RemoteResponseUtils.getData(response);
        fillResult(data);

        return response;
    }

    private void fillResult(PageInfo<TikTokRedeemRecordItemDTO> data) {
        if (Objects.isNull(data) || CollUtil.isEmpty(data.getList())) {
            return;
        }

        List<Long> typeIds = data.getList().stream().map(TikTokRedeemRecordItemDTO::getEquipmentTypeId).distinct()
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(typeIds)) {
            List<EquipmentTypeDTO> typeDTOList = RemoteResponseUtils.getCommonData(equipmentTypeClient.findByTypeIds(typeIds));
            if (CollUtil.isNotEmpty(typeDTOList)) {
                Map<Long, EquipmentTypeDTO> map = typeDTOList.stream()
                        .collect(Collectors.toMap(EquipmentTypeDTO::getId, Function.identity(), (v1, v2) -> v1));
                data.getList().forEach(o ->
                        o.setEquipmentTypeName(ofNullable(map.get(o.getEquipmentTypeId())).map(EquipmentTypeDTO::getName).orElse("")));
            }
        }

        of(data.getList())
                .map(o -> o.stream().map(TikTokRedeemRecordItemDTO::getGroupId).distinct().collect(Collectors.toList()))
                .filter(CollUtil::isNotEmpty)
                .map(o -> {
                    MerchantGroupRequest request = new MerchantGroupRequest();
                    request.setDistributor(getAdOrgIdNotNull());
                    request.setGroups(o);

                    return RemoteResponseUtils.getData(merchantGroupService.listGroup(request));
                })
                .filter(CollUtil::isNotEmpty)
                .map(o -> o.stream().collect(Collectors.toMap(MerchantGroupDTO::getEquipmentGroupId, Function.identity(), (v1, v2) -> v1)))
                .ifPresent(map -> data.getList().forEach(o -> o
                        .setGroupName(ofNullable(map.get(o.getGroupId())).map(MerchantGroupDTO::getName).orElse(""))));


    }

    private void fillQuery(TikTokRedeemRecordQuery query) {
        if (CharSequenceUtil.isBlank(query.getQuery())) {
            return;
        }

        MerchantGroupRequest request = new MerchantGroupRequest();
        request.setDistributor(getAdOrgIdNotNull());
//        request.setIsActive(1);
        request.setGroupName(query.getQuery());

        BaseResponse<List<MerchantGroupDTO>> listBaseResponse = merchantGroupService.listGroup(request);
        List<Long> fuzzyGroupIds = ofNullable(RemoteResponseUtils.getData(listBaseResponse))
                .filter(CollUtil::isNotEmpty)
                .map(o -> o.stream().map(MerchantGroupDTO::getEquipmentGroupId).collect(Collectors.toList()))
                .orElse(Collections.emptyList());

        query.setFuzzyGroupIds(fuzzyGroupIds);
    }
}
