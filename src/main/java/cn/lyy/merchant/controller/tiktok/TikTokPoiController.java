package cn.lyy.merchant.controller.tiktok;


import cn.hutool.core.collection.CollUtil;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.marketing.api.service.TiktokActivityClient;
import cn.lyy.marketing.dto.tiktok.MerchantTikTokAccountDTO;
import cn.lyy.marketing.dto.tiktok.MerchantTikTokPoiAssociateDTO;
import cn.lyy.marketing.dto.tiktok.MerchantTikTokPoiDetailDTO;
import cn.lyy.marketing.dto.tiktok.MerchantTikTokPoiInfoDTO;
import cn.lyy.marketing.dto.tiktok.TikTokPoiGroupItemDTO;
import cn.lyy.marketing.dto.tiktok.TikTokPoiQuery;
import cn.lyy.merchant.api.service.MerchantGroupService;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.merchant.request.MerchantGroupRequest;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import cn.lyy.merchant.utils.RemoteResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @date: 2023/5/22
 * @author: YL
 */
@Api(tags = "抖音商家门店")
@RestController
@RequestMapping("/rest/tiktok/poi")
@Slf4j
public class TikTokPoiController extends BaseController {

    @Autowired
    private TiktokActivityClient tiktokActivityClient;

    @Autowired
    private MerchantGroupService merchantGroupService;

    @ApiOperation("获取商家关联的抖音商户列表")
    @GetMapping({"/merchant/accounts"})
    public BaseResponse<List<MerchantTikTokAccountDTO>> merchantTiktokAccounts() {
        return tiktokActivityClient.merchantTiktokAccounts(getAdOrgIdNotNull());
    }

    @ApiOperation("获取商家抖音门店列表")
    @PostMapping({"/merchant/pois"})
    public BaseResponse<List<MerchantTikTokPoiInfoDTO>> merchantPoiList(@RequestBody TikTokPoiQuery query) {
        query.setDistributorId(getAdOrgIdNotNull());
        AdUserInfoDTO currentUser = getCurrentUser();
        query.setAdUserId(getAdUserIdNotNull());
        query.setMainAccount(currentUser.getIsApprover());
        return tiktokActivityClient.merchantPoiList(query);
    }

    @ApiOperation("抖音门店详情")
    @GetMapping({"/merchant/poi-detail"})
    public BaseResponse<MerchantTikTokPoiDetailDTO> merchantPoiDetail(@RequestParam("poiId") String poiId) {
        BaseResponse<MerchantTikTokPoiDetailDTO> response = tiktokActivityClient.merchantPoiDetail(getAdOrgIdNotNull(), poiId);
        MerchantTikTokPoiDetailDTO data = RemoteResponseUtils.getData(response);

        List<TikTokPoiGroupItemDTO> groups = Optional.ofNullable(data)
                .map(MerchantTikTokPoiDetailDTO::getGroupIds)
                .filter(CollUtil::isNotEmpty)
                .map(ids -> {
                    MerchantGroupRequest request = new MerchantGroupRequest();
                    request.setDistributor(getAdOrgIdNotNull());
                    request.setGroups(ids);
                    request.setIsActive(1);

                    return Optional.ofNullable(RemoteResponseUtils.getData(merchantGroupService.listGroup(request)))
                            .filter(CollUtil::isNotEmpty)
                            .map(list -> list.stream()
                                    .map(o -> {
                                        TikTokPoiGroupItemDTO dto = new TikTokPoiGroupItemDTO();
                                        dto.setGroupId(o.getEquipmentGroupId());
                                        dto.setGroupName(o.getName());

                                        return dto;
                                    }).collect(Collectors.toList()))
                            .orElse(Collections.emptyList());
                }).orElse(Collections.emptyList());
        if (Objects.nonNull(data)) {
            data.setGroups(groups);
        }

        return response;
    }

    @ApiOperation("关联集合号门店")
    @PostMapping({"/merchant/associate-assembly"})
    public BaseResponse<Boolean> associateAssemblyPoi(@RequestBody MerchantTikTokPoiAssociateDTO dto) {
        AdUserInfoDTO currentUser = getCurrentUser();
        dto.setDistributorId(currentUser.getAdOrgId());
        dto.setMerchantName(currentUser.getPhone());
        dto.setOperatorId(currentUser.getAdUserId());
        return tiktokActivityClient.associateAssemblyPoi(dto);
    }
}
