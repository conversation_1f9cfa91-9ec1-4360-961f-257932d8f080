package cn.lyy.merchant.controller.buss;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.utils.RemoteResponseUtils;
import com.lyy.platform.enums.PlatformEnum;
import com.lyy.platform.rpc.rule.PlatformBizChargeRuleGroupApi;
import com.lyy.starter.common.resp.RespBody;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @date: 2023/5/16
 * @author: YL
 */
@Api("业务服务费扣款范围")
@Slf4j
@RestController
@RequestMapping("/rest/platform-biz-charge/group")
public class PlatformBizChargeGroupController extends BaseController {

    @Autowired
    private PlatformBizChargeRuleGroupApi platformBizChargeRuleGroupApi;

    @ApiOperation("是否有过扣款范围关联")
    @GetMapping("/has-biz-charge")
    public BaseResponse<Boolean> hasBizCharge() {
        RespBody<Boolean> respBody = platformBizChargeRuleGroupApi.checkHasAssociations(PlatformEnum.LYY.getPlatformId(),
                getAdOrgIdNotNull());
        Boolean result = RemoteResponseUtils.getRespBodyData(respBody, Boolean.FALSE);

        return ResponseUtils.success(result);
    }
}
