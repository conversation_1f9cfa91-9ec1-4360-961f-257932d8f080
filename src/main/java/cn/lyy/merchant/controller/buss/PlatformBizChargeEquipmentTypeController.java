package cn.lyy.merchant.controller.buss;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.utils.RemoteResponseUtils;
import com.lyy.platform.dto.response.rule.PlatformBizChargeEquipmentTypeDTO;
import com.lyy.platform.enums.PlatformEnum;
import com.lyy.platform.rpc.rule.PlatformBizChargeEquipmentTypeApi;
import com.lyy.starter.common.resp.RespBody;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @date: 2023/5/16
 * @author: YL
 */
@Api("业务服务费收费设备")
@Slf4j
@RestController
@RequestMapping("/rest/platform-biz-charge/equipment-type")
public class PlatformBizChargeEquipmentTypeController extends BaseController {

    @Autowired
    private PlatformBizChargeEquipmentTypeApi bizChargeEquipmentTypeApi;

    @ApiOperation("业务收费设备类型列表")
    @GetMapping
    public BaseResponse<List<PlatformBizChargeEquipmentTypeDTO>> types() {
        RespBody<List<PlatformBizChargeEquipmentTypeDTO>> respBody = bizChargeEquipmentTypeApi.listAll(PlatformEnum.LYY.getPlatformId());
        List<PlatformBizChargeEquipmentTypeDTO> data = RemoteResponseUtils.getRespBodyData(respBody, Collections.emptyList());

        return ResponseUtils.success(data);
    }

}
