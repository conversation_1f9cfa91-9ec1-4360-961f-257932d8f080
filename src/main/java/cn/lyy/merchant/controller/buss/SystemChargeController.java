package cn.lyy.merchant.controller.buss;

import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.base.util.WebUtil;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import cn.lyy.merchant.microservice.SystemChargeEquipmentApi;
import com.lyy.charge.dto.charge.SystemChargeClauseRecordDTO;
import com.lyy.charge.dto.charge.SystemChargeDistributorInfoDTO;
import com.lyy.charge.enums.BoolEnum;
import com.lyy.charge.enums.charge.ClauseRecordAccountTypeEnum;
import com.lyy.charge.enums.response.ResponseEnum;
import com.lyy.charge.param.charge.SystemChargeClauseRecordParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@RestController
@RequestMapping("/rest/systemCharge")
public class SystemChargeController extends BaseController {

    @Resource
    private SystemChargeEquipmentApi systemChargeEquipmentApi;

    protected HttpServletRequest getHttpServletRequest() {
        return WebUtil.getHttpServletRequest();
    }

    @GetMapping("/getAgreeClause")
    @AuthorityResource(name = "获取商业化条款",value = "mb_getAgreeClause",role = "Saas_Merchant",parentValue = "Feerule")
    public BaseResponse<Map<Integer,Integer>> getAgreeClause () {

        AdUserInfoDTO currentUser = getCurrentUser();
        com.lyy.charge.dto.response.BaseResponse<List<SystemChargeClauseRecordDTO>> baseResponse = systemChargeEquipmentApi.getByAdOrgIdOrAgentUserId(currentUser.getAdOrgId(), null);

        //没有查询到 默认不弹出协议  防止因为收费系统问题 导致重复签署协议
        if (baseResponse == null || baseResponse.getData() == null || !ResponseEnum.SUCCESS.getCode().equals(baseResponse.getCode())) {
            return ResponseUtils.success();
        }

        Map<Integer, Integer> resultMap = baseResponse.getData().stream().collect(Collectors.toMap(SystemChargeClauseRecordDTO::getAgreementId, SystemChargeClauseRecordDTO::getIsAgree));

        return ResponseUtils.success(resultMap);

    }

    /**
     * 商家同意条款
     *
     * @return 成功或失败
     */
    @GetMapping("/agreeClause")
    @AuthorityResource(name = "商家同意条款",value = "mb_agreeClause",role = "Saas_Merchant",parentValue = "Feerule")
    public BaseResponse<Boolean> agreeClause(Integer agreementId) {

        AdUserInfoDTO currentUser = getCurrentUser();

        //判断是否为主账号
        if (!currentUser.getIsApprover()) {
            return ResponseUtils.error(ResponseCodeEnum.FAIL.getCode(),"当前账号不是主账号，不能签约");
        }
        Long adOrgId = currentUser.getAdOrgId();
        SystemChargeClauseRecordParam param = new SystemChargeClauseRecordParam();
        param.setLyyDistributorId(currentUser.getAdOrgId());
        param.setUserId(currentUser.getAdUserId());
        param.setIsAgree(BoolEnum.Y.getValue());
        param.setAccountType(ClauseRecordAccountTypeEnum.ENTERPRISE.getValue());
        param.setAgreementId(agreementId);
        com.lyy.charge.dto.response.BaseResponse<Boolean> baseResponse = systemChargeEquipmentApi.agreeClause(param);
        if (baseResponse == null || !ResponseEnum.SUCCESS.getCode().equals(baseResponse.getCode())) {
            return ResponseUtils.success(Boolean.FALSE);
        }
        return ResponseUtils.success(baseResponse.getData());
    }

    /**
     * 商家收费系统信息
     *
     * @return 收费系统系信息（是否存在缴费设备和是否签约）
     */
    @GetMapping("distributorInfo")
    public BaseResponse<List<SystemChargeDistributorInfoDTO>> distributorInfo() {
        com.lyy.charge.dto.response.BaseResponse<List<SystemChargeDistributorInfoDTO>> response = systemChargeEquipmentApi.distributorInfo(getAdOrgIdNotNull().longValue());
        return ResponseUtils.success(response.getData());
    }

}
