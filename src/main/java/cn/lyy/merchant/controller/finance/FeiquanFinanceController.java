package cn.lyy.merchant.controller.finance;

import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.merchant.controller.common.BaseController;

import com.lyy.payment.merchant.api.service.finance.FeiquanFinanceClient;
import com.lyy.payment.merchant.request.FinanceAuditMerchantInfoRequest;
import com.lyy.payment.merchant.request.FinanceAuditStatusRequest;
import com.lyy.payment.merchant.request.FinanceIdentityRequest;
import com.lyy.payment.merchant.request.FinanceMerchantCreditInfoRequest;
import com.lyy.payment.merchant.response.CommonFinanceResp;
import com.lyy.payment.merchant.response.FinanceIdentityResponse;
import com.lyy.payment.merchant.response.FinanceMerchantCreditInfoResponse;
import com.lyy.starter.common.resp.RespBody;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
@RequestMapping("/merchant/finance/credit")
public class FeiquanFinanceController extends BaseController {

    @Autowired
    private FeiquanFinanceClient feiquanFinanceClient;


    /**
     * 获取授信记录
     *
     * @return
     */
    @PostMapping("/getAuditStatus")
    @AuthorityResource(name = "获取授信记录",value = "mb_merchant_audit_status",role = "Merchant")

    public RespBody<Boolean> getApplyLogs() {
        try {
            FinanceAuditStatusRequest request = new FinanceAuditStatusRequest();
            request.setAdOrg(getAdOrgIdNotNull());
            return feiquanFinanceClient.getAuditStatus(request);
        }catch (Exception e){
            throw e;
        }

    }


    /**
     * 获取身份信息
     *
     * @param request
     * @return
     */
    @PostMapping("/getMerchantIdentity")
    @AuthorityResource(name = "获取身份信息",value = "mb_merchant_audit_identity",role = "Merchant")
    public RespBody<FinanceIdentityResponse> getApplyLogs(@RequestBody FinanceIdentityRequest request) {
        try {
            request.setAdOrg(getAdOrgIdNotNull());
            return feiquanFinanceClient.getMerchantIdentity(request);
        }catch (Exception e){
            throw e;
        }

    }

    /**
     * 获取用户信息
     *
     * @return
     */
    @GetMapping("/getMerchantCreditInfo")
    @AuthorityResource(name = "获取用户信息",value = "mb_merchant_audit_creditInfo",role = "Merchant")
    public RespBody<String> getAuditMerchantInfo() {
        try {
            FinanceMerchantCreditInfoRequest request = new FinanceMerchantCreditInfoRequest();
            request.setAdOrgId(getAdOrgIdNotNull());
            return RespBody.ok(feiquanFinanceClient.getMerchantCreditInfo(request));
        }catch (Exception e){
            throw e;
        }

    }


}
