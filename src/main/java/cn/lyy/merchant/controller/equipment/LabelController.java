package cn.lyy.merchant.controller.equipment;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.lyy_api.constant.multistore.MultiStoreLabelTypeEnum;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.equipment.EquipmentLabelDTO;
import cn.lyy.merchant.service.equipment.LabelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 类描述：设备标签管理
 * <p>
 *
 * <AUTHOR>
 * @since 2020/11/5 13:48
 */
@RestController
@RequestMapping("/label")
public class LabelController extends BaseController {

    @Autowired
    private LabelService labelService;

    /**
     * @see MultiStoreLabelTypeEnum
     * @param labelType
     * @return
     */
    @GetMapping("/all")
    public BaseResponse<List<EquipmentLabelDTO>> queryAll(@RequestParam(required = false,defaultValue = "1", value = "labelType") Integer labelType) {
        return ResponseUtils.success(labelService.queryAll(getCurrentUser().getAdOrgId(), labelType));
    }

}
