package cn.lyy.merchant.controller.equipment;

import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.equipment.dto.equipment.EquipmentStatusDTO;
import cn.lyy.equipment.dto.equipment.EquipmentTypeDTO;
import cn.lyy.merchant.api.service.MerchantGroupService;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.RemoteStartParamDTO;
import cn.lyy.merchant.dto.equipment.EquipmentListDTO;
import cn.lyy.merchant.dto.equipment.EquipmentQueryDTO;
import cn.lyy.merchant.dto.group.GroupEquipmentTypeDTO;
import cn.lyy.merchant.dto.response.EquipmentFunctionDTO;
import cn.lyy.merchant.dto.response.EquipmentProfileDTO;
import cn.lyy.merchant.service.IEquipmentBusinessService;
import cn.lyy.merchant.service.equipment.EquipmentTypeService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * 类描述：设备相关接口
 * <p>
 *
 * <AUTHOR>
 * @since 2020/11/5 09:58
 */
@RestController
@RequestMapping("/equipment")
@Slf4j
public class EquipmentController extends BaseController {

    @Autowired
    private IEquipmentBusinessService equipmentBusinessService;

    @Autowired
    private EquipmentTypeService equipmentTypeService;

    @Autowired
    private MerchantGroupService merchantGroupService;

    /**
     * 根据设备类型和场地查询当前用户的设备列表
     */
    @PostMapping("/by/type/group")
    @AuthorityResource(name = "根据设备类型和场地查询设备", value="mb_equipment_by_group", role = "Saas_Merchant", parentValue = "Plugin-RemoteCoin")
    public BaseResponse<List<EquipmentListDTO>> byTypeAndGroup(EquipmentQueryDTO query) {
        query.setLyyDistributorId(getCurrentUser().getAdOrgId().intValue());
        return ResponseUtils.success(equipmentBusinessService.queryByTypeAndGroup(query, true));
    }

    /**
     * 远程启动
     * @param param
     * @return
     */
    @PostMapping("/remote/start")
    public BaseResponse remoteStart(@RequestBody RemoteStartParamDTO param) {
        log.info("远程启动，参数：{}", JSON.toJSONString(param));
        param.setAdUserId(getCurrentUser().getAdUserId());
        equipmentBusinessService.remoteStart(param);
        return ResponseUtils.success();

    }

    @GetMapping("/list/equipmentType")
    @AuthorityResource(name = "获取所有设备类型", value="mb_all_equipment_type", role = "Saas_Merchant", parentValue = "Plugin-RemoteCoin")
    public BaseResponse<List<EquipmentTypeDTO>> getListEquipmentTypeInfo(){
        log.debug("获取登录用户拥有的设备类型信息");
        BaseResponse<List<EquipmentTypeDTO>> baseResponse = new BaseResponse<>();
        baseResponse.setData(equipmentTypeService.selectAllEquipmentType(getCurrentUser().getAdUserId(),getCurrentUser().getAdOrgId()));
        return baseResponse;
    }

    @GetMapping(value = "/group/listEquipmentTypes",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @AuthorityResource(name = "获取场地下的设备类型", value="mb_getEquipmentTypes", role = "Saas_Merchant", parentValue = "Equipment")
    public BaseResponse<GroupEquipmentTypeDTO> listEquipmentTypes(@RequestParam(required = false) Long groupId){
        log.debug("获取登录用户 场地下的设备类型信息");
        BaseResponse<GroupEquipmentTypeDTO> result = merchantGroupService.getGroupEquipType(groupId,this.getCurrentUser().getAdOrgId());
        GroupEquipmentTypeDTO list = Optional.ofNullable(result).filter(r -> r.getCode() == 0).map(BaseResponse::getData).get();
        return ResponseUtils.success(list);
    }

    /**
     * 查询设备状态
     * @param equipmentValue
     * @return
     */
    @GetMapping("/online")
    @AuthorityResource(name = "获取设备状态", value="mb_equipment_status", role = "Saas_Merchant", parentValue = "Plugin-RemoteCoin")
    public BaseResponse<Boolean> onlineStatus(@RequestParam("equipmentValue") String equipmentValue) {
        BaseResponse<Boolean> baseResponse = new BaseResponse<>();
        baseResponse.setData(equipmentBusinessService.onlineStatus(equipmentValue));
        return baseResponse;
    }

    /**
     * 查询设备简介
     * @param equipmentValue
     * @return
     */
    @GetMapping("/profile")
    @AuthorityResource(name = "获取设备简介信息", value="mb_equipment_profile", role = "Saas_Merchant", parentValue = "Plugin-RemoteCoin")
    public BaseResponse<EquipmentProfileDTO> equipmentProfile(@RequestParam("equipmentValue") String equipmentValue) {
        BaseResponse<EquipmentProfileDTO> baseResponse = new BaseResponse<>();
        baseResponse.setData(equipmentBusinessService.equipmentProfile(equipmentValue,getAdOrgIdNotNull()));
        return baseResponse;
    }


    /**
     * 查询设备支持的功能
     * @param equipmentValue
     * @return
     */
    @GetMapping("/support/function")
    @AuthorityResource(name = "查询设备支持的功能", value="mb_equipment_function", role = "Saas_Merchant", parentValue = "Plugin-RemoteCoin")
    public BaseResponse<EquipmentFunctionDTO> supportFunction(@RequestParam("equipmentValue") String equipmentValue) {
        BaseResponse<EquipmentFunctionDTO> baseResponse = new BaseResponse<>();
        baseResponse.setData(equipmentBusinessService.supportFunction(equipmentValue));
        return baseResponse;
    }

    /**
     * 查询当前商家故障洗衣机
     */
    @PostMapping("/m/listErrorWasher")
    @AuthorityResource(name = "查询当前商家故障洗衣机", value="mb_equipment_washer_errorList", role = "Saas_Merchant", parentValue = "Equipment")
    public BaseResponse<List<EquipmentStatusDTO>> listErrorWasher() {
        EquipmentQueryDTO query = new EquipmentQueryDTO();
        int lyyDistributorId = getCurrentUser().getAdOrgId().intValue();
        query.setLyyDistributorId(lyyDistributorId);
        //query.setAdUserId(getCurrentUser().getAdUserId().intValue());
        return ResponseUtils.success(equipmentBusinessService.listErrorWasher(query));

    }



}