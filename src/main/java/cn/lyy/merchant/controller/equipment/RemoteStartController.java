package cn.lyy.merchant.controller.equipment;

import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.equipment.dto.equipment.RemoteCoinsDailyDTO;
import cn.lyy.equipment.dto.equipment.RemoteCoinsRecordDTO;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.RemoteStartParamDTO;
import cn.lyy.merchant.dto.RemoteStopParamDTO;
import cn.lyy.merchant.service.IEquipmentBusinessService;
import cn.lyy.merchant.service.equipment.RemoteStartService;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 类描述：
 * <p>
 *
 * <AUTHOR>
 * @since 2020/11/10 11:27
 */
@RestController
@RequestMapping("/remote")
public class RemoteStartController extends BaseController {

    @Autowired
    private IEquipmentBusinessService equipmentBusinessService;

    @Autowired
    private RemoteStartService remoteStartService;
    /**
     * 远程启动
     * @param param
     * @return
     */
    @PostMapping("/start")
    @AuthorityResource(name = "远程启动", value="mb_remote_start", role = "Saas_Merchant",  parentValue = "Plugin-RemoteCoin")
    public BaseResponse remoteStart(@RequestBody RemoteStartParamDTO param) {
        param.setAdUserId(getCurrentUser().getAdUserId());
        equipmentBusinessService.remoteStart(param);
        return ResponseUtils.success();

    }

    /**
     * 远程停止
     * @param param
     * @return
     */
    @PostMapping("/stop")
    @AuthorityResource(name = "远程停止", value="mb_remote_stop", role = "Saas_Merchant",  parentValue = "Plugin-RemoteCoin")
    public BaseResponse<Boolean> remoteStop(@RequestBody RemoteStopParamDTO param) {
        param.setAdUserId(getCurrentUser().getAdUserId());
        String webSocketToken = getWebSocketToken();
        return ResponseUtils.success(equipmentBusinessService.remoteStop(param,webSocketToken));

    }

    /**
     * 分页获取场地
     * @param pageIndex
     * @param pageSize
     * @return
     */
    @GetMapping("/record/page")
    @AuthorityResource(name = "远程启动记录", value="mb_remote_start_record",   parentValue = "Plugin-RemoteCoin", role="Saas_Merchant")
    public BaseResponse<PageInfo<RemoteCoinsRecordDTO>> recordPage(@RequestParam("pageIndex") Integer pageIndex,
                                                                   @RequestParam("pageSize") Integer pageSize,
                                                                   @RequestParam("month") String month) {
        return ResponseUtils.success(remoteStartService.recordPage(pageIndex, pageSize, month, getCurrentUser().getAdOrgId(), getCurrentUser().getAdUserId()));
    }

    /**
     * 每日远程启动统计
     * @param month,
     * @return
     */
    @GetMapping("/statistics/daily")
    @AuthorityResource(name = "远程启动记录每日统计", value="mb_remote_start_daily",  role="Saas_Merchant",  parentValue = "Plugin-RemoteCoin")
    public BaseResponse<List<RemoteCoinsDailyDTO>> statisticsDaily(@RequestParam("month") String month) {

        return ResponseUtils.success(remoteStartService.statisticsDaily(month, getCurrentUser().getAdOrgId(), getCurrentUser().getAdUserId()));
    }
}
