package cn.lyy.merchant.controller.equipment;

import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.base.dto.JsonObject;
import cn.lyy.base.dto.Status;
import cn.lyy.base.util.StringUtil;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.equipment.dto.equipment.FactoryMotheroardDTO;
import cn.lyy.equipment.dto.equipment.MainboardDTO;
import cn.lyy.equipment.dto.param.EquipmentExtendButtonsBean;
import cn.lyy.equipment.dto.param.EquipmentExtendParamSettingDTO;
import cn.lyy.equipment.dto.protocol.ProtocolMainDTO;
import cn.lyy.equipment.service.IEquipmentParamService;
import cn.lyy.equipment.service.IEquipmentService;
import cn.lyy.equipment.service.MainboardMicroService;
import cn.lyy.equipment.service.ProtocolMicroService;
import cn.lyy.merchant.async.EquipmentParamAsync;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.service.equipment.EquipmentParamService;
import cn.lyy.tools.constants.customer.CustomerResult;
import cn.lyy.tools.constants.open.FunctionConstants;
import cn.lyy.tools.equipment.OpenSettingInfo;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.util.Asserts;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

/**
 * 设备参数
 * @description:
 * @author: qgw
 * @date on 2020/11/11.
 * @Version: 1.0
 */
@Slf4j
@RestController
@RequestMapping("/rest/equipmentParam")
public class EquipmentParamController extends BaseController {
    @Resource
    private IEquipmentService equipmentService;

    @Resource
    private ProtocolMicroService protocolMicroService;

    @Resource
    private EquipmentParamService equipmentParamService;

    @Resource
    private IEquipmentParamService iEquipmentParamService;

    @Resource
    private MainboardMicroService mainboardMicroService;

    @Resource
    private EquipmentParamAsync equipmentParamAsync;

    /**
     * 获取设备参数定义
     *
     * @param equipmentId
     * @return
     */
    @RequestMapping(value = "/getEquipmentParamDef", method = RequestMethod.GET)
    public JsonObject getEquipmentParamDef(@RequestParam(value = "equipmentId") String equipmentId) {
        JsonObject jsonObject = new JsonObject();
        try {
            if (StringUtil.isEmpty(equipmentId)) {
                return CustomerResult.paramFail(jsonObject, "设备id不能为空");
            }
            if (getAdUserIdNotNull()==null) {
                jsonObject.setDescription("页面已过期,获取用户信息失败，请重新扫码进入页面。");
                jsonObject.setResult(Status.STATUS_FAIL);
                return jsonObject;
            }
            jsonObject.setData(equipmentService.listEquipmentParamDef(equipmentId));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            jsonObject.setDescription("获取设备参数定义失败");
            jsonObject.setResult(Status.STATUS_FAIL);
        }
        return jsonObject;
    }

    /**
     * 根据设备类型，获取协议列表
     * @param equipmentType
     * @return
     */
    @Deprecated
    @GetMapping(value = "/getProtocolByType")
    @AuthorityResource(name = "根据设备类型获取协议", value="mb_getProtocolByEquipmentType", role = "Saas_Merchant", parentValue = "Equipment")
    public BaseResponse<List<ProtocolMainDTO>> getProtocolByEquipmentType(@RequestParam String equipmentType){
        log.info("根据设备类型获取协议 param:{}",equipmentType);
        BaseResponse<List<ProtocolMainDTO>> result =  protocolMicroService.getProtocolByEquipmentType(equipmentType,this.getCurrentUser().getAdOrgId());
        List<ProtocolMainDTO> list = Optional.ofNullable(result).filter(r -> r.getCode() == 0).map(r -> r.getData()).get();
        return ResponseUtils.success(list);
    }

    /**
     * 根据设备类型，获取主板列表
     * @param equipmentType
     * @return
     */
    @GetMapping(value = "/getMainBoardByType")
    @AuthorityResource(name = "根据设备类型获取协议", value="mb_getMainBoardByEquipmentType", role = "Saas_Merchant", parentValue = "Equipment")
    public BaseResponse<List<FactoryMotheroardDTO>> getMainBoardByEquipmentType(@RequestParam String equipmentType){
        log.info("根据设备类型获取主板 param:{}",equipmentType);
        BaseResponse<List<FactoryMotheroardDTO>> result = mainboardMicroService.queryByEquipmentType(equipmentType,this.getCurrentUser().getAdOrgId());
        List<FactoryMotheroardDTO> list = Optional.ofNullable(result).filter(r -> r.getCode() == 0).map(r -> r.getData()).orElse(Collections.emptyList());
        return ResponseUtils.success(list);
    }


    /**
     * 2.0协议方式获取主板参数列表信息
     * @param uniqueCode
     * @return
     */
    @GetMapping("s2/paramConfigList")
    @AuthorityResource(name = "2.0协议方式获取主板参数列表信息", value="mb_s2_paramConfigList", role = "Saas_Merchant", parentValue = "Equipment")
    public BaseResponse paramConfigList(@RequestParam String uniqueCode) {
        BaseResponse<List<EquipmentExtendButtonsBean>> response = iEquipmentParamService.getParamConfigListFromProduct(uniqueCode);
//        BaseResponse<List<EquipmentExtendParamJsonDTO.ButtonsBean>> response = iEquipmentParamService.getParamConfigListFromProduct(uniqueCode);

        return response;
    }

    @PostMapping("s2/paramQuery")
    @AuthorityResource(name = "2.0协议方式查询参数项信息", value="b_s2_paramQuery", role = "Saas_Merchant", parentValue = "Equipment")
    public BaseResponse paramQuery(@RequestBody JSONObject param) {
        String uniqueCode = param.getString("uniqueCode");
        Long buttonId = param.getLong("buttonId");
        Integer cmdQuery = param.getInteger("cmdQuery");
        BaseResponse<EquipmentExtendParamSettingDTO> response = iEquipmentParamService.paramQuery(uniqueCode, buttonId, cmdQuery);
        return response;
    }

    @PostMapping("s2/paramSetting")
    @AuthorityResource(name = "2.0协议方式设置参数项信息", value="b_s2_paramSetting", role = "Saas_Merchant", parentValue = "Equipment")
    public BaseResponse paramSetting(@RequestBody EquipmentExtendParamSettingDTO equipmentExtendParamSettingDTO){
        String uniqueCode = equipmentExtendParamSettingDTO.getDeviceNo();
        BaseResponse response = iEquipmentParamService.paramSetting(uniqueCode,equipmentExtendParamSettingDTO);
        if(ResponseCodeEnum.SUCCESS.getCode() == response.getCode()){
            return success();
        }
        return error(ResponseCodeEnum.FAIL.getCode(),"设置失败");
    }


    /**
     * 1.0协议方式获取主板参数列表信息
     * @param uniqueCode
     * @return
     */
    @RequestMapping(value = "s1/paramConfigList", method = RequestMethod.GET)
    @AuthorityResource(name = "1.0协议方式获取主板参数列表", value="mb_s1_paramConfigList", role = "Saas_Merchant", parentValue = "Equipment")
    public BaseResponse s1ParamConfigList(@RequestParam String uniqueCode) {
        String settingInfo = equipmentParamService.queryParamInfoFromS1ByAsync(uniqueCode, FunctionConstants.BSYS_SAAS_QUERY_FUNCTION, null,this.getCurrentUser().getAdUserId(),this.getWebSocketToken());
        if (StringUtils.isBlank(settingInfo)){
            log.info(" {} 设备无法协议上的自定义参数信息",uniqueCode);
            BaseResponse BaseResponse = success(null);
            return BaseResponse;
        }
        Gson gson = new Gson();
        List<Object> list = gson.fromJson(settingInfo, new TypeToken<List<Object>>() {
        }.getType());
        return success(list);
    }

    /**
     * 1.0协议方式查询参数项信息
     * @param uniqueCode
     * @param data
     * @return
     */
    @RequestMapping(value = "s1/paramQuery", method = RequestMethod.GET)
    @AuthorityResource(name = "1.0协议方式查询参数项信息", value="mb_s1_paramQuery", role = "Saas_Merchant", parentValue = "Equipment")
    public BaseResponse s1ParamQuery(String uniqueCode, String data) {
        log.info("1.0协议方式查询参数项信息,uniqueCode:{},data:{}",uniqueCode,data);
        String settingInfo = equipmentParamService.queryParamInfoFromS1ByAsync(uniqueCode, FunctionConstants.BSYS_SAAS_QUERY_PARAM, data,this.getCurrentUser().getAdUserId(),this.getWebSocketToken());
        if (StringUtils.isBlank(settingInfo)){
            log.info(" {} 设备无法协议上的自定义参数信息",uniqueCode);
            BaseResponse BaseResponse = success(null);
            return BaseResponse;
        }
        Gson gson = new Gson();
        HashMap<String, String> hashMap = gson.fromJson(settingInfo, new TypeToken<HashMap<String, String>>() {
        }.getType());
        return ResponseUtils.success(hashMap);
    }

    /**
     * 1.0协议方式设置参数项信息
     * @param info
     * @return
     */
    @RequestMapping(value = "/s1/paramSetting", method = RequestMethod.POST)
    @AuthorityResource(name = "1.0协议方式设置参数项信息", value="mb_s1_paramSetting", role = "Saas_Merchant", parentValue = "Equipment")
    public BaseResponse s1ParamSetting(@RequestBody OpenSettingInfo info) {
        log.info("1.0协议方式设置参数项信息,param:{}", JSON.toJSONString(info));
        info.setFunctionCode(FunctionConstants.BSYS_SAAS_SETTING);
        Map<String,Boolean> resultMap = equipmentParamService.batchSettingParamInfoFromS1(Arrays.asList(info));
        if(resultMap.get(info.getUniqueCode())){
            return success();
        }
        return error(Status.STATUS_FAIL,"设置参数失败，请确保设备网络畅通");
    }

}
