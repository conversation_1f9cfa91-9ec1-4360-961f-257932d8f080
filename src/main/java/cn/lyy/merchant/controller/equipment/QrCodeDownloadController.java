package cn.lyy.merchant.controller.equipment;

import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.request.QrCodeDownloadDTO;
import cn.lyy.merchant.service.equipment.QrCodeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2020/12/26 08:43
 * </p>
 */
@RestController
@RequestMapping("/qrcode")
@Slf4j
public class QrCodeDownloadController extends BaseController {

    private QrCodeService qrCodeService;

    public QrCodeDownloadController(QrCodeService qrCodeService) {
        this.qrCodeService = qrCodeService;
    }

    @PostMapping(value = "/batch/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    @AuthorityResource(name = "批量下载设备二维码", value = "mb_qrcode_batch_export", seq = 1, role = "Saas_Merchant", parentValue = "EquipmentManage")
    public BaseResponse<String> batchDownload(@RequestBody QrCodeDownloadDTO dto) {
        log.debug("批量下载设备二维码:{}", dto);
        String url = qrCodeService.batchCreate(dto, getCurrentUser().getAdUserId());
        return ResponseUtils.success(url);
    }
}
