package cn.lyy.merchant.controller.equipment;

import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.equipment.dto.equipment.RegisterItemDTO;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.request.EquipmentGetByCodeDTO;
import cn.lyy.merchant.dto.request.EquipmentRegisterDTO;
import cn.lyy.merchant.dto.response.BindCheckDTO;
import cn.lyy.merchant.service.equipment.EquipmentOperateService;
import com.alibaba.fastjson.JSON;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 类描述：设备的一些通用操作
 * 绑定、转移、解绑等
 * <p>
 *
 * <AUTHOR>
 * @since 2020/11/11 10:46
 */
@Slf4j
@RestController
@RequestMapping("/operate")
public class EquipmentOperateController extends BaseController {

    @Autowired
    private EquipmentOperateService equipmentOperateService;

    /**
     * 注册前检查
     * @param param
     * @return
     */
    @PostMapping("/bind/check")
    @AuthorityResource(name = "注册前检查是否可注册", value="mb_equipment_bind_check", role = "Saas_Merchant",  parentValue = "Equipment")
    public BaseResponse<BindCheckDTO> bindCheck(@RequestBody EquipmentGetByCodeDTO param) {
        return ResponseUtils.success(equipmentOperateService.bindCheck(param));
    }

    /**
     * 注册设备
     * @param param
     * @return
     */
    @PostMapping("/bind")
    @AuthorityResource(name = "设备注册", value="mb_equipment_bind", role = "Saas_Merchant",  parentValue = "Equipment")
    public BaseResponse bind(@RequestBody @Validated EquipmentRegisterDTO param) {
        log.info("设备注册参数:{},批量注册设备数:{}", JSON.toJSONString(param),param.getEquipments().size());
        if (!CollectionUtils.isEmpty(param.getEquipments())) {
            List<RegisterItemDTO> list = param.getEquipments().stream().filter(a -> !a.getCode().startsWith("92")).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(list)) {
                String collect = list.stream().map(RegisterItemDTO::getCode).collect(Collectors.joining(","));
                return ResponseUtils.error(-1,collect+"，注册设备必须是92开头的设备");
            }
        }
        param.setDistributorId(getCurrentUser().getAdOrgId());
        param.setUserId(getCurrentUser().getAdUserId());
        equipmentOperateService.bind(param);
        return ResponseUtils.success();
    }

}
