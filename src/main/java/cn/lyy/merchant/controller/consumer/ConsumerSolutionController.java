package cn.lyy.merchant.controller.consumer;

import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.request.ConsumerSolutionListReqDTO;
import cn.lyy.merchant.dto.request.ConsumerSolutionSaveDTO;
import cn.lyy.merchant.dto.request.ConsumerSolutionUpdateDTO;
import cn.lyy.merchant.dto.request.EquipmentConsumerSolutionReqDTO;
import cn.lyy.merchant.dto.request.GroupConsumerSolutionReqDTO;
import cn.lyy.merchant.dto.request.baichuan.BaichuanConsumerSolutionPageReqDTO;
import cn.lyy.merchant.dto.request.baichuan.BaichuanConsumerSolutionReqDTO;
import cn.lyy.merchant.dto.request.baichuan.BaichuanConsumerSolutionUpsertDTO;
import cn.lyy.merchant.dto.request.baichuan.ConsumerSolutionDetailVO;
import cn.lyy.merchant.dto.request.baichuan.ConsumerSolutionGroupReqDTO;
import cn.lyy.merchant.dto.request.baichuan.ConsumerSolutionGroupVO;
import cn.lyy.merchant.dto.request.baichuan.ConsumerSolutionOverviewVO;
import cn.lyy.merchant.dto.request.baichuan.EquipmentAssociationReqDTO;
import cn.lyy.merchant.dto.request.baichuan.EquipmentConfigurationListVO;
import cn.lyy.merchant.dto.response.ConsumerSolutionDetailDTO;
import cn.lyy.merchant.dto.response.ConsumerSolutionListDTO;
import cn.lyy.merchant.dto.response.EquipmentConsumerSolutionDTO;
import cn.lyy.merchant.dto.response.GroupConsumerSolutionDTO;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import cn.lyy.merchant.service.consumer.BaichuanConsumerSolutionService;
import cn.lyy.merchant.service.consumer.ConsumerSolutionService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.billing.interfaces.consumer.dto.request.AssociationEquipmentReqDTO;
import com.lyy.billing.interfaces.consumer.dto.request.EquipmentConfigurationExistRatioReqDTO;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/7
 */
@RestController
@RequestMapping("/consumer/solution")
@Slf4j
public class ConsumerSolutionController extends BaseController {

    @Resource
    private ConsumerSolutionService consumerSolutionService;
    @Resource
    private BaichuanConsumerSolutionService baichuanConsumerSolutionService;

    @PostMapping(value = "/save")
    @AuthorityResource(name = "新增消费方案",value = "mb_consumer_solution_save",role = "Saas_Merchant",parentValue = "Feerule")
    public BaseResponse<Long> save(@RequestBody @Validated ConsumerSolutionSaveDTO consumerSolutionSaveDTO) {
        log.info("新增消费方案,consumerSolutionSaveDTO:{}", consumerSolutionSaveDTO);
        Long id = consumerSolutionService.save(consumerSolutionSaveDTO, getAdOrgIdNotNull(), getAdUserIdNotNull());
        return ResponseUtils.success(id);
    }

    @PostMapping(value = "/edit")
    @AuthorityResource(name = "编辑消费方案",value = "mb_consumer_solution_edit",role = "Saas_Merchant",parentValue = "Feerule")
    public BaseResponse<Boolean> edit(@RequestBody @Validated ConsumerSolutionUpdateDTO consumerSolutionUpdateDTO) {
        log.info("编辑消费方案,consumerSolutionUpdateDTO:{}", consumerSolutionUpdateDTO);
        Boolean success = consumerSolutionService.edit(consumerSolutionUpdateDTO, getAdOrgIdNotNull(), getAdUserIdNotNull());
        return ResponseUtils.success(success);
    }

    @DeleteMapping("/delete")
    @AuthorityResource(name = "删除消费方案",value = "mb_consumer_solution_delete",role = "Saas_Merchant",parentValue = "Feerule")
    public BaseResponse<Boolean> delete(@RequestParam("id") Long id) {
        log.info("删除消费方案,id:{}", id);
        Boolean success = consumerSolutionService.delete(id, getAdOrgIdNotNull());
        return ResponseUtils.success(success);
    }

    @PostMapping("/list")
    @AuthorityResource(name = "获取消费方案列表",value = "mb_consumer_solution_list",role = "Saas_Merchant",parentValue = "Feerule")
    public BaseResponse<List<ConsumerSolutionListDTO>> list(@RequestBody @Validated ConsumerSolutionListReqDTO consumerSolutionListReqDTO) {
        log.info("获取消费方案列表,consumerSolutionListReqDTO:{}", consumerSolutionListReqDTO);
        List<ConsumerSolutionListDTO> list = consumerSolutionService.list(consumerSolutionListReqDTO, getAdOrgIdNotNull());
        return ResponseUtils.success(list);
    }

    @GetMapping("/detail")
    @AuthorityResource(name = "获取消费方案详情",value = "mb_consumer_solution_detail",role = "Saas_Merchant",parentValue = "Feerule")
    public BaseResponse<ConsumerSolutionDetailDTO> detail(@RequestParam("id") Long id, @RequestParam("category") Integer category,
        @RequestParam(value = "type",required = false,defaultValue = "1") Integer type) {
        log.info("获取消费方案详情,id:{},category:{},type:{}", id, category, type);
        ConsumerSolutionDetailDTO detail = consumerSolutionService.detail(id, category, type, getAdOrgIdNotNull());
        return ResponseUtils.success(detail);
    }

    @PostMapping("/equipment/association")
    @AuthorityResource(name = "消费方案关联设备",value = "mb_consumer_solution_equipment",role = "Saas_Merchant",parentValue = "Feerule")
    public BaseResponse<Boolean> associationEquipments(@Validated @RequestBody AssociationEquipmentReqDTO associationEquipmentReqDTO) {
        log.info("消费方案关联设备,associationEquipmentReqDTO:{}", associationEquipmentReqDTO);
        Boolean success = consumerSolutionService.associationEquipments(associationEquipmentReqDTO, getAdOrgIdNotNull());
        return ResponseUtils.success(success);
    }

    @PostMapping("/groups")
    @AuthorityResource(name = "获取用户场地下设备消费方案列表",value = "mb_consumer_solution_groups",role = "Saas_Merchant",parentValue = "Feerule")
    public BaseResponse<List<GroupConsumerSolutionDTO>> findGroupConsumerSolutions(@RequestBody GroupConsumerSolutionReqDTO groupConsumerSolutionReqDTO) {
        Long merchantId = getAdOrgIdNotNull();
        Long adUserId = getAdUserIdNotNull();
        log.info("获取用户场地下设备消费方案列表,groupConsumerSolutionReqDTO:{},merchantId:{},adUserId:{}",
            groupConsumerSolutionReqDTO, merchantId, adUserId);
        List<GroupConsumerSolutionDTO> list = consumerSolutionService.findGroupConsumerSolutions(groupConsumerSolutionReqDTO,
            merchantId, adUserId);
        return ResponseUtils.success(list);
    }

    @PostMapping("/equipment/list")
    @AuthorityResource(name = "获取设备关联消费方案",value = "mb_consumer_equipment_list",role = "Saas_Merchant",parentValue = "Feerule")
    public BaseResponse<List<EquipmentConsumerSolutionDTO>> findEquipmentConsumerSolutions(
        @RequestBody EquipmentConsumerSolutionReqDTO equipmentConsumerSolutionReqDTO) {
        log.info("获取设备关联消费方案,equipmentConsumerSolutionReqDTO:{}", equipmentConsumerSolutionReqDTO);
        List<EquipmentConsumerSolutionDTO> list = consumerSolutionService.findEquipmentConsumerSolutions(equipmentConsumerSolutionReqDTO, getAdOrgIdNotNull());
        return ResponseUtils.success(list);
    }

    @ApiOperation(value = "百川新增或更新消费方案")
    @PostMapping(value = "/baichuan/upsert")
    @AuthorityResource(name = "百川新增或更新消费方案",value = "mb_consumer_solution_baichuan_upsert",role = "Saas_Merchant",parentValue = "Feerule")
    public BaseResponse<Long> baichuanUpsert(@RequestBody @Validated BaichuanConsumerSolutionUpsertDTO upsertDTO) {
        log.info("百川新增或更新消费方案,param:{}", log.isDebugEnabled() ? JSONObject.toJSONString(upsertDTO) : upsertDTO);
        AdUserInfoDTO currentUser = getCurrentUser();
        Long consumerSolutionId = baichuanConsumerSolutionService.upsertConsumerSolution(upsertDTO, currentUser.getAdOrgId(), currentUser.getAdUserId());
        return ResponseUtils.success(consumerSolutionId);
    }

    @ApiOperation(value = "百川获取消费方案列表")
    @PostMapping("/baichuan/list")
    @AuthorityResource(name = "百川获取消费方案列表",value = "mb_consumer_solution_baichuan_list",role = "Saas_Merchant",parentValue = "Feerule")
    public BaseResponse<List<ConsumerSolutionOverviewVO>> listOverviews(@RequestBody @Validated BaichuanConsumerSolutionReqDTO request) {
        log.debug("百川获取消费方案列表,request:{}", request);
        List<ConsumerSolutionOverviewVO> list = baichuanConsumerSolutionService.listOverviews(request, getAdOrgIdNotNull());
        return ResponseUtils.success(list);
    }

    @ApiOperation(value = "百川获取消费方案详情")
    @GetMapping("/baichuan/detail")
    @AuthorityResource(name = "百川获取消费方案详情",value = "mb_consumer_solution_baichuan_detail",role = "Saas_Merchant",parentValue = "Feerule")
    public BaseResponse<ConsumerSolutionDetailVO> getConsumerSolution(@RequestParam("id") Long id) {
        log.debug("百川获取消费方案详情,id:{}", id);
        ConsumerSolutionDetailVO detail = baichuanConsumerSolutionService.detail(id);
        return ResponseUtils.success(detail);
    }

    @ApiOperation(value = "百川消费方案关联设备")
    @PostMapping("/baichuan/equipment/association")
    @AuthorityResource(name = "百川消费方案关联设备",value = "mb_consumer_solution_baichuan_equipment",role = "Saas_Merchant",parentValue = "Feerule")
    public BaseResponse<Boolean> equipmentAssociation(@Validated @RequestBody EquipmentAssociationReqDTO request) {
        log.info("百川消费方案关联设备,param:{}", request);
        AdUserInfoDTO currentUser = getCurrentUser();
        Boolean result = baichuanConsumerSolutionService.equipmentAssociate(request, currentUser.getAdOrgId(), currentUser.getAdUserId());
        return ResponseUtils.success(result);
    }

    @ApiOperation(value = "用户场地下设备关联消费方案情况")
    @PostMapping("/baichuan/groups")
    @AuthorityResource(name = "用户场地下设备关联消费方案情况",value = "mb_consumer_solution_baichuan_groups",role = "Saas_Merchant",parentValue = "Feerule")
    public BaseResponse<List<ConsumerSolutionGroupVO>> findSolutionGroupsEquipment(@RequestBody ConsumerSolutionGroupReqDTO request) {
        log.debug("用户场地下设备关联消费方案情况,param:{}",request);
        List<ConsumerSolutionGroupVO> groups = baichuanConsumerSolutionService.findSolutionGroupsEquipment(request);
        return ResponseUtils.success(groups);
    }

    @ApiOperation(value = "百川消费方案获取设备配置列表")
    @GetMapping("/baichuan/equipment/configuration/list")
    @AuthorityResource(name = "获取设备配置列表",value = "mb_consumer_solution_baichuan_equipment_configuration_list",role = "Saas_Merchant",parentValue = "Feerule")
    public BaseResponse<List<EquipmentConfigurationListVO>> equipmentConfigurationList(@RequestParam("equipmentType") String equipmentType,
            @RequestParam(value = "equipmentValue", required = false) String equipmentValue) {
        log.info("百川消费方案获取设备配置列表入参:{}",equipmentType);
        List<EquipmentConfigurationListVO> result =baichuanConsumerSolutionService.equipmentConfigurationList(equipmentType, equipmentValue);
        log.debug("百川消费方案获取设备配置列表出参:{}",result);
        return ResponseUtils.success(result);
    }


    @ApiOperation(value = "分页查询百川获取消费方案列表")
    @PostMapping("/baichuan/page")
    @AuthorityResource(name = "分页查询百川获取消费方案列表",value = "mb_consumer_solution_baichuan_page",role = "Saas_Merchant",parentValue = "Feerule")
    public BaseResponse<Page<ConsumerSolutionOverviewVO>> listOverviews(@RequestBody @Validated BaichuanConsumerSolutionPageReqDTO request) {
        log.debug("分页查询百川获取消费方案列表,request:{}", request);
        Page<ConsumerSolutionOverviewVO> page = baichuanConsumerSolutionService.pageOverviews(request, getAdOrgIdNotNull());
        return ResponseUtils.success(page);
    }

    @ApiOperation(value = "百川删除消费方案")
    @DeleteMapping("/baichuan/delete")
    @AuthorityResource(name = "百川删除消费方案",value = "mb_consumer_solution_baichuan_delete",role = "Saas_Merchant",parentValue = "Feerule")
    public BaseResponse<Boolean> baichuanDelete(@RequestParam("id") Long id) {
        log.info("百川删除消费方案,id:{}", id);
        AdUserInfoDTO currentUser = getCurrentUser();
        Boolean result = baichuanConsumerSolutionService.deleteConsumerSolution(id, currentUser.getAdOrgId(), currentUser.getAdUserId());
        return ResponseUtils.success(result);
    }


    @ApiOperation(value = "设备和消费方案是否配置比例")
    @PostMapping("/existConfigurationRatio")
    @AuthorityResource(name = "设备和消费方案是否配置比例", value = "mb_consumer_solution_exist_configuration_ratio", role = "Saas_Merchant", parentValue = "Feerule")
    public BaseResponse<Boolean> existConfigurationRatio(@Valid @RequestBody EquipmentConfigurationExistRatioReqDTO request) {
        log.debug("设备的和消费方案是否配置比例,param:{}", request);
        return ResponseUtils.success(baichuanConsumerSolutionService.existConfigurationRatio(request));
    }

}
