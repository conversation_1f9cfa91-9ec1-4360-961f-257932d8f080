package cn.lyy.merchant.controller;

import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.merchant.api.service.MerchantEquipmentService;
import cn.lyy.merchant.api.service.MerchantUserService;
import cn.lyy.merchant.controller.common.UserModelContrller;
import cn.lyy.merchant.dto.menu.MenuSwitchVisibilityDTO;
import cn.lyy.merchant.dto.menu.MerchantAuthMenuGetDTO;
import cn.lyy.merchant.dto.user.AdUserDTO;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.service.UserService;
import cn.lyy.merchant.utils.ValidatorUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <p>Title:saas2</p>
 * <p>Desc: 用户接口</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020/11/14
 */
@Slf4j
@RestController
@RequestMapping("/rest/user")
public class UserController extends UserModelContrller {

    @Autowired
    private MerchantUserService merchantUserService;
    @Autowired
    private UserService userService;

    @Resource
    private MerchantEquipmentService merchantEquipmentService;

    static Pattern p = Pattern.compile("^[0-9A-Za-z]{6,20}$");

    @Value("${sms.sign:}")
    private String registerSmsSign;

    @PostMapping("/getUserInfo")
    @AuthorityResource(name = "获取用户信息", value = "mb_getUserInfo", seq = 1, role = "Saas_Merchant", parentValue = "MechantUser")
    public BaseResponse<AdUserInfoDTO> getUserInfo() throws BusinessException {
        AdUserInfoDTO userInfoDTO = getCurrentUser();
        //userInfoDTO.setThreeAccount(userService.isWhiteDistributor(userInfoDTO));
        //在2.0的商户名单内,前端才跳转到2.0网址
        userInfoDTO.setSecondAccount(userService.isSecondWhiteDistributor(userInfoDTO));
        userInfoDTO.setOneAccount(userService.isOneWhiteDistributor(userInfoDTO));
        Integer typeCount = merchantEquipmentService.typeCount(userInfoDTO.getAdUserId()).getData();
        userInfoDTO.setIsHere(
                typeCount != null ? typeCount > 0 : false
                );
        return success(userInfoDTO);
    }

    @PostMapping("/modifyPassword")
    @AuthorityResource(name = "修改密码", value = "mb_modifyPassword", seq = 4, role = "Saas_Merchant", parentValue = "MechantUser")
    public BaseResponse modifyPassword(@ModelAttribute(value = "param") JSONObject param) {
        AdUserDTO adUserDTO = JSON.toJavaObject(param, AdUserDTO.class);
        String error = ValidatorUtils.validDTO(adUserDTO);
        if(StringUtils.isNotBlank(error)) {
            return error(ResponseCodeEnum.PARAMETER_ERROR.getCode(), error);
        }

        String newPassword = adUserDTO.getPassword();
        String repeatPassword = adUserDTO.getRepeatPassword();
        if (!validParameter(repeatPassword)) {
            return error(ResponseCodeEnum.FAIL.getCode(), "请再次输入新密码！");
        }
        if (!newPassword.equals(repeatPassword)) {
            return error(ResponseCodeEnum.FAIL.getCode(), "新密码不一致，请重新输入！");
        }
        Matcher m = p.matcher(newPassword);
        if(!m.matches()){
            return error(ResponseCodeEnum.FAIL.getCode(), "密码需要6-20位的字符组合！");
        }
        BaseResponse response = userService.modifyPassword(adUserDTO);
        return success(response.getData());
    }

    @PostMapping("/menu/switchVisibility")
    @AuthorityResource(name = "切换菜单可视状态", value = "mb_switchVisibility", seq = 9, role = "Saas_Merchant", parentValue = "MechantUser")
    public BaseResponse menuSwitchVisibility(@Valid @ModelAttribute(value = "param") JSONObject param){
        MenuSwitchVisibilityDTO visibilityDTO = JSON.toJavaObject(param, MenuSwitchVisibilityDTO.class);
        BaseResponse response = merchantUserService.menuSwitchVisibility(visibilityDTO);
        return success(response.getData());
    }

    @PostMapping("/getAuthMenus")
    @AuthorityResource(name = "获取用户权限菜单", value = "mb_getAuthMenus", seq = 10, role = "Saas_Merchant", parentValue = "MechantUser")
    public BaseResponse getAuthMenus(@ModelAttribute(value = "param") JSONObject param){
        MerchantAuthMenuGetDTO menuGetDTO = JSON.toJavaObject(param, MerchantAuthMenuGetDTO.class);
        return success(userService.getUserAuthMenu(menuGetDTO));
    }

    @GetMapping("/shieldingFunction")
    @AuthorityResource(name = "是否屏蔽充电桩设备功能", value = "mb_shieldingFunction", seq = 10, role = "Saas_Merchant", parentValue = "MechantUser")
    public BaseResponse<Boolean> shieldingFunction(){

        AdUserInfoDTO userInfoDTO = getCurrentUser();

        return success(userService.isOneWhiteDistributor(userInfoDTO));
    }

}
