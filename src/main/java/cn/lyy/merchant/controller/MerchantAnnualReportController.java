package cn.lyy.merchant.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.lyy_switch_api.cache.SwitchCache;
import cn.lyy.lyy_switch_api.dto.SwitchMemoryDTO;
import cn.lyy.lyy_switch_api.enums.SwitchKey;
import cn.lyy.merchant.api.service.MerchantEquipmentService;
import cn.lyy.merchant.api.service.MerchantWhiteClient;
import cn.lyy.merchant.constants.BooleanEnum;
import cn.lyy.merchant.constants.BusinessExceptionEnums;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.DaMerchantAnnualReportYiVO;
import cn.lyy.merchant.dto.MerchantAnnualReportDto;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.microservice.MerchantService;
import cn.lyy.merchant.service.MerchantAnnualReportService;
import cn.lyy.merchant.utils.RemoteResponseUtils;
import cn.lyy.tools.constants.LyyDistributorConstants.WhiteListType;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * <AUTHOR> =￣ω￣=
 * @date 2022/12/2
 */
@RestController
@RequestMapping("/rest/merchant/annual/report")
@Slf4j
public class MerchantAnnualReportController extends BaseController {

    @Autowired
    private MerchantAnnualReportService merchantAnnualReportService;
    
    @Autowired
    private MerchantWhiteClient merchantWhiteClient;
    
    @Autowired
    private MerchantEquipmentService merchantEquipmentService;

    @Value("${merchant.annual.report.config}")
    private String configJson;

    @Value("#{'${merchant.annual.report.equipment.list}'.split(',')}")
    private List<String> equipmentTypeList;
    
    @Value("${merchant.annual.report.whiteCheck:YES}")
    private String whiteCheck;

    @GetMapping("")
//    @AuthorityResource(name = "获取商家年度报告", value = "mb_merchant_annual_report", role = "Saas_Merchant")
    public BaseResponse merchantAnnualReport() {
        AdUserInfoDTO currentUser = getCurrentUser();
        Boolean isApprover = Optional.ofNullable(currentUser).map(u -> Optional.ofNullable(u.getIsApprover()).orElse(false)).orElse(false);
        BaseResponse response = new BaseResponse();
        if (!isApprover) {
            throw new BusinessException(BusinessExceptionEnums.MERCHANT_ANNUAL_REPORT_SUB_ACCOUNT_NOT_PERMIT);
        }
        DaMerchantAnnualReportYiVO daMerchantAnnualReportYiVo = merchantAnnualReportService.getByMerchantId(currentUser.getAdOrgId());
        daMerchantAnnualReportYiVo.setUserId(currentUser.getAdUserId());
        response.setData(daMerchantAnnualReportYiVo);
        return response;
    }

    @PostMapping("/cache")
//    @AuthorityResource(name = "缓存商家年度报告", value = "mb_merchant_annual_report_cache", role = "Saas_Merchant")
    public BaseResponse cacheReportByMerchantIds(@RequestBody MerchantAnnualReportDto dto) {
        merchantAnnualReportService.cacheReportByMerchantIds(dto.getMerchantIds());
        return new BaseResponse();
    }

    @PostMapping("/cache/delete")
//    @AuthorityResource(name = "删除商家年度报告的缓存", value = "mb_merchant_annual_report_delCac", role = "Saas_Merchant")
    public BaseResponse deleteCacheReportByMerchantIds(@RequestBody MerchantAnnualReportDto dto) {
        merchantAnnualReportService.deleteCacheReportByMerchantIds(dto.getMerchantIds());
        return new BaseResponse();
    }

    @PostMapping("/cache/popUps/delete")
    public BaseResponse deleteCachePopUpsByMerchantId(@RequestBody MerchantAnnualReportDto dto) {
        merchantAnnualReportService.deletePopUpsCache(dto.getMerchantIds().get(0), dto.getDateStr());
        return new BaseResponse();
    }

    @GetMapping("/config")
//    @AuthorityResource(name = "获取所需协议列表",value = "mb_merchant_annual_report_config",role = "ROLE_MERCHANT_NOT_LOGIN")
    public BaseResponse getDefaultConfig() {
        BaseResponse baseResponse = new BaseResponse();
        baseResponse.setData(configJson);
        return baseResponse;
//        return configJson;
    }

    @GetMapping("/equipment/type/count")
    public BaseResponse<Boolean> getEquipmentTypeCount() {
        BaseResponse<Boolean> response = new BaseResponse<>();
        response.setData(false);

        // 主账号校验
        if (Objects.equals(Boolean.FALSE, getCurrentUser().getIsApprover())) {
            return response;
        }
        
        if (CollUtil.isEmpty(equipmentTypeList)) {
            return response;
        }
        
        // 开关校验
        SwitchMemoryDTO aSwitch = SwitchCache.getSwitch(SwitchKey.PLAY_MERCHANT_ANNUAL_REPORT_SWITCH);
        if (BooleanEnum.NO.getValue().equals(aSwitch.getValue())) {
            if (log.isDebugEnabled()) {
                log.debug("【娱乐年度商家报告】 开关关闭，全部不可访问");
            }
            return response;
        }

        if (BooleanEnum.YES.getName().equalsIgnoreCase(whiteCheck)) {
            // 白名单校验
            Boolean isWhiteDistributor = RemoteResponseUtils.getData(merchantWhiteClient.isWhiteDistributor(getAdOrgIdNotNull(),
                    WhiteListType.PLAY_MERCHANT_ANNUAL_REPORT_WHITE.getValue()), Boolean.FALSE);
            if (Boolean.FALSE.equals(isWhiteDistributor)) {
                if (log.isDebugEnabled()) {
                    log.debug("【娱乐年度商家报告】 非白名单商家，不可访问");
                }
                return response;
            }
        }
        
        // 娱乐类设备校验
        int equipmentCount = RemoteResponseUtils.getData(merchantEquipmentService
                .countEquipmentByMerchantIdAndTypeValues(getAdOrgIdNotNull(), equipmentTypeList), 0);

        if (equipmentCount <= 0) {
            return response;
        }
        
        // 是否符合其他弹窗条件
        response.setData(merchantAnnualReportService.isPopUps(String.valueOf(getAdOrgIdNotNull())));
        return response;
    }

}
