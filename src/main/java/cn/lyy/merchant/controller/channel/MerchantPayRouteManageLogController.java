package cn.lyy.merchant.controller.channel;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.utils.text.JSONObjectUtils;
import cn.lyy.merchant.api.service.channel.MerchantPayRouteManageLogClient;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.mchchannel.MerchantPayRouteBindGroupLogPageDTO;
import cn.lyy.merchant.dto.mchchannel.MerchantPayRouteLogModifyPageDTO;
import cn.lyy.merchant.dto.mchchannel.request.MerchantPayRouteModifyLogDetailPageQuery;
import cn.lyy.merchant.dto.mchchannel.request.MerchantPayRouteModifyLogPageQuery;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @date: 2023/4/1
 * @author: YL
 */
@Api(tags = "收款设置日志")
@Slf4j
@RestController
@RequestMapping("/rest/merchant/pay-route/manage-log")
public class MerchantPayRouteManageLogController extends BaseController {

    @Autowired
    private MerchantPayRouteManageLogClient merchantPayRouteManageLogClient;

    @ApiOperation("日志列表")
    @GetMapping("/page")
    public BaseResponse<PageInfo<MerchantPayRouteLogModifyPageDTO>> logPage(@Valid MerchantPayRouteModifyLogPageQuery query) {
        if (log.isDebugEnabled()) {
            log.debug("[收款设置日志] 日志列表:{}", JSONObjectUtils.toJSONString(query));
        }
        
        query.setDistributorId(getAdOrgIdNotNull());
        
        return merchantPayRouteManageLogClient.logPage(query);
    }

    @ApiOperation("卡绑定场地记录列表")
    @GetMapping("/page/bind-groups")
    public BaseResponse<PageInfo<MerchantPayRouteBindGroupLogPageDTO>> bindGroupsPage(@Valid MerchantPayRouteModifyLogDetailPageQuery query) {
        if (log.isDebugEnabled()) {
            log.debug("[收款设置日志] 卡绑定场地记录列表:{}", JSONObjectUtils.toJSONString(query));
        }
        
        return merchantPayRouteManageLogClient.bindGroupsPage(query);
    }
    
}
