package cn.lyy.merchant.controller.channel;

import static java.util.Optional.ofNullable;

import cn.hutool.core.collection.CollUtil;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.base.utils.text.JSONObjectUtils;
import cn.lyy.income.dto.utils.AddressTypeUtil;
import cn.lyy.merchant.api.service.AdOrgClient;
import cn.lyy.merchant.api.service.MerchantWhiteClient;
import cn.lyy.merchant.api.service.channel.MerchantPayRouteManageClient;
import cn.lyy.merchant.constants.WhiteDistributorEnum;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.Channel.MerchantPayRouteCardBindGroupsRequest;
import cn.lyy.merchant.dto.Channel.MerchantPayRouteGroupBindCardRequest;
import cn.lyy.merchant.dto.mchchannel.request.BankCardGroupPageQuery;
import cn.lyy.merchant.dto.mchchannel.request.BankCardQuery;
import cn.lyy.merchant.dto.mchchannel.request.GroupBindInfoQuery;
import cn.lyy.merchant.dto.mchchannel.request.MerchantBankCardBindGroupReqDTO;
import cn.lyy.merchant.dto.mchchannel.request.PreferredCardUpdateReqDTO;
import cn.lyy.merchant.dto.mchchannel.response.BankCardGroupPageDTO;
import cn.lyy.merchant.dto.mchchannel.response.MerchantGroupWithBankInfoRespDTO;
import cn.lyy.merchant.dto.mchchannel.response.MerchantPayBankCardRespDTO;
import cn.lyy.merchant.dto.merchant.AdOrgDTO;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.utils.RemoteResponseUtils;
import cn.lyy.merchant.utils.ResponseCheckUtil;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @date: 2023/4/1
 * @author: YL
 */
@Api(tags = "收款设置")
@Slf4j
@RestController
@RequestMapping("/rest/merchant/pay-route/manage")
public class MerchantPayRouteManageController extends BaseController {

    private static final String UNKNOWN_NAME = "未知名称";

    @Autowired
    private MerchantPayRouteManageClient merchantPayRouteManageClient;

    @Autowired
    private MerchantWhiteClient merchantWhiteClient;

    @Autowired
    private AdOrgClient adOrgClient;

    @Value("${multiAccount.SkipCheck:false}")
    private Boolean skipCheckWhite;

    @ApiOperation("获取卡列表")
    @GetMapping("/all-bank-cards")
    public BaseResponse<List<MerchantPayBankCardRespDTO>> listAllBankCard(BankCardQuery query) {
        if (log.isDebugEnabled()) {
            log.debug("[收款设置] 获取卡列表:{}", JSONObjectUtils.toJSONString(query));
        }

        Long adOrgIdNotNull = getAdOrgIdNotNull();

        checkWhite(adOrgIdNotNull);

        query.setDistributorId(adOrgIdNotNull);

        return merchantPayRouteManageClient.listAllBankCard(query);
    }

    @ApiOperation("获取卡绑定场地列表--收款设置卡已绑定场地")
    @GetMapping("/page/bank-groups")
    public BaseResponse<PageInfo<BankCardGroupPageDTO>> bankGroupPage(@Valid BankCardGroupPageQuery query) {
        if (log.isDebugEnabled()) {
            log.debug("[收款设置] 获取卡绑定场地列表:{}", JSONObjectUtils.toJSONString(query));
        }

        Long adOrgIdNotNull = getAdOrgIdNotNull();

        checkWhite(adOrgIdNotNull);

        query.setDistributorId(adOrgIdNotNull);

        return merchantPayRouteManageClient.bankGroupPage(query);
    }

    @ApiOperation("获取可选场地列表（包含场地收款信息）--收款设置卡选择收款场地处")
    @GetMapping("/page/groups/select")
    public BaseResponse<PageInfo<MerchantGroupWithBankInfoRespDTO>> groupWithBankInfoSelectPage(@Valid BankCardGroupPageQuery query) {
        if (log.isDebugEnabled()) {
            log.debug("[收款设置] 获取场地列表（包含场地收款信息）:{}", JSONObjectUtils.toJSONString(query));
        }

        Long adOrgIdNotNull = getAdOrgIdNotNull();

        checkWhite(adOrgIdNotNull);

        query.setDistributorId(adOrgIdNotNull);
        query.setCountEquipment(false);

        return merchantPayRouteManageClient.groupWithBankInfoPage(query);
    }

    @ApiOperation("获取场地列表（包含场地收款信息）--收款设置按场地设置处")
    @GetMapping("/page/groups")
    public BaseResponse<PageInfo<MerchantGroupWithBankInfoRespDTO>> groupWithBankInfoPage(@Valid BankCardGroupPageQuery query) {
        if (log.isDebugEnabled()) {
            log.debug("[收款设置] 获取场地列表（包含场地收款信息）:{}", JSONObjectUtils.toJSONString(query));
        }

        Long adOrgIdNotNull = getAdOrgIdNotNull();

        checkWhite(adOrgIdNotNull);

        query.setDistributorId(adOrgIdNotNull);
        query.setCountEquipment(true);

        BaseResponse<PageInfo<MerchantGroupWithBankInfoRespDTO>> response = merchantPayRouteManageClient.groupWithBankInfoPage(query);
        ofNullable(ResponseCheckUtil.getDataSkipError(response)).map(PageInfo::getList)
                .filter(CollUtil::isNotEmpty)
                .ifPresent(groups -> groups.forEach(group -> ofNullable(group.getAddressType())
                        .ifPresent(o -> group.setAddressTypeName(AddressTypeUtil.getAddressTypeContainAlipay(o))))
                );

        return response;
    }

    @ApiOperation("设置默认卡")
    @PostMapping("/card/preference")
    public BaseResponse<Void> setPreferredCard(@RequestBody @Valid PreferredCardUpdateReqDTO request) {
        if (log.isDebugEnabled()) {
            log.debug("[收款设置] 设置默认卡:{}", JSONObjectUtils.toJSONString(request));
        }

        AdUserInfoDTO currentUser = getCurrentUser();

        checkWhite(currentUser.getAdOrgId());

        request.setDistributorId(currentUser.getAdOrgId());
        request.setOperatorId(currentUser.getAdUserId());
        request.setOperatorName(getCurrentUserName(currentUser));

        return merchantPayRouteManageClient.setPreferredCard(request);
    }

    @ApiOperation("卡绑定多场地")
    @PostMapping("/card/bind-groups")
    public BaseResponse<Void> bindGroups2Card(@RequestBody @Valid MerchantPayRouteCardBindGroupsRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("[收款设置] 卡绑定多场地:{}", JSONObjectUtils.toJSONString(request));
        }
        AdUserInfoDTO currentUser = getCurrentUser();

        checkWhite(currentUser.getAdOrgId());

        MerchantBankCardBindGroupReqDTO dto = new MerchantBankCardBindGroupReqDTO();
        dto.setSwiftPassMerchantId(request.getSwiftPassMerchantId());
        dto.setEquipmentGroupIds(request.getEquipmentGroupIds());
        dto.setExcludeGroupIds(request.getExcludeGroupIds());

        dto.setDistributorId(currentUser.getAdOrgId());
        dto.setOperatorId(currentUser.getAdUserId());
        dto.setOperatorName(getCurrentUserName(currentUser));

        return merchantPayRouteManageClient.bindGroups2Card(dto);
    }

    @ApiOperation("场地绑定卡")
    @PostMapping("/group/bind-card")
    public BaseResponse<Void> bindGroups2Card(@RequestBody @Valid MerchantPayRouteGroupBindCardRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("[收款设置] 场地绑定卡:{}", JSONObjectUtils.toJSONString(request));
        }

        AdUserInfoDTO currentUser = getCurrentUser();

        checkWhite(currentUser.getAdOrgId());

        MerchantBankCardBindGroupReqDTO dto = new MerchantBankCardBindGroupReqDTO();
        dto.setSwiftPassMerchantId(request.getSwiftPassMerchantId());
        dto.setEquipmentGroupIds(Collections.singletonList(request.getEquipmentGroupId()));

        dto.setDistributorId(currentUser.getAdOrgId());
        dto.setOperatorId(currentUser.getAdUserId());
        dto.setOperatorName(getCurrentUserName(currentUser));

        return merchantPayRouteManageClient.bindGroups2Card(dto);
    }

    @ApiOperation("获取场地收场设置信息-场地编辑处用")
    @GetMapping({"/group/bound-pay"})
    public BaseResponse<MerchantPayBankCardRespDTO> groupBoundPayInfo(@Valid GroupBindInfoQuery query) {

        Long adOrgIdNotNull = getAdOrgIdNotNull();

        checkWhite(adOrgIdNotNull);

        query.setDistributorId(adOrgIdNotNull);

        return merchantPayRouteManageClient.groupBoundPayInfo(query);
    }

    private String getCurrentUserName(AdUserInfoDTO currentUser) {
        if (Objects.equals(Boolean.TRUE, currentUser.getIsApprover())) {
            BaseResponse<AdOrgDTO> adOrg = adOrgClient.getById(currentUser.getAdOrgId());
            return ofNullable(RemoteResponseUtils.getData(adOrg)).map(AdOrgDTO::getName)
                    .orElseGet(() -> UNKNOWN_NAME + currentUser.getAdUserId());
        }
        
        return ofNullable(currentUser.getUserName()).orElseGet(() -> UNKNOWN_NAME + currentUser.getAdUserId());
    }

    private void checkWhite(Long distributorId) {
        if (Objects.equals(Boolean.TRUE, skipCheckWhite)) {
            return;
        }

        Boolean isWhite = ResponseCheckUtil.getData(
                merchantWhiteClient.isWhiteDistributor(distributorId, WhiteDistributorEnum.SINGLE_MERCHANT_MULTI_ACCOUNT.getType()));

        if (!isWhite) {
            throw new BusinessException("非白名单商家");
        }
    }
}
