package cn.lyy.merchant.controller;

import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.base.dto.Status;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.equipment.constant.DeviceTypeConstant;
import cn.lyy.equipment.constant.ThroughOperation;
import cn.lyy.equipment.dto.SettingEquipmentInfo;
import cn.lyy.equipment.dto.equipment.*;
import cn.lyy.equipment.dto.machine.Button;
import cn.lyy.equipment.dto.machine.Setting;
import cn.lyy.equipment.dto.operation.OperationParam;
import cn.lyy.equipment.dto.query.EquipmentNumber;
import cn.lyy.equipment.dto.through.ThroughInfo;
import cn.lyy.equipment.sdk.BoxKit;
import cn.lyy.equipment.service.EquipmentRedisService;
import cn.lyy.equipment.service.IEquipmentService;
import cn.lyy.equipment.service.SettingsService;
import cn.lyy.merchant.api.service.MerchantEquipmentService;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.WashFaultStatusDTO;
import cn.lyy.merchant.dto.equipment.EquipmentUpdateRequestDTO;
import cn.lyy.merchant.dto.merchant.response.MerchantEquipmentUniqueCodeDTO;
import cn.lyy.merchant.dto.request.MerchantEquipmentRequest;
import cn.lyy.merchant.dto.response.EquipmentResponseDTO;
import cn.lyy.merchant.dto.response.MerchantEquipmentDTO;
import cn.lyy.merchant.service.IEquipmentBusinessService;
import cn.lyy.merchant.service.equipment.EquipmentTypeService;
import cn.lyy.merchant.service.setting.FactoryHandler;
import cn.lyy.merchant.service.setting.FactoryHandlerStrategy;
import cn.lyy.merchant.util.MachineUtil;
import cn.lyy.tools.constants.open.FunctionConstants;
import cn.lyy.tools.equipment.DistributedInfo;
import cn.lyy.tools.equipment.LyyConstant;
import cn.lyy.tools.equipment.OpenSettingInfo;
import cn.lyy.tools.redis.RedisKeys;
import cn.lyy.tools.redis.RedisLock;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.lyy.merchant.service.setting.FactoryHandler.FACTORY_HANDLER_SUFFIX;
import static java.util.Optional.ofNullable;

/**
 * <AUTHOR> {<EMAIL>}
 * @date 2020/11/2 23:02
 **/
@RestController
@RequestMapping("/rest/equipment")
@Slf4j
public class EquipmentBusinessController extends BaseController {

    private  static final String WASHER_FAULT_STATUS_KEY = "washer:fault:status:key:";

    private static final String EQUIP_SETTING_KEY_PREFIX = "Esetting_open_";

    @Autowired
    private IEquipmentBusinessService equipmentBusinessService;

    @Autowired
    private EquipmentRedisService equipmentRedisService;

    @Autowired
    private IEquipmentService equipmentService;

    @Resource
    private EquipmentTypeService equipmentTypeService;

    @Autowired
    private SettingsService settingsService;

    @Autowired
    private FactoryHandlerStrategy factoryHandlerStrategy;

    @Autowired
    private MerchantEquipmentService merchantEquipmentService;

    /**
     * 通过uniqueCode 查询设备信息
     * @param uniqueCode
     * @return
     */
    @GetMapping("/info/byCode")
    @AuthorityResource(name = "根据code查询设备信息", value = "mb_equipment_by_code", seq = 1, role = "Saas_Merchant", parentValue = "Equipment")
    public BaseResponse<EquipmentDTO> getByUniqueCode(@RequestParam("uniqueCode") String uniqueCode) {
        BaseResponse<EquipmentDTO> response = new BaseResponse<>();
        EquipmentDTO respDTO = equipmentBusinessService.getByUniqueCode(uniqueCode);
        response.setCode(ResponseCodeEnum.SUCCESS.getCode());
        response.setData(respDTO);
        return response;
    }

    /**
     * 查询设备信息
     * @param value
     * @return
     */
    @GetMapping(value = "/info/value",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @AuthorityResource(name = "根据value查询设备信息", value = "mb_equipment_by_value", seq = 2, role = "Saas_Merchant", parentValue = "Equipment")
    public BaseResponse<EquipmentDTO> getByValue(@RequestParam("value") String value) {
        BaseResponse<EquipmentDTO> response = new BaseResponse<>();
        log.debug("查询设备信息:{}", value);
        EquipmentDTO respDTO = equipmentBusinessService.getByValue(value);
        response.setCode(ResponseCodeEnum.SUCCESS.getCode());
        response.setData(respDTO);
        return response;
    }

    @GetMapping("/info/detail/byId")
    @AuthorityResource(name = "根据id查询设备信息", value = "mb_equipment_by_id", seq = 3, role = "Saas_Merchant", parentValue = "Equipment")
    public BaseResponse<EquipmentResponseDTO> getEquipmentDetailByValue(@RequestParam("equipmentId") Long equipmentId){
        BaseResponse<EquipmentResponseDTO> response = new BaseResponse<>();
        log.debug("查询设备详情信息:{}", equipmentId);
        response.setData(equipmentBusinessService.getEquipmentDetailById(equipmentId));
        return response;
    }

    @PostMapping("/list")
    @AuthorityResource(name = "查询设备列表", value = "mb_equipment_list", seq = 4, role = "Saas_Merchant", parentValue = "Equipment")
    public BaseResponse<MerchantEquipmentDTO> selectEquipment(@RequestBody MerchantEquipmentRequest request) {
        log.debug("查询设备列表:{}", request);
        request.setAdUser(getAdUserIdNotNull());
        request.setDistributor(getAdOrgIdNotNull());
        MerchantEquipmentDTO result = equipmentBusinessService.selectEquipment(request);
        BaseResponse<MerchantEquipmentDTO> response = new BaseResponse<>();
        response.setCode(ResponseCodeEnum.SUCCESS.getCode());
        response.setData(result);
        return response;
    }

    @PostMapping(value = "/manager/equipmentList",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @AuthorityResource(name = "查询场地及设备列表", value = "mb_equipment_manager_list", seq = 5, role = "Saas_Merchant", parentValue = "Equipment")
    public BaseResponse<MerchantEquipmentDTO> equipmentCount(@RequestBody MerchantEquipmentRequest request) {
        log.debug("超过400台设备,设备列表查询:{}", request);
        request.setAdUser(getAdUserIdNotNull());
        request.setDistributor(getAdOrgIdNotNull());
        MerchantEquipmentDTO result = equipmentBusinessService.equipmentCount(request);
        return ResponseUtils.success(result);
    }


    @GetMapping("/getPositionInfoByEquipmentId")
    @AuthorityResource(name = "根据设备Id获取仓位信息", value = "mb_positionInfo_Id", seq = 6, role = "Saas_Merchant", parentValue = "Equipment")
    public BaseResponse<List<PositionDTO>> getPositionInfoByEquipmentId(@RequestParam("equipmentId") Long equipmentId){
        log.debug("根据设备Id获取仓位信息:{}", equipmentId);
        BaseResponse<List<PositionDTO>> response = new BaseResponse<>();
        response.setData(equipmentBusinessService.getPositionInfoByEquipmentId(equipmentId));
        return response;
    }

    /**
     * 更新设备信息--机台号、备注信息和禁用设备
     * @param equipmentUpdateDTO
     * @return
     */
    @PostMapping("/update")
    @AuthorityResource(name = "更新设备信息--机台号、备注信息和禁用设备", value = "mb_equipment_update", seq = 7, role = "Saas_Merchant", parentValue = "Equipment")
    public BaseResponse updateEquipmentInfo(@RequestBody EquipmentUpdateDTO equipmentUpdateDTO){
        log.debug("更新设备信息:{}", equipmentUpdateDTO);
        BaseResponse response = equipmentBusinessService.updateEquipmentInfo(equipmentUpdateDTO,getAdOrgIdNotNull(),getAdUserIdNotNull());
        return response;
    }

    @PostMapping("/unbind")
    @AuthorityResource(name = "设备解绑", value = "mb_equipment_unbind", seq = 8, role = "Saas_Merchant", parentValue = "Equipment")
    public BaseResponse unbind(@RequestBody EquipmentUpdateRequestDTO dto){
        log.debug("设备解绑,{}",dto);
        BaseResponse baseResponse = new BaseResponse();
        //设备解绑
        if(dto == null || dto.getEquipmentId() == null){
            baseResponse.setCode(Status.STATUS_PARAMETER_ERROR);
            baseResponse.setMessage("请选择要解绑的设备");
            return baseResponse;
        }
        if(StringUtils.isBlank(dto.getUniqueCode())){
            baseResponse.setCode(Status.STATUS_PARAMETER_ERROR);
            baseResponse.setMessage("请传入设备唯一码");
            return baseResponse;
        }
        dto.setAdOrgId(getAdOrgIdNotNull());
        dto.setAdUserId(getAdOrgIdNotNull());
        equipmentBusinessService.unbind(dto);
        return baseResponse;
    }

    @GetMapping("/getEquipmentSignal")
    @AuthorityResource(name = "获取设备实时信号值", value = "mb_getEquipmentSignal", seq = 9, role = "Saas_Merchant", parentValue = "Equipment")
    public BaseResponse<Integer> getEquipmentSignal(@RequestParam("uniqueCode") String uniqueCode){
        log.debug("根据设备uniqueCode获取设备实时信号值:{}", uniqueCode);
        BaseResponse<Integer> baseResponse = new BaseResponse<>();
        baseResponse.setData(equipmentBusinessService.getEquipmentSignal(uniqueCode));
        return baseResponse;
    }

    /**
     * 更新故障状态
     * @param washFaultStatusDTO
     * @return
     */
    @PostMapping("/updateFaultStatus")
    @AuthorityResource(name = "更新故障状态", value = "mb_updateFaultStatus", seq = 10, role = "Saas_Merchant", parentValue = "Equipment")
    public BaseResponse<Integer> updateFaultStatus(@RequestBody @Valid WashFaultStatusDTO washFaultStatusDTO){
        BaseResponse<Integer> baseResponse = new BaseResponse<>();
        String webSocketToken = getWebSocketToken();
        Integer result = equipmentBusinessService.updateFaultStatus(washFaultStatusDTO,webSocketToken);
        boolean lock = RedisLock.lock(WASHER_FAULT_STATUS_KEY + washFaultStatusDTO.getGroupId(), washFaultStatusDTO.getGroupId() + "", 60);
        if(!lock){
            baseResponse.setCode(Status.STATUS_PARAMETER_ERROR);
            baseResponse.setMessage("操作频繁,请稍后再试");
        }
        baseResponse.setData(result);
        return baseResponse;
    }

    /**
     * 根据设备类型id获取设备类型的扩展功能列表
     * @param equipmentTypeId
     * @return
     */
    @GetMapping("/manager/extendFunctionList")
    @AuthorityResource(name = "扩展功能列表", value = "mb_manager_extendFunctionList", seq = 11, role = "Saas_Merchant", parentValue = "Equipment")
    public BaseResponse extendFunctionList(@RequestParam("equipmentTypeId")Long equipmentTypeId){
        return ResponseUtils.success(equipmentBusinessService.extendFunctionList(equipmentTypeId));
    }


    /**
     * 开放平台查询设备功能
     * @param info
     *  新旧区别： para 换成data  异常返回500，新的返回 -1 ？
     *  原来返回的是result ，新的是 code
     * @return
     */
    @PostMapping("/lyyopen/query")
    public BaseResponse equipmentFunction(@RequestBody OpenSettingInfo info) {
        log.debug("开放平台查询设备功能:{}", info);
        BaseResponse baseResponse = new BaseResponse();
        try {
            if (checkEquipNotExist(info.getUniqueCode())) {
                baseResponse.setCode(Status.STATUS_FAIL);
                baseResponse.setMessage("找不到该设备");
                return baseResponse;
            }
            // 删除参数缓存
            equipmentRedisService.del(getCompleteKey(info.getUniqueCode(), info.getFunctionCode(), info.getData()));

            OperationParam param = new OperationParam();
            param.setUniqueCode(info.getUniqueCode());
            param.setIdentity(info.getFunctionCode());
            param.setParam(info.getData());

            boolean success = ofNullable(equipmentService.operation(param)).filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode()).isPresent();
            if (!success) {
                baseResponse.setCode(Status.STATUS_FAIL);
                baseResponse.setMessage("查询参数失败，请确保设备网络畅通");
                return baseResponse;
            }
        } catch (Exception e) {
            log.error("开放平台查询设备功能异常:{}", e);
            baseResponse.setCode(Status.STATUS_500);
            baseResponse.setMessage("系统错误，请稍后重试");
        }
        return baseResponse;
    }



    /**
     * 开放平台加载数据
     *  新旧区别： para 换成data  异常返回500，新的返回 -1 ？
     *  原来返回的是result ，新的是code
     */
    @GetMapping("/lyyopen/load")
    @ResponseBody
    public BaseResponse load(String uniqueCode, String functionCode, String data) {
        log.debug("开放平台加载数据:{}", uniqueCode);
        BaseResponse baseResponse = new BaseResponse();
        try {
            if (checkEquipNotExist(uniqueCode)) {
                baseResponse.setCode(Status.STATUS_FAIL);
                baseResponse.setMessage("找不到该设备");
                return baseResponse;
            }
            String key = getCompleteKey(uniqueCode, functionCode, data);
            String settingInfo = ofNullable(equipmentRedisService.getString(key)).filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode())
                    .map(BaseResponse::getData).orElse(null);
            log.debug("开放平台参数KEY:{},settingInfo: {}", key, settingInfo);
            if (StringUtils.isBlank(settingInfo)) {
                baseResponse.setCode(Status.STATUS_FAIL);
                baseResponse.setMessage("未查询到数据");
                return baseResponse;
            }
            Gson gson = new Gson();
            if (FunctionConstants.BSYS_SAAS_QUERY_PARAM.equals(functionCode)) {
                HashMap<String, String> hashMap = gson.fromJson(settingInfo, new TypeToken<HashMap<String, String>>() {
                }.getType());
                baseResponse.setData(hashMap);
            } else if (FunctionConstants.BSYS_SAAS_QUERY_FUNCTION.equals(functionCode)) {
                List<Object> list = gson.fromJson(settingInfo, new TypeToken<List<Object>>() {
                }.getType());
                baseResponse.setData(list);
            }
        } catch (Exception e) {
            log.debug("开放平台加载数据异常:{}", uniqueCode);
            baseResponse.setCode(Status.STATUS_500);
            baseResponse.setMessage("系统错误，请稍后重试");
        }
        return baseResponse;
    }

    private String getCompleteKey(String uniqueCode, String functionCode, String data) {
        String key = String.format(EQUIP_SETTING_KEY_PREFIX+"%s_%s", functionCode, uniqueCode);
        if (StringUtils.isNotBlank(data)) {
            HashMap<String, String> dataMap = new Gson().fromJson(data, new TypeToken<HashMap<String, String>>() {
            }.getType());
            String cmd = dataMap.get("cmd");
            log.info("dataMap: {}", dataMap);
            if (StringUtils.isNotBlank(cmd)) {
                key += "_" + cmd;
            }
        }
        return key;
    }
    private boolean checkEquipNotExist(String uniqueCode) {
        if (StringUtils.isBlank(uniqueCode)) {
            return true;
        }
        Long adOrgId = getAdOrgIdNotNull();
        EquipmentDTO equipment = equipmentBusinessService.getByUniqueCode(uniqueCode);
        if (equipment == null || !adOrgId.equals(equipment.getDistributorId())) {
            return true;
        }
        return false;
    }
    /**
     * 读取控制板参数
     */
    @RequestMapping(value = "/cxEquipment", method = RequestMethod.GET)
    @ResponseBody
    public BaseResponse cxEquipment(@RequestParam String value) {
        return checkEquipInfo(value, 0);
    }

    private BaseResponse checkEquipInfo(String value, int query) {
        BaseResponse baseResponse = new BaseResponse();
        EquipmentDTO equipment = equipmentBusinessService.getByValue(value);
        if (judgeEquipExistOrDeviceType(equipment)){
            baseResponse.setCode(Status.STATUS_FAIL);
            baseResponse.setMessage("error value.");
            return baseResponse;
        }
        boolean success = fetchEquipment(equipment.getUniqueCode(), query);
        if (!success) {
            baseResponse.setCode(Status.STATUS_FAIL);
            baseResponse.setMessage("send error.");
        }
        return baseResponse;
    }

    /**
     * 读取控制板参数(新协议)
     */
    @GetMapping("/cxNewEquipment")
    @ResponseBody
    public BaseResponse cxNewEquipment(@RequestParam String value,@RequestParam String data) {
        return checkEquipInfo(value, Integer.parseInt(data));
    }

    /**
     * 读取控制板参数（新协议）
     * 更改入参与返回数据
     */
    @RequestMapping(value = "/readNewEquipment")
    @ResponseBody
    public BaseResponse readNewEquipment(@RequestParam String value , @RequestParam String typeValue) {
        BaseResponse baseResponse = new BaseResponse();
        EquipmentDTO equipment = equipmentBusinessService.getByValue(value);

        if(log.isDebugEnabled()){
            log.debug("设备 {} 获取到的信息为 ->pulseWidth1:{},pulseInterval1:{},battery:{}",value
                    ,equipment.getPulseWidthOne(),equipment.getPulseIntervalOne(),equipment.getStandbyStatus());
        }

        if (judgeEquipExistOrDeviceType(equipment)){
            baseResponse.setCode(Status.STATUS_FAIL);
            baseResponse.setMessage("error value.");
            return baseResponse;
        }
        Map<String, Object> result = new HashMap<>();
            result.put("pulseWidth1", equipment.getPulseWidthOne());
            result.put("pulseInterval1", equipment.getPulseIntervalOne());
            result.put("pulseWidth2", equipment.getPulseWidthTwo());
            result.put("pulseInterval2", equipment.getPulseIntervalTwo());
            result.put("battery", equipment.getBattery());
            result.put("gift", equipment.getSupportGift());
            result.put("drag", equipment.getSupportGrip());
            result.put("paperModel", equipment.getPaperModel());
            result.put("interfaceType", equipment.getInterfaceType());
            result.put("interfaceBaudRate", equipment.getInterfaceBaudRate());
            if ("ETL".equals(typeValue) || "XYJ".equals(typeValue) || "AMY".equals(typeValue) || "CDZ".equals(typeValue)) {
                String pulsePatternName = ofNullable(equipmentService.getEquipmentParamDef(equipment.getEquipmentTypeId(), equipment.getPulseWidthOne(), equipment.getPulseIntervalOne()))
                        .filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode())
                        .map(BaseResponse::getData)
                        .map(EquipmentParamDefDTO::getPulsePatternName)
                        .orElse("自定义");
                result.put("pulsePatternName", pulsePatternName);

            }
            baseResponse.setData(result);

            return baseResponse;
    }

    private boolean judgeEquipExistOrDeviceType(EquipmentDTO equipment) {
        if (equipment == null || equipment.getDeviceType() == null || DeviceTypeConstant.YXB.equals(equipment.getDeviceType())) {
            return true;
        }
        return false;
    }

    /**
     * 读取设备控制板参数
     *
     * @param uniqueCode 设备编号
     * @param query      查询参数
     * @return 是否成功
     */
    private boolean fetchEquipment(String uniqueCode, int query) {
        ThroughInfo through = new ThroughInfo();
        through.setUniqueCode(uniqueCode);
        through.setOperation(ThroughOperation.FETCH_CONTROL_PANEL.getCode());
        DistributedInfo distributedInfo = new DistributedInfo();
        distributedInfo.setUniqueCode(uniqueCode);
        distributedInfo.setQuery(query);
        through.setData(distributedInfo);
        BaseResponse response = equipmentService.through(through);
        return ofNullable(response).filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode())
                .map(r -> (boolean) r.getData()).orElse(false);
    }

    /**
     * 设置终端设备参数
     * 修改了入参方式
     */
    @RequestMapping(value = "/szTerminalEquipment", method = RequestMethod.GET)
    @ResponseBody
    public BaseResponse szTerminalEquipment(@RequestParam String value,@RequestParam String functionCode,
                                            @RequestParam String coins) {
        BaseResponse baseResponse = new BaseResponse();
        EquipmentDTO equipment = equipmentBusinessService.getByValue(value);
        if (judgeEquipExistOrDeviceType(equipment)){
            baseResponse.setCode(Status.STATUS_FAIL);
            baseResponse.setMessage("设备编码错误");
            return baseResponse;
        }
            byte[] data = new byte[12];
            if (functionCode.equals("0x03")) {
                int coin = Integer.parseInt(coins);
                data[1] = (byte) coin;
            }
            ThroughInfo through = new ThroughInfo();
            through.setOperation(ThroughOperation.SET_TERMINAL_EQUIPMENT.getCode());
            through.setUniqueCode(equipment.getUniqueCode());
            cn.lyy.tools.equipment.SettingEquipmentInfo info = new cn.lyy.tools.equipment.SettingEquipmentInfo();
            info.setFunctionCode(functionCode);
            info.setData(data);
            through.setInfo(info);
            BaseResponse response = equipmentService.through(through);
            Boolean success = ofNullable(response).filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode())
                    .map(r -> (boolean) r.getData()).orElse(false);
            if (!success) {
                baseResponse.setCode(Status.STATUS_FAIL);
                baseResponse.setMessage("设备网络不在线，设置失败");
            }

        return baseResponse;
    }
    /**
     * 设置域名
     */
    @RequestMapping(value = "/szDomain", method = RequestMethod.GET)
    @ResponseBody
    public BaseResponse szDomain(@RequestParam String domain,@RequestParam String value) {
        BaseResponse baseResponse = new BaseResponse();
        if (StringUtils.isEmpty(domain)) {
            baseResponse.setCode(Status.STATUS_FAIL);
            baseResponse.setMessage("error domain.");
            return baseResponse;
        }
            String uniqueCode = null;
            if (!StringUtils.isEmpty(value)) {
                EquipmentDTO equipment = equipmentBusinessService.getByValue(value);
                if (equipment != null) {
                    uniqueCode = equipment.getUniqueCode();
                }
            }
            ThroughInfo through = new ThroughInfo();
            through.setUniqueCode(uniqueCode);
            through.setOperation(ThroughOperation.UPDATE_DOMAIN.getCode());
            Map<String, Object> param = new HashMap<>();
            param.put("uniqueCode", uniqueCode);
            param.put("domain", domain);
            through.setParam(param);
            BaseResponse response = equipmentService.through(through);
            Boolean success = ofNullable(response).filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode())
                    .map(r -> (boolean) r.getData()).orElse(false);
            if (!success) {
                baseResponse.setCode(Status.STATUS_FAIL);
                baseResponse.setMessage("send error.");
            }
        return baseResponse;
    }


    /**
     * 设置投币数、退礼数、上分数
     */
    @RequestMapping(value = "/setDataStatistics", method = RequestMethod.GET)
    @ResponseBody
    public BaseResponse setDataStatistics(@RequestParam String equipmentValue,@RequestParam String data,@RequestParam String value) {
        BaseResponse baseResponse = new BaseResponse();
        try {
            if (StringUtils.isEmpty(equipmentValue)) {
                baseResponse.setCode(Status.STATUS_FAIL);
                baseResponse.setMessage("param error.");
                return baseResponse;
            }
            EquipmentDTO equipment = equipmentBusinessService.getByValue(equipmentValue);
                HashMap<String, Object> distributeMap = new HashMap<>();
                distributeMap.put("data", data);
                distributeMap.put("value", value);
                String uniqueCode = equipment.getUniqueCode();
                distributeMap.put("unique_code", uniqueCode);

                ThroughInfo through = new ThroughInfo();
                through.setParam(distributeMap);
                through.setUniqueCode(uniqueCode);
                through.setOperation(ThroughOperation.SET_DATA_STATISTICS.getCode());
                BaseResponse response = equipmentService.through(through);
                Boolean success = ofNullable(response).filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode())
                        .map(r -> (boolean) r.getData()).orElse(false);
                if (!success) {
                    baseResponse.setCode(Status.STATUS_FAIL);
                    baseResponse.setMessage("send error.");
                    return baseResponse;
                }
        } catch (Exception e) {
            log.error("设置投币数、退礼数、上分数异常:{}", e);
            baseResponse.setCode(Status.STATUS_500);
            baseResponse.setMessage("send error.");
        }
        return baseResponse;
    }
    /**
     * 查询设备参数
     */
    @GetMapping(value = "/queryEquipment/param")
    @ResponseBody
    public BaseResponse queryEquipmentSetting(HttpServletRequest request, @RequestParam("factory") String factory) {
        BaseResponse baseResponse = new BaseResponse();
        try {
            String uniqueCode = request.getParameter("uniqueCode");
            if (checkEquipNotExist(uniqueCode)) {
                baseResponse.setCode(Status.STATUS_FAIL);
                baseResponse.setMessage("找不到该设备");
                return baseResponse;
            }

            String queryFunctionCode = request.getParameter("queryFunctionCode");
            if (queryFunctionCode == null) {
                queryFunctionCode = "5";
            }
            if (LyyConstant.SW_FACTORY.equals(factory) || LyyConstant.XN_FACTORY.equals(factory) ||
                    LyyConstant.GL_FACTORY.equals(factory) || LyyConstant.TY_FACTORY.equals(factory)) {
                equipmentRedisService.set(RedisKeys.SEND_FUNCTION_CODE_TYPE, "query");
                equipmentRedisService.del(RedisKeys.EQUIPMENT_SETTING + uniqueCode);

                byte[] packet = new byte[2];
                SettingEquipmentInfo info = new SettingEquipmentInfo();
                info.setData(packet);
                info.setUniqueCode(uniqueCode);
                info.setFunctionCode(queryFunctionCode);
                info.setSendType("query");
                boolean success = BoxKit.queryEquipmentExtendParam(uniqueCode, JSONObject.toJSONString(info));
                if (!success) {
                    baseResponse.setCode(Status.STATUS_FAIL);
                    baseResponse.setMessage("查询参数失败，请确保设备网络畅通");
                    return baseResponse;
                }
            } else {
                baseResponse.setCode(Status.STATUS_FAIL);
                baseResponse.setMessage("查询参数错误");
                return baseResponse;
            }
        } catch (Exception e) {
            log.error("查询设备参数异常:{}", e);
            baseResponse.setCode(Status.STATUS_500);
            baseResponse.setMessage("系统错误，请稍后重试");
        }
        return baseResponse;
    }

    /**
     * 重启设备
     */
    @PostMapping("/rebootEquipment")
    @ResponseBody
    public BaseResponse rebootEquipment(HttpServletRequest request) {
        BaseResponse baseResponse = new BaseResponse();
        try {
            String uniqueCode = request.getParameter("uniqueCode");
            byte[] packet = new byte[2];
            packet[0] = (byte) 0;
            packet[1] = (byte) 0;
            SettingEquipmentInfo param = new SettingEquipmentInfo();
            param.setData(packet);
            param.setUniqueCode(uniqueCode);
            param.setFunctionCode("2");
            boolean result = settingsService.issue(param).getData();
            if (!result) {
                baseResponse.setCode(Status.STATUS_FAIL);
                baseResponse.setMessage("重启设备失败，请确保设备网络畅通");
                return baseResponse;
            }
        } catch (Exception e) {
            log.error("重启设备异常:{}", e);
            baseResponse.setCode(Status.STATUS_500);
            baseResponse.setMessage("系统错误，请稍后重试");
        }
        return baseResponse;
    }

    /**
     * 恢复出厂设置
     */
    @PostMapping("/resetEquipment")
    @ResponseBody
    public BaseResponse resetEquipment(HttpServletRequest request) {
        BaseResponse baseResponse = new BaseResponse();
        try {
            String uniqueCode = request.getParameter("uniqueCode");
            byte[] packet = new byte[2];
            packet[0] = (byte) 0;
            packet[1] = (byte) 0;
            SettingEquipmentInfo param = new SettingEquipmentInfo();
            param.setData(packet);
            param.setUniqueCode(uniqueCode);
            param.setFunctionCode("11");
            boolean result = settingsService.issue(param).getData();
            if (!result) {
                baseResponse.setCode(Status.STATUS_FAIL);
                baseResponse.setMessage("设备恢复出厂设置失败，请确保设备网络畅通");
                return baseResponse;
            }
        } catch (Exception e) {
            log.error("恢复出厂设置异常:{}", e);
            baseResponse.setCode(Status.STATUS_500);
            baseResponse.setMessage("系统错误，请稍后重试");
        }
        return baseResponse;
    }
    /**
     * 终端设置
     */
    @ResponseBody
    @PostMapping("/setEquipment")
    public BaseResponse setEquipment(@RequestParam String uniqueCode, @RequestParam String functionCode) {
        BaseResponse baseResponse = new BaseResponse();
        try {
            if (checkEquipNotExist(uniqueCode)) {
                baseResponse.setCode(Status.STATUS_FAIL);
                baseResponse.setMessage("找不到该设备");
                return baseResponse;
            }
            byte[] packet = new byte[2];
            packet[0] = (byte) 0;
            packet[1] = (byte) 0;
            String setting = "setting";
            SettingEquipmentInfo param = new SettingEquipmentInfo();
            param.setData(packet);
            param.setUniqueCode(uniqueCode);
            param.setFunctionCode(functionCode);
            param.setSendType(setting);
            boolean result = settingsService.issue(param).getData();
            if (!result) {
                baseResponse.setCode(Status.STATUS_FAIL);
                baseResponse.setMessage("设置失败，请确保设备网络畅通");
                return baseResponse;
            }
        } catch (Exception e) {
            log.error("终端设置异常:{}", e);
            baseResponse.setCode(Status.STATUS_500);
            baseResponse.setMessage("系统错误，请稍后重试");
        }
        return baseResponse;
    }

    /**
     * 设置控制板参数
     */
    @RequestMapping(value = "/szEquipment", method = RequestMethod.GET)
    @ResponseBody
    public BaseResponse szEquipment(HttpServletRequest request) {
        BaseResponse baseResponse = new BaseResponse();
        String value = request.getParameter("value");
        EquipmentDTO equipment = equipmentBusinessService.getByValue(value);
        if(log.isDebugEnabled()){
            log.debug("设备 {} 获取到的信息为 ->pulseWidth1:{},pulseInterval1:{},battery:{}",value
                    ,equipment.getPulseWidthOne(),equipment.getPulseIntervalOne(), equipment.getBattery());
        }
         if (judgeEquipExistOrDeviceType(equipment)){
            baseResponse.setCode(Status.STATUS_FAIL);
            baseResponse.setMessage("设备编码错误");
            return baseResponse;
        }
            String pulseWidth = request.getParameter("pulseWidth");
            String pulseInterval = request.getParameter("pulseInterval");
            if (pulseWidth == null || pulseInterval == null) {
                baseResponse.setCode(Status.STATUS_FAIL);
                baseResponse.setMessage("脉冲宽度和脉冲间隔不能为空");
                return baseResponse;
            }
                try {
                    String battery = request.getParameter("battery");
                    String coins = request.getParameter("coins");
                    String gift = request.getParameter("gift");
                    cn.lyy.tools.equipment.SettingEquipmentInfo info = new cn.lyy.tools.equipment.SettingEquipmentInfo();
                    info.setUniqueCode(equipment.getUniqueCode());
                    info.setPulseWidth(Integer.parseInt(pulseWidth));
                    info.setPulseInterval(Integer.parseInt(pulseInterval));
                    info.setBattery(Integer.parseInt(battery));
                    info.setCoins(Integer.parseInt(coins));
                    info.setGift(Integer.parseInt(gift));
                    boolean success = setControlPanel(equipment.getUniqueCode(), info);
                    if (!success) {
                        baseResponse.setCode(Status.STATUS_FAIL);
                        baseResponse.setMessage("设备网络不在线，设置失败");
                        return baseResponse;
                    }
                } catch (Exception e) {
                    log.error("设置控制板参数异常:{}", e);
                    baseResponse.setCode(Status.STATUS_500);
                    baseResponse.setMessage("参数值错误，设置失败");
                }
        return baseResponse;
    }

    @RequestMapping(value = "/button/setting", method = RequestMethod.POST)
    @ResponseBody
    public BaseResponse setEquipmentButtonSetting(@RequestBody Map<String, Object> paraMap) {
        BaseResponse baseResponse = new BaseResponse();
        try {
            String uniqueCode = paraMap.get("uniqueCode").toString();
            int functionCode = Integer.parseInt(paraMap.get("functionCode").toString());
            equipmentRedisService.set(RedisKeys.SEND_FUNCTION_CODE_TYPE, "setting");
            String settingInfo = ofNullable(equipmentRedisService.getString(RedisKeys.EQUIPMENT_SETTING + uniqueCode))
                    .filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode()).map(BaseResponse::getData).orElse(null);
            if (settingInfo == null) {
                baseResponse.setCode(Status.STATUS_FAIL);
                baseResponse.setMessage("读取参数失败，请确保设备网络畅通");
                return baseResponse;
            }
            Setting settings = new Gson().fromJson(settingInfo, Setting.class);
            Button button = MachineUtil.buttonDecode(settings, paraMap);
            if (button == null) {
                baseResponse.setCode(Status.STATUS_FAIL);
                baseResponse.setMessage("读取功能参数失败，请确保设备网络畅通");
                return baseResponse;
            }
            SettingEquipmentInfo param = new SettingEquipmentInfo();
            param.setData(MachineUtil.getButtonBytes(button, settings.getMachineTypeCode()));
            param.setUniqueCode(uniqueCode);
            param.setFunctionCode(String.valueOf(functionCode));
            param.setSendType("setting");
            boolean result = settingsService.issue(param).getData();
            if (!result) {
                baseResponse.setCode(Status.STATUS_FAIL);
                baseResponse.setMessage("设置参数失败，请确保设备网络畅通");
                return baseResponse;
            }
        } catch (Exception e) {
            log.error("/button/setting异常:{}", e);
            baseResponse.setCode(Status.STATUS_500);
            baseResponse.setMessage("系统错误，请稍后重试");
        }

        return baseResponse;
    }
    /**
     * 设置设备参数
     */
    @RequestMapping(value = "/setting", method = RequestMethod.POST)
    @ResponseBody
    public BaseResponse setEquipmentSetting(HttpServletRequest request, @RequestParam("factory") String factory, @RequestBody Map<String, Object> paraMap) {
        BaseResponse baseResponse = new BaseResponse();
        Long adUserId = getAdUserIdNotNull();
        Long adOrgId = getAdOrgIdNotNull();
        try {
            String uniqueCode = paraMap.get("uniqueCode").toString();
            EquipmentDTO equipment = equipmentBusinessService.getByUniqueCode(uniqueCode);
            if (equipment == null || !adOrgId.equals(equipment.getDistributorId())) {
                baseResponse.setCode(Status.STATUS_FAIL);
                baseResponse.setMessage("找不到该设备");
                return baseResponse;
            }
            String settingInfo = settingsService.settingsQuery(uniqueCode).getData();

            if (settingInfo == null) {
                baseResponse.setCode(Status.STATUS_FAIL);
                baseResponse.setMessage("读取参数失败，请确保设备网络畅通");
                return baseResponse;
            }
            String type = request.getParameter("type");
                FactoryHandler handler = factoryHandlerStrategy.getHandler(factory.concat(FACTORY_HANDLER_SUFFIX));
                if (handler == null) {
                    log.warn("对应bean处理工厂方法不存在! factory:{}",factory);
                    baseResponse.setCode(Status.STATUS_FAIL);
                    baseResponse.setMessage("设置参数错误");
                    return baseResponse;
                }
                return handler.handle(equipment,settingInfo,paraMap,type, adUserId);
        } catch (Exception e) {
            log.error("设置设备参数异常:{}", e);
            baseResponse.setCode(Status.STATUS_500);
            baseResponse.setMessage("系统错误，请稍后重试");
        }
        return baseResponse;
    }

    private boolean setControlPanel(String uniqueCode, cn.lyy.tools.equipment.SettingEquipmentInfo info) {
        ThroughInfo through = new ThroughInfo();
        through.setUniqueCode(uniqueCode);
        through.setOperation(ThroughOperation.SET_CONTROL_PARAM.getCode());
        through.setInfo(info);
        BaseResponse response = equipmentService.through(through);
        return ofNullable(response).filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode())
                .map(r -> (boolean) r.getData()).orElse(false);
    }

   /**
     * 获取指定商家已注册过的支持批量处理的设备类型
     * @return
     */
    @GetMapping("/getBatchRegEquipmentType")
    @AuthorityResource(name="获取支持批量处理的设备类型",value = "mb_getBatchRegEquipmentType",role ="Saas_Merchant",parentValue = "Equipment")
    public BaseResponse<List<cn.lyy.merchant.dto.equipment.EquipmentTypeDTO>> getSupportBatchRegisterEquipmentType(){
        Long orgId = this.getAdOrgIdNotNull();
        List<cn.lyy.merchant.dto.equipment.EquipmentTypeDTO> list =  equipmentTypeService.getSupportBatchRegisterEquipmentType(orgId);
        return ResponseUtils.success(list);
    }

    @GetMapping("/getEquipmentNum")
    @AuthorityResource(name = "获取用户设备总数信息（在线数、总数）", value = "mb_getEquipmentNum", seq = 12, role = "Saas_Merchant", parentValue = "MechantUser")
    public BaseResponse<Map<String,Integer>> getEquipmentNum(){
        Long orgId = getAdOrgIdNotNull();
        Long adUserId= getAdUserIdNotNull();
        return success(equipmentBusinessService.onlineAndOfflineNumber(orgId, adUserId));
    }

    @PostMapping("/selectGroupEquipmentList")
    @AuthorityResource(name = "查询指定设备类型在场地的列表", value = "mb_group_equipment_list", seq = 13, role = "Saas_Merchant", parentValue = "Equipment")
    public BaseResponse<MerchantEquipmentDTO> selectGroupEquipmentList(@RequestBody MerchantEquipmentRequest request) {
        log.debug("查询指定设备类型在场地的列表:{}", request);
        request.setAdUser(getAdUserIdNotNull());
        request.setDistributor(getAdOrgIdNotNull());
        MerchantEquipmentDTO result = equipmentBusinessService.selectGroupEquipmentList(request);
        BaseResponse<MerchantEquipmentDTO> response = new BaseResponse<>();
        response.setCode(ResponseCodeEnum.SUCCESS.getCode());
        response.setData(result);
        return response;
    }

    /**
     * 查询场地设备信息，只显示场地及场地下面的信息，内容与list接口类似，但是没有其他详细的设备信息，
     * 主要用于某些选择场地的请求
     * @param request
     * @return
     */
    @PostMapping("/findGroupEquipment")
    @AuthorityResource(name = "查询场地设备信息", value = "mb_group_equipment", seq = 14, role = "Saas_Merchant", parentValue = "Equipment")
    public BaseResponse<MerchantEquipmentDTO> findGroupEquipment(@RequestBody MerchantEquipmentRequest request) {
        log.debug("查询场地设备信息:{}", request);
        request.setAdUser(getAdUserIdNotNull());
        request.setDistributor(getAdOrgIdNotNull());
        MerchantEquipmentDTO result = equipmentBusinessService.findGroupEquipment(request);
        BaseResponse<MerchantEquipmentDTO> response = new BaseResponse<>();
        response.setCode(ResponseCodeEnum.SUCCESS.getCode());
        response.setData(result);
        return response;
    }

}


