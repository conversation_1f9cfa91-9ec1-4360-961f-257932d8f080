package cn.lyy.merchant.controller.productmatrix;

import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.productmatrix.ExamineGroupResult;
import cn.lyy.merchant.dto.productmatrix.PackageDistributionResult;
import cn.lyy.merchant.dto.productmatrix.ReportConfigResult;
import cn.lyy.merchant.dto.productmatrix.ReportInfoResult;
import cn.lyy.merchant.service.productmatrix.ProductMatrixService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description
 * <AUTHOR>
 * @date 2023/4/14 11:30
*/
@Api(tags = "智能导购")
@Slf4j
@RestController
@RequestMapping("/rest/product-matrix")
public class ProductMatrixController extends BaseController {

    @Autowired
    private ProductMatrixService productMatrixService;

    private static final String LOG_PRE = "智能导购：";
    
    @ApiOperation("获取报告内容配置")
    @GetMapping("/get-report-config")
    @AuthorityResource(name = "获取报告内容配置", value = "p_matrix_get_report_config", seq = 1, role = "Saas_Merchant", parentValue = "Common")
    public BaseResponse<ReportConfigResult> getReportConfig(@RequestParam(value = "probationId", defaultValue = "-1") Integer probationId) {
        log.info("{}获取报告内容配置查询参数，merchantId={}，merchantId={}", LOG_PRE, getCurrentUser().getAdOrgId(), probationId);
        return success(productMatrixService.getReportConfig(getCurrentUser().getAdOrgId(), probationId));
    }

    @ApiOperation("获取参与测试场地")
    @GetMapping("/get-examine-group")
    @AuthorityResource(name = "获取参与测试场地", value = "p_matrix_get_examine_group", seq = 2, role = "Saas_Merchant", parentValue = "Common")
    public BaseResponse<ExamineGroupResult> getExamineGroup(@RequestParam(value = "probationId", defaultValue = "-1") Integer probationId) {
        log.info("{}获取报告内容配置查询参数，merchantId={}，merchantId={}", LOG_PRE, getCurrentUser().getAdOrgId(), probationId);
        return success(productMatrixService.getExamineGroup(getCurrentUser().getAdOrgId(), probationId));
    }

    @ApiOperation("获取测试报告内容")
    @GetMapping("/get-report-info")
    @AuthorityResource(name = "获取测试报告内容", value = "p_matrix_get_report_info", seq = 3, role = "Saas_Merchant", parentValue = "Common")
    public BaseResponse<ReportInfoResult> getReportInfo(@RequestParam(value = "probationId", defaultValue = "-1") Integer probationId) {
        log.info("{}获取报告内容配置查询参数，merchantId={}，merchantId={}", LOG_PRE, getCurrentUser().getAdOrgId(), probationId);
        return success(productMatrixService.getReportInfo(getCurrentUser().getAdOrgId(), probationId));
    }

    @ApiOperation("获取成功导购数据")
    @GetMapping("/get-package-distribution")
    @AuthorityResource(name = "获取成功导购数据", value = "p_matrix_get_pg_distribution", seq = 4, role = "Saas_Merchant", parentValue = "Common")
    public BaseResponse<PackageDistributionResult> getPackageDistribution(
            @RequestParam(value = "probationId", defaultValue = "-1") Integer probationId) {
        log.info("{}获取报告内容配置查询参数，merchantId={}，merchantId={}", LOG_PRE, getCurrentUser().getAdOrgId(), probationId);
        return success(productMatrixService.PackageDistributionResult(getCurrentUser().getAdOrgId(), probationId));
    }

    /*------------------------------免登录------------------------------*/

    @ApiOperation("获取报告内容配置免登录")
    @GetMapping("/not-auth/get-report-config")
    @AuthorityResource(name = "获取报告内容配置免登录", value = "p_matrix_get_report_config_na", seq = 5, role = "ROLE_MERCHANT_NOT_LOGIN", parentValue = "Common")
    public BaseResponse<ReportConfigResult> getReportConfigNotAuth(@RequestParam("merchantId") Long merchantId,
            @RequestParam(value = "probationId", defaultValue = "-1") Integer probationId) {
        log.info("{}获取报告内容配置免登录查询参数，merchantId={}，merchantId={}", LOG_PRE, merchantId, probationId);
        return success(productMatrixService.getReportConfig(merchantId, probationId));
    }

    @ApiOperation("获取参与测试场地免登录")
    @GetMapping("/not-auth/get-examine-group")
    @AuthorityResource(name = "获取参与测试场地免登录", value = "p_matrix_get_examine_group_na", seq = 6, role = "ROLE_MERCHANT_NOT_LOGIN", parentValue = "Common")
    public BaseResponse<ExamineGroupResult> getExamineGroupNotAuth(@RequestParam("merchantId") Long merchantId,
            @RequestParam(value = "probationId", defaultValue = "-1") Integer probationId) {
        log.info("{}获取报告内容配置免登录查询参数，merchantId={}，merchantId={}", LOG_PRE, merchantId, probationId);
        return success(productMatrixService.getExamineGroup(merchantId, probationId));
    }

    @ApiOperation("获取测试报告内容免登录")
    @GetMapping("/not-auth/get-report-info")
    @AuthorityResource(name = "获取测试报告内容免登录", value = "p_matrix_get_report_info_na", seq = 7, role = "ROLE_MERCHANT_NOT_LOGIN", parentValue = "Common")
    public BaseResponse<ReportInfoResult> getReportInfoNotAuth(@RequestParam("merchantId") Long merchantId,
            @RequestParam(value = "probationId", defaultValue = "-1") Integer probationId) {
        log.info("{}获取报告内容配置免登录查询参数，merchantId={}，merchantId={}", LOG_PRE, merchantId, probationId);
        return success(productMatrixService.getReportInfo(merchantId, probationId));
    }

    @ApiOperation("获取成功导购数据免登录")
    @GetMapping("/not-auth/get-package-distribution")
    @AuthorityResource(name = "获取成功导购数据免登录", value = "p_matrix_get_pg_distribution_na", seq = 8, role = "ROLE_MERCHANT_NOT_LOGIN", parentValue = "Common")
    public BaseResponse<PackageDistributionResult> getPackageDistributionNotAuth(@RequestParam("merchantId") Long merchantId,
            @RequestParam(value = "probationId", defaultValue = "-1") Integer probationId) {
        log.info("{}获取报告内容配置免登录查询参数，merchantId={}，merchantId={}", LOG_PRE, merchantId, probationId);
        return success(productMatrixService.PackageDistributionResult(merchantId, probationId));
    }
}
