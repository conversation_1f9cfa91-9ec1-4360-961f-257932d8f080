package cn.lyy.merchant.controller.permission;

import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.permission.AdPermissionResultDTO;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import cn.lyy.merchant.dto.user.UserExtentDTO;
import cn.lyy.merchant.service.permission.IPermissionService;
import com.lyy.error.exception.BusinessException;
import com.lyy.starter.common.resp.RespBody;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("rest/permission")
@Slf4j
public class PermissionController extends BaseController {

    @Autowired
    private IPermissionService permissionService;

    /**
     * 获取当前账号权限资源
     */
    @GetMapping("/access")
    public RespBody<AdPermissionResultDTO> authorityResources() {
        UserExtentDTO currentUser = getCurrentUserExtend();
        Long authorityUserId = currentUser.getAuthorityUserId();
        Long adOrgId = currentUser.getAdOrgId();
        return RespBody.ok(permissionService.authorityResources(authorityUserId, adOrgId,currentUser.getIsWalletSettlement()));
    }
}
