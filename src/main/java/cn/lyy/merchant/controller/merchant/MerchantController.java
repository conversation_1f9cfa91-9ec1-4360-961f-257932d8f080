package cn.lyy.merchant.controller.merchant;

import static java.util.Optional.ofNullable;

import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.base.utils.converter.CommonConverterTools;
import cn.lyy.merchant.api.service.EquipmentTransferClient;
import cn.lyy.merchant.api.service.MerchantBaseOptClient;
import cn.lyy.merchant.api.service.MerchantChannelLogClient;
import cn.lyy.merchant.api.service.MerchantWhiteClient;
import cn.lyy.merchant.api.service.PositionMessageClient;
import cn.lyy.merchant.api.service.SwiftpassMerchantClient;
import cn.lyy.merchant.api.service.merchant.MerchantFeignClientBean;
import cn.lyy.merchant.config.UmsGrayConfig;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.constants.MchBaseOptEnum;
import cn.lyy.merchant.dto.constants.MerchantChannelApplyTypeEnum;
import cn.lyy.merchant.dto.mch.request.MchBaseQueryAuditResultRequestDTO;
import cn.lyy.merchant.dto.mch.request.MchBaseQueryAuthExistRequestDTO;
import cn.lyy.merchant.dto.mch.request.MchBaseWechantAuthConfirmRequestDTO;
import cn.lyy.merchant.dto.merchant.LyySwiftpassMerchantDTO;
import cn.lyy.merchant.dto.merchant.PrimaryCategoryInitDTO;
import cn.lyy.merchant.dto.merchant.attach.dto.UserFeatureTourUpdateDTO;
import cn.lyy.merchant.dto.merchant.request.MerchantChannelNoAuthConfirmRequestDTO;
import cn.lyy.merchant.dto.positionmessage.MarkMsgReadDTO;
import cn.lyy.merchant.dto.positionmessage.MessageOrTodoDTO;
import cn.lyy.merchant.dto.positionmessage.MessageOrTodoPageDTO;
import cn.lyy.merchant.dto.request.GetAllMessageOrTodoDTO;
import cn.lyy.merchant.dto.request.GetTopMessageAndTodoDTO;
import cn.lyy.merchant.dto.request.MarkMessageReadDTO;
import cn.lyy.merchant.dto.request.MchBaseWechantAuthConfirmDTO;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import cn.lyy.merchant.schedule.ExportChargingOrderDataSchedule;
import cn.lyy.merchant.schedule.ExportChargingPileGatewayDataSchedule;
import cn.lyy.merchant.schedule.StatisticsPayOrderSchedule;
import cn.lyy.merchant.service.merchant.MerchantAppService;
import cn.lyy.merchant.service.merchant.MerchantMaterialService;
import cn.lyy.merchant.service.merchant.MerchantWhitelistSwitchService;
import cn.lyy.merchant.service.merchant.dto.MerchantInfoStatusDTO;
import cn.lyy.merchant.service.messagecenter.MessageAndTodoService;
import cn.lyy.merchant.utils.ResponseCheckUtil;
import cn.lyy.message.UmsPositionMessageClient;
import cn.lyy.message.dto.positionmessage.PositionMessageDTO;
import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.starter.common.resp.RespBody;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2021/1/4 17:53
 * </p>
 */
@RestController
@RequestMapping("/rest/merchant")
@Slf4j
public class MerchantController extends BaseController {

    @Autowired
    private MerchantBaseOptClient merchantBaseOptClient;

    @Autowired
    private MerchantWhiteClient merchantWhiteClient;
    @Autowired
    private MerchantFeignClientBean merchantClient;

    @Autowired
    private MerchantChannelLogClient merchantChannelLogClient;

    @Autowired
    private SwiftpassMerchantClient swiftpassMerchantClient;

    @Autowired
    private EquipmentTransferClient equipmentTransferClient;

    @Autowired
    private MerchantMaterialService merchantMaterialService;

    @Autowired
    private MessageAndTodoService messageAndTodoService;

    @Autowired
    private PositionMessageClient positionMessageClient;
    @Autowired
    private MerchantWhitelistSwitchService merchantWhitelistSwitchService;

    @Autowired
    private UmsPositionMessageClient umsPositionMessageClient;
    @Autowired
    private UmsGrayConfig umsGrayConfig;
    @Autowired
    private MerchantAppService merchantAppService;

    /**
     * 测试，后删
     */
    @Autowired
    private StatisticsPayOrderSchedule statisticsPayOrderSchedule;

    @GetMapping("/test")
    public BaseResponse<Boolean> test(@RequestParam("param") String param) {
        statisticsPayOrderSchedule.execute(param);
        return ResponseUtils.success();
    }

    @Resource
    private ExportChargingPileGatewayDataSchedule schedule;

    @GetMapping("/test/export")
    public BaseResponse<Boolean> testExport(@RequestParam("param") String param) {
        schedule.execute(param);
        return ResponseUtils.success();
    }

    @Resource
    private ExportChargingOrderDataSchedule chargingOrderDataSchedule;

    @GetMapping("/test/exportOrder")
    public BaseResponse<Boolean> exportOrder(@RequestParam("param") String param) {
        chargingOrderDataSchedule.execute(param);
        return ResponseUtils.success();
    }

    @PostMapping("/audit/review")
    @AuthorityResource(name = "资料审核状态", value = "mb_merchant_review", seq = 1, role = "Saas_Merchant", parentValue = "CertificateReview")
    public BaseResponse merchantBaseOpt() {
        MchBaseQueryAuditResultRequestDTO request = new MchBaseQueryAuditResultRequestDTO();
        //商户ID
        request.setDistributorId(getCurrentUser().getAdOrgId());
        //功能类型
        request.setFunctionType(MchBaseOptEnum.MCH_BASE_QUERYAUDITRESULT.getValue());

        return merchantBaseOptClient.merchantBaseOpt(request);
    }

    @PostMapping("/auth/review")
    @AuthorityResource(name = "实名认证", value = "mb_merchant_auth_review", seq = 1, role = "Saas_Merchant", parentValue = "CertificateReview")
    public BaseResponse merchantAuthReview() {
        MchBaseQueryAuthExistRequestDTO request = new MchBaseQueryAuthExistRequestDTO();
        //商户ID
        request.setDistributorId(getCurrentUser().getAdOrgId());
        //功能类型
        request.setFunctionType(MchBaseOptEnum.MCH_BASE_QUERYAUTHEXIST.getValue());

        return merchantBaseOptClient.merchantBaseOpt(request);
    }

    @PostMapping("/channel/wxAuthConfirm")
    @AuthorityResource(name = "商户渠道微信授权确认", value = "mb_merchant_wxconfirm", seq = 2, role = "Saas_Merchant", parentValue = "CertificateReview")
    public BaseResponse wxAuthConfirm(@RequestBody MchBaseWechantAuthConfirmDTO dto) {
        MchBaseWechantAuthConfirmRequestDTO request = CommonConverterTools.convert(MchBaseWechantAuthConfirmRequestDTO.class, dto);
        request.setFunctionType(MchBaseOptEnum.MCH_BASE_WECHATAUTHCONFIRM.getValue());
        request.setDistributorId(getCurrentUser().getAdOrgId());
        request.setType(MerchantChannelApplyTypeEnum.CHANNELAUTH_WX_HUIFU.getValue());
        return merchantBaseOptClient.merchantBaseOpt(request);
    }

    @ApiOperation(tags = "商家信息", value = "获取商家白名单")
    @GetMapping("/whiteList")
    @AuthorityResource(name = "获取白名单列表", value = "mb_merchant_white_list", seq = 3, role = "Saas_Merchant", parentValue = "AutoPassAuth")
    public BaseResponse whiteList() {
        return merchantWhiteClient.getAllWhiteByDistributorId(getCurrentUser().getAdOrgId());
    }


    @GetMapping("/white-by-type")
    // @AuthorityResource(name = "根据白名单编码查询是商家否在白名单", value = "mb_merchant_white_by_type", seq = 5, role = "ROLE_MERCHANT_FOR_ALL")
    public BaseResponse isWhiteByType(@RequestParam(value = "type") Integer type) {
        AdUserInfoDTO userInfoDTO = getCurrentUser();
        return success(merchantWhiteClient.isWhiteDistributor(userInfoDTO.getAdOrgId(), type));
    }

    @PostMapping("/real/name/confirm")
    @AuthorityResource(name = "实名认证确认", value = "mb_merchant_real_name_confirm", seq = 1, role = "Saas_Merchant", parentValue =
            "CertificateReview")
    public BaseResponse realNameConfirm() {
        MerchantChannelNoAuthConfirmRequestDTO request = new MerchantChannelNoAuthConfirmRequestDTO();
        request.setDistributorId(getCurrentUser().getAdOrgId());
        BaseResponse response = merchantChannelLogClient.authConfirm(request);
        ofNullable(response).map(BaseResponse::getCode).filter(c -> ResponseCodeEnum.SUCCESS.getCode() == c).ifPresent(code -> {
            log.info("[渠道实名认证确认]处理成功");
        });
        return response;
    }

    @GetMapping("/getLyySwiftpassMerchant")
    @AuthorityResource(name = "获取商户资料", value = "mb_merchant_swiftpass_list", role = "Saas_Merchant")
    public BaseResponse<List<LyySwiftpassMerchantDTO>> getLyySwiftpassMerchant() {
        return swiftpassMerchantClient.getLyySwiftpassMerchant(getCurrentUser().getAdOrgId());
    }

    /**
     * 新手任务绑定设备
     *
     * @return
     */
    @PostMapping("/hasFirstRegister")
    @AuthorityResource(name = "新手任务绑定设备", value = "mb_merchant_hasFirstRegister", role = "Saas_Merchant")
    public BaseResponse<Boolean> hasFirstRegister() {
        BaseResponse baseResponse = new BaseResponse();
        baseResponse.setData(messageAndTodoService.hasFirstRegister(getCurrentUser().getAdOrgId()));
        return baseResponse;
    }

    /**
     * 资料审核聚合状态
     *
     * @return
     */
    @PostMapping("/info/status")
    public BaseResponse<MerchantInfoStatusDTO> info() {
        BaseResponse baseResponse = new BaseResponse();
        baseResponse.setData(merchantMaterialService.getMerchantInfoStatus(getCurrentUser().getAdOrgId(), null, null));
        return baseResponse;
    }

    /**
     * 新手任务资料审核聚合状态
     *
     * @return
     */
    @PostMapping("/info/tip/status")
    public BaseResponse<MerchantInfoStatusDTO> infoTipStatus() {
        BaseResponse baseResponse = new BaseResponse();
        MerchantInfoStatusDTO merchantInfoTipStatus = merchantMaterialService.getMerchantInfoTipStatus(getCurrentUser().getAdOrgId(), null, null, Boolean.TRUE);
        if (log.isDebugEnabled()) {
            log.debug("新手任务资料审核聚合状态:{}", merchantInfoTipStatus);
        }
        baseResponse.setData(merchantInfoTipStatus);
        return baseResponse;
    }

    /**
     * 查询站内信详情
     */
    @GetMapping("/message/detail")
    @AuthorityResource(name = "查询站内信详情", value = "mb_merchant_getMessageDetail", role = "Saas_Merchant")
    public BaseResponse<PositionMessageDTO> getPositionDetail(@RequestParam(required = false) Long id,
            @RequestParam(required = false) String messageId) {
        log.info("[查询站内信详情],id:{},messageId:{}", id, messageId);
        if (Objects.isNull(id) && StringUtils.isBlank(messageId)) {
            return ResponseUtils.error(ResponseCodeEnum.PARAMETER_ERROR.getCode(), "参数错误");
        }
        AdUserInfoDTO currentUser = getCurrentUser();
        return ResponseUtils.success(messageAndTodoService.getPositionDetail(id, messageId, currentUser));
    }

    /**
     * 分页查询所有消息和待办，0为消息，1为待办
     */
    @PostMapping("/getAllMessageOrTodo")
    @AuthorityResource(name = "分页查询所有消息或待办", value = "mb_merchant_getALLMessageOrTodo", role = "Saas_Merchant")
    public BaseResponse<MessageOrTodoPageDTO> getAllMessageOrTodo(@RequestBody GetAllMessageOrTodoDTO getAllMessageOrTodoDTO) {
        getAllMessageOrTodoDTO.setMerchantId(getCurrentUser().getAdOrgId());
        getAllMessageOrTodoDTO.setAdUserId(getCurrentUser().getAdUserId());
        return ResponseUtils.success(messageAndTodoService.getAllMessageOrTodo(getAllMessageOrTodoDTO));
    }

    /**
     * 获取首页前5条消息和待办
     */
    @PostMapping("/getTopMessageAndTodo")
    @AuthorityResource(name = "获取首页前5条消息和待办", value = "mb_merchant_getTopMessageAndTodo", role = "Saas_Merchant")
    public BaseResponse<List<MessageOrTodoDTO>> getTopMessageAndTodo(@RequestBody GetTopMessageAndTodoDTO getTopMessageAndTodoDTO) {
        long start = System.currentTimeMillis();
        AdUserInfoDTO currentUser = getCurrentUser();
        getTopMessageAndTodoDTO.setMerchantId(currentUser.getAdOrgId());
        getTopMessageAndTodoDTO.setAdUserId(currentUser.getAdUserId());
        List<MessageOrTodoDTO> topMessageAndTodo = messageAndTodoService.getTopMessageAndTodo(getTopMessageAndTodoDTO);
        log.debug("adUserId:[{}], merchantId:[{}] getTopMessageAndTodo() ===> [{}]", currentUser.getAdUserId(), currentUser.getAdOrgId(), (System.currentTimeMillis() - start));
        return ResponseUtils.success(topMessageAndTodo);
    }

    /**
     * 标记消息为已读
     *
     * @param markMessageReadDTO
     * @return
     */
    @PostMapping("/markMsgRead")
    @AuthorityResource(name = "标记消息为已读", value = "mb_merchant_markMsgRead", role = "Saas_Merchant")
    public BaseResponse<Boolean> markMsgRead(@RequestBody MarkMessageReadDTO markMessageReadDTO) {
        MarkMsgReadDTO markMsgReadDTO = new MarkMsgReadDTO();
        markMsgReadDTO.setMerchantId(getCurrentUser().getAdOrgId());
        markMsgReadDTO.setAdUserId(getCurrentUser().getAdUserId());
        markMsgReadDTO.setMsgIdList(markMessageReadDTO.getMsgIdList());
        Boolean markReadSuccess;
        if (umsGrayConfig.isUmsGray(markMsgReadDTO.getMerchantId())) {
            cn.lyy.message.dto.positionmessage.MarkMsgReadDTO umsMarkMsgReadDTO
                    = new cn.lyy.message.dto.positionmessage.MarkMsgReadDTO();
            umsMarkMsgReadDTO.setMsgIdList(markMsgReadDTO.getMsgIdList());
            markReadSuccess = ResponseUtils.checkResponse(umsPositionMessageClient.markMsgRead(umsMarkMsgReadDTO));
        } else {
            markReadSuccess = ResponseUtils.checkResponse(positionMessageClient.markMsgRead(markMsgReadDTO));
        }
        BaseResponse<Boolean> response = new BaseResponse<>();
        response.setData(markReadSuccess);
        return response;
    }

    /**
     * 将指定商户添加或移除指定白名单
     */
    @PostMapping("/switchMerchantWhitelist")
    public BaseResponse<Boolean> switchMerchantWhitelist(@RequestParam("type") Integer type) {
        Boolean flag = merchantWhitelistSwitchService.switchMerchantWhitelist(getCurrentUser().getAdOrgId(), type);
        BaseResponse<Boolean> response = new BaseResponse<>();
        response.setData(flag);
        return response;
    }

    /**
     * 用户功能引导状态更新
     */
    @PostMapping("/feature-tour")
    public BaseResponse<Boolean> changeFeatureTourStatus(@RequestBody UserFeatureTourUpdateDTO dto) {
        log.info("用户功能引导状态更新");
        AdUserInfoDTO currentUser = getCurrentUser();
        dto.setMerchantId(currentUser.getAdOrgId());
        dto.setAdUserId(currentUser.getAdUserId());
        Boolean data = ResponseCheckUtil.getData(merchantClient.changeFeatureTourStatus(dto));
        return ResponseUtils.success(data);
    }

    /**
     * 商户主营品类注册
     */
    @PostMapping("/primary-category/initial")
    public RespBody<Boolean> initPrimaryCategoryFunction(@RequestBody PrimaryCategoryInitDTO dto) {
        AdUserInfoDTO currentUser = getCurrentUser();
        if (BooleanUtils.isNotTrue(currentUser.getIsApprover())) {
            return RespBody.fail(GlobalErrorCode.NO_PERMISSION);
        }
        Boolean result = merchantAppService.initPrimaryCategoryFunction(dto, currentAuthorityUserId(), currentUser.getAdOrgId());
        return RespBody.ok(result);
    }

}

