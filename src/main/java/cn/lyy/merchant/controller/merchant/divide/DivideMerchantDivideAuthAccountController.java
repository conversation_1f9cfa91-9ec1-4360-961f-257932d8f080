package cn.lyy.merchant.controller.merchant.divide;

import static java.util.Optional.ofNullable;

import cn.hutool.core.util.StrUtil;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.merchant.api.service.AdOrgClient;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.merchant.AdOrgNewDTO;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import com.github.pagehelper.PageInfo;
import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.payment.divide.bff.interfaces.MerchantDivideAuthAccountClient;
import com.lyy.payment.divide.bff.request.MerchantDivideAssociationAddReqDTO;
import com.lyy.payment.divide.bff.request.MerchantDivideAssociationAuthDetailReqDTO;
import com.lyy.payment.divide.bff.request.MerchantDivideAssociationCancelAuthDetailReqDTO;
import com.lyy.payment.divide.bff.request.MerchantDivideAssociationDeleteReqDTO;
import com.lyy.payment.divide.bff.request.MerchantDivideAssociationDivideUserSelectReqDTO;
import com.lyy.payment.divide.bff.request.MerchantDivideAuthAccountAddReqDTO;
import com.lyy.payment.divide.bff.request.MerchantDivideAuthAccountListReqDTO;
import com.lyy.payment.divide.bff.request.MerchantDivideAuthAccountMainListReqDTO;
import com.lyy.payment.divide.bff.response.MerchantDivideAssociationAuthDetailResDTO;
import com.lyy.payment.divide.bff.response.MerchantDivideAssociationAuthUserDTO;
import com.lyy.payment.divide.bff.response.MerchantDivideAssociationDivideUserSelectResDTO;
import com.lyy.payment.divide.bff.response.MerchantDivideAuthAccountAddResDTO;
import com.lyy.payment.divide.bff.response.MerchantDivideAuthAccountListResDTO;
import com.lyy.payment.divide.bff.response.MerchantDivideAuthAccountMainListResDTO;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 授权分成方看收益
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/rest/divide-auth")
@Slf4j
public class DivideMerchantDivideAuthAccountController extends BaseController {

    @Autowired
    private MerchantDivideAuthAccountClient merchantDivideAuthAccountClient;
    @Autowired
    private AdOrgClient adOrgClient;

    @PostMapping("/account-list")
    public BaseResponse<PageInfo<MerchantDivideAuthAccountListResDTO>> authAccountList(
            @RequestBody MerchantDivideAuthAccountListReqDTO dto) {
        AdUserInfoDTO currentUser = getCurrentUser();
        dto.setDistributorId(currentUser.getAdOrgId());
        return ofNullable(merchantDivideAuthAccountClient.authAccountList(dto)).map(e -> {
            if (GlobalErrorCode.OK.getCode().equals(e.getCode())) {
                return new BaseResponse(e.getBody());
            } else {
                return new BaseResponse(ResponseCodeEnum.FAIL.getCode(), e.getMessage());
            }
        }).orElse(new BaseResponse(ResponseCodeEnum.FAIL.getCode(), "接口异常"));
    }

    @PostMapping("/account-main-list")
    public BaseResponse<PageInfo<MerchantDivideAuthAccountMainListResDTO>> authAccountMainList(
            @RequestBody MerchantDivideAuthAccountMainListReqDTO dto) {
        AdUserInfoDTO currentUser = getCurrentUser();
        dto.setDivideDistributorId(currentUser.getAdOrgId());
        return ofNullable(merchantDivideAuthAccountClient.authAccountMainList(dto)).map(e -> {
            if (GlobalErrorCode.OK.getCode().equals(e.getCode())) {
                return new BaseResponse(e.getBody());
            } else {
                return new BaseResponse(ResponseCodeEnum.FAIL.getCode(), e.getMessage());
            }
        }).orElse(new BaseResponse(ResponseCodeEnum.FAIL.getCode(), "接口异常"));
    }

    @PostMapping("/account-detail")
    public BaseResponse<MerchantDivideAssociationAuthDetailResDTO> authAccountDetail(
            @RequestBody MerchantDivideAssociationAuthDetailReqDTO dto) {
        AdUserInfoDTO currentUser = getCurrentUser();
        dto.setDistributorId(currentUser.getAdOrgId());
        return ofNullable(merchantDivideAuthAccountClient.authAccountDetail(dto)).map(e -> {
            if (GlobalErrorCode.OK.getCode().equals(e.getCode())) {
                return new BaseResponse(e.getBody());
            } else {
                return new BaseResponse(ResponseCodeEnum.FAIL.getCode(), e.getMessage());
            }
        }).orElse(new BaseResponse(ResponseCodeEnum.FAIL.getCode(), "接口异常"));
    }

    @PostMapping("/cancel-list")
    public BaseResponse<PageInfo<MerchantDivideAssociationAuthUserDTO>> cancelAuthList(
            @RequestBody MerchantDivideAssociationCancelAuthDetailReqDTO dto) {
        AdUserInfoDTO currentUser = getCurrentUser();
        dto.setDistributorId(currentUser.getAdOrgId());
        return ofNullable(merchantDivideAuthAccountClient.cancelAuthList(dto)).map(e -> {
            if (GlobalErrorCode.OK.getCode().equals(e.getCode())) {
                return new BaseResponse(e.getBody());
            } else {
                return new BaseResponse(ResponseCodeEnum.FAIL.getCode(), e.getMessage());
            }
        }).orElse(new BaseResponse(ResponseCodeEnum.FAIL.getCode(), "接口异常"));

    }

    @PostMapping("/select-divide-user")
    public BaseResponse<List<MerchantDivideAssociationDivideUserSelectResDTO>> selectDivideUser(
            @RequestBody MerchantDivideAssociationDivideUserSelectReqDTO dto) {
        AdUserInfoDTO currentUser = getCurrentUser();
        dto.setDistributorId(currentUser.getAdOrgId());
        return ofNullable(merchantDivideAuthAccountClient.selectDivideUser(dto)).map(e -> {
            if (GlobalErrorCode.OK.getCode().equals(e.getCode())) {
                return new BaseResponse(e.getBody());
            } else {
                return new BaseResponse(ResponseCodeEnum.FAIL.getCode(), e.getMessage());
            }
        }).orElse(new BaseResponse(ResponseCodeEnum.FAIL.getCode(), "接口异常"));
    }

    @PostMapping("/add-auth-account")
    public BaseResponse<MerchantDivideAuthAccountAddResDTO> addAuthAccount(@RequestBody MerchantDivideAuthAccountAddReqDTO dto) {
        AdUserInfoDTO currentUser = getCurrentUser();
        if (currentUser.getIsApprover()) {
            AdOrgNewDTO adOrgNewDTO = adOrgClient.getNewById(currentUser.getAdOrgId()).getData();
            dto.setDistributorName(adOrgNewDTO.getName());
        } else {
            dto.setDistributorName(currentUser.getUserName());
        }
        if (currentUser.getPhone().length() > 11) {
            dto.setDistributorPhone(StrUtil.sub(currentUser.getPhone(), 0, 11));
        } else {
            dto.setDistributorPhone(currentUser.getPhone());
        }
        dto.setDistributorId(currentUser.getAdOrgId())
                .setAdUserId(currentUser.getAdUserId());
        return ofNullable(merchantDivideAuthAccountClient.addAuthAccount(dto)).map(e -> {
            if (GlobalErrorCode.OK.getCode().equals(e.getCode())) {
                return new BaseResponse(e.getBody());
            } else {
                return new BaseResponse(ResponseCodeEnum.FAIL.getCode(), e.getMessage());
            }
        }).orElse(new BaseResponse(ResponseCodeEnum.FAIL.getCode(), "接口异常"));
    }

    @PostMapping("/add-auth-divide-user")
    public BaseResponse<Boolean> addAuthDivideUser(@RequestBody MerchantDivideAssociationAddReqDTO dto) {
        AdUserInfoDTO currentUser = getCurrentUser();
        dto.setDistributorId(currentUser.getAdOrgId())
                .setAdUserId(currentUser.getAdUserId());
        return ofNullable(merchantDivideAuthAccountClient.addAuthDivideUser(dto)).map(e -> {
            if (GlobalErrorCode.OK.getCode().equals(e.getCode())) {
                return new BaseResponse(e.getBody());
            } else {
                return new BaseResponse(ResponseCodeEnum.FAIL.getCode(), e.getMessage());
            }
        }).orElse(new BaseResponse(ResponseCodeEnum.FAIL.getCode(), "接口异常"));
    }

    @PostMapping("/delete-auth-divide-user")
    public BaseResponse<Boolean> deleteAuthDivideUser(@RequestBody MerchantDivideAssociationDeleteReqDTO dto) {
        AdUserInfoDTO currentUser = getCurrentUser();
        dto.setDistributorId(currentUser.getAdOrgId())
                .setAdUserId(currentUser.getAdUserId());
        return ofNullable(merchantDivideAuthAccountClient.deleteAuthDivideUser(dto)).map(e -> {
            if (GlobalErrorCode.OK.getCode().equals(e.getCode())) {
                return new BaseResponse(e.getBody());
            } else {
                return new BaseResponse(ResponseCodeEnum.FAIL.getCode(), e.getMessage());
            }
        }).orElse(new BaseResponse(ResponseCodeEnum.FAIL.getCode(), "接口异常"));
    }

}
