package cn.lyy.merchant.controller.merchant.divide;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.merchant.api.service.MerchantEquipmentService;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.divide.DivideRuleEquipmentRelationLogQueryVO;
import cn.lyy.merchant.dto.divide.response.DivideRuleEquipmentRelationLogVO;
import cn.lyy.merchant.dto.equipment.param.EquipmentGroupPageParamDTO;
import cn.lyy.merchant.dto.equipment.rule.EquipmentRuleInfoDTO;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import cn.lyy.merchant.service.divide.IEquipmentDivideService;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * @Author: liuFaLin
 * @DateTime: 14:44 2023/5/16
 * @Return
 */
@RestController
@RequestMapping("/rest/equipment/divide")
@Slf4j
public class EquipmentDividerController extends BaseController {

    @Autowired
    private MerchantEquipmentService merchantEquipmentService;

    @Autowired
    private IEquipmentDivideService equipmentDividerService;

    /**
     * 设备分成设置列表查询(分页)
     */
    @PostMapping("/list")
    public BaseResponse<PageInfo<EquipmentRuleInfoDTO>> listEquipmentDivideInfoPage(@RequestBody @Valid EquipmentGroupPageParamDTO param) {
        AdUserInfoDTO userInfoDTO = getCurrentUser();
        param.setDistributor(userInfoDTO.getAdOrgId());
        log.info("设备分成设置列表查询：{}", param);
        return merchantEquipmentService.listEquipmentDivideInfoPage(param);
    }

    /**
     * 设备分成修改记录日志列表
     *
     * @param dto 请求参数
     * @return 返回结果
     */
    @PostMapping("/relation-log")
    public BaseResponse<PageInfo<DivideRuleEquipmentRelationLogVO>> pageDivideRuleEquipmentRelationLog(@RequestBody @Valid DivideRuleEquipmentRelationLogQueryVO dto) {
        AdUserInfoDTO userInfoDTO = getCurrentUser();
        log.info("[设备分成修改记录日志列表] 请求参数:[{}] 商家信息:[{}]", dto, userInfoDTO);
        return equipmentDividerService.pageDivideRuleEquipmentRelationLog(dto,userInfoDTO);
    }

   
}
