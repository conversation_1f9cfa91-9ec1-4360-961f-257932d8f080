package cn.lyy.merchant.controller.merchant.divide;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.lyy_api.AdUserClient;
import cn.lyy.merchant.api.service.AdOrgClient;
import cn.lyy.merchant.api.service.DivideRuleClient;
import cn.lyy.merchant.constant.merchantDivide.MerchantDivideDistributorTypeEnum;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.divide.DivideRuleConfigDTO;
import cn.lyy.merchant.dto.divide.DivideRuleInfoDTO;
import cn.lyy.merchant.dto.divide.DivideRuleInfoMessageDTO;
import cn.lyy.merchant.dto.divide.DivideRuleQueryRequestDTO;
import cn.lyy.merchant.dto.divide.DivideRuleSaveRequestDTO;
import cn.lyy.merchant.dto.divide.DivideRuleSelectVO;
import cn.lyy.merchant.dto.divide.DivideRuleStoreConfigDTO;
import cn.lyy.merchant.dto.merchant.AdOrgDTO;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import cn.lyy.merchant.service.divide.IEquipmentDivideService;
import cn.lyy.merchant.utils.RemoteResponseUtils;
import com.github.pagehelper.PageInfo;
import com.lyy.payment.divide.bff.interfaces.DividRuleClient;
import com.lyy.payment.divide.bff.interfaces.DivideCommodityClient;
import com.lyy.payment.divide.bff.response.DivideCommodityInfoRespDTO;
import com.lyy.starter.common.resp.RespBodyUtil;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Objects;

import static java.util.Optional.ofNullable;

/**
 * <AUTHOR>
 * @date 2023/4/1
 */
@RestController
@RequestMapping("/rest/divide-rule")
@Slf4j
public class DividerRuleController extends BaseController {

    @Autowired
    private DivideRuleClient divideRuleClient;

    @Autowired
    private DivideCommodityClient divideCommodityClient;

    @Autowired
    private IEquipmentDivideService equipmentDividerService;

    private static final String UNKNOWN_NAME = "未知名称";

    @Autowired
    private AdUserClient adUserClient;

    /**
     * 分账规则列表（分页）
     */
    @PostMapping("/page")
    public BaseResponse<PageInfo<DivideRuleInfoMessageDTO>> divideRulePage(@RequestBody DivideRuleQueryRequestDTO dto) {
        AdUserInfoDTO userInfoDTO = getCurrentUser();
        dto.setDistributorId(userInfoDTO.getAdOrgId());
        dto.setDistributorType(MerchantDivideDistributorTypeEnum.MERCHANT.getType());
        log.info("分账规则分页列表：{}", dto);
        return success(equipmentDividerService.divideRulePage(dto,userInfoDTO.getAdUserId()));
    }

    /**
     * 根据adOrgId获取分账规则
     * 如果传了规则id,标记出选择的规则id给前端回显
     */
    @PostMapping("/simpleList")
    public BaseResponse<DivideRuleSelectVO> divideRuleSimpleList(@RequestBody DivideRuleQueryRequestDTO dto) {
        AdUserInfoDTO userInfoDTO = getCurrentUser();
        dto.setDistributorId(userInfoDTO.getAdOrgId());
        dto.setDistributorType(MerchantDivideDistributorTypeEnum.MERCHANT.getType());
        log.info("分账规则列表：{}", dto);
        return equipmentDividerService.divideRuleSimpleList(dto) != null ? success(equipmentDividerService.divideRuleSimpleList(dto)) : error(null, "规则信息有误");
    }

    /**
     * 分账规则详情
     */
    @PostMapping("/detail")
    public BaseResponse<DivideRuleInfoDTO> divideRuleDetail(@RequestBody DivideRuleSaveRequestDTO dto) {
        AdUserInfoDTO userInfoDTO = getCurrentUser();
        dto.setDistributorId(userInfoDTO.getAdOrgId());
        dto.setDistributorType(MerchantDivideDistributorTypeEnum.MERCHANT.getType());
        log.info("分账规则详情：{}", dto);
        return equipmentDividerService.divideRuleDetail(dto,userInfoDTO.getAdUserId());
    }

    /**
     * 分账规则详情（编辑时展示）
     *
     * @param dto 请求参数
     * @return 返回结果
     */
    @PostMapping("/to-update-detail")
    public BaseResponse<DivideRuleInfoDTO> divideRuleUpdateDetail(@RequestBody DivideRuleSaveRequestDTO dto) {
        AdUserInfoDTO userInfoDTO = getCurrentUser();
        dto.setDistributorId(userInfoDTO.getAdOrgId());
        dto.setDistributorType(MerchantDivideDistributorTypeEnum.MERCHANT.getType());
        log.info("分账规则详情（编辑时展示）：{}", dto);
        return equipmentDividerService.divideRuleUpdateDetail(dto,userInfoDTO.getAdUserId());
    }

    /**
     * 新增分账规则
     */
    @PostMapping("/add")
    public BaseResponse divideRuleAdd(@RequestBody DivideRuleSaveRequestDTO dto) {
        commonRequestDivideDTO(dto);
        dto.setDistributorType(MerchantDivideDistributorTypeEnum.MERCHANT.getType());
        dto.setBusinessType("lyy");
        dto.setLevelCode("merchant");
        log.info("新增分账规则：{}", dto);
        return divideRuleClient.divideRuleAdd(dto);
    }


    /**
     * 更新分账规则
     */
    @PostMapping("/update")
    public BaseResponse divideRuleUpdate(@RequestBody DivideRuleSaveRequestDTO dto) {
        commonRequestDivideDTO(dto);
        dto.setDistributorType( MerchantDivideDistributorTypeEnum.MERCHANT.getType());
        dto.setBusinessType("lyy");
        dto.setLevelCode("merchant");
        log.info("更新分账规则：{}", dto);
        return divideRuleClient.divideRuleUpdate(dto);
    }

    /**
     * 删除分账规则
     */
    @PostMapping("/delete")
    public BaseResponse divideRuleDelete(@RequestBody DivideRuleSaveRequestDTO dto) {
        commonRequestDivideDTO(dto);
        dto.setDistributorType(MerchantDivideDistributorTypeEnum.MERCHANT.getType());
        log.info("删除分账规则：{}", dto);
        return divideRuleClient.divideRuleDelete(dto);
    }

    /**
     * 启用、禁用分账规则
     */
    @PostMapping("/update-status")
    public BaseResponse divideRuleUpdateStatus(@RequestBody DivideRuleSaveRequestDTO dto) {
        commonRequestDivideDTO(dto);
        dto.setDistributorType(MerchantDivideDistributorTypeEnum.MERCHANT.getType());
        log.info("启用、禁用分账规则：{}", dto);
        return divideRuleClient.divideRuleUpdateStatus(dto);
    }

    /**
     * 新增/取消规则和设备的关联（批量）
     */
    @PostMapping("/condition/addOrCancel")
    public BaseResponse conditionAddOrCancel(@RequestBody @Valid DivideRuleConfigDTO dto) {
        AdUserInfoDTO userInfoDTO = getCurrentUser();
        userInfoDTO.setUserName(getCurrentUserName(userInfoDTO));
        log.info("[条件分账]设备关联编辑入参:{}", dto);
        return equipmentDividerService.conditionAddOrCancel(dto, userInfoDTO);
    }

    /**
     * 新增/取消规则和场地的关联（批量）
     */
    @PostMapping("/conditionStore/addOrCancel")
    public BaseResponse conditionGroupAddOrCancel(@RequestBody @Valid DivideRuleStoreConfigDTO dto) {
        String creator = getCurrentUserName(getCurrentUser());
        log.info("[条件分账]场地关联编辑入参:{}", dto);
        return equipmentDividerService.conditionStoreAddOrCancel(dto, getAdOrgIdNotNull(), getAdUserIdNotNull(), creator);
    }

    private void commonRequestDivideDTO(DivideRuleSaveRequestDTO dto) {
        AdUserInfoDTO userInfoDTO = getCurrentUser();
        dto.setDistributorId(userInfoDTO.getAdOrgId());
        dto.setOperatorId(userInfoDTO.getAdUserId());
        dto.setOperatorName(getCurrentUserName(userInfoDTO));
    }

    private String getCurrentUserName(AdUserInfoDTO currentUser) {
        BaseResponse<cn.lyy_dto.AdUserInfoDTO> adUserInfoRes = adUserClient.getAdUserInfo(currentUser.getAdOrgId(), currentUser.getAdUserId());
        return ofNullable(RemoteResponseUtils.getData(adUserInfoRes)).map(cn.lyy_dto.AdUserInfoDTO::getName)
            .orElse(null);
    }

    /**
     * 获取商品分账商品列表
     * @return
     */
    @GetMapping("/commodity/list/query")
    public BaseResponse<DivideCommodityInfoRespDTO> queryDivideCommodityList () {
        List<DivideCommodityInfoRespDTO> commodityInfoRespList = RespBodyUtil
                .checkResp(divideCommodityClient.queryDivideCommodityList());
        return new BaseResponse(commodityInfoRespList);
    }
}
