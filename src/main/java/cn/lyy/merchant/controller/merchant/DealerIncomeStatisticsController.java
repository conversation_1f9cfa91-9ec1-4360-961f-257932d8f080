package cn.lyy.merchant.controller.merchant;

import static java.util.Optional.ofNullable;

import cn.lyy.base.utils.text.JSONObjectUtils;
import cn.lyy.income.api.IDealerIncomeStatisticsService;
import cn.lyy.income.dto.bean.BaseResponse;
import cn.lyy.income.dto.constant.ResponseCodeEnum;
import cn.lyy.income.dto.dealer.DealeIncomeStatisticsDateMchGroupSummaryRespDTO;
import cn.lyy.income.dto.dealer.DealeIncomeStatisticsDateMchSummaryRespDTO;
import cn.lyy.income.dto.dealer.DealerIncomeStatisticsDateMchGroupReqDTO;
import cn.lyy.income.dto.dealer.DealerIncomeStatisticsDateMchReqDTO;
import cn.lyy.income.dto.dealer.DealerIncomeStatisticsDetailDTO;
import cn.lyy.income.dto.dealer.DealerIncomeStatisticsDetailReqDTO;
import cn.lyy.income.dto.dealer.DealerIncomeStatisticsExportReqDTO;
import cn.lyy.income.dto.dealer.DealerIncomeStatisticsExportRespDTO;
import cn.lyy.income.dto.dealer.DealerIncomeStatisticsGroupNameIdDTO;
import cn.lyy.income.dto.dealer.DealerIncomeStatisticsGroupNameIdReqDTO;
import cn.lyy.lyy_api.AdUserClient;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.redis.MerchantRedisClient;
import cn.lyy.merchant.redis.MerchantRedisKeyEnum;
import cn.lyy.merchant.service.divide.DealerDivideIncomeStatisticsService;
import cn.lyy_dto.AdUserInfoDTO;
import com.github.pagehelper.PageInfo;
import com.lyy.payment.merchant.api.service.SystemOperationLogClient;
import com.lyy.payment.merchant.constant.SystemOperationLogTypeEnum;
import com.lyy.payment.merchant.dto.operationlog.SystemOperationLogDivideStatisticsDataDTO;
import com.lyy.payment.merchant.dto.operationlog.SystemOperationLogSaveDTO;
import com.lyy.starter.common.resp.RespBody;
import java.util.Date;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 资金池经营收益统计
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/rest/dealer/incomeStatistics")
@Slf4j
public class DealerIncomeStatisticsController extends BaseController {

    @Autowired
    private IDealerIncomeStatisticsService dealerIncomeStatisticsService;

    @Autowired
    private AdUserClient adUserClient;

    @Autowired
    private SystemOperationLogClient systemOperationLogClient;

    @Autowired
    private DealerDivideIncomeStatisticsService dealerDivideIncomeStatisticsService;

    /**
     * 按收益日期统计
     */
    @PostMapping("/getIncomeByDateMch")
    public BaseResponse<DealeIncomeStatisticsDateMchSummaryRespDTO> getIncomeByDateMch(
            @RequestBody DealerIncomeStatisticsDateMchReqDTO dto) {
        dto.setDistributorIds(dealerDivideIncomeStatisticsService.getDistributorId(getAdOrgIdNotNull(), dto.getIsDivideReceiverIncome()));
        dto.setDivideDistributorId(dealerDivideIncomeStatisticsService.getDivideReceiverDivideDistributorId(getAdOrgIdNotNull(), dto.getDivideDistributorId(), dto.getIsDivideReceiverIncome()));
        dto.setStoreIds(dealerDivideIncomeStatisticsService.getUserAuthorizedStoreIds(Long.valueOf(getAdUserIdNotNull()), dto.getStoreIds(), dto.getIsDivideReceiverIncome()));
        log.info("[资金池]按收益日期统计,当前商户ID:{},入参:{}", getAdOrgIdNotNull(), dto);
        return dealerIncomeStatisticsService.getIncomeByDateMch(dto);
    }

    /**
     * 按收益日期场地统计
     *
     * @param dto
     * @return
     */
    @PostMapping("/getIncomeByDateMchGroup")
    public BaseResponse<DealeIncomeStatisticsDateMchGroupSummaryRespDTO> getIncomeByDateMchGroup(
            @RequestBody DealerIncomeStatisticsDateMchGroupReqDTO dto) {
        dto.setDistributorIds(dealerDivideIncomeStatisticsService.getDistributorId(getAdOrgIdNotNull(), dto.getIsDivideReceiverIncome()));
        dto.setDivideDistributorId(dealerDivideIncomeStatisticsService.getDivideReceiverDivideDistributorId(getAdOrgIdNotNull(), dto.getDivideDistributorId(), dto.getIsDivideReceiverIncome()));
        dto.setStoreIds(dealerDivideIncomeStatisticsService.getUserAuthorizedStoreIds(Long.valueOf(getAdUserIdNotNull()), dto.getStoreIds(), dto.getIsDivideReceiverIncome()));
        log.info("[资金池]按收益日期场地统计,当前商户ID:{},入参:{}", getAdOrgIdNotNull(), dto);
        return dealerIncomeStatisticsService.getIncomeByDateMchGroup(dto);
    }

    /**
     * 按收益日期场地分成人员统计
     *
     * @param dto
     * @return
     */
    @PostMapping("/getIncomeByDateMchDivideGroup")
    public BaseResponse<DealeIncomeStatisticsDateMchGroupSummaryRespDTO> getIncomeByDateMchDivideGroup(
            @RequestBody DealerIncomeStatisticsDateMchGroupReqDTO dto) {
        dto.setDistributorIds(dealerDivideIncomeStatisticsService.getDistributorId(getAdOrgIdNotNull(), dto.getIsDivideReceiverIncome()));
        dto.setDivideDistributorId(dealerDivideIncomeStatisticsService.getDivideReceiverDivideDistributorId(getAdOrgIdNotNull(), dto.getDivideDistributorId(), dto.getIsDivideReceiverIncome()));
        dto.setStoreIds(dealerDivideIncomeStatisticsService.getUserAuthorizedStoreIds(Long.valueOf(getAdUserIdNotNull()), dto.getStoreIds(), dto.getIsDivideReceiverIncome()));
        log.info("[资金池]按收益日期场地分成人员统计,当前商户ID:{},入参:{}", getAdOrgIdNotNull(), dto);
        return dealerIncomeStatisticsService.getIncomeByDateMchDivideGroup(dto);
    }


    /**
     * 收益统计收益详情分页查询
     *
     * @param dto
     * @return
     */
    @PostMapping("/getIncomeDetail")
    public BaseResponse<PageInfo<DealerIncomeStatisticsDetailDTO>> getIncomeDetail(
            @RequestBody DealerIncomeStatisticsDetailReqDTO dto) {
        dto.setDistributorIds(dealerDivideIncomeStatisticsService.getDistributorId(getAdOrgIdNotNull(), dto.getIsDivideReceiverIncome()));
        dto.setDivideDistributorId(dealerDivideIncomeStatisticsService.getDivideReceiverDivideDistributorId(getAdOrgIdNotNull(), dto.getDivideDistributorId(), dto.getIsDivideReceiverIncome()));
        dto.setStoreIds(dealerDivideIncomeStatisticsService.getUserAuthorizedStoreIds(Long.valueOf(getAdUserIdNotNull()), dto.getStoreIds(), dto.getIsDivideReceiverIncome()));
        log.info("[资金池]收益统计收益详情分页查询,当前商户ID:{},入参:{}", getAdOrgIdNotNull(), dto);
        return dealerIncomeStatisticsService.getIncomeDetail(dto);
    }

    /**
     * 收益统计收益详情分页查询（不含比例汇总）
     *
     * @param dto
     * @return
     */
    @PostMapping("/getIncomeDetailTotal")
    public BaseResponse<PageInfo<DealerIncomeStatisticsDetailDTO>> getIncomeDetailTotal(
            @RequestBody DealerIncomeStatisticsDetailReqDTO dto) {
        dto.setDistributorIds(dealerDivideIncomeStatisticsService.getDistributorId(getAdOrgIdNotNull(), dto.getIsDivideReceiverIncome()));
        dto.setDivideDistributorId(dealerDivideIncomeStatisticsService.getDivideReceiverDivideDistributorId(getAdOrgIdNotNull(), dto.getDivideDistributorId(), dto.getIsDivideReceiverIncome()));
        dto.setStoreIds(dealerDivideIncomeStatisticsService.getUserAuthorizedStoreIds(Long.valueOf(getAdUserIdNotNull()), dto.getStoreIds(), dto.getIsDivideReceiverIncome()));
        log.info("[资金池]收益统计收益详情分页查询（不含比例汇总）,当前商户ID:{},入参:{}", getAdOrgIdNotNull(), dto);
        return dealerIncomeStatisticsService.getIncomeDetailTotal(dto);
    }

    /**
     * 查询分账场地
     *
     * @param dto
     * @return
     */
    @PostMapping("/getDivideGroup")
    public BaseResponse<PageInfo<DealerIncomeStatisticsGroupNameIdDTO>> getDivideGroup(
            @RequestBody DealerIncomeStatisticsGroupNameIdReqDTO dto) {
        dto.setDistributorIds(dealerDivideIncomeStatisticsService.getDistributorId(getAdOrgIdNotNull(), dto.getIsDivideReceiverIncome()));
        dto.setDivideDistributorId(dealerDivideIncomeStatisticsService.getDivideReceiverDivideDistributorId(getAdOrgIdNotNull(), dto.getDivideDistributorId(), dto.getIsDivideReceiverIncome()));
        dto.setStoreIds(dealerDivideIncomeStatisticsService.getUserAuthorizedStoreIds(Long.valueOf(getAdUserIdNotNull()), dto.getStoreIds(), dto.getIsDivideReceiverIncome()));
        log.info("[资金池]收益统计收益详情分页查询（不含比例汇总）,当前商户ID:{},入参:{}", getAdOrgIdNotNull(), dto);
        return dealerIncomeStatisticsService.getDivideGroup(dto);
    }

    /**
     * 收益统计收益详情分页查询
     *
     * @param dto
     * @return
     */
    @PostMapping("/exportEmail")
    public BaseResponse exportEmail(@RequestBody DealerIncomeStatisticsExportReqDTO dto) {
        BaseResponse respBody = new BaseResponse();
        Long adUserId = getAdUserIdNotNull();
        dto.setDistributorIds(dealerDivideIncomeStatisticsService.getDistributorId(getAdOrgIdNotNull(), dto.getIsDivideReceiverIncome()));
        dto.setDivideDistributorIds(dealerDivideIncomeStatisticsService.getDivideReceiverDivideDistributorIds(getAdOrgIdNotNull(), dto.getDivideDistributorIds(), dto.getIsDivideReceiverIncome()));
        dto.setEquipmentGroupIds(dealerDivideIncomeStatisticsService.getUserAuthorizedStoreIds(Long.valueOf(getAdUserIdNotNull()), dto.getEquipmentGroupIds(), dto.getIsDivideReceiverIncome()));
        dto.setAdUserId(adUserId);
        log.info("[资金池]收益统计收益导出到邮箱,当前商户ID:{},入参:{}", getAdOrgIdNotNull(), dto);
        String redisData = MerchantRedisClient.get(MerchantRedisKeyEnum.MERCHANT_EXPORTEMAIL_CONCURRENT_LOCK, String.valueOf(adUserId));
        if (StringUtils.isNotEmpty(redisData)) {
            respBody.setCode(ResponseCodeEnum.FAIL.getCode());
            respBody.setMessage("您操作太频繁啦！请稍后重试");
            return respBody;
        }
        MerchantRedisClient.setex(MerchantRedisKeyEnum.MERCHANT_EXPORTEMAIL_CONCURRENT_LOCK, String.valueOf(adUserId),
                String.valueOf(adUserId));

        cn.lyy.base.communal.bean.BaseResponse<AdUserInfoDTO> adUserRes = adUserClient.getAdUserInfo(Long.valueOf(getAdOrgIdNotNull()),
                Long.valueOf(getAdUserIdNotNull()));
        AdUserInfoDTO user = ofNullable(adUserRes).filter(r -> ResponseCodeEnum.SUCCESS.getCode() == r.getCode())
                .map(cn.lyy.base.communal.bean.BaseResponse::getData).orElse(null);
        if (Objects.nonNull(user)) {
            dto.setAdUserName(user.getName());
            dto.setAdUserId(Long.valueOf(user.getAdUserId()));
            BaseResponse<DealerIncomeStatisticsExportRespDTO> baseResponse = dealerIncomeStatisticsService.exportEmail(dto);
            if (Objects.nonNull(baseResponse)
                    && baseResponse.getCode() == cn.lyy.base.communal.constant.ResponseCodeEnum.SUCCESS.getCode()) {
                DealerIncomeStatisticsExportRespDTO data = baseResponse.getData();
                if (Objects.nonNull(data)) {
                    saveLog(SystemOperationLogTypeEnum.DIVIDE_STATISTICS_DOWNLOAD.getValue(), dto, data.getFileName(), data.getSucc(),
                            data.getDesc(), data.getFileFormat());
                }
            }
        } else {
            log.warn("[资金池]导出到邮箱,找不到用户:{},跳过", getAdUserIdNotNull());
        }
        return respBody;
    }

    /**
     * 记录操作日志
     */
    private void saveLog(Integer type, DealerIncomeStatisticsExportReqDTO dto, String fileName, Boolean succ, String desc,
            String fileFormat) {
        SystemOperationLogSaveDTO ol = new SystemOperationLogSaveDTO();
        ol.setType(type);
        ol.setAssociatedId(dto.getDistributorId());
        ol.setCreateTime(new Date());
        ol.setOperatorName(dto.getAdUserName());
        ol.setOperatorId(dto.getAdUserId());
        SystemOperationLogDivideStatisticsDataDTO logData = new SystemOperationLogDivideStatisticsDataDTO();
        logData.setFileName(fileName);
        logData.setSuccess(succ);
        logData.setRemark(desc);
        logData.setFileFormat(fileFormat);
        String logDataJson = JSONObjectUtils.toJSONString(logData);
        ol.setSource(logDataJson);
        ol.setTarget(logDataJson);
        RespBody respBody = systemOperationLogClient.save(ol);
        log.info("[资金池]保存日志记录,{}", respBody);
    }


}
