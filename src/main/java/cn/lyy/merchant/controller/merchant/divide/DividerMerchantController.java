package cn.lyy.merchant.controller.merchant.divide;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.dto.Status;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.lyy_api.EquipmentGroupService;
import cn.lyy.merchant.api.service.MerchantEquipmentService;
import cn.lyy.merchant.api.service.rule.DivideRuleGroupEquipmentClient;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.divide.MerchantDivideDTO;
import cn.lyy.merchant.dto.request.MerchantDivideAccess;
import cn.lyy_dto.PageInfoDTO;
import cn.lyy_dto.group.EquipmentGroupInfoVO;
import cn.lyy_dto.group.EquipmentGroupPageDTO;
import com.github.pagehelper.PageInfo;
import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.payment.divide.bff.interfaces.DividerMerchantClient;
import com.lyy.payment.divide.bff.interfaces.MerchantDivideAssociationClient;
import com.lyy.payment.divide.bff.request.MerchantDivideAssociationQueryReqDTO;
import com.lyy.payment.divide.bff.request.MerchantDivideRuleConfigReqDTO;
import com.lyy.payment.divide.bff.response.DivideRuleInfoRespDTO;
import com.lyy.payment.divide.constants.DivideDistributorTypeEnum;
import com.lyy.starter.common.resp.RespBody;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Objects;

import static java.util.Optional.ofNullable;


/**
 * @Author: liuFaLin
 * @DateTime: 14:44 2023/5/16
 * @Return
 */
@RestController
@RequestMapping("/rest/divide-merchant")
@Slf4j
public class DividerMerchantController extends BaseController {

    @Autowired
    private EquipmentGroupService equipmentGroupService;

    @Autowired
    private MerchantEquipmentService merchantEquipmentService;

    @Autowired
    private DivideRuleGroupEquipmentClient divideRuleGroupEquipmentClient;

    @Autowired
    private MerchantDivideAssociationClient merchantDivideAssociationClient;

    @Autowired
    private DividerMerchantClient dividerMerchantClient;

    @PostMapping("/group-list")
    public BaseResponse<PageInfo<EquipmentGroupInfoVO>> divideMerchantGroupPage(@RequestBody MerchantDivideDTO merchantDivideDTO) {
        if (CollectionUtils.isEmpty(merchantDivideDTO.getMainBusinessList()) && Objects.nonNull(merchantDivideDTO.getMainBusinessId())) {
            return ResponseUtils.error(Status.STATUS_PARAMETER_ERROR, "主营商户信息不能为空");
        }
        EquipmentGroupPageDTO request = new EquipmentGroupPageDTO();
        request.setPageIndex(merchantDivideDTO.getPageIndex());
        request.setPageSize(merchantDivideDTO.getPageSize());
        request.setMerchantId(merchantDivideDTO.getMainBusinessId());
        request.setMerchantIdList(merchantDivideDTO.getMainBusinessList());
        RespBody<PageInfoDTO<EquipmentGroupInfoVO>> customizeBasePageGroup = equipmentGroupService.getCustomizeBasePageGroup(request);
        if (!GlobalErrorCode.OK.getCode().equals(customizeBasePageGroup.getCode())) {
            return ResponseUtils.success(new PageInfo<>(new ArrayList<>()));
        }
        PageInfo<EquipmentGroupInfoVO> groupPageInfo = new PageInfo<>(customizeBasePageGroup.getBody().getRecords());
        return ResponseUtils.success(groupPageInfo);
    }


    /**
     * 设备分成列表查询(分页)
     */
    @PostMapping("/equipment-divide-list")
    public BaseResponse<PageInfo<DivideRuleInfoRespDTO>> listEquipmentDivideInfoPage(@RequestBody MerchantDivideRuleConfigReqDTO param) {
        log.info("分成方-设备分成列表查询：{}", param);
        Long adOrgId = getAdOrgIdNotNull();
        param.setDivideDistributorId(adOrgId);
        param.setDivideDistributorTypes(ofNullable(param.getDivideDistributorTypes()).orElse(Collections.singletonList(DivideDistributorTypeEnum.DIVIDER.getType())));
        PageInfo<DivideRuleInfoRespDTO> dtoPageInfo = ofNullable(dividerMerchantClient.equipmentDivideList(param)).filter(res -> GlobalErrorCode.OK.getCode().equals(res.getCode()))
                .map(RespBody::getBody).orElse(new PageInfo<>(new ArrayList<>()));
        return ResponseUtils.success(dtoPageInfo);

    }

    /**
     * 分账规则-场地分账列表
     */
    @PostMapping("/group-divide-list")
    public BaseResponse<PageInfo<DivideRuleInfoRespDTO>> listGroupDivideInfoPage(@RequestBody MerchantDivideRuleConfigReqDTO param) {
        log.info("分成方-场地分账列表查询：{}", param);
        Long adOrgId = getAdOrgIdNotNull();
        param.setDivideDistributorId(adOrgId);
        param.setDivideDistributorTypes(ofNullable(param.getDivideDistributorTypes()).orElse(Collections.singletonList(DivideDistributorTypeEnum.DIVIDER.getType())));
        PageInfo<DivideRuleInfoRespDTO> dtoPageInfo = ofNullable(dividerMerchantClient.groupDivideList(param)).filter(res -> GlobalErrorCode.OK.getCode().equals(res.getCode()))
                .map(RespBody::getBody).orElse(new PageInfo<>(new ArrayList<>()));
        return ResponseUtils.success(dtoPageInfo);
    }

    /**
     * 分成收益入口
     * @return
     */
    @PostMapping("/merchant-access")
    public BaseResponse<MerchantDivideAccess> merchantAccess() {
        MerchantDivideAccess merchantDivideAccess = new MerchantDivideAccess();
        merchantDivideAccess.setAccessFlag(Boolean.FALSE);
        MerchantDivideAssociationQueryReqDTO merchantDivideAssociationQueryReqDTO = new MerchantDivideAssociationQueryReqDTO();
        Long adOrgId = getAdOrgIdNotNull();
        merchantDivideAssociationQueryReqDTO.setDivideDistributorId(adOrgId);
        Integer data = ofNullable(merchantDivideAssociationClient.getAssociationCount(merchantDivideAssociationQueryReqDTO)).filter(res -> GlobalErrorCode.OK.getCode().equals(res.getCode()))
                .map(RespBody::getBody).orElse(0);
        log.info("分成方分成收益入口查询商户ID{},结果:{}", adOrgId, data);
        merchantDivideAccess.setAccessFlag(data > 0 ? Boolean.TRUE : Boolean.FALSE);
        return ResponseUtils.success(merchantDivideAccess);
    }


}
