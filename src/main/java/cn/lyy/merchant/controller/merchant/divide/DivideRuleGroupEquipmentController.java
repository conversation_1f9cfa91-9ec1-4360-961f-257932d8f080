package cn.lyy.merchant.controller.merchant.divide;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.merchant.api.service.MerchantWhiteClient;
import cn.lyy.merchant.api.service.rule.DivideRuleGroupEquipmentClient;
import cn.lyy.merchant.config.MerchantDivideConfig;
import cn.lyy.merchant.constant.LyyDistributorConstants;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.divide.DivideRuleInfoDTO;
import cn.lyy.merchant.dto.divide.request.DivideConditionRuleStoreQueryReqDTO;
import cn.lyy.merchant.dto.divide.request.DivideModifyLogConditionReqDTO;
import cn.lyy.merchant.dto.divide.request.DivideModifyLogGroupEquipRuleReqDTO;
import cn.lyy.merchant.dto.divide.request.DivideModifyLogRuleLogReqDTO;
import cn.lyy.merchant.dto.divide.request.DivideModifyLogRuleReqDTO;
import cn.lyy.merchant.dto.divide.request.DivideRuleMchIsExistReqDTO;
import cn.lyy.merchant.dto.divide.response.DivideConditionRuleStoreQueryResDTO;
import cn.lyy.merchant.dto.divide.response.DivideModifyLogGroupEquipRuleRespDTO;
import cn.lyy.merchant.dto.equipment.rule.DivideRuleGroupEquipmentInfoRequestDTO;
import cn.lyy.merchant.dto.equipment.rule.DivideRuleGroupEquipmentInfoResponseDTO;
import cn.lyy.merchant.dto.equipment.rule.DivideRuleGroupIsExistEquDivideRequestDTO;
import cn.lyy.merchant.service.divide.IEquipmentDivideService;
import cn.lyy.merchant.utils.ResponseCheckUtil;
import com.github.pagehelper.PageInfo;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import static java.util.Optional.ofNullable;

/**
 * 分账规则
 * <AUTHOR>
 */
@RestController
@RequestMapping("/rest/divideRule")
@Slf4j
public class DivideRuleGroupEquipmentController extends BaseController {

    @Autowired
    private DivideRuleGroupEquipmentClient divideRuleGroupEquipmentClient;

    @Autowired
    private IEquipmentDivideService equipmentDivideService;

    @Autowired
    private MerchantWhiteClient merchantWhiteClient;

    @Autowired
    private MerchantDivideConfig merchantDivideConfig;

    /**
     * 分账规则-场地分账列表
     */
    @PostMapping("/group/list")
    public BaseResponse<PageInfo<DivideRuleGroupEquipmentInfoResponseDTO>> listEquipmentDivideInfoPage(@RequestBody DivideRuleGroupEquipmentInfoRequestDTO param) {
        param.setMerchantId(getAdOrgIdNotNull());
        param.setAdUserId(getAdUserIdNotNull());
        log.info("分账规则-场地分账列表:{}", param);
        return divideRuleGroupEquipmentClient.getAdUserGroupEquipmentCount(param);
    }

    /**
     * 分账规则-查询用户场地是否存在设备分账
     */
    @PostMapping("/group/isExistEquDivide")
    public BaseResponse<Boolean> isExistEquDivide(@RequestBody DivideRuleGroupIsExistEquDivideRequestDTO param) {
        param.setMerchantId(getAdOrgIdNotNull());
        param.setAdUserId(getAdUserIdNotNull());
        log.info("[分账规则]查询用户场地是否存在设备分账:{}", param);
        return divideRuleGroupEquipmentClient.isExistEquDivide(param);
    }

    /**
     * 查询规则最近待生效记录日志入参
     */
    @PostMapping("/getRuleRecentlyToEffective")
    public BaseResponse<DivideRuleInfoDTO> getRuleRecentlyToEffective(@RequestBody DivideModifyLogRuleReqDTO param) {
        param.setMerchantId(getAdOrgIdNotNull());
        log.info("[分账规则]查询规则最近待生效记录日志入参:{}", param);
        BaseResponse<DivideRuleInfoDTO> response = divideRuleGroupEquipmentClient.getRuleRecentlyToEffective(param);
        if (Objects.nonNull(response) && response.getCode() == ResponseCodeEnum.SUCCESS.getCode() && Objects.nonNull(response.getData())) {
            equipmentDivideService.getDivideDistributorInfo(response.getData(), param.getMerchantId(), getAdUserIdNotNull());
        }
        return response;
    }

    /**
     * 查询关联最近待生效记录日志入参
     */
    @PostMapping("/getConditionRecentlyToEffective")
    public BaseResponse<DivideRuleInfoDTO> getConditionRecentlyToEffective(@RequestBody DivideModifyLogConditionReqDTO param) {
        param.setMerchantId(getAdOrgIdNotNull());
        log.info("[分账规则]查询关联最近待生效记录日志入参:{}", param);
        BaseResponse<DivideRuleInfoDTO> response = divideRuleGroupEquipmentClient.getConditionRecentlyToEffective(param);
        if (Objects.nonNull(response) && response.getCode() == ResponseCodeEnum.SUCCESS.getCode() && Objects.nonNull(response.getData())) {
            equipmentDivideService.getDivideDistributorInfo(response.getData(), param.getMerchantId(), getAdUserIdNotNull());
        }
        return response;
    }

    /**
     * 查询操作日志
     */
    @PostMapping("/getOperationLog")
    public BaseResponse<PageInfo<DivideModifyLogGroupEquipRuleRespDTO>> getOperationLog(@RequestBody DivideModifyLogGroupEquipRuleReqDTO param) {
        param.setDistributorId(getAdOrgIdNotNull());
        param.setAdUserId(getAdUserIdNotNull());
        log.info("[分账规则]查询操作日志:{}", param);
        return divideRuleGroupEquipmentClient.getOperationLog(param);
    }

    /**
     * 查询操作日志
     */
    @PostMapping("/getLogDataById")
    public BaseResponse<DivideRuleInfoDTO> getLogDataById(@RequestBody DivideModifyLogRuleLogReqDTO param) {
        param.setMerchantId(getAdOrgIdNotNull());
        log.info("[分账规则]根据日志ID查询操作日志:{}", param);
        BaseResponse<DivideRuleInfoDTO> response = divideRuleGroupEquipmentClient.getLogDataById(param);
        if (Objects.nonNull(response) && response.getCode() == ResponseCodeEnum.SUCCESS.getCode() && Objects.nonNull(response.getData())) {
            equipmentDivideService.getDivideDistributorInfo(response.getData(), param.getMerchantId(), getAdUserIdNotNull());
        }
        return response;
    }

    /**
     * 分账规则-商户是否存在规则设置
     */
    @PostMapping("/isMchExistRuleSet")
    public BaseResponse<Boolean> isMchExistRuleSet(@RequestBody DivideRuleMchIsExistReqDTO param) {
        param.setMerchantId(getAdOrgIdNotNull());
        log.info("[分账规则]商户是否存在规则设置:{}", param);
        return divideRuleGroupEquipmentClient.isMchExistRuleSet(param);
    }

    /**
     * 分账规则-是否使用新分账操作记录页面
     */
    @GetMapping("/isNewOperationPage")
    public BaseResponse<Boolean> isNewOperationPage() {
        BaseResponse response = new BaseResponse();
        Long adOrgId = getAdOrgIdNotNull();
        boolean isWhite;
        if (ofNullable(merchantDivideConfig.getMerchantDivideNewOperationPageEnable()).orElse(Boolean.FALSE)) {
            isWhite = true;
        } else {
            isWhite = ResponseCheckUtil.getData(merchantWhiteClient.isWhiteDistributor(adOrgId,
                    LyyDistributorConstants.WhiteListType.MERCHANT_GROUP_DIVIDE_SETTING_LOG_MIGRATE_WHITE.getValue()));
        }
        response.setData(isWhite);
        log.info("[分账规则]是否使用新分账操作记录页面,商户ID:{},全局开关:{},是否生效:{}", adOrgId, merchantDivideConfig.getMerchantDivideNewOperationPageEnable(),
                isWhite);
        return response;
    }
    /**
     * 根据规则ID获取关联场地信息
     */
    @PostMapping("/getConditonStoreByRuleId")
    public BaseResponse<PageInfo<DivideConditionRuleStoreQueryResDTO>> getConditonStoreByRuleId(@RequestBody DivideConditionRuleStoreQueryReqDTO param) {
        param.setDistributorId(getAdOrgIdNotNull());
        log.info("[分账规则]根据规则ID获取关联场地信息:{}", param);
        BaseResponse<PageInfo<DivideConditionRuleStoreQueryResDTO>> response = divideRuleGroupEquipmentClient.getConditonStoreByRuleId(param);
        return response;
    }

}
