package cn.lyy.merchant.controller.merchant;

import cn.lyy.merchant.controller.common.BaseController;
import com.github.pagehelper.PageInfo;
import com.lyy.payment.merchant.api.service.wallet.MerchantWalletAccountingDataClient;
import com.lyy.payment.merchant.dto.wallet.MerchantWalletAccountingDataListSummaryReqDTO;
import com.lyy.payment.merchant.dto.wallet.MerchantWalletAccountingDataListSummaryRespDTO;
import com.lyy.payment.merchant.dto.wallet.MerchantWalletAccountingDataSummaryReqDTO;
import com.lyy.payment.merchant.dto.wallet.MerchantWalletAccountingDataSummaryRespDTO;
import com.lyy.starter.common.resp.RespBody;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 商户钱包记账数据
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/rest/merchant/wallet/accounting/data")
@Slf4j
public class MerchantWalletAccountingDataController extends BaseController {

    @Autowired
    private MerchantWalletAccountingDataClient merchantWalletAccountingDataClient;

    /**
     * 汇总查询
     */
    @PostMapping("/summary")
    public RespBody<MerchantWalletAccountingDataSummaryRespDTO> summary(
            @RequestBody MerchantWalletAccountingDataSummaryReqDTO dto) {
        dto.setMerchantId(getAdOrgIdNotNull());
        log.info("[商户钱包明细]汇总查询:{}", dto);
        return merchantWalletAccountingDataClient.summary(dto);
    }

    /**
     * 列表查询
     */
    @PostMapping("/list")
    public RespBody<MerchantWalletAccountingDataListSummaryRespDTO> list(
            @RequestBody MerchantWalletAccountingDataListSummaryReqDTO dto) {
        dto.setMerchantId(getAdOrgIdNotNull());
        log.info("[商户钱包明细]列表查询:{}", dto);
        return merchantWalletAccountingDataClient.list(dto);
    }

}
