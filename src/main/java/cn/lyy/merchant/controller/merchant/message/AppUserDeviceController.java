package cn.lyy.merchant.controller.merchant.message;

import cn.lyy.base.utils.text.JSONObjectUtils;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import cn.lyy.merchant.service.merchant.dto.message.AppDeviceRegisterDTO;
import cn.lyy.merchant.service.merchant.message.AppUserDeviceService;
import com.lyy.starter.common.resp.RespBody;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2025/6/26 - 14:40
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/ums/app")
public class AppUserDeviceController extends BaseController {

    private final AppUserDeviceService appUserDeviceService;

    @RequestMapping("/device/register")
    public RespBody<Boolean> registerDevice(@Validated @RequestBody AppDeviceRegisterDTO registerDevice) {
        log.info("APP设备注册:{}", log.isDebugEnabled() ? JSONObjectUtils.toJSONString(registerDevice) : registerDevice);
        AdUserInfoDTO currentUser = getCurrentUser();
        Boolean registered = appUserDeviceService.registerDevice(registerDevice, currentUser);
        return RespBody.ok(registered);
    }


    /**
     * 用户绑定接口
     */
//    @PostMapping("/user/binding")
    RespBody<Boolean> bindingUser(@Validated @RequestBody AppDeviceRegisterDTO dto) {
        log.info("APP用户绑定设备:{}", dto);
        return RespBody.ok(appUserDeviceService.bindingUser(dto));
    }

    /**
     * 用户解绑接口
     */
    @PostMapping("/user/unbinding")
    RespBody<Boolean> unbindingUser(@Validated @RequestBody AppDeviceRegisterDTO dto) {
        log.info("APP用户解绑设备:{}", dto);
        AdUserInfoDTO currentUser = getCurrentUser();
        return RespBody.ok(appUserDeviceService.unbindingUser(dto, currentUser));
    }

}
