package cn.lyy.merchant.controller.merchant.divide;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.merchant.api.service.MerchantGroupService;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.merchant.response.MerchantGroupDTO;
import com.github.pagehelper.PageInfo;
import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.merchant.applyment.dto.material.response.MerchantBaseMaterialRespDTO;
import com.lyy.merchant.applyment.interfaces.material.MerchantMaterialClient;
import com.lyy.payment.divide.bff.interfaces.MerchantDividRuleUserRelationClient;
import com.lyy.payment.divide.bff.interfaces.MerchantDivideAssociationClient;
import com.lyy.payment.divide.bff.request.MerchantDivideAssociationQueryReqDTO;
import com.lyy.payment.divide.bff.request.MerchantDivideAssociationReqDTO;
import com.lyy.payment.divide.bff.request.MerchantDivideRuleConditionCurrEffectiveReqDTO;
import com.lyy.payment.divide.bff.response.*;
import com.lyy.payment.divide.constants.DivideConditionTypeCodeEnum;
import com.lyy.payment.divide.constants.DivideDistributorTypeEnum;
import com.lyy.starter.common.resp.RespBody;
import com.lyy.starter.common.resp.RespBodyUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import static java.util.Optional.ofNullable;

/**
 * 分成方商户查询
 * <AUTHOR>
 */
@RestController
@RequestMapping("/rest/divideMch")
@Slf4j
public class DivideMerchantDivideController extends BaseController {

    @Autowired
    private MerchantDivideAssociationClient merchantDivideAssociationClient;

    @Autowired
    private MerchantDividRuleUserRelationClient merchantDividRuleUserRelationClient;

    @Autowired
    private MerchantMaterialClient merchantMaterialClient;

    @Autowired
    private MerchantGroupService merchantGroupService;

    /**
     * 分成方商户查询-分账关联列表查询
     */
    @PostMapping("/getDivideAssociationList")
    public BaseResponse<PageInfo<MerchantDivideAssociationMainMchQueryResDTO>> getDivideAssociationList(@RequestBody MerchantDivideAssociationQueryReqDTO param) {
        log.info("[分成方商户查询]分账关联列表查询:{}", param);
        param.setDivideDistributorId(getAdOrgIdNotNull());
        PageInfo<MerchantDivideAssociationMainMchQueryResDTO> pageInfo = ofNullable(merchantDivideAssociationClient.distributorIdList(param)).filter(res -> GlobalErrorCode.OK.getCode().equals(res.getCode()))
                .map(RespBody::getBody).orElse(null);
        BaseResponse response = new BaseResponse();
        response.setData(pageInfo);
        return response;
    }

    /**
     * 分成方商户查询-当前分账规则详情查询
     */
    @PostMapping("/getCurrEffRuleUserRelation")
    public BaseResponse<MerchantDivideRuleUserRelationQueryResDTO> getCurrEffRuleUserRelation(@RequestBody MerchantDivideRuleConditionCurrEffectiveReqDTO param) {
        if(Objects.isNull(param.getDistributorId()) || Objects.isNull(param.getPayDivideDistributorId())){
            log.warn("[分成方商户查询]当前分账规则详情查询,参数不完整跳过:{}", param);
            return null;
        }
        MerchantDivideAssociationReqDTO queryAsso = new MerchantDivideAssociationReqDTO();
        queryAsso.setDistributorId(param.getDistributorId());
        queryAsso.setDivideDistributorId(Long.valueOf(getAdOrgIdNotNull()));
        queryAsso.setPayDivideDistributorId(param.getPayDivideDistributorId());
        log.info("[分成方商户查询]当前分账规则详情查询:{}", param);
        MerchantDivideAssociationQueryResDTO asso = ofNullable(merchantDivideAssociationClient.get(queryAsso)).filter(res -> GlobalErrorCode.OK.getCode().equals(res.getCode()))
                .map(RespBody::getBody).orElse(null);
        Long actualPayMchOrgId = ofNullable(asso).map(MerchantDivideAssociationQueryResDTO::getPayDivideDistributorId).orElse(null);
        if(Objects.isNull(actualPayMchOrgId)){
            log.warn("[分成方商户查询]当前分账规则详情查询,无关联跳过:{}", queryAsso);
            return null;
        }
        param.setDistributorId(param.getDistributorId());
        param.setDivideDistributorId(getAdOrgIdNotNull());
        param.setPayDivideDistributorId(actualPayMchOrgId);
        param.setConditionId(param.getConditionId());
        param.setConditionType(param.getConditionType());
        param.setDivideDistributorTypes(ofNullable(param.getDivideDistributorTypes()).orElse(Arrays.asList(DivideDistributorTypeEnum.DIVIDER.getType())));
        MerchantDivideConditionRuleCurrEffectiveResDTO data = ofNullable(merchantDividRuleUserRelationClient.getCurrEffective(param)).filter(res -> GlobalErrorCode.OK.getCode().equals(res.getCode()))
                .map(RespBody::getBody).orElse(null);
        if (Objects.nonNull(data) && Objects.nonNull(data.getDivideDistributorId())) {
            List<MerchantBaseMaterialRespDTO> bankCardList = RespBodyUtil.checkResp(merchantMaterialClient.getBaseMaterialByAdOrgIds(Arrays.asList(data.getDivideDistributorId())));
            MerchantBaseMaterialRespDTO bankCard = ofNullable(bankCardList).orElse(new ArrayList<>()).stream().findFirst().orElse(null);
            if (Objects.nonNull(bankCard) && !StringUtils.isEmpty(bankCard.getAccountCode()) && bankCard.getAccountCode().length() > 4) {
                int length = bankCard.getAccountCode().length();
                data.setBankAcountNameDesc("银行卡：" + bankCard.getBankName() + "（尾号：" + bankCard.getAccountCode().substring(length - 4, length) + "）");
            }
            if (DivideConditionTypeCodeEnum.STORE.getCode().equals(data.getConditionType()) && !StringUtils.isEmpty(data.getConditionId())) {
                BaseResponse<MerchantGroupDTO> baseResponse = merchantGroupService.getGroup(Long.valueOf(data.getConditionId()));
                if (Objects.nonNull(baseResponse) && baseResponse.getCode() == ResponseCodeEnum.SUCCESS.getCode()) {
                    data.setGroupIdName(ofNullable(baseResponse.getData()).map(MerchantGroupDTO::getName).orElse(null));
                }
            }
        }
        BaseResponse response = new BaseResponse();
        response.setData(data);
        return response;
    }

}
