package cn.lyy.merchant.controller.merchant;

import static java.util.Optional.ofNullable;

import cn.lyy.authority_common.constant.merchant.function.MerchantFunctionCodeEnum;
import cn.lyy.authority_common.constant.merchant.function.MerchantFunctionVersionConstant;
import cn.lyy.authority_service_api.merchant.function.MerchantFunctionDTO;
import cn.lyy.authority_service_api.merchant.function.MerchantFunctionUpdateDTO;
import cn.lyy.authority_service_api.miscroservice.function.MerchantFunctionVersionClient;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.merchant.api.service.MerchantWhiteClient;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.function.MerchantFunctionVersionSwitchRequest;
import cn.lyy.merchant.utils.RemoteResponseUtils;
import cn.lyy.tools.constants.LyyDistributorConstants.WhiteListType;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import java.util.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @date: 2023/2/9
 * @author: YL
 */
@RestController
@RequestMapping("/rest/merchant/function/version/manage")
public class MerchantFunctionVersionManageController extends BaseController {

    @Autowired
    private MerchantFunctionVersionClient merchantFunctionVersionClient;
    
    @Autowired
    private MerchantWhiteClient merchantWhiteClient;

    @GetMapping("/version")
    @ApiImplicitParam(name = "versionCode", value = "功能码，SUPER_STAFF-超级导购员,DBJ_DISCOUNT_RULE_DISPLAY-高高级套餐")
    public BaseResponse<String> getVersionInfo(@RequestParam String versionCode) {
        Long distributorId = getAdOrgIdNotNull();

        MerchantFunctionDTO versionInfo = RemoteResponseUtils.getData(merchantFunctionVersionClient.getByCode(distributorId, versionCode));
        if (Objects.isNull(versionInfo)) {
            return ResponseUtils.error(ResponseCodeEnum.FAIL.getCode(), "功能版本信息获取失败，稍后重试");
        }

        if (Objects.equals(MerchantFunctionVersionConstant.DBJ_DISCOUNT_RULE_DISPLAY_DEFAULT_VERSION, versionInfo.getVersion())) {
            return ResponseUtils.success(versionInfo.getVersion());
        }
        if (Objects.equals(MerchantFunctionVersionConstant.DBJ_DISCOUNT_RULE_DISPLAY_PLUS_VERSION, versionInfo.getVersion())
            && hasWhiteType(distributorId, WhiteListType.MERCHANT_FUNCTION_VERSION_DBJ_DISCOUNT_DISPLAY)) {
            return ResponseUtils.success(versionInfo.getVersion());
        }
        if (Objects.equals(MerchantFunctionVersionConstant.DBJ_DISCOUNT_RULE_DISPLAY_PLUS_VERSION_THREE, versionInfo.getVersion())
                && hasWhiteType(distributorId, WhiteListType.MERCHANT_DBJ_CHARGE_RULE_NEW_VERSION_THREE_WHITE)) {
            return ResponseUtils.success(versionInfo.getVersion());
        }
        
        return ResponseUtils.success(MerchantFunctionVersionConstant.DBJ_DISCOUNT_RULE_DISPLAY_DEFAULT_VERSION);
    }

    private boolean hasWhiteType(Long distributorId, WhiteListType whiteListType) {
        return ofNullable(merchantWhiteClient.isWhiteDistributor(distributorId, whiteListType.getValue()))
                .map(RemoteResponseUtils::getData)
                .orElse(Boolean.FALSE);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "功能码，SUPER_STAFF-超级导购员,DBJ_DISCOUNT_RULE_DISPLAY-高高级套餐"),
            @ApiImplicitParam(name = "version", value = "版本号")
    })
    @PostMapping("/switch")
    public BaseResponse<Void> switchVersion(@RequestBody @Validated MerchantFunctionVersionSwitchRequest request) {

        Long distributorId = getAdOrgIdNotNull();

        MerchantFunctionUpdateDTO updateDTO = new MerchantFunctionUpdateDTO();
        updateDTO.setCode(request.getCode());
        updateDTO.setVersion(request.getVersion());
        updateDTO.setUpdatedBy(getAdUserIdNotNull());
        updateDTO.setLyyDistributorId(distributorId);

        merchantFunctionVersionClient.switchVersion(updateDTO);
        
        return ResponseUtils.success();
    }
}
