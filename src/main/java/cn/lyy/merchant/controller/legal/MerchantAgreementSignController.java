package cn.lyy.merchant.controller.legal;

import cn.lyy.base.dto.JsonObject;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.legal.MerchantAgreementQueryDTO;
import cn.lyy.merchant.dto.legal.MerchantAgreementSignDTO;
import cn.lyy.merchant.service.legal.MerchantAgreementSignService;
import cn.lyy.tools.constants.BaseResult;
import com.lyy.legal.interfaces.agreement.dto.request.AgreementBatchSignReqDTO;
import com.lyy.legal.interfaces.agreement.dto.response.LegalUserAgreementRespDTO;
import com.lyy.legal.interfaces.constants.LegalBasicCompanyEnum;
import com.lyy.legal.interfaces.constants.LegalBasicUserTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "商户协议签署")
@Slf4j
@RequestMapping("/agreement")
@RestController
public class MerchantAgreementSignController extends BaseController {

    @Autowired
    private MerchantAgreementSignService merchantAgreementService;
    
    @GetMapping("/getAgreement")
    @ApiOperation("获取所需协议列表")
    public JsonObject getAgreement(MerchantAgreementQueryDTO agreementQueryDTO) {
        agreementQueryDTO.setAdOrgId(getAdOrgIdNotNull());
        agreementQueryDTO.setCompanyCode(LegalBasicCompanyEnum.LYY.getCode());
        agreementQueryDTO.setUserTypeCode(LegalBasicUserTypeEnum.MERCHANT.getCode());
        List<LegalUserAgreementRespDTO> agreementInfo = merchantAgreementService.getAgreementInfo(agreementQueryDTO);
        return BaseResult.ok(agreementInfo);
    }
    
    @GetMapping("/getSignList")
    @ApiOperation("获取已签署协议")
    public JsonObject getMerchantSignList(@Valid MerchantAgreementQueryDTO agreementQueryDTO) {
        agreementQueryDTO.setAdOrgId(getAdOrgIdNotNull());
        agreementQueryDTO.setCompanyCode(LegalBasicCompanyEnum.LYY.getCode());
        agreementQueryDTO.setUserTypeCode(LegalBasicUserTypeEnum.MERCHANT.getCode());
        return BaseResult.ok(merchantAgreementService.getUserSignList(agreementQueryDTO));
    }
    
    @GetMapping("/agreementDetail")
    @ApiOperation("查看协议内容")
    public JsonObject agreementDetail(Long legalAgreementId, Long legalAgreementSignId) {
        String userId = getAdOrgIdNotNull().toString();
        return BaseResult.ok(merchantAgreementService.agreementDetail(legalAgreementId, legalAgreementSignId, userId));
    }
    
    @ApiOperation("批量签署协议")
    @PostMapping("/sign")
    public JsonObject sign(@Valid @RequestBody MerchantAgreementSignDTO signDTO) {
        log.info("签署批量签署协议参数: {}", signDTO);

        AgreementBatchSignReqDTO reqDTO = new AgreementBatchSignReqDTO();

        BeanUtils.copyProperties(signDTO, reqDTO);
        reqDTO.setUserId(String.valueOf(getAdOrgIdNotNull()));
        reqDTO.setCompanyCode(LegalBasicCompanyEnum.LYY.getCode());
        reqDTO.setUserTypeCode(LegalBasicUserTypeEnum.MERCHANT.getCode());
        merchantAgreementService.batchSignAgreement(reqDTO);
        return BaseResult.ok();
    }
    
    @ApiOperation("获取协议详情")
    @PostMapping("/info/list/get")
    public JsonObject getAgreementInfoList(@Valid @RequestBody List<Long> agreementIds){
        return BaseResult.ok(merchantAgreementService.getAgreementInfoList(agreementIds));
    }
}
