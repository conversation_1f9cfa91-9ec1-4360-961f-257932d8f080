package cn.lyy.merchant.controller;

import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.dto.Status;
import cn.lyy.base.utils.wechat.WeixinJsSignUtil;
import cn.lyy.merchant.controller.common.BaseController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.net.URLDecoder;
import java.util.Map;

/**
 * @ClassName: WeChatBusinessController
 * @description: 微信接口相关接口
 * @author: pengkun
 * @create: 2020-11-06 16:02
 * @Version 1.0
 **/
@Slf4j
@RestController
@RequestMapping("/rest/wx")
public class WeChatBusinessController extends BaseController {

    @Autowired
    private WeixinJsSignUtil weixinJsSignUtil;

    @Value("${wechant.appId}")
    private String appId;

    @Value("${wechant.appSecret}")
    private String appSecret;

    /**
     * 获取微信js-sdk配置信息
     * @param url
     * @return
     */
    @GetMapping("/jsapi/config")
    @AuthorityResource(name = "获取微信js-sdk配置信息", value = "mb_jsapi_config", seq = 12, role = "Saas_Merchant", parentValue = "Equipment")
    public BaseResponse getWeChatConfig(@RequestParam(value = "url") String url){
        BaseResponse baseResponse = new BaseResponse();
        try{
            //获取js-sdk配置信息
            Map<String, String> wxconfig = weixinJsSignUtil.appSign(URLDecoder.decode(url, "UTF-8"),appId,appId,appSecret);
            baseResponse.setData(wxconfig);
        }catch (Exception e){
            log.error(e.getMessage(), e);
            baseResponse.setMessage("获取微信配置文件失败");
            baseResponse.setCode(Status.STATUS_500);
        }
        return baseResponse;
    }

    @GetMapping("/qyh/jsapi/config")
    @AuthorityResource(name = "获取微信企业号js-sdk配置信息", value = "mb_qyh_jsapi_config", seq = 13, role = "Saas_Merchant", parentValue = "Equipment")
    public BaseResponse getWeChatQyhConfig(@RequestParam(value = "url") String url){
        BaseResponse baseResponse = new BaseResponse();
        try{
            //获取js-sdk配置信息
            Map<String, String> wxconfig = weixinJsSignUtil.qyhSystemSign(URLDecoder.decode(url, "UTF-8"));
            baseResponse.setData(wxconfig);
        }catch (Exception e){
            log.error(e.getMessage(), e);
            baseResponse.setMessage("获取微信配置文件失败");
            baseResponse.setCode(Status.STATUS_500);
        }
        return baseResponse;
    }
}
