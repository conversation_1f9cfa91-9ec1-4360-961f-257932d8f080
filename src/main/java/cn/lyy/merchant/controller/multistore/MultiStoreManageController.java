package cn.lyy.merchant.controller.multistore;

import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.base.utils.exception.BizException;
import cn.lyy.base.utils.text.JSONObjectUtils;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.multistore.MultiStoreListBizReqDTO;
import cn.lyy.merchant.service.multistore.MultiStoreManageService;
import cn.lyy_dto.wash.multistore.MultiStorePointDetailDTO;
import io.swagger.annotations.Api;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025/2/22 星期六 10:09
 * @description:
 */
@Slf4j
@RestController
@RequestMapping("rest/multi-store")
@Api(tags = "多级场地管理")
public class MultiStoreManageController extends BaseController {

    @Autowired
    private MultiStoreManageService multiStoreManageService;




    /**
     * 获取商家的所有多级场地列表
     */
    @PostMapping("/point/all")
    @AuthorityResource(name = "多级场地查询列表", value = "mb_wash_multi_store_list_all", seq = 5, role = "Saas_Merchant", parentValue = "Common")
    public BaseResponse<List<MultiStorePointDetailDTO>> allMultiStorePoint(@RequestBody MultiStoreListBizReqDTO request) {
        request.setMerchantId(getCurrentUser().getAdOrgId());
        request.setAdUserId(getCurrentUser().getAdUserId());
        log.debug("allMultiStorePoint:[{}]", JSONObjectUtils.toJSONString(request));
        try {
            request.setAdUserDTO(getCurrentUser());
            return multiStoreManageService.queryStoreLabel(request);
        } catch (BizException e) {
            return ResponseUtils.error(ResponseCodeEnum.FAIL.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("allMultiStorePoint error:[{}]", e.getMessage(), e);
            return ResponseUtils.error(ResponseCodeEnum.FAIL.getCode(), "查询失败");
        }

    }

}
