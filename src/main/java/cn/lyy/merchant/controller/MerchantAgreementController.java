package cn.lyy.merchant.controller;

import cn.hutool.json.JSONUtil;
import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.dto.JsonObject;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.AgreementBatchSignReqDTO;
import cn.lyy.merchant.dto.LegalUserAgreementRespDTO;
import cn.lyy.merchant.dto.MerchantAgreementQueryDTO;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import cn.lyy.merchant.microservice.MerchantAgreementService;
import cn.lyy.tools.constants.BaseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR> =￣ω￣=
 * @date 2022/12/5
 */
@Slf4j
@RequestMapping("/rest/agreement")
@RestController
public class MerchantAgreementController extends BaseController {

    @Autowired
    private MerchantAgreementService merchantAgreementService;

    /**
     * 获取所需协议列表-未登录情况也可以访问
     */
    @GetMapping("/getAgreement")
    @AuthorityResource(name = "获取所需协议列表",value = "mb_merchant_agreement_item",role = "ROLE_MERCHANT_NOT_LOGIN")
    public BaseResponse getAgreement(MerchantAgreementQueryDTO agreementQueryDTO) {
        AdUserInfoDTO currentUser = getCurrentUser();
        agreementQueryDTO.setAdOrgId(Math.toIntExact(currentUser.getAdOrgId()));
        agreementQueryDTO.setUserId(String.valueOf(currentUser.getAdOrgId()));
        log.debug("currentUser: {}", JSONUtil.toJsonStr(currentUser));
//        log.info("currentUser.getAdOrgId(): {}", JSONUtil.toJsonStr(currentUser.getAdOrgId()));
        agreementQueryDTO.setCompanyCode("LYY");
        agreementQueryDTO.setUserTypeCode("MERCHANT");
        log.debug("agreementQueryDTO: {}", JSONUtil.toJsonStr(agreementQueryDTO));
        BaseResponse<List<LegalUserAgreementRespDTO>> agreementInfo = merchantAgreementService.getUserAgreementInfo(agreementQueryDTO);
        return agreementInfo;
    }

    /**
     * 签署协议-支持批量签署
     *
     * @param signDTO
     * @return
     */
    @PostMapping("/sign")
    @AuthorityResource(name = "批量签署协议",value = "mb_merchant_sign_agreement",role = "Saas_Merchant")
    public JsonObject sign(@Valid @RequestBody AgreementBatchSignReqDTO signDTO) {
        log.info("B端签署批量签署协议参数: {}", signDTO);

        AgreementBatchSignReqDTO reqDTO = new AgreementBatchSignReqDTO();

        BeanUtils.copyProperties(signDTO, reqDTO);
        AdUserInfoDTO currentUser = getCurrentUser();
        reqDTO.setUserId(String.valueOf(currentUser.getAdOrgId()));
        reqDTO.setCompanyCode("LYY");
        reqDTO.setUserTypeCode("MERCHANT");
        log.debug("AgreementBatchSignReqDTO: {}", JSONUtil.toJsonStr(reqDTO));
        ResponseUtils.checkResponse(merchantAgreementService.batchSignAgreement(reqDTO));
        return BaseResult.ok();
    }
}
