package cn.lyy.merchant.controller.group;

import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.dto.Status;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.group.GroupLabelRequestDTO;
import cn.lyy.merchant.service.group.EquipmentGroupLabelService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName: EquipmentGroupLabelController
 * @description: 场地标签
 * @author: pengkun
 * @create: 2020-11-15 15:20
 * @Version 1.0
 **/
@RestController
@RequestMapping("/rest/equipment/group/label")
@Slf4j
public class EquipmentGroupLabelController  extends BaseController {

    @Autowired
    private EquipmentGroupLabelService equipmentGroupLabelService;

    /**
     * 保存或修改场地标签
     * @param dto
     * @return
     */
    @PostMapping("/save")
    @AuthorityResource(name = "保存或修改场地标签", value = "mb_group_label_save", seq = 7, role = "Saas_Merchant", parentValue = "MerchantShop")
    public BaseResponse saveOrUpdate(@RequestBody GroupLabelRequestDTO dto){
        if(StringUtils.isBlank(dto.getLabelName())){
            return ResponseUtils.error(Status.STATUS_PARAMETER_ERROR,"标签名称为空");
        }
        dto.setAdOrgId(getAdOrgIdNotNull());
        dto.setAdUserId(getAdUserIdNotNull());
        return ResponseUtils.success(equipmentGroupLabelService.saveOrUpdate(dto));
    }

    /**
     * 删除场地标签
     * @param dto
     * @return
     */
    @PostMapping("/deleteLabel")
    @AuthorityResource(name = "删除场地标签", value = "mb_group_label_delete", seq = 8, role = "Saas_Merchant", parentValue = "MerchantShop")
    public BaseResponse deleteLabel(@RequestBody GroupLabelRequestDTO dto){
        if(dto.getLabelId() == null){
            return ResponseUtils.error(Status.STATUS_PARAMETER_ERROR,"请选择需要删除的标签");
        }
        dto.setAdOrgId(getAdOrgIdNotNull());
        dto.setAdUserId(getAdUserIdNotNull());
        return ResponseUtils.success(equipmentGroupLabelService.deleteLabel(dto));
    }

    /**
     * 获取场地标签
     * @param dto
     * @return
     */
    @AuthorityResource(name = "获取场地标签", value = "mb_group_label_list", seq = 9, role = "Saas_Merchant", parentValue = "MerchantShop")
    @PostMapping("/getGroupLabels")
    public BaseResponse getGroupLabels(@RequestBody GroupLabelRequestDTO dto){
        dto.setAdOrgId(getAdOrgIdNotNull());
        dto.setAdUserId(getAdUserIdNotNull());
        return ResponseUtils.success(equipmentGroupLabelService.getGroupLabels(dto));
    }

}
