package cn.lyy.merchant.controller.group;

import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.equipment.service.IEquipmentService;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.group.GroupVO;
import cn.lyy.merchant.dto.group.SaveGroupDTO;
import cn.lyy.merchant.dto.merchant.response.MerchantGroupDTO;
import cn.lyy.merchant.dto.response.MerchantEquipmentDTO;
import cn.lyy.merchant.dto.response.MerchantGroupListDTO;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import cn.lyy.merchant.service.IEquipmentGroupBusinessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: EquipmentGroupBusinessController
 * @description: 设备场地
 * @author: pengkun
 * @create: 2020-11-03 14:42
 * @Version 1.0
 **/
@RestController
@RequestMapping("/rest/equipment/group")
@Slf4j
public class EquipmentGroupBusinessController extends BaseController {

    @Autowired
    private IEquipmentGroupBusinessService equipmentGroupBusinessService;

    @Resource
    private IEquipmentService equipmentService;

    /**
     * 获取当前账号下的场地列表
     * @return
     */
    @GetMapping("/getGroupNameAndAddress")
    @AuthorityResource(name = "获取当前账号下的场地列表", value = "mb_group_list", seq = 1, role = "Saas_Merchant", parentValue = "MerchantShop")
    public BaseResponse<List<MerchantGroupListDTO>> getGroupNameAndAddress(@RequestParam(value = "isShowCount",required = false) boolean isShowCount,
                                                                           @RequestParam(value = "context",required = false) String context,
                                                                           @RequestParam(value = "labelId", required = false) Long labelId){
        List<MerchantGroupListDTO> list = equipmentGroupBusinessService.getGroupInfoDistributorId(getAdOrgIdNotNull(), getAdUserIdNotNull(),
                isShowCount,context, labelId);
        return new BaseResponse<>(list);
    }

    /**
     * 根据设备类型ID 获取有相同设备类型的场地信息
     * @param equipmentTypeId
     * @return
     */
    @GetMapping(value = "/listGroupAndEq",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @AuthorityResource(name="列出所有场地及设备数",value="mb_listGroupAndEq",role = "Saas_Merchant",parentValue = "Common")
    public BaseResponse<List<GroupVO>> getGroupByEtype(Long equipmentTypeId){
        List<GroupVO> list =  equipmentGroupBusinessService.getGroupByEtype(equipmentTypeId,this.getCurrentUser().getAdOrgId());
        return ResponseUtils.success(list);
    }

    /**
     * 通过设备类型和协议查询 场地及设备信息
     * @return
     */
    @GetMapping(value = "/getGroupEquipmentByTypeProduct")
    @AuthorityResource(name="通过设备类型和主板Id获取场地及设备信息",value="mb_get_GE_product",role = "Saas_Merchant",parentValue = "Common")
    public BaseResponse<MerchantEquipmentDTO> getGroupEquipmentByTypeProduct(@RequestParam String equipmentType, @RequestParam Long productId){
        log.info("通过主板获取场地设备信息,equipmentType:{},productId:{}",equipmentType,productId);
        AdUserInfoDTO adUserInfoDTO =  this.getCurrentUser();
        MerchantEquipmentDTO merchantEquipmentDTO =  equipmentGroupBusinessService.getGroupEquipmentByTypeProduct(equipmentType,adUserInfoDTO.getAdUserId(),adUserInfoDTO.getIsApprover(),adUserInfoDTO.getAdOrgId(),productId);
        return ResponseUtils.success(merchantEquipmentDTO);
    }

    /**
     * 获取没有标签的场地列表
     * @return
     */
    @GetMapping("/getGroupNameAndAddressNoLabel")
    @AuthorityResource(name = "获取没有标签的场地列表", value = "mb_group_list_no_label", seq = 2, role = "Saas_Merchant", parentValue = "MerchantShop")
    public BaseResponse<List<MerchantGroupDTO>> getGroupNameAndAddressNoLabel(@RequestParam(value = "isShowCount",required = false) boolean isShowCount,
                                                                              @RequestParam(value = "typeValue",required = false) String typeValue){
        List<MerchantGroupDTO> list = equipmentGroupBusinessService.getGroupNameAndAddressNoLabel(getAdOrgIdNotNull(), getAdUserIdNotNull(),isShowCount,typeValue);
        return new BaseResponse<>(list);
    }

    /**
     * 保存店铺/场地
     * @param param
     * @return
     */
    @PostMapping("/save")
    @AuthorityResource(name = "保存店铺/场地", value = "mb_group_save", seq = 3, role = "Saas_Merchant", parentValue = "MerchantShop")
    public BaseResponse<Long> saveGroup(@RequestBody SaveGroupDTO param) {
        param.setDistributorId(getAdOrgIdNotNull());
        param.setUserId(getAdUserIdNotNull());
        return ResponseUtils.success(equipmentGroupBusinessService.saveGroup(param));
    }

    /**
     * 删除场地
     * @param groupId
     * @return
     */
    @GetMapping("/delete")
    @AuthorityResource(name = "删除场地", value = "mb_group_delete", seq = 4, role = "Saas_Merchant", parentValue = "MerchantShop")
    public BaseResponse deleteGroup(@RequestParam Long groupId) {
        equipmentGroupBusinessService.deleteGroup(groupId, getAdOrgIdNotNull(), getAdUserIdNotNull());
        return ResponseUtils.success();
    }

    /**
     * 根据场地id获取场地信息
     * @param groupId
     * @return
     */
    @GetMapping("/info/byId")
    @AuthorityResource(name = "根据场地id获取场地信息", value = "mb_group_info_id", seq = 5, role = "Saas_Merchant", parentValue = "MerchantShop")
    public BaseResponse getGroupInfoById(@RequestParam("groupId") Long groupId){
        log.debug("根据场地id获取场地信息,groupId:{}",groupId);
        return ResponseUtils.success(equipmentGroupBusinessService.getGroupInfoById(groupId));
    }

    /**
     * 根据场地查询已有的机台编号
     * @param groupId
     * @return
     */
    @GetMapping("/device/number/list")
    @AuthorityResource(name = "根据场地id获取场地信息", value = "mb_group_number_list", seq = 5, role = "Saas_Merchant", parentValue = "Equipment")
    public BaseResponse<List<Integer>> queryGroupNumber(@RequestParam("groupId") Long groupId) {
        return ResponseUtils.success(equipmentGroupBusinessService.queryGroupNumber(groupId, getAdOrgIdNotNull()));
    }

    /**
     * 获取省市区级联信息
     * @return
     */
    @GetMapping("/loadDistrictData")
    @AuthorityResource(name = "获取省市区级联信息", value = "mb_group_loadDistrictData", seq = 6, role = "Saas_Merchant", parentValue = "MerchantShop")
    public BaseResponse<List<Map<String, Object>>> loadDistrictData(){
        BaseResponse<List<Map<String, Object>>> baseResponse = new BaseResponse<>();
        List<Map<String, Object>> dataList = equipmentGroupBusinessService.getAllDistrict();
        if(dataList == null || dataList.size() == 0 ){
            log.error("获取省市区级联信息失败");
            baseResponse.setCode(ResponseCodeEnum.FAIL.getCode());
            baseResponse.setMessage("获取省市区级联信息失败");
        }else {
            baseResponse.setData(dataList);
        }
        return baseResponse;
    }



}
