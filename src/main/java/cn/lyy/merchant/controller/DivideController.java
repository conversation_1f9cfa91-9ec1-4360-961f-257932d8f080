package cn.lyy.merchant.controller;

import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.merchant.controller.common.UserModelContrller;
import cn.lyy.merchant.dto.account.DivideUserDTO;
import cn.lyy.merchant.dto.common.DivideDTO;
import cn.lyy.merchant.service.DivideService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @description: 分账接口
 * @author: qgw
 * @date on 2021/3/5.
 * @Version: 1.0
 */
@Slf4j
@RestController
@RequestMapping("/rest/divide")
public class DivideController extends UserModelContrller {

    @Autowired
    private DivideService divideService;
    /**
     获取商户分成人员列表
     * @param param
     * @return
     */
    @PostMapping("/getDivideUser")
    @AuthorityResource(name = "获取商户分成人员列表", value = "mb_getDivideUser", seq = 50, parentValue = "SubAccount", role = "Saas_Merchant")
    public BaseResponse<List<DivideUserDTO>> getDivideUser(@ModelAttribute(value = "param") JSONObject param){
        log.debug("获取商户分成人员列表 param:{}", param);
        DivideDTO idDTO = JSON.toJavaObject(param, DivideDTO.class);
        return success(divideService.getDivideUsers(idDTO));
    }

}
