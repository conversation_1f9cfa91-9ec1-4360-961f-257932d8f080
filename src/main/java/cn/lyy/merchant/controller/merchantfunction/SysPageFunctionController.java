package cn.lyy.merchant.controller.merchantfunction;

import cn.lyy.merchant.constants.SysPageFunctionConstants;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.user.UserExtentDTO;
import cn.lyy.merchant.service.merchantfunction.SysPageFunctionService;
import com.lyy.ram.service.interfaces.dto.function.request.MarketingFunctionQueryReq;
import com.lyy.ram.service.interfaces.dto.function.request.PersonalFunctionQueryReq;
import com.lyy.ram.service.interfaces.dto.function.request.PersonalFunctionSaveReq;
import com.lyy.ram.service.interfaces.dto.function.request.PopupDisplayRecordCreateReq;
import com.lyy.ram.service.interfaces.dto.function.response.FunctionCenterResp;
import com.lyy.ram.service.interfaces.dto.function.response.MarketingFunctionResp;
import com.lyy.ram.service.interfaces.dto.function.response.PersonalFunctionResp;
import com.lyy.ram.service.interfaces.feign.IFunctionFeignClient;
import com.lyy.starter.common.resp.RespBody;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * 页面功能 controller
 *
 * <AUTHOR>
 * @since 2024-03-05
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/sys-page-function")
public class SysPageFunctionController extends BaseController {

    private final IFunctionFeignClient functionFeignClient;
    private final SysPageFunctionService sysPageFunctionService;

    /**
     * 功能列表(首页-根据个人的权限设置和排序)
     */
    @GetMapping("/personal/list")
    public RespBody<PersonalFunctionResp> getPersonalFunction(@RequestParam("pageId") String pageId) {
        PersonalFunctionQueryReq req = new PersonalFunctionQueryReq();
        req.setAuthSystemId(SysPageFunctionConstants.AUTH_SYSTEM_ID);
        req.setAuthUserId(String.valueOf(currentAuthorityUserId()));
        req.setPageId(pageId);
        req.setQueryFunctionArea(true);
        req.setQueryBannerFunction(true);
        req.setQueryOtherFunction(true);
        return sysPageFunctionService.getPersonalFunction(getCurrentUser(),req,Boolean.TRUE.equals(getCurrentUserExtend().getIsWalletSettlement()));
    }

    /**
     * 保存个人功能列表设置
     */
    @PostMapping("/personal/save")
    public RespBody<Boolean> savePersonalFunction(@RequestBody PersonalFunctionSaveReq req) {
        req.setAuthSystemId(SysPageFunctionConstants.AUTH_SYSTEM_ID);
        req.setPageId(req.getPageId());
        req.setAuthUserId(String.valueOf(currentAuthorityUserId()));
        return functionFeignClient.savePersonalFunction(req);
    }

    /**
     * 功能中心
     */
    @GetMapping("/center")
    public RespBody<FunctionCenterResp> functionCenter(@RequestParam("pageId") String pageId) {
        PersonalFunctionQueryReq req = new PersonalFunctionQueryReq();
        req.setAuthSystemId(SysPageFunctionConstants.AUTH_SYSTEM_ID);
        req.setAuthUserId(String.valueOf(currentAuthorityUserId()));
        req.setPageId(pageId);
        return sysPageFunctionService.getFunctionCenter(getCurrentUser(), req,Boolean.TRUE.equals(getCurrentUserExtend().getIsWalletSettlement()));
    }

    /**
     * 弹窗列表
     */
    @GetMapping("/popup/list")
    public RespBody<PersonalFunctionResp> getPopupFunction(@RequestParam("pageId") String pageId) {
        PersonalFunctionQueryReq req = new PersonalFunctionQueryReq();
        req.setAuthSystemId(SysPageFunctionConstants.AUTH_SYSTEM_ID);
        req.setAuthUserId(String.valueOf(currentAuthorityUserId()));
        req.setPageId(pageId);
        req.setQueryFunctionArea(false);
        req.setQueryPopupFunction(true);
        return sysPageFunctionService.getPopupFunction(getCurrentUser(), req);
    }

    /**
     * 保存弹窗展示记录
     */
    @PostMapping("/popup/record")
    public RespBody<Boolean> savePopupRecord(@RequestBody PopupDisplayRecordCreateReq req) {
        req.setAuthSystemId(SysPageFunctionConstants.AUTH_SYSTEM_ID);
        req.setUserId(String.valueOf(currentAuthorityUserId()));
        return functionFeignClient.savePopupDisplayRecord(req);
    }

    /**
     * 首页营销中心列表
     */
    @GetMapping("/marketing/homepage")
    public RespBody<MarketingFunctionResp> getMarketingFunction(@RequestParam("pageId") String pageId) {
        MarketingFunctionQueryReq req = new MarketingFunctionQueryReq();
        req.setPageId(pageId);
        req.setAuthSystemId(SysPageFunctionConstants.AUTH_SYSTEM_ID);
        req.setAuthUserId(String.valueOf(currentAuthorityUserId()));
        return sysPageFunctionService.getMarketingFunction(getCurrentUser(), req,Boolean.TRUE.equals(getCurrentUserExtend().getIsWalletSettlement()));
    }

    /**
     * 营销中心
     */
    @GetMapping("/marketing/center")
    public RespBody<MarketingFunctionResp> getMarketingCenter(@RequestParam("pageId") String pageId) {
        MarketingFunctionQueryReq req = new MarketingFunctionQueryReq();
        req.setPageId(pageId);
        req.setAuthSystemId(SysPageFunctionConstants.AUTH_SYSTEM_ID);
        req.setAuthUserId(String.valueOf(currentAuthorityUserId()));
        return sysPageFunctionService.getMarketingCenter(getCurrentUser(), req,Boolean.TRUE.equals(getCurrentUserExtend().getIsWalletSettlement()));
    }

}
