package cn.lyy.merchant.controller.merchantfunction;

import cn.lyy.authority_service_api.AdResourcesDTO;
import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.equipment.dto.equipment.EquipmentTypeDTO;
import cn.lyy.merchant.api.service.MerchantWhiteClient;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.controller.onedata.dto.DayParamDto;
import cn.lyy.merchant.service.equipment.EquipmentTypeService;
import cn.lyy.merchant.service.merchantfunction.MerchantFunctionService;
import cn.lyy.merchant.service.merchantfunction.dto.FunctionRule;
import cn.lyy.merchant.service.merchantfunction.dto.MerchantDto;
import cn.lyy.merchant.service.merchantfunction.dto.SystemFunction;
import cn.lyy.merchant.service.merchantfunction.fegin.AuthorityFeign;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/authority/merchant/function")
public class MerchantFunctionRuleController extends BaseController {

    @Autowired
    private MerchantFunctionService merchantFunctionService;

    @Autowired
    private EquipmentTypeService equipmentTypeService;

    @Autowired
    private MerchantWhiteClient merchantWhiteClient;

    @Autowired
    private AuthorityFeign authorityFeign;

    @Value("${merchantFunction.isNewMerchant}")
    private String isNewMerchant;

    @GetMapping
    @AuthorityResource(name = "获取商家功能菜单", value = "mf_merchant_system_functions", seq = 1, role = "Saas_Merchant", parentValue = "mf_b_function")
    public BaseResponse getReportData(FunctionRule functionRule) {
        //log.info("商家id：{}",getAdOrgIdNotNull());
        MerchantDto merchant = new MerchantDto();
        merchant.setId(getAdOrgIdNotNull());
        Map<String,List<SystemFunction>> matchedFunctionMap = new HashMap<>();
        List<JSONObject> matchedGroupFunctionMap = new ArrayList<>();
        Map<Long,JSONObject> matchedObject = new HashMap<>();
        try{
            //获取商家设备类型
            List<EquipmentTypeDTO> list =  equipmentTypeService.selectAllEquipmentType(getCurrentUser().getAdUserId(),getCurrentUser().getAdOrgId());
            //list = JSONArray.parseArray("[{\"type\":\"2\",\"equipmentTypeId\":1000028,\"balanceType\":2,\"name\":\"洗衣机\",\"value\":\"XYJ\"},{\"type\":\"8\",\"equipmentTypeId\":1001131,\"balanceType\":2,\"name\":\"售货机\",\"value\":\"SHJ\"}]",EquipmentTypeDTO.class);
            if(list != null && list.size() >0){
                //log.info("equipmentType:{}", JSONObject.toJSON(list).toString());
                Set<MerchantDto.EquipmentType> equipmentTypes = new HashSet<>();
                for(EquipmentTypeDTO equipmentTypeDTO: list){
                    MerchantDto.EquipmentType equipmentType = merchant.new EquipmentType();
                    equipmentType.setId(equipmentTypeDTO.getEquipmentTypeId()+"");
                    equipmentType.setName(equipmentTypeDTO.getName());
                    equipmentTypes.add(equipmentType);
                }
                if(equipmentTypes.size()>0){
                    merchant.setEquipmentTypes(equipmentTypes);
                }
            }

            //log.info("equipmentType:{}", JSONObject.toJSON(list).toString());
            BaseResponse<List<Integer>> witheList = merchantWhiteClient.getAllWhiteByDistributorId(getCurrentUser().getAdOrgId());
            if(witheList.getCode() == 0){
                //log.info("商家：{}，白名单：{}", merchant.getId(),witheList.getData());
                List<Integer> withes = witheList.getData();
                if(withes != null && withes.size()>0){
                    Set<MerchantDto.BlackWhiteList> blackWhiteLists = new HashSet<>();
                    for(Integer with : withes){
                        MerchantDto.BlackWhiteList blackWhiteList = merchant.new BlackWhiteList();
                        blackWhiteList.setType(1);
                        blackWhiteList.setId(with+"");
                        blackWhiteLists.add(blackWhiteList);
                    }
                    if(blackWhiteLists.size()>0){
                        merchant.setBlackWhiteLists(blackWhiteLists);
                    }
                }
            }


            //设置账户类型 TODO
            if(getCurrentUser().getIsApprover()){
                merchant.setAccountType(1l);
            }

            //获取账户权限
            Set<String> auths = getUserAuth();
            if(auths != null && auths.size() >0){
                Set<MerchantDto.Reouces> reouces = new HashSet<>();
                for(String auth: auths){
                    MerchantDto.Reouces reouce = merchant.new Reouces();
                    reouce.setCode(auth);
                    reouce.setId(auth);
                    reouce.setName(auth);
                    reouces.add(reouce);
                }
                if(reouces.size()>0){
                    merchant.setReouces(reouces);
                }
            }

            //处理系统菜单规则
            Map<String, Map<String,List>> functionRulesMap = merchantFunctionService.getFunctionRule(functionRule.getSystemId());


            //开始匹配商家和规则
            if(functionRulesMap != null && functionRulesMap.get("functions") != null){
                Map<String,List> functionMap = functionRulesMap.get("functions");
                Map<String,List> ruleMap = functionRulesMap.get("rules");

                Set<String> groupKeys = functionMap.keySet();
                if(groupKeys != null){
                    for(String groupKey : groupKeys){
                        List<SystemFunction> groupSystemFunction = functionMap.get(groupKey);
                        if(groupSystemFunction != null){
                            for(SystemFunction systemFunction : groupSystemFunction ){
                                //获取这个菜单配置的规则，如果没有配置的话就默认允许用户访问
                                List<FunctionRule> functionRuleList = ruleMap.get(systemFunction.getCode());
                                if(functionRuleList == null || functionRuleList.isEmpty()){
                                    List<SystemFunction> matchedFunctionList = matchedFunctionMap.get(groupKey);
                                    if(matchedFunctionList == null){
                                        matchedFunctionList = new ArrayList<>();
                                    }

                                    matchedObject.put(systemFunction.getId(),JSONObject.parseObject(JSONObject.toJSONString(systemFunction)));
                                    matchedFunctionList.add(systemFunction);
                                    matchedFunctionMap.put(groupKey,matchedFunctionList);
                                }else{
                                    //商户是否符合该规则
                                    //log.info("登录账户匹配信息:{}",JSONObject.toJSONString(merchant));
                                    if(merchantFunctionService.checkMerchantFunction(merchant,functionRuleList)){
                                        List<SystemFunction> matchedFunctionList = matchedFunctionMap.get(groupKey);
                                        if(matchedFunctionList == null){
                                            matchedFunctionList = new ArrayList<>();
                                        }

                                        matchedObject.put(systemFunction.getId(),JSONObject.parseObject(JSONObject.toJSONString(systemFunction)));
                                        matchedFunctionList.add(systemFunction);
                                        matchedFunctionMap.put(groupKey,matchedFunctionList);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            //处理最后的包装，匹配到的每个分组的功能菜单
            Set<String> keys = matchedFunctionMap.keySet();
            //辅助类，用于哪些以及遍历过的，后续子分组要挂到这个类下
            Map<String, JSONArray> doChildMap = new HashMap<>();
            Map<String, JSONObject> allGroupMap = new HashMap<>();
            if(keys != null && keys.size()>0){
                for(String key : keys){
                    List<SystemFunction> groupSystemFunction = matchedFunctionMap.get(key);
                    //非功能菜单
                    if(groupSystemFunction != null && groupSystemFunction.size()>0 && groupSystemFunction.get(0) != null && groupSystemFunction.get(0).getType() == 2){
                        JSONObject obj = new JSONObject();
                        obj.put("group",key);
                        obj.put("functionGroupName",groupSystemFunction.get(0).getFunctionGroupName());
                        obj.put("groupOrderIndex",groupSystemFunction.get(0).getGroupOrderIndex());
                        obj.put("superScript",groupSystemFunction.get(0).getSuperScript());
                        //处理孩子节点
                        if(doChildMap.get(key) != null){
                            obj.put("groups",doChildMap.get(key));
                        }
                        allGroupMap.put(key,obj);
                        //如果没有父亲节点，证明是一级节点
                        if(groupSystemFunction.get(0).getParentId() == null){
                            matchedGroupFunctionMap.add(obj);
                        }else{
                            //1、拿到父亲节点的分组
                            String parentGroup = matchedObject.get(groupSystemFunction.get(0).getParentId()).getString("functionGroup");
                            //父亲节点存在
                            JSONObject parent = allGroupMap.get(parentGroup);
                            if(parent != null){
                                JSONArray parentGroups = parent.getJSONArray("groups");
                                if(parentGroups == null){
                                    parentGroups = new JSONArray();
                                }
                                parentGroups.add(obj);
                                parent.put("groups",parentGroups);
                            }else{
                                //父亲节点还不存在
                                JSONArray parentGroups = doChildMap.get(parentGroup);
                                if(parentGroups == null){
                                    parentGroups = new JSONArray();
                                }
                                parentGroups.add(obj);
                                doChildMap.put(parentGroup,parentGroups);
                            }

                        }

                    }

                    //功能菜单
                    if(groupSystemFunction != null && groupSystemFunction.size()>0 && groupSystemFunction.get(0) != null && groupSystemFunction.get(0).getType() == 1){
                        JSONObject obj = new JSONObject();
                        obj.put("group",key);
                        obj.put("functionGroupName",groupSystemFunction.get(0).getFunctionGroupName());
                        obj.put("groupOrderIndex",groupSystemFunction.get(0).getGroupOrderIndex());
                        obj.put("superScript",groupSystemFunction.get(0).getSuperScript());
                        obj.put("functions",groupSystemFunction);

                        if(groupSystemFunction.get(0).getParentId() == null){
                            matchedGroupFunctionMap.add(obj);
                        }else{
                            //1、拿到父亲节点的分组
                            String parentGroup = matchedObject.get(groupSystemFunction.get(0).getParentId()).getString("functionGroup");
                            //父亲节点存在
                            JSONObject parent = allGroupMap.get(parentGroup);
                            if(parent != null){
                                JSONArray parentGroups = parent.getJSONArray("groups");
                                if(parentGroups == null){
                                    parentGroups = new JSONArray();
                                }
                                parentGroups.add(obj);
                                parent.put("groups",parentGroups);
                            }else{
                                //父亲节点还不存在
                                JSONArray parentGroups = doChildMap.get(parentGroup);
                                if(parentGroups == null){
                                    parentGroups = new JSONArray();
                                }
                                parentGroups.add(obj);
                                doChildMap.put(parentGroup,parentGroups);
                            }

                        }
                    }

                    //特殊处理前base分组前3个图标为大图标
                    if("base".equals(key) && groupSystemFunction != null && groupSystemFunction.size()>0){
                        for(int i=0; i<groupSystemFunction.size(); i++){
                            groupSystemFunction.get(i).setIcon(groupSystemFunction.get(i).getIcon().replace("-icon.","."));
                            if(i==2){
                                break;
                            }
                        }
                    }
                }
            }
        }catch (Exception e){
            log.error("{}",e.getStackTrace());
            return error(500,e.getMessage());
        }



        //log.info("商家id：{}",getAdOrgIdNotNull());

        return success(matchedGroupFunctionMap);
    }

    @GetMapping("/isNewMerchant")
    @AuthorityResource(name = "B端首页改造灰度", value = "mf_merchant_system_functions_isNewMerchant", seq = 1, role = "Saas_Merchant", parentValue = "mf_b_function")
    public BaseResponse getIsMainAccount(DayParamDto dayParamDto) {
        log.info("商家id：{}",getAdOrgIdNotNull());

        if(isNewMerchant.contains(getAdOrgIdNotNull().intValue()+"")){
            return success(true);
        }
        return success(false);
    }

    private Set<String> getUserAuth(){
        try {
            List<AdResourcesDTO> list = authorityFeign.getUserResource(getCurrentUser().getPhone(),3);
            Set<String> s = list.stream()
                    .map(item -> item.getValue())
                    .collect(Collectors.toSet());
            log.info("账户权限：{}",JSONObject.toJSONString(s));
            return s;
        }catch (Exception e){
            log.info("{}",e.fillInStackTrace());
        }
        return null;
    }
}
