package cn.lyy.merchant.controller;

import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.dto.Status;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.merchant.constants.BusinessExceptionEnums;
import cn.lyy.merchant.constants.SystemConstants;
import cn.lyy.merchant.controller.common.UserModelContrller;
import cn.lyy.merchant.dto.account.SubAccountUserInfoDTO;
import cn.lyy.merchant.dto.auth.RoleDTO;
import cn.lyy.merchant.dto.common.IdDTO;
import cn.lyy.merchant.dto.common.UserInfoDTO;
import cn.lyy.merchant.dto.menu.MerchantAuthMenuDTO;
import cn.lyy.merchant.dto.menu.MerchantAuthMenuGetDTO;
import cn.lyy.merchant.dto.template.IdNameVO;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import cn.lyy.merchant.dto.user.SubAccountDTO;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.service.UserService;
import cn.lyy.merchant.utils.ValidatorUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @description: 子账号、岗位管理 员工账号接口
 * @author: qgw
 * @date on 2021/3/3.
 * @Version: 1.0
 */
@Slf4j
@RequestMapping("/rest/subaccount")
@RestController
public class SubAccountController extends UserModelContrller {

    @Autowired
    private UserService userService;

    /**
     新建子账号
     * @param param
     * @return
     */
    @PostMapping("/createSubAccount")
    @AuthorityResource(name = "新建子账号", value = "mb_createSubAccount", seq = 9, parentValue = "MerchantShop", role = "Saas_Merchant")
    public BaseResponse createSubAccount(@ModelAttribute(value = "param") JSONObject param){
        log.debug("新建子账号 param:{}", param);
        //只能主账号访问
        AdUserInfoDTO currentUser = getCurrentUser();
        checkUserNotSubAccount(currentUser);
        SubAccountDTO adUserDTO = JSON.toJavaObject(param, SubAccountDTO.class);
        //
        adUserDTO.setParentAdUserId(currentUser.getAdUserId());
        String phone = adUserDTO.getPhone();
        if(Strings.isNullOrEmpty(adUserDTO.getPassword())) {
            String pass = phone.substring(phone.length() - 6);
            adUserDTO.setPassword(pass);
        }
        adUserDTO.setRepeatPassword(adUserDTO.getPassword());
        BaseResponse resultVO = validRegister(adUserDTO);
        if(Objects.nonNull(resultVO)) {
            return resultVO;
        }
        if(Objects.isNull(adUserDTO.getUserOrgId()) ||
                CollectionUtils.isEmpty(adUserDTO.getRoleIds())) {
            return error(Status.STATUS_PARAMETER_ERROR, "参数错误！");
        }
        //这里不能带userId
        adUserDTO.setUserId(null);
        adUserDTO.setSub(true);
        try {
            userService.saveOrUpdateSubAccount(adUserDTO);
        } catch (BusinessException e) {
            log.warn("新建子账号异常:{}", e);
            return ResponseUtils.error(Status.STATUS_FAIL, e.getMsg());
        }
        return ResponseUtils.success();
    }

    /**
     * 更新账号
     * @param param
     * @return
     */
    @PostMapping("/updateSubAccount")
    @AuthorityResource(name = "更新账号", value = "mb_updateSubAccount", seq = 7, parentValue = "MerchantShop", role = "Saas_Merchant")
    public BaseResponse updateSubAccount(@ModelAttribute(value = "param") JSONObject param){
        log.debug("更新账号 param:{}", param);
        AdUserInfoDTO currentUser = getCurrentUser();
        checkUserNotSubAccount(currentUser);
        SubAccountDTO dto = JSON.toJavaObject(param, SubAccountDTO.class);
        dto.setParentAdUserId(currentUser.getAdUserId());
        dto.setUserId(param.getLong("id"));
        if(Objects.isNull(dto.getUserOrgId()) ||
                Objects.isNull(dto.getUserId()) ||
                Objects.isNull(dto.getAuthorityUserId()) ||
                CollectionUtils.isEmpty(dto.getRoleIds())) {
            return error(Status.STATUS_PARAMETER_ERROR, "参数错误！");
        }
        dto.setSub(true);
        try {
            userService.saveOrUpdateSubAccount(dto);
        } catch (BusinessException e) {
           log.error("更新账号异常:{}", e);
            return ResponseUtils.error(Status.STATUS_FAIL, e.getMsg());
        }
        return success();
    }

    /**
     删除子账号
     * @param param
     * @return
     */
    @PostMapping("/deleteSubAccount")
    @AuthorityResource(name = "删除子账号", value = "mb_deleteSubAccount", seq = 8, parentValue = "MerchantShop", role = "Saas_Merchant")
    public BaseResponse deleteSubAccount(@ModelAttribute(value = "param") JSONObject param){
        log.debug("删除子账号 param:{}", param);
        //只能主账号访问
        IdDTO idDTO = JSON.toJavaObject(param, IdDTO.class);
        if(!idDTO.getIsApprover()) {
            throw new BusinessException(BusinessExceptionEnums.SUB_ACCOUNT_NOT_PERMIT);
        }
        if(Objects.isNull(idDTO.getUserOrgId()) ||
                Objects.isNull(idDTO.getId())) {
            return error(Status.STATUS_PARAMETER_ERROR, "参数错误！");
        }
        //这里不能带userId
        idDTO.setUserId(null);
      return   userService.deleteSubAccount(idDTO);
    }


    /**
     * @param param
     * @return
     */
    @PostMapping("/getSubAccountInfos")
    @AuthorityResource(name = "获取员工账号列表或指定员工账号信息", value = "mb_getSubAccountInfos", seq = 1, parentValue = "MerchantShop", role = "Saas_Merchant")
    public BaseResponse<List<SubAccountUserInfoDTO>> getSubAccountInfos(@ModelAttribute(value = "param") JSONObject param){
        log.debug("获取员工账号列表或指定员工账号信息:{}", param);
        //只能主账号访问
        checkUserNotSubAccount(getCurrentUser());
        IdDTO userInfoDTO = JSON.toJavaObject(param, IdDTO.class);

        return success(userService.getSubAccountInfos(userInfoDTO));
    }

    @PostMapping("/role/list")
    @AuthorityResource(name = "获取岗位列表", value = "mb_listSubAccountRole", seq = 2, parentValue = "MerchantShop", role = "Saas_Merchant")
    public BaseResponse<List<IdNameVO>> listSubAccountRole(@ModelAttribute(value = "param") JSONObject param){
        log.debug("获取岗位列表 param:{}", param);
        UserInfoDTO userInfoDTO = JSON.toJavaObject(param, UserInfoDTO.class);
        return success(userService.listSubAccountRole(userInfoDTO));
    }

    @PostMapping("/resource/list")
    @AuthorityResource(name = "获取所有岗位权限,用于创建岗位", value = "mb_listSubAccountResource", seq = 3, parentValue = "MerchantShop", role = "Saas_Merchant")
    public BaseResponse<List<MerchantAuthMenuDTO>> listSubAccountResource(@ModelAttribute(value = "param") JSONObject param){
        log.debug("listSubAccountResource param:{}", param);
        MerchantAuthMenuGetDTO userInfoDTO = JSON.toJavaObject(param, MerchantAuthMenuGetDTO.class);
        return success(userService.getUserAuthMenu(userInfoDTO));
    }

    @PostMapping("/role/getMyAuth")
    @AuthorityResource(name = "获取岗位详情(拥有的权限列表)", value = "mb_getSubAccountMyRoleAuth", seq = 4, parentValue = "MerchantShop", role = "Saas_Merchant")
    public BaseResponse<List<String>> getSubAccountMyRoleAuth(@ModelAttribute(value = "param") JSONObject param){
        log.debug("获取岗位详情(拥有的权限列表) param:{}", param);
        IdDTO idDTO = JSON.toJavaObject(param, IdDTO.class);
        return success(userService.getUserAuthMenu(idDTO));
    }

    /**
     * 返回角色ID
     * @param param
     * @return
     */
    @PostMapping("/role/save")
    @AuthorityResource(name = "创建/修改岗位", value = "mb_saveSubAccountRole", seq = 5, parentValue = "MerchantShop", role = "Saas_Merchant")
    public BaseResponse saveSubAccountRole(@ModelAttribute(value = "param") JSONObject param){
        log.debug("创建/修改岗位 param:{}", param);
        RoleDTO roleDTO = JSON.toJavaObject(param, RoleDTO.class);
        return userService.saveSubAccountRole(roleDTO);
    }

    /**
     //删除岗位，判断是否固定的三个岗位
     * @param param
     * @return
     */
    @PostMapping("/role/delete")
    @AuthorityResource(name = "删除岗位", value = "mb_deleteSubAccountRole", seq = 6, parentValue = "MerchantShop", role = "Saas_Merchant")
    public BaseResponse deleteSubAccountRole(@ModelAttribute(value = "param") JSONObject param){
        log.debug("删除岗位 param:{}", param);
        IdDTO idDTO = JSON.toJavaObject(param, IdDTO.class);
        return userService.deleteSubAccountRole(idDTO);
    }

    /**
     * 校验用户信息
     * @param adUserDTO
     * @return
     */
    public BaseResponse validRegister(SubAccountDTO adUserDTO) {
        /**
         * 校验入参
         */
        String error = ValidatorUtils.validDTO(adUserDTO);
        if(StringUtils.isNotBlank(error)) {
            return  error(Status.STATUS_PARAMETER_ERROR, error);
        }
        String newPassword = adUserDTO.getPassword();
        String repeatPassword = adUserDTO.getRepeatPassword();
        if (!validParameter(adUserDTO.getUserName())) {
            return error(Status.STATUS_FAIL, "请输入姓名！");
        }
        if (!validParameter(repeatPassword)) {
            return error(Status.STATUS_FAIL, "请输入确认密码！");
        }
        if (!newPassword.equals(repeatPassword)) {
            return error(Status.STATUS_FAIL, "两次输入的密码不一致！");
        }
        Pattern p = Pattern.compile(SystemConstants.PASSWORD_PATTERN);
        Matcher m = p.matcher(newPassword);
        if(!m.matches()){
            return error(Status.STATUS_FAIL, "密码需要6-20位的字符组合！");
        }
        return null;
    }

    private void checkUserNotSubAccount(AdUserInfoDTO userInfoDTO) {
        if(!userInfoDTO.getIsApprover()) {
                throw new BusinessException(BusinessExceptionEnums.SUB_ACCOUNT_NOT_PERMIT);
        }
    }
}
