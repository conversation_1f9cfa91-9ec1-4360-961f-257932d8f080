package cn.lyy.merchant.controller;

import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.merchant.api.service.StartRefundService;
import cn.lyy.merchant.controller.common.UserModelContrller;
import cn.lyy.merchant.dto.common.UserInfoDTO;
import cn.lyy.merchant.dto.merchant.response.startAndRefund.QueryStartAndRefundBatchDTO;
import cn.lyy.merchant.dto.merchant.response.startAndRefund.SaveStartAndRefundDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 *  启动与退费
 * <AUTHOR>
 * @version 1.0
 * @since 2020/11/21
 */
@Slf4j
@RestController
@RequestMapping("/rest/start-refund")
//@Api(tags = "启动与退费接口")
public class StartRefundController extends UserModelContrller {

    @Autowired
    private StartRefundService startRefundService;


    @PostMapping("/getGroupAndEquipmentTypeList")
    @AuthorityResource(name = "获取场地与品类列表", value = "mb_getGroupAndEquipmentTypeList", seq = 201, parentValue = "Plugin-StartRefund", role = "Saas_Merchant")
    public BaseResponse<QueryStartAndRefundBatchDTO> getGroupAndEquipmentTypeList(@ModelAttribute(value = "param") JSONObject param){
        UserInfoDTO userInfoDTO = JSON.toJavaObject(param, UserInfoDTO.class);
        return startRefundService.getGroupAndEquipmentTypeList(userInfoDTO);
    }

    @PostMapping("/getGroupAndConfig")
    @AuthorityResource(name = "获取场地、品类、默认配置", value = "mb_getGroupAndConfig", seq = 202, parentValue = "Plugin-StartRefund", role = "Saas_Merchant")
    public BaseResponse<QueryStartAndRefundBatchDTO> getGroupAndConfig(@ModelAttribute(value = "param") JSONObject param){
        UserInfoDTO userInfoDTO = JSON.toJavaObject(param, UserInfoDTO.class);
        return startRefundService.getGroupAndConfig(userInfoDTO);
    }

    @PostMapping("/saveRefundConfig")
    @AuthorityResource(name = "保存退费配置", value = "mb_saveRefundConfig", seq = 203, parentValue = "Plugin-StartRefund", role = "Saas_Merchant")
    public BaseResponse saveRefundConfig(@ModelAttribute(value = "param") JSONObject param){
        SaveStartAndRefundDTO saveDTO = JSON.toJavaObject(param, SaveStartAndRefundDTO.class);
        return startRefundService.saveRefundConfig(saveDTO);
    }
}
