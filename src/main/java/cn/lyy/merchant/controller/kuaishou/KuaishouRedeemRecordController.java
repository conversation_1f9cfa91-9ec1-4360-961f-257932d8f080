package cn.lyy.merchant.controller.kuaishou;

import static java.util.Optional.of;
import static java.util.Optional.ofNullable;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.marketing.api.service.KuaishouActivityClient;
import cn.lyy.marketing.dto.kuaishou.KuaishouRedeemRecordItemDTO;
import cn.lyy.marketing.dto.kuaishou.KuaishouRedeemRecordQuery;
import cn.lyy.merchant.api.service.EquipmentTypeClient;
import cn.lyy.merchant.api.service.MerchantGroupService;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.equipment.EquipmentTypeDTO;
import cn.lyy.merchant.dto.merchant.request.MerchantGroupRequest;
import cn.lyy.merchant.dto.merchant.response.MerchantGroupDTO;
import cn.lyy.merchant.utils.RemoteResponseUtils;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2023/10/10
 */
@Api(tags = "商家快手核销记录")
@RestController
@RequestMapping("/rest/kuaishou/redeem-record")
@Slf4j
public class KuaishouRedeemRecordController extends BaseController {



    @Autowired
    private KuaishouActivityClient kuaishouActivityClient;

    @Autowired
    private MerchantGroupService merchantGroupService;

    @Autowired
    private EquipmentTypeClient equipmentTypeClient;

    @ApiOperation("核销记录")
    @PostMapping({"/kuaishouActivity/redeem/record"})
    public BaseResponse<PageInfo<KuaishouRedeemRecordItemDTO>> queryRedeemRecordPage(@RequestBody KuaishouRedeemRecordQuery query) {
        query.setDistributorId(getAdOrgIdNotNull());
        fillQuery(query);

        BaseResponse<PageInfo<KuaishouRedeemRecordItemDTO>> response = kuaishouActivityClient.queryRedeemRecordPage(query);
        PageInfo<KuaishouRedeemRecordItemDTO> data = RemoteResponseUtils.getData(response);
        fillResult(data);

        return response;
    }

    private void fillResult(PageInfo<KuaishouRedeemRecordItemDTO> data) {
        if (Objects.isNull(data) || CollUtil.isEmpty(data.getList())) {
            return;
        }

        List<Long> typeIds = data.getList().stream().map(KuaishouRedeemRecordItemDTO::getEquipmentTypeId).distinct()
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(typeIds)) {
            List<EquipmentTypeDTO> typeDTOList = RemoteResponseUtils.getCommonData(equipmentTypeClient.findByTypeIds(typeIds));
            if (CollUtil.isNotEmpty(typeDTOList)) {
                Map<Long, EquipmentTypeDTO> map = typeDTOList.stream()
                        .collect(Collectors.toMap(EquipmentTypeDTO::getId, Function.identity(), (v1, v2) -> v1));
                data.getList().forEach(o ->
                        o.setEquipmentTypeName(ofNullable(map.get(o.getEquipmentTypeId())).map(EquipmentTypeDTO::getName).orElse("")));
            }
        }

        of(data.getList())
                .map(o -> o.stream().map(KuaishouRedeemRecordItemDTO::getGroupId).distinct().collect(Collectors.toList()))
                .filter(CollUtil::isNotEmpty)
                .map(o -> {
                    MerchantGroupRequest request = new MerchantGroupRequest();
                    request.setDistributor(getAdOrgIdNotNull());
                    request.setGroups(o);

                    return RemoteResponseUtils.getData(merchantGroupService.listGroup(request));
                })
                .filter(CollUtil::isNotEmpty)
                .map(o -> o.stream().collect(Collectors.toMap(MerchantGroupDTO::getEquipmentGroupId, Function.identity(), (v1, v2) -> v1)))
                .ifPresent(map -> data.getList().forEach(o -> o
                        .setGroupName(ofNullable(map.get(o.getGroupId())).map(MerchantGroupDTO::getName).orElse(""))));


    }

    private void fillQuery(KuaishouRedeemRecordQuery query) {
        if (CharSequenceUtil.isBlank(query.getQuery())) {
            return;
        }

        MerchantGroupRequest request = new MerchantGroupRequest();
        request.setDistributor(getAdOrgIdNotNull());
        //        request.setIsActive(1);
        request.setGroupName(query.getQuery());

        BaseResponse<List<MerchantGroupDTO>> listBaseResponse = merchantGroupService.listGroup(request);
        List<Long> fuzzyGroupIds = ofNullable(RemoteResponseUtils.getData(listBaseResponse))
                .filter(CollUtil::isNotEmpty)
                .map(o -> o.stream().map(MerchantGroupDTO::getEquipmentGroupId).collect(Collectors.toList()))
                .orElse(Collections.emptyList());

        query.setFuzzyGroupIds(fuzzyGroupIds);
    }

}
