package cn.lyy.merchant.controller.kuaishou;

import cn.hutool.core.collection.CollUtil;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.dto.JsonObject;
import cn.lyy.base.exception.ServiceException;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.marketing.api.service.KuaishouActivityClient;
import cn.lyy.marketing.dto.kuaishou.KuaishouActivityPoiRequestDTO;
import cn.lyy.marketing.dto.kuaishou.KuaishouPoiGroupItemDTO;
import cn.lyy.marketing.dto.kuaishou.KuaishouPoiQuery;
import cn.lyy.marketing.dto.kuaishou.MerchantKuaishouAccountDTO;
import cn.lyy.marketing.dto.kuaishou.MerchantKuaishouPoiAssociateDTO;
import cn.lyy.marketing.dto.kuaishou.MerchantKuaishouPoiDetailDTO;
import cn.lyy.marketing.dto.kuaishou.MerchantKuaishouPoiInfoDTO;
import cn.lyy.merchant.api.service.MerchantGroupService;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.merchant.request.MerchantGroupRequest;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import cn.lyy.merchant.utils.RemoteResponseUtils;
import cn.lyy.tools.constants.BaseResult;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2023/10/10
 */
@Api(tags = "快手商家门店")
@RestController
@RequestMapping("/rest/kuaishou/poi")
@Slf4j
public class KuaishouPoiController extends BaseController {

  @Autowired
  private KuaishouActivityClient kuaishouActivityClient;

  @Autowired
  private MerchantGroupService merchantGroupService;

  @ApiOperation("获取商家关联的快手商户列表")
  @GetMapping({"/merchant/accounts"})
  public BaseResponse<List<MerchantKuaishouAccountDTO>> merchantKuaishouAccounts() {
    return kuaishouActivityClient.merchantKuaishouAccounts(getAdOrgIdNotNull());
  }

  @ApiOperation("获取商家快手门店列表")
  @PostMapping({"/merchant/pois"})
  public BaseResponse<List<MerchantKuaishouPoiInfoDTO>> merchantPoiList(
      @RequestBody KuaishouPoiQuery query) {
    query.setDistributorId(getAdOrgIdNotNull());
    return kuaishouActivityClient.merchantPoiList(query);
  }

  @ApiOperation("快手门店详情")
  @GetMapping({"/merchant/poi-detail"})
  public BaseResponse<MerchantKuaishouPoiDetailDTO> merchantPoiDetail(
      @RequestParam("poiId") String poiId) {
    BaseResponse<MerchantKuaishouPoiDetailDTO> response = kuaishouActivityClient.merchantPoiDetail(
        getAdOrgIdNotNull(), poiId);
    MerchantKuaishouPoiDetailDTO data = RemoteResponseUtils.getData(response);

    List<KuaishouPoiGroupItemDTO> groups = Optional.ofNullable(data)
        .map(MerchantKuaishouPoiDetailDTO::getGroupIds)
        .filter(CollUtil::isNotEmpty)
        .map(ids -> {
          MerchantGroupRequest request = new MerchantGroupRequest();
          request.setDistributor(getAdOrgIdNotNull());
          request.setGroups(ids);
          request.setIsActive(1);

          return Optional.ofNullable(
                  RemoteResponseUtils.getData(merchantGroupService.listGroup(request)))
              .filter(CollUtil::isNotEmpty)
              .map(list -> list.stream()
                  .map(o -> {
                    KuaishouPoiGroupItemDTO dto = new KuaishouPoiGroupItemDTO();
                    dto.setGroupId(o.getEquipmentGroupId());
                    dto.setGroupName(o.getName());

                    return dto;
                  }).collect(Collectors.toList()))
              .orElse(Collections.emptyList());
        }).orElse(Collections.emptyList());
    if (Objects.nonNull(data)) {
      data.setGroups(groups);
    }
    return response;
  }

  @ApiOperation("关联快手门店/集合号")
  @PostMapping({"/merchant/poi-associate"})
  public BaseResponse<Boolean> associatePoi(@RequestBody MerchantKuaishouPoiAssociateDTO dto) {
    AdUserInfoDTO currentUser = getCurrentUser();
    dto.setDistributorId(currentUser.getAdOrgId());
    dto.setMerchantName(currentUser.getPhone());
    dto.setOperatorId(currentUser.getAdUserId());
    return kuaishouActivityClient.associatePoi(dto);
  }

  /**
   * 配置商家套餐映射
   */
  @PostMapping("/merchant/configKuaishouCoupon")
  public JsonObject configTiktokCoupon(@RequestBody KuaishouActivityPoiRequestDTO request) {
    log.debug("configTiktokCoupon param:{}", JSONObject.toJSONString(request));
    try {
      Boolean result = ResponseUtils.checkResponse(
          kuaishouActivityClient.configKuaishouCoupon(request));
      return BaseResult.ok(result);
    } catch (ServiceException e) {
      return BaseResult.defFail(e.getMessage());
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return BaseResult.defFail("配置商家套餐映射失败");
    }
  }

}