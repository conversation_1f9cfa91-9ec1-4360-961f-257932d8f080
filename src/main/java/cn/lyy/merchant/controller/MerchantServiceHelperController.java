package cn.lyy.merchant.controller;

import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.merchant.api.service.MerchantServiceHelperClient;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.common.UserInfoDTO;
import cn.lyy.merchant.dto.request.WasherGainStatusDTO;
import cn.lyy.merchant.dto.request.WasherHelperClearRequestDTO;
import cn.lyy.merchant.dto.saashelper.EquipmentReportDTO;
import cn.lyy.merchant.dto.saashelper.MerchantServiceHelperClearRequestDTO;
import cn.lyy.merchant.dto.saashelper.MerchantServiceHelperRequestDTO;
import cn.lyy.merchant.dto.saashelper.MerchantServiceHelperResponseDTO;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import cn.lyy.merchant.microservice.WasherBusinessClient;
import cn.lyy.merchant.service.IEquipmentBusinessService;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.ArrayStack;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @ClassName:
 * @description:  商家服务助手表
 * @author: qgw
 * @create: 2020年12月9日
 * @Version 1.0
 **/
@RestController
@RequestMapping("/rest/service/helper")
@Slf4j
public class MerchantServiceHelperController extends BaseController {

    @Autowired
    private MerchantServiceHelperClient merchantServiceHelperClient;

    @Autowired
    private WasherBusinessClient washerBusinessClient;

    @Autowired
    private IEquipmentBusinessService equipmentBusinessService;



    /**
     * 分页查询商家服务助手
     * @param serviceHelperRequestDTO
     * @return
     */
    @PostMapping("/findOfPage")
    @AuthorityResource(name = "分页查询商家服务助手", value = "mb_helper_findOfPage", seq = 1, role = "Saas_Merchant", parentValue = "Common")
    public BaseResponse<PageInfo<MerchantServiceHelperResponseDTO<EquipmentReportDTO>>> findOfPage(@RequestBody MerchantServiceHelperRequestDTO serviceHelperRequestDTO){
        updateUserInfo(serviceHelperRequestDTO);
        return merchantServiceHelperClient.findOfPage(serviceHelperRequestDTO);
    }




    /**
     * 清除记录
     * @param serviceHelperRequestDTO
     * @return
     */
    @PostMapping("/removeRecord")
    @AuthorityResource(name = "清除记录", value = "mb_helper_removeRecord", seq = 2, role = "Saas_Merchant", parentValue = "Common")
    public BaseResponse<Integer> removeRecord(@RequestBody MerchantServiceHelperRequestDTO serviceHelperRequestDTO){
        if (equipmentBusinessService.getWasherOperationAsyncEnable()) {
            return removeRecordAsync(serviceHelperRequestDTO);
        }
        updateUserInfo(serviceHelperRequestDTO);
        serviceHelperRequestDTO.setPageIndex(1);
        serviceHelperRequestDTO.setPageSize(Integer.MAX_VALUE);
        BaseResponse<PageInfo<MerchantServiceHelperResponseDTO<EquipmentReportDTO>>> baseResponse =merchantServiceHelperClient.findOfPage(serviceHelperRequestDTO);
        if(ResponseCodeEnum.SUCCESS.getCode() == baseResponse.getCode() && baseResponse.getData() != null && baseResponse.getData().getList() != null){
            Set<String> equipmentValue = new HashSet<>(baseResponse.getData().getList().size() *2);
            //处理到主板清除故障
            List<String> uniqueSignList = baseResponse.getData().getList().stream()
                    .map(serviceHelper->{
                        EquipmentReportDTO report = serviceHelper.getData();
                        if(equipmentValue.contains(report.getValue())){
                            return serviceHelper.getUniqueSign();
                        }
                        if("XYJ".equals(report.getEquipmentType()) && report.getErrCode() != 0){
                            log.debug("开始清除 {} 设备的故障状态",report.getValue());
                            //洗衣机故障需要先下发下发清除故障指令
                            try {
                                BaseResponse<Boolean> response = washerBusinessClient.clearErrorStatus(report.getValue());
                                if(ResponseCodeEnum.FAIL.getCode() == response.getCode()
                                        || response.getData() == null || !response.getData()){
                                    //清除故障失败的
                                    log.debug("{}设备清除故障失败",report.getValue());
                                    return null;
                                }
                            }catch (Exception e){
                                log.error(e.getMessage(),e);
                                return null;
                            }
                        }
                        equipmentValue.add(report.getValue());
                        return serviceHelper.getUniqueSign();
                    })
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
            if(uniqueSignList.isEmpty()){
                log.debug("没有需要清除故障的设备，或无法同步主板故障情况-->{}",baseResponse.getData().getList());
                return ResponseUtils.error(ResponseCodeEnum.FAIL.getCode(),"清除失败");
            }
            MerchantServiceHelperClearRequestDTO clearRequestDTO = new MerchantServiceHelperClearRequestDTO();
            clearRequestDTO.setType(serviceHelperRequestDTO.getType());
            clearRequestDTO.setUniqueSignList(uniqueSignList);
            updateUserInfo(clearRequestDTO);
            log.info("清除设备故障，对应设备号：{}",equipmentValue);
            return merchantServiceHelperClient.removeRecordByUniqueSign(clearRequestDTO);
        }
        log.info("清除故障失败-->{}",serviceHelperRequestDTO);
        return ResponseUtils.error(ResponseCodeEnum.FAIL.getCode(),"清除失败");
    }




    /**
     * 清除记录 -异步
     * @param serviceHelperRequestDTO
     * @return
     */
    public BaseResponse<Integer> removeRecordAsync(MerchantServiceHelperRequestDTO serviceHelperRequestDTO) {
        updateUserInfo(serviceHelperRequestDTO);
        serviceHelperRequestDTO.setPageIndex(1);
        serviceHelperRequestDTO.setPageSize(Integer.MAX_VALUE);
        BaseResponse<PageInfo<MerchantServiceHelperResponseDTO<EquipmentReportDTO>>> baseResponse = merchantServiceHelperClient.findOfPage(serviceHelperRequestDTO);
        if (ResponseCodeEnum.SUCCESS.getCode() == baseResponse.getCode() && baseResponse.getData() != null && baseResponse.getData().getList() != null) {
            log.info("[3.0洗衣机同步改异步]-异步清除记录-getList:{}", baseResponse.getData().getList());
            Set<String> otherUniqueSignList = new HashSet<>(baseResponse.getData().getList().size());
            Map<String, Set<String>> uniqueSignMap = new HashMap<>();

            //处理到主板清除故障
            baseResponse.getData().getList().stream()
                    .forEach(serviceHelper -> {
                        EquipmentReportDTO report = serviceHelper.getData();
                        if ("XYJ".equals(report.getEquipmentType()) && report.getErrCode() != 0) {
                            Set<String> uniqueSignSet = uniqueSignMap.get(report.getValue());
                            if (CollectionUtils.isEmpty(uniqueSignSet)) {
                                uniqueSignSet = new HashSet<>();
                                uniqueSignSet.add(serviceHelper.getUniqueSign());
                                uniqueSignMap.put(report.getValue(), uniqueSignSet);
                                return;
                            }
                            uniqueSignSet.add(serviceHelper.getUniqueSign());
                        } else {
                            otherUniqueSignList.add(serviceHelper.getUniqueSign());
                        }
                    });
            if (CollectionUtils.isEmpty(otherUniqueSignList) && uniqueSignMap.isEmpty()) {
                log.info("[3.0洗衣机同步改异步]-异步清除记录-没有需要清除故障的设备，或无法同步主板故障情况-->{}", baseResponse.getData().getList());
                return ResponseUtils.error(ResponseCodeEnum.FAIL.getCode(), "清除失败");
            }
            if (!CollectionUtils.isEmpty(otherUniqueSignList)) {
                MerchantServiceHelperClearRequestDTO clearRequestDTO = new MerchantServiceHelperClearRequestDTO();
                clearRequestDTO.setType(serviceHelperRequestDTO.getType());
                clearRequestDTO.setUniqueSignList(new ArrayList<>(otherUniqueSignList));
                updateUserInfo(clearRequestDTO);
                log.info("[3.0洗衣机同步改异步]-异步清除记录-非洗衣机设备:{}", otherUniqueSignList);
                merchantServiceHelperClient.removeRecordByUniqueSign(clearRequestDTO);
            }

            if (!uniqueSignMap.isEmpty()) {
                List<WasherHelperClearRequestDTO> washerHelperClearRequestDTOList = new ArrayList<>();
                for (Map.Entry<String, Set<String>> entry : uniqueSignMap.entrySet()) {
                    WasherHelperClearRequestDTO clearRequestDTO = new WasherHelperClearRequestDTO();
                    clearRequestDTO.setType(serviceHelperRequestDTO.getType());
                    clearRequestDTO.setEquipmentValue(entry.getKey());
                    clearRequestDTO.setUniqueSignList(new ArrayList<>(entry.getValue()));
                    updateUserInfo(clearRequestDTO);
                    washerHelperClearRequestDTOList.add(clearRequestDTO);
                }
                WasherGainStatusDTO gainStatusDTO = new WasherGainStatusDTO();
                gainStatusDTO.setWasherHelperClearRequestDTOList(washerHelperClearRequestDTOList);
                log.info("[3.0洗衣机同步改异步]-异步清除记录-洗衣机设备:{}", gainStatusDTO);
                washerBusinessClient.clearErrorStatusAsync(gainStatusDTO);
            }
        }
        return ResponseUtils.error(ResponseCodeEnum.SUCCESS.getCode(), "清除操作成功");
    }

    /**
     * 填充用户信息
     * @param userInfoDTO
     */
    private void updateUserInfo(UserInfoDTO userInfoDTO) {
        AdUserInfoDTO adUserInfoDTO = getCurrentUser();
        userInfoDTO.setIsApprover(adUserInfoDTO.getIsApprover());
        userInfoDTO.setUserOrgId(adUserInfoDTO.getAdOrgId());
        userInfoDTO.setUserId(adUserInfoDTO.getAdUserId());
        userInfoDTO.setFactoryOrgId(adUserInfoDTO.getBelongTo());
    }

}
