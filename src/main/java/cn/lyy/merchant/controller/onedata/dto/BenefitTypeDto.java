package cn.lyy.merchant.controller.onedata.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class BenefitTypeDto {

    private List<BenefitType> benefitTypeList;
    private Map<String,String> benefitTransMap;

    public BenefitTypeDto(){
        benefitTypeList = new ArrayList<>();
        BenefitType yb = new BenefitType("余币","coin");
        BenefitType ye = new BenefitType("余额","amount");
        BenefitType ck = new BenefitType("次卡","timeCard");
        BenefitType yk = new BenefitType("月卡","monthCard");
        BenefitType sck = new BenefitType("时长卡","timeRangeCard");
        BenefitType zkk = new BenefitType("折扣卡","discountCard");
        benefitTypeList.add(yb);
        benefitTypeList.add(ye);
        benefitTypeList.add(ck);
        benefitTypeList.add(yk);
        benefitTypeList.add(sck);
        benefitTypeList.add(zkk);

        benefitTransMap = new HashMap<>();
        benefitTransMap.put("coin","余币");
        benefitTransMap.put("amount","余额");
        benefitTransMap.put("timeCard","次卡");
        benefitTransMap.put("monthCard","月卡");
        benefitTransMap.put("timeRangeCard","时长卡");
        benefitTransMap.put("discountCard","折扣卡");
    }

    @Data
    class BenefitType{
        private String name;
        private String type;

        public BenefitType(){}

        public BenefitType(String name, String type){
            this.name = name;
            this.type = type;
        }
    }
}
