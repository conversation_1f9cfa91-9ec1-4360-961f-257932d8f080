package cn.lyy.merchant.controller.onedata.dto;

import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * 类描述：日报请求参数DTO
 * <p>
 *
 * <AUTHOR>
 * @since 2023/5/19 17:23
 */
@Data
public class WeekParamDto {
    private Integer provinceId;
    private Integer cityId;
    private Integer areaId;
    private Integer groupId;
    private Integer equipmentTypeId;
    private Integer merchantId;
    private String comboType;
    private Integer week;
    private String month;
    private String benefitType;
    private Set<String> groupIds;
    private Integer pageNum;
    private String orderBy;
    private String order;
    private String groupName;       //支持场地名称模糊搜索


    public String getComboType(){
        String type = "商家";
        if(this.getProvinceId() != null && this.getProvinceId() > 0){
            type += "+省份";
        }

        if(this.getCityId() != null && this.getCityId() > 0){
            type += "+城市";
        }

        if(this.getAreaId() != null && this.getAreaId() > 0){
            type += "+区域";
        }

        if(this.getGroupId() != null && this.getGroupId() > 0){
            if(type.startsWith("商家+省份+城市+区域")){
                type += "+场地";
            }else{
                type = "商家+省份+城市+区域+场地";
            }
        }

        if(this.getEquipmentTypeId() != null && this.getEquipmentTypeId() > 0){
            type += "+设备类型";
        }
        return type+"维度";
    }

    public String getBenefitType(){
        if(this.benefitType == null || this.benefitType.trim().isEmpty()){
            this.benefitType = "余额";
        }
        return this.benefitType;
    }
}
