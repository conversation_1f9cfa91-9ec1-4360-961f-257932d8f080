package cn.lyy.merchant.controller.onedata;

import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.merchant.api.service.MerchantWhiteClient;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.controller.onedata.dto.DayParamDto;
import cn.lyy.merchant.controller.onedata.dto.MonthParamDto;
import cn.lyy.merchant.controller.onedata.dto.WeekParamDto;
import cn.lyy.merchant.controller.onedata.dto.YearParamDto;
import cn.lyy.merchant.controller.onedata.util.TimeUtil;
import cn.lyy.merchant.dto.merchant.response.MerchantGroupDTO;
import cn.lyy.merchant.dto.response.MerchantGroupListDTO;
import cn.lyy.merchant.service.IEquipmentGroupBusinessService;
import cn.lyy.merchant.service.onedata.mobile.EquipmentGroupDataService;
import cn.lyy.merchant.service.onedata.mobile.MemberDataService;
import cn.lyy.merchant.service.onedata.mobile.OrderDataService;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
@RestController
@RequestMapping("/oneData")
public class ComputerReportController extends BaseController {

    @Value("${oneData.testModel}")
    private Boolean testModel;

    @Autowired
    private IEquipmentGroupBusinessService equipmentGroupBusinessService;

    @Autowired
    private MerchantWhiteClient merchantWhiteClient;
    
    @Autowired
    private MemberDataService memberDataService ;
    
    @Autowired
    private OrderDataService orderDataService ;
    
    @Autowired
    private EquipmentGroupDataService equipmentGroupDataService ;

    @Value("#{${oneData.merchantTrans}}")
    private Map<Long,Long> merchantTransMap;

    @Value("${oneData.stableWhiteListCode:1905}")
    private int stableWhiteListCode ;

    private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
    private SimpleDateFormat msdf = new SimpleDateFormat("yyyy-MM");

    @GetMapping("/pc/dayReport/group/sum")
    @AuthorityResource(name = "PC端-基础数据", value = "mb_pc_report_day_group_sum", seq = 1, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getDayGroupSum(DayParamDto dayParamDto) {

        BaseResponse result = null;
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(dayParamDto.getDay() == null || dayParamDto.getDay().trim().isEmpty()){
            return error(500,"请求日期不能为空");
        }

        try{
            Date day = sdf.parse(dayParamDto.getDay());
        }catch (Exception e){
            return error(500,"日期格式不正确，正确的日期格式为：yyyy-MM-dd");
        }
        getGroupId(dayParamDto);

        dayParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));
        JSONObject orderResult = orderDataService.getOrderData(dayParamDto);
        JSONObject groupEquipmentResult = equipmentGroupDataService.getGroupEquipmentData(dayParamDto);
        JSONObject memberResult = memberDataService.getMemberData(dayParamDto);

        JSONObject data = null;

        if(orderResult !=null && orderResult.containsKey("code") && "200".equals(orderResult.getString("code"))){
            result = success(orderResult.get("info"));
            data = orderResult.getJSONObject("info").getJSONArray("list") != null && orderResult.getJSONObject("info").getJSONArray("list").size()>0 ? orderResult.getJSONObject("info").getJSONArray("list").getJSONObject(0) : new JSONObject();
        }else{
            return error(500,result == null ? "请求出错":orderResult.getString("msg"));
        }

        if(groupEquipmentResult !=null && groupEquipmentResult.containsKey("code") && "200".equals(groupEquipmentResult.getString("code"))){
            if(groupEquipmentResult.getJSONObject("info").getJSONArray("list") != null && groupEquipmentResult.getJSONObject("info").getJSONArray("list").size()>0){
                data.putAll(groupEquipmentResult.getJSONObject("info").getJSONArray("list").getJSONObject(0));
            }
        }

        if(memberResult !=null && memberResult.containsKey("code") && "200".equals(memberResult.getString("code"))){
            if(memberResult.getJSONObject("info").getJSONArray("list") != null && memberResult.getJSONObject("info").getJSONArray("list").size()>0){
                data.putAll(memberResult.getJSONObject("info").getJSONArray("list").getJSONObject(0));
            }
        }

        //处理笔单价
        if(data.getDouble("onlinePayCounts") == null || data.getDoubleValue("onlinePayCounts") == 0 ||  data.getDouble("onlinePayAmount") == null || data.getDoubleValue("onlinePayAmount") == 0){
            data.put("perOrderPrice",0);
        }else{
            DecimalFormat decimalFormat = new DecimalFormat("0.00");
            data.put("perOrderPrice",decimalFormat.format(data.getDoubleValue("onlinePayAmount")/data.getDoubleValue("onlinePayCounts")));
        }
        //处理客单价
        if(data.getInteger("memberCounts") == null || data.getIntValue("memberCounts") == 0 ||  data.getDouble("onlinePayAmount") == null || data.getDoubleValue("onlinePayAmount") == 0){
            data.put("perMemberOrderAmount",0);
        }else{
            DecimalFormat decimalFormat = new DecimalFormat("0.00");
            data.put("perMemberOrderAmount",decimalFormat.format(data.getDoubleValue("onlinePayAmount")/data.getIntValue("memberCounts")));
        }

        //处理会员均数
        if(data.getInteger("memberCounts") == null || data.getIntValue("memberCounts") == 0 ||  data.getDouble("groupCounts") == null || data.getDoubleValue("groupCounts") == 0){
            data.put("perGroupMember",0);
        }else{
            DecimalFormat decimalFormat = new DecimalFormat("0.00");
            data.put("perGroupMember",decimalFormat.format(data.getDoubleValue("memberCounts")/data.getIntValue("groupCounts")));
        }

        return result;
    }

    @GetMapping("/pc/weekReport/group/sum")
    @AuthorityResource(name = "PC端-基础数据", value = "mb_pc_report_week_group_sum", seq = 1, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getWeekGroupSum(WeekParamDto weekParamDto) {

        BaseResponse result = null;
        log.info("订单分析-日报:{}",JSONObject.toJSON(merchantTransMap).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(weekParamDto.getMonth() == null || weekParamDto.getMonth().trim().isEmpty()){
            JSONObject monthWeek = TimeUtil.getDateMonthWeek(new Date());
            weekParamDto.setMonth(monthWeek.getString("month"));
            weekParamDto.setWeek(monthWeek.getInteger("week"));
        }

        try{
            Date day = msdf.parse(weekParamDto.getMonth());
        }catch (Exception e){
            return error(500,"月份格式不正确，正确的日期格式为：yyyy-MM");
        }

        getGroupId(weekParamDto);

        weekParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        JSONObject orderResult = orderDataService.getWeekOrderData(weekParamDto);
        JSONObject groupEquipmentResult = equipmentGroupDataService.getWeekGroupEquipmentData(weekParamDto);
        JSONObject memberResult = memberDataService.getWeekMemberData(weekParamDto);
        
        JSONObject data = null;

        if(orderResult !=null && orderResult.containsKey("code") && "200".equals(orderResult.getString("code"))){
            result = success(orderResult.get("info"));
            data = orderResult.getJSONObject("info").getJSONArray("list") != null && orderResult.getJSONObject("info").getJSONArray("list").size()>0 ? orderResult.getJSONObject("info").getJSONArray("list").getJSONObject(0) : new JSONObject();
        }else{
            return error(500,result == null ? "请求出错":orderResult.getString("msg"));
        }

        if(groupEquipmentResult !=null && groupEquipmentResult.containsKey("code") && "200".equals(groupEquipmentResult.getString("code"))){
            if(groupEquipmentResult.getJSONObject("info").getJSONArray("list") != null && groupEquipmentResult.getJSONObject("info").getJSONArray("list").size()>0){
                data.putAll(groupEquipmentResult.getJSONObject("info").getJSONArray("list").getJSONObject(0));
            }
        }

        if(memberResult !=null && memberResult.containsKey("code") && "200".equals(memberResult.getString("code"))){
            if(memberResult.getJSONObject("info").getJSONArray("list") != null && memberResult.getJSONObject("info").getJSONArray("list").size()>0){
                data.putAll(memberResult.getJSONObject("info").getJSONArray("list").getJSONObject(0));
            }
        }

        //处理笔单价
        if(data.getDouble("onlinePayCounts") == null || data.getDoubleValue("onlinePayCounts") == 0 ||  data.getDouble("onlinePayAmount") == null || data.getDoubleValue("onlinePayAmount") == 0){
            data.put("perOrderPrice",0);
        }else{
            DecimalFormat decimalFormat = new DecimalFormat("0.00");
            data.put("perOrderPrice",decimalFormat.format(data.getDoubleValue("onlinePayAmount")/data.getDoubleValue("onlinePayCounts")));
        }
        //处理客单价
        if(data.getInteger("memberCounts") == null || data.getIntValue("memberCounts") == 0 ||  data.getDouble("onlinePayAmount") == null || data.getDoubleValue("onlinePayAmount") == 0){
            data.put("perMemberOrderAmount",0);
        }else{
            DecimalFormat decimalFormat = new DecimalFormat("0.00");
            data.put("perMemberOrderAmount",decimalFormat.format(data.getDoubleValue("onlinePayAmount")/data.getIntValue("memberCounts")));
        }

        //处理会员均数
        if(data.getInteger("memberCounts") == null || data.getIntValue("memberCounts") == 0 ||  data.getDouble("groupCounts") == null || data.getDoubleValue("groupCounts") == 0){
            data.put("perGroupMember",0);
        }else{
            DecimalFormat decimalFormat = new DecimalFormat("0.00");
            data.put("perGroupMember",decimalFormat.format(data.getDoubleValue("memberCounts")/data.getIntValue("groupCounts")));
        }

        return result;
    }

    @GetMapping("/pc/monthReport/group/sum")
    @AuthorityResource(name = "PC端-基础数据", value = "mb_pc_report_month_group_sum", seq = 1, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getMonthGroupSum(MonthParamDto monthParamDto) {

        BaseResponse result = null;
        log.info("订单分析-日报:{}",JSONObject.toJSON(merchantTransMap).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(monthParamDto.getMonth() == null || monthParamDto.getMonth().trim().isEmpty()){
            JSONObject monthWeek = TimeUtil.getDateMonthWeek(new Date());
            monthParamDto.setMonth(monthWeek.getString("month"));
        }

        try{
            Date day = msdf.parse(monthParamDto.getMonth());
        }catch (Exception e){
            return error(500,"月份格式不正确，正确的日期格式为：yyyy-MM");
        }

        getGroupId(monthParamDto);
        monthParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));
        JSONObject orderResult = orderDataService.getMonthOrderData(monthParamDto);
        JSONObject groupEquipmentResult = equipmentGroupDataService.getMonthGroupEquipmentData(monthParamDto);
        JSONObject memberResult = memberDataService.getMonthMemberData(monthParamDto);

        JSONObject data = null;

        if(orderResult !=null && orderResult.containsKey("code") && "200".equals(orderResult.getString("code"))){
            result = success(orderResult.get("info"));
            data = orderResult.getJSONObject("info").getJSONArray("list") != null && orderResult.getJSONObject("info").getJSONArray("list").size()>0 ? orderResult.getJSONObject("info").getJSONArray("list").getJSONObject(0) : new JSONObject();
        }else{
            return error(500,result == null ? "请求出错":orderResult.getString("msg"));
        }

        if(groupEquipmentResult !=null && groupEquipmentResult.containsKey("code") && "200".equals(groupEquipmentResult.getString("code"))){
            if(groupEquipmentResult.getJSONObject("info").getJSONArray("list") != null && groupEquipmentResult.getJSONObject("info").getJSONArray("list").size()>0){
                data.putAll(groupEquipmentResult.getJSONObject("info").getJSONArray("list").getJSONObject(0));
            }
        }

        if(memberResult !=null && memberResult.containsKey("code") && "200".equals(memberResult.getString("code"))){
            if(memberResult.getJSONObject("info").getJSONArray("list") != null && memberResult.getJSONObject("info").getJSONArray("list").size()>0){
                data.putAll(memberResult.getJSONObject("info").getJSONArray("list").getJSONObject(0));
            }
        }

        //处理笔单价
        if(data.getDouble("onlinePayCounts") == null || data.getDoubleValue("onlinePayCounts") == 0 ||  data.getDouble("onlinePayAmount") == null || data.getDoubleValue("onlinePayAmount") == 0){
            data.put("perOrderPrice",0);
        }else{
            DecimalFormat decimalFormat = new DecimalFormat("0.00");
            data.put("perOrderPrice",decimalFormat.format(data.getDoubleValue("onlinePayAmount")/data.getDoubleValue("onlinePayCounts")));
        }
        //处理客单价
        if(data.getInteger("memberCounts") == null || data.getIntValue("memberCounts") == 0 ||  data.getDouble("onlinePayAmount") == null || data.getDoubleValue("onlinePayAmount") == 0){
            data.put("perMemberOrderAmount",0);
        }else{
            DecimalFormat decimalFormat = new DecimalFormat("0.00");
            data.put("perMemberOrderAmount",decimalFormat.format(data.getDoubleValue("onlinePayAmount")/data.getIntValue("memberCounts")));
        }

        //处理会员均数
        if(data.getInteger("memberCounts") == null || data.getIntValue("memberCounts") == 0 ||  data.getDouble("groupCounts") == null || data.getDoubleValue("groupCounts") == 0){
            data.put("perGroupMember",0);
        }else{
            DecimalFormat decimalFormat = new DecimalFormat("0.00");
            data.put("perGroupMember",decimalFormat.format(data.getDoubleValue("memberCounts")/data.getIntValue("groupCounts")));
        }

        return result;
    }


    @GetMapping("/pc/yearReport/group/sum")
    @AuthorityResource(name = "PC端-基础数据", value = "mb_pc_report_year_group_sum", seq = 1, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getYearGroupSum(YearParamDto yearParamDto) {

        BaseResponse result = null;
        log.info("订单分析-日报:{}",JSONObject.toJSON(merchantTransMap).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(yearParamDto.getYear() == null || yearParamDto.getYear() < 1000){
            return error(500,"年份不能为空或者不正确");
        }

        getGroupId(yearParamDto);

        yearParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        JSONObject orderResult = orderDataService.getYearOrderData(yearParamDto);
        JSONObject groupEquipmentResult = equipmentGroupDataService.getYearGroupEquipmentData(yearParamDto);
        JSONObject memberResult = memberDataService.getYearMemberData(yearParamDto);

        JSONObject data = null;
        if(orderResult !=null && orderResult.containsKey("code") && "200".equals(orderResult.getString("code"))){
            result = success(orderResult.get("info"));
            data = orderResult.getJSONObject("info").getJSONArray("list") != null && orderResult.getJSONObject("info").getJSONArray("list").size()>0 ? orderResult.getJSONObject("info").getJSONArray("list").getJSONObject(0) : new JSONObject();
        }else{
            return error(500,result == null ? "请求出错":orderResult.getString("msg"));
        }

        if(groupEquipmentResult !=null && groupEquipmentResult.containsKey("code") && "200".equals(groupEquipmentResult.getString("code"))){
            if(groupEquipmentResult.getJSONObject("info").getJSONArray("list") != null && groupEquipmentResult.getJSONObject("info").getJSONArray("list").size()>0){
                data.putAll(groupEquipmentResult.getJSONObject("info").getJSONArray("list").getJSONObject(0));
            }
        }

        if(memberResult !=null && memberResult.containsKey("code") && "200".equals(memberResult.getString("code"))){
            if(memberResult.getJSONObject("info").getJSONArray("list") != null && memberResult.getJSONObject("info").getJSONArray("list").size()>0){
                data.putAll(memberResult.getJSONObject("info").getJSONArray("list").getJSONObject(0));
            }
        }

        //处理笔单价
        if(data.getDouble("onlinePayCounts") == null || data.getDoubleValue("onlinePayCounts") == 0 ||  data.getDouble("onlinePayAmount") == null || data.getDoubleValue("onlinePayAmount") == 0){
            data.put("perOrderPrice",0);
        }else{
            DecimalFormat decimalFormat = new DecimalFormat("0.00");
            data.put("perOrderPrice",decimalFormat.format(data.getDoubleValue("onlinePayAmount")/data.getDoubleValue("onlinePayCounts")));
        }
        //处理客单价
        if(data.getInteger("memberCounts") == null || data.getIntValue("memberCounts") == 0 ||  data.getDouble("onlinePayAmount") == null || data.getDoubleValue("onlinePayAmount") == 0){
            data.put("perMemberOrderAmount",0);
        }else{
            DecimalFormat decimalFormat = new DecimalFormat("0.00");
            data.put("perMemberOrderAmount",decimalFormat.format(data.getDoubleValue("onlinePayAmount")/data.getIntValue("memberCounts")));
        }

        //处理会员均数
        if(data.getInteger("memberCounts") == null || data.getIntValue("memberCounts") == 0 ||  data.getDouble("groupCounts") == null || data.getDoubleValue("groupCounts") == 0){
            data.put("perGroupMember",0);
        }else{
            DecimalFormat decimalFormat = new DecimalFormat("0.00");
            data.put("perGroupMember",decimalFormat.format(data.getDoubleValue("memberCounts")/data.getIntValue("groupCounts")));
        }

        return result;
    }

    private void getGroupId(Object param){

        //统一处理有选择场地，则维度为场地维度
        if(param instanceof DayParamDto){
            if(((DayParamDto) param).getGroupId() != null && ((DayParamDto) param).getGroupId() > 0){
                ((DayParamDto) param).setComboType("商家+省份+城市+区域+场地维度");
            }
        }

        if(param instanceof WeekParamDto){
            if(((WeekParamDto) param).getGroupId() != null &&  ((WeekParamDto) param).getGroupId() > 0){
                ((WeekParamDto) param).setComboType("商家+省份+城市+区域+场地维度");
            }
        }

        if(param instanceof MonthParamDto){
            if(((MonthParamDto) param).getGroupId() != null &&  ((MonthParamDto) param).getGroupId() > 0){
                ((MonthParamDto) param).setComboType("商家+省份+城市+区域+场地维度");
            }
        }

        if(param instanceof YearParamDto){
            if(((YearParamDto) param).getGroupId() != null &&  ((YearParamDto) param).getGroupId() > 0){
                ((YearParamDto) param).setComboType("商家+省份+城市+区域+场地维度");
            }
        }

        if(getCurrentUser().getIsApprover()){
            return;
        }
        List<MerchantGroupListDTO> list = equipmentGroupBusinessService.getGroupInfoDistributorId(getAdOrgIdNotNull(), getAdUserIdNotNull(),
                false,"", null);
        if(list != null ){
            Set<String> groupIds = new TreeSet<>();
            for(MerchantGroupListDTO merchantGroupListDTO : list){
                if(merchantGroupListDTO != null && merchantGroupListDTO.getGroups() !=null && merchantGroupListDTO.getGroups().size()>0){
                    for(MerchantGroupDTO merchantGroupDTO: merchantGroupListDTO.getGroups()){
                        if(merchantGroupDTO != null && "Y".equals(merchantGroupDTO.getIsactive())){
                            groupIds.add(merchantGroupDTO.getEquipmentGroupId()+"");
                        }
                    }
                }
            }
            if(groupIds.size()>0){
                if(param instanceof DayParamDto){
                    ((DayParamDto) param).setGroupIds(groupIds);
                }

                if(param instanceof WeekParamDto){
                    ((WeekParamDto) param).setGroupIds(groupIds);
                }

                if(param instanceof MonthParamDto){
                    ((MonthParamDto) param).setGroupIds(groupIds);
                }

                if(param instanceof YearParamDto){
                    ((YearParamDto) param).setGroupIds(groupIds);
                }
            }
        }

    }

    private boolean isWith(int targetWith){
        BaseResponse<List<Integer>> witheList = merchantWhiteClient.getAllWhiteByDistributorId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull():merchantTransMap.get(getAdOrgIdNotNull())));
        if(witheList.getCode() == 0){
            //log.info("商家：{}，白名单：{}", merchant.getId(),witheList.getData());
            List<Integer> withes = witheList.getData();
            if(withes != null && withes.size()>0){
                for(Integer with : withes){
                    if(with.intValue() == targetWith){
                        return true;
                    }
                }
            }
        }
        return false;
    }

}
