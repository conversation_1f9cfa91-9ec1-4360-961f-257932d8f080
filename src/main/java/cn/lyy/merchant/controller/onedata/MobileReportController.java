package cn.lyy.merchant.controller.onedata;

import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.merchant.api.service.MerchantWhiteClient;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.controller.onedata.dto.*;
import cn.lyy.merchant.controller.onedata.util.TimeUtil;
import cn.lyy.merchant.dto.merchant.response.MerchantGroupDTO;
import cn.lyy.merchant.dto.response.MerchantGroupListDTO;
import cn.lyy.merchant.service.IEquipmentGroupBusinessService;
import cn.lyy.merchant.service.onedata.OneDataService;
import cn.lyy.merchant.service.onedata.mobile.CityDataService;
import cn.lyy.merchant.service.onedata.mobile.EquipmentDataService;
import cn.lyy.merchant.service.onedata.mobile.EquipmentGroupDataService;
import cn.lyy.merchant.service.onedata.mobile.EquipmentTypeDataService;
import cn.lyy.merchant.service.onedata.mobile.MemberBenefitDataService;
import cn.lyy.merchant.service.onedata.mobile.MemberDataService;
import cn.lyy.merchant.service.onedata.mobile.OrderDataService;
import cn.lyy.merchant.service.onedata.mobile.PayTypeDataService;
import cn.lyy.merchant.service.onedata.mobile.ProvinceDataService;
import cn.lyy.merchant.service.onedata.mobile.TimeRangeDataService;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 类描述：移动B端新报表数据接口服务
 * <p>
 *
 * <AUTHOR>
 * @since 2023/5/19 17:23
 */
@Slf4j
@RestController
@RequestMapping("/oneData")
public class MobileReportController extends BaseController {
    @Autowired
    private OneDataService oneDataService;
    
    @Autowired
    private OrderDataService orderDataService ;
    
    @Autowired
    private PayTypeDataService payTypeDataService;
    
    @Autowired
    private MemberDataService memberDataService ;
    
    @Autowired
    private ProvinceDataService provinceDataService;
    
    @Autowired
    private CityDataService cityDataService ;
    
    @Autowired
    private EquipmentGroupDataService equipmentGroupService ;
    
    @Autowired
    private EquipmentDataService equipmentDataService ;
    
    @Autowired
    private MemberBenefitDataService memberBenefitDataService ;
    
    @Autowired
    private TimeRangeDataService timeRangeDataService ;
    
    @Autowired
    private EquipmentTypeDataService equipmentTypeDataService ;

    @Autowired
    private IEquipmentGroupBusinessService equipmentGroupBusinessService;

    @Autowired
    private MerchantWhiteClient merchantWhiteClient;

//    private int isReporter = 1905;      		//移动端报表一期白名单
//    private int isSecReporter = 1906;    		//移动端报表二期白名单
//    private int isEverydayReporter = 1908;  	//移动端报表每日账单黑名单
//    private int isThirdReportor = 1909;    	//移动端报表三期白名单
    
    @Value("${oneData.stableWhiteListCode:1905}")
    private int stableWhiteListCode ;
    
    @Value("${oneData.everyDayReportWhileListCode:1908}")
    private int everyDayReportWhileListCode = 1908;  //移动端报表每日账单黑名单
    
//    @Value("${oneData.instableWhiteListCode:1922}")
//    private int instableWhiteListCode ;

    @Value("${oneData.testModel}")
    private Boolean testModel;
    @Value("#{${oneData.merchantTrans}}")
    private Map<Long,Long> merchantTransMap;

    private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
    private SimpleDateFormat msdf = new SimpleDateFormat("yyyy-MM");

    @GetMapping("/mobile/dayReport/order")
    @AuthorityResource(name = "订单分析-日报", value = "mb_mobile_report_day_order", seq = 1, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getReportData(DayParamDto dayParamDto) {
        log.info("订单分析-日报:{}",JSONObject.toJSON(merchantTransMap).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(dayParamDto.getDay() == null || dayParamDto.getDay().trim().isEmpty()){
            return error(500,"请求日期不能为空");
        }

        /*if(dayParamDto.getMerchantId() == null || dayParamDto.getMerchantId() <=0){
            return error(500,"商户id不能为空");
        }*/
        try{
            Date day = sdf.parse(dayParamDto.getDay());
        }catch (Exception e){
            return error(500,"日期格式不正确，正确的日期格式为：yyyy-MM-dd");
        }
        getGroupId(dayParamDto);

        dayParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        JSONObject result = orderDataService.getOrderData(dayParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/weekReport/order")
    @AuthorityResource(name = "订单分析-周报", value = "mb_mobile_report_week_order", seq = 19, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getWeekReportData(WeekParamDto weekParamDto) {
        log.info("day_report_param:{}",JSONObject.toJSON(weekParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }

        if(weekParamDto.getMonth() == null || weekParamDto.getMonth().trim().isEmpty()){
            JSONObject monthWeek = TimeUtil.getDateMonthWeek(new Date());
            weekParamDto.setMonth(monthWeek.getString("month"));
            weekParamDto.setWeek(monthWeek.getInteger("week"));
        }

        try{
            Date day = msdf.parse(weekParamDto.getMonth());
        }catch (Exception e){
            return error(500,"月份格式不正确，正确的日期格式为：yyyy-MM");
        }
        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(weekParamDto);
        weekParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        JSONObject result = orderDataService.getWeekOrderData(weekParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/monthReport/order")
    @AuthorityResource(name = "订单分析-月报", value = "mb_mobile_report_month_order", seq = 19, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getMonthReportData(MonthParamDto monthParamDto) {
        log.info("day_report_param:{}",JSONObject.toJSON(monthParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }

        if(monthParamDto.getMonth() == null || monthParamDto.getMonth().trim().isEmpty()){
            JSONObject monthWeek = TimeUtil.getDateMonthWeek(new Date());
            monthParamDto.setMonth(monthWeek.getString("month"));
        }

        try{
            Date day = msdf.parse(monthParamDto.getMonth());
        }catch (Exception e){
            return error(500,"月份格式不正确，正确的日期格式为：yyyy-MM");
        }
        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(monthParamDto);
        monthParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        JSONObject result = orderDataService.getMonthOrderData(monthParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/yearReport/order")
    @AuthorityResource(name = "订单分析-年报", value = "mb_mobile_report_year_order", seq = 19, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getYearReportData(YearParamDto yearParamDto) {
        log.info("订单分析-年报:{}",JSONObject.toJSON(yearParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }

        if(yearParamDto.getYear() == null || yearParamDto.getYear() < 1000){
            return error(500,"年份不能为空或者不正确");
        }


        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(yearParamDto);
        yearParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        JSONObject result = orderDataService.getYearOrderData(yearParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            filterYearUp(result.getJSONObject("info"));
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/dayReport/memberBenefitAnalyse")
    @AuthorityResource(name = "会员储值分析-日报", value = "mb_mobile_report_day_memberBenefitAnayse", seq = 24, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getMemberBenefitAnayse(DayParamDto dayParamDto) {
        log.info("会员储值分析-日报:{}",JSONObject.toJSON(dayParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(dayParamDto.getDay() == null || dayParamDto.getDay().trim().isEmpty()){
            return error(500,"请求日期不能为空");
        }

        if(dayParamDto.getBenefitType() == null || dayParamDto.getBenefitType().trim().isEmpty()){
            dayParamDto.setBenefitType(new BenefitTypeDto().getBenefitTransMap().get("amount"));
        }else{
            dayParamDto.setBenefitType(new BenefitTypeDto().getBenefitTransMap().get(dayParamDto.getBenefitType()));
        }

        /*if(dayParamDto.getMerchantId() == null || dayParamDto.getMerchantId() <=0){
            return error(500,"商户id不能为空");
        }*/
        try{
            Date day = sdf.parse(dayParamDto.getDay());
        }catch (Exception e){
            return error(500,"日期格式不正确，正确的日期格式为：yyyy-MM-dd");
        }

        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(dayParamDto);
        dayParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));
        JSONObject result = memberBenefitDataService.getMemberBenefitData(dayParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/weekReport/memberBenefitAnalyse")
    @AuthorityResource(name = "会员储值分析-周报", value = "mb_mobile_report_week_memberBenefitAnayse", seq = 25, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getWeekMemberBenefitAnayse(WeekParamDto weekParamDto) {
        log.info("会员储值分析-周报:{}",JSONObject.toJSON(weekParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(weekParamDto.getMonth() == null || weekParamDto.getMonth().trim().isEmpty()){
            JSONObject monthWeek = TimeUtil.getDateMonthWeek(new Date());
            weekParamDto.setMonth(monthWeek.getString("month"));
            weekParamDto.setWeek(monthWeek.getInteger("week"));
        }

        if(weekParamDto.getWeek() == null || weekParamDto.getWeek() < 1){
            return error(500,"周数不能为空");
        }

        if(weekParamDto.getBenefitType() == null || weekParamDto.getBenefitType().trim().isEmpty()){
            weekParamDto.setBenefitType(new BenefitTypeDto().getBenefitTransMap().get("amount"));
        }else{
            weekParamDto.setBenefitType(new BenefitTypeDto().getBenefitTransMap().get(weekParamDto.getBenefitType()));
        }

        try{
            Date day = msdf.parse(weekParamDto.getMonth());
        }catch (Exception e){
            return error(500,"月份格式不正确，正确的日期格式为：yyyy-MM");
        }

        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(weekParamDto);
        weekParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));
        JSONObject result = memberBenefitDataService.getWeekMemberBenefitData(weekParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/monthReport/memberBenefitAnalyse")
    @AuthorityResource(name = "会员储值分析-月报", value = "mb_mobile_report_month_memberBenefitAnayse", seq = 25, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getMonthMemberBenefitAnayse(MonthParamDto monthParamDto) {
        log.info("会员储值分析-月报:{}",JSONObject.toJSON(monthParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(monthParamDto.getMonth() == null || monthParamDto.getMonth().trim().isEmpty()){
            return error(500,"月份不能为空");
        }

        if(monthParamDto.getBenefitType() == null || monthParamDto.getBenefitType().trim().isEmpty()){
            monthParamDto.setBenefitType(new BenefitTypeDto().getBenefitTransMap().get("amount"));
        }else{
            monthParamDto.setBenefitType(new BenefitTypeDto().getBenefitTransMap().get(monthParamDto.getBenefitType()));
        }

        try{
            Date day = msdf.parse(monthParamDto.getMonth());
        }catch (Exception e){
            return error(500,"月份格式不正确，正确的日期格式为：yyyy-MM");
        }

        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(monthParamDto);
        monthParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));
        JSONObject result = memberBenefitDataService.getMonthMemberBenefitData(monthParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }
    
    @GetMapping("/mobile/yearReport/memberBenefitAnalyse")
    @AuthorityResource(name = "会员储值分析-年报", value = "mb_mobile_report_year_memberBenefitAnayse", seq = 25, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getYearMemberBenefitAnayse(YearParamDto yearParamDto) {
        log.info("会员储值分析-年报:{}",JSONObject.toJSON(yearParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(yearParamDto.getYear() == null || yearParamDto.getYear() < 1000){
            return error(500,"年份不能为空或者不正确");
        }

        if(yearParamDto.getBenefitType() == null || yearParamDto.getBenefitType().trim().isEmpty()){
            yearParamDto.setBenefitType(new BenefitTypeDto().getBenefitTransMap().get("amount"));
        }else{
            yearParamDto.setBenefitType(new BenefitTypeDto().getBenefitTransMap().get(yearParamDto.getBenefitType()));
        }
        
        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(yearParamDto);
        yearParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));
        JSONObject result = memberBenefitDataService.getYearMemberBenefitData(yearParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            filterYearUp(result.getJSONObject("info"));
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/dayReport/payTypeAnalyse")
    @AuthorityResource(name = "支付方式分析-日报", value = "mb_mobile_report_day_payTypeAnalyse", seq = 2, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getPayTypeAnalyse(DayParamDto dayParamDto) {
        log.info("支付方式分析-日报:{}",JSONObject.toJSON(dayParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(dayParamDto.getDay() == null || dayParamDto.getDay().trim().isEmpty()){
            return error(500,"请求日期不能为空");
        }

        /*if(dayParamDto.getMerchantId() == null || dayParamDto.getMerchantId() <=0){
            return error(500,"商户id不能为空");
        }*/
        try{
            Date day = sdf.parse(dayParamDto.getDay());
        }catch (Exception e){
            return error(500,"日期格式不正确，正确的日期格式为：yyyy-MM-dd");
        }

        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(dayParamDto);
        dayParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));
        JSONObject result = payTypeDataService.getPayTypeAnalyseData(dayParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }
    
    @GetMapping("/mobile/weekReport/payTypeAnalyse")
    @AuthorityResource(name = "支付方式分析-周报", value = "mb_mobile_report_week_payTypeAnalyse", seq = 20, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getWeekPayTypeAnalyse(WeekParamDto weekParamDto) {
        log.info("支付方式分析-周报:{}",JSONObject.toJSON(weekParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(weekParamDto.getMonth() == null || weekParamDto.getMonth().trim().isEmpty()){
            JSONObject monthWeek = TimeUtil.getDateMonthWeek(new Date());
            weekParamDto.setMonth(monthWeek.getString("month"));
            weekParamDto.setWeek(monthWeek.getInteger("week"));
        }

        try{
            Date day = msdf.parse(weekParamDto.getMonth());
        }catch (Exception e){
            return error(500,"月份格式不正确，正确的日期格式为：yyyy-MM");
        }

        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(weekParamDto);
        weekParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));
        JSONObject result = payTypeDataService.getWeekPayTypeAnalyseData(weekParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/monthReport/payTypeAnalyse")
    @AuthorityResource(name = "支付方式分析-月报", value = "mb_mobile_report_month_payTypeAnalyse", seq = 20, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getMonthPayTypeAnalyse(MonthParamDto monthParamDto) {
        log.info("支付方式分析-月报:{}",JSONObject.toJSON(monthParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(monthParamDto.getMonth() == null || monthParamDto.getMonth().trim().isEmpty()){
            return error(500,"月份不能为空");
        }

        try{
            Date day = msdf.parse(monthParamDto.getMonth());
        }catch (Exception e){
            return error(500,"月份格式不正确，正确的日期格式为：yyyy-MM");
        }


        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(monthParamDto);
        monthParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));
        JSONObject result = payTypeDataService.getMonthPayTypeAnalyseData(monthParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/yearReport/payTypeAnalyse")
    @AuthorityResource(name = "支付方式分析-年报", value = "mb_mobile_report_year_payTypeAnalyse", seq = 20, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getYearPayTypeAnalyse(YearParamDto yearParamDto) {
        log.info("支付方式分析-年报:{}",JSONObject.toJSON(yearParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(yearParamDto.getYear() == null || yearParamDto.getYear() < 1000){
            return error(500,"年份不能为空或者错误");
        }

        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(yearParamDto);
        yearParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));
        JSONObject result = payTypeDataService.getYearPayTypeAnalyseData(yearParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            filterYearUp(result.getJSONObject("info"));
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }


    @GetMapping("/mobile/dayReport/groupEquipmentAnalyse")
    @AuthorityResource(name = "场地设备分析-日报", value = "mb_mobile_report_day_groupEquipmentAnalyse", seq = 3, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getGroupEquipmentAnalyse(DayParamDto dayParamDto) {
        log.info("场地设备分析-日报:{}",JSONObject.toJSON(dayParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(dayParamDto.getDay() == null || dayParamDto.getDay().trim().isEmpty()){
            return error(500,"请求日期不能为空");
        }

        /*if(dayParamDto.getMerchantId() == null || dayParamDto.getMerchantId() <=0){
            return error(500,"商户id不能为空");
        }*/
        try{
            Date day = sdf.parse(dayParamDto.getDay());
        }catch (Exception e){
            return error(500,"日期格式不正确，正确的日期格式为：yyyy-MM-dd");
        }

        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(dayParamDto);
        dayParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));
        JSONObject result = equipmentGroupService.getGroupEquipmentData(dayParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            //filterYearUp(result.getJSONObject("info"));
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }


    @GetMapping("/mobile/weekReport/groupEquipmentAnalyse")
    @AuthorityResource(name = "场地设备分析-周报", value = "mb_mobile_report_week_groupEquipmentAnalyse", seq = 21, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getWeekGroupEquipmentAnalyse(WeekParamDto weekParamDto) {
        log.info("场地设备分析-日报:{}",JSONObject.toJSON(weekParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(weekParamDto.getMonth() == null || weekParamDto.getMonth().trim().isEmpty()){
            JSONObject monthWeek = TimeUtil.getDateMonthWeek(new Date());
            weekParamDto.setMonth(monthWeek.getString("month"));
            weekParamDto.setWeek(monthWeek.getInteger("week"));
        }

        try{
            Date day = msdf.parse(weekParamDto.getMonth());
        }catch (Exception e){
            return error(500,"月份格式不正确，正确的日期格式为：yyyy-MM");
        }

        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(weekParamDto);
        weekParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));
        JSONObject result = equipmentGroupService.getWeekGroupEquipmentData(weekParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }


    @GetMapping("/mobile/monthReport/groupEquipmentAnalyse")
    @AuthorityResource(name = "场地设备分析-月报", value = "mb_mobile_report_month_groupEquipmentAnalyse", seq = 21, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getMonthGroupEquipmentAnalyse(MonthParamDto monthParamDto) {
        log.info("场地设备分析-月报:{}",JSONObject.toJSON(monthParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(monthParamDto.getMonth() == null || monthParamDto.getMonth().trim().isEmpty()){
            return error(500,"月份不能为空");
        }

        try{
            Date day = msdf.parse(monthParamDto.getMonth());
        }catch (Exception e){
            return error(500,"月份格式不正确，正确的日期格式为：yyyy-MM");
        }

        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(monthParamDto);
        monthParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));
        JSONObject result = equipmentGroupService.getMonthGroupEquipmentData(monthParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/yearReport/groupEquipmentAnalyse")
    @AuthorityResource(name = "场地设备分析-年报", value = "mb_mobile_report_year_groupEquipmentAnalyse", seq = 21, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getYearGroupEquipmentAnalyse(YearParamDto yearParamDto) {
        log.info("场地设备分析-年报:{}",JSONObject.toJSON(yearParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(yearParamDto.getYear() == null || yearParamDto.getYear() < 1000){
            return error(500,"年份不能为空或者错误");
        }


        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(yearParamDto);
        yearParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));
        JSONObject result = equipmentGroupService.getYearGroupEquipmentData(yearParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            filterYearUp(result.getJSONObject("info"));
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }


    @GetMapping("/mobile/dayReport/orderMonthDayAnalyse")
    @AuthorityResource(name = "日历模式营收-日报", value = "mb_mobile_report_day_orderMonthDayAnalyse", seq = 4, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getOrderMonthDayAnalyse(DayParamDto dayParamDto) {
        log.info("日历模式营收-日报:{}",JSONObject.toJSON(dayParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        Date month = null;
        if(dayParamDto.getMonth() == null || dayParamDto.getMonth().trim().isEmpty()){
            return error(500,"请求月份不能为空");
        }

        /*if(dayParamDto.getMerchantId() == null || dayParamDto.getMerchantId() <=0){
            return error(500,"商户id不能为空");
        }*/
        try{
            month = msdf.parse(dayParamDto.getMonth());
        }catch (Exception e){
            return error(500,"月份格式不正确，正确的日期格式为：yyyy-MM");
        }
        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(dayParamDto);
        dayParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));
        JSONObject result = orderDataService.getDayOrderData(dayParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            //组装月份每天数据返回
            Map<String,JSONObject> map = new HashMap<>();
            if(result.getJSONObject("info") != null && result.getJSONObject("info").getJSONArray("list") != null && result.getJSONObject("info").getJSONArray("list").size()>0){
                for(int i=0; i<result.getJSONObject("info").getJSONArray("list").size(); i++){
                    map.put(result.getJSONObject("info").getJSONArray("list").getJSONObject(i).getString("day"),result.getJSONObject("info").getJSONArray("list").getJSONObject(i));
                }
            }
            JSONArray list = new JSONArray();
            TreeMap<Double, List<Integer>> treeMap = new TreeMap<>();
            int currentMonth = month.getMonth()+1;
            if(currentMonth == 1 || currentMonth == 3 || currentMonth == 5 || currentMonth == 7 || currentMonth == 8 || currentMonth == 10 || currentMonth == 12){
                for(int i=1; i<32; i++){
                    JSONObject data = map.get(i<10? dayParamDto.getMonth()+"-0"+i : dayParamDto.getMonth()+"-"+i);
                    if(data == null){
                        data = new JSONObject();
                        data.put("merchantId",dayParamDto.getMerchantId());
                        data.put("payAmount",null);
                        data.put("day",i<10 ? dayParamDto.getMonth()+"-0"+i:dayParamDto.getMonth()+"-"+i);
                        data.put("level",null);
                    }else{
                        data.put("level",2);
                        if(treeMap.get(data.getDouble("payAmount")) != null){
                            treeMap.get(data.getDouble("payAmount")).add(i-1);
                        }else{
                            List<Integer> indexList = new ArrayList<>();
                            indexList.add(i-1);
                            treeMap.put(data.getDouble("payAmount"),indexList);
                        }
                    }
                    list.add(data);

                }
            }

            if(currentMonth == 4 || currentMonth == 6 || currentMonth == 9 || currentMonth == 11){
                for(int i=1; i<31; i++){
                    JSONObject data = map.get(i<10? dayParamDto.getMonth()+"-0"+i : dayParamDto.getMonth()+"-"+i);
                    if(data == null){
                        data = new JSONObject();
                        data.put("merchantId",dayParamDto.getMerchantId());
                        data.put("payAmount",null);
                        data.put("day",i<10 ? dayParamDto.getMonth()+"-0"+i:dayParamDto.getMonth()+"-"+i);
                        data.put("level",null);;
                    }else{
                        data.put("level",2);
                        if(treeMap.get(data.getDouble("payAmount")) != null){
                            treeMap.get(data.getDouble("payAmount")).add(i-1);
                        }else{
                            List<Integer> indexList = new ArrayList<>();
                            indexList.add(i-1);
                            treeMap.put(data.getDouble("payAmount"),indexList);
                        }
                    }
                    list.add(data);
                }
            }

            if(currentMonth == 2 ){
                if(month.getYear() % 400 == 0 || (month.getYear() % 100 !=0 && month.getYear() % 4 ==0)){
                    for(int i=1; i<30; i++){
                        JSONObject data = map.get(i<10? dayParamDto.getMonth()+"-0"+i : dayParamDto.getMonth()+"-"+i);
                        if(data == null){
                            data = new JSONObject();
                            data.put("merchantId",dayParamDto.getMerchantId());
                            data.put("payAmount",null);
                            data.put("day",i<10 ? dayParamDto.getMonth()+"-0"+i:dayParamDto.getMonth()+"-"+i);
                            data.put("level",null);
                        }else{
                            data.put("level",2);
                            if(treeMap.get(data.getDouble("payAmount")) != null){
                                treeMap.get(data.getDouble("payAmount")).add(i-1);
                            }else{
                                List<Integer> indexList = new ArrayList<>();
                                indexList.add(i-1);
                                treeMap.put(data.getDouble("payAmount"),indexList);
                            }
                        }
                        list.add(data);
                    }
                }else{
                    for(int i=1; i<29; i++){
                        JSONObject data = map.get(i<10? dayParamDto.getMonth()+"-0"+i : dayParamDto.getMonth()+"-"+i);
                        if(data == null){
                            data = new JSONObject();
                            data.put("merchantId",dayParamDto.getMerchantId());
                            data.put("payAmount",null);
                            data.put("day",i<10 ? dayParamDto.getMonth()+"-0"+i:dayParamDto.getMonth()+"-"+i);
                            data.put("level",null);
                        }else{
                            data.put("level",2);
                            if(treeMap.get(data.getDouble("payAmount")) != null){
                                treeMap.get(data.getDouble("payAmount")).add(i-1);
                            }else{
                                List<Integer> indexList = new ArrayList<>();
                                indexList.add(i-1);
                                treeMap.put(data.getDouble("payAmount"),indexList);
                            }
                        }
                        list.add(data);
                    }
                }

            }

            //根据排序，算出每日营收等级
            if(treeMap.size()>0){
                Double[] keys = treeMap.keySet().toArray(new Double[0]);
                int flags = (int)Math.floor(keys.length*0.2);
                if(flags > 0){
                    //先处理较低的数据
                    for(int i = 0; i<flags; i++){
                        List<Integer> index  = treeMap.get(keys[i]);
                        for(int j=0; j< index.size(); j++){
                            list.getJSONObject(index.get(j)).put("level",1);
                        }
                    }

                    //处理较高的数据
                    for(int k=keys.length-1; k>=keys.length-flags; k--){
                        List<Integer> index  = treeMap.get(keys[k]);
                        for(int j=0; j< index.size(); j++){
                            list.getJSONObject(index.get(j)).put("level",3);
                        }
                    }
                }
            }
            //处理超过万的钱数
            for(int i=0; i<list.size(); i++){
                JSONObject obj = list.getJSONObject(i);
                if(obj.getDouble("payAmount") != null && obj.getDouble("payAmount") < 100){
                    DecimalFormat decimalFormat = new DecimalFormat("0.##");
                    obj.put("payAmount",decimalFormat.format(obj.getDouble("payAmount")));
                }

                if(obj.getDouble("payAmount") != null && obj.getDouble("payAmount") >= 100 && obj.getDouble("payAmount") < 1000){
                    DecimalFormat decimalFormat = new DecimalFormat("0.#");
                    obj.put("payAmount",decimalFormat.format(obj.getDouble("payAmount")));
                }

                if(obj.getDouble("payAmount") != null && obj.getDouble("payAmount") >= 1000 && obj.getDouble("payAmount") < 10000){
                    DecimalFormat decimalFormat = new DecimalFormat("#");
                    obj.put("payAmount",decimalFormat.format(obj.getDouble("payAmount")));
                }

                if(obj.getDouble("payAmount") != null && obj.getDouble("payAmount") > 10000){
                    DecimalFormat decimalFormat = new DecimalFormat("0.#");
                    obj.put("payAmount",decimalFormat.format(obj.getDouble("payAmount")/10000)+"万");
                }
            }
            result.getJSONObject("info").remove("list");
            result.getJSONObject("info").put("list",list);
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/weekReport/orderMonthWeekAnalyse")
    @AuthorityResource(name = "日历模式营收-周报", value = "mb_mobile_report_week_orderMonthWeekAnalyse", seq = 23, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getOrderMonthWeekAnalyse(WeekParamDto weekParamDto) {
        log.info("日历模式营收-周报:{}",JSONObject.toJSON(weekParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(weekParamDto.getMonth() == null || weekParamDto.getMonth().trim().isEmpty()){
            JSONObject monthWeek = TimeUtil.getDateMonthWeek(new Date());
            weekParamDto.setMonth(monthWeek.getString("month"));
        }
        Date day = null;
        try{
            day = msdf.parse(weekParamDto.getMonth());
        }catch (Exception e){
            return error(500,"月份格式不正确，正确的日期格式为：yyyy-MM");
        }
        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(weekParamDto);
        weekParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));
        JSONObject result = orderDataService.getPerWeekOrderData(weekParamDto);
        //JSONObject result = JSONObject.parseObject("{\"msg\":\"\",\"code\":\"200\",\"info\":{\"totalPage\":1,\"list\":[{\"payAmount\":\"27533.67\",\"week\":\"4\",\"merchantId\":\"1000319\"},{\"payAmount\":\"30646.12\",\"week\":\"3\",\"merchantId\":\"1000319\"},{\"payAmount\":\"42256.23\",\"week\":\"2\",\"merchantId\":\"1000319\"},{\"payAmount\":\"42535.04\",\"week\":\"1\",\"merchantId\":\"1000319\"}],\"totalCount\":4}}");
        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            //组装月份每天数据返回
            Map<String,JSONObject> map = new HashMap<>();
            if(result.getJSONObject("info") != null && result.getJSONObject("info").getJSONArray("list") != null && result.getJSONObject("info").getJSONArray("list").size()>0){
                for(int i=0; i<result.getJSONObject("info").getJSONArray("list").size(); i++){
                    map.put(result.getJSONObject("info").getJSONArray("list").getJSONObject(i).getString("week"),result.getJSONObject("info").getJSONArray("list").getJSONObject(i));
                }
            }
            JSONArray list = new JSONArray();
            TreeMap<Double, List<Integer>> treeMap = new TreeMap<>();
            JSONObject weekCount = TimeUtil.getMonthWeekCount(day);
            for(int i=1; i<= weekCount.getInteger("week"); i++){
                JSONObject data = map.get(i+"");
                if(data == null){
                    data = new JSONObject();
                    data.put("merchantId",weekParamDto.getMerchantId());
                    data.put("payAmount",null);
                    data.put("week",i);
                    data.put("level",null);
                }else{
                    data.put("level",2);
                    if(treeMap.get(data.getDouble("payAmount")) != null){
                        treeMap.get(data.getDouble("payAmount")).add(i-1);
                    }else{
                        List<Integer> indexList = new ArrayList<>();
                        indexList.add(i-1);
                        treeMap.put(data.getDouble("payAmount"),indexList);
                    }
                }
                list.add(data);
            }


            //根据排序，算出每周营收等级
            if(treeMap.size()>0){
                Double[] keys = treeMap.keySet().toArray(new Double[0]);
                int flags = (int)Math.floor(keys.length*0.25);
                if(flags > 0){
                    //先处理较低的数据
                    for(int i = 0; i<flags; i++){
                        List<Integer> index  = treeMap.get(keys[i]);
                        for(int j=0; j< index.size(); j++){
                            list.getJSONObject(index.get(j)).put("level",1);
                        }
                    }

                    //处理较高的数据
                    for(int k=keys.length-1; k>=keys.length-flags; k--){
                        List<Integer> index  = treeMap.get(keys[k]);
                        for(int j=0; j< index.size(); j++){
                            list.getJSONObject(index.get(j)).put("level",3);
                        }
                    }
                }
            }
            //处理超过万的钱数
            JSONObject weekRange = TimeUtil.getMonthWeekRange(day);
            for(int i=0; i<list.size(); i++){
                JSONObject obj = list.getJSONObject(i);
                obj.put("dateRange",weekRange.getString((i+1)+""));
                if(obj.getDouble("payAmount") != null && obj.getDouble("payAmount") < 100){
                    DecimalFormat decimalFormat = new DecimalFormat("0.##");
                    obj.put("payAmount",decimalFormat.format(obj.getDouble("payAmount")));
                }

                if(obj.getDouble("payAmount") != null && obj.getDouble("payAmount") >= 100 && obj.getDouble("payAmount") < 1000){
                    DecimalFormat decimalFormat = new DecimalFormat("0.#");
                    obj.put("payAmount",decimalFormat.format(obj.getDouble("payAmount")));
                }

                if(obj.getDouble("payAmount") != null && obj.getDouble("payAmount") >= 1000 && obj.getDouble("payAmount") < 10000){
                    DecimalFormat decimalFormat = new DecimalFormat("#");
                    obj.put("payAmount",decimalFormat.format(obj.getDouble("payAmount")));
                }

                if(obj.getDouble("payAmount") != null && obj.getDouble("payAmount") > 10000){
                    DecimalFormat decimalFormat = new DecimalFormat("0.#");
                    obj.put("payAmount",decimalFormat.format(obj.getDouble("payAmount")/10000)+"万");
                }
            }
            result.getJSONObject("info").remove("list");
            result.getJSONObject("info").put("list",list);
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/monthReport/orderYearMonthAnalyse")
    @AuthorityResource(name = "日历模式营收-月报", value = "mb_mobile_report_year_orderYearMonthAnalyse", seq = 23, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getOrderYearMonthAnalyse(MonthParamDto monthParamDto) {
        log.info("日历模式营收-月报:{}",JSONObject.toJSON(monthParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(monthParamDto.getYear() == null || monthParamDto.getYear()<=0){
            return error(500,"年份不能为空");
        }

        /*Date day = null;
        try{
            day = msdf.parse(weekParamDto.getMonth());
        }catch (Exception e){
            return error(500,"月份格式不正确，正确的日期格式为：yyyy-MM");
        }*/
        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(monthParamDto);
        monthParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));
        JSONObject result = orderDataService.getPerMonthOrderData(monthParamDto);
        //JSONObject result = JSONObject.parseObject("{\"msg\":\"\",\"code\":\"200\",\"info\":{\"totalPage\":1,\"list\":[{\"payAmount\":\"27533.67\",\"week\":\"4\",\"merchantId\":\"1000319\"},{\"payAmount\":\"30646.12\",\"week\":\"3\",\"merchantId\":\"1000319\"},{\"payAmount\":\"42256.23\",\"week\":\"2\",\"merchantId\":\"1000319\"},{\"payAmount\":\"42535.04\",\"week\":\"1\",\"merchantId\":\"1000319\"}],\"totalCount\":4}}");
        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            //组装月份每天数据返回
            Map<String,JSONObject> map = new HashMap<>();
            if(result.getJSONObject("info") != null && result.getJSONObject("info").getJSONArray("list") != null && result.getJSONObject("info").getJSONArray("list").size()>0){
                for(int i=0; i<result.getJSONObject("info").getJSONArray("list").size(); i++){
                    map.put(result.getJSONObject("info").getJSONArray("list").getJSONObject(i).getString("month"),result.getJSONObject("info").getJSONArray("list").getJSONObject(i));
                }
            }
            JSONArray list = new JSONArray();
            TreeMap<Double, List<Integer>> treeMap = new TreeMap<>();

            for(int i=1; i<= 12; i++){
                JSONObject data = map.get(monthParamDto.getYear()+"-"+(i>9? i : ("0"+i)));
                if(data == null){
                    data = new JSONObject();
                    data.put("merchantId",monthParamDto.getMerchantId());
                    data.put("payAmount",null);
                    data.put("month",monthParamDto.getYear()+"-"+(i>9? i : ("0"+i)));
                    data.put("level",null);
                }else{
                    data.put("level",2);
                    if(treeMap.get(data.getDouble("payAmount")) != null){
                        treeMap.get(data.getDouble("payAmount")).add(i-1);
                    }else{
                        List<Integer> indexList = new ArrayList<>();
                        indexList.add(i-1);
                        treeMap.put(data.getDouble("payAmount"),indexList);
                    }
                }
                list.add(data);
            }


            //根据排序，算出每周营收等级
            if(treeMap.size()>0){
                Double[] keys = treeMap.keySet().toArray(new Double[0]);
                int flags = (int)Math.floor(keys.length*0.2);
                if(flags > 0){
                    //先处理较低的数据
                    for(int i = 0; i<flags; i++){
                        List<Integer> index  = treeMap.get(keys[i]);
                        for(int j=0; j< index.size(); j++){
                            list.getJSONObject(index.get(j)).put("level",1);
                        }
                    }

                    //处理较高的数据
                    for(int k=keys.length-1; k>=keys.length-flags; k--){
                        List<Integer> index  = treeMap.get(keys[k]);
                        for(int j=0; j< index.size(); j++){
                            list.getJSONObject(index.get(j)).put("level",3);
                        }
                    }
                }
            }
            //处理超过万的钱数
            for(int i=0; i<list.size(); i++){
                JSONObject obj = list.getJSONObject(i);
                if(obj.getDouble("payAmount") != null && obj.getDouble("payAmount") < 100){
                    DecimalFormat decimalFormat = new DecimalFormat("0.##");
                    obj.put("payAmount",decimalFormat.format(obj.getDouble("payAmount")));
                }

                if(obj.getDouble("payAmount") != null && obj.getDouble("payAmount") >= 100 && obj.getDouble("payAmount") < 1000){
                    DecimalFormat decimalFormat = new DecimalFormat("0.#");
                    obj.put("payAmount",decimalFormat.format(obj.getDouble("payAmount")));
                }

                if(obj.getDouble("payAmount") != null && obj.getDouble("payAmount") >= 1000 && obj.getDouble("payAmount") < 10000){
                    DecimalFormat decimalFormat = new DecimalFormat("#");
                    obj.put("payAmount",decimalFormat.format(obj.getDouble("payAmount")));
                }

                if(obj.getDouble("payAmount") != null && obj.getDouble("payAmount") > 10000){
                    DecimalFormat decimalFormat = new DecimalFormat("0.#");
                    obj.put("payAmount",decimalFormat.format(obj.getDouble("payAmount")/10000)+"万");
                }
            }
            result.getJSONObject("info").remove("list");
            result.getJSONObject("info").put("list",list);
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/yearReport/orderYearAnalyse")
    @AuthorityResource(name = "日历模式营收-年报", value = "mb_mobile_report_year_orderYearAnalyse", seq = 23, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getOrderYearAnalyse(YearParamDto yearParamDto) {
        log.info("日历模式营收-年报:{}",JSONObject.toJSON(yearParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(yearParamDto.getYear() == null || yearParamDto.getYear()<=1000){
            return error(500,"年份不能为空或者不正确");
        }

        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(yearParamDto);
        yearParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));
        JSONObject result = orderDataService.getPerYearOrderData(yearParamDto);
        //JSONObject result = JSONObject.parseObject("{\"msg\":\"\",\"code\":\"200\",\"info\":{\"totalPage\":1,\"list\":[{\"payAmount\":\"27533.67\",\"week\":\"4\",\"merchantId\":\"1000319\"},{\"payAmount\":\"30646.12\",\"week\":\"3\",\"merchantId\":\"1000319\"},{\"payAmount\":\"42256.23\",\"week\":\"2\",\"merchantId\":\"1000319\"},{\"payAmount\":\"42535.04\",\"week\":\"1\",\"merchantId\":\"1000319\"}],\"totalCount\":4}}");
        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            JSONObject obj = null;
            if(result.getJSONObject("info") != null && result.getJSONObject("info").getJSONArray("list") != null && result.getJSONObject("info").getJSONArray("list").size()>0){
                obj = result.getJSONObject("info").getJSONArray("list").getJSONObject(0);
            }


            if(obj != null){
                if(obj.getDouble("payAmount") != null && obj.getDouble("payAmount") < 100){
                    DecimalFormat decimalFormat = new DecimalFormat("0.##");
                    obj.put("payAmount",decimalFormat.format(obj.getDouble("payAmount")));
                }

                if(obj.getDouble("payAmount") != null && obj.getDouble("payAmount") >= 100 && obj.getDouble("payAmount") < 1000){
                    DecimalFormat decimalFormat = new DecimalFormat("0.#");
                    obj.put("payAmount",decimalFormat.format(obj.getDouble("payAmount")));
                }

                if(obj.getDouble("payAmount") != null && obj.getDouble("payAmount") >= 1000 && obj.getDouble("payAmount") < 10000){
                    DecimalFormat decimalFormat = new DecimalFormat("#");
                    obj.put("payAmount",decimalFormat.format(obj.getDouble("payAmount")));
                }

                if(obj.getDouble("payAmount") != null && obj.getDouble("payAmount") > 10000){
                    DecimalFormat decimalFormat = new DecimalFormat("0.#");
                    obj.put("payAmount",decimalFormat.format(obj.getDouble("payAmount")/10000)+"万");
                }
            }


            return success(obj);
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/dayReport/timeRangeAnalyse")
    @AuthorityResource(name = "经营时段分析-日报", value = "mb_mobile_report_day_timeRangeAnalyse", seq = 5, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getTimeRangeAnalyse(DayParamDto dayParamDto) {
        log.info("经营时段分析-日报:{}",JSONObject.toJSON(dayParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(dayParamDto.getDay() == null || dayParamDto.getDay().trim().isEmpty()){
            return error(500,"请求日期不能为空");
        }

        /*if(dayParamDto.getMerchantId() == null || dayParamDto.getMerchantId() <=0){
            return error(500,"商户id不能为空");
        }*/
        try{
            Date day = sdf.parse(dayParamDto.getDay());
        }catch (Exception e){
            return error(500,"日期格式不正确，正确的日期格式为：yyyy-MM-dd");
        }

        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(dayParamDto);
        dayParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));
        JSONObject result = timeRangeDataService.getTimeRangeData(dayParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/weekReport/timeRangeAnalyse")
    @AuthorityResource(name = "经营时段分析-周报", value = "mb_mobile_report_week_timeRangeAnalyse", seq = 13, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getWeekTimeRangeAnalyse(WeekParamDto weekParamDto) {
        log.info("day_report_param:{}",JSONObject.toJSON(weekParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(weekParamDto.getMonth() == null || weekParamDto.getMonth().trim().isEmpty()){
            JSONObject monthWeek = TimeUtil.getDateMonthWeek(new Date());
            weekParamDto.setMonth(monthWeek.getString("month"));
            weekParamDto.setWeek(monthWeek.getInteger("week"));
        }

        try{
            Date day = msdf.parse(weekParamDto.getMonth());
        }catch (Exception e){
            return error(500,"月份格式不正确，正确的日期格式为：yyyy-MM");
        }


        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(weekParamDto);
        weekParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));
        JSONObject result = timeRangeDataService.getWeekTimeRangeData(weekParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/monthReport/timeRangeAnalyse")
    @AuthorityResource(name = "经营时段分析-月报", value = "mb_mobile_report_month_timeRangeAnalyse", seq = 13, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getMonthTimeRangeAnalyse(MonthParamDto monthParamDto) {
        log.info("经营时段分析-月报:{}",JSONObject.toJSON(monthParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(monthParamDto.getMonth() == null || monthParamDto.getMonth().trim().isEmpty()){
            return error(500,"月份不能为空");
        }

        try{
            Date day = msdf.parse(monthParamDto.getMonth());
        }catch (Exception e){
            return error(500,"月份格式不正确，正确的日期格式为：yyyy-MM");
        }


        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(monthParamDto);
        monthParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));
        JSONObject result = timeRangeDataService.getMonthTimeRangeData(monthParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/yearReport/timeRangeAnalyse")
    @AuthorityResource(name = "经营时段分析-年报", value = "mb_mobile_report_year_timeRangeAnalyse", seq = 13, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getYearTimeRangeAnalyse(YearParamDto yearParamDto) {
        log.info("经营时段分析-年报:{}",JSONObject.toJSON(yearParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(yearParamDto.getYear() == null || yearParamDto.getYear() < 1000){
            return error(500,"年份不能为空或者不正确");
        }


        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(yearParamDto);
        yearParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));
        JSONObject result = timeRangeDataService.getYearTimeRangeData(yearParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            filterYearUp(result.getJSONObject("info"));
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/dayReport/memberAnalyse")
    @AuthorityResource(name = "会员分析-日报", value = "mb_mobile_report_day_memberAnalyse", seq = 6, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getMemberAnalyse(DayParamDto dayParamDto) {
        log.info("会员分析-日报:{}",JSONObject.toJSON(dayParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(dayParamDto.getDay() == null || dayParamDto.getDay().trim().isEmpty()){
            return error(500,"请求日期不能为空");
        }

        /*if(dayParamDto.getMerchantId() == null || dayParamDto.getMerchantId() <=0){
            return error(500,"商户id不能为空");
        }*/
        try{
            Date day = sdf.parse(dayParamDto.getDay());
        }catch (Exception e){
            return error(500,"日期格式不正确，正确的日期格式为：yyyy-MM-dd");
        }

        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(dayParamDto);
        dayParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));
        JSONObject result = memberDataService.getMemberData(dayParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/weekReport/memberAnalyse")
    @AuthorityResource(name = "会员分析-周报", value = "mb_mobile_report_week_memberAnalyse", seq = 18, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getWeekMemberAnalyse(WeekParamDto weekParamDto) {
        log.info("会员分析-周报:{}",JSONObject.toJSON(weekParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }

        if(weekParamDto.getMonth() == null || weekParamDto.getMonth().trim().isEmpty()){
            JSONObject monthWeek = TimeUtil.getDateMonthWeek(new Date());
            weekParamDto.setMonth(monthWeek.getString("month"));
            weekParamDto.setWeek(monthWeek.getInteger("week"));
        }

        try{
            Date day = msdf.parse(weekParamDto.getMonth());
        }catch (Exception e){
            return error(500,"月份格式不正确，正确的日期格式为：yyyy-MM");
        }

        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(weekParamDto);
        weekParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));
        JSONObject result = memberDataService.getWeekMemberData(weekParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }


    @GetMapping("/mobile/monthReport/memberAnalyse")
    @AuthorityResource(name = "会员分析-月报", value = "mb_mobile_report_month_memberAnalyse", seq = 18, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getMonthMemberAnalyse(MonthParamDto monthParamDto) {
        log.info("会员分析-周报:{}",JSONObject.toJSON(monthParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }

        if(monthParamDto.getMonth() == null || monthParamDto.getMonth().trim().isEmpty()){
            return error(500,"月份不能为空");
        }

        try{
            Date day = msdf.parse(monthParamDto.getMonth());
        }catch (Exception e){
            return error(500,"月份格式不正确，正确的日期格式为：yyyy-MM");
        }

        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(monthParamDto);
        monthParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));
        JSONObject result = memberDataService.getMonthMemberData(monthParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/yearReport/memberAnalyse")
    @AuthorityResource(name = "会员分析-年报", value = "mb_mobile_report_year_memberAnalyse", seq = 18, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getYearMemberAnalyse(YearParamDto yearParamDto) {
        log.info("会员分析-年报:{}",JSONObject.toJSON(yearParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }

        if(yearParamDto.getYear() == null || yearParamDto.getYear() < 1000){
            return error(500,"年份不能为空或者错误");
        }


        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(yearParamDto);
        yearParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));
        JSONObject result = memberDataService.getYearMemberData(yearParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            filterYearUp(result.getJSONObject("info"));
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/dayReport/orderPriceRangeAnalyse")
    @AuthorityResource(name = "客单价分析-日报", value = "mb_mobile_report_day_orderPriceRangeAnalyse", seq = 7, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getOrderPriceRangeAnalyse(DayParamDto dayParamDto) {
        log.info("客单价分析-日报:{}",JSONObject.toJSON(dayParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(dayParamDto.getDay() == null || dayParamDto.getDay().trim().isEmpty()){
            return error(500,"请求日期不能为空");
        }

        /*if(dayParamDto.getMerchantId() == null || dayParamDto.getMerchantId() <=0){
            return error(500,"商户id不能为空");
        }*/
        try{
            Date day = sdf.parse(dayParamDto.getDay());
        }catch (Exception e){
            return error(500,"日期格式不正确，正确的日期格式为：yyyy-MM-dd");
        }
        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(dayParamDto);
        dayParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));
        JSONObject result = orderDataService.getPerOrderPriceCount(dayParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/weekReport/orderPriceRangeAnalyse")
    @AuthorityResource(name = "客单价分析-周报", value = "mb_mobile_report_week_orderPriceRangeAnalyse", seq = 7, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getWeekOrderPriceRangeAnalyse(WeekParamDto weekParamDto) {
        log.info("客单价分析-周报:{}",JSONObject.toJSON(weekParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(weekParamDto.getMonth() == null || weekParamDto.getMonth().trim().isEmpty()){
            JSONObject monthWeek = TimeUtil.getDateMonthWeek(new Date());
            weekParamDto.setMonth(monthWeek.getString("month"));
            weekParamDto.setWeek(monthWeek.getInteger("week"));
        }

        if(weekParamDto.getWeek() == null || weekParamDto.getWeek() <1){
            return error(500,"周数不能为空");
        }

        try{
            Date day = msdf.parse(weekParamDto.getMonth());
        }catch (Exception e){
            return error(500,"月份格式不正确，正确的日期格式为：yyyy-MM");
        }
        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(weekParamDto);
        weekParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));
        JSONObject result = orderDataService.getWeekPerOrderPriceCount(weekParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }


    @GetMapping("/mobile/monthReport/orderPriceRangeAnalyse")
    @AuthorityResource(name = "客单价分析-月报", value = "mb_mobile_report_month_orderPriceRangeAnalyse", seq = 7, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getMonthOrderPriceRangeAnalyse(MonthParamDto monthParamDto) {
        log.info("客单价分析-月报:{}",JSONObject.toJSON(monthParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(monthParamDto.getMonth() == null || monthParamDto.getMonth().trim().isEmpty()){
            JSONObject monthWeek = TimeUtil.getDateMonthWeek(new Date());
            monthParamDto.setMonth(monthWeek.getString("month"));
        }


        try{
            Date day = msdf.parse(monthParamDto.getMonth());
        }catch (Exception e){
            return error(500,"月份格式不正确，正确的日期格式为：yyyy-MM");
        }
        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(monthParamDto);
        monthParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));
        JSONObject result = orderDataService.getMonthPerOrderPriceCount(monthParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/yearReport/orderPriceRangeAnalyse")
    @AuthorityResource(name = "客单价分析-年报", value = "mb_mobile_report_year_orderPriceRangeAnalyse", seq = 7, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getYearOrderPriceRangeAnalyse(YearParamDto yearParamDto) {
        log.info("客单价分析-月报:{}",JSONObject.toJSON(yearParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(yearParamDto.getYear() == null || yearParamDto.getYear() < 1000){
            return error(500,"年份不能为空或者错误");
        }

        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(yearParamDto);
        yearParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));
        JSONObject result = orderDataService.getYearPerOrderPriceCount(yearParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            filterYearUp(result.getJSONObject("info"));
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/baseInfo/areaInfo")
    @AuthorityResource(name = "区域综合信息-基础数据", value = "mb_mobile_baseinfo_areaInfo", seq = 8, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getAreaInfo(DayParamDto dayParamDto,Integer type) {
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }

        if(type == null || type <1 || type > 3){
            return error(500,"获取省份城市信息类型错误");
        }
        if(type == 1){
            BaseResponse response = getProvince(dayParamDto);
            if(response.getData() != null){
                JSONObject data = (JSONObject)JSONObject.toJSON(response.getData());
                if(data.getJSONArray("list") != null && data.getJSONArray("list").size()>0){
                    for(int i=0; i<data.getJSONArray("list").size(); i++){
                        JSONObject obj = data.getJSONArray("list").getJSONObject(i);
                        obj.put("id",obj.getIntValue("provinceId"));
                        obj.put("name",obj.getString("provinceName"));
                        obj.put("level",1);
                        obj.remove("provinceId");
                        obj.remove("provinceName");
                    }
                    response.setData(data);
                }
            }
            return response;
        }

        if(type == 2){
            dayParamDto.setProvinceId(dayParamDto.getParentId());
            BaseResponse response = getCity(dayParamDto);
            if(response.getData() != null){
                JSONObject data = (JSONObject)JSONObject.toJSON(response.getData());
                if(data.getJSONArray("list") != null && data.getJSONArray("list").size()>0){
                    for(int i=0; i<data.getJSONArray("list").size(); i++){
                        JSONObject obj = data.getJSONArray("list").getJSONObject(i);
                        obj.put("id",obj.getIntValue("cityId"));
                        obj.put("name",obj.getString("cityName"));
                        obj.put("level",2);
                        obj.remove("cityId");
                        obj.remove("cityName");
                    }
                    response.setData(data);
                }
            }
            return response;
        }

        if(type == 3){
            dayParamDto.setCityId(dayParamDto.getParentId());
            BaseResponse response = getArea(dayParamDto);
            if(response.getData() != null){
                JSONObject data = (JSONObject)JSONObject.toJSON(response.getData());
                if(data.getJSONArray("list") != null && data.getJSONArray("list").size()>0){
                    for(int i=0; i<data.getJSONArray("list").size(); i++){
                        JSONObject obj = data.getJSONArray("list").getJSONObject(i);
                        obj.put("id",obj.getIntValue("areaId"));
                        obj.put("name",obj.getString("areaName"));
                        obj.put("level",3);
                        obj.remove("areaId");
                        obj.remove("areaName");
                    }
                    response.setData(data);
                }
            }
            return response;
        }

        return error(500,"服务器异常");
    }

    @GetMapping("/mobile/baseInfo/province")
    @AuthorityResource(name = "省份信息-基础数据", value = "mb_mobile_baseinfo_province", seq = 8, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getProvince(DayParamDto dayParamDto) {
        log.info("省份信息-基础数据:{}",JSONObject.toJSON(dayParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }

        /*if(dayParamDto.getMerchantId() == null || dayParamDto.getMerchantId() <=0){
            return error(500,"商户id不能为空");
        }*/
        getGroupId(dayParamDto);
        log.info("商家id：{}",getAdOrgIdNotNull());
        dayParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));
        JSONObject result = oneDataService.getProvince(dayParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/baseInfo/city")
    @AuthorityResource(name = "城市信息-基础数据", value = "mb_mobile_baseinfo_city", seq = 9, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getCity(DayParamDto dayParamDto) {
        log.info("城市信息-基础数据:{}",JSONObject.toJSON(dayParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        /*if(dayParamDto.getMerchantId() == null || dayParamDto.getMerchantId() <=0){
            return error(500,"商户id不能为空");
        }*/

        if(dayParamDto.getProvinceId() == null){
            return error(500,"请求省份id不能为空");
        }
        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(dayParamDto);
        dayParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));
        JSONObject result = oneDataService.getCity(dayParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/baseInfo/area")
    @AuthorityResource(name = "区域信息-基础数据", value = "mb_mobile_baseinfo_area", seq = 10, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getArea(DayParamDto dayParamDto) {
        log.info("区域信息-基础数据:{}",JSONObject.toJSON(dayParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }

        /*if(dayParamDto.getMerchantId() == null || dayParamDto.getMerchantId() <=0){
            return error(500,"商户id不能为空");
        }*/

        /*if(dayParamDto.getProvinceId() == null){
            return error(500,"请求省份id不能为空");
        }*/

        if(dayParamDto.getCityId() == null){
            return error(500,"请求城市id不能为空");
        }
        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(dayParamDto);
        dayParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));
        JSONObject result = oneDataService.getArea(dayParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/merchant/group")
    @AuthorityResource(name = "商家场地信息-基础数据", value = "mb_mobile_baseinfo_merchant_group", seq = 11, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getMerchantGroup(DayParamDto dayParamDto) {
        log.info("商家场地信息-基础数据:{}",JSONObject.toJSON(dayParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }

        /*if(dayParamDto.getMerchantId() == null || dayParamDto.getMerchantId() <=0){
            return error(500,"商户id不能为空");
        }*/

        /*if(dayParamDto.getProvinceId() == null || dayParamDto.getProvinceId() <=0){
            return error(500,"请求省份id不能为空");
        }

        if(dayParamDto.getCityId() == null || dayParamDto.getCityId() <=0){
            return error(500,"请求城市id不能为空");
        }

        if(dayParamDto.getAreaId() == null || dayParamDto.getAreaId() <=0){
            return error(500,"请求区域id不能为空");
        }*/
        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(dayParamDto);
        dayParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));
        JSONObject result = oneDataService.getGroup(dayParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/stableWhiteListCode")
    @AuthorityResource(name = "移动端报表白名单-基础数据", value = "mb_mobile_report_stableWhiteListCode", seq = 12, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getstableWhiteListCode(DayParamDto dayParamDto) {
        log.info("移动端报表白名单-基础数据:{}",JSONObject.toJSON(dayParamDto).toString());
        /*if(dayParamDto.getMerchantId() == null || dayParamDto.getMerchantId() <=0){
            return error(500,"商户id不能为空");
        }*/
        log.info("商家id：{}",getAdOrgIdNotNull());

        if(testModel){
            return success(false);
        }
        if(isWith(stableWhiteListCode)){
            return success(true);
        }
        return success(false);
    }

    @GetMapping("/mobile/isReporter")
    @AuthorityResource(name = "移动端报表白名单-基础数据", value = "mb_mobile_report_isReporter", seq = 12, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getIsReporter(DayParamDto dayParamDto) {
        log.info("移动端报表白名单-基础数据:{}",JSONObject.toJSON(dayParamDto).toString());
        /*if(dayParamDto.getMerchantId() == null || dayParamDto.getMerchantId() <=0){
            return error(500,"商户id不能为空");
        }*/
        log.info("商家id：{}",getAdOrgIdNotNull());

        if(testModel){
            return success(false);
        }
        if(isWith(stableWhiteListCode)){
            return success(true);
        }
        return success(false);
    }

    @GetMapping("/mobile/isEverydayReporter")
    @AuthorityResource(name = "移动端报表每日账单-基础数据", value = "mb_mobile_report_isEverydayReporter", seq = 12, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getIsEverydayReporter(DayParamDto dayParamDto) {
        log.info("移动端报表白名单-基础数据:{}",JSONObject.toJSON(dayParamDto).toString());

        log.info("商家id：{}",getAdOrgIdNotNull());

        if(isWith(everyDayReportWhileListCode) && !getCurrentUser().getIsApprover()){
            return success(false);
        }
        return success(true);
    }

    @GetMapping("/mobile/isSecReporter")
    @AuthorityResource(name = "移动端报表二期白名单-基础数据", value = "mb_mobile_report_isSecReporter", seq = 12, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getIsSecReporter(DayParamDto dayParamDto) {
        log.info("移动端报表白名单-基础数据:{}",JSONObject.toJSON(dayParamDto).toString());
        log.info("商家id：{}",getAdOrgIdNotNull());

        if(testModel){
            return success(false);
        }
        if(isWith(stableWhiteListCode)){
            return success(true);
        }
        return success(false);
    }
    
    @GetMapping("/mobile/isThirdReporter")
    @AuthorityResource(name = "移动端报表三期白名单-基础数据", value = "mb_mobile_report_isThirdReporter", seq = 12, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getIsThirdReporter(DayParamDto dayParamDto) {
        log.info("移动端报表白名单-基础数据:{}",JSONObject.toJSON(dayParamDto).toString());
        log.info("商家id：{}",getAdOrgIdNotNull());

        if(testModel){
            return success(false);
        }
        if(isWith(stableWhiteListCode)){
            return success(true);
        }
        return success(false);
    }


    @GetMapping("/mobile/isMainAccount")
    @AuthorityResource(name = "移动端报表账户类型-基础数据", value = "mb_mobile_report_isMainAccount", seq = 12, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getIsMainAccount(DayParamDto dayParamDto) {
        log.info("移动端报表账户类型-基础数据:{}",JSONObject.toJSON(dayParamDto).toString());

        log.info("商家id：{}",getAdOrgIdNotNull());
        if(getCurrentUser().getIsApprover()){
            return success(true);
        }

        return success(false);
    }

    @GetMapping("/mobile/equipment/type")
    @AuthorityResource(name = "获取设备类型-基础数据", value = "mb_mobile_report_equipment_type", seq = 13, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getEquipmentType(DayParamDto dayParamDto) {
        log.info("获取设备类型-基础数据:{}",JSONObject.toJSON(dayParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        /*if(dayParamDto.getMerchantId() == null || dayParamDto.getMerchantId() <=0){
            return error(500,"商户id不能为空");
        }*/
        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(dayParamDto);
        dayParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));
        JSONObject result = oneDataService.getMerchantEquipmentType(dayParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/baseInfo/benefitType")
    @AuthorityResource(name = "获取储值类型-基础数据", value = "mb_mobile_report_benefit_type", seq = 26, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getBenefitType(DayParamDto dayParamDto) {
        log.info("获取储值类型-基础数据:{}",JSONObject.toJSON(dayParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        /*if(dayParamDto.getMerchantId() == null || dayParamDto.getMerchantId() <=0){
            return error(500,"商户id不能为空");
        }*/
        return success(new BenefitTypeDto().getBenefitTypeList());
    }

    @GetMapping("/mobile/dayReport/province")
    @AuthorityResource(name = "省份二级分析-日报", value = "mb_mobile_report_day_province_analyse", seq = 31, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getProvinceDayAnalyse(DayParamDto dayParamDto) {
        log.info("省份二级分析-日报:{}",JSONObject.toJSON(dayParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        
        if(dayParamDto.getDay() == null || dayParamDto.getDay().trim().isEmpty()){
            return error(500,"请求日期不能为空");
        }

        try{
            Date day = sdf.parse(dayParamDto.getDay());
        }catch (Exception e){
            return error(500,"日期格式不正确，正确的日期格式为：yyyy-MM-dd");
        }
        dayParamDto.setOrderBy(null);
        dayParamDto.setOrder(null);
        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(dayParamDto);
        dayParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        JSONObject result = provinceDataService.getDayProvinceData(dayParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
        	return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/weekReport/province")
    @AuthorityResource(name = "省份二级分析-周报", value = "mb_mobile_report_week_province_analyse", seq = 31, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getProvinceWeekAnalyse(WeekParamDto weekParamDto) {
        log.info("省份二级分析-周报:{}",JSONObject.toJSON(weekParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(weekParamDto.getMonth() == null || weekParamDto.getMonth().trim().isEmpty()){
            JSONObject monthWeek = TimeUtil.getDateMonthWeek(new Date());
            weekParamDto.setMonth(monthWeek.getString("month"));
            weekParamDto.setWeek(monthWeek.getInteger("week"));
        }

        if(weekParamDto.getWeek() == null || weekParamDto.getWeek() <1){
            return error(500,"周数不能为空");
        }

        try{
            Date day = msdf.parse(weekParamDto.getMonth());
        }catch (Exception e){
            return error(500,"月份格式不正确，正确的日期格式为：yyyy-MM");
        }
        weekParamDto.setOrderBy(null);
        weekParamDto.setOrder(null);
        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(weekParamDto);
        weekParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        JSONObject result = provinceDataService.getWeekProvinceData(weekParamDto);
        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
        	return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/monthReport/province")
    @AuthorityResource(name = "省份二级分析-月报", value = "mb_mobile_report_month_province_analyse", seq = 31, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getProvinceMonthAnalyse(MonthParamDto monthParamDto) {
        log.info("省份二级分析-月报:{}",JSONObject.toJSON(monthParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(monthParamDto.getMonth() == null || monthParamDto.getMonth().trim().isEmpty()){
            JSONObject monthWeek = TimeUtil.getDateMonthWeek(new Date());
            monthParamDto.setMonth(monthWeek.getString("month"));
        }


        try{
            Date day = msdf.parse(monthParamDto.getMonth());
        }catch (Exception e){
            return error(500,"月份格式不正确，正确的日期格式为：yyyy-MM");
        }
        monthParamDto.setOrderBy(null);
        monthParamDto.setOrder(null);
        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(monthParamDto);
        monthParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        JSONObject result = provinceDataService.getMonthProvinceData(monthParamDto);
        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
        	return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/yearReport/province")
    @AuthorityResource(name = "省份二级分析-年报", value = "mb_mobile_report_year_province_analyse", seq = 31, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getProvinceYearAnalyse(YearParamDto yearParamDto) {
        log.info("省份二级分析-年报:{}",JSONObject.toJSON(yearParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(yearParamDto.getYear() == null || yearParamDto.getYear() < 1000){
            return error(500,"年份不能为空或者错误");
        }

        yearParamDto.setOrderBy(null);
        yearParamDto.setOrder(null);
        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(yearParamDto);
        yearParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        JSONObject result = provinceDataService.getYearProvinceData(yearParamDto);
        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
        	return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }
    
    @GetMapping("/mobile/dayReport/province/avg")
    @AuthorityResource(name = "省份二级分析-日报-底部均值", value = "mb_mobile_report_day_province_avg_analyse", seq = 31, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getProvinceDayAvgAnalyse(DayParamDto dayParamDto) {
        log.info("省份二级分析-日报-底部均值:{}",JSONObject.toJSON(dayParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        
        if(dayParamDto.getDay() == null || dayParamDto.getDay().trim().isEmpty()){
            return error(500,"请求日期不能为空");
        }

        try{
            Date day = sdf.parse(dayParamDto.getDay());
        }catch (Exception e){
            return error(500,"日期格式不正确，正确的日期格式为：yyyy-MM-dd");
        }
        dayParamDto.setOrderBy(null);
        dayParamDto.setOrder(null);
        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(dayParamDto);
        dayParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        JSONObject result = provinceDataService.getDayProvinceAvgData(dayParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
        	return success(result.getJSONObject("info").get("list") );
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/weekReport/province/avg")
    @AuthorityResource(name = "省份二级分析-周报-底部均值", value = "mb_mobile_report_week_province_avg_analyse", seq = 31, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getProvinceWeekAvgAnalyse(WeekParamDto weekParamDto) {
        log.info("省份二级分析-周报-底部均值:{}",JSONObject.toJSON(weekParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(weekParamDto.getMonth() == null || weekParamDto.getMonth().trim().isEmpty()){
            JSONObject monthWeek = TimeUtil.getDateMonthWeek(new Date());
            weekParamDto.setMonth(monthWeek.getString("month"));
            weekParamDto.setWeek(monthWeek.getInteger("week"));
        }

        if(weekParamDto.getWeek() == null || weekParamDto.getWeek() <1){
            return error(500,"周数不能为空");
        }

        try{
            Date day = msdf.parse(weekParamDto.getMonth());
        }catch (Exception e){
            return error(500,"月份格式不正确，正确的日期格式为：yyyy-MM");
        }
        weekParamDto.setOrderBy(null);
        weekParamDto.setOrder(null);
        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(weekParamDto);
        weekParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        JSONObject result = provinceDataService.getWeekProvinceAvgData(weekParamDto);
        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
        	return success(result.getJSONObject("info").get("list") );
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/monthReport/province/avg")
    @AuthorityResource(name = "省份二级分析-月报-底部均值", value = "mb_mobile_report_month_province_avg_analyse", seq = 31, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getProvinceMonthAvgAnalyse(MonthParamDto monthParamDto) {
        log.info("省份二级分析-月报-底部均值:{}",JSONObject.toJSON(monthParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(monthParamDto.getMonth() == null || monthParamDto.getMonth().trim().isEmpty()){
            JSONObject monthWeek = TimeUtil.getDateMonthWeek(new Date());
            monthParamDto.setMonth(monthWeek.getString("month"));
        }


        try{
            Date day = msdf.parse(monthParamDto.getMonth());
        }catch (Exception e){
            return error(500,"月份格式不正确，正确的日期格式为：yyyy-MM");
        }
        monthParamDto.setOrderBy(null);
        monthParamDto.setOrder(null);
        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(monthParamDto);
        monthParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        JSONObject result = provinceDataService.getMonthProvinceAvgData(monthParamDto);
        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
        	return success(result.getJSONObject("info").get("list") );
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/yearReport/province/avg")
    @AuthorityResource(name = "省份二级分析-年报-底部均值", value = "mb_mobile_report_year_province_avg_analyse", seq = 31, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getProvinceYearAvgAnalyse(YearParamDto yearParamDto) {
        log.info("省份二级分析-年报-底部均值:{}",JSONObject.toJSON(yearParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(yearParamDto.getYear() == null || yearParamDto.getYear() < 1000){
            return error(500,"年份不能为空或者错误");
        }

        yearParamDto.setOrderBy(null);
        yearParamDto.setOrder(null);
        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(yearParamDto);
        yearParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        JSONObject result = provinceDataService.getYearProvinceAvgData(yearParamDto);
        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
        	return success(result.getJSONObject("info").get("list") );
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }
    
    @GetMapping("/mobile/dayReport/city")
    @AuthorityResource(name = "城市二级分析-日报", value = "mb_mobile_report_day_city_analyse", seq = 31, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getCityDayAnalyse(DayParamDto dayParamDto) {
        log.info("城市二级分析-日报:{}",JSONObject.toJSON(dayParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        
        if(dayParamDto.getDay() == null || dayParamDto.getDay().trim().isEmpty()){
            return error(500,"请求日期不能为空");
        }

        try{
            Date day = sdf.parse(dayParamDto.getDay());
        }catch (Exception e){
            return error(500,"日期格式不正确，正确的日期格式为：yyyy-MM-dd");
        }
        dayParamDto.setOrderBy(null);
        dayParamDto.setOrder(null);
        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(dayParamDto);
        dayParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        JSONObject result = cityDataService.getDayCityData(dayParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
        	return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/weekReport/city")
    @AuthorityResource(name = "城市二级分析-周报", value = "mb_mobile_report_week_city_analyse", seq = 31, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getCityWeekAnalyse(WeekParamDto weekParamDto) {
        log.info("城市二级分析-周报:{}",JSONObject.toJSON(weekParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(weekParamDto.getMonth() == null || weekParamDto.getMonth().trim().isEmpty()){
            JSONObject monthWeek = TimeUtil.getDateMonthWeek(new Date());
            weekParamDto.setMonth(monthWeek.getString("month"));
            weekParamDto.setWeek(monthWeek.getInteger("week"));
        }

        if(weekParamDto.getWeek() == null || weekParamDto.getWeek() <1){
            return error(500,"周数不能为空");
        }

        try{
            Date day = msdf.parse(weekParamDto.getMonth());
        }catch (Exception e){
            return error(500,"月份格式不正确，正确的日期格式为：yyyy-MM");
        }
        weekParamDto.setOrderBy(null);
        weekParamDto.setOrder(null);
        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(weekParamDto);
        weekParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        JSONObject result = cityDataService.getWeekCityData(weekParamDto);
        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
        	return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/monthReport/city")
    @AuthorityResource(name = "城市二级分析-月报", value = "mb_mobile_report_month_city_analyse", seq = 31, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getCityMonthAnalyse(MonthParamDto monthParamDto) {
        log.info("城市二级分析-月报:{}",JSONObject.toJSON(monthParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(monthParamDto.getMonth() == null || monthParamDto.getMonth().trim().isEmpty()){
            JSONObject monthWeek = TimeUtil.getDateMonthWeek(new Date());
            monthParamDto.setMonth(monthWeek.getString("month"));
        }

        try{
            Date day = msdf.parse(monthParamDto.getMonth());
        }catch (Exception e){
            return error(500,"月份格式不正确，正确的日期格式为：yyyy-MM");
        }
        monthParamDto.setOrderBy(null);
        monthParamDto.setOrder(null);
        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(monthParamDto);
        monthParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        JSONObject result = cityDataService.getMonthCityData(monthParamDto);
        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
        	return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/yearReport/city")
    @AuthorityResource(name = "城市二级分析-年报", value = "mb_mobile_report_year_city_analyse", seq = 31, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getCityYearAnalyse(YearParamDto yearParamDto) {
        log.info("城市二级分析-年报:{}",JSONObject.toJSON(yearParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(yearParamDto.getYear() == null || yearParamDto.getYear() < 1000){
            return error(500,"年份不能为空或者错误");
        }

        yearParamDto.setOrderBy(null);
        yearParamDto.setOrder(null);
        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(yearParamDto);
        yearParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        JSONObject result = cityDataService.getYearCityData(yearParamDto);
        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
        	return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }
    
    @GetMapping("/mobile/dayReport/city/avg")
    @AuthorityResource(name = "城市二级分析-日报-底部均值", value = "mb_mobile_report_day_city_avg_analyse", seq = 31, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getCityDayAvgAnalyse(DayParamDto dayParamDto) {
        log.info("城市二级分析-日报-底部均值:{}",JSONObject.toJSON(dayParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        
        if(dayParamDto.getDay() == null || dayParamDto.getDay().trim().isEmpty()){
            return error(500,"请求日期不能为空");
        }

        try{
            Date day = sdf.parse(dayParamDto.getDay());
        }catch (Exception e){
            return error(500,"日期格式不正确，正确的日期格式为：yyyy-MM-dd");
        }
        dayParamDto.setOrderBy(null);
        dayParamDto.setOrder(null);
        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(dayParamDto);
        dayParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        JSONObject result = cityDataService.getDayCityAvgData(dayParamDto);
        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
        	return success(result.getJSONObject("info").get("list") );
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/weekReport/city/avg")
    @AuthorityResource(name = "城市二级分析-周报-底部均值", value = "mb_mobile_report_week_city_avg_analyse", seq = 31, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getCityWeekAvgAnalyse(WeekParamDto weekParamDto) {
        log.info("城市二级分析-周报-底部均值:{}",JSONObject.toJSON(weekParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(weekParamDto.getMonth() == null || weekParamDto.getMonth().trim().isEmpty()){
            JSONObject monthWeek = TimeUtil.getDateMonthWeek(new Date());
            weekParamDto.setMonth(monthWeek.getString("month"));
            weekParamDto.setWeek(monthWeek.getInteger("week"));
        }

        if(weekParamDto.getWeek() == null || weekParamDto.getWeek() <1){
            return error(500,"周数不能为空");
        }

        try{
            Date day = msdf.parse(weekParamDto.getMonth());
        }catch (Exception e){
            return error(500,"月份格式不正确，正确的日期格式为：yyyy-MM");
        }
        weekParamDto.setOrderBy(null);
        weekParamDto.setOrder(null);
        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(weekParamDto);
        weekParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        JSONObject result = cityDataService.getWeekCityAvgData(weekParamDto);
        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
        	return success(result.getJSONObject("info").get("list") );
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/monthReport/city/avg")
    @AuthorityResource(name = "城市二级分析-月报-底部均值", value = "mb_mobile_report_month_city_avg_analyse", seq = 31, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getCityMonthAvgAnalyse(MonthParamDto monthParamDto) {
        log.info("城市二级分析-月报-底部均值:{}",JSONObject.toJSON(monthParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(monthParamDto.getMonth() == null || monthParamDto.getMonth().trim().isEmpty()){
            JSONObject monthWeek = TimeUtil.getDateMonthWeek(new Date());
            monthParamDto.setMonth(monthWeek.getString("month"));
        }

        try{
            Date day = msdf.parse(monthParamDto.getMonth());
        }catch (Exception e){
            return error(500,"月份格式不正确，正确的日期格式为：yyyy-MM");
        }
        monthParamDto.setOrderBy(null);
        monthParamDto.setOrder(null);
        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(monthParamDto);
        monthParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        JSONObject result = cityDataService.getMonthCityAvgData(monthParamDto);
        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
        	return success(result.getJSONObject("info").get("list") );
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/yearReport/city/avg")
    @AuthorityResource(name = "城市二级分析-年报-底部均值", value = "mb_mobile_report_year_city_avg_analyse", seq = 31, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getCityYearAvgAnalyse(YearParamDto yearParamDto) {
        log.info("城市二级分析-年报-底部均值:{}",JSONObject.toJSON(yearParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(yearParamDto.getYear() == null || yearParamDto.getYear() < 1000){
            return error(500,"年份不能为空或者错误");
        }

        yearParamDto.setOrderBy(null);
        yearParamDto.setOrder(null);
        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(yearParamDto);
        yearParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        JSONObject result = cityDataService.getYearCityAvgData(yearParamDto);
        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
        	return success(result.getJSONObject("info").get("list") );
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }
    
    @GetMapping("/mobile/dayReport/group")
    @AuthorityResource(name = "场地二级分析-日报", value = "mb_mobile_report_day_group_analyse", seq = 31, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getGroupAnalyse(DayParamDto dayParamDto) {
        log.info("场地二级分析-日报:{}",JSONObject.toJSON(dayParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(dayParamDto.getDay() == null || dayParamDto.getDay().trim().isEmpty()){
            return error(500,"请求日期不能为空");
        }

//        /*if(dayParamDto.getMerchantId() == null || dayParamDto.getMerchantId() <=0){
//            return error(500,"商户id不能为空");
//        }*/
        try{
            Date day = sdf.parse(dayParamDto.getDay());
        }catch (Exception e){
            return error(500,"日期格式不正确，正确的日期格式为：yyyy-MM-dd");
        }
        dayParamDto.setOrderBy(null);
        dayParamDto.setOrder(null);
        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(dayParamDto);
        dayParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        JSONObject result = equipmentGroupService.getDayGroupData(dayParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/weekReport/group")
    @AuthorityResource(name = "场地二级分析-日报", value = "mb_mobile_report_week_group_analyse", seq = 31, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getGroupWeekAnalyse(WeekParamDto weekParamDto) {
        log.info("场地二级分析-周报:{}",JSONObject.toJSON(weekParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(weekParamDto.getMonth() == null || weekParamDto.getMonth().trim().isEmpty()){
            JSONObject monthWeek = TimeUtil.getDateMonthWeek(new Date());
            weekParamDto.setMonth(monthWeek.getString("month"));
            weekParamDto.setWeek(monthWeek.getInteger("week"));
        }

        if(weekParamDto.getWeek() == null || weekParamDto.getWeek() <1){
            return error(500,"周数不能为空");
        }

        try{
            Date day = msdf.parse(weekParamDto.getMonth());
        }catch (Exception e){
            return error(500,"月份格式不正确，正确的日期格式为：yyyy-MM");
        }
        weekParamDto.setOrderBy(null);
        weekParamDto.setOrder(null);
        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(weekParamDto);
        weekParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        JSONObject result = equipmentGroupService.getWeekGroupData(weekParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/monthReport/group")
    @AuthorityResource(name = "场地二级分析-日报", value = "mb_mobile_report_month_group_analyse", seq = 31, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getGroupMonthAnalyse(MonthParamDto monthParamDto) {
        log.info("场地二级分析-月报:{}",JSONObject.toJSON(monthParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(monthParamDto.getMonth() == null || monthParamDto.getMonth().trim().isEmpty()){
            JSONObject monthWeek = TimeUtil.getDateMonthWeek(new Date());
            monthParamDto.setMonth(monthWeek.getString("month"));
        }


        try{
            Date day = msdf.parse(monthParamDto.getMonth());
        }catch (Exception e){
            return error(500,"月份格式不正确，正确的日期格式为：yyyy-MM");
        }
        monthParamDto.setOrderBy(null);
        monthParamDto.setOrder(null);
        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(monthParamDto);
        monthParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        JSONObject result = equipmentGroupService.getMonthGroupData(monthParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/yearReport/group")
    @AuthorityResource(name = "场地二级分析-日报", value = "mb_mobile_report_year_group_analyse", seq = 31, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getGroupYearAnalyse(YearParamDto yearParamDto) {
        log.info("场地二级分析-年报:{}",JSONObject.toJSON(yearParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(yearParamDto.getYear() == null || yearParamDto.getYear() < 1000){
            return error(500,"年份不能为空或者错误");
        }

        yearParamDto.setOrderBy(null);
        yearParamDto.setOrder(null);
        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(yearParamDto);
        yearParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        JSONObject result = equipmentGroupService.getYearGroupData(yearParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            filterYearUp(result.getJSONObject("info"));
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }
   
    @GetMapping("/mobile/dayReport/equipment")
    @AuthorityResource(name = "设备二级分析-日报", value = "mb_mobile_report_day_equipment_analyse", seq = 31, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getEquipmentAnalyse(DayParamDto dayParamDto) {
        log.info("设备二级分析-日报:{}",JSONObject.toJSON(dayParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(dayParamDto.getDay() == null || dayParamDto.getDay().trim().isEmpty()){
            return error(500,"请求日期不能为空");
        }

        /*if(dayParamDto.getMerchantId() == null || dayParamDto.getMerchantId() <=0){
            return error(500,"商户id不能为空");
        }*/
        try{
            Date day = sdf.parse(dayParamDto.getDay());
        }catch (Exception e){
            return error(500,"日期格式不正确，正确的日期格式为：yyyy-MM-dd");
        }
        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(dayParamDto);
        dayParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        JSONObject result = equipmentDataService.getDayEquipmentData(dayParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }


    @GetMapping("/mobile/weekReport/equipment")
    @AuthorityResource(name = "设备二级分析-日报", value = "mb_mobile_report_week_equipment_analyse", seq = 31, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getEquipmentWeekAnalyse(WeekParamDto weekParamDto) {
        log.info("设备二级分析-周报:{}",JSONObject.toJSON(weekParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(weekParamDto.getMonth() == null || weekParamDto.getMonth().trim().isEmpty()){
            JSONObject monthWeek = TimeUtil.getDateMonthWeek(new Date());
            weekParamDto.setMonth(monthWeek.getString("month"));
            weekParamDto.setWeek(monthWeek.getInteger("week"));
        }

        if(weekParamDto.getWeek() == null || weekParamDto.getWeek() <1){
            return error(500,"周数不能为空");
        }

        try{
            Date day = msdf.parse(weekParamDto.getMonth());
        }catch (Exception e){
            return error(500,"月份格式不正确，正确的日期格式为：yyyy-MM");
        }
        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(weekParamDto);
        weekParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        JSONObject result = equipmentDataService.getWeekEquipmentData(weekParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/monthReport/equipment")
    @AuthorityResource(name = "设备二级分析-日报", value = "mb_mobile_report_month_equipment_analyse", seq = 31, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getEquipmentMonthAnalyse(MonthParamDto monthParamDto) {
        log.info("设备二级分析-月报:{}",JSONObject.toJSON(monthParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(monthParamDto.getMonth() == null || monthParamDto.getMonth().trim().isEmpty()){
            JSONObject monthWeek = TimeUtil.getDateMonthWeek(new Date());
            monthParamDto.setMonth(monthWeek.getString("month"));
        }


        try{
            Date day = msdf.parse(monthParamDto.getMonth());
        }catch (Exception e){
            return error(500,"月份格式不正确，正确的日期格式为：yyyy-MM");
        }
        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(monthParamDto);
        monthParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        JSONObject result = equipmentDataService.getMonthEquipmentData(monthParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/yearReport/equipment")
    @AuthorityResource(name = "设备二级分析-日报", value = "mb_mobile_report_year_equipment_analyse", seq = 31, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getEquipmentYearAnalyse(YearParamDto yearParamDto) {
        log.info("设备二级分析-年报:{}",JSONObject.toJSON(yearParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(yearParamDto.getYear() == null || yearParamDto.getYear() < 1000){
            return error(500,"年份不能为空或者错误");
        }

        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(yearParamDto);
        yearParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        JSONObject result = equipmentDataService.getYearEquipmentData(yearParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            filterYearUp(result.getJSONObject("info"));
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/dayReport/equipment/sum")
    @AuthorityResource(name = "设备二级分析-日报", value = "mb_mobile_report_day_equipment_sum_analyse", seq = 31, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getDaySumEquipmentAnalyse(DayParamDto dayParamDto) {
        log.info("设备二级分析-日报:{}",JSONObject.toJSON(dayParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(dayParamDto.getDay() == null || dayParamDto.getDay().trim().isEmpty()){
            return error(500,"请求日期不能为空");
        }

        /*if(dayParamDto.getMerchantId() == null || dayParamDto.getMerchantId() <=0){
            return error(500,"商户id不能为空");
        }*/
        try{
            Date day = sdf.parse(dayParamDto.getDay());
        }catch (Exception e){
            return error(500,"日期格式不正确，正确的日期格式为：yyyy-MM-dd");
        }
        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(dayParamDto);
        dayParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        JSONObject result = equipmentDataService.getDaySumEquipmentData(dayParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }


    @GetMapping("/mobile/weekReport/equipment/sum")
    @AuthorityResource(name = "设备二级分析-日报", value = "mb_mobile_report_week_equipment_sum_analyse", seq = 31, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getWeekSumEquipmentWeekAnalyse(WeekParamDto weekParamDto) {
        log.info("设备二级分析-周报:{}",JSONObject.toJSON(weekParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(weekParamDto.getMonth() == null || weekParamDto.getMonth().trim().isEmpty()){
            JSONObject monthWeek = TimeUtil.getDateMonthWeek(new Date());
            weekParamDto.setMonth(monthWeek.getString("month"));
            weekParamDto.setWeek(monthWeek.getInteger("week"));
        }

        if(weekParamDto.getWeek() == null || weekParamDto.getWeek() <1){
            return error(500,"周数不能为空");
        }

        try{
            Date day = msdf.parse(weekParamDto.getMonth());
        }catch (Exception e){
            return error(500,"月份格式不正确，正确的日期格式为：yyyy-MM");
        }
        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(weekParamDto);
        weekParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        JSONObject result = equipmentDataService.getWeekSumEquipmentData(weekParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/monthReport/equipment/sum")
    @AuthorityResource(name = "设备二级分析-日报", value = "mb_mobile_report_month_equipment_sum_analyse", seq = 31, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getMonthSumEquipmentMonthAnalyse(MonthParamDto monthParamDto) {
        log.info("设备二级分析-月报:{}",JSONObject.toJSON(monthParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(monthParamDto.getMonth() == null || monthParamDto.getMonth().trim().isEmpty()){
            JSONObject monthWeek = TimeUtil.getDateMonthWeek(new Date());
            monthParamDto.setMonth(monthWeek.getString("month"));
        }


        try{
            Date day = msdf.parse(monthParamDto.getMonth());
        }catch (Exception e){
            return error(500,"月份格式不正确，正确的日期格式为：yyyy-MM");
        }
        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(monthParamDto);
        monthParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        JSONObject result = equipmentDataService.getMonthSumEquipmentData(monthParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/yearReport/equipment/sum")
    @AuthorityResource(name = "设备二级分析-日报", value = "mb_mobile_report_year_equipment_sum_analyse", seq = 31, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getYearSumEquipmentYearAnalyse(YearParamDto yearParamDto) {
        log.info("设备二级分析-年报:{}",JSONObject.toJSON(yearParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(yearParamDto.getYear() == null || yearParamDto.getYear() < 1000){
            return error(500,"年份不能为空或者错误");
        }

        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(yearParamDto);
        yearParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        JSONObject result = equipmentDataService.getYearSumEquipmentData(yearParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            filterYearUp(result.getJSONObject("info"));
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/dayReport/group/sum")
    @AuthorityResource(name = "设备二级分析-日报", value = "mb_mobile_report_day_group_sum_analyse", seq = 31, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getDaySumGroupAnalyse(DayParamDto dayParamDto) {
        log.info("设备二级分析-日报:{}",JSONObject.toJSON(dayParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(dayParamDto.getDay() == null || dayParamDto.getDay().trim().isEmpty()){
            return error(500,"请求日期不能为空");
        }

        /*if(dayParamDto.getMerchantId() == null || dayParamDto.getMerchantId() <=0){
            return error(500,"商户id不能为空");
        }*/
        try{
            Date day = sdf.parse(dayParamDto.getDay());
        }catch (Exception e){
            return error(500,"日期格式不正确，正确的日期格式为：yyyy-MM-dd");
        }
        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(dayParamDto);
        dayParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        JSONObject result = equipmentGroupService.getDaySumGroupData(dayParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/weekReport/group/sum")
    @AuthorityResource(name = "场地二级分析-日报", value = "mb_mobile_report_week_group_sum_analyse", seq = 31, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getWeekSumGroupWeekAnalyse(WeekParamDto weekParamDto) {
        log.info("场地二级分析-周报:{}",JSONObject.toJSON(weekParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(weekParamDto.getMonth() == null || weekParamDto.getMonth().trim().isEmpty()){
            JSONObject monthWeek = TimeUtil.getDateMonthWeek(new Date());
            weekParamDto.setMonth(monthWeek.getString("month"));
            weekParamDto.setWeek(monthWeek.getInteger("week"));
        }

        if(weekParamDto.getWeek() == null || weekParamDto.getWeek() <1){
            return error(500,"周数不能为空");
        }

        try{
            Date day = msdf.parse(weekParamDto.getMonth());
        }catch (Exception e){
            return error(500,"月份格式不正确，正确的日期格式为：yyyy-MM");
        }
        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(weekParamDto);
        weekParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        JSONObject result = equipmentGroupService.getWeekSumGroupData(weekParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/monthReport/group/sum")
    @AuthorityResource(name = "场地二级分析-日报", value = "mb_mobile_report_month_group_sum_analyse", seq = 31, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getMonthSumGroupMonthAnalyse(MonthParamDto monthParamDto) {
        log.info("场地二级分析-月报:{}",JSONObject.toJSON(monthParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(monthParamDto.getMonth() == null || monthParamDto.getMonth().trim().isEmpty()){
            JSONObject monthWeek = TimeUtil.getDateMonthWeek(new Date());
            monthParamDto.setMonth(monthWeek.getString("month"));
        }

        try{
            Date day = msdf.parse(monthParamDto.getMonth());
        }catch (Exception e){
            return error(500,"月份格式不正确，正确的日期格式为：yyyy-MM");
        }
        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(monthParamDto);
        monthParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        JSONObject result = equipmentGroupService.getMonthSumGroupData(monthParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/yearReport/group/sum")
    @AuthorityResource(name = "场地二级分析-日报", value = "mb_mobile_report_year_group_sum_analyse", seq = 31, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getYearSumGroupYearAnalyse(YearParamDto yearParamDto) {
        log.info("场地二级分析-年报:{}",JSONObject.toJSON(yearParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(yearParamDto.getYear() == null || yearParamDto.getYear() < 1000){
            return error(500,"年份不能为空或者错误");
        }

        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(yearParamDto);
        yearParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        JSONObject result = equipmentGroupService.getYearSumGroupData(yearParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            filterYearUp(result.getJSONObject("info"));
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    /**
     * 设备类型报表
     * @param dayParamDto
     * @return
     */
    @GetMapping("/mobile/dayReport/equipmentType")
    @AuthorityResource(name = "设备类型-日报", value = "mb_mobile_report_dayReport_equipmentType_analyse", seq = 31, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getDayReportEquipmentTypeAnalyse(DayParamDto dayParamDto) {
        log.info("场地二级分析-年报:{}",JSONObject.toJSON(dayParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(dayParamDto.getDay() == null || dayParamDto.getDay().trim().isEmpty()){
            return error(500,"请求日期不能为空");
        }

        try{
            Date day = sdf.parse(dayParamDto.getDay());
        }catch (Exception e){
            return error(500,"日期格式不正确，正确的日期格式为：yyyy-MM-dd");
        }

        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(dayParamDto);
        dayParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        JSONObject result = equipmentTypeDataService.getDayEquipmentTypeData(dayParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    /**
     * 设备类型报表
     * @param weekParamDto
     * @return
     */
    @GetMapping("/mobile/weekReport/equipmentType")
    @AuthorityResource(name = "设备类型-日报", value = "mb_mobile_report_weekReport_equipmentType_analyse", seq = 31, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getWeekReportEquipmentTypeAnalyse(WeekParamDto weekParamDto) {
        log.info("场地二级分析-年报:{}",JSONObject.toJSON(weekParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(weekParamDto.getMonth() == null || weekParamDto.getMonth().trim().isEmpty()){
            JSONObject monthWeek = TimeUtil.getDateMonthWeek(new Date());
            weekParamDto.setMonth(monthWeek.getString("month"));
            weekParamDto.setWeek(monthWeek.getInteger("week"));
        }

        if(weekParamDto.getWeek() == null || weekParamDto.getWeek() <1){
            return error(500,"周数不能为空");
        }

        try{
            Date day = msdf.parse(weekParamDto.getMonth());
        }catch (Exception e){
            return error(500,"月份格式不正确，正确的日期格式为：yyyy-MM");
        }

        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(weekParamDto);
        weekParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        JSONObject result = equipmentTypeDataService.getWeekEquipmentTypeData(weekParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    /**
     * 设备类型报表
     * @param monthParamDto
     * @return
     */
    @GetMapping("/mobile/monthReport/equipmentType")
    @AuthorityResource(name = "设备类型-日报", value = "mb_mobile_report_monthReport_equipmentType_analyse", seq = 31, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getMonthReportEquipmentTypeAnalyse(MonthParamDto monthParamDto) {
        log.info("场地二级分析-年报:{}",JSONObject.toJSON(monthParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(monthParamDto.getMonth() == null || monthParamDto.getMonth().trim().isEmpty()){
            JSONObject monthWeek = TimeUtil.getDateMonthWeek(new Date());
            monthParamDto.setMonth(monthWeek.getString("month"));
        }


        try{
            Date day = msdf.parse(monthParamDto.getMonth());
        }catch (Exception e){
            return error(500,"月份格式不正确，正确的日期格式为：yyyy-MM");
        }

        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(monthParamDto);
        monthParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        JSONObject result = equipmentTypeDataService.getMonthEquipmentTypeData(monthParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    /**
     * 设备类型报表
     * @param yearParamDto
     * @return
     */
    @GetMapping("/mobile/yearReport/equipmentType")
    @AuthorityResource(name = "设备类型-日报", value = "mb_mobile_report_yearReport_equipmentType_analyse", seq = 31, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getYearReportEquipmentTypeAnalyse(YearParamDto yearParamDto) {
        log.info("场地二级分析-年报:{}",JSONObject.toJSON(yearParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(yearParamDto.getYear() == null || yearParamDto.getYear() < 1000){
            return error(500,"年份不能为空或者错误");
        }

        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(yearParamDto);
        yearParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        JSONObject result = equipmentTypeDataService.getYearEquipmentTypeData(yearParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            filterYearUp(result.getJSONObject("info"));
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/dayReport/everyDay")
    @AuthorityResource(name = "设备二级分析-日报", value = "mb_mobile_report_day_everyDay_analyse", seq = 31, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getEveryDayAnalyse(DayParamDto dayParamDto) {
        log.info("设备二级分析-日报:{}",JSONObject.toJSON(dayParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(dayParamDto.getMonth() == null || dayParamDto.getMonth().trim().isEmpty()){
            return error(500,"请求日期不能为空");
        }


        getGroupId(dayParamDto);
        dayParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        JSONObject result = orderDataService.getEveryDayData(dayParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/weekReport/everyWeek")
    @AuthorityResource(name = "设备二级分析-日报", value = "mb_mobile_report_week_everyWeek_analyse", seq = 31, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getEveryWeekAnalyse(WeekParamDto weekParamDto) {
        log.info("设备二级分析-日报:{}",JSONObject.toJSON(weekParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(weekParamDto.getMonth() == null || weekParamDto.getMonth().trim().isEmpty()){
            JSONObject monthWeek = TimeUtil.getDateMonthWeek(new Date());
            weekParamDto.setMonth(monthWeek.getString("month"));
        }


        try{
            Date day = msdf.parse(weekParamDto.getMonth());
        }catch (Exception e){
            return error(500,"月份格式不正确，正确的日期格式为：yyyy-MM");
        }

        getGroupId(weekParamDto);
        weekParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        JSONObject result = orderDataService.getEveryWeekData(weekParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/monthReport/everyMonth")
    @AuthorityResource(name = "设备二级分析-日报", value = "mb_mobile_report_month_everyMonth_analyse", seq = 31, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getEveryMonthAnalyse(MonthParamDto monthParamDto) {
        log.info("设备二级分析-日报:{}",JSONObject.toJSON(monthParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        if(monthParamDto.getYear() == null || monthParamDto.getYear() < 1000){
            return error(500,"年份不能为空或者错误");
        }

        getGroupId(monthParamDto);
        monthParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        JSONObject result = orderDataService.getEveryMonthData(monthParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/yearReport/everyYear")
    @AuthorityResource(name = "设备二级分析-日报", value = "mb_mobile_report_year_everyYear_analyse", seq = 31, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getEveryYearAnalyse(YearParamDto yearParamDto) {
        log.info("设备二级分析-日报:{}",JSONObject.toJSON(yearParamDto).toString());
        if(!isWith(stableWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        //每年

        getGroupId(yearParamDto);
        yearParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        JSONObject result = orderDataService.getEveryYearData(yearParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    private void getGroupId(Object param){

        //统一处理有选择场地，则维度为场地维度
        if(param instanceof DayParamDto){
            if(((DayParamDto) param).getGroupId() != null && ((DayParamDto) param).getGroupId() > 0){
                ((DayParamDto) param).setComboType("商家+省份+城市+区域+场地维度");
            }
        }

        if(param instanceof WeekParamDto){
            if(((WeekParamDto) param).getGroupId() != null &&  ((WeekParamDto) param).getGroupId() > 0){
                ((WeekParamDto) param).setComboType("商家+省份+城市+区域+场地维度");
            }
        }

        if(param instanceof MonthParamDto){
            if(((MonthParamDto) param).getGroupId() != null &&  ((MonthParamDto) param).getGroupId() > 0){
                ((MonthParamDto) param).setComboType("商家+省份+城市+区域+场地维度");
            }
        }

        if(param instanceof YearParamDto){
            if(((YearParamDto) param).getGroupId() != null &&  ((YearParamDto) param).getGroupId() > 0){
                ((YearParamDto) param).setComboType("商家+省份+城市+区域+场地维度");
            }
        }

        if(getCurrentUser().getIsApprover()){
            return;
        }
        List<MerchantGroupListDTO> list = equipmentGroupBusinessService.getGroupInfoDistributorId(getAdOrgIdNotNull(), getAdUserIdNotNull(),
                false,"", null);
        if(list != null ){
            Set<String> groupIds = new TreeSet<>();
            for(MerchantGroupListDTO merchantGroupListDTO : list){
                if(merchantGroupListDTO != null && merchantGroupListDTO.getGroups() !=null && merchantGroupListDTO.getGroups().size()>0){
                    for(MerchantGroupDTO merchantGroupDTO: merchantGroupListDTO.getGroups()){
                        if(merchantGroupDTO != null && "Y".equals(merchantGroupDTO.getIsactive())){
                            groupIds.add(merchantGroupDTO.getEquipmentGroupId()+"");
                        }
                    }
                }
            }
            if(groupIds.size()>0){
                if(param instanceof DayParamDto){
                    ((DayParamDto) param).setGroupIds(groupIds);
                }

                if(param instanceof WeekParamDto){
                    ((WeekParamDto) param).setGroupIds(groupIds);
                }

                if(param instanceof MonthParamDto){
                    ((MonthParamDto) param).setGroupIds(groupIds);
                }

                if(param instanceof YearParamDto){
                    ((YearParamDto) param).setGroupIds(groupIds);
                }
            }
        }



    }

    private void filterYearUp(JSONObject obj){
        if(obj.getJSONArray("list") != null && obj.getJSONArray("list").size()>0){
            JSONArray data = obj.getJSONArray("list");
            for(int i=0; i< data.size(); i++){
                JSONObject item = data.getJSONObject(i);
                Set<String> keys = item.keySet();
                if(keys != null && keys.size()>0){
                    for(String key : keys){
                        if(key.endsWith("Up")){
                            item.put(key,"0");
                        }
                    }
                }
            }
        }

    }

    private boolean isWith(int targetWith){
    	log.info("targetWith>>"+targetWith);
        BaseResponse<List<Integer>> witheList = merchantWhiteClient.getAllWhiteByDistributorId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull():merchantTransMap.get(getAdOrgIdNotNull())));
        if(witheList.getCode() == 0){
            log.info("商家：{}，白名单：{}", getAdOrgIdNotNull(),witheList.getData());
            List<Integer> withes = witheList.getData();
            if(withes != null && withes.size()>0){
                for(Integer with : withes){
                    if(with.intValue() == targetWith){
                        return true;
                    }
                }
            }
        }
        return false;
//    	return true ;
    }
    
//    protected Long getAdOrgIdNotNull() {
//    	return 1268871L;
//    }
    
}
