package cn.lyy.merchant.controller.onedata;

import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.merchant.api.service.MerchantWhiteClient;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.controller.onedata.dto.DayParamDto;
import cn.lyy.merchant.controller.onedata.dto.MonthParamDto;
import cn.lyy.merchant.controller.onedata.dto.WeekParamDto;
import cn.lyy.merchant.controller.onedata.dto.YearParamDto;
import cn.lyy.merchant.controller.onedata.util.TimeUtil;
import cn.lyy.merchant.dto.merchant.response.MerchantGroupDTO;
import cn.lyy.merchant.dto.response.MerchantGroupListDTO;
import cn.lyy.merchant.service.IEquipmentGroupBusinessService;
import cn.lyy.merchant.service.onedata.OneDataRealTimeService;
import cn.lyy.merchant.service.onedata.mobile.EquipmentGroupDataService;
import cn.lyy.merchant.service.onedata.mobile.MemberBenefitDataService;
import cn.lyy.merchant.service.onedata.mobile.OrderDataService;
import lombok.extern.slf4j.Slf4j;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.internal.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 新B端首页-数据看板接口
 * <AUTHOR>
 * @date 2024-03-04
 *
 */
@Slf4j
@RestController
@RequestMapping("/oneData")
public class MerchantReportController extends BaseController {

    @Autowired
    private IEquipmentGroupBusinessService equipmentGroupBusinessService;

    @Autowired
    private MerchantWhiteClient merchantWhiteClient;
    
    @Autowired
    private OrderDataService orderDataService ;
    
    @Autowired
    private EquipmentGroupDataService equipmentGroupDataService ;
    
    @Autowired
    private MemberBenefitDataService memberBenefitDataService ;
    
    @Autowired
    private OneDataRealTimeService oneDataRealTimeService;

    @Value("#{${oneData.merchantTrans}}")
    private Map<Long,Long> merchantTransMap;
    
    @Value("${oneData.merchantReportWhiteListCode:1002}")
    private int merchantReportWhiteListCode ;

    private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

    @GetMapping("/merchant/common/isReporter")
    @AuthorityResource(name = "新B端首页-数据看板-白名单-基础数据", value = "mb_merchant_common_report_isreporter", seq = 12, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getIsReporter(DayParamDto dayParamDto) {
        log.info("移动端报表白名单-基础数据:{}",JSONObject.toJSON(dayParamDto).toString());
        log.info("商家id：{}",getAdOrgIdNotNull());
        if(isWith(merchantReportWhiteListCode)){
            return success(true);
        }
        return success(false);
    }
    
    @GetMapping("/merchant/common/realTimeReport")
    @AuthorityResource(name = "新B端-数据看板-实时", value = "mb_merchant_common_report_realtime", seq = 1, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getReportData(DayParamDto dayParamDto) {
    	BaseResponse result = null ;
    	
    	if(!isWith(merchantReportWhiteListCode)){
             return error(500,"不在使用范围内");
        }
    	dayParamDto.setDay(sdf.format(new Date()));
        getGroupId(dayParamDto,Boolean.TRUE);

        dayParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        //在线收入
        JSONObject orderResult = oneDataRealTimeService.getOrderData(dayParamDto) ;
        
//        //今日会员
//        JSONObject memberResult = oneDataRealTimeService.getMemberData(dayParamDto);
//        
//        //笔单价
//        JSONObject priceCountResult = oneDataRealTimeService.getPerOrderPriceCount(dayParamDto);
//        
//        //时段趋势
//        JSONObject timeRangeResult = oneDataRealTimeService.getTimeRangeData(dayParamDto);
        
        JSONObject data = new JSONObject() ;
        JSONObject orderDataJSON = new JSONObject() ;
        if(orderResult !=null && orderResult.containsKey("code") && "200".equals(orderResult.getString("code"))){
            if(orderResult.getJSONObject("info").getJSONArray("list") != null && orderResult.getJSONObject("info").getJSONArray("list").size()>0){
            	orderDataJSON = orderResult.getJSONObject("info").getJSONArray("list").getJSONObject(0) ;
            }
        }else{
            return error(500,result == null ? "请求出错":orderResult.getString("msg"));
        }

        if(orderDataJSON !=null) {
        	Map<String,Object> map = new HashMap<String,Object>() ;
        	
        	map.put("onlineAmount", orderDataJSON.getOrDefault("onlineAmount",0)) ;
        	map.put("onlineAmountUp", orderDataJSON.getOrDefault("onlineAmountUp",0)) ;
        	map.put("onlinePayAmount", orderDataJSON.getOrDefault("onlinePayAmount",0)) ;
        	map.put("onlinePayAmountUp", orderDataJSON.getOrDefault("onlinePayAmountUp",0)) ;
        	map.put("onlinePayAmount", orderDataJSON.getOrDefault("onlinePayAmount",0)) ;
        	map.put("onlinePayAmountUp", orderDataJSON.getOrDefault("onlinePayAmountUp",0)) ;
        	map.put("onlinePayCounts", orderDataJSON.getOrDefault("onlinePayCounts",0)) ;
        	map.put("onlinePayCountsUp", orderDataJSON.getOrDefault("onlinePayCountsUp",0)) ;
        	map.put("onlineRefundAmount", orderDataJSON.getOrDefault("onlineRefundAmount", 0)) ;
        	map.put("onlineRefundAmountUp", orderDataJSON.getOrDefault("onlineRefundAmountUp", 0)) ;
        	map.put("onlineRefundCounts", orderDataJSON.getOrDefault("onlineRefundCounts",0)) ;
        	map.put("onlineRefundCountsUp", orderDataJSON.getOrDefault("onlineRefundCountsUp",0)) ;
        	
        	map.put("cashAmount", orderDataJSON.getOrDefault("cashAmount",0)) ;
        	map.put("cashAmountUp", orderDataJSON.getOrDefault("cashAmountUp",0)) ;
        	map.put("cashPayAmount", orderDataJSON.getOrDefault("cashPayAmount",0)) ;
        	map.put("cashPayAmountUp", orderDataJSON.getOrDefault("cashPayAmountUp",0)) ;
        	map.put("cashPayCounts", orderDataJSON.getOrDefault("cashPayCounts",0)) ;
        	map.put("cashPayCountsUp", orderDataJSON.getOrDefault("cashPayCountsUp",0)) ;
        	map.put("cashRefundAmount", orderDataJSON.getOrDefault("cashRefundAmount",0)) ;
        	map.put("cashRefundAmountUp", orderDataJSON.getOrDefault("cashRefundAmountUp",0)) ;
        	map.put("cashRefundCounts", orderDataJSON.getOrDefault("cashRefundCounts",0)) ;
        	map.put("cashRefundCountsUp", orderDataJSON.getOrDefault("cashRefundCountsUp",0)) ;
        	
        	data.put("order", map) ;
        	data.put("totalCount", 1) ;
            data.put("totalPage", 1) ;
        }
        
        result = success(data) ;
        return result ;
    }
    
    @GetMapping("/merchant/common/dayReport")
    @AuthorityResource(name = "新B端-数据看板-天指标", value = "mb_merchant_common_report_day", seq = 1, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getCommonDataDayReport(DayParamDto dayParamDto) {
        BaseResponse result = null;
        if(!isWith(merchantReportWhiteListCode)){
            return error(500,"不在使用范围内");
       }
        if(dayParamDto.getDay() == null || dayParamDto.getDay().trim().isEmpty()){
            return error(500,"请求日期不能为空");
        }

        try{
            @SuppressWarnings("unused")
			Date day = sdf.parse(dayParamDto.getDay());
        }catch (Exception e){
            return error(500,"日期格式不正确，正确的日期格式为：yyyy-MM-dd");
        }
        getGroupId(dayParamDto,Boolean.FALSE);

        dayParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        //订单收入指标
        JSONObject orderResult = orderDataService.getOrderData(dayParamDto);
        
        //设备指标
        JSONObject groupEquipmentResult = equipmentGroupDataService.getGroupEquipmentData(dayParamDto);
        
        //储值指标-余额
        dayParamDto.setBenefitType("余额") ;
        JSONObject benefitBalanceResult = memberBenefitDataService.getMemberBenefitData(dayParamDto) ;
        
        //储值指标-余币
        dayParamDto.setBenefitType("余币") ;
        JSONObject benefitSurplusResult = memberBenefitDataService.getMemberBenefitData(dayParamDto) ;
        
        JSONObject data = new JSONObject();
        JSONObject orderDataJSON = new JSONObject() ;
        JSONObject groupEquipmentDataJSON = new JSONObject() ;
        JSONObject benefitBalanceDataJSON = new JSONObject()  ;//余额
        JSONObject benefitSurplusCoinsDataJSON = new JSONObject()  ;//余币
        if(orderResult !=null && orderResult.containsKey("code") && "200".equals(orderResult.getString("code"))){
            if(orderResult.getJSONObject("info").getJSONArray("list") != null && orderResult.getJSONObject("info").getJSONArray("list").size()>0){
            	orderDataJSON = orderResult.getJSONObject("info").getJSONArray("list").getJSONObject(0) ;
            }
        }else{
            return error(500,result == null ? "请求出错":orderResult.getString("msg"));
        }
        
        if(groupEquipmentResult !=null && groupEquipmentResult.containsKey("code") && "200".equals(groupEquipmentResult.getString("code"))){
            if(groupEquipmentResult.getJSONObject("info").getJSONArray("list") != null && groupEquipmentResult.getJSONObject("info").getJSONArray("list").size()>0){
            	groupEquipmentDataJSON = groupEquipmentResult.getJSONObject("info").getJSONArray("list").getJSONObject(0) ;
            } 
        }

        //余额
        if(benefitBalanceResult !=null && benefitBalanceResult.containsKey("code") && "200".equals(benefitBalanceResult.getString("code"))){
            if(benefitBalanceResult.getJSONObject("info").getJSONArray("list") != null && benefitBalanceResult.getJSONObject("info").getJSONArray("list").size()>0){
            	benefitBalanceDataJSON = benefitBalanceResult.getJSONObject("info").getJSONArray("list").getJSONObject(0) ;
            } 
        }
        
        //余币
        if(benefitSurplusResult !=null && benefitSurplusResult.containsKey("code") && "200".equals(benefitSurplusResult.getString("code"))){
            if(benefitSurplusResult.getJSONObject("info").getJSONArray("list") != null && benefitSurplusResult.getJSONObject("info").getJSONArray("list").size()>0){
            	benefitSurplusCoinsDataJSON = benefitSurplusResult.getJSONObject("info").getJSONArray("list").getJSONObject(0) ;
            } 
        }
       
        data = compactResult(data,orderDataJSON,groupEquipmentDataJSON,benefitBalanceDataJSON,benefitSurplusCoinsDataJSON) ;
        result = success(data) ;
        
        return result;
    }
    
    @GetMapping("/merchant/common/weekReport")
    @AuthorityResource(name = "新B端-数据看板-周指标", value = "mb_merchant_common_report_week", seq = 1, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getCommonDataWeekReport(WeekParamDto weekParamDto,String day) {
        BaseResponse result = null;
        
        if(!isWith(merchantReportWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        
        if(StringUtils.isEmpty(day)){
            return error(500,"请求日期不能为空");
        }

        try{
        	//前端传过来的day一定是周一,TimeUtil.getDateMonthWeek计算week的逻辑有问题
			Date parseDay = sdf.parse(day);
			java.util.Date nextDay = cn.hutool.core.date.DateUtil.offsetDay(parseDay,1);
            JSONObject monthWeek = TimeUtil.getDateMonthWeek(nextDay);
            weekParamDto.setMonth(monthWeek.getString("month"));
            weekParamDto.setWeek(monthWeek.getInteger("week"));
        }catch (Exception e){
            return error(500,"日期格式不正确，正确的日期格式为：yyyy-MM-dd");
        }
        
        getGroupId(weekParamDto,Boolean.FALSE);
        weekParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        //订单收入指标
        JSONObject orderResult = orderDataService.getWeekOrderData(weekParamDto);
        
        //设备指标
        JSONObject groupEquipmentResult = equipmentGroupDataService.getWeekGroupEquipmentData(weekParamDto);
        
        //储值指标-余额
        weekParamDto.setBenefitType("余额") ;
        JSONObject benefitBalanceResult = memberBenefitDataService.getWeekMemberBenefitData(weekParamDto) ;
        
        //储值指标-余币
        weekParamDto.setBenefitType("余币") ;
        JSONObject benefitSurplusResult = memberBenefitDataService.getWeekMemberBenefitData(weekParamDto) ;
        
        JSONObject data = new JSONObject();
        JSONObject orderDataJSON = new JSONObject() ;
        JSONObject groupEquipmentDataJSON = new JSONObject() ;
        JSONObject benefitBalanceDataJSON = new JSONObject()  ;//余额
        JSONObject benefitSurplusCoinsDataJSON = new JSONObject()  ;//余币
        if(orderResult !=null && orderResult.containsKey("code") && "200".equals(orderResult.getString("code"))){
            if(orderResult.getJSONObject("info").getJSONArray("list") != null && orderResult.getJSONObject("info").getJSONArray("list").size()>0){
            	orderDataJSON = orderResult.getJSONObject("info").getJSONArray("list").getJSONObject(0) ;
            }
        }else{
            return error(500,result == null ? "请求出错":orderResult.getString("msg"));
        }

        if(groupEquipmentResult !=null && groupEquipmentResult.containsKey("code") && "200".equals(groupEquipmentResult.getString("code"))){
            if(groupEquipmentResult.getJSONObject("info").getJSONArray("list") != null && groupEquipmentResult.getJSONObject("info").getJSONArray("list").size()>0){
            	groupEquipmentDataJSON = groupEquipmentResult.getJSONObject("info").getJSONArray("list").getJSONObject(0) ;
            } 
        }

        //余额
        if(benefitBalanceResult !=null && benefitBalanceResult.containsKey("code") && "200".equals(benefitBalanceResult.getString("code"))){
            if(benefitBalanceResult.getJSONObject("info").getJSONArray("list") != null && benefitBalanceResult.getJSONObject("info").getJSONArray("list").size()>0){
            	benefitBalanceDataJSON = benefitBalanceResult.getJSONObject("info").getJSONArray("list").getJSONObject(0) ;
            } 
        }
        
        //余币
        if(benefitSurplusResult !=null && benefitSurplusResult.containsKey("code") && "200".equals(benefitSurplusResult.getString("code"))){
            if(benefitSurplusResult.getJSONObject("info").getJSONArray("list") != null && benefitSurplusResult.getJSONObject("info").getJSONArray("list").size()>0){
            	benefitSurplusCoinsDataJSON = benefitSurplusResult.getJSONObject("info").getJSONArray("list").getJSONObject(0) ;
            } 
        }
       
        data = compactResult(data,orderDataJSON,groupEquipmentDataJSON,benefitBalanceDataJSON,benefitSurplusCoinsDataJSON) ;
        result = success(data) ;
        
        return result;
    }
    
    @GetMapping("/merchant/common/monthReport")
    @AuthorityResource(name = "新B端-数据看板-月指标", value = "mb_merchant_common_report_month", seq = 1, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getCommonDataMonthReport(MonthParamDto monthParamDto,String day) {
        BaseResponse result = null;
        if(!isWith(merchantReportWhiteListCode)){
            return error(500,"不在使用范围内");
        }
        
        if(StringUtils.isEmpty(day)){
            return error(500,"请求日期不能为空");
        }

        try{
			Date parseDay = sdf.parse(day);
//            JSONObject monthWeek = TimeUtil.getDateMonthWeek(parseDay);
//            monthParamDto.setMonth(monthWeek.getString("month"));
			String month = sdf.format(parseDay).substring(0,7) ;
            monthParamDto.setMonth(month);
        }catch (Exception e){
            return error(500,"日期格式不正确，正确的日期格式为：yyyy-MM-dd");
        }
       
        getGroupId(monthParamDto,Boolean.FALSE);

        monthParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));

        //订单收入指标
        JSONObject orderResult = orderDataService.getMonthOrderData(monthParamDto);
        
        //设备指标
        JSONObject groupEquipmentResult = equipmentGroupDataService.getMonthGroupEquipmentData(monthParamDto);
        
        //储值指标-余额
        monthParamDto.setBenefitType("余额") ;
        JSONObject benefitBalanceResult = memberBenefitDataService.getMonthMemberBenefitData(monthParamDto) ;
        
        //储值指标-余币
        monthParamDto.setBenefitType("余币") ;
        JSONObject benefitSurplusResult = memberBenefitDataService.getMonthMemberBenefitData(monthParamDto) ;
        
        JSONObject data = new JSONObject();
        JSONObject orderDataJSON = new JSONObject() ;
        JSONObject groupEquipmentDataJSON = new JSONObject() ;
        JSONObject benefitBalanceDataJSON = new JSONObject()  ;//余额
        JSONObject benefitSurplusCoinsDataJSON = new JSONObject()  ;//余币
        if(orderResult !=null && orderResult.containsKey("code") && "200".equals(orderResult.getString("code"))){
            if(orderResult.getJSONObject("info").getJSONArray("list") != null && orderResult.getJSONObject("info").getJSONArray("list").size()>0){
            	orderDataJSON = orderResult.getJSONObject("info").getJSONArray("list").getJSONObject(0) ;
            }
        }else{
            return error(500,result == null ? "请求出错":orderResult.getString("msg"));
        }

        if(groupEquipmentResult !=null && groupEquipmentResult.containsKey("code") && "200".equals(groupEquipmentResult.getString("code"))){
            if(groupEquipmentResult.getJSONObject("info").getJSONArray("list") != null && groupEquipmentResult.getJSONObject("info").getJSONArray("list").size()>0){
            	groupEquipmentDataJSON = groupEquipmentResult.getJSONObject("info").getJSONArray("list").getJSONObject(0) ;
            } 
        }

        //余额
        if(benefitBalanceResult !=null && benefitBalanceResult.containsKey("code") && "200".equals(benefitBalanceResult.getString("code"))){
            if(benefitBalanceResult.getJSONObject("info").getJSONArray("list") != null && benefitBalanceResult.getJSONObject("info").getJSONArray("list").size()>0){
            	benefitBalanceDataJSON = benefitBalanceResult.getJSONObject("info").getJSONArray("list").getJSONObject(0) ;
            } 
        }
        
        //余币
        if(benefitSurplusResult !=null && benefitSurplusResult.containsKey("code") && "200".equals(benefitSurplusResult.getString("code"))){
            if(benefitSurplusResult.getJSONObject("info").getJSONArray("list") != null && benefitSurplusResult.getJSONObject("info").getJSONArray("list").size()>0){
            	benefitSurplusCoinsDataJSON = benefitSurplusResult.getJSONObject("info").getJSONArray("list").getJSONObject(0) ;
            } 
        }
       
        data = compactResult(data,orderDataJSON,groupEquipmentDataJSON,benefitBalanceDataJSON,benefitSurplusCoinsDataJSON) ;
        result = success(data) ;
        
        return result;
    }
 
    /**
     * 聚合指标结果数据
     * @param data
     * @param orderDataJSON				订单收入指标数据
     * @param groupEquipmentDataJSON	场地设备指标数据	
     * @param benefitBalanceDataJSON	储值余额指标数据
     * @param benefitSurplusCoinsDataJSON 储值余币指标数据
     * @return
     */
    private JSONObject compactResult(JSONObject data,JSONObject orderDataJSON,JSONObject groupEquipmentDataJSON,JSONObject benefitBalanceDataJSON,JSONObject benefitSurplusCoinsDataJSON) {
    	if(data == null) {
    		data = new JSONObject() ;
    		data.put("totalCount", 0) ;
            data.put("totalPage", 0) ;
    	}
    	if(orderDataJSON !=null) {
        	Map<String,Object> map = new HashMap<String, Object>() ;

        	map.put("onlineAmount", orderDataJSON.getOrDefault("onlineAmount",0)) ;
        	map.put("onlineAmountUp", orderDataJSON.getOrDefault("onlineAmountUp",0)) ;
        	map.put("onlinePayAmount", orderDataJSON.getOrDefault("onlinePayAmount",0)) ;
        	map.put("onlinePayAmountUp", orderDataJSON.getOrDefault("onlinePayAmountUp",0)) ;
        	map.put("onlinePayAmount", orderDataJSON.getOrDefault("onlinePayAmount",0)) ;
        	map.put("onlinePayAmountUp", orderDataJSON.getOrDefault("onlinePayAmountUp",0)) ;
        	map.put("onlinePayCounts", orderDataJSON.getOrDefault("onlinePayCounts",0)) ;
        	map.put("onlinePayCountsUp", orderDataJSON.getOrDefault("onlinePayCountsUp",0)) ;
        	map.put("onlineRefundAmount", orderDataJSON.getOrDefault("onlineRefundAmount", 0)) ;
        	map.put("onlineRefundAmountUp", orderDataJSON.getOrDefault("onlineRefundAmountUp", 0)) ;
        	map.put("onlineRefundCounts", orderDataJSON.getOrDefault("onlineRefundCounts",0)) ;
        	map.put("onlineRefundCountsUp", orderDataJSON.getOrDefault("onlineRefundCountsUp",0)) ;
        	
        	map.put("cashAmount", orderDataJSON.getOrDefault("cashAmount",0)) ;
        	map.put("cashAmountUp", orderDataJSON.getOrDefault("cashAmountUp",0)) ;
        	map.put("cashPayAmount", orderDataJSON.getOrDefault("cashPayAmount",0)) ;
        	map.put("cashPayAmountUp", orderDataJSON.getOrDefault("cashPayAmountUp",0)) ;
        	map.put("cashPayCounts", orderDataJSON.getOrDefault("cashPayCounts",0)) ;
        	map.put("cashPayCountsUp", orderDataJSON.getOrDefault("cashPayCountsUp",0)) ;
        	map.put("cashRefundAmount", orderDataJSON.getOrDefault("cashRefundAmount",0)) ;
        	map.put("cashRefundAmountUp", orderDataJSON.getOrDefault("cashRefundAmountUp",0)) ;
        	map.put("cashRefundCounts", orderDataJSON.getOrDefault("cashRefundCounts",0)) ;
        	map.put("cashRefundCountsUp", orderDataJSON.getOrDefault("cashRefundCountsUp",0)) ;
        	
        	data.put("order", map) ;
        	data.put("totalCount", 1) ;
            data.put("totalPage", 1) ;
        } 
        
        if(groupEquipmentDataJSON != null) {
        	Map<String,Object> map = new HashMap<String, Object>() ;
        	map.put("payEquipments", groupEquipmentDataJSON.getOrDefault("payEquipments", 0)) ;
        	map.put("equipmentCounts",groupEquipmentDataJSON.getOrDefault("equipmentCounts", 0)) ;
        	map.put("onlineEquipmentCounts", groupEquipmentDataJSON.getOrDefault("onlineEquipmentCounts", 0)) ;
        	map.put("perEquipmentOrder", groupEquipmentDataJSON.getOrDefault("perEquipmentOrder", 0)) ;
        	
        	map.put("payEquipmentsUp", groupEquipmentDataJSON.getOrDefault("payEquipmentsUp", 0)) ;
        	map.put("equipmentCountsUp",groupEquipmentDataJSON.getOrDefault("equipmentCountsUp", 0)) ;
        	map.put("onlineEquipmentCountsUp", groupEquipmentDataJSON.getOrDefault("onlineEquipmentCountsUp", 0)) ;
        	map.put("perEquipmentOrderUp", groupEquipmentDataJSON.getOrDefault("perEquipmentOrderUp", 0)) ;
        	
        	data.put("equipment", map) ;
        	data.put("totalCount", 1) ;
            data.put("totalPage", 1) ;
        } 

        if(benefitBalanceDataJSON !=null) {
        	Map<String,Object> map = new HashMap<String, Object>() ;
        	map.put("addAmount", benefitBalanceDataJSON.getOrDefault("addAmount",0)) ;
        	map.put("benefitPayAmount", benefitBalanceDataJSON.getOrDefault("benefitPayAmount",0)) ;
        	map.put("benefitOrders", benefitBalanceDataJSON.getOrDefault("benefitOrders",0)) ;
        	map.put("consumerBenefitOrders", benefitBalanceDataJSON.getOrDefault("consumerBenefitOrders",0)) ;
        	map.put("consumerBenefit", benefitBalanceDataJSON.getOrDefault("consumerBenefit",0)) ;
        	map.put("consumerBenefitUsers", benefitBalanceDataJSON.getOrDefault("consumerBenefitUsers",0)) ;
        	
        	map.put("addAmountUp", benefitBalanceDataJSON.getOrDefault("addAmountUp",0)) ;
        	map.put("benefitPayAmountUp", benefitBalanceDataJSON.getOrDefault("benefitPayAmountUp",0)) ;
        	map.put("benefitOrdersUp", benefitBalanceDataJSON.getOrDefault("benefitOrdersUp",0)) ;
        	map.put("consumerBenefitOrdersUp", benefitBalanceDataJSON.getOrDefault("consumerBenefitOrdersUp",0)) ;
        	map.put("consumerBenefitUp", benefitBalanceDataJSON.getOrDefault("consumerBenefitUp",0)) ;
        	map.put("consumerBenefitUsersUp", benefitBalanceDataJSON.getOrDefault("consumerBenefitUsersUp",0)) ;
        	
        	data.put("benefitBalance", map) ;
        	data.put("totalCount", 1) ;
            data.put("totalPage", 1) ;
        }
        
        if(benefitSurplusCoinsDataJSON !=null) {
        	Map<String,Object> map = new HashMap<String, Object>() ;
        	map.put("addAmount", benefitSurplusCoinsDataJSON.getOrDefault("addAmount",0)) ;
        	map.put("benefitPayAmount", benefitSurplusCoinsDataJSON.getOrDefault("benefitPayAmount",0)) ;
        	map.put("benefitOrders", benefitSurplusCoinsDataJSON.getOrDefault("benefitOrders",0)) ;
        	map.put("consumerBenefitOrders", benefitSurplusCoinsDataJSON.getOrDefault("consumerBenefitOrders",0)) ;
        	map.put("consumerBenefit", benefitSurplusCoinsDataJSON.getOrDefault("consumerBenefit",0)) ;
        	map.put("consumerBenefitUsers", benefitSurplusCoinsDataJSON.getOrDefault("consumerBenefitUsers",0)) ;
        	
        	map.put("addAmountUp", benefitSurplusCoinsDataJSON.getOrDefault("addAmountUp",0)) ;
        	map.put("benefitPayAmountUp", benefitSurplusCoinsDataJSON.getOrDefault("benefitPayAmountUp",0)) ;
        	map.put("benefitOrdersUp", benefitSurplusCoinsDataJSON.getOrDefault("benefitOrdersUp",0)) ;
        	map.put("consumerBenefitOrdersUp", benefitSurplusCoinsDataJSON.getOrDefault("consumerBenefitOrdersUp",0)) ;
        	map.put("consumerBenefitUp", benefitSurplusCoinsDataJSON.getOrDefault("consumerBenefitUp",0)) ;
        	map.put("consumerBenefitUsersUp", benefitSurplusCoinsDataJSON.getOrDefault("consumerBenefitUsersUp",0)) ;
        	
        	data.put("benefitSurplusCoins", map) ;
        	data.put("totalCount", 1) ;
            data.put("totalPage", 1) ;
        }
        
        
        return data ;
    }
    

    private void getGroupId(Object param,boolean isRealTime){
        //统一处理有选择场地，则维度为场地维度
    	if(!isRealTime) {
    		if(param instanceof DayParamDto){
                if(((DayParamDto) param).getGroupId() != null && ((DayParamDto) param).getGroupId() > 0){
                    ((DayParamDto) param).setComboType("商家+省份+城市+区域+场地维度");
                }
            }

            if(param instanceof WeekParamDto ){
                if(((WeekParamDto) param).getGroupId() != null &&  ((WeekParamDto) param).getGroupId() > 0){
                    ((WeekParamDto) param).setComboType("商家+省份+城市+区域+场地维度");
                }
            }

            if(param instanceof MonthParamDto){
                if(((MonthParamDto) param).getGroupId() != null &&  ((MonthParamDto) param).getGroupId() > 0){
                    ((MonthParamDto) param).setComboType("商家+省份+城市+区域+场地维度");
                }
            }

            if(param instanceof YearParamDto){
                if(((YearParamDto) param).getGroupId() != null &&  ((YearParamDto) param).getGroupId() > 0){
                    ((YearParamDto) param).setComboType("商家+省份+城市+区域+场地维度");
                }
            }
        }
        
        if(getCurrentUser().getIsApprover()){
            return;
        }
        List<MerchantGroupListDTO> list = equipmentGroupBusinessService.getGroupInfoDistributorId(getAdOrgIdNotNull(), getAdUserIdNotNull(),
                false,"", null);
        if(list != null ){
            Set<String> groupIds = new TreeSet<>();
            for(MerchantGroupListDTO merchantGroupListDTO : list){
                if(merchantGroupListDTO != null && merchantGroupListDTO.getGroups() !=null && merchantGroupListDTO.getGroups().size()>0){
                    for(MerchantGroupDTO merchantGroupDTO: merchantGroupListDTO.getGroups()){
                        if(merchantGroupDTO != null && "Y".equals(merchantGroupDTO.getIsactive())){
                            groupIds.add(merchantGroupDTO.getEquipmentGroupId()+"");
                        }
                    }
                }
            }
            if(groupIds.size()>0){
                if(param instanceof DayParamDto){
                    ((DayParamDto) param).setGroupIds(groupIds);
                }

                if(param instanceof WeekParamDto){
                    ((WeekParamDto) param).setGroupIds(groupIds);
                }

                if(param instanceof MonthParamDto){
                    ((MonthParamDto) param).setGroupIds(groupIds);
                }

                if(param instanceof YearParamDto){
                    ((YearParamDto) param).setGroupIds(groupIds);
                }
            }
        }

    }

    private boolean isWith(int merchantReportWhiteListCode){
        BaseResponse<List<Integer>> witheList = merchantWhiteClient.getAllWhiteByDistributorId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull():merchantTransMap.get(getAdOrgIdNotNull())));
        if(witheList.getCode() == 0){
            //log.info("商家：{}，白名单：{}", merchant.getId(),witheList.getData());
            List<Integer> withes = witheList.getData();
            if(withes != null && withes.size()>0){
                for(Integer with : withes){
                    if(with.intValue() == merchantReportWhiteListCode){
                        return true;
                    }
                }
            }
        }
        return false;
//    	return true ;
    }
    
//    protected Long getAdOrgIdNotNull() {
//    	return 1036475L ;
//    }

}
