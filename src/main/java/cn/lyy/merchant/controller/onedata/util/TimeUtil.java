package cn.lyy.merchant.controller.onedata.util;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

@Slf4j
public class TimeUtil {

    public static void main(String[] args) throws Exception{
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        Date date = sdf.parse("2023-12");
        System.out.println(getMonthWeekRange(date).toJSONString());

    }

    public static JSONObject getDateMonthWeek(Date date){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar current = Calendar.getInstance();
        current.setTime(date);
        current.add(Calendar.DATE,-1);
        int month = current.get(Calendar.MONTH);
        int year = current.get(Calendar.YEAR);

        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR,year);
        calendar.set(Calendar.DATE,1);
        calendar.set(Calendar.MONTH,month);
        int count =0;
        while (calendar.get(Calendar.MONTH) == month){
            if(calendar.get(Calendar.DAY_OF_WEEK) == Calendar.MONDAY){
                ++count;
                if(count == 1){
                    String str = year+"-";
                    if(month+1 > 9){
                        str += (month+1)+"-";
                    }else{
                        str +="0"+(month+1)+"-";
                    }

                    if(calendar.get(Calendar.DATE) > 9){
                        str += calendar.get(Calendar.DATE);
                    }else{
                        str += "0"+calendar.get(Calendar.DATE);
                    }
                    log.info("本月第一周的时间是：{}",str);

                    //判断当前日期是否大于本月第一周开始时间
                    try{
                       Date firstWeekDate = sdf.parse(str);
                       date = sdf.parse(sdf.format(current.getTime()));
                       if(date.getTime()>=firstWeekDate.getTime()){
                           long day = (1000*60*60*24);
                           log.info("formatDate:{},month:{},week:{}",sdf.format(date),calendar.get(Calendar.YEAR)+"-"+(calendar.get(Calendar.MONTH)+1),((date.getTime()-firstWeekDate.getTime())/day+1)/7+1);
                           JSONObject result = new JSONObject();
                           result.put("month",calendar.get(Calendar.YEAR)+"-"+((calendar.get(Calendar.MONTH)+1) > 9 ? (calendar.get(Calendar.MONTH)+1): "0"+ (calendar.get(Calendar.MONTH)+1)));
                           result.put("week",((date.getTime()-firstWeekDate.getTime())/day+1)%7==0 ? ((date.getTime()-firstWeekDate.getTime())/day+1)/7 : ((date.getTime()-firstWeekDate.getTime())/day+1)/7+1);
                           return result;
                       }else{
                           return getMonthWeeks(date);
                       }
                    }catch (Exception e){
                        log.error("{}",e);
                    }
                }
            }
            calendar.add(Calendar.DATE,1);
        }
        return null;
    }

    public static JSONObject getMonthWeeks(Date date){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar current = Calendar.getInstance();
        current.setTime(date);
        current.add(Calendar.MONTH,-1);
        int month = current.get(Calendar.MONTH);
        int year = current.get(Calendar.YEAR);

        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR,year);
        calendar.set(Calendar.DATE,1);
        calendar.set(Calendar.MONTH,month);
        int count =0;
        while (calendar.get(Calendar.MONTH) == month){
            if(calendar.get(Calendar.DAY_OF_WEEK) == Calendar.MONDAY){
                ++count;
            }
            calendar.add(Calendar.DATE,1);
        }
        JSONObject result = new JSONObject();
        result.put("month",year+"-"+((month+1) > 9 ? (month+1): "0"+ (month+1)));
        result.put("week",count);
        return result;
    }

    public static JSONObject getMonthWeekCount(Date date){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar current = Calendar.getInstance();
        current.setTime(date);
        int month = current.get(Calendar.MONTH);
        int year = current.get(Calendar.YEAR);

        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR,year);
        calendar.set(Calendar.DATE,1);
        calendar.set(Calendar.MONTH,month);
        int count =0;
        while (calendar.get(Calendar.MONTH) == month){
            if(calendar.get(Calendar.DAY_OF_WEEK) == Calendar.MONDAY){
                ++count;
            }
            calendar.add(Calendar.DATE,1);
        }
        JSONObject result = new JSONObject();
        result.put("month",year+"-"+((month+1) > 9 ? (month+1): "0"+ (month+1)));
        result.put("week",count);
        return result;
    }

    public static JSONObject getMonthWeekRange(Date date){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar current = Calendar.getInstance();
        current.setTime(date);
        int month = current.get(Calendar.MONTH);
        int year = current.get(Calendar.YEAR);

        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR,year);
        calendar.set(Calendar.DATE,1);
        calendar.set(Calendar.MONTH,month);
        int count =0;
        JSONObject result = new JSONObject();
        while (calendar.get(Calendar.MONTH) == month){
            if(calendar.get(Calendar.DAY_OF_WEEK) == Calendar.MONDAY){
                ++count;
                String start = ((calendar.get(Calendar.MONTH)+1) > 9 ? (calendar.get(Calendar.MONTH)+1): "0"+ (calendar.get(Calendar.MONTH)+1))+"."+((calendar.get(Calendar.DATE)) > 9 ? (calendar.get(Calendar.DATE)): "0"+ (calendar.get(Calendar.DATE)));
                calendar.add(Calendar.DATE,6);
                String end = ((calendar.get(Calendar.MONTH)+1) > 9 ? (calendar.get(Calendar.MONTH)+1): "0"+ (calendar.get(Calendar.MONTH)+1))+"."+((calendar.get(Calendar.DATE)) > 9 ? (calendar.get(Calendar.DATE)): "0"+ (calendar.get(Calendar.DATE)));
                result.put(count+"",start+"-"+end);
            }
            calendar.add(Calendar.DATE,1);
        }
        //JSONObject result = new JSONObject();
        return result;
    }

    public static String yesterday(){
        try {
            // 创建 Calendar 对象
            Calendar calendar = Calendar.getInstance();

            // 将日期设置为当前日期
            calendar.setTimeInMillis(System.currentTimeMillis());

            // 在当前日期上加1天
            calendar.add(Calendar.DAY_OF_MONTH, -1);

            // 获取年、月、日信息
            int year = calendar.get(Calendar.YEAR);
            int month = calendar.get(Calendar.MONTH) + 1; // 注意月份从0开始计数，所以需要+1
            int dayOfMonth = calendar.get(Calendar.DAY_OF_MONTH);

            // 构造输出字符串
            String dateStr = String.format("%d-%02d-%02d", year, month, dayOfMonth);

            return dateStr;

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
