package cn.lyy.merchant.controller.onedata;

import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.merchant.api.service.MerchantWhiteClient;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.controller.onedata.dto.DayParamDto;
import cn.lyy.merchant.controller.onedata.dto.MonthParamDto;
import cn.lyy.merchant.controller.onedata.dto.WeekParamDto;
import cn.lyy.merchant.controller.onedata.dto.YearParamDto;
import cn.lyy.merchant.dto.merchant.response.MerchantGroupDTO;
import cn.lyy.merchant.dto.response.MerchantGroupListDTO;
import cn.lyy.merchant.service.IEquipmentGroupBusinessService;
import cn.lyy.merchant.service.onedata.OneDataRealTimeService;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
@RestController
@RequestMapping("/oneData/realTime")
public class RealTimeReportController extends BaseController {

    @Autowired
    private OneDataRealTimeService oneDataRealTimeService;

    @Autowired
    private IEquipmentGroupBusinessService equipmentGroupBusinessService;

    @Autowired
    private MerchantWhiteClient merchantWhiteClient;

    @Value("${oneData.testModel}")
    private Boolean testModel;

    @Value("${oneData.realTimeWhiteListCode:1907}")
    private int realTimeWhiteListCode = 1907;
    
//    @Value("${oneData.stableWhiteListCode:1905}")
//    private int stableWhiteListCode ;

    @Value("#{${oneData.merchantTrans}}")
    private Map<Long,Long> merchantTransMap;

    private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
    private SimpleDateFormat msdf = new SimpleDateFormat("yyyy-MM");

    @GetMapping("/mobile/isRealTimeReporter")
    @AuthorityResource(name = "移动端报表二期白名单-基础数据", value = "mb_mobile_report_isRealTimeReporter", seq = 12, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getIsSecReporter(DayParamDto dayParamDto) {
        log.info("移动端报表白名单-基础数据:{}",JSONObject.toJSON(dayParamDto).toString());
        /*if(dayParamDto.getMerchantId() == null || dayParamDto.getMerchantId() <=0){
            return error(500,"商户id不能为空");
        }*/
        log.info("商家id：{}",getAdOrgIdNotNull());

        if(testModel){
            return success(false);
        }
        if(isWith(realTimeWhiteListCode)){
            return success(true);
        }
        return success(false);
    }

    @GetMapping("/mobile/order")
    @AuthorityResource(name = "订单分析-实时", value = "mb_mobile_realTime_order", seq = 1, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getReportData(DayParamDto dayParamDto) {
        log.info("订单分析-实时:{}", JSONObject.toJSON(dayParamDto).toString());
        if(!isWith(realTimeWhiteListCode)){
            return error(500,"不在使用范围内");
        }

        dayParamDto.setDay(sdf.format(new Date()));
        getGroupId(dayParamDto);

        dayParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));


        JSONObject result = oneDataRealTimeService.getOrderData(dayParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/timeRangeAnalyse")
    @AuthorityResource(name = "经营时段分析-日报", value = "mb_mobile_realTime_timeRangeAnalyse", seq = 5, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getTimeRangeAnalyse(DayParamDto dayParamDto) {
        log.info("经营时段分析-实时:{}",JSONObject.toJSON(dayParamDto).toString());
        if(!isWith(realTimeWhiteListCode)){
            return error(500,"不在使用范围内");
        }

        dayParamDto.setDay(sdf.format(new Date()));
        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(dayParamDto);
        dayParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));
        JSONObject result = oneDataRealTimeService.getTimeRangeData(dayParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }


    @GetMapping("/mobile/orderPriceRangeAnalyse")
    @AuthorityResource(name = "客单价-日报", value = "mb_mobile_realTime_orderPriceRangeAnalyse", seq = 5, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getOrderPriceRangeAnalyse(DayParamDto dayParamDto) {
        log.info("客单价-实时:{}",JSONObject.toJSON(dayParamDto).toString());
        if(!isWith(realTimeWhiteListCode)){
            return error(500,"不在使用范围内");
        }

        dayParamDto.setDay(sdf.format(new Date()));
        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(dayParamDto);
        dayParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));
        JSONObject result = oneDataRealTimeService.getPerOrderPriceCount(dayParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/memberAnalyse")
    @AuthorityResource(name = "会员分析-实时", value = "mb_mobile_realTime_memberAnalyse", seq = 5, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getMemberAnalyse(DayParamDto dayParamDto) {
        log.info("会员分析-实时:{}",JSONObject.toJSON(dayParamDto).toString());
        if(!isWith(realTimeWhiteListCode)){
            return error(500,"不在使用范围内");
        }

        dayParamDto.setDay(sdf.format(new Date()));
        log.info("商家id：{}",getAdOrgIdNotNull());
        getGroupId(dayParamDto);
        dayParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));
        JSONObject result = oneDataRealTimeService.getMemberData(dayParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    @GetMapping("/mobile/group")
    @AuthorityResource(name = "场地分析-实时", value = "mb_mobile_realTime_order", seq = 1, role = "Saas_Merchant", parentValue = "mb_mobile_report")
    public BaseResponse getGroupReportData(DayParamDto dayParamDto) {
        log.info("订单分析-实时:{}", JSONObject.toJSON(dayParamDto).toString());
        if(!isWith(realTimeWhiteListCode)){
            return error(500,"不在使用范围内");
        }

        dayParamDto.setDay(sdf.format(new Date()));
        getGroupId(dayParamDto);

        dayParamDto.setMerchantId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull().intValue():merchantTransMap.get(getAdOrgIdNotNull()).intValue()));


        JSONObject result = oneDataRealTimeService.getGroupData(dayParamDto);

        if(result !=null && result.containsKey("code") && "200".equals(result.getString("code"))){
            return success(result.get("info"));
        }else{
            return error(500,result == null ? "请求出错":result.getString("msg"));
        }
    }

    private void getGroupId(Object param){

        //子账户处理
        if(getCurrentUser().getIsApprover()){
            return;
        }
        List<MerchantGroupListDTO> list = equipmentGroupBusinessService.getGroupInfoDistributorId(getAdOrgIdNotNull(), getAdUserIdNotNull(),
                false,"", null);
        if(list != null ){
            Set<String> groupIds = new TreeSet<>();
            for(MerchantGroupListDTO merchantGroupListDTO : list){
                if(merchantGroupListDTO != null && merchantGroupListDTO.getGroups() !=null && merchantGroupListDTO.getGroups().size()>0){
                    for(MerchantGroupDTO merchantGroupDTO: merchantGroupListDTO.getGroups()){
                        if(merchantGroupDTO != null && "Y".equals(merchantGroupDTO.getIsactive())){
                            groupIds.add(merchantGroupDTO.getEquipmentGroupId()+"");
                        }
                    }
                }
            }
            if(groupIds.size()>0){
                if(param instanceof DayParamDto){
                    ((DayParamDto) param).setGroupIds(groupIds);
                }

                if(param instanceof WeekParamDto){
                    ((WeekParamDto) param).setGroupIds(groupIds);
                }

                if(param instanceof MonthParamDto){
                    ((MonthParamDto) param).setGroupIds(groupIds);
                }

                if(param instanceof YearParamDto){
                    ((YearParamDto) param).setGroupIds(groupIds);
                }
            }
        }

    }

    private boolean isWith(int targetWith){
        log.info("当前登录商家id：{}",getAdOrgIdNotNull());
        BaseResponse<List<Integer>> witheList = merchantWhiteClient.getAllWhiteByDistributorId((merchantTransMap.get(getAdOrgIdNotNull())==null? getAdOrgIdNotNull():merchantTransMap.get(getAdOrgIdNotNull())));
        if(witheList.getCode() == 0){
            //log.info("商家：{}，白名单：{}", merchant.getId(),witheList.getData());
            List<Integer> withes = witheList.getData();
            if(withes != null && withes.size()>0){
                for(Integer with : withes){
                    if(with.intValue() == targetWith){
                        return true;
                    }
                }
            }
        }
        return false;
    }
    
}
