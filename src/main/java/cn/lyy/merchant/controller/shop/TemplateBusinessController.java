package cn.lyy.merchant.controller.shop;

import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.merchant.api.service.MerchantTemplateService;
import cn.lyy.merchant.controller.common.UserModelContrller;
import cn.lyy.merchant.dto.common.IdDTO;
import cn.lyy.merchant.dto.equipment.EquipmentTypeDTO;
import cn.lyy.merchant.dto.merchant.MerchantShopTemplateDTO;
import cn.lyy.merchant.dto.template.EquipmentTemplateDTO;
import cn.lyy.merchant.dto.template.EquipmentTemplateListDTO;
import cn.lyy.merchant.dto.template.MerchantShopActiveTemplateDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>Title:saas2</p>
 * <p>Desc: 商家店铺信息接口</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020/11/13
 */
@RestController
@RequestMapping("/template")
public class TemplateBusinessController extends UserModelContrller {

    @Autowired
    private MerchantTemplateService templateService;

    @PostMapping("/listEquipmentType")
    @AuthorityResource(name = "查询有模板的设备类型", value = "mb_listEquipmentTypeV3", role = "Saas_Merchant", seq = 201, parentValue = "MerchantShop")
    public BaseResponse listEquipmentType(@ModelAttribute(value = "param") JSONObject param){
        EquipmentTemplateDTO templateDTO = JSON.toJavaObject(param, EquipmentTemplateDTO.class);
        BaseResponse<List<EquipmentTypeDTO>> response = templateService.getTmplEquipmentType(templateDTO);
        List<EquipmentTypeDTO> list = response.getData();
        if(list != null && list.size() > 0){
            return success(list.stream().map(item -> new IdDTO(item.getId() , item.getName())).collect(Collectors.toList()));
        }
        return success(null);
    }

    @PostMapping("/listTemplate")
    @AuthorityResource(name = "根据设备类型列出模板", value = "mb_listTemplateV3", role = "Saas_Merchant",seq = 202, parentValue = "MerchantShop")
    public BaseResponse listTemplate(@ModelAttribute(value = "param") JSONObject param){
        EquipmentTemplateDTO templateDTO = JSON.toJavaObject(param, EquipmentTemplateDTO.class);
        templateDTO.setSystemType("C");
        templateDTO.setRoleType("ADMIN");
        templateDTO.setAdOrgId(null);
        BaseResponse<List<EquipmentTemplateListDTO>> response = templateService.listTemplateAndPage(templateDTO);
        List<EquipmentTemplateListDTO> tmplList = response.getData();
        List<MerchantShopTemplateDTO> adminTemplates = Lists.newArrayList();
        for (int i = 0; i < tmplList.size(); i++) {
            EquipmentTemplateListDTO tmpl = tmplList.get(i);
            MerchantShopTemplateDTO vo = new MerchantShopTemplateDTO();
            vo.setUsed(tmpl.getIsDefault());
            vo.setName(tmpl.getName() + "-" + (i + 1));
            if (StringUtils.isNotBlank(tmpl.getCoverUrl())) {
                List<String> coverUrls = Arrays.asList(tmpl.getCoverUrl().split(","));
                vo.setCoverUrls(coverUrls);
            }
            vo.setTmplId(tmpl.getLyyEquipmentTmplId());
            adminTemplates.add(vo);
        }

        templateDTO.setRoleType("MERCHANT");
        templateDTO.setAdOrgId(getAdOrgIdNotNull());
        response = templateService.listTemplateAndPage(templateDTO);
        tmplList = response.getData();
        List<MerchantShopTemplateDTO> merchantTemplates = Lists.newArrayList();
        for (int i = 0; i < tmplList.size(); i++) {
            EquipmentTemplateListDTO tmpl = tmplList.get(i);
            MerchantShopTemplateDTO vo = new MerchantShopTemplateDTO();
            vo.setUsed(tmpl.getIsDefault());
            vo.setName(tmpl.getName() + "-" + (i + 1));
            if (StringUtils.isNotBlank(tmpl.getCoverUrl())) {
                List<String> coverUrls = Arrays.asList(tmpl.getCoverUrl().split(","));
                vo.setCoverUrls(coverUrls);
            }
            vo.setTmplId(tmpl.getLyyEquipmentTmplId());
            merchantTemplates.add(vo);
        }

        JSONObject obj = new JSONObject();
        obj.put("admin", adminTemplates);
        obj.put("shop", merchantTemplates);
        return success(obj);
    }

    @PostMapping("/templateDetail")
    @AuthorityResource(name = "获取模板详情", value = "mb_templateDetailV3",role = "Saas_Merchant", seq = 203, parentValue = "MerchantShop")
    public BaseResponse templateDetail(@ModelAttribute(value = "param") JSONObject param) {
        EquipmentTemplateDTO templateDTO = JSON.toJavaObject(param, EquipmentTemplateDTO.class);
        BaseResponse<EquipmentTemplateListDTO> response = templateService.templateDetail(templateDTO.getTemplateId());
        EquipmentTemplateListDTO data = response.getData();
        MerchantShopTemplateDTO vo = new MerchantShopTemplateDTO();
        vo.setContent(data.getPageContent());
        return success(vo);
    }

    @PostMapping("/activeTemplate")
    @AuthorityResource(name = "启用模板", value = "mb_activeTemplateV3",role = "Saas_Merchant", seq = 204, parentValue = "MerchantShop")
    public BaseResponse activeTemplate(@ModelAttribute(value = "param") JSONObject param) {
        MerchantShopActiveTemplateDTO templateDTO = JSON.toJavaObject(param, MerchantShopActiveTemplateDTO.class);
        BaseResponse<Long> response = templateService.activeTemplate(templateDTO);
        return response.getCode() == ResponseCodeEnum.FAIL.getCode() ? error(response.getCode(), response.getMessage()) : success(response.getData());
    }

    @PostMapping("/deleteTemplate")
    @AuthorityResource(name = "删除模板", value = "mb_deleteTemplateV3",role = "Saas_Merchant", seq = 205, parentValue = "MerchantShop")
    public BaseResponse deleteTemplate(@ModelAttribute(value = "param") JSONObject param) {
        EquipmentTemplateDTO templateDTO = JSON.toJavaObject(param, EquipmentTemplateDTO.class);
        templateDTO.setRoleType("MERCHANT");
        BaseResponse response = templateService.deleteTemplate(templateDTO);
        return response.getCode() == ResponseCodeEnum.FAIL.getCode() ? error(response.getCode(), response.getMessage()) : success(response.getMessage());
    }
}
