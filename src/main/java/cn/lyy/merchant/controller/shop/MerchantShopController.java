package cn.lyy.merchant.controller.shop;

import cn.lyy.authority_service_api.plugin.authority.anno.AuthorityResource;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.base.utils.ResponseUtils;
import cn.lyy.merchant.constants.BusinessExceptionEnums;
import cn.lyy.merchant.controller.common.BaseController;
import cn.lyy.merchant.dto.common.IdDTO;
import cn.lyy.merchant.dto.group.BalanceSharedGroupDTO;
import cn.lyy.merchant.dto.group.MerchantEquipmentTypeConfigDTO;
import cn.lyy.merchant.dto.group.SummationOfEquipmentTypeDTO;
import cn.lyy.merchant.dto.merchant.request.MerchantShopNoticeRequest;
import cn.lyy.merchant.dto.merchant.request.ShopInfoSetDTO;
import cn.lyy.merchant.dto.merchant.response.ShopTitleRespDTO;
import cn.lyy.merchant.dto.request.BalanceSettingReqDTO;
import cn.lyy.merchant.dto.response.MerchantShopNoticeResponseDTO;
import cn.lyy.merchant.dto.response.ShopProfileDTO;
import cn.lyy.merchant.dto.user.AdUserInfoDTO;
import cn.lyy.merchant.exception.BusinessException;
import cn.lyy.merchant.service.shop.MerchantShopService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * @ClassName: MerchantShopController
 * @description: 店铺
 * @author: pengkun
 * @create: 2020-11-16 16:40
 * @Version 1.0
 **/
@RestController
@RequestMapping("/rest/shop")
@Slf4j
public class MerchantShopController extends BaseController {

    @Autowired
    private MerchantShopService merchantShopService;

    /**
     * 获取店铺概况
     * @return
     */
    @GetMapping("/profile")
    @AuthorityResource(name = "获取店铺概览", value = "mb_shop_profile", seq = 1, role = "Saas_Merchant", parentValue = "MerchantShop")
    public BaseResponse<ShopProfileDTO> shopProfile() {
        AdUserInfoDTO currentUser = getCurrentUser();
        IdDTO idDTO = new IdDTO();
        idDTO.setUserOrgId(currentUser.getAdOrgId());
        idDTO.setAuthorityUserId(currentUser.getAuthorityUserId());
        idDTO.setFactoryOrgId(currentFactoryId());
        idDTO.setIsApprover(currentUser.getIsApprover());
        idDTO.setUserId(currentUser.getAdUserId());

        return ResponseUtils.success(merchantShopService.shopProfile(idDTO));
    }

    /**
     * 公告保存
     * @param request
     * @return
     */
    @PostMapping("/notice/save")
    @AuthorityResource(name = "保存公告", value = "mb_notice_save", seq = 1, role = "Saas_Merchant", parentValue = "MerchantShop")
    public BaseResponse<Long> save(@RequestBody MerchantShopNoticeRequest request){
        if(request.getGroups() == null || request.getGroups().size() == 0){
            return ResponseUtils.error(ResponseCodeEnum.PARAMETER_ERROR.getCode(),"请选择生效场地");
        }
        if(StringUtils.isBlank(request.getContent())){
            return ResponseUtils.error(ResponseCodeEnum.PARAMETER_ERROR.getCode(),"请输入公告内容");
        }
        request.setAdOrgId(getAdOrgIdNotNull());
        request.setAdUserId(getAdUserIdNotNull());
        Long id = merchantShopService.saveOrUpdateMerchantShopNotice(request);
        return ResponseUtils.success(id);
    }

    /**
     * 公告更新
     * @param request
     * @return
     */
    @PostMapping("/notice/update")
    @AuthorityResource(name = "更新公告", value = "mb_notice_update", seq = 1, role = "Saas_Merchant", parentValue = "MerchantShop")
    public BaseResponse<Long> update(@RequestBody MerchantShopNoticeRequest request){
        if(request.getNoticeId() == null ){
            return ResponseUtils.error(ResponseCodeEnum.PARAMETER_ERROR.getCode(),"请选择要修改的公告");
        }
        if(request.getGroups() == null || request.getGroups().size() == 0){
            return ResponseUtils.error(ResponseCodeEnum.PARAMETER_ERROR.getCode(),"请选择生效场地");
        }
        if(StringUtils.isBlank(request.getContent())){
            return ResponseUtils.error(ResponseCodeEnum.PARAMETER_ERROR.getCode(),"请输入公告内容");
        }
        request.setAdOrgId(getAdOrgIdNotNull());
        request.setAdUserId(getAdUserIdNotNull());
        Long id = merchantShopService.saveOrUpdateMerchantShopNotice(request);
        return ResponseUtils.success(id);
    }

    /**
     * 公告删除
     * @param request
     * @return
     */
    @PostMapping("/notice/delete")
    @AuthorityResource(name = "删除公告", value = "mb_notice_del", seq = 1, role = "Saas_Merchant", parentValue = "MerchantShop")
    public BaseResponse<Boolean> delete(@RequestBody MerchantShopNoticeRequest request){
        if(request.getNoticeId() == null ){
            return ResponseUtils.error(ResponseCodeEnum.PARAMETER_ERROR.getCode(),"请选择要删除的公告");
        }
        request.setAdOrgId(getAdOrgIdNotNull());
        request.setAdUserId(getAdUserIdNotNull());
        boolean flag = merchantShopService.delMerchantShopNotice(request);
        if(!flag){
            return ResponseUtils.error(ResponseCodeEnum.FAIL.getCode(),"删除失败");
        }
        return ResponseUtils.success();
    }

    /**
     * 公告列表查询
     * @param request
     * @return
     */
    @PostMapping("/notice/list")
    @AuthorityResource(name = "公告列表", value = "mb_notice_list", seq = 1, role = "Saas_Merchant", parentValue = "MerchantShop")
    public BaseResponse<List<MerchantShopNoticeResponseDTO>> getNoticeList(@RequestBody MerchantShopNoticeRequest request){
        request.setAdOrgId(getAdOrgIdNotNull());
        request.setAdUserId(getAdUserIdNotNull());
        return ResponseUtils.success(merchantShopService.getNoticeList(request));
    }

    /**
     * 公告列表查询
     * @param request
     * @return
     */
    @PostMapping("/notice/detail")
    @AuthorityResource(name = "公告详情", value = "mb_notice_detail", seq = 1, role = "Saas_Merchant", parentValue = "MerchantShop")
    public BaseResponse<MerchantShopNoticeResponseDTO> getNoticeDetail(@RequestBody MerchantShopNoticeRequest request){
        if (request.getNoticeId() == null) {
            throw new BusinessException(BusinessExceptionEnums.PARAM_ERROR);
        }
        request.setAdOrgId(getAdOrgIdNotNull());
        request.setAdUserId(getAdUserIdNotNull());
        List<MerchantShopNoticeResponseDTO> list = merchantShopService.getNoticeList(request);
        if (CollectionUtils.isEmpty(list)) {
            return ResponseUtils.success();
        } else {
            return ResponseUtils.success(list.get(0));
        }
    }

    /**
     * 查询场地标题
     * @return
     */
    @GetMapping("/page/title/list")
    @AuthorityResource(name = "场地标题列表", value = "mb_title_list", seq = 1, role = "Saas_Merchant", parentValue = "MerchantShop")
    public BaseResponse<ShopTitleRespDTO> pageTitleList() {
        return ResponseUtils.success(merchantShopService.pageTitleList(getAdOrgIdNotNull(), getAdUserIdNotNull()));
    }

    /**
     * 设置页面标题
     * @param param
     * @return
     */
    @PostMapping("/page/title/set")
    @AuthorityResource(name = "场地标题设置", value = "mb_notice_set", seq = 1, role = "Saas_Merchant", parentValue = "MerchantShop")
    public BaseResponse pageTitleSet(@RequestBody @Valid ShopInfoSetDTO param, BindingResult result) {
        if (result.hasErrors()) {
            throw new BusinessException(result.getAllErrors().get(0).getDefaultMessage());
        }
        merchantShopService.pageTitleSet(getAdOrgIdNotNull(), getAdUserIdNotNull(), param);
        return ResponseUtils.success();
    }

    /**
     * 获取客服电话列表
     * @return
     */
    @GetMapping("/service/phone/list")
    @AuthorityResource(name = "客服电话列表", value = "mb_service_phone_list", seq = 1, role = "Saas_Merchant", parentValue = "MerchantShop")
    public BaseResponse<ShopTitleRespDTO> servicePhoneList() {
        return ResponseUtils.success(merchantShopService.servicePhoneList(getAdOrgIdNotNull(), getAdUserIdNotNull()));
    }

    /**
     * 设置客服电话
     * @return
     */
    @PostMapping("/service/phone/set")
    @AuthorityResource(name = "设置客服电话", value = "mb_service_phone_set", seq = 1, role = "Saas_Merchant", parentValue = "MerchantShop")
    public BaseResponse<ShopTitleRespDTO> servicePhoneList(@RequestBody ShopInfoSetDTO param) {
        merchantShopService.pageTitleSet(getAdOrgIdNotNull(), getAdUserIdNotNull(), param);
        return ResponseUtils.success();
    }

    /**
     * 展示强制充值的开关
     * @return
     */
    @GetMapping("/enforce/recharge/switch/list")
    @AuthorityResource(name = "强制充值开关列表", value = "mb_enforce_switch_list", seq = 1, role = "Saas_Merchant", parentValue = "MerchantShop")
    public BaseResponse<List<SummationOfEquipmentTypeDTO>> enforceRechargeList() {
        return ResponseUtils.success(merchantShopService.enforceRechargeList(getAdOrgIdNotNull(), getAdUserIdNotNull()));
    }

    /**
     * 设置强制充值开关
     * @return
     */
    @PostMapping("/enforce/recharge/switch/set")
    @AuthorityResource(name = "强制充值开关设置", value = "mb_enforce_switch_set", seq = 1, role = "Saas_Merchant", parentValue = "MerchantShop")
    public BaseResponse enforceRechargeSet(@RequestBody List<MerchantEquipmentTypeConfigDTO> param) {
        merchantShopService.enforceRechargeSet(getAdOrgIdNotNull(), getAdUserIdNotNull(), param);
        return ResponseUtils.success();
    }

    /**
     * 展示场地通用分组列表
     * @return
     */
    @GetMapping("/balance/shared/group/list")
    @AuthorityResource(name = "展示场地通用分组列表", value = "mb_balance_shared_list", seq = 1, role = "Saas_Merchant", parentValue = "MerchantShop")
    public BaseResponse<List<BalanceSharedGroupDTO>> balanceSharedGroupList() {
        if (!getCurrentUser().getIsApprover()) {
            throw new BusinessException(BusinessExceptionEnums.YOU_HAS_NO_AUTHORIZE);
        }
        return ResponseUtils.success(merchantShopService.balanceSharedGroupList(getAdOrgIdNotNull(), getAdUserIdNotNull()));
    }

    /**
     * 展示场地通用分组详情
     * @return
     */
    @GetMapping("/balance/shared/group/info")
    @AuthorityResource(name = "展示场地通用分组详情", value = "mb_balance_shared_info", seq = 1, role = "Saas_Merchant", parentValue = "MerchantShop")
    public BaseResponse<BalanceSharedGroupDTO> balanceSharedGroupInfo(@RequestParam(value = "balanceSharedGroupId", required = false) Long balanceSharedGroupId) {
        if (!getCurrentUser().getIsApprover()) {
            throw new BusinessException(BusinessExceptionEnums.YOU_HAS_NO_AUTHORIZE);
        }
        return ResponseUtils.success(merchantShopService.balanceSharedGroupInfo(getAdOrgIdNotNull(), balanceSharedGroupId, getAdUserIdNotNull()));
    }

    /**
     * 设置场地通用分组
     * @return
     */
    @PostMapping("/balance/shared/group/set")
    @AuthorityResource(name = "设置场地通用分组", value = "mb_balance_shared_set", seq = 1, role = "Saas_Merchant", parentValue = "MerchantShop")
    public BaseResponse<BalanceSharedGroupDTO> balanceSharedGroupSet(@RequestBody BalanceSettingReqDTO param) {
        if (!getCurrentUser().getIsApprover()) {
            throw new BusinessException(BusinessExceptionEnums.YOU_HAS_NO_AUTHORIZE);
        }
        if (CollectionUtils.isEmpty(param.getGroups())) {
            throw new BusinessException(BusinessExceptionEnums.AT_LEAST_ONE_GROUP);
        }
        merchantShopService.balanceSharedGroupSet(getAdOrgIdNotNull(), getAdUserIdNotNull(), param);
        return ResponseUtils.success();
    }

}
