package cn.lyy.merchant.util;

public class CDZTypeUtil {
    private static final int CK_START = 300;
    private static final int CK_END = 599;
    private static final int CD_F = 10000;
    private static final int CDZ_306 = 306;

    public CDZTypeUtil() {
    }

    public static CDZTypeUtil.EquipemntCDZtype getCDZtype(Integer loginFlag) {
        if (null != loginFlag && loginFlag >= 0) {
            return (loginFlag < 300 || loginFlag > 599) && loginFlag != 10000 ? CDZTypeUtil.EquipemntCDZtype.MZ : CDZTypeUtil.EquipemntCDZtype.CK;
        } else {
            return null;
        }
    }

    public static boolean isCK(Integer loginFlag) {
        return loginFlag >= 300 && loginFlag <= 599 || loginFlag == 10000;
    }

    public static boolean isCKFor306(Integer loginFlag) {
        return loginFlag >= 300 && loginFlag <= 599 && 306 != loginFlag || loginFlag == 10000;
    }

    public static void main(String[] args) {
        Integer loginFlag = 301;
        System.out.println(isCK(loginFlag));
    }

    public static enum EquipemntCDZtype {
        MZ,
        CK;

        private EquipemntCDZtype() {
        }
    }
}
