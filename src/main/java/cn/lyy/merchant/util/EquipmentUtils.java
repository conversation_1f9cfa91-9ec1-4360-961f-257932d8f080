package cn.lyy.merchant.util;

import cn.lyy.merchant.constants.EquipmentTypeEnums;

import java.util.Arrays;
import java.util.List;

/**
 * 类描述：
 * <p>
 *
 * <AUTHOR>
 * @since 2020/11/6 14:47
 */
public class EquipmentUtils {

    /**
     * 脉冲设备相关品类
     */
    public static final List<EquipmentTypeEnums> EQUIPMENT_TYPE_PULSE_LIST = Arrays.asList(EquipmentTypeEnums.MCCDZ,
            EquipmentTypeEnums.CHILDREN_CLASS,EquipmentTypeEnums.BOXING_MACHINE,EquipmentTypeEnums.SELL_WATER_MACHINE,EquipmentTypeEnums.GAME_MACHINE);

    /**
     * 判断设备登录标识是否开放平台设备
     *
     * @param loginFlag 设备登录标识
     */
    public static boolean isOpenEquipment(Integer loginFlag) {
        return loginFlag != null && loginFlag == 10000;
    }

    public static boolean checkPulse(String equipmentTypeValue){
        for(EquipmentTypeEnums equipmentTypeEnums: EQUIPMENT_TYPE_PULSE_LIST){
            if(equipmentTypeEnums.getValue().equals(equipmentTypeValue)){
                return true;
            }
        }
        return false;
    }

}
