package cn.lyy.merchant.util;

import org.apache.commons.lang.math.NumberUtils;

import java.io.UnsupportedEncodingException;

/**
 * 协议解析工具类
 */
public class YTQUtils {
/**
 * 设置参数，需要在规定范围内，否则原值不变
 *
 * @param from   需要设置的参数原值
 * @param target 需要设置的参数新值
 * @param min    最小值
 * @param max    最大值
 * @return
 */
public static int setOptions(int from, String target, int min, int max) {
        if (NumberUtils.isNumber(target)) {
        Integer integer = Integer.valueOf(target);
        if (integer >= min && integer <= max) {
        from = integer;
        }
        }
        return from;
        }


    /**
     * 设置参数，需要在规定范围内，否则原值不变
     *
     * @param from   需要设置的参数原值
     * @param target 需要设置的参数新值
     * @param min    最小值
     * @param max    最大值
     * @return
     */
    public static float setFloatOptions(float from, String target, float min, float max) {
        if (NumberUtils.isNumber(target)) {
            float integer = Float.valueOf(target);
            if (integer >= min && integer <= max) {
                from = integer;
            }
        }
        return from;
    }

    /**
     * 设置参数，需要在规定范围内，否则原值不变
     *
     * @param from   需要设置的参数原值
     * @param target 需要设置的参数新值
     * @param min    最小值
     * @param max    最大值
     * @return
     */
    public static String setCharOptions(String from, String target, String charset, int min, int max) {
        if (target != null && !"".equals(target)) {
            int length = 0;
            try {
                length = target.getBytes(charset).length;
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
            if (length >= min && length <= max) {
                from = target;
            }
        }
        return from;
    }

    /**
     * @param packet 包
     * @param start  起始位置
     * @param source 需要放进去的数值
     * @return
     */
    public static void into2Packet(byte[] packet, int start, int source) {
        byte first = (byte) (source >> 8);
        byte second = (byte) (source & 0xff);
        packet[start + 1] = first;
        packet[start] = second;
    }
    /**
     * @param packet 包
     * @param start  起始位置
     * @param source 需要放进去的数值
     * @return
     */
    public static void into3Packet(byte[] packet, int start, int source) {
        packet[start + 2] = (byte)((source >> 16) & 0xFF);
        packet[start + 1] = (byte)((source >> 8) & 0xFF);
        packet[start] = (byte)(source & 0xFF);
    }

    /**
     * @param packet 包
     * @param start  起始位置
     * @param source 需要放进去的数值
     * @return
     */
    public static void into4Packet(byte[] packet, int start, int source) {
        packet[start + 3] = (byte)((source >> 24) & 0xFF);
        packet[start + 2] = (byte)((source >> 16) & 0xFF);
        packet[start + 1] = (byte)((source >> 8) & 0xFF);
        packet[start] = (byte)(source & 0xFF);
    }

    /**
     * @param packet 包
     * @param start  起始位置
     * @param source 需要放进去的数值
     * @return
     */
    public static void floatTo2Packet(byte[] packet, int start, float source) {
        String sourceStr = String.valueOf(source);
        String[] sourceStrs = sourceStr.split("\\.");
        int innteger = Integer.valueOf(sourceStrs[0]);
        int decimal = Integer.valueOf(sourceStrs[1]);
        packet[start] = (byte) innteger;
        packet[start + 1] = (byte) decimal;
    }
}