package cn.lyy.merchant.util;

import cn.lyy.merchant.constants.BusinessExceptionEnums;
import cn.lyy.merchant.constants.FeeRuleModeSettingEnum;
import cn.lyy.merchant.constants.FeeRuleModeTimeElecEnum;
import cn.lyy.merchant.dto.response.FeeRuleUnitDTO;
import cn.lyy.merchant.exception.BusinessException;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Optional;
import java.util.logging.Logger;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2019/11/29
 * @Version 1.0
 **/
@Slf4j
public class FeeRuleUnitGenerater {

    private static JSONObject jsonRoot;
    private static String ruleUnit = "{\"time\":{\"default\":{\"unit\":\"min\",\"text\":\"分钟\"}},\"elec\":{\"default\":{\"unit\":\"du\",\"text\":\"度\"}},\"num\":{\"default\":{\"unit\":\"num\",\"text\":\"次\"},\"mccdz\":{\"unit\":\"mc\",\"text\":\"脉冲\"},\"yxj\":{\"unit\":\"num\",\"text\":\"币\"}},\"mode\":{\"default\":{\"unit\":\"min\",\"text\":\"分钟\"},\"xyj\":{\"level2\":{\"unit\":\"ml\",\"text\":\"毫升\"}}}}";
    static {
        jsonRoot = JSON.parseObject(ruleUnit);
    }
    public void setRuleUnit(String ruleUnit) {
        this.ruleUnit = ruleUnit;
        jsonRoot = JSON.parseObject(ruleUnit);
    }

    /**
     * 根据条件获取计费规则的单位
     * @param settingEnum   运营后台配置的计费规则
     * @param equipmentTypeValue    设备类型值
     * @param cdzTimeOrElec_feeMode  如果充电桩就要告诉是按时长还是电量，如果为null就默认时长
     * @param level 要获取第几级别的计费规则，如果为null则找default
     * @return
     */
    public static FeeRuleUnitDTO generater(FeeRuleModeSettingEnum settingEnum , String equipmentTypeValue , String cdzTimeOrElec_feeMode , Integer level){

        Optional.ofNullable(settingEnum).orElseThrow(() -> new BusinessException(BusinessExceptionEnums.FEE_MODE_MISSING));

        String subMode = null;
        if(settingEnum.equals(FeeRuleModeSettingEnum.TIME_ELEC)){
            if(StringUtils.isBlank(cdzTimeOrElec_feeMode)){
                settingEnum = FeeRuleModeSettingEnum.TIME;
            }
            else {
                try {
                    String timeElec = FeeRuleModeTimeElecEnum.findByCode(cdzTimeOrElec_feeMode.toLowerCase());
                    Optional.ofNullable(timeElec).orElseThrow(() -> new BusinessException(BusinessExceptionEnums.FEE_MODE_NOT_SUPPORT, cdzTimeOrElec_feeMode));
                    subMode = timeElec.toLowerCase();
                }catch (Exception e){
                    throw new BusinessException(BusinessExceptionEnums.FEE_MODE_NOT_SUPPORT, cdzTimeOrElec_feeMode);
                }
            }
        }

        if (settingEnum.equals(FeeRuleModeSettingEnum.MULTI) && StringUtils.isNotEmpty(cdzTimeOrElec_feeMode)) {
            subMode = cdzTimeOrElec_feeMode.toLowerCase();
        }

        boolean isDefault = false;
        if(StringUtils.isBlank(equipmentTypeValue)){
            equipmentTypeValue = "default";
            isDefault = true;
        }
        equipmentTypeValue = equipmentTypeValue.toLowerCase();

        FeeRuleUnitDTO feeRuleUnit = new FeeRuleUnitDTO();

        //如果有子模式(充电桩计费标准) 则使用
        String unitKey = subMode == null ? settingEnum.getKey().toLowerCase() : subMode;

        feeRuleUnit.setType(unitKey);

        if(jsonRoot.containsKey(unitKey)){
            JSONObject unit1 = jsonRoot.getJSONObject(unitKey); // 第一层
            if(unit1.containsKey(equipmentTypeValue)){  //判断第二层
                JSONObject unitItem = unit1.getJSONObject(equipmentTypeValue);
                if(isDefault || level == null){
                    try {
                        setUnitObj(unitItem,feeRuleUnit);
                    }catch (IllegalArgumentException e){
                        setDefaultUnitObj(unit1 , feeRuleUnit);  //默认
                    }
                }
                else {
                    if(unitItem.containsKey("level"+level)) { //判断第三层
                        JSONObject levelJson = unitItem.getJSONObject("level" + level);
                        try {
                            setUnitObj(levelJson, feeRuleUnit);
                        }catch (IllegalArgumentException e){
                            setDefaultUnitObj(unit1 , feeRuleUnit);  //默认
                        }
                    }
                    else
                    if(level == 1){     //如果为第一层，那就读这层的内容，因为level不为空时都基本上是1值
                        try {
                            setUnitObj(unitItem,feeRuleUnit);
                        }catch (IllegalArgumentException e){
                            setDefaultUnitObj(unit1 , feeRuleUnit);  //默认
                        }
                    }
                    else
                        setDefaultUnitObj(unit1 , feeRuleUnit);  //默认
                }
            }
            else
                setDefaultUnitObj(unit1 , feeRuleUnit);     //默认
        }
        else
            throw new BusinessException(BusinessExceptionEnums.FEE_MODE_NOT_SUPPORT, settingEnum.getName() + ", subMode:" + subMode);

        return feeRuleUnit;
    }

    private static void setDefaultUnitObj(JSONObject json , FeeRuleUnitDTO feeRuleUnit){
        JSONObject unitItem = json.getJSONObject("default");
        try {
            setUnitObj(unitItem, feeRuleUnit);
        }catch (IllegalArgumentException e){}
    }

    private static void setUnitObj(JSONObject unitItem , FeeRuleUnitDTO feeRuleUnit)throws IllegalArgumentException{
        if(unitItem == null || !unitItem.containsKey("text") || !unitItem.containsKey("unit")) {
            throw new IllegalArgumentException("不存在text和unit");
        }
        feeRuleUnit.setText(unitItem.getString("text"));
        feeRuleUnit.setUnit(unitItem.getString("unit"));
    }


    public static void main(String[] args){
        FeeRuleUnitGenerater generater = new FeeRuleUnitGenerater();
        FeeRuleUnitDTO unit = generater.generater(FeeRuleModeSettingEnum.MODE , "XYJ" , null, 1);
        Logger.getGlobal().info(unit.toString());
        unit = generater.generater(FeeRuleModeSettingEnum.MODE , "XYJ" , null, 2);
        Logger.getGlobal().info(unit.toString());
        unit = generater.generater(FeeRuleModeSettingEnum.NUMBER , "YXJ" , null, 1);
        Logger.getGlobal().info(unit.toString());
        unit = generater.generater(FeeRuleModeSettingEnum.TIME_ELEC , "CDZ" , "TIME", 1);
        Logger.getGlobal().info(unit.toString());
        unit = generater.generater(FeeRuleModeSettingEnum.TIME_ELEC , "CDZ" , "ELEC", null);
        Logger.getGlobal().info(unit.toString());
        unit = generater.generater(FeeRuleModeSettingEnum.NUMBER , "MCCDZ" , null, null);
        Logger.getGlobal().info(unit.toString());
    }
}
