package cn.lyy.merchant.util;


import cn.lyy.merchant.dto.response.CommonResult;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;


/**
 * 模板消息接口
 *
 * <AUTHOR>
 * @date 2019/3/5.
 */
@Slf4j
public class TemplateUtil {

    // 设置所属行业（POST）
    public final static String api_set_industry_url = "https://api.weixin.qq.com/cgi-bin/template/api_set_industry?access_token=ACCESS_TOKEN";

    // 获取设置的行业信息（GET）
    public final static String get_industry_url = "https://api.weixin.qq.com/cgi-bin/template/get_industry?access_token=ACCESS_TOKEN";

    // 获得模板ID（POST）
    public final static String api_add_template_url = "https://api.weixin.qq.com/cgi-bin/template/api_add_template?access_token=ACCESS_TOKEN";

    // 获取模板列表（GET）
    public final static String get_all_private_template_url = "https://api.weixin.qq.com/cgi-bin/template/get_all_private_template?access_token=ACCESS_TOKEN";

    // 删除模板（GET）
    public final static String del_private_template_url = "https://api.weixin.qq.com/cgi-bin/template/del_private_template?access_token=ACCESS_TOKEN";

    /**
     * 设置所属行业
     *
     * @param industryId1 模板消息所属行业编号
     * @param industryId2 模板消息所属行业编号
     * @param accessToken 接口调用凭证
     * @return
     */
    public static boolean apiSetIndustry(String industryId1, String industryId2, String accessToken) {
        String url = api_set_industry_url.replace("ACCESS_TOKEN", accessToken);
        // 将菜单对象转换成json字符串
        String jsonMsg = "{\"industry_id1\":\"%s\",\"industry_id2\":\"%s\"}";
        jsonMsg = String.format(jsonMsg, industryId1, industryId2);
        log.info("模板消息配置, 参数:{}", jsonMsg);
        // 发起POST请求创建菜单
        JSONObject jsonObject = CommonUtil.httpsRequest(url, "POST", jsonMsg);
        if (null != jsonObject) {
            log.info("模板消息配置结果:{}", jsonObject.toString());
            return jsonObject.getIntValue("errcode") == 0;
        }
        return false;
    }

    /**
     * 获取设置的行业信息
     *
     * @param accessToken 接口调用凭证
     * @return
     */
    public static JSONObject getIndustry(String accessToken) {
        String url = get_industry_url.replace("ACCESS_TOKEN", accessToken);
        JSONObject jsonObject = CommonUtil.httpsRequest(url, "GET", null);
        if (jsonObject != null) {
            log.debug("查询行业配置信息成功, 结果:{}" + jsonObject.toString());
        }
        return jsonObject;
    }

    /**
     * 获得模板ID
     *
     * @param templateIdShort 模板库中模板的编号，有“TM**”和“OPENTMTM**”等形式
     * @param accessToken
     * @return errcode为0时成功, errmsg, template_id
     */
    public static JSONObject apiAddTemplate(String templateIdShort, String accessToken) {
        try {
            String url = api_add_template_url.replace("ACCESS_TOKEN", accessToken);
            // 将菜单对象转换成json字符串
            String jsonMsg = "{\"template_id_short\":\"%s\"}";
            jsonMsg = String.format(jsonMsg, templateIdShort);
            log.info("微信第三方平台-获得模板ID请求参数：{}", jsonMsg);
            // 发起POST请求创建菜单
            JSONObject jsonObject = CommonUtil.httpsRequest(url, "POST", jsonMsg);
            log.info("微信第三方平台-获得模板ID结果：{}", jsonObject.toString());
            return jsonObject;
        } catch (Exception e) {
            log.error(e.getLocalizedMessage(), e);
        }
        return null;
    }

    /**
     * 获取模板列表
     *
     * @param accessToken
     * @return
     */
    public static JSONObject getAllPrivateTemplate(String accessToken) {
        try {
            String url = get_all_private_template_url.replace("ACCESS_TOKEN", accessToken);
            JSONObject jsonObject = CommonUtil.httpsRequest(url, "GET", null);
            // System.out.println("微信第三方平台-获取模板列表结果：" + jsonObject.toString());
            return jsonObject;
        } catch (Exception e) {
            log.error(e.getLocalizedMessage(), e);
        }
        return null;
    }

    /**
     * 删除模板
     *
     * @param templateId
     * @param accessToken
     * @return
     */
    public static CommonResult dePrivateTemplate(String templateId, String accessToken) {
        CommonResult result = new CommonResult();
        try {
            String url = del_private_template_url.replace("ACCESS_TOKEN", accessToken);
            // 将菜单对象转换成json字符串
            String jsonMsg = "{\"template_id\":\"%s\"}";
            jsonMsg = String.format(jsonMsg, templateId);
            // 发起POST请求创建菜单
            log.info("微信第三方平台-删除模板请求：{}", jsonMsg);
            JSONObject jsonObject = CommonUtil.httpsRequest(url, "POST", jsonMsg);
            if (null != jsonObject) {
                int errorCode = jsonObject.getInteger("errcode");
                String errorMsg = jsonObject.getString("errmsg");
                if (0 != errorCode) {
                    result.setErrorResult(errorCode, errorMsg);
                    log.error("删除模板 errcode:{} errmsg:{}", errorCode, errorMsg);
                }
                log.info("微信第三方平台-删除模板结果：{}", jsonObject.toString());
            }
            return result;
        } catch (Exception e) {
            log.error(e.getLocalizedMessage(), e);
            result.setSystemErrorResult();
            return result;
        }
    }

}
