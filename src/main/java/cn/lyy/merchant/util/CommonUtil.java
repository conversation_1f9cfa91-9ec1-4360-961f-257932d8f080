package cn.lyy.merchant.util;

import cn.lyy.tools.util.wechat.MessageUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Charsets;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.lang.Nullable;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <p>Title:saas2</p>
 * <p>Desc: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/8/28
 */
@Slf4j
public class CommonUtil {

    private static Pattern p = Pattern.compile("^(1)\\d{10}$");

    public static boolean isMobile(String mobile) {
        if (Strings.isNullOrEmpty(mobile)) {
            return false;
        } else {
            Matcher m = p.matcher(mobile);
            return m.matches();
        }
    }

    public static String toHexString(byte[] byteArray) {
        if (byteArray != null && byteArray.length >= 1) {
            StringBuilder hexString = new StringBuilder();

            for(int i = 0; i < byteArray.length; ++i) {
                if ((byteArray[i] & 255) < 16) {
                    hexString.append("0");
                }

                hexString.append(Integer.toHexString(255 & byteArray[i]));
            }

            return hexString.toString().toLowerCase();
        } else {
            throw new IllegalArgumentException("this byteArray must not be null or empty");
        }
    }

    public static byte[] toByteArray(String hexString) {
        if (hexString == null) {
            throw new IllegalArgumentException("this hexString must not be empty");
        } else {
            hexString = hexString.toLowerCase();
            byte[] byteArray = new byte[hexString.length() / 2];
            int k = 0;

            for(int i = 0; i < byteArray.length; ++i) {
                byte high = (byte)(Character.digit(hexString.charAt(k), 16) & 255);
                byte low = (byte)(Character.digit(hexString.charAt(k + 1), 16) & 255);
                byteArray[i] = (byte)(high << 4 | low & 255);
                k += 2;
            }

            return byteArray;
        }
    }

    public static String trimAllWhitespace(String str) {
        if (!hasLength(str)) {
            return str;
        } else {
            int len = str.length();
            StringBuilder sb = new StringBuilder(str.length());

            for(int i = 0; i < len; ++i) {
                char c = str.charAt(i);
                if (!Character.isWhitespace(c)) {
                    sb.append(c);
                }
            }

            return sb.toString();
        }
    }

    public static boolean hasLength(@Nullable String str) {
        return str != null && !str.isEmpty();
    }


    public static String CURRENCY_FORMAT = "#.##";
    public static String formatCurrency(double value){
        DecimalFormat decimalFormat = new DecimalFormat(CURRENCY_FORMAT);
        return decimalFormat.format(value);
    }
    public static String formatCurrency(float value){
        DecimalFormat decimalFormat = new DecimalFormat(CURRENCY_FORMAT);
        return decimalFormat.format(value);
    }
    public static String formatCurrency(float value , String format){
        DecimalFormat decimalFormat = new DecimalFormat(format);
        return decimalFormat.format(value);
    }
    public static String formatCurrency(int value){
        DecimalFormat decimalFormat = new DecimalFormat(CURRENCY_FORMAT);
        return decimalFormat.format(value);
    }

    /**
     * 金额元转为分
     * @param price
     * @return
     */
    public static int yuanToCent(BigDecimal price) {
        if(Objects.isNull(price)) {
            return 0;
        }
        price = price.setScale(2, RoundingMode.HALF_UP);
        BigDecimal a = price.multiply(new BigDecimal(100));
        return a.intValue();
    }

    /**
     * 金额分转为元
     * @param price
     * @return
     */
    public static BigDecimal centToYuan(Number price) {
        if(Objects.isNull(price)) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(price.intValue()).divide(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP);
    }

    public static String blankDefault(String value , String defaultValue){
        if(StringUtils.isBlank(value))
            return defaultValue;
        else
            return value;
    }


    public static String urlEncodeUTF8(String source) {
        String result = source;

        try {
            result = URLEncoder.encode(source, "utf-8");
        } catch (UnsupportedEncodingException var3) {
            log.error(var3.getMessage(), var3);
        }

        return result;
    }

    public static String getFileExt(String contentType) {
        String fileExt = "";
        if ("image/jpeg".equals(contentType)) {
            fileExt = ".jpg";
        } else if ("audio/mpeg".equals(contentType)) {
            fileExt = ".mp3";
        } else if ("audio/amr".equals(contentType)) {
            fileExt = ".amr";
        } else if ("video/mp4".equals(contentType)) {
            fileExt = ".mp4";
        } else if ("video/mpeg4".equals(contentType)) {
            fileExt = ".mp4";
        }

        return fileExt;
    }


    /**
     * 属性去重
     * @param keyExtractor
     * @param <T>
     * @return
     */
    public static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Set<Object> seen = ConcurrentHashMap.newKeySet();
        return t -> seen.add(keyExtractor.apply(t));
    }

    /**
     * 获取功率
     * @param power    功率
     * @param voltage  电压
     * @param current  电流
     * @return
     */
    public static BigDecimal getPower(BigDecimal power, BigDecimal voltage, BigDecimal current) {
        BigDecimal finalPower = null;
        if (Objects.isNull(power)) {
            if(Objects.isNull(voltage) || Objects.isNull(current)) {
                return finalPower;
            }
            if (Objects.nonNull(voltage) && Objects.nonNull(current)) {
                finalPower = voltage.multiply(current).setScale(2, RoundingMode.HALF_UP);
            }
        } else {
            finalPower = power.setScale(2, RoundingMode.HALF_UP);
        }
        return finalPower;
    }

    /**
     * 计算百分比
     * @param num
     * @return
     */
    public static String numberFormatPercent(Double num) {
        NumberFormat percent = NumberFormat.getPercentInstance();
        percent.setMaximumFractionDigits(2);

        return percent.format(num);
    }

    /**
     * 转换编码
     * @param oldStr
     * @param charset
     * @return
     */
    public static String transformEncoding(String oldStr, Charset charset) {
        if(Strings.isNullOrEmpty(oldStr)) {
            return oldStr;
        }
        try {
            if (oldStr.equals(new String(oldStr.getBytes(Charsets.ISO_8859_1), Charsets.ISO_8859_1))) {
                return new String(oldStr.getBytes(Charsets.ISO_8859_1), charset);
            }
            if (oldStr.equals(new String(oldStr.getBytes(Charsets.US_ASCII), Charsets.US_ASCII))) {
                return new String(oldStr.getBytes(Charsets.US_ASCII), charset);
            }
            if (oldStr.equals(new String(oldStr.getBytes(Charsets.UTF_8), Charsets.UTF_8))) {
                return new String(oldStr.getBytes(Charsets.UTF_8), charset);
            }
        } catch (Exception e) {
        }
        return oldStr;
    }

    /**
     * 列表分片
     * @param list  全列表
     * @param fragmentSize 单片内容大小
     * @param <T>
     * @return
     */
    public static <T> List<List<T>> fragmentList(List<T> list , int fragmentSize){
        int listSize = list.size();
        List<List<T>> fragmentList = new ArrayList<>();
        int fragmentCount = Double.valueOf(Math.ceil(list.size() / Float.valueOf(fragmentSize))).intValue();
        for(int curFragment=0;curFragment<fragmentCount;curFragment++) {
            int max = (curFragment + 1) * fragmentSize;
            if (max > listSize) max = listSize;
            fragmentList.add(list.subList(curFragment * fragmentSize, max));
        }
        return fragmentList;
    }

    /**
     * 计算两点（经纬度）距离
     * @param lat1
     * @param lon1
     * @param lat2
     * @param lon2
     * @return
     */
    public static double computeDistance(double lat1, double lon1, double lat2, double lon2) {
        // 地球半径6371公里
        double R = 6371;
        double dLat = (lat2 - lat1) * Math.PI / 180;
        double dLon = (lon2 - lon1) * Math.PI / 180;
        double a = Math.sin(dLat / 2) * Math.sin(dLat / 2)
                + Math.cos(lat1 * Math.PI / 180)
                * Math.cos(lat2 * Math.PI / 180) * Math.sin(dLon / 2)
                * Math.sin(dLon / 2);
        double distance = (2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))) * R;
        DecimalFormat df = new DecimalFormat("#.00");
        return Double.parseDouble(df.format(distance));
    }

    public static JSONObject httpsRequest(String requestUrl, String requestMethod, String outputStr) {
        JSONObject jsonObject = null;
        String result = null;

        try {
            result = MessageUtil.sendWeiXinMessage(requestUrl, requestMethod, outputStr);
            if (StringUtils.isEmpty(result)) {
                return null;
            }

            jsonObject = JSONObject.parseObject(result);
        } catch (Exception var6) {
            log.error(result + "\n" + var6.getMessage(), var6);
        }

        return jsonObject;
    }
}
