package cn.lyy.merchant.util;

import lombok.Data;
import lombok.Getter;
import lombok.ToString;

import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;

/**
 * <p>Desc: 日期工具类</p>
 */
public class DateUtils {

    @Getter
    public enum DatePattern {
        yyyyMMdd("yyyyMMdd"),
        yyyyMM("yyyyMM"),
        yyyyMMddHHmmss("yyyyMMddHHmmss"),
        yyyyMMddHHmmssSSS("yyyyMMddHHmmssSSS"),
        yyyy_MM("yyyy-MM"),
        yyyy_MM_dd("yyyy-MM-dd"),
        yyyy_MM_dd_HH("yyyy-MM-dd-HH"),
        yyyy_MM_ddHH("yyyy-MM-dd HH"),
        yyyy_MM_dd_HH_mm_ss("yyyy-MM-dd HH:mm:ss"),
        yyyy_MM_dd_HH_mm_ss_SSS("yyyy-MM-dd HH:mm:ss:SSS"),
        MM_dd_HH_mm_ss("MM-dd HH:mm:ss"),
        yyyy_MM_dd_HHmm("yyyy年MM月dd日 HH:mm"),
        HH_mm("HH:mm"),
        yyyy_MM_dd_T_HH_mm_ss_SSS_Z("yyyy-MM-d'T'HH:mm:ss.SSS'Z'")
        ;

        private String pat;
        DatePattern(String pat) { this.pat = pat; }
    }

    /**
     * 获取当前时间
     * @return
     */
    public static LocalDateTime now() {
        return LocalDateTime.now();
    }

    /**
     * 获取当天时间
     * @return
     */
    public static LocalDate nowDate() {
        return LocalDate.now();
    }


    /**
     * 日期转字符串
     * @param date
     * @param pattern
     * @return
     */
    public static String dateFormat(LocalDateTime date, DatePattern pattern) {
        String format = null;
        try {
            format = date.format(DateTimeFormatter.ofPattern(pattern.pat));
        } catch (Exception e) {
        }
        return format;
    }

    /**
     * 日期转字符串
     * @param date
     * @param pattern
     * @return
     */
    public static String dateFormat(LocalDate date, DatePattern pattern) {
        String format = null;
        try {
            format = date.format(DateTimeFormatter.ofPattern(pattern.pat));
        } catch (Exception e) {
        }
        return format;
    }

    public static String dateFormat(Date date, DatePattern pattern) {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern.pat);
        String format = null;
        try {
            format = sdf.format(date);
        } catch (Exception e) {
        }
        return format;
    }

    /**
     * 字符串转日期
     * @param dateFormat
     * @param pattern
     * @return
     */
    public static Date parse(String dateFormat, DatePattern pattern) {
        if(Objects.isNull(dateFormat) || dateFormat.isEmpty()) {
            return null;
        }
        Date date = null;
        SimpleDateFormat sdf = new SimpleDateFormat(pattern.pat);
        try {
            date = sdf.parse(dateFormat);
        } catch (Exception e) {
        }
        return date;
    }

    /**
     * 字符串转日期
     * @param dateFormat
     * @param pattern
     * @return
     */
    public static LocalDateTime dateParse(String dateFormat, DatePattern pattern) {
        if(Objects.isNull(dateFormat) || dateFormat.isEmpty()) {
            return null;
        }
        LocalDateTime parse = null;
        try {
            parse = LocalDateTime.parse(dateFormat, DateTimeFormatter.ofPattern(pattern.pat));
        } catch (Exception e) {

        }
        return parse;
    }

    public static LocalDate localDateParse(String dateFormat, DatePattern pattern) {
        if(Objects.isNull(dateFormat) || dateFormat.isEmpty()) {
            return null;
        }
        LocalDate parse = null;
        try {
            parse = LocalDate.parse(dateFormat, DateTimeFormatter.ofPattern(pattern.pat));
        } catch (Exception e) {

        }
        return parse;
    }


    /**
     * 添加指定年数
     * @param date
     * @param years
     * @return
     */
    public static LocalDateTime addYear(LocalDateTime date, long years) {
        return date.plusYears(years);
    }

    /**
     * 添加指定月份数
     * @param date
     * @param months
     * @return
     */
    public static LocalDate addMonth(LocalDate date, long months) {
        return date.plusMonths(months);
    }

    /**
     * 添加指定月份数
     * @param date
     * @param months
     * @return
     */
    public static LocalDateTime addMonth(LocalDateTime date, long months) {
        return date.plusMonths(months);
    }

    /**
     * 添加指定月份数
     * @param date
     * @param months
     * @return
     */
    public static Date addMonth(Date date, int months) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.DAY_OF_YEAR, months);
        return c.getTime();
    }

    /**
     * 添加指定天数
     * @param date
     * @param days
     * @return
     */
    public static LocalDateTime addDay(LocalDateTime date, long days) {
        return date.plusDays(days);
    }

    /**
     * 添加指定天数
     * @param date
     * @param days
     * @return
     */
    public static LocalDate addDay(LocalDate date, long days) {
        return date.plusDays(days);
    }

    /**
     * 添加指定天数
     * @param date
     * @param days
     * @return
     */
    public static Date addDay(Date date, int days) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.DAY_OF_MONTH, days);
        return c.getTime();
    }

    /**
     * 添加指定小时数
     * @param date
     * @param hours
     * @return
     */
    public static LocalDateTime addHour(LocalDateTime date, long hours) {
        return date.plusHours(hours);
    }


    /**
     * 添加指定分钟数
     * @param date
     * @param minutes
     * @return
     */
    public static LocalDateTime addMinutes(LocalDateTime date, long minutes) {
        return date.plusMinutes(minutes);
    }

    /**
     * localdatetime 转为date
     * @param localDateTime
     * @return
     */
    public static Date localDateTimeToDate(LocalDateTime localDateTime) {
        if(null == localDateTime) {
            return Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant());
        }
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * localDate 转为date
     * @param localDate
     * @return
     */
    public static Date localDateToDate(LocalDate localDate) {
        if(null == localDate) {
            return Date.from(LocalDate.now().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
        }
        return Date.from(localDate.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * date 转为 LocalDateTime
     * @param date
     * @return
     */
    public static LocalDateTime dateToLocalDateTime(Date date) {
        if(null == date) {
            return LocalDateTime.now();
        }
        return LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
    }

    /**
     * date 转为 LocalDateTime
     * @param date
     * @return
     */
    public static LocalDate dateToLocalDate(Date date) {
        if(null == date) {
            return LocalDate.now();
        }
        return LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault()).toLocalDate();
    }

    /**
     * 添加指定秒数
     * @param date
     * @param hours)
     * @return
     */
    public static LocalDateTime addHours(LocalDateTime date, long hours) {
        return date.plusHours(hours);
    }

    /**
     * 添加指定小时
     * @param date
     * @param hours
     * @return
     */
    public static Date addHours(Date date, int hours) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.HOUR_OF_DAY, hours);
        return c.getTime();
    }

    /**
     * 毫秒转为描述性的字符串
     * @param milseconds
     * @return
     */
    public static String parseMilseconds(long milseconds) {
        long mils = milseconds % 1000;

        // total seconds
        milseconds /= 1000;
        long seconds = milseconds % 60;

        // total minutes
        milseconds /= 60;
        long minutes = milseconds % 60;

        // total hours
        milseconds /= 60;
        long hours = milseconds % 24;

        return hours + " hours " + minutes + " minutes " + seconds + " seconds " + mils + " milseconds";
    }

    /**
     * 当月第一天
     * @return
     */
    public static LocalDate firstDayOfMonth() {
        LocalDate date = nowDate();
        return LocalDate.of(date.getYear(), date.getMonth(), 1);
    }

    /**
     * 当月最后一天
     * @return
     */
    public static LocalDate lastDayOfMonth() {
        LocalDate date = nowDate();
        return date.with(TemporalAdjusters.lastDayOfMonth());
    }

    /**
     * 当月第一天
     * @return
     */
    public static LocalDate firstDayOfMonth(LocalDate yearAndMonth) {
        return LocalDate.of(yearAndMonth.getYear(), yearAndMonth.getMonth(), 1);
    }

    /**
     * 当月最后一天
     * @return
     */
    public static LocalDate lastDayOfMonth(LocalDate yearAndMonth) {
        return yearAndMonth.with(TemporalAdjusters.lastDayOfMonth());
    }

    /**
     * 计算日期时间差
     * @param fromDate
     * @param toDate
     * @return
     */
    public static Period dateBetween(LocalDate fromDate, LocalDate toDate) {
        return Period.between(fromDate, toDate);
    }

    /**
     * 计算日期时间差
     * @param fromDate
     * @param toDate
     * @return
     */
    public static Long daysBetween(LocalDate fromDate, LocalDate toDate) {
        return ChronoUnit.DAYS.between(fromDate, toDate);
    }

    /**
     * 计算分钟时间差
     * @param fromDate
     * @param toDate
     * @return
     */
    public static Long minutesBetween(LocalDateTime fromDate, LocalDateTime toDate) {
        return ChronoUnit.MINUTES.between(fromDate, toDate);
    }

    /**
     * 计算小时时间差
     * @param fromDate
     * @param toDate
     * @return
     */
    public static Long hoursBetween(LocalDateTime fromDate, LocalDateTime toDate) {
        return ChronoUnit.HOURS.between(fromDate, toDate);
    }

    /**
     * 两个日期相差的天数，不算时分秒
     *
     * @param from 开始日期
     * @param to   结束日期
     * @return
     */
    public static int daysBetween(String from, String to) {
        if(from == null || to == null)
            return 0;
        long fromL = parse(from, DatePattern.yyyyMMdd).getTime() / (1000 * 24 * 60 * 60);
        long toL = parse(to, DatePattern.yyyyMMdd).getTime() / (1000 * 24 * 60 * 60);
        return (int) (toL - fromL);
    }

    public static int daysBetween(Date from, Date to) {
        if(from == null || to == null)
            return 0;
        long fromL = from.getTime() / (1000 * 24 * 60 * 60);
        long toL = to.getTime() / (1000 * 24 * 60 * 60);
        return (int) (toL - fromL);
    }

    /**
     * 日期查询条件，返回开始和结束时间，
     * @param type  天、周、月 分类
     * @param offset  0表示当天、周、月，向前查是负数，没有正数
     * by mjl
     */
    public static DateQueryResult queryCondition(String type , int offset){
        LocalDate today = LocalDate.now();
        LocalDate beg = today;
        LocalDate end = today;
        switch (type){
            case "day":
                beg = today.plusDays(offset);
                end = beg;
                break;
            case "week":
                beg = today.plusWeeks(offset);
                beg = beg.minusDays(beg.getDayOfWeek().getValue()-1);
                if(offset == 0) {
                    end = today;
                } else {
                    end = beg.plusDays(6);
                }
                break;
            case "month":
                beg = today.plusMonths(offset);
                beg = beg.minusDays(beg.getDayOfMonth()-1);
                if(offset == 0) {
                    end = today;
                }
                else {
                    end = beg.plusMonths(1).plusDays(-1);
                }
                break;
        }

        DateQueryResult result = new DateQueryResult();
        result.setHasNextOffset(offset < 0);
        result.setHasPrevOffset(true);
        result.setCurOffset(offset);
        result.setNextOffset(offset >= 0 ? 0 : offset + 1);
        result.setPrevOffset(offset - 1);
        result.setType(type);
        result.setBegDate(beg);
        result.setEndDate(end);
        return result;

    }

    @Data
    @ToString
    public static class DateQueryResult{
        String type;
        boolean hasPrevOffset;
        boolean hasNextOffset;
        int prevOffset;
        int nextOffset;
        int curOffset;
        LocalDate begDate;
        LocalDate endDate;
    }

    /**
     * 时间戳转localdatetime
     * @param timestamp
     * @return
     */
    public static LocalDateTime timestampToLocalDateTime(long timestamp) {
        Instant instant = Instant.ofEpochMilli(timestamp);
        ZoneId zone = ZoneId.systemDefault();
        return LocalDateTime.ofInstant(instant, zone);
    }

}
