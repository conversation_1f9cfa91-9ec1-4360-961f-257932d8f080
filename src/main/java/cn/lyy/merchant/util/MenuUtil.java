package cn.lyy.merchant.util;


import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;


/**
 * 自定义菜单工具类
 */
@Slf4j
public class MenuUtil {

    // 菜单创建（POST）
    public final static String menu_create_url = "https://api.weixin.qq.com/cgi-bin/menu/create?access_token=ACCESS_TOKEN";

    // 菜单查询（GET）
    public final static String menu_get_url = "https://api.weixin.qq.com/cgi-bin/menu/get?access_token=ACCESS_TOKEN";

    // 菜单删除（GET）
    public final static String menu_delete_url = "https://api.weixin.qq.com/cgi-bin/menu/delete?access_token=ACCESS_TOKEN";

    /**
     * 创建菜单
     *
     * @param accessToken 凭证
     * @return true成功 false失败
     */
    public static boolean createMenu(String menuConfig, String accessToken) {
        log.info("创建菜单...参数:{}", menuConfig);
        String url = menu_create_url.replace("ACCESS_TOKEN", accessToken);
        // 发起POST请求创建菜单
        JSONObject jsonObject = CommonUtil.httpsRequest(url, "POST", menuConfig);
        if (null != jsonObject) {
            log.info("创建菜单结果：{}", jsonObject.toString());
            return jsonObject.getIntValue("errcode") == 0;
        }
        return false;
    }

    /**
     * 查询菜单
     *
     * @param accessToken 凭证
     * @return
     */
    public static JSONObject getMenu(String accessToken) {
        // 发起GET请求查询菜单
        String requestUrl = menu_get_url.replace("ACCESS_TOKEN", accessToken);
        JSONObject jsonObject = CommonUtil.httpsRequest(requestUrl, "GET", null);
        if (jsonObject != null) {
            log.debug("查询菜单结果:{}", jsonObject.toString());
        }
        return jsonObject;
    }

    /**
     * 删除菜单
     *
     * @param accessToken 凭证
     * @return true成功 false失败
     */
    public static boolean deleteMenu(String accessToken) {
        // 发起GET请求删除菜单
        String requestUrl = menu_delete_url.replace("ACCESS_TOKEN", accessToken);
        JSONObject jsonObject = CommonUtil.httpsRequest(requestUrl, "GET", null);
        if (jsonObject != null) {
            log.info("删除菜单结果:{}", jsonObject.toString());
            return jsonObject.getIntValue("errcode") == 0;
        }
        return false;
    }

}
