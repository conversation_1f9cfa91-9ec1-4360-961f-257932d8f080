package cn.lyy.merchant.util;

import javax.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/09/02
 */
@Component
public class SAASPermissionUtil {

    public static final int LOCK_MODE_NORMAL = 0;
    public static final int LOCK_MODE_LOCK_ENTRY = 1;
    public static final int LOCK_MODE_LOCK_ENTRY_AND_BUTTON = 2;
    private static final Logger log = LoggerFactory.getLogger(SAASPermissionUtil.class);

    // saas权限判断全局开关
    @Value("${saas.permission.enabled:true}")
    private Boolean saasPermissionEnabled;

    // 标准版角色加锁模式：0-按标准版角色处理 1-只锁入口，不锁按钮和API 2-只锁入口和按钮，不锁API
    @Value("${saas.permission.lock-mode:0}")
    private Integer saasPermissionLockMode;

    private static SAASPermissionUtil saasPermissionUtil;

    @PostConstruct
    public void init() {
        saasPermissionUtil = this;
        log.info("saas权限判断全局开关:{},加锁模式:{}", saasPermissionUtil.saasPermissionEnabled, saasPermissionUtil.saasPermissionLockMode);
    }

    public static boolean enabled() {
        return saasPermissionUtil.saasPermissionEnabled;
    }

    public static boolean lockEntry() {
        return saasPermissionUtil.saasPermissionLockMode == LOCK_MODE_LOCK_ENTRY;
    }

    public static boolean lockEntryAndButton() {
        return saasPermissionUtil.saasPermissionLockMode == LOCK_MODE_LOCK_ENTRY_AND_BUTTON;
    }
}
