package cn.lyy.merchant.util;

import cn.lyy.base.dto.Pagination;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 类描述：
 * <p>
 *
 * <AUTHOR>
 * @since 2020/12/12 11:31
 */
public class PageConverter {

    public static PageInfo convert(Pagination pagination) {
        if (pagination == null) {
            return null;
        }
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNum(pagination.getPage());
        pageInfo.setPageSize(pagination.getPageSize());
        pageInfo.setPages(pagination.getMaxPage());
        pageInfo.setTotal(pagination.getTotal());
        pageInfo.setList(pagination.getItems());
        return pageInfo;
    }

    public static <T> PageInfo<T> fromPageInfo(PageInfo<?> pageInfo, List<T> list) {
        PageInfo<T> page = new PageInfo<>();
        page.setPageNum(pageInfo.getPageNum());
        page.setPageSize(pageInfo.getPageSize());
        page.setSize(pageInfo.getSize());
        page.setPages(pageInfo.getPages());
        page.setTotal(pageInfo.getTotal());
        page.setList(list);
        return page;
    }
}
