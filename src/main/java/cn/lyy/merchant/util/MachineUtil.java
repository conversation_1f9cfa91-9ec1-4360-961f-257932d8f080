package cn.lyy.merchant.util;

import cn.lyy.equipment.dto.machine.Button;
import cn.lyy.equipment.dto.machine.Param;
import cn.lyy.equipment.dto.machine.Setting;
import lombok.extern.slf4j.Slf4j;

import java.io.UnsupportedEncodingException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2017/3/3.
 */
@Slf4j
public class MachineUtil {
    public static void decode(Setting setting, Map<String, Object> paramMap) {
        if(paramMap == null || paramMap.isEmpty()) {
            return;
        }

        for (Map.Entry<String, Object> entry : paramMap.entrySet()) {
            if(entry.getKey().equals("uniqueCode")) {
                continue;
            }

            int id = Integer.parseInt(entry.getKey());
            String value = entry.getValue().toString();

            Param param = getParamById(setting, id);
            if(param != null) {
                if(param.getComponentValueType().equals("int") || param.getComponentValueType().equals("int2") || param.getComponentValueType().equals("negativeint")) {
                    param.setComponentValue(
                        String.valueOf(
                            YTQUtils.setOptions(
                                Integer.parseInt(param.getComponentValue()),
                                value,
                                Integer.parseInt(param.getComponentValueRange().getMin()),
                                Integer.parseInt(param.getComponentValueRange().getMax())
                            )
                        )
                    );
                } else {
                    param.setComponentValue(
                        String.valueOf(
                            YTQUtils.setFloatOptions(
                                Float.parseFloat(param.getComponentValue()),
                                value,
                                Float.parseFloat(param.getComponentValueRange().getMin()),
                                Float.parseFloat(param.getComponentValueRange().getMax())
                            )
                        )
                    );
                }
            }
        }
    }

    public static byte[] getBytes(Setting setting) {
        byte[] packet = new byte[setting.getParamsLength() + 2];
        packet[0] = (byte) setting.getMachineTypeCode();
        packet[1] = (byte) setting.getParamsLength();
        toBytes(packet, 2, setting.getParams());

        return packet;
    }

    public static Button buttonDecode(Setting setting, Map<String, Object> paramMap) {
        if(paramMap == null || paramMap.isEmpty()) {
            return null;
        }

        int functionCode = Integer.parseInt(paramMap.get("functionCode").toString());
        Button button = getButtonByFunctionCode(setting, functionCode);
        if(button != null) {
            for (Map.Entry<String, Object> entry : paramMap.entrySet()) {
                if (entry.getKey().equals("uniqueCode") || entry.getKey().equals("functionCode")) {
                    continue;
                }

                int id = Integer.parseInt(entry.getKey());
                String value = entry.getValue().toString();
                Param param = getParamById(button, id);
                if(param != null) {
                    if(param.getComponentValueType().equals("int") || param.getComponentValueType().equals("int2") || param.getComponentValueType().equals("negativeint")) {
                        param.setComponentValue(
                                String.valueOf(
                                        YTQUtils.setOptions(
                                                Integer.parseInt(param.getComponentValue()),
                                                value,
                                                Integer.parseInt(param.getComponentValueRange().getMin()),
                                                Integer.parseInt(param.getComponentValueRange().getMax())
                                        )
                                )
                        );
                    } else if("GBK".equals(param.getComponentValueType()) || "GB2312".equals(param.getComponentValueType())
                            || "UTF-8".equals(param.getComponentValueType())){
                        try {
                            param.setComponentValue(new String(
                                    YTQUtils.setCharOptions(param.getComponentValue(), value, param.getComponentValueType(), 1 , param.getLength()).getBytes(param.getComponentValueType()),
                                    param.getComponentValueType()));
                        } catch (UnsupportedEncodingException e) {
                            e.printStackTrace();
                        }
                    } else {
                        param.setComponentValue(
                                String.valueOf(
                                        YTQUtils.setFloatOptions(
                                                Float.parseFloat(param.getComponentValue()),
                                                value,
                                                Float.parseFloat(param.getComponentValueRange().getMin()),
                                                Float.parseFloat(param.getComponentValueRange().getMax())
                                        )
                                )
                        );
                    }
                }
            }
        }

        return button;
    }

    public static byte[] getButtonBytes(Button button, int machineTypeCode) {
        byte[] packet = new byte[button.getParamsLength() + 2];
        packet[0] = (byte) machineTypeCode;
        packet[1] = (byte) button.getParamsLength();

        toBytes(packet, 2, button.getParams());

        return packet;
    }

    private static Param getParamById(Setting setting, int id) {
        for(Param param : setting.getParams()) {
            if(param.getId() == id) {
                return param;
            }
        }

        return null;
    }

    private static Button getButtonByFunctionCode(Setting setting, int functionCode) {
        for(Button button : setting.getButtons()) {
            if(button.getFunctionCode() == functionCode) {
                return button;
            }
        }

        return null;
    }

    private static Param getParamById(Button button, int id) {
        for(Param param : button.getParams()) {
            if(param.getId() == id) {
                return param;
            }
        }

        return null;
    }

    private static void toBytes(byte[] packet, int index, List<Param> params) {
        for(Param param : params) {
            if("GBK".equals(param.getComponentValueType()) || "GB2312".equals(param.getComponentValueType())
                    || "UTF-8".equals(param.getComponentValueType())) {
                byte[] bytes = null;
                try {
                    bytes = param.getComponentValue().getBytes(param.getComponentValueType());
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                }
                log.debug("修改后的值对应的字节数组 -> {}" ,Arrays.toString(bytes));
                System.arraycopy(bytes, 0, packet, index, bytes.length);
                index += param.getLength();
                continue;
            }

            if(param.getLength() == 1) {
            	if(param.getComponentValueType().equals("float2")) {
            		// 乘10方式
            		packet[index] = (byte) ((int) (Float.parseFloat(param.getComponentValue()) * 10));
            	} else {
            		packet[index] = (byte) Integer.parseInt(param.getComponentValue());
            	}
            	index += 1;
            } else {
                if(param.getComponentValueType().equals("int") || param.getComponentValueType().equals("negativeint")) {
                    switch (param.getLength()) {
                        case 2:
                            YTQUtils.into2Packet(packet, index, Integer.parseInt(param.getComponentValue()));
                            index += 2;
                            break;
                        case 3:
                            YTQUtils.into3Packet(packet, index, Integer.parseInt(param.getComponentValue()));
                            index += 3;
                            break;
                        case 4:
                            YTQUtils.into4Packet(packet, index, Integer.parseInt(param.getComponentValue()));
                            index += 4;
                            break;
                    }
                } else if(param.getComponentValueType().equals("int2")) {
                    int value, first, second, third, fourth;
                    switch (param.getLength()) {
                        case 2:
                            value = Integer.parseInt(param.getComponentValue());
                            first = value / 100;
                            second = value - first * 100;

                            packet[index] = (byte) second;
                            index += 1;
                            packet[index] = (byte) first;
                            index += 1;
                            break;
                        case 3:
                            value = Integer.parseInt(param.getComponentValue());
                            first = value / 10000;
                            second = (value - first * 10000) / 100;
                            third = value - first * 10000 - second * 100;

                            packet[index] = (byte) third;
                            index += 1;
                            packet[index] = (byte) second;
                            index += 1;
                            packet[index] = (byte) first;
                            index += 1;
                            break;
                        case 4:
                            value = Integer.parseInt(param.getComponentValue());
                            first = value / 1000000;
                            second = (value - first * 1000000) / 10000;
                            third = (value - first * 1000000 - second * 10000) / 100;
                            fourth = value - first * 1000000 - second * 10000 - third * 100;

                            packet[index] = (byte) fourth;
                            index += 1;
                            packet[index] = (byte) third;
                            index += 1;
                            packet[index] = (byte) second;
                            index += 1;
                            packet[index] = (byte) first;
                            index += 1;
                            break;
                    }
                } else {
                    switch(param.getComponentValueType()) {
                        case "float1":
                            // 高低位方式
                            YTQUtils.floatTo2Packet(packet, index, Float.parseFloat(param.getComponentValue()));
                            break;
                        case "float2":
                            // 乘10方式
                            YTQUtils.into2Packet(packet, index, (int) (Float.parseFloat(param.getComponentValue()) * 10));
                            break;
                    }

                    index += 2;
                }
            }
        }
    }
}
