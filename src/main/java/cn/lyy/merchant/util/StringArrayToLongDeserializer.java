package cn.lyy.merchant.util;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 将string 数组转换成List<Long>对象
 *
 * <AUTHOR>
 * @create 2021/6/25 15:14
 */
@Slf4j
public class StringArrayToLongDeserializer extends JsonDeserializer<List<Long>> {

    @Override
    public List<Long> deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException, JsonProcessingException {
        List<Long> list = null;
        if(jsonParser.getCurrentToken() == JsonToken.START_ARRAY){
            list = new ArrayList<>();
            while (jsonParser.nextToken() != JsonToken.END_ARRAY){
                String value = jsonParser.getValueAsString();
                Long longValue = jsonParser.getValueAsLong();
                if(!String.valueOf(longValue).equals(value)){
                    throw new IllegalArgumentException("不合法的参数,参数转换异常");
                }
                list.add(jsonParser.getValueAsLong());
            }
        }
        return list;
    }
}
