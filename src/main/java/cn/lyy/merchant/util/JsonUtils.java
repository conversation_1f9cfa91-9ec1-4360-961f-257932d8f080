package cn.lyy.merchant.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import java.util.List;

/**
 * <p>Title:lyyopen_admin</p>
 * <p>Desc: Json 工具类</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/6/21
 */
public class JsonUtils {

    /**
     * bean to json
     * @param obj
     * @return
     */
    public static String toJsonString(Object obj) {
        return JSON.toJSONString(obj);
    }

    /**
     * json to JSONObject
     * @param json
     * @return
     */
    public static JSONObject parseObject(String json) {
        return JSON.parseObject(json);
    }

    /**
     * json to bean
     * @param json
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T> T parse(String json, Class<T> clazz) {
        return JSON.parseObject(json, clazz);
    }

    /**
     * json to bean list
     * @param json
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T> List<T> parseArray(String json, Class<T> clazz) {
        return JSON.parseArray(json, clazz);
    }
}
