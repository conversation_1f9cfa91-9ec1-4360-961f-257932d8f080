package cn.lyy.merchant;

import cn.lyy.merchant.dto.member.PayoutWelfareDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Optional;


public class DemoTest {

	public static void main(String[] args) {

		String v = "123";
		Long l = 123L;
		System.out.println(l.toString().equals(v));

	}
//	@Test
//	void contextLoads() {
//		User user = new User("<EMAIL>", "1234","Developer");
//		user.setPosition("Developer");
//		String position = Optional.ofNullable(user)
//				.map(u -> u.getPosition()).orElse("default");
//
//		Assert.notNull(position,"position不为空");
//		//assertEquals(position, user.getPosition().get());
//	}


}

