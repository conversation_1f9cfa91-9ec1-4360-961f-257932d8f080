package cn.lyy.merchant.service.commodity.impl;

import cn.lyy.merchant.dto.request.FeeCommodityDTO;
import cn.lyy.merchant.service.commodity.FeeRuleService;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;


@SpringBootTest
@Slf4j
class FeeRuleServiceImplTest {

    @Autowired
    private FeeRuleService feeRuleService;

    @Test
    void listByCondition() {
        Long distributorId=1062179L;
        Long groupId=1028498L;
        Long equipmentTypeId=null;
        Long equipmentId=1031020L;
        String categoryCode=null;
        String classifyCode=null;
        Boolean isCompose=false;
        List<FeeCommodityDTO> feeCommodityDTOS = feeRuleService
            .listByCondition(distributorId, groupId, equipmentTypeId, equipmentId, categoryCode, classifyCode, isCompose);
        Assertions.assertThat(feeCommodityDTOS).isNotEmpty();
    }
}