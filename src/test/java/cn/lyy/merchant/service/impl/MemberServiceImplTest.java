package cn.lyy.merchant.service.impl;

import static org.assertj.core.api.Assertions.assertThat;

import cn.lyy.merchant.ResourceUtil;
import cn.lyy.open.order.dto.request.v2.OrderGoodsDTO;
import cn.lyy.open.order.dto.request.v2.OrderRefundRequest.RefundOrderGoodsRequest;
import com.fasterxml.jackson.core.type.TypeReference;
import java.math.BigDecimal;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ExtendWith(SpringExtension.class)
public class MemberServiceImplTest {

    @Test
    void generateRefundGoods() {
        List<OrderGoodsDTO> goodsList  = ResourceUtil.readJson("asset/退款/多商品申诉退款/goods.json", new TypeReference<List<OrderGoodsDTO>>() {
        });
        List<RefundOrderGoodsRequest> goodsRequests = MemberServiceImpl.generateRefundGoods(new BigDecimal("0.10"), goodsList);
        assertThat(goodsRequests.size()).isEqualTo(1);
        assertThat(goodsRequests.get(0).getRefundAmount().toString()).isEqualTo("0.10");
    }
    @Test
    void generateRefundGoods2() {
        List<OrderGoodsDTO> goodsList  = ResourceUtil.readJson("asset/退款/多商品申诉退款/goods.json", new TypeReference<List<OrderGoodsDTO>>() {
        });
        List<RefundOrderGoodsRequest> goodsRequests = MemberServiceImpl.generateRefundGoods(new BigDecimal("0.20"), goodsList);
        assertThat(goodsRequests.size()).isEqualTo(2);
        assertThat(goodsRequests.get(0).getRefundAmount().toString()).isEqualTo("0.10");
        assertThat(goodsRequests.get(1).getRefundAmount().toString()).isEqualTo("0.10");
    }

    @Test
    void generateRefundGoods3() {
        List<OrderGoodsDTO> goodsList  = ResourceUtil.readJson("asset/退款/多商品申诉退款/goods.json", new TypeReference<List<OrderGoodsDTO>>() {
        });
        List<RefundOrderGoodsRequest> goodsRequests = MemberServiceImpl.generateRefundGoods(new BigDecimal("0.15"), goodsList);
        assertThat(goodsRequests.size()).isEqualTo(2);
        assertThat(goodsRequests.get(0).getRefundAmount().toString()).isEqualTo("0.10");
        assertThat(goodsRequests.get(1).getRefundAmount().toString()).isEqualTo("0.05");
    }

}
