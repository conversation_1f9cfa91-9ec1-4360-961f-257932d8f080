package cn.lyy.merchant.controller;

import cn.lyy.merchant.dto.request.*;
import com.alibaba.fastjson.JSON;
import com.lyy.commodity.rpc.constants.ValueUnitEnum;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.hamcrest.CoreMatchers.is;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest
@AutoConfigureMockMvc
public class DiscountRuleControllerTest {


    @Autowired
    private MockMvc mockMvc;


    // 王海 个人用户
    private final  String username = "15018442322";
    private final String adUserId = "1220509";

    //private final String groupId = "1028493";  // 1119新建   address 仲东学院
    private final String groupId = "1028421";   // 11

    private final String equipmentTypeId = "1000078"; // 充电桩
    private final String XYJ_equipmentTypeId = "1000028"; // 洗衣机


    @Test
    public void getDiscountRule() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/discount/rule/getRuleByGroup")
                .param("groupId","1028498")
                .param("equipmentTypeId",XYJ_equipmentTypeId)
                ;
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);
        //requestBuilder.content(JSON.toJSONString(classify));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));

    }

    @Test
    public void addDiscountRule() throws Exception {

        DiscountRuleSaveDTO discountRuleSaveDTO = new DiscountRuleSaveDTO();
        discountRuleSaveDTO.setEquipmentTypeId(Long.valueOf(1001286));
        discountRuleSaveDTO.setEquipmentTypeValue(null);
        discountRuleSaveDTO.setGroupId(Long.valueOf(1028167L));
        // 冲 0.01 送 100元
        //discountRuleSaveDTO.setDetailId(22100l);
        discountRuleSaveDTO.setPrice(new BigDecimal(0.02));
        discountRuleSaveDTO.setUnitValue(new BigDecimal(2));
        discountRuleSaveDTO.setUnit(ValueUnitEnum.COIN.getId());

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/discount/rule/saveOrUpdate");

        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);
        requestBuilder.content(JSON.toJSONString(discountRuleSaveDTO));

        this.mockMvc.perform(requestBuilder)
                .andDo(print())
                .andExpect(status().isOk());

    }

    @Test
    public void batchAddDiscountRule() throws Exception {

        Long groupId  = 1028413l;  //大学城南

        BatchDiscountRuleSaveDTO batchDiscountRuleSaveDTO = new BatchDiscountRuleSaveDTO();

        List<Long> codes = new ArrayList<>();
        codes.add(groupId);

        List<BatchDiscountRuleSaveDTO.DiscountRuleDetailDTO> list = new ArrayList<>();
        BatchDiscountRuleSaveDTO.DiscountRuleDetailDTO discountRuleSaveDTO = new BatchDiscountRuleSaveDTO.DiscountRuleDetailDTO();
        //discountRuleSaveDTO.setDetailId(17600l);
        discountRuleSaveDTO.setPrice(new BigDecimal(2));
        discountRuleSaveDTO.setCoin(new BigDecimal(40));
        //discountRuleSaveDTO.setUnit(ValueUnitEnum.COIN.getId());
        list.add(discountRuleSaveDTO);

        batchDiscountRuleSaveDTO.setCodes(codes);
        batchDiscountRuleSaveDTO.setEquipmentTypeId(1000078l);
        batchDiscountRuleSaveDTO.setDiscountRule(list);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/discount/rule/batchSaveOrUpdate");

        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);
        requestBuilder.content(JSON.toJSONString(batchDiscountRuleSaveDTO));

        this.mockMvc.perform(requestBuilder)
                .andDo(print())
                .andExpect(status().isOk());

    }



    @Test
    public void delDiscountRule() throws Exception {

        RuleDelDTO ruleDelDTO = new RuleDelDTO();
        ruleDelDTO.setDetailId(31703l);
        ruleDelDTO.setGroupId(1028420l);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/discount/rule/del")
                ;
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);
        requestBuilder.content(JSON.toJSONString(ruleDelDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));
    }

    @Test
    public void restoreRule() throws Exception {

        RestroreDefaultDTO restroreDefaultDTO = new RestroreDefaultDTO();
        restroreDefaultDTO.setGroupId(1028499l);
        restroreDefaultDTO.setEquipmentTypeId(1000078l);
        restroreDefaultDTO.setEquipmentType("CDZ");

        String requestBody = JSON.toJSONString(restroreDefaultDTO);
        System.out.println(requestBody);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/discount/rule/restoreDefault");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);
        requestBuilder.content(requestBody);
        this.mockMvc.perform(requestBuilder)
                .andDo(print())
                .andExpect(status().isOk());

    }



}
