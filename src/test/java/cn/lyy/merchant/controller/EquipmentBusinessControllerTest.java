package cn.lyy.merchant.controller;

import cn.lyy.merchant.dto.request.MerchantEquipmentRequest;
import com.alibaba.fastjson.JSON;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.util.ArrayList;
import java.util.List;

import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest
@AutoConfigureMockMvc
public class EquipmentBusinessControllerTest {


    @Autowired
    private MockMvc mockMvc;


    // 王海 个人用户
    private final  String username = "***********";
    private final String adUserId = "1220509";

    private final String groupId = "1028493";  // 1119新建   address 仲东学院

    @Test
    public void getGroupEquipmentList() throws Exception {

        MerchantEquipmentRequest merchantEquipmentRequest = new MerchantEquipmentRequest();
        merchantEquipmentRequest.setStatus(0);


        List<Long> groupList = new ArrayList<>();
        groupList.add(1028420l);  // 筛选 1119新建
        merchantEquipmentRequest.setGroups(groupList);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/rest/equipment/manager/equipmentList")
                ;
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);
        requestBuilder.content(JSON.toJSONString(merchantEquipmentRequest));
        this.mockMvc.perform(requestBuilder)
                .andDo(print())
                .andExpect(status().isOk());

    }

    @Test
    public void getGroupEquipmentTypeList() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/equipment/group/listEquipmentTypes")
               // .param("groupId",groupId)
                ;
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);

        this.mockMvc.perform(requestBuilder)
                .andDo(print())
                .andExpect(status().isOk());

    }

    @Test
    public void getBatchRegEquipmentType() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/rest/equipment/getBatchRegEquipmentType")
                ;
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username","***********");
        requestBuilder.header("adUserId","1220509");
      //  requestBuilder.content(JSON.toJSONString(merchantEquipmentRequest));
        this.mockMvc.perform(requestBuilder)
                .andDo(print())
                .andExpect(status().isOk());
    }

    @Test
    public void getByUniqueCode() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/rest/equipment/info/byCode")
                .param("uniqueCode","92000285")
                ;
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username","***********");
        requestBuilder.header("adUserId","1220509");
        //  requestBuilder.content(JSON.toJSONString(merchantEquipmentRequest));
        this.mockMvc.perform(requestBuilder)
                .andDo(print())
                .andExpect(status().isOk());
    }

    @Test
    public void getByValue() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/rest/equipment/info/value")
                .param("value","92025833")
                ;
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username","***********");
        requestBuilder.header("adUserId","1220509");
        //  requestBuilder.content(JSON.toJSONString(merchantEquipmentRequest));
        this.mockMvc.perform(requestBuilder)
                .andDo(print())
                .andExpect(status().isOk());
    }

}
