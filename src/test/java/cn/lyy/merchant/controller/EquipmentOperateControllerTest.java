package cn.lyy.merchant.controller;

import cn.lyy.equipment.dto.equipment.RegisterItemDTO;
import cn.lyy.merchant.dto.request.*;
import com.alibaba.fastjson.JSON;
import com.lyy.commodity.rpc.constants.ClassifyCodeEnum;
import com.lyy.commodity.rpc.constants.ValueUnitEnum;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.hamcrest.CoreMatchers.is;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest
@AutoConfigureMockMvc
public class EquipmentOperateControllerTest {

    @Autowired
    private MockMvc mockMvc;

    // 王海 个人用户
    private final  String username = "15018442322";
    private final String adUserId = "1220509";

//    private final Long groupId  = 1028419l; // 穷人住不起的二沙岛
    private final Long groupId  = 1028167L; // 沙面


    @Test
    public void bind() throws Exception {

        EquipmentRegisterDTO equipmentRegisterDTO = new EquipmentRegisterDTO();

        List<RegisterItemDTO> equipments = new ArrayList<>();
        RegisterItemDTO registerItemDTO = new RegisterItemDTO();
        registerItemDTO.setGroupId(groupId);
        registerItemDTO.setCode("92025835");
        registerItemDTO.setGroupNumber(1);
        equipments.add(registerItemDTO);
        equipmentRegisterDTO.setEquipments(equipments);


        equipmentRegisterDTO.setEquipmentType("ETL");
        equipmentRegisterDTO.setGroupId(groupId);
        List<FeeCommodityDTO> feeCommodityDTOList = new ArrayList<>();
//        FeeCommodityDTO feeCommodityDTO = new FeeCommodityDTO();
//        feeCommodityDTO.setPrice(new BigDecimal("4"));
//
//        List<PriceValueDTO> fixPriceValueList = new ArrayList<>();
//        PriceValueDTO fixPriceValueDTO = new PriceValueDTO();
//        fixPriceValueDTO.setValue(new BigDecimal(45));
//        fixPriceValueDTO.setUnit(ValueUnitEnum.MIN.getId());
//        fixPriceValueList.add(fixPriceValueDTO);
//
//        PriceValueDTO coinValueDTO = new PriceValueDTO();
//        coinValueDTO.setValue(new BigDecimal(1));
//        coinValueDTO.setUnit(ValueUnitEnum.COIN.getId());
//        fixPriceValueList.add(coinValueDTO);
//
//        feeCommodityDTO.setPriceValueList(fixPriceValueList);
//        feeCommodityDTO.setClassifyCode(ClassifyCodeEnum.TIME.getCode());
//        feeCommodityDTO.setName("大物件");
//        feeCommodityDTOList.add(feeCommodityDTO);
//
//        // 第二个商品
//        FeeCommodityDTO feeCommodityDTO2 = new FeeCommodityDTO();
//        feeCommodityDTO2.setPrice(new BigDecimal("3"));
//
//        List<PriceValueDTO> fixPriceValueList2 = new ArrayList<>();
//        PriceValueDTO fixPriceValueDTO2 = new PriceValueDTO();
//        fixPriceValueDTO2.setValue(new BigDecimal(35));
//        fixPriceValueDTO2.setUnit(ValueUnitEnum.MIN.getId());
//        fixPriceValueList2.add(fixPriceValueDTO2);
//
//        PriceValueDTO coinValueDTO2 = new PriceValueDTO();
//        coinValueDTO2.setValue(new BigDecimal(2));
//        coinValueDTO2.setUnit(ValueUnitEnum.COIN.getId());
//        fixPriceValueList2.add(coinValueDTO2);
//
//        feeCommodityDTO2.setPriceValueList(fixPriceValueList2);
//        feeCommodityDTO2.setClassifyCode(ClassifyCodeEnum.TIME.getCode());
//        feeCommodityDTO2.setName("标准洗");
//        feeCommodityDTOList.add(feeCommodityDTO2);


        equipmentRegisterDTO.setFeeRule(feeCommodityDTOList);


        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/operate/bind");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);
        requestBuilder.content(JSON.toJSONString(equipmentRegisterDTO));

        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));

    }

    /**
     * 绑定洗衣机加液机
     * @throws Exception
     */
    @Test
    public void bindXYJAndJYJ() throws Exception {

        EquipmentRegisterDTO equipmentRegisterDTO = new EquipmentRegisterDTO();

        List<RegisterItemDTO> equipments = new ArrayList<>();
        RegisterItemDTO registerItemDTO = new RegisterItemDTO();
        registerItemDTO.setGroupId(groupId);
        registerItemDTO.setCode("92025833");
        registerItemDTO.setGroupNumber(3);
        equipments.add(registerItemDTO);
        equipmentRegisterDTO.setEquipments(equipments);


        equipmentRegisterDTO.setEquipmentType("XYJ");
        equipmentRegisterDTO.setGroupId(groupId);
        List<FeeCommodityDTO> feeCommodityDTOList = new ArrayList<>();
        FeeCommodityDTO feeCommodityDTO = new FeeCommodityDTO();
        feeCommodityDTO.setPrice(new BigDecimal("4"));

        List<PriceValueDTO> fixPriceValueList = new ArrayList<>();
        PriceValueDTO fixPriceValueDTO = new PriceValueDTO();
        fixPriceValueDTO.setValue(new BigDecimal(45));
        fixPriceValueDTO.setUnit(ValueUnitEnum.MIN.getId());
        fixPriceValueList.add(fixPriceValueDTO);

        PriceValueDTO coinValueDTO = new PriceValueDTO();
        coinValueDTO.setValue(new BigDecimal(1));
        coinValueDTO.setUnit(ValueUnitEnum.COIN.getId());
        fixPriceValueList.add(coinValueDTO);

        feeCommodityDTO.setPriceValueList(fixPriceValueList);
        feeCommodityDTO.setClassifyCode(ClassifyCodeEnum.TIME.getCode());
        feeCommodityDTO.setName("大物件");
        feeCommodityDTOList.add(feeCommodityDTO);

        // 第二个商品
        FeeCommodityDTO feeCommodityDTO2 = new FeeCommodityDTO();
        feeCommodityDTO2.setPrice(new BigDecimal("3"));

        List<PriceValueDTO> fixPriceValueList2 = new ArrayList<>();
        PriceValueDTO fixPriceValueDTO2 = new PriceValueDTO();
        fixPriceValueDTO2.setValue(new BigDecimal(35));
        fixPriceValueDTO2.setUnit(ValueUnitEnum.MIN.getId());
        fixPriceValueList2.add(fixPriceValueDTO2);

        PriceValueDTO coinValueDTO2 = new PriceValueDTO();
        coinValueDTO2.setValue(new BigDecimal(2));
        coinValueDTO2.setUnit(ValueUnitEnum.COIN.getId());
        fixPriceValueList2.add(coinValueDTO2);

        // 加液机商品
        FeeCommodityDTO jyjFeeCommodityDTO = new FeeCommodityDTO();
        jyjFeeCommodityDTO.setPrice(new BigDecimal("3"));

        List<PriceValueDTO> jyjFixPriceValueList = new ArrayList<>();
        PriceValueDTO jyjFixPriceValueDTO = new PriceValueDTO();
        jyjFixPriceValueDTO.setValue(new BigDecimal(10));
        jyjFixPriceValueDTO.setUnit(ValueUnitEnum.ML.getId());
        jyjFixPriceValueList.add(jyjFixPriceValueDTO);

        jyjFeeCommodityDTO.setPriceValueList(jyjFixPriceValueList);
        jyjFeeCommodityDTO.setClassifyCode(ClassifyCodeEnum.VOLUME.getCode());
        jyjFeeCommodityDTO.setName("加液机商品");
        feeCommodityDTOList.add(jyjFeeCommodityDTO);


        equipmentRegisterDTO.setFeeRule(feeCommodityDTOList);


        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/operate/bind");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);
        requestBuilder.content(JSON.toJSONString(equipmentRegisterDTO));

        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));

    }

    /**
     * 检查设备是否可以注册
     * @throws Exception
     */
    @Test
    public void check() throws Exception {

        EquipmentGetByCodeDTO equipmentGetByCodeDTO = new EquipmentGetByCodeDTO();
        equipmentGetByCodeDTO.setCode("92025833");
        //equipmentGetByCodeDTO.setAttachEquipmentType("JYJ");
//        equipmentGetByCodeDTO.setEquipmentType("XYJ");
//        equipmentGetByCodeDTO.setProtocolId(22404l);
//        equipmentGetByCodeDTO.setProductId(1000463l);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/operate/bind/check");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);
        requestBuilder.content(JSON.toJSONString(equipmentGetByCodeDTO));

        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));

    }
}
