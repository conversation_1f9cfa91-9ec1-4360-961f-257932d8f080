package cn.lyy.merchant.controller;

import cn.lyy.merchant.dto.request.GetTopMessageAndTodoDTO;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <AUTHOR>
 * @description
 * @create 2025/7/22 10:14
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@AutoConfigureMockMvc
public class MerchantControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    private HttpHeaders httpHeaders = new HttpHeaders();

    @Before
    public void setup() {
        httpHeaders.add("username", "13640857002");
        httpHeaders.add("adUserId", "1023678");
    }

    @Test
    public void getTopMessageAndTodo() throws Exception {
        Long adUserId = 1023678L;
        Long merchantId = 1444799L;

        GetTopMessageAndTodoDTO dto = new GetTopMessageAndTodoDTO();
        dto.setMerchantId(merchantId);
        dto.setAdUserId(adUserId);
        ResultActions perform = mockMvc.perform(post("/rest/merchant/getTopMessageAndTodo").headers(httpHeaders)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(dto)));
        perform.andReturn().getResponse().setCharacterEncoding("UTF-8");
        perform.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("0"))
                .andExpect(jsonPath("$.message").value("成功"))
                .andReturn();
    }
}
