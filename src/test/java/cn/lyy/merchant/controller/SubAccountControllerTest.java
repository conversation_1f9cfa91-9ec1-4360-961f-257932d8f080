package cn.lyy.merchant.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.util.Arrays;

import static org.hamcrest.CoreMatchers.is;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * @description:
 * @author: qgw
 * @date on 2021/3/6.
 * @Version: 1.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@AutoConfigureMockMvc
public class SubAccountControllerTest {



    // 王海 个人用户
    private final  String username = "***********";
    private final String adUserId = "1220509";

    @Autowired
    private MockMvc mockMvc;

    @Test
    public void getSubAccountInfos() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/rest/subaccount/equipment");
                //.param("equipmentValue","********");
        //.param("equipmentType","JYJ");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);
        //requestBuilder.content(JSON.toJSONString(classify));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));

    }

    @Test
    public void listSubAccountRole() {
    }

    @Test
    public void listSubAccountResource() {
    }

    @Test
    public void getSubAccountMyRoleAuth() {
    }

    @Test
    public  void saveSubAccountRole() {
    }

    @Test
    public  void deleteSubAccountRole() {
    }

    @Test
    public  void updateSubAccount() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/rest/subaccount/saveOrUpdate");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);
        JSONObject param = new JSONObject();
        param.put("phone", "***********");
        param.put("userName", "测试M");
        param.put("password", "********");

        param.put("groupIds", Arrays.asList(1028497,1028493 ));
        param.put("roleIds", Arrays.asList(1100827 ));
        param.put("id", "1023503");
        requestBuilder.content(JSON.toJSONString(param));


        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));

    }

    @Test
    public void deleteSubAccount() {
    }

    @Test
    public void createSubAccount() throws Exception {
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/rest/subaccount/createSubAccount");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);

        JSONObject info = JSONObject.parseObject("{\"phone\":\"***********\",\"password\":\"123456\",\"roleIds\":[1100704],\"userName\":\"开发A\",\"groupIds\":[1052199],\"divideUserIds\":[1000608]}");

        requestBuilder.content(JSON.toJSONString(info));


        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));

    }

    @Test
    public void validRegister() {
    }
}