package cn.lyy.merchant.controller;

import cn.lyy.base.util.DateUtil;
import cn.lyy.merchant.dto.member.BenefitDetailQueryDTO;
import cn.lyy.merchant.dto.member.BenefitQueryDTO;
import cn.lyy.merchant.dto.member.PayoutWelfareDTO;
import cn.lyy.merchant.dto.request.UserBalanceAdjustReqDTO;
import com.alibaba.fastjson.JSON;
import com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum;
import com.lyy.user.account.infrastructure.constant.BenefitClassifyGroupEnum;
import com.lyy.user.account.infrastructure.constant.TimeScopeEnum;
import com.lyy.user.account.infrastructure.statistics.dto.StatisticsUserQueryDTO;
import com.lyy.user.account.infrastructure.statistics.dto.UserStatisticsConditionDTO;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserQueryDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.hamcrest.CoreMatchers.is;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest
@AutoConfigureMockMvc
public class UserMemberControllerTest {


    @Autowired
    private MockMvc mockMvc;

    // 个人用户
    //private final  String username = "15018442322";
    //private final String adUserId = "1220509";

    // 小黑用户
    private final  String username = "***********";
    private final String adUserId = "1000367";


    private final Long equipmentId = 1186128l;
    private final String equipmentCode = "92000285";

    private final Long MERCHANT_ID = Long.valueOf("1000319");
    private final Long MERCHANT_USER_ID = Long.valueOf("857647292438020096");


    private final Long equipmentTypeId = 1000078l; // 充电桩
    private final String equipmentTypeCode = "CDZ";  // 充电桩

    private final Long xyjEquipmentTypeId = 1000028l; // 洗衣机

    private final String groupId = "1028421";   // 11

    @Test
    public void adjustUserBenefit() throws Exception {

        UserBalanceAdjustReqDTO userBalanceAdjustReqDTO = new UserBalanceAdjustReqDTO();
        userBalanceAdjustReqDTO.setUserId(21757975L);
        //userBalanceAdjustReqDTO.setCoins(new BigDecimal("-25"));
        userBalanceAdjustReqDTO.setClassify(BenefitClassifyEnum.MERCHANT_PAYOUT_COIN.getCode());
        userBalanceAdjustReqDTO.setIsEmpty(4);
        userBalanceAdjustReqDTO.setAccountBenefitId(955830142079598592L);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/userMember/balance/adjust");

        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);
        requestBuilder.content(JSON.toJSONString(userBalanceAdjustReqDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.code", is(0)));
    }

    @Test
    public void queryUserListByMerchant() throws Exception {

        MerchantUserQueryDTO merchantUserQueryDTO = new MerchantUserQueryDTO();
        merchantUserQueryDTO.setPageIndex(1);
        merchantUserQueryDTO.setPageSize(10);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/userMember/queryUserListByMerchant");

        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);
        requestBuilder.content(JSON.toJSONString(merchantUserQueryDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));

    }

    @Test
    public void countUserListByMerchant() throws Exception {

        MerchantUserQueryDTO merchantUserQueryDTO = new MerchantUserQueryDTO();
        merchantUserQueryDTO.setPageIndex(1);
        merchantUserQueryDTO.setPageSize(10);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/userMember/countUserListByMerchant");

        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);
        requestBuilder.content(JSON.toJSONString(merchantUserQueryDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));

    }


    @Test
    public void testClear() throws Exception {
        List<String> ids = new ArrayList<>();
        ids.add("856169297483399168");
        ids.add("856169297483399169");

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/userMember/benefit/clear");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);

        requestBuilder.content(JSON.toJSONString(ids));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));
    }

    @Test
    public void testBenefitCleanAll() throws Exception {
        BenefitQueryDTO dto = new BenefitQueryDTO();
        dto.setMerchantUserId(Long.valueOf("856168697790201856"));
        //dto.setStoreIds();
        //dto.setEquipmentTypeIds();
        //dto.setBenefitGroupType();
        //dto.setClassify(2);
        //dto.setStartTime();
        //dto.setEndTime();
        //dto.setBenefitGroupType();
        dto.setBenefitIds(Arrays.asList("856169297483399168","856169297483399169"));

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/userMember/benefit/cleanAll");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);

        requestBuilder.content(JSON.toJSONString(dto));

        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code",  is(0)));
    }


    @Test
    public void getUserInfo() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/userMember/getUserInfo")
                .param("userId","******** ")
                .param("merchantUserId","852132553238773760");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username","***********");
        requestBuilder.header("adUserId","1000367");
        //requestBuilder.content(JSON.toJSONString(classify));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));

    }

    @Test
    public void delUser() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/userMember/delUser")
                .param("userId","********")
                .param("merchantUserId","852132553238773760");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username","***********");
        requestBuilder.header("adUserId","1000367");
        //requestBuilder.content(JSON.toJSONString(classify));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));

    }

    @Test
    public void getUserAccountInfo() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/userMember/getUserAccountInfo")
                .param("userId","********");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username","***********");
        requestBuilder.header("adUserId","1000367");
        //requestBuilder.content(JSON.toJSONString(classify));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));

    }

    @Test
    public void findAccountBenefitData() throws Exception {
        BenefitQueryDTO dto = new BenefitQueryDTO();
        dto.setMerchantUserId(MERCHANT_USER_ID);
        //dto.setStoreIds();
        //dto.setEquipmentTypeIds();
        dto.setBenefitGroupType(BenefitClassifyGroupEnum.COINS.getType());
        dto.setClassify(2);
        //dto.setStartTime();
        //dto.setEndTime();

        MockHttpServletRequestBuilder requestBuilder =
                MockMvcRequestBuilders.post("/userMember/benefit/findAccountBenefitData");
        requestBuilder.content(JSON.toJSONString(dto));
        requestBuilder.header("Content-Type", "application/json");
        requestBuilder.header("username", username);
        requestBuilder.header("adUserId", adUserId);
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));

    }

    @Test
    public void listBenefitDetail() throws Exception {
        BenefitDetailQueryDTO dto = new BenefitDetailQueryDTO();
        dto.setPageIndex(1);
        dto.setPageSize(10);
        dto.setMerchantUserId(MERCHANT_USER_ID);
        //dto.setStoreIds();
        //dto.setEquipmentTypeIds();
        dto.setBenefitGroupType(BenefitClassifyGroupEnum.COINS.getType());
        dto.setClassify(2);
        //dto.setStartTime();
        //dto.setEndTime();



        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/userMember/benefit/listBenefitDetail")
                .param("userId","********");
        requestBuilder.content(JSON.toJSONString(dto));
        requestBuilder.header("Content-Type", "application/json");
        requestBuilder.header("username", username);
        requestBuilder.header("adUserId", adUserId);
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));

    }

    @Test
    public void getChooseBenefitBalance() throws Exception {
        BenefitDetailQueryDTO dto = new BenefitDetailQueryDTO();
        dto.setPageIndex(1);
        dto.setPageSize(10);
        dto.setMerchantUserId(MERCHANT_USER_ID);
        dto.setStoreIds(Arrays.asList(1028395L,1028515L));
        //dto.setEquipmentTypeIds();
        dto.setBenefitGroupType(BenefitClassifyGroupEnum.COINS.getType());
        dto.setClassify(2);
        dto.setStartTime(DateUtil.parseToDate("2021-06-22 15:44:08.892"));
        dto.setEndTime(new Date());
        dto.setNotHandleBenefitIds(Arrays.asList("857646864560291840"));




        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/userMember/benefit/getChooseBenefitBalance")
                ;
        requestBuilder.content(JSON.toJSONString(dto));
        requestBuilder.header("Content-Type", "application/json");
        requestBuilder.header("username", username);
        requestBuilder.header("adUserId", adUserId);
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));

    }


    @Test
    public void testPayoutWelfare() throws Exception {

        PayoutWelfareDTO payoutWelfareDTO = new PayoutWelfareDTO();
        //payoutWelfareDTO.setIsAllUser(true);
        payoutWelfareDTO.setMerchantUserIdList(Arrays.asList(859495275731369984L));
        // 登录用户获取

        payoutWelfareDTO.setIsAllEquipmentType(true);
        payoutWelfareDTO.setIsAllGroup(true);
//        List<Long> groupIdList = new ArrayList<>();
//        groupIdList.add(1028505L);

        //payoutWelfareDTO.setGroupIdList(groupIdList);
        payoutWelfareDTO.setPayoutBalance(new BigDecimal("1000"));
       // payoutWelfareDTO.setPayoutCoin(new BigDecimal("1"));

        System.out.println(JSON.toJSONString(payoutWelfareDTO));
        //String requestBody = "{\"isAllEquipmentType\":true,\"isAllGroup\":true,\"merchantUserIdList\":[\"856168698587119616\",\"abc856168698587119617\"],\"payoutBalance\":1}";

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/userMember/payout/welfare");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);
        requestBuilder.content(JSON.toJSONString(payoutWelfareDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));
    }

    @Test
    public void getMerchantStatistics()throws Exception{

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/userMember/statistics/getMerchantStatistics");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);

        //requestBuilder.content(JSON.toJSONString(dto));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
        ;
    }

    @Test
    public void queryStatisticsUserList()throws Exception{

        StatisticsUserQueryDTO statisticsUserQueryDTO = new StatisticsUserQueryDTO();
        statisticsUserQueryDTO.setPageIndex(1);
        statisticsUserQueryDTO.setPageSize(10);
        statisticsUserQueryDTO.setTimeScope(TimeScopeEnum.ALL);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/userMember/statistics/queryStatisticsUserList");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);

        requestBuilder.content(JSON.toJSONString(statisticsUserQueryDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
        ;
    }


    @Test
    public void findStatisticsUser()throws Exception{

        UserStatisticsConditionDTO userStatisticsConditionDTO = new UserStatisticsConditionDTO();
        userStatisticsConditionDTO.setUserId(21757012L);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/userMember/statistics/findUser");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);

        requestBuilder.content(JSON.toJSONString(userStatisticsConditionDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
        ;
    }
}
