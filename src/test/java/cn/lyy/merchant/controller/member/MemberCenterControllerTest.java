package cn.lyy.merchant.controller.member;

import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;

import com.alibaba.fastjson.JSONObject;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

/**
 * <AUTHOR>
 * @date 2022/4/21
 **/
@RunWith(SpringRunner.class)
@SpringBootTest
@AutoConfigureMockMvc
public class MemberCenterControllerTest {

    private final Long MERCHANT_ID = Long.valueOf("1000319");
    private final Long MERCHANT_USER_ID = Long.valueOf("856168697790201856");


    // 开发 个人用户
    private final  String username = "13020175108";
    private final String adUserId = "1022285";

    @Autowired
    private MockMvc mockMvc;


    @Test
    public void memberDetail()throws Exception{

        JSONObject jsonObject=new JSONObject();
        jsonObject.put("lyyUserId","21751572");
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/rest/member-center/memberDetail");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);
        requestBuilder.content(jsonObject.toJSONString());

        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
        ;
    }
}
