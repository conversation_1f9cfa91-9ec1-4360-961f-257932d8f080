package cn.lyy.merchant.controller.member;


import com.alibaba.fastjson.JSON;
import com.lyy.user.account.infrastructure.account.dto.AccountRecordQueryDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;

/**
 * @description:
 * @author: qgw
 * @date on 2021-06-03.
 * @Version: 1.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@AutoConfigureMockMvc
public class MemberConsumptionControllerTest {

    private final Long MERCHANT_ID = Long.valueOf("1000319");
    private final Long MERCHANT_USER_ID = Long.valueOf("856168697790201856");


    // 开发 个人用户
    private final  String username = "***********";
    private final String adUserId = "1220349";


    @Autowired
    private MockMvc mockMvc;
    @Test
    public void listConsumptionRecord()throws Exception{

        AccountRecordQueryDTO dto = new AccountRecordQueryDTO();
        dto.setMerchantId(MERCHANT_ID);
        dto.setMerchantUserId(MERCHANT_USER_ID);
        //dto.setStoreId();
        //dto.setEquipmentTypeId();
        //dto.setTradeType();
        //dto.setBalanceType();
        //dto.setStartTime();
        //dto.setEndTime();
        dto.setPageIndex(1);
        dto.setPageSize(10);



        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/rest/member/consumption/list");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);


        requestBuilder.content(JSON.toJSONString(dto));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
        ;
    }

    @Test
    public void tradeTypeList()throws Exception{


        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/rest/member/consumption/tradeTypeList");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);

        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
        ;
    }
}