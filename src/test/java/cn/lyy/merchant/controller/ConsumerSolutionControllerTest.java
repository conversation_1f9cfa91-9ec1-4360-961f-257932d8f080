package cn.lyy.merchant.controller;

import static org.hamcrest.CoreMatchers.is;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import cn.lyy.merchant.dto.request.ConsumerSolutionListReqDTO;
import cn.lyy.merchant.dto.request.ConsumerSolutionSaveDTO;
import cn.lyy.merchant.dto.request.ConsumerSolutionSaveDTO.StageTimeInfo;
import cn.lyy.merchant.dto.request.ConsumerSolutionUpdateDTO;
import cn.lyy.merchant.dto.request.GroupConsumerSolutionReqDTO;
import com.alibaba.fastjson.JSON;
import com.lyy.billing.infrastructure.constants.ConsumerModelEnum;
import com.lyy.billing.infrastructure.constants.ConsumerSolutionCategoryEnum;
import com.lyy.billing.infrastructure.constants.EquipmentTypeEnum;
import com.lyy.billing.infrastructure.constants.PricingTypeEnum;
import com.lyy.billing.infrastructure.constants.StartWayEnum;
import com.lyy.billing.interfaces.consumer.dto.request.ConsumerSolutionBillingCalculateDTO;
import com.lyy.billing.interfaces.consumer.dto.request.ConsumerSolutionBillingPayDTO;
import com.lyy.commodity.rpc.constants.StageTimeTypeEnum;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

/**
 * <AUTHOR>
 * @date 2022/3/8
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@AutoConfigureMockMvc
public class ConsumerSolutionControllerTest {

    @Autowired
    private MockMvc mockMvc;

    private static final String BELONG_TO = "authorization-belong-to";

    @Test
    public void save() throws Exception {
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/consumer/solution/save");

        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("merchantId",1000319);
        requestBuilder.header("adUserId",1000367);
        requestBuilder.header("storeId",1037708);
        requestBuilder.header("username", "15899974725");
        requestBuilder.header(BELONG_TO, 1000319);

        ConsumerSolutionSaveDTO saveDTO = new ConsumerSolutionSaveDTO();
        saveDTO.setModel(ConsumerModelEnum.TIME.getCategory());
        saveDTO.setStartWay(Collections.singletonList(StartWayEnum.PAY.getCategory()));
        saveDTO.setCategory(ConsumerSolutionCategoryEnum.SCAN_CODE.getCategory());
        saveDTO.setPrice(BigDecimal.TEN);
        saveDTO.setEquipmentType(1001350L);
        saveDTO.setEquipmentTypeName("共享设备");
        saveDTO.setDescription("共享设备扫码消费方案计时模式");
        saveDTO.setName("共享设备计时消费");
        saveDTO.setFreeTime(10L);
        saveDTO.setPricingType(PricingTypeEnum.COMMON.getType());
        saveDTO.setStores(Collections.singletonList(1037708L));
        saveDTO.setAssociationType(EquipmentTypeEnum.TERMINAL.getType());
        saveDTO.setEquipmentIds(Collections.singletonList(1307313L));
        saveDTO.setForceUpdate(true);
        ConsumerSolutionSaveDTO.StageTimeInfo stageTimeInfo = new StageTimeInfo();
        stageTimeInfo.setStageTimeType(StageTimeTypeEnum.minute.getValue());
        stageTimeInfo.setStageTime(new BigDecimal(15));
        stageTimeInfo.setAmount(BigDecimal.TEN);
        saveDTO.setStageTimeList(Collections.singletonList(stageTimeInfo));

        requestBuilder.content(JSON.toJSONString(saveDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.code", is(0)));

    }

    @Test
    public void edit() throws Exception {
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/consumer/solution/edit");

        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("merchantId",1000319);
        requestBuilder.header("adUserId",1000367);
        requestBuilder.header("storeId",1028460);
        requestBuilder.header("username", "15899974725");
        requestBuilder.header(BELONG_TO, 1000319);


        ConsumerSolutionUpdateDTO updateDTO = new ConsumerSolutionUpdateDTO();
        updateDTO.setId(73L);
        updateDTO.setModel(ConsumerModelEnum.TIME.getCategory());
        updateDTO.setStartWay(Collections.singletonList(StartWayEnum.PAY.getCategory()));
        updateDTO.setCategory(ConsumerSolutionCategoryEnum.SCAN_CODE.getCategory());
        updateDTO.setPrice(new BigDecimal("100"));
        updateDTO.setEquipmentType(1001340L);
        updateDTO.setEquipmentTypeName("沙滩车");
        updateDTO.setDescription("沙滩车扫码消费方案计时模式");
        updateDTO.setName("沙滩车计时消费");
        updateDTO.setFreeTime(15L);
        updateDTO.setPricingType(PricingTypeEnum.COMMON.getType());
        updateDTO.setStores(Collections.singletonList(1028460L));
        updateDTO.setAssociationType(EquipmentTypeEnum.TERMINAL.getType());
        updateDTO.setAddEquipmentIds(Collections.singletonList(1353670L));
        updateDTO.setRemoveEquipmentIds(Collections.singletonList(1307313L));
        updateDTO.setForceUpdate(true);
        updateDTO.setCommodityId(235900L);
        ConsumerSolutionUpdateDTO.StageTimeInfo stageTimeInfo = new ConsumerSolutionUpdateDTO.StageTimeInfo();
        stageTimeInfo.setStageTimeType(StageTimeTypeEnum.minute.getValue());
        stageTimeInfo.setStageTime(new BigDecimal("1"));
        stageTimeInfo.setAmount(BigDecimal.ONE);
        stageTimeInfo.setSort(0);
        updateDTO.setStageTimeList(Collections.singletonList(stageTimeInfo));

        requestBuilder.content(JSON.toJSONString(updateDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.code", is(0)));

    }

    @Test
    public void list() throws Exception {
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/consumer/solution/list");

        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("merchantId",1000319);
        requestBuilder.header("adUserId",1000367);
        requestBuilder.header("authorization-belong-to", 1000319);
        //requestBuilder.header("storeId",1028460);
        //requestBuilder.header("username", "15899974725");

        ConsumerSolutionListReqDTO consumerSolutionListReqDTO = new ConsumerSolutionListReqDTO();
        consumerSolutionListReqDTO.setIsCount(true);
        consumerSolutionListReqDTO.setMerchantId(1000319L);
        consumerSolutionListReqDTO.setScope(1);
        consumerSolutionListReqDTO.setEquipmentTypeId(1001350L);
        //consumerSolutionListReqDTO.setKeyword("沙滩车");

        requestBuilder.content(JSON.toJSONString(consumerSolutionListReqDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.code", is(0)));

    }

    @Test
    public void detail() throws Exception {
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/consumer/solution/detail");

        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("merchantId",1000319);
        requestBuilder.header("adUserId",1000367);
        //requestBuilder.header("storeId",1028460);
        requestBuilder.header("username", "15899974725");
        requestBuilder.header(BELONG_TO, 1000319);


        requestBuilder.param("id","109");
        requestBuilder.param("category","2");
        requestBuilder.param("type", "2");

        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.code", is(0)));

    }

    @Test
    public void delete() throws Exception {
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.delete("/consumer/solution/delete");

        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("merchantId",1000319);
        requestBuilder.header("adUserId",1000367);
        requestBuilder.header("storeId",1028460);
        requestBuilder.header("username", "15899974725");

        requestBuilder.param("id", "68");

        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.code", is(0)));

    }

    @Test
    public void findGroupConsumerSolutions() throws Exception {
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/consumer/solution/groups");

        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("merchantId",1000319);
        requestBuilder.header("adUserId",1000367);
        requestBuilder.header("storeId",1028460);
        requestBuilder.header("username", "15899974725");
        requestBuilder.header(BELONG_TO, 1000319);


        GroupConsumerSolutionReqDTO groupConsumerSolutionReqDTO = new GroupConsumerSolutionReqDTO();
        groupConsumerSolutionReqDTO.setAssociationType(EquipmentTypeEnum.TERMINAL.getType());
        groupConsumerSolutionReqDTO.setCategory(ConsumerSolutionCategoryEnum.SCAN_CODE.getCategory());
        groupConsumerSolutionReqDTO.setEquipmentTypeList(Arrays.asList(1001350L));

        requestBuilder.content(JSON.toJSONString(groupConsumerSolutionReqDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.code", is(0)));
    }

}
