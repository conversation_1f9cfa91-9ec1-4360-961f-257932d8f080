package cn.lyy.merchant.controller;

import cn.lyy.merchant.dto.request.*;
import com.alibaba.fastjson.JSON;
import com.lyy.commodity.rpc.constants.ClassifyCodeEnum;
import com.lyy.commodity.rpc.constants.ValueUnitEnum;
import com.lyy.commodity.rpc.dto.request.SetUseDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;


import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;


import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.hamcrest.CoreMatchers.is;

@RunWith(SpringRunner.class)
@SpringBootTest
@AutoConfigureMockMvc
public class FeeRuleControllerTest {


    @Autowired
    private MockMvc mockMvc;

    // 王海 个人用户
    private final  String username = "15018442322";
    private final String adUserId = "1220509";

//    private final Long equipmentId = 1186459l;
//    private final String equipmentCode = "92001601";

    private final Long equipmentId = 1186128l;
    private final String equipmentCode = "92000285";


    private final Long equipmentTypeId = 1000078l; // 充电桩
    private final String equipmentTypeCode = "CDZ";  // 充电桩

    private final Long xyjEquipmentTypeId = 1000028l; // 洗衣机

    private final String groupId = "1028421";   // 11


    @Test
    public void listFeeRule() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/fee/rule/list/by/equipment")
                .param("equipmentValue","92000285");
                //.param("equipmentType","JYJ");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);
        //requestBuilder.content(JSON.toJSONString(classify));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));

    }


    /**
     * 查询历史计费记录
     * @throws Exception
     */
    @Test
    public void listHistoryFeeRule() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/fee/rule/list/by/group/equipment/type")
                .param("groupId","1028419")
                .param("equipmentTypeId",xyjEquipmentTypeId.toString())
                .param("classifyCode",ClassifyCodeEnum.TIME.getCode());

        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);
        //requestBuilder.content(JSON.toJSONString(classify));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));

    }

    @Test
    public void addFeeRuleSave() throws Exception {

        FeeRuleSaveReqDTO feeRuleSaveReqDTO = new FeeRuleSaveReqDTO();
        feeRuleSaveReqDTO.setEquipmentId(1186205l);

        feeRuleSaveReqDTO.setTitle("快充0021");
        feeRuleSaveReqDTO.setClassifyCode(ClassifyCodeEnum.TIME.getCode());
        feeRuleSaveReqDTO.setPrice(new BigDecimal(3));

        List<FixPriceValueDTO> fixPriceValueList = new ArrayList<>();
        FixPriceValueDTO fixPriceValueDTO = new FixPriceValueDTO();
        fixPriceValueDTO.setValue(new BigDecimal(20));
        fixPriceValueDTO.setValueUnit(ValueUnitEnum.MIN.getId());
        fixPriceValueList.add(fixPriceValueDTO);

        feeRuleSaveReqDTO.setFixPriceValueList(fixPriceValueList);


        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/fee/rule/saveOrUpdate");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);

        requestBuilder.content(JSON.toJSONString(feeRuleSaveReqDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));

    }

    @Test
    public void addCarFeeRuleSave() throws Exception {

        FeeRuleSaveReqDTO feeRuleSaveReqDTO = new FeeRuleSaveReqDTO();
        feeRuleSaveReqDTO.setEquipmentId(1186205l);

        feeRuleSaveReqDTO.setTitle("快充0021");
        feeRuleSaveReqDTO.setClassifyCode(ClassifyCodeEnum.TIME.getCode());
        feeRuleSaveReqDTO.setPrice(new BigDecimal(3));

        List<FixPriceValueDTO> fixPriceValueList = new ArrayList<>();
        FixPriceValueDTO fixPriceValueDTO = new FixPriceValueDTO();
        fixPriceValueDTO.setValue(new BigDecimal(20));
        fixPriceValueDTO.setValueUnit(ValueUnitEnum.MIN.getId());
        fixPriceValueList.add(fixPriceValueDTO);

        feeRuleSaveReqDTO.setFixPriceValueList(fixPriceValueList);


        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/fee/rule/saveOrUpdate");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);

        requestBuilder.content(JSON.toJSONString(feeRuleSaveReqDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));

    }

    /**
     * 实际没有洗衣机增加规则
     * @throws Exception
     */
    @Test
    public void addXYJFeeRuleSave() throws Exception {

        FeeRuleSaveReqDTO feeRuleSaveReqDTO = new FeeRuleSaveReqDTO();
        feeRuleSaveReqDTO.setEquipmentId(1213988l);
        feeRuleSaveReqDTO.setDetailId(46800l);   // 此标识意味着更新

        feeRuleSaveReqDTO.setTitle("快充0021");
        feeRuleSaveReqDTO.setClassifyCode(ClassifyCodeEnum.TIME.getCode());
        feeRuleSaveReqDTO.setPrice(new BigDecimal(3));

        List<FixPriceValueDTO> fixPriceValueList = new ArrayList<>();
        FixPriceValueDTO fixPriceValueDTO = new FixPriceValueDTO();
        fixPriceValueDTO.setValue(new BigDecimal(20));
        fixPriceValueDTO.setValueUnit(ValueUnitEnum.MIN.getId());
        fixPriceValueList.add(fixPriceValueDTO);
        feeRuleSaveReqDTO.setFixPriceValueList(fixPriceValueList);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/fee/rule/saveOrUpdate");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);

        requestBuilder.content(JSON.toJSONString(feeRuleSaveReqDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));

    }

    /**
     * 通用脉冲增加规则
     * @throws Exception
     */
    @Test
    public void addGeneralPulseFeeRuleSave() throws Exception {

        FeeRuleSaveReqDTO feeRuleSaveReqDTO = new FeeRuleSaveReqDTO();
        feeRuleSaveReqDTO.setEquipmentId(1306730L);

        feeRuleSaveReqDTO.setTitle("脉冲标题");
        feeRuleSaveReqDTO.setClassifyCode(ClassifyCodeEnum.NUM.getCode());
        feeRuleSaveReqDTO.setPrice(new BigDecimal(0.01));

        List<FixPriceValueDTO> fixPriceValueList = new ArrayList<>();
        FixPriceValueDTO fixPriceValueDTO = new FixPriceValueDTO();
        fixPriceValueDTO.setValue(new BigDecimal(1));
        fixPriceValueDTO.setValueUnit(ValueUnitEnum.COIN.getId());
        fixPriceValueList.add(fixPriceValueDTO);
        feeRuleSaveReqDTO.setFixPriceValueList(fixPriceValueList);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/fee/rule/saveOrUpdate");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);

        requestBuilder.content(JSON.toJSONString(feeRuleSaveReqDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));

    }

    @Test
    public void addJYJFeeRuleSave() throws Exception {

        FeeRuleSaveReqDTO feeRuleSaveReqDTO = new FeeRuleSaveReqDTO();
        feeRuleSaveReqDTO.setEquipmentId(1306729l);
        feeRuleSaveReqDTO.setEquipmentTypeValue("JYJ");
        feeRuleSaveReqDTO.setDetailId(52005l);   // 此标识意味着更新

        feeRuleSaveReqDTO.setTitle("商品名称");
        feeRuleSaveReqDTO.setClassifyCode(ClassifyCodeEnum.VOLUME.getCode());
        feeRuleSaveReqDTO.setPrice(new BigDecimal(3));

        List<FixPriceValueDTO> fixPriceValueList = new ArrayList<>();
        FixPriceValueDTO fixPriceValueDTO = new FixPriceValueDTO();
        fixPriceValueDTO.setValue(new BigDecimal(30));
        fixPriceValueDTO.setValueUnit(ValueUnitEnum.ML.getId());
        fixPriceValueList.add(fixPriceValueDTO);
        feeRuleSaveReqDTO.setFixPriceValueList(fixPriceValueList);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/fee/rule/saveOrUpdate");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);

        requestBuilder.content(JSON.toJSONString(feeRuleSaveReqDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));

    }

    @Test
    public void addBatchFeeRuleSave() throws Exception {

        BatchFeeRuleSaveDTO batchFeeRuleSaveDTO = new BatchFeeRuleSaveDTO();

        // 相关的设备
        List<String> codes = new ArrayList<>();
        codes.add("92025833");
        batchFeeRuleSaveDTO.setCodes(codes);
        //batchFeeRuleSaveDTO.setEquipmentTypeId(1000078l);
        batchFeeRuleSaveDTO.setEquipmentType("JYJ");
        batchFeeRuleSaveDTO.setClassifyCode(ClassifyCodeEnum.VOLUME.getCode());

        List<BatchFeeRuleSaveDTO.BatchFeeRuleDTO> rules = new ArrayList<>();
        BatchFeeRuleSaveDTO.BatchFeeRuleDTO batchFeeRuleDTO = new BatchFeeRuleSaveDTO.BatchFeeRuleDTO();
        batchFeeRuleDTO.setPrice(new BigDecimal(1));
        batchFeeRuleDTO.setValue(new BigDecimal(10));
        batchFeeRuleDTO.setValueUnit(ValueUnitEnum.ML.getId());
        rules.add(batchFeeRuleDTO);
        batchFeeRuleSaveDTO.setFeeRules(rules);

        batchFeeRuleSaveDTO.setIsShowTitle(true);
        //batchFeeRuleSaveDTO.setIsShowTi


        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/fee/rule/batchSaveOrUpdate");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);

        requestBuilder.content(JSON.toJSONString(batchFeeRuleSaveDTO));

        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));

    }

    /**
     * 批量增加洗衣机的计费规则
     * @throws Exception
     */
    @Test
    public void addXYJBatchFeeRuleSave() throws Exception {

        BatchFeeRuleSaveDTO batchFeeRuleSaveDTO = new BatchFeeRuleSaveDTO();

        // 相关的设备
        List<String> codes = new ArrayList<>();
        codes.add("92000255");
        batchFeeRuleSaveDTO.setCodes(codes);
        batchFeeRuleSaveDTO.setEquipmentTypeId(xyjEquipmentTypeId);
        batchFeeRuleSaveDTO.setClassifyCode(ClassifyCodeEnum.TIME.getCode());

        List<BatchFeeRuleSaveDTO.BatchFeeRuleDTO> rules = new ArrayList<>();
        BatchFeeRuleSaveDTO.BatchFeeRuleDTO batchFeeRuleDTO = new BatchFeeRuleSaveDTO.BatchFeeRuleDTO();
        batchFeeRuleDTO.setPrice(new BigDecimal(1));
//        batchFeeRuleDTO.setValue(new BigDecimal(40));
//        batchFeeRuleDTO.setValueUnit(ValueUnitEnum.MIN.getId());
        List<PriceValueDTO> fixPriceValueList = new ArrayList<>();
        PriceValueDTO priceValueDTO = new PriceValueDTO();
        priceValueDTO.setValue(new BigDecimal(40));
        priceValueDTO.setUnit(ValueUnitEnum.MIN.getId());
        fixPriceValueList.add(priceValueDTO);

        PriceValueDTO coinPriceValueDTO = new PriceValueDTO();
        coinPriceValueDTO.setValue(new BigDecimal(1));
        coinPriceValueDTO.setUnit(ValueUnitEnum.COIN.getId());
        fixPriceValueList.add(coinPriceValueDTO);

        batchFeeRuleDTO.setFixPriceValueList(fixPriceValueList);
        rules.add(batchFeeRuleDTO);
        batchFeeRuleSaveDTO.setFeeRules(rules);

        // batchFeeRuleSaveDTO.setIsShowTitle(true);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/fee/rule/batchSaveOrUpdate");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);
        requestBuilder.content(JSON.toJSONString(batchFeeRuleSaveDTO));

        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));

    }



    /**
     * 批量增加洗衣机的计费规则
     * @throws Exception
     */
    @Test
    public void addJYJBatchFeeRuleSave() throws Exception {

        String str = "  {\"codes\":[\"92025833\"],\"classifyCode\":\"VOLUME\",\"categoryCode\":\"device_service\",\"equipmentType\":\"JYJ\",\"feeRules\":[{\"price\":\"1\",\"value\":\"10\",\"valueUnit\":7}],\"isShowTitle\":true,\"isShowFeeRuleUnit\":true}\n";

        BatchFeeRuleSaveDTO batchFeeRuleSaveDTO = JSON.parseObject(str,BatchFeeRuleSaveDTO.class);

        // 相关的设备
//        List<String> codes = new ArrayList<>();
//        codes.add("92000255");
//        batchFeeRuleSaveDTO.setCodes(codes);
//        batchFeeRuleSaveDTO.setEquipmentTypeId(xyjEquipmentTypeId);
//        batchFeeRuleSaveDTO.setClassifyCode(ClassifyCodeEnum.TIME.getCode());
//
//        List<BatchFeeRuleSaveDTO.BatchFeeRuleDTO> rules = new ArrayList<>();
//        BatchFeeRuleSaveDTO.BatchFeeRuleDTO batchFeeRuleDTO = new BatchFeeRuleSaveDTO.BatchFeeRuleDTO();
//        batchFeeRuleDTO.setPrice(new BigDecimal(1));
////        batchFeeRuleDTO.setValue(new BigDecimal(40));
////        batchFeeRuleDTO.setValueUnit(ValueUnitEnum.MIN.getId());
//        List<PriceValueDTO> fixPriceValueList = new ArrayList<>();
//        PriceValueDTO priceValueDTO = new PriceValueDTO();
//        priceValueDTO.setValue(new BigDecimal(40));
//        priceValueDTO.setUnit(ValueUnitEnum.MIN.getId());
//        fixPriceValueList.add(priceValueDTO);
//
//        PriceValueDTO coinPriceValueDTO = new PriceValueDTO();
//        coinPriceValueDTO.setValue(new BigDecimal(1));
//        coinPriceValueDTO.setUnit(ValueUnitEnum.COIN.getId());
//        fixPriceValueList.add(coinPriceValueDTO);
//
//        batchFeeRuleDTO.setFixPriceValueList(fixPriceValueList);
//        rules.add(batchFeeRuleDTO);
//        batchFeeRuleSaveDTO.setFeeRules(rules);

        // batchFeeRuleSaveDTO.setIsShowTitle(true);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/fee/rule/batchSaveOrUpdate");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);
        requestBuilder.content(JSON.toJSONString(batchFeeRuleSaveDTO));

        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));

    }

    @Test
    public void delFeeRule() throws Exception {

        RuleDelDTO ruleDelDTO = new RuleDelDTO();
        ruleDelDTO.setDetailId(41600l);
        ruleDelDTO.setGroupId(1028413l);
        ruleDelDTO.setEquipmentValue("92000607");

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/fee/rule/del");

        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);
        requestBuilder.content(JSON.toJSONString(ruleDelDTO));

        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));

    }


    @Test
    public void getFeeListMode() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/fee/rule/listFeeMode")
                .param("equipmentCode","92000285");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);

        this.mockMvc.perform(requestBuilder)
                .andDo(print())
                .andExpect(status().isOk());
    }

    @Test
    public void setPro() throws Exception {

        SetProDTO setProDTO = new SetProDTO();
        setProDTO.setEquipmentId(1306568l);
        setProDTO.setIsShowFeeRuleUnit(false);
        setProDTO.setIsShowFeeRuleUnit(true);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/fee/rule/setPro");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);

        requestBuilder.content(JSON.toJSONString(setProDTO));
        this.mockMvc.perform(requestBuilder)
                .andDo(print())
                .andExpect(status().isOk())
        ;
    }

    @Test
    public void setUse() throws Exception {

        SetUseDTO setUseDTO = new SetUseDTO();
        //setUseDTO.setEquipmentRelateId(42704l);
        setUseDTO.setRelateEquipmentId(42704l);
        setUseDTO.setIsUse(true);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/fee/rule/setUse");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);

        requestBuilder.content(JSON.toJSONString(setUseDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));
        ;
    }

    @Test
    public void updateFeeMode() throws Exception {

        UpdateFeeModeDTO updateFeeModeDTO = new UpdateFeeModeDTO();
        updateFeeModeDTO.setEquipmentId(equipmentId);
        updateFeeModeDTO.setFeeMode(ClassifyCodeEnum.TIME.getCode());

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/fee/rule/updateFeeMode");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);

        requestBuilder.content(JSON.toJSONString(updateFeeModeDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));

    }

    @Deprecated
    @Test
    public void getDefaultFeeRule() throws Exception {


        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/fee/rule/getDefaultFeeRule")
                .param("protocolId","22404");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);

        //requestBuilder.content(JSON.toJSONString(updateFeeModeDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));
    }

    @Test
    public void getMainboardDefaultFeeRule() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/fee/rule/getMainBoardDefaultFeeRule")
                .param("productId","1000463")  //此为主板Id(lyy_factory_motherboard 主键ID)
                .param("equipmentTypeValue","XYJ");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);

        //requestBuilder.content(JSON.toJSONString(updateFeeModeDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));
    }
}
