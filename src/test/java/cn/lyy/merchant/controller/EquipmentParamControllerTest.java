package cn.lyy.merchant.controller;

import cn.lyy.merchant.dto.request.MerchantEquipmentRequest;
import cn.lyy.tools.equipment.OpenSettingInfo;
import com.alibaba.fastjson.JSON;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.util.ArrayList;

import static org.hamcrest.CoreMatchers.is;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest
@AutoConfigureMockMvc
public class EquipmentParamControllerTest {

    @Autowired
    private MockMvc mockMvc;


    // 王海 个人用户
    private final  String username = "15018442322";
    private final String adUserId = "1220509";


    @Deprecated
    @Test
    public void getProtocolByType() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/rest/equipmentParam/getProtocolByType")
                .param("equipmentType","XYJ")
                ;
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);
        //requestBuilder.content(JSON.toJSONString(merchantEquipmentRequest));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));

    }

    @Test
    public void getMainBoardByType() throws Exception {


        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/rest/equipmentParam/getMainBoardByType")
                .param("equipmentType","XYJ")
                ;
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);
        //requestBuilder.content(JSON.toJSONString(merchantEquipmentRequest));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));

    }

    @Test
    public void getS1Test() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/rest/equipmentParam/s1/test")
                ;
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);
        //requestBuilder.content(JSON.toJSONString(merchantEquipmentRequest));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));

    }

    @Test
    public void getS1ConfigList() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/rest/equipmentParam/s1/paramConfigList")
                .param("uniqueCode","0000000092025831")
                //.param("data","{\"cmd\":\"ee07\"}")
                ;
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);
        //requestBuilder.content(JSON.toJSONString(merchantEquipmentRequest));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));

    }

    @Test
    public void getS1Param() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/rest/equipmentParam/s1/paramQuery")
                .param("uniqueCode","0000000092025831")
                .param("data","{\"cmd\":\"ee07\"}")
                ;
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);
        //requestBuilder.content(JSON.toJSONString(merchantEquipmentRequest));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));

    }

    @Test
    public void s1SetParam() throws Exception {

        OpenSettingInfo openSettingInfo = new OpenSettingInfo();
        openSettingInfo.setUniqueCode("0000000092000660");
        openSettingInfo.setData("{\"params\":\"[{\\\"key\\\":\\\"PORT_NUM\\\",\\\"name\\\":\\\"设备端口数量\\\",\\\"length\\\":1,\\\"componentType\\\":\\\"inputInt\\\",\\\"componentValueType\\\":\\\"int\\\",\\\"componentValue\\\":\\\"12\\\",\\\"componentValueUnit\\\":\\\"\\\",\\\"componentValueRange\\\":\\\"{\\\\\\\"min\\\\\\\":\\\\\\\"0\\\\\\\",\\\\\\\"max\\\\\\\":\\\\\\\"255\\\\\\\"}\\\"},{\\\"key\\\":\\\"START_ITEM\\\",\\\"name\\\":\\\"第一个端口的名称\\\",\\\"length\\\":1,\\\"componentType\\\":\\\"inputInt\\\",\\\"componentValueType\\\":\\\"int\\\",\\\"componentValue\\\":\\\"1\\\",\\\"componentValueUnit\\\":\\\"\\\",\\\"componentValueRange\\\":\\\"{\\\\\\\"min\\\\\\\":\\\\\\\"0\\\\\\\",\\\\\\\"max\\\\\\\":\\\\\\\"255\\\\\\\"}\\\"}]\",\"cmd\":\"\"}");

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/rest/equipmentParam/s1/paramSetting")
                 ;
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);
        requestBuilder.content(JSON.toJSONString(openSettingInfo));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));

    }


}
