package cn.lyy.merchant.controller;

import cn.lyy.merchant.Application;
import cn.lyy.merchant.dto.request.OrderQueryRequest;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * @createTime 2020-12-07
 * @auther peterguo
 * @Description
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@AutoConfigureMockMvc
public class OrderControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    private HttpHeaders httpHeaders = new HttpHeaders();

    @Before
    public void setup(){
        httpHeaders.add("username","18707642679");
        httpHeaders.add("adUserId","1265593");
    }


    //订单查询
    @Test
    public void query() throws Exception {

        OrderQueryRequest request = new OrderQueryRequest();
        //request.setSearchWord("78690274316294553618932629369526");
        request.setPageIndex(1);
        request.setPageSize(10);

        LocalDateTime startTime = LocalDateTime.now().plusDays(-3);
        LocalDateTime endTime = LocalDateTime.now();

        request.setStartTime(startTime);
        request.setEndTime(endTime);

        mockMvc.perform(post("/rest/order/getOrderList").headers(httpHeaders)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("0"))
                .andExpect(jsonPath("$.message").value("成功"))
                .andReturn();
    }

    @Test
    public void getOrderDetail() throws Exception {
        String outTradeNo = "79133329822287462443978441316316";

        mockMvc.perform(get("/rest/order/getOrderDetail")
                            .headers(httpHeaders)
                            .param("outTradeNo",outTradeNo))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("0"))
                .andReturn();
    }

    @Test
    public void getRefundDetail() throws Exception {
        String refundNo = "b6448ecf22f34e7e88e7";

        mockMvc.perform(get("/rest/order/getRefundDetail")
                                .param("refundNo",refundNo)
                                .headers(httpHeaders)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("0"))
                .andReturn();
    }

}
