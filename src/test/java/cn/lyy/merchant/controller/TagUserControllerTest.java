package cn.lyy.merchant.controller;

import cn.lyy.merchant.Application;
import cn.lyy.merchant.dto.tag.TagQueryDTO;
import cn.lyy.merchant.dto.tag.TagSaveDTO;
import cn.lyy.merchant.dto.tag.TagUpdateNameDTO;
import com.google.gson.Gson;
import com.lyy.user.account.infrastructure.constant.TagBusinessTypeEnum;
import com.lyy.user.account.infrastructure.constant.TagCategoryEnum;
import com.lyy.user.account.infrastructure.user.dto.tag.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.util.*;

import static org.hamcrest.CoreMatchers.is;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * @description:
 * @author: qgw
 * @date on 2021-04-20. @Version: 1.0
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
@AutoConfigureMockMvc
public class TagUserControllerTest {

    Gson gson = new Gson();
    @Autowired
    private MockMvc mockMvc;
    // 小黑 个人用户
    private final String username = "15899974725";
    private final String adUserId = "1220349";

    private static final Long MERCHANT_ID = 1000319L;
    private static final String CODE = "CODE100";
    private static final Long DB_LOGIN_USERID = 1000367L;
    private static Long TAG_ID = Long.valueOf("1384463819079761923");

    private final Long USER_ID_1062066 =  21743568L;
    private final Long MERCHANT_ID_1062066 =  1062066L;
    private final Long MERCHANT_USER_ID_1062066 =  857926184700755968L;
    private final Long MERCHANT_TAG_ID_1062066 =  1408307968310513665L;

    private static List<Long> USER_ID_LIST = Arrays.asList(21744049L, 21743568L);


    @Test
    public void list() throws Exception {


        TagQueryDTO dto = new TagQueryDTO();
        //dto.setId(0L);
        dto.setMerchantId(MERCHANT_ID);
        //dto.setName("穷");
        //dto.setActive(Boolean.FALSE);
        dto.setCategory(TagCategoryEnum.MANUAL.getStatus());
        //dto.setBusinessType(TagBusinessTypeEnum.GROUP_NAME.getStatus());
        dto.setPageIndex(1);
        dto.setPageSize(20);


        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/rest/tag/list");
        requestBuilder.content(gson.toJson(dto));
        requestBuilder.header("Content-Type", "application/json");
        requestBuilder.header("username", username);
        requestBuilder.header("adUserId", adUserId);
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));
    }

    /**
     * 测场地标签
     * @throws Exception
     */
    @Test
    public void listWithoutUser() throws Exception {
        TagQueryDTO dto = new TagQueryDTO();

        Map<String, Object> data = new HashMap<>();

        data.put("id", MERCHANT_TAG_ID_1062066.toString());
        data.put("merchantId", MERCHANT_ID_1062066);
        data.put("notHandleUserIds", Arrays.asList(MERCHANT_TAG_ID_1062066.toString()));
        data.put("merchantUserId", MERCHANT_USER_ID_1062066);
        data.put("operatorId", DB_LOGIN_USERID);
        data.put("name", "标签创建标标99");
        data.put("active", true);
        data.put("category", TagCategoryEnum.AUTO.getStatus());
        data.put("businessType", TagBusinessTypeEnum.GROUP_NAME.getStatus());
        data.put("tagType", 1);
        data.put("state", 0);
        data.put("queryUserNumber", true);
        data.put("pageIndex", 1);
        data.put("pageSize", Integer.MAX_VALUE);


        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/rest/tag/list");
        requestBuilder.content(gson.toJson(data));
        requestBuilder.header("Content-Type", "application/json");
        requestBuilder.header("username", username);
        requestBuilder.header("adUserId", adUserId);
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));
    }

    @Test
    public void saveOrUpdateTagUser() throws Exception {
        Map<String, Object> data = new HashMap<>();

        data.put("userIds", Arrays.asList(USER_ID_1062066.toString()));
        data.put("tagIds", Arrays.asList(MERCHANT_TAG_ID_1062066.toString()));
        data.put("merchantId", MERCHANT_ID_1062066);
        data.put("operatorId", DB_LOGIN_USERID);
        data.put("name", "标签创建标标991");
        data.put("category", 2);
        data.put("description", "创建手动标签1");

        TagSaveDTO dto = new TagSaveDTO();


        MockHttpServletRequestBuilder requestBuilder =
                MockMvcRequestBuilders.post("/rest/tag/saveOrUpdateTagUser");
        requestBuilder.content(gson.toJson(data));
        requestBuilder.header("Content-Type", "application/json");
        requestBuilder.header("username", username);
        requestBuilder.header("adUserId", adUserId);
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果

        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));
    }

    @Test
    public void changeTagStatus() throws Exception {
        List<Long> tagIds = Arrays.asList(TAG_ID);
        TagStatusDTO dto = new TagStatusDTO();
        List<TagStatusInfoDTO> tagInfos = new ArrayList<>();
        TagStatusInfoDTO tagInfo = new TagStatusInfoDTO();
        tagInfo.setTagIds(tagIds);
        tagInfo.setMerchantId(MERCHANT_ID);
        tagInfos.add(tagInfo);
        dto.setTagInfos(tagInfos);

        //dto.setActive(Boolean.FALSE);
        dto.setActive(true);
        dto.setState(0);
        dto.setOperatorId(DB_LOGIN_USERID);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/rest/tag/changeTagStatus");
        requestBuilder.content(gson.toJson(dto));
        requestBuilder.header("Content-Type", "application/json");
        requestBuilder.header("username", username);
        requestBuilder.header("adUserId", adUserId);
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));
    }

    @Test
    public void bindUser() throws Exception {
        Map<String, Object> data = new HashMap<>();

        data.put("userIds", Arrays.asList(USER_ID_1062066.toString()));
        data.put("tagIds", Arrays.asList(MERCHANT_TAG_ID_1062066.toString()));
        data.put("merchantId", MERCHANT_ID_1062066);
        data.put("operatorId", DB_LOGIN_USERID);
        TagBindUserDTO dto = new TagBindUserDTO();

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/rest/tag/bindUser");
        requestBuilder.content(gson.toJson(data));
        requestBuilder.header("Content-Type", "application/json");
        requestBuilder.header("username", username);
        requestBuilder.header("adUserId", adUserId);
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));
    }

    @Test
    public void unBindUser() throws Exception {
        TagUnBindUserDTO dto = new TagUnBindUserDTO();
        List<Long> tagIds = Arrays.asList(MERCHANT_TAG_ID_1062066);
        dto.setTagIds(tagIds);
        dto.setUserIds(Arrays.asList(Long.valueOf(MERCHANT_USER_ID_1062066)));
        dto.setChooseAll(Boolean.TRUE);
        dto.setMerchantId(MERCHANT_ID_1062066);
        //dto.setOperatorId(DB_LOGIN_USERID);
        dto.setPageIndex(1);
        dto.setPageSize(20);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/rest/tag/unBindUser");
        requestBuilder.content(gson.toJson(dto));
        requestBuilder.header("Content-Type", "application/json");
        requestBuilder.header("username", username);
        requestBuilder.header("adUserId", adUserId);
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));
    }

    @Test
    public void updateTagName() throws Exception {

        Map<String, Object> data = new HashMap<>();

        data.put("tagId", MERCHANT_TAG_ID_1062066.toString());
        data.put("merchantId", MERCHANT_ID_1062066);

        data.put("newName", "1标签创建标标999");


        TagUpdateNameDTO dto = new TagUpdateNameDTO();


        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/rest/tag/updateTagName");
        requestBuilder.content(gson.toJson(data));
        requestBuilder.header("Content-Type", "application/json");
        requestBuilder.header("username", username);
        requestBuilder.header("adUserId", adUserId);
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));
    }

    private static final Long MERCHANT_ID2 = Long.valueOf("1062179");
    private static final Long MERCHANT_USER_ID2 = Long.valueOf("1405709499812106241");
    private static String TAG_ID2 = "1405709537778946050";
    @Test
    public void listTagByUser() throws Exception {

        Map<String, Object> data = new HashMap<>();

        //data.put("id", MERCHANT_TAG_ID_1062066.toString());
        data.put("merchantId", MERCHANT_ID_1062066);
        data.put("notHandleUserIds", Arrays.asList(MERCHANT_TAG_ID_1062066.toString()));
        data.put("merchantUserId", MERCHANT_USER_ID_1062066);
        data.put("operatorId", DB_LOGIN_USERID);
        data.put("name", "标签创建标标99");
        data.put("active", true);
        data.put("category", TagCategoryEnum.AUTO.getStatus());
        data.put("businessType", TagBusinessTypeEnum.GROUP_NAME.getStatus());
        data.put("tagType", 1);
        data.put("state", 0);
        data.put("queryUserNumber", true);
        data.put("pageIndex", 1);
        data.put("pageSize", Integer.MAX_VALUE);


        TagUserQueryDTO dto = new TagUserQueryDTO();
        //dto.setId(Long.valueOf(TAG_ID2));
        dto.setMerchantUserId(MERCHANT_USER_ID);
        dto.setMerchantId(MERCHANT_ID2);
        //dto.setName();
        dto.setActive(true);
        dto.setTagType(1);
        //dto.setCategory();
        //dto.setBusinessType();
        //dto.setState();
        //dto.setQueryUserNumber();
        dto.setPageIndex(1);
        dto.setPageSize(10);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/rest/tag/listTagByUser");
        requestBuilder.content(gson.toJson(data));
        requestBuilder.header("Content-Type", "application/json");
        requestBuilder.header("username", username);
        requestBuilder.header("adUserId", adUserId);
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));
    }





    private static final Long MERCHANT_ID1 = Long.valueOf("1000319");
    private static final Long MERCHANT_USER_ID = Long.valueOf("856168697790201856");
    private static String TAG_ID1 = "1400411232363614209";
    @Test
    public void findByTagId() throws Exception {

        Map<String, Object> data = new HashMap<>();

        data.put("id", MERCHANT_TAG_ID_1062066.toString());
        data.put("merchantId", MERCHANT_ID_1062066);
        data.put("notHandleUserIds", Arrays.asList(MERCHANT_TAG_ID_1062066.toString()));
        data.put("merchantUserId", MERCHANT_USER_ID_1062066);
        data.put("operatorId", DB_LOGIN_USERID);
        data.put("name", "标签创建标标99");
        data.put("active", true);
        data.put("category", 2);
        data.put("tagType", 1);
        data.put("state", 0);
        data.put("queryUserNumber", true);
        data.put("pageIndex", 1);
        data.put("pageSize", 10);


        TagUserQueryDTO dto = new TagUserQueryDTO();
        dto.setId(Long.valueOf(TAG_ID1));
        //dto.setMerchantUserId(MERCHANT_USER_ID);
        dto.setMerchantId(MERCHANT_ID1);
        //dto.setName();
        dto.setActive(true);
        dto.setTagType(1);
        //dto.setCategory();
        //dto.setBusinessType();
        //dto.setState();
        //dto.setQueryUserNumber();
        dto.setPageIndex(1);
        dto.setPageSize(10);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/rest/tag/findByTagId");
        requestBuilder.content(gson.toJson(data));
        requestBuilder.header("Content-Type", "application/json");
        requestBuilder.header("username", username);
        requestBuilder.header("adUserId", adUserId);
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));
    }



    @Test
    public void countFindByTagId() throws Exception {
        Map<String, Object> data = new HashMap<>();

        data.put("id", MERCHANT_TAG_ID_1062066.toString());
        data.put("merchantId", MERCHANT_ID_1062066);
        data.put("notHandleUserIds", Arrays.asList(MERCHANT_TAG_ID_1062066.toString()));
        data.put("merchantUserId", MERCHANT_USER_ID_1062066);
        data.put("operatorId", DB_LOGIN_USERID);
        data.put("name", "标签创建标标99");
        data.put("active", true);
        data.put("category", 2);
        data.put("tagType", 1);
        data.put("state", 0);
        data.put("queryUserNumber", true);
        data.put("pageIndex", 1);
        data.put("pageSize", 10);



        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/rest/tag/countFindByTagId");
        requestBuilder.content(gson.toJson(data));
        requestBuilder.header("Content-Type", "application/json");
        requestBuilder.header("username", username);
        requestBuilder.header("adUserId", adUserId);
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));
    }




}
