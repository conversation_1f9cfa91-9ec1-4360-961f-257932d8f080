package cn.lyy.merchant.controller;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.lyy_ic_service_api.dto.LyyIcCardDTO;
import cn.lyy.merchant.Application;
import cn.lyy.merchant.dto.ChangeIcCardBalanceDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.hamcrest.CoreMatchers.is;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@AutoConfigureMockMvc
public class IcCardControllerTest {

    @Autowired
    private MockMvc mockMvc;

    // 王海 个人用户
    private final  String username = "15018442322";
    private final String adUserId = "1220509";

    @Test
    public void saveCard() throws Exception {

        LyyIcCardDTO lyyIcCardDTO = new LyyIcCardDTO();
        lyyIcCardDTO.setType(1);
        lyyIcCardDTO.setCardNo("4187849819");
        lyyIcCardDTO.setUserName("关云长");
        lyyIcCardDTO.setPhone(13662693676l);
        lyyIcCardDTO.setStatus(1);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/rest/ic/saveCard");

        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);

        requestBuilder.content(JSON.toJSONString(lyyIcCardDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));
    }

    @Test
    public void editCard() throws Exception {

        LyyIcCardDTO lyyIcCardDTO = new LyyIcCardDTO();
        lyyIcCardDTO.setLyyIcCardId(10342l);
        lyyIcCardDTO.setType(1);
        //lyyIcCardDTO.setCardNo("4187849819");
        lyyIcCardDTO.setUserName("马云4");
        lyyIcCardDTO.setPhone(136626936769l);
        lyyIcCardDTO.setStatus(0);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/rest/ic/saveCard");

        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);

        requestBuilder.content(JSON.toJSONString(lyyIcCardDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));
    }


    @Test
    public void list() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/rest/ic/list")
                .param("queryStr","")
                .param("status","0")
                .param("type","")
                .param("pageIndex","1")
                .param("pageSize","10")
                ;

        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);

       // requestBuilder.content(JSON.toJSONString(lyyIcCardDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));
    }

    @Test
    public void findByCardNo() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/rest/ic/findByCardNo")
                .param("cardNo","4187849819")
                ;

        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);

        // requestBuilder.content(JSON.toJSONString(lyyIcCardDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));
    }

    /**
     * 修改IC卡的余额
     * @throws Exception
     */
    @Test
    public void changeIcCardBalance() throws Exception {

        ChangeIcCardBalanceDTO changeIcCardBalanceDTO = new ChangeIcCardBalanceDTO();
        List<String> list = new ArrayList<>();
        list.add("13");
        //list.add("10086");
        changeIcCardBalanceDTO.setCards(list);
        changeIcCardBalanceDTO.setChangeAmount(new BigDecimal("1"));

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/rest/ic/change/balance")
                ;

        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);

         requestBuilder.content(JSON.toJSONString(changeIcCardBalanceDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));
    }

    /**
     * ic 卡 商户修改记录
     * @throws Exception
     */
    @Test
    public void listChangeIcCardBalance() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/rest/ic/change/list")
                .param("pageIndex","1")
                .param("pageSize","10")
                .param("type","")
                .param("searchKey","")
                ;

        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);

        //requestBuilder.content(JSON.toJSONString(changeIcCardBalanceDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));
    }

    @Test
    public void saveFillingPreferential() throws Exception {

        JSONObject requestBody = new JSONObject();
        requestBody.put("price","3");
        requestBody.put("value","30");
        requestBody.put("id","27700");

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/rest/ic/saveFillingPreferential");

        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);

        requestBuilder.content(JSON.toJSONString(requestBody));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));
    }

    /**
     * 充值优惠列表
     * @throws Exception
     */
    @Test
    public void fillingPreferentialList() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/rest/ic/fillingPreferentialList");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);

        //requestBuilder.content(JSON.toJSONString(requestBody));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));
    }

    @Test
    public void delFillingPreferentialList() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.delete("/rest/ic/deleteFillingPreferential")
                .param("lyyIcFillingPreferentialId","27500");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);

        //requestBuilder.content(JSON.toJSONString(requestBody));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));
    }



}
