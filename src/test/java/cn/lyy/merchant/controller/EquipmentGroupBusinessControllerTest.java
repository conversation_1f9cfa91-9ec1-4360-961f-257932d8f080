package cn.lyy.merchant.controller;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import static org.hamcrest.CoreMatchers.is;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest
@AutoConfigureMockMvc
public class EquipmentGroupBusinessControllerTest {


    @Autowired
    private MockMvc mockMvc;

    // 王海 个人用户
    private final  String username = "***********";
    private final String adUserId = "1220509";

    private final String equipmentTypeId = "1000078"; // 充电桩
    private final Long protocolId = 22404l;


    @Test
    public void listGroupAndEq() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/rest/equipment/group/listGroupAndEq")
                .param("equipmentTypeId",equipmentTypeId);
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);
        //requestBuilder.content(JSON.toJSONString(classify));
        this.mockMvc.perform(requestBuilder)
                .andDo(print())
                .andExpect(status().isOk());

    }

    @Deprecated
    @Test
    public void testGetGroupEquipmentByTypePotocol() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/rest/equipment/group/getGroupEquipmentByTypePotocol")
                .param("equipmentType","XYJ")
                .param("protocolId", protocolId.toString());
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);
        //requestBuilder.content(JSON.toJSONString(classify));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));

    }

    @Test
    public void  testGetGroupEquipmentByTypeProduct() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/rest/equipment/group/getGroupEquipmentByTypeProduct")
                .param("equipmentType","XYJ")
                .param("productId", "1000615");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.header("username",username);
        requestBuilder.header("adUserId",adUserId);
        //requestBuilder.content(JSON.toJSONString(classify));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(0)));

    }




}
