package cn.lyy.merchant.doc;


import com.lyy.user.account.infrastructure.resp.RespBody;
import com.power.doc.builder.PostmanJsonBuilder;
import com.power.doc.model.ApiConfig;
import com.power.doc.model.CustomField;
import com.power.doc.model.SourceCodePath;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

/**
 * <AUTHOR>
 * @create 2021/6/16 10:40
 */
@Slf4j
public class DocGenerate {

    @Test
    public void testBuilderControllersApi() {
        ApiConfig config = new ApiConfig();
        //true会严格要求代码中必须有java注释，首次体验可关闭，正式产品推荐设置true
        //config.setStrict(true);

        String serverUrl = "http://192.168.0.132:6758/mb";
        config.setServerUrl(serverUrl);
        //当把AllInOne设置为true时，Smart-doc将会把所有接口生成到一个Markdown、HHTML或者AsciiDoc中
        config.setAllInOne(true);

        config.setProjectName("B端商户Business项目");

        config.setPackageFilters("cn.lyy.merchant");

        //Set the api document output path.
        config.setOutPath("D:\\文档\\smartdoc\\merchant_business");

        config.setSourceCodePaths(
                SourceCodePath.builder().setDesc("本项目代码").setPath("src/main/java")
                //SourceCodePath.builder().setDesc("加载dto代码").setPath("D:\\gitRepo\\leyaoyao\\middleStage\\user-member\\user-member-rpc\\src\\main\\java")
        );

        //CustomField customField  = CustomField.builder().setName("message").setDesc("自定义业务提示说明");
        config.setCustomResponseFields(
                CustomField.builder().setOwnerClassName(RespBody.class.getName()).setName("message").setDesc("自定义业务提示说明"),
                CustomField.builder().setOwnerClassName(RespBody.class.getName()).setName("data").setDesc("接口响应数据"),
                CustomField.builder().setOwnerClassName(RespBody.class.getName()).setName("code").setValue("0").setDesc("自定义响应代码，0代表成功")
        );
        //生成Markdown文件
        PostmanJsonBuilder.buildPostmanCollection(config);
        log.info("postman json create");
        // 生成html文件
       // HtmlApiDocBuilder.buildApiDoc(config);
        //log.info("html文档生成成功！！！");

    }
}
