package cn.lyy.merchant;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.File;
import java.io.IOException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ResourceUtils;

/**
 * <AUTHOR>
 * @since 2022/10/11 - 10:35
 */
@Slf4j
public class ResourceUtil {

    private ResourceUtil() {
    }
   private final static ObjectMapper mapper = new ObjectMapper();

    static {
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    @SuppressWarnings("rawtypes")
    public static <T> T readJson(String resource, Class<T> clazz) {
        try {
            File file = ResourceUtils.getFile("classpath:" + resource);
            String json = FileUtils.readFileToString(file);
            if (StringUtils.isBlank(json)) {
                log.warn("Json is blank");
                return null;
            }
            return mapper.readValue(json, clazz);
        } catch (IOException e) {
            log.error(e.getMessage());
            return null;
        }
    }

    public static <T> T readJson(String resource, TypeReference<T> valueTypeRef) {
        try {
            File file = ResourceUtils.getFile("classpath:" + resource);
            String json = FileUtils.readFileToString(file);
            if (StringUtils.isBlank(json)) {
                log.warn("Json is blank");
                return null;
            }
            return mapper.readValue(json, valueTypeRef);
        } catch (IOException e) {
            log.error(e.getMessage());
            return null;
        }
    }

}
