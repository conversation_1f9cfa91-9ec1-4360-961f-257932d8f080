<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.lyy.base</groupId>
        <artifactId>base-parent</artifactId>
        <version>1.1.0</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>merchant_business</artifactId>
    <version>${project.version}</version>
    <name>merchant_business</name>


    <properties>
        <maven-jar-plugin.version>3.0.0</maven-jar-plugin.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <slf4j.version>1.7.28</slf4j.version>
        <discovery.version>6.5.0</discovery.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.lyy.base</groupId>
                <artifactId>base-bom</artifactId>
                <version>${base-bom.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>cn.lyy.base</groupId>
            <artifactId>communal</artifactId>
            <version>${communal.version}</version>
        </dependency>
        <dependency>
            <groupId>com.lyy</groupId>
            <artifactId>third-member-server</artifactId>
            <version>${third-member-server.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.lyy</groupId>
                    <artifactId>user-member-rpc</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.lyy</groupId>
            <artifactId>life_class_service_api</artifactId>
            <version>${life_class_service_api.version}</version>
            <exclusions>
<!--                <exclusion>-->
<!--                    <artifactId>spring-cloud-openfeign-core</artifactId>-->
<!--                    <groupId>org.springframework.cloud</groupId>-->
<!--                </exclusion>-->
                <exclusion>
                    <artifactId>netty-common</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>netty-handler</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>netty-transport</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.lyy.payment</groupId>
            <artifactId>payment-merchant-rpc</artifactId>
            <version>${payment-merchant.version}</version>
        </dependency>

        <dependency>
            <groupId>com.lyy.payment</groupId>
            <artifactId>payment-merchant-dto</artifactId>
            <version>${payment-merchant.version}</version>
        </dependency>
        <!-- xxljob -->
         <dependency>
            <groupId>cn.lyy.open</groupId>
            <artifactId>order_common</artifactId>
            <version>${order-service.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.lyy.open</groupId>
            <artifactId>payment_api</artifactId>
            <version>${payment-rpc.version}</version>
        </dependency>
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
            <version>2.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.caucho</groupId>
            <artifactId>hessian</artifactId>
            <version>4.0.63</version>
        </dependency>

        <!-- odps -->
        <dependency>
            <groupId>com.aliyun.odps</groupId>
            <artifactId>odps-jdbc</artifactId>
            <version>3.1.0</version>
        </dependency>
       <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
        </dependency>

        <!--export-->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml-schemas</artifactId>
            <version>4.1.2</version>
        </dependency>
        <!--export-->

        <dependency>
            <groupId>com.lyy.starter</groupId>
            <artifactId>common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.lyy</groupId>
            <artifactId>lyy-lock</artifactId>
        </dependency>

        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
        </dependency>

        <dependency>
            <groupId>com.lyy</groupId>
            <artifactId>circurtbreaker-client</artifactId>
        </dependency>
        <dependency>
          <groupId>com.lyy.sysrisk</groupId>
          <artifactId>sysrisk</artifactId>
          <version>${sysrisk.version}</version>
        </dependency>
        <dependency>
            <groupId>com.lyy</groupId>
            <artifactId>commodity-rpc</artifactId>
            <version>${commodity.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.lyy</groupId>
            <artifactId>data_sync_dto</artifactId>
            <version>${default.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.lyy.base</groupId>
            <artifactId>utils</artifactId>
            <version>${default.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>jackson-dataformat-xml</artifactId>
                    <groupId>com.fasterxml.jackson.dataformat</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-to-slf4j</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>simpleclient</artifactId>
                    <groupId>io.prometheus</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>

        <dependency>
            <groupId>com.lyy.starter</groupId>
            <artifactId>nacos-discovery-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-sleuth</artifactId>
        </dependency>
        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <version>4.6</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.framework.apollo</groupId>
            <artifactId>apollo-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>cn.lyy</groupId>
            <artifactId>equipment-api</artifactId>
            <version>${equipment_api_version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>base</artifactId>
                    <groupId>cn.lyy</groupId>
                </exclusion>

            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.lyy</groupId>
            <artifactId>equipment_dto</artifactId>
            <version>${equipment_api_version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>cn.lyy</groupId>
            <artifactId>ic_card_service_api</artifactId>
            <version>${default.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-hystrix</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-feign</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>base</artifactId>
                    <groupId>cn.lyy</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.lyy</groupId>
            <artifactId>lyy_coupon_api</artifactId>
            <version>${counpon_service_api.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-hystrix</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-feign</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.lyy</groupId>
            <artifactId>lyy_cmember_service_api</artifactId>
            <version>${cmember_service_api.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-hystrix</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-feign</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.lyy</groupId>
            <artifactId>merchant_center_api</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-hystrix</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-feign</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.lyy</groupId>
            <artifactId>merchant_center_dto</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>base</artifactId>
                    <groupId>cn.lyy</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>cn.lyy</groupId>
                    <artifactId>lyy_dto</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.lyy</groupId>
            <artifactId>merchant-bff-rpc</artifactId>
            <version>${merchant-bff-rpc.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.lyy.income</groupId>
            <artifactId>income_dto</artifactId>
            <version>${income_api.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.lyy.income</groupId>
            <artifactId>income_api</artifactId>
            <version>${income_api.version}</version>
            <scope>compile</scope>
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>org.springframework.cloud</groupId>-->
<!--                    <artifactId>spring-cloud-starter-hystrix</artifactId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <groupId>org.springframework.cloud</groupId>-->
<!--                    <artifactId>spring-cloud-starter-feign</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
        </dependency>

        <dependency>
            <groupId>cn.lyy</groupId>
            <artifactId>equipment_coins_service_api</artifactId>
            <version>${default.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-hystrix</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-feign</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>base</artifactId>
                    <groupId>cn.lyy</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.lyy</groupId>
            <artifactId>base</artifactId>
            <version>${base_version}</version>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>druid</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>cn.lyy</groupId>
            <artifactId>lyy_data_service_api</artifactId>
            <version>${lyy_data_service_api.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-hystrix</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-feign</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>base</artifactId>
                    <groupId>cn.lyy</groupId>
                </exclusion>
                <exclusion>
                    <groupId>cn.lyy</groupId>
                    <artifactId>switch_service_api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.lyy</groupId>
            <artifactId>switch_service_api</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.lyy</groupId>
            <artifactId>websocket_api</artifactId>
            <version>${websocket.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-hystrix</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-feign</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.lyy</groupId>
            <artifactId>ram-service-rpc</artifactId>
            <version>${ram-service-rpc.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.lyy</groupId>
            <artifactId>authority_service_api</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>base</artifactId>
                    <groupId>cn.lyy</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.lyy</groupId>
            <artifactId>authority_common</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.lyy</groupId>
            <artifactId>user_statistics_api</artifactId>
            <version>${default.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.lyy</groupId>
            <artifactId>consumption_service_api</artifactId>
            <version>${consumption_service_api.version}</version>
            <exclusions>
                <exclusion>
                    <groupId> org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-config-client</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>base</artifactId>
                    <groupId>cn.lyy</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>cn.lyy</groupId>
            <artifactId>user_members_api</artifactId>
            <version>${default.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.lyy</groupId>
            <artifactId>user_members_dto</artifactId>
            <version>${default.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.lyy.open</groupId>
            <artifactId>order_api</artifactId>
            <version>${order-service.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.lyy.open</groupId>
            <artifactId>payment_common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.lyy</groupId>
            <artifactId>redis_api</artifactId>
            <version>${default.version}</version>
        </dependency>
        <!-- zxing -->
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>3.4.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>net.lingala.zip4j</groupId>
            <artifactId>zip4j</artifactId>
            <version>1.3.3</version>
            <scope>compile</scope>
        </dependency>

        <!-- user-member -->
        <dependency>
            <groupId>com.lyy</groupId>
            <artifactId>user-member-rpc</artifactId>
            <version>${user-member.version}</version>
        </dependency>

        <dependency>
            <groupId>com.lyy</groupId>
            <artifactId>error-code</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.shalousun</groupId>
            <artifactId>smart-doc</artifactId>
            <version>2.1.7</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.alipay.sdk</groupId>
            <artifactId>alipay-sdk-java</artifactId>
            <version>4.10.0.ALL</version>
        </dependency>

        <dependency>
            <groupId>cn.lyy</groupId>
            <artifactId>tools</artifactId>
            <version>${tools.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>xstream</artifactId>
                    <groupId>com.thoughtworks.xstream</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>poi</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>cn.lyy</groupId>
                    <artifactId>base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

      <dependency>
        <groupId>com.lyy</groupId>
        <artifactId>billing-common</artifactId>
        <version>${billing_version}</version>
      </dependency>



        <dependency>
            <groupId>cn.lyy</groupId>
            <artifactId>auth_api</artifactId>
            <version>${default.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>cn.lyy.base</groupId>
                    <artifactId>utils2</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.lyy</groupId>
            <artifactId>charge-rpc</artifactId>
            <version>${charge-rpc.version}</version>
        </dependency>

      <dependency>
        <groupId>com.lyy</groupId>
        <artifactId>equipment-rpc</artifactId>
        <version>${equipment.version}</version>
      </dependency>

        <dependency>
            <groupId>com.lyy</groupId>
            <artifactId>user-app-rpc</artifactId>
            <version>${user-app.version}</version>
        </dependency>
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>

        <dependency>
            <groupId>com.lyy</groupId>
            <artifactId>user-behavior-rpc</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.lyy</groupId>
            <artifactId>legal-rpc</artifactId>
            <version>${legal-rpc.version}</version>
        </dependency>

        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.lyy</groupId>
            <artifactId>marketing_dto</artifactId>
            <version>${marketing.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.lyy</groupId>
            <artifactId>marketing_api</artifactId>
            <version>${marketing.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.lyy</groupId>
            <artifactId>message_send_rpc</artifactId>
            <version>${message-send.version}</version>
        </dependency>

        <dependency>
            <groupId>com.lyy</groupId>
            <artifactId>billing-rpc</artifactId>
            <version>${billing_version}</version>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>
        <dependency>
            <groupId>com.lyy.starter</groupId>
            <artifactId>oss-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.lyy</groupId>
            <artifactId>lyy_service_api</artifactId>
            <version>${lyy_service_api.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.lyy</groupId>
            <artifactId>lyy_dto</artifactId>
            <version>${lyy_service_api.version}</version>
        </dependency>

      <dependency>
        <groupId>cn.lyy</groupId>
        <artifactId>bigdata_service_api</artifactId>
        <version>${bigdata_service_api.version}</version>
      </dependency>

        <dependency>
            <groupId>com.lyy.vending</groupId>
            <artifactId>vending_business_rpc</artifactId>
            <version>${vending_business_rpc_version}</version>
        </dependency>

        <dependency>
            <groupId>io.lettuce</groupId>
            <artifactId>lettuce-core</artifactId>
            <version>6.1.10.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.lyy</groupId>
            <artifactId>payment-divide-bff-rpc</artifactId>
            <version>${payment-divide-bff.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>cn.hutool</groupId>
                    <artifactId>hutool-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.lyy.base</groupId>
                    <artifactId>base-application-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.lyy.starter</groupId>
                    <artifactId>rocketmq</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.lyy.starter</groupId>
                    <artifactId>common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>


    <distributionManagement>
        <repository>
            <id>nexus-releases</id>
            <url>https://nexus.leyaoyao.com/repository/releases/</url>
        </repository>
        <snapshotRepository>
            <id>nexus-snapshots</id>
            <url>https://nexus.leyaoyao.com/repository/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <repositories>
        <repository>
            <id>nexus-releases</id>
            <url>https://nexus.leyaoyao.com/repository/public/</url>
        </repository>
    </repositories>


    <profiles>
        <profile>
            <id>local</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <deploy>local</deploy>
                <project.version>0.1.0-SNAPSHOT</project.version>
                <consumption.version>0.1.0-SNAPSHOT</consumption.version>
                <default.version>0.0.1-SNAPSHOT</default.version>
                <order-service.version>0.2.0-SNAPSHOT</order-service.version>
                <payment-rpc.version>0.1.0-SNAPSHOT</payment-rpc.version>
                <utils.version>0.0.2-SNAPSHOT</utils.version>
                <workflow.version>0.0.2-SNAPSHOT</workflow.version>
                <websocket.version>0.1.0-SNAPSHOT</websocket.version>
                <base_version>0.1.2-dev-SNAPSHOT</base_version>
                <lock.version>1.0-SNAPSHOT</lock.version>
                <counpon_service_api.version>0.0.1-SNAPSHOT</counpon_service_api.version>
                <cmember_service_api.version>2.0.0-SNAPSHOT</cmember_service_api.version>
                <tools.version>0.0.5-dev-SNAPSHOT</tools.version>
                <charge-rpc.version>0.1.0-SNAPSHOT</charge-rpc.version>
                <life_class_service_api.version>0.1.0-SNAPSHOT</life_class_service_api.version>
                <base-bom.version>1.1.1-SNAPSHOT</base-bom.version>
                <user-member.version>0.2.0-SNAPSHOT</user-member.version>
                <commodity.version>2.1.0-SNAPSHOT</commodity.version>
                <legal-rpc.version>0.1.0-SNAPSHOT</legal-rpc.version>
                <equipment_api_version>1.1.0-SNAPSHOT</equipment_api_version>
                <marketing.version>0.1.0-SNAPSHOT</marketing.version>
                <billing_version>0.2.0-SNAPSHOT</billing_version>
                <user-app.version>0.1.0-SNAPSHOT</user-app.version>
                <sysrisk.version>2.0-SNAPSHOT</sysrisk.version>
                <third-member-server.version>0.1.0-SNAPSHOT</third-member-server.version>
                <lyy_data_service_api.version>0.1.0-SNAPSHOT</lyy_data_service_api.version>
                <lyy_service_api.version>2.0.0-SNAPSHOT</lyy_service_api.version>
            	<consumption_service_api.version>0.1.0-SNAPSHOT</consumption_service_api.version>
                <equipment.version>0.2.0-SNAPSHOT</equipment.version>
                <ram-service-rpc.version>2.0.0-SNAPSHOT</ram-service-rpc.version>
                <merchant-bff-rpc.version>0.1.0-SNAPSHOT</merchant-bff-rpc.version>
                <bigdata_service_api.version>2.0.0-SNAPSHOT</bigdata_service_api.version>
                <vending_business_rpc_version>0.1.0-SNAPSHOT</vending_business_rpc_version>
                <payment-client-version>0.1.0-SNAPSHOT</payment-client-version>
                <payment-dto-version>0.1.0-SNAPSHOT</payment-dto-version>
                <payment-merchant.version>0.1.0-SNAPSHOT</payment-merchant.version>
                <payment-divide-bff.version>2.0.0-SNAPSHOT</payment-divide-bff.version>
                <message-send.version>3.1.0-SNAPSHOT</message-send.version>
                <communal.version>0.0.2-SNAPSHOT</communal.version>
                <message-send.version>3.1.0-SNAPSHOT</message-send.version>
                <income_api.version>2.0.1-SNAPSHOT</income_api.version>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <deploy>dev</deploy>
                <project.version>0.1.0-SNAPSHOT</project.version>
                <consumption.version>0.1.0-SNAPSHOT</consumption.version>
                <default.version>0.0.1-SNAPSHOT</default.version>
                <order-service.version>0.2.0-SNAPSHOT</order-service.version>
                <payment-rpc.version>0.1.0-SNAPSHOT</payment-rpc.version>
                <utils.version>0.0.2-SNAPSHOT</utils.version>
                <workflow.version>0.0.2-SNAPSHOT</workflow.version>
                <websocket.version>0.1.0-SNAPSHOT</websocket.version>
                <base_version>0.1.2-dev-SNAPSHOT</base_version>
                <counpon_service_api.version>0.0.1-SNAPSHOT</counpon_service_api.version>
                <cmember_service_api.version>2.0.0-SNAPSHOT</cmember_service_api.version>
                <tools.version>0.0.5-dev-SNAPSHOT</tools.version>
                <charge-rpc.version>0.1.0-SNAPSHOT</charge-rpc.version>
                <life_class_service_api.version>0.1.0-SNAPSHOT</life_class_service_api.version>
                <base-bom.version>1.1.1-SNAPSHOT</base-bom.version>
                <user-member.version>0.2.0-SNAPSHOT</user-member.version>
                <sysrisk.version>2.0-SNAPSHOT</sysrisk.version>
                <commodity.version>2.1.0-SNAPSHOT</commodity.version>
                <legal-rpc.version>0.1.0-SNAPSHOT</legal-rpc.version>
                <equipment_api_version>1.1.0-SNAPSHOT</equipment_api_version>
                <marketing.version>0.1.0-SNAPSHOT</marketing.version>
                <billing_version>0.2.0-SNAPSHOT</billing_version>
                <user-app.version>0.1.0-SNAPSHOT</user-app.version>
                <third-member-server.version>0.1.0-SNAPSHOT</third-member-server.version>
                <lyy_data_service_api.version>0.1.0-SNAPSHOT</lyy_data_service_api.version>
                <lyy_service_api.version>2.0.0-SNAPSHOT</lyy_service_api.version>
                <equipment.version>0.2.0-SNAPSHOT</equipment.version>
            	<consumption_service_api.version>0.1.0-SNAPSHOT</consumption_service_api.version>
                <ram-service-rpc.version>2.0.0-SNAPSHOT</ram-service-rpc.version>
                <merchant-bff-rpc.version>0.1.0-SNAPSHOT</merchant-bff-rpc.version>
              <bigdata_service_api.version>2.0.0-SNAPSHOT</bigdata_service_api.version>
                <vending_business_rpc_version>0.1.0-SNAPSHOT</vending_business_rpc_version>
                <payment-merchant.version>0.1.0-SNAPSHOT</payment-merchant.version>
                <payment-divide-bff.version>2.0.0-SNAPSHOT</payment-divide-bff.version>
                <message-send.version>3.1.0-SNAPSHOT</message-send.version>
                <communal.version>0.0.2-SNAPSHOT</communal.version>
                <message-send.version>3.1.0-SNAPSHOT</message-send.version>
                <income_api.version>2.0.1-SNAPSHOT</income_api.version>
            </properties>
        </profile>
        <profile>
            <id>sit</id>
            <properties>
                <deploy>sit</deploy>
                <project.version>0.1.0-SNAPSHOT</project.version>
                <consumption.version>0.1.0-SNAPSHOT</consumption.version>
                <default.version>0.0.1-SNAPSHOT</default.version>
                <order-service.version>0.2.0-SNAPSHOT</order-service.version>
                <payment-rpc.version>0.1.0-SNAPSHOT</payment-rpc.version>
                <utils.version>0.0.2-SNAPSHOT</utils.version>
                <workflow.version>0.0.2-SNAPSHOT</workflow.version>
                <websocket.version>0.1.0-SNAPSHOT</websocket.version>
                <base_version>0.1.2-dev-SNAPSHOT</base_version>
                <counpon_service_api.version>0.0.1-SNAPSHOT</counpon_service_api.version>
                <cmember_service_api.version>2.0.0-SNAPSHOT</cmember_service_api.version>
                <tools.version>0.0.5-dev-SNAPSHOT</tools.version>
                <charge-rpc.version>0.1.0-SNAPSHOT</charge-rpc.version>
                <life_class_service_api.version>0.1.0-SNAPSHOT</life_class_service_api.version>
                <base-bom.version>1.1.1-SNAPSHOT</base-bom.version>
                <user-member.version>0.2.0-SNAPSHOT</user-member.version>
                <commodity.version>2.1.0-SNAPSHOT</commodity.version>
                <legal-rpc.version>0.1.0-SNAPSHOT</legal-rpc.version>
                <equipment_api_version>1.1.0-SNAPSHOT</equipment_api_version>
                <marketing.version>0.1.0-SNAPSHOT</marketing.version>
                <billing_version>0.2.0-SNAPSHOT</billing_version>
                <user-app.version>0.1.0-SNAPSHOT</user-app.version>
                <sysrisk.version>2.0-SNAPSHOT</sysrisk.version>
                <third-member-server.version>0.1.0-SNAPSHOT</third-member-server.version>
                <lyy_data_service_api.version>0.1.0-SNAPSHOT</lyy_data_service_api.version>
                <lyy_service_api.version>2.0.0-SNAPSHOT</lyy_service_api.version>
                <equipment.version>0.2.0-SNAPSHOT</equipment.version>
            	<consumption_service_api.version>0.1.0-SNAPSHOT</consumption_service_api.version>
                <ram-service-rpc.version>2.0.0-SNAPSHOT</ram-service-rpc.version>
                <merchant-bff-rpc.version>0.1.0-SNAPSHOT</merchant-bff-rpc.version>
                <bigdata_service_api.version>2.0.0-SNAPSHOT</bigdata_service_api.version>
                <vending_business_rpc_version>0.1.0-SIT</vending_business_rpc_version>
                <payment-merchant.version>0.1.0-SNAPSHOT</payment-merchant.version>
                <payment-divide-bff.version>2.0.0-SNAPSHOT</payment-divide-bff.version>
                <message-send.version>3.1.0-SNAPSHOT</message-send.version>
                <communal.version>0.0.2-SNAPSHOT</communal.version>
                <message-send.version>3.1.0-SNAPSHOT</message-send.version>
                <income_api.version>2.0.1-SNAPSHOT</income_api.version>
            </properties>
        </profile>
        <profile>
            <id>uat</id>
            <properties>
                <deploy>uat</deploy>
                <project.version>0.1.0-BETA</project.version>
                <consumption.version>0.1.0-BETA</consumption.version>
                <default.version>0.0.1-BETA</default.version>
                <order-service.version>0.2.0-BETA</order-service.version>
                <payment-rpc.version>0.1.0-BETA</payment-rpc.version>
                <utils.version>0.0.2-BETA</utils.version>
                <workflow.version>0.0.2-BETA</workflow.version>
                <websocket.version>0.1.0-BETA</websocket.version>
                <base_version>0.1.2-uat-BETA</base_version>
                <counpon_service_api.version>0.0.1-BETA</counpon_service_api.version>
                <cmember_service_api.version>2.0.0-BETA</cmember_service_api.version>
                <tools.version>0.0.5-uat-BETA</tools.version>
                <charge-rpc.version>0.1.0-BETA</charge-rpc.version>
                <life_class_service_api.version>0.1.0-BETA</life_class_service_api.version>
                <base-bom.version>1.1.1-BETA</base-bom.version>
                <user-member.version>0.2.0-BETA</user-member.version>
                <commodity.version>2.1.0-BETA</commodity.version>
                <legal-rpc.version>0.1.0-BETA</legal-rpc.version>
                <equipment_api_version>1.1.0-BETA</equipment_api_version>
                <marketing.version>0.1.0-BETA</marketing.version>
                <billing_version>0.2.0-BETA</billing_version>
                <user-app.version>0.1.0-BETA</user-app.version>
                <sysrisk.version>2.0</sysrisk.version>
                <third-member-server.version>0.1.0-BETA</third-member-server.version>
                <lyy_data_service_api.version>0.1.0-BETA</lyy_data_service_api.version>
                <lyy_service_api.version>2.0.0-BETA</lyy_service_api.version>
                <equipment.version>0.2.0-BETA</equipment.version>
            	<consumption_service_api.version>0.1.0-BETA</consumption_service_api.version>
                <ram-service-rpc.version>2.0.0-BETA</ram-service-rpc.version>
                <merchant-bff-rpc.version>0.1.0-BETA</merchant-bff-rpc.version>
                <bigdata_service_api.version>2.0.0-BETA</bigdata_service_api.version>
                <vending_business_rpc_version>0.1.0-BETA</vending_business_rpc_version>
                <payment-merchant.version>0.1.0-BETA</payment-merchant.version>
                <payment-divide-bff.version>2.0.0-BETA</payment-divide-bff.version>
                <message-send.version>3.1.0-BETA</message-send.version>
                <communal.version>0.0.2-BETA</communal.version>
                <message-send.version>3.1.0-BETA</message-send.version>
                <income_api.version>2.0.1-BETA</income_api.version>
            </properties>
        </profile>
        <profile>
            <id>release</id>
            <properties>
                <deploy>release</deploy>
                <project.version>0.1.0-BETA</project.version>
                <consumption.version>0.1.0-BETA</consumption.version>
                <default.version>0.0.1-BETA</default.version>
                <order-service.version>0.2.0-BETA</order-service.version>
                <payment-rpc.version>0.1.0-BETA</payment-rpc.version>
                <utils.version>0.0.2-BETA</utils.version>
                <workflow.version>0.0.2-BETA</workflow.version>
                <websocket.version>0.1.0-BETA</websocket.version>
                <base_version>0.1.2-uat-BETA</base_version>
                <counpon_service_api.version>0.0.1-BETA</counpon_service_api.version>
                <cmember_service_api.version>2.0.0-BETA</cmember_service_api.version>
                <tools.version>0.0.5-uat-BETA</tools.version>
                <charge-rpc.version>0.1.0-BETA</charge-rpc.version>
                <life_class_service_api.version>0.1.0-BETA</life_class_service_api.version>
                <base-bom.version>1.1.1-BETA</base-bom.version>
                <user-member.version>0.2.0-BETA</user-member.version>
                <commodity.version>2.1.0-BETA</commodity.version>
                <legal-rpc.version>0.1.0-BETA</legal-rpc.version>
                <equipment_api_version>1.1.0-BETA</equipment_api_version>
                <marketing.version>0.1.0-BETA</marketing.version>
                <billing_version>0.2.0-BETA</billing_version>
                <user-app.version>0.1.0-BETA</user-app.version>
                <sysrisk.version>2.0</sysrisk.version>
                <third-member-server.version>0.1.0-BETA</third-member-server.version>
                <lyy_data_service_api.version>0.1.0-BETA</lyy_data_service_api.version>
                <lyy_service_api.version>2.0.0-BETA</lyy_service_api.version>
                <equipment.version>0.2.0-BETA</equipment.version>
            	<consumption_service_api.version>0.1.0-BETA</consumption_service_api.version>
                <ram-service-rpc.version>2.0.0-BETA</ram-service-rpc.version>
                <merchant-bff-rpc.version>0.1.0-BETA</merchant-bff-rpc.version>
                <bigdata_service_api.version>2.0.0-BETA</bigdata_service_api.version>
                <vending_business_rpc_version>0.1.0-BETA</vending_business_rpc_version>
                <payment-merchant.version>0.1.0-BETA</payment-merchant.version>
                <payment-divide-bff.version>2.0.0-BETA</payment-divide-bff.version>
                <message-send.version>3.1.0-BETA</message-send.version>
                <communal.version>0.0.2-BETA</communal.version>
                <message-send.version>3.1.0-BETA</message-send.version>
                <income_api.version>2.0.1-BETA</income_api.version>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <deploy>prod</deploy>
                <project.version>0.1.0-RELEASE</project.version>
                <consumption.version>0.1.0-RELEASE</consumption.version>
                <default.version>0.0.1-RELEASE</default.version>
                <order-service.version>0.2.0-RELEASE</order-service.version>
                <payment-rpc.version>0.1.0-RELEASE</payment-rpc.version>
                <utils.version>0.0.2-RELEASE</utils.version>
                <workflow.version>0.0.2-RELEASE</workflow.version>
                <websocket.version>0.1.0-RELEASE</websocket.version>
                <base_version>0.1.2-prod-RELEASE</base_version>
                <counpon_service_api.version>0.0.1-RELEASE</counpon_service_api.version>
                <cmember_service_api.version>2.0.0-RELEASE</cmember_service_api.version>
                <tools.version>0.0.5-prod-RELEASE</tools.version>
                <charge-rpc.version>0.1.0-RELEASE</charge-rpc.version>
                <life_class_service_api.version>0.1.0-RELEASE</life_class_service_api.version>
                <base-bom.version>1.1.1-RELEASE</base-bom.version>
                <user-member.version>0.2.0-RELEASE</user-member.version>
                <commodity.version>2.1.0-RELEASE</commodity.version>
                <legal-rpc.version>0.1.0-RELEASE</legal-rpc.version>
                <equipment_api_version>1.1.0-RELEASE</equipment_api_version>
                <marketing.version>0.1.0-RELEASE</marketing.version>
                <billing_version>0.2.0-RELEASE</billing_version>
                <user-app.version>0.1.0-RELEASE</user-app.version>
                <sysrisk.version>2.0</sysrisk.version>
                <third-member-server.version>0.1.0-RELEASE</third-member-server.version>
                <lyy_data_service_api.version>0.1.0-RELEASE</lyy_data_service_api.version>
                <lyy_service_api.version>2.0.0-RELEASE</lyy_service_api.version>
                <equipment.version>0.2.0-RELEASE</equipment.version>
            	<consumption_service_api.version>0.1.0-RELEASE</consumption_service_api.version>
                <ram-service-rpc.version>2.0.0-RELEASE</ram-service-rpc.version>
                <merchant-bff-rpc.version>0.1.0-RELEASE</merchant-bff-rpc.version>
                <bigdata_service_api.version>2.0.0-RELEASE</bigdata_service_api.version>
                <vending_business_rpc_version>0.1.0-RELEASE</vending_business_rpc_version>
                <payment-merchant.version>0.1.0-RELEASE</payment-merchant.version>
                <payment-divide-bff.version>2.0.0-RELEASE</payment-divide-bff.version>
                <message-send.version>3.1.0-RELEASE</message-send.version>
                <communal.version>0.0.2-RELEASE</communal.version>
                <message-send.version>3.1.0-RELEASE</message-send.version>
                <income_api.version>2.0.1-RELEASE</income_api.version>
            </properties>
        </profile>
    </profiles>

    <build>
        <finalName>app</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-resources-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-prod-resources</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <!-- this is important -->
                            <overwrite>true</overwrite>
                            <outputDirectory>${basedir}/target/classes</outputDirectory>
                            <resources>
                                <resource>
                                    <filtering>true</filtering>
                                    <directory>deploy/${deploy}</directory>
                                    <targetPath>${basedir}/target/classes</targetPath>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <sourceDirectory>${basedir}/src/main/java</sourceDirectory>
        <testSourceDirectory>${basedir}/src/test/java</testSourceDirectory>
        <testOutputDirectory>target/test-classes</testOutputDirectory>
    </build>

</project>
